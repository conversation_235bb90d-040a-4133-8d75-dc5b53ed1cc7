{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{Box,IconButton,Button}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import{useTranslation}from'react-i18next';import useDrawerStore from\"../../store/drawerStore\";import\"./Canvas.module.css\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const OverlaySettings=_ref=>{let{selectedTemplate,onStatusChange,setOverLays,anchorEl,setOverlaySettings,setDesignPopup,setMenuPopup}=_ref;const{t:translate}=useTranslation();const[isOpen,setIsOpen]=useState(true);const{setOverlayEnabled,overlayEnabled,selectedTemplateTour,setIsUnSavedChanges}=useDrawerStore(state=>state);const{pageinteraction,setPageInteraction}=useDrawerStore(state=>state);// Initialize local state from store values\nconst[interaction,setInteraction]=useState(pageinteraction);const[status,setStatus]=useState(overlayEnabled);// Debug: Log the store values\nconsole.log('Overlay Component - Store values:',{overlayEnabled,pageinteraction});console.log('Overlay Component - Local state:',{status,interaction});// Keep local state in sync with store when store values change\nuseEffect(()=>{console.log('Overlay Component - useEffect triggered:',{overlayEnabled,pageinteraction});setStatus(overlayEnabled);setInteraction(pageinteraction);},[overlayEnabled,pageinteraction]);const handleStatusChange=event=>{const newStatus=event.target.checked;setStatus(newStatus);// Implement mutual exclusivity: when overlay changes, page interaction must be opposite\nif(newStatus===false){// When overlay is disabled, automatically enable page interaction\nsetInteraction(true);}else{// When overlay is enabled, automatically disable page interaction\nsetInteraction(false);}};const handleApplyChanges=()=>{// Create a batch update function to record a single history entry\nconst batchUpdate=useDrawerStore.getState().batchUpdate;// Use the batch update function to record a single history entry\nbatchUpdate(()=>{// Apply the changes\nsetOverlayEnabled(status);setPageInteraction(interaction);},'OVERLAY_BATCH_UPDATE',`Updated overlay settings`);// Update UI state\nsetIsOpen(false);setOverlaySettings(false);setMenuPopup(true);setIsUnSavedChanges(true);};const handleInteractionChange=event=>{const newInteraction=event.target.checked;setInteraction(newInteraction);// Implement asymmetric mutual exclusivity: only disable overlay when page interaction is enabled\nif(newInteraction===true){// When page interaction is enabled, automatically disable overlay\nsetStatus(false);}// When page interaction is disabled, do NOT automatically enable overlay\n// This allows both options to be disabled simultaneously\n};const handleClose=()=>{setIsOpen(false);setOverlaySettings(false);setMenuPopup(true);};if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":translate(\"close\"),onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-title\",children:[\" \",selectedTemplate!==\"Banner\"||selectedTemplateTour!==\"Banner\"?translate(\"Overlay\"):translate(\"Shadow\")]}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":translate(\"close\"),onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\"//style={{ opacity: \"0.5\", height: \"60px\" }}\n,children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:selectedTemplate===\"Banner\"||selectedTemplateTour===\"Banner\"?translate(\"Enable Shadow\"):translate(\"Enable Overlay\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch \",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:status,onChange:handleStatusChange,name:\"toggleSwitch\",disabled:selectedTemplate===\"Tour\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),selectedTemplate!==\"Banner\"&&selectedTemplateTour!==\"Banner\"&&!status&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Interact with Page\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch qadpt-toggle-group\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:interaction,onChange:handleInteractionChange,name:\"toggleSwitch\",disabled:selectedTemplate===\"Tour\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn`,children:translate(\"Apply\")})})]})});};export default OverlaySettings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useTranslation", "useDrawerStore", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "OverlaySettings", "_ref", "selectedTemplate", "onStatusChange", "setOverLays", "anchorEl", "setOverlaySettings", "setDesignPopup", "setMenuPopup", "t", "translate", "isOpen", "setIsOpen", "setOverlayEnabled", "overlayEnabled", "selectedTemplateTour", "setIsUnSavedChanges", "state", "pageinteraction", "setPageInteraction", "interaction", "setInteraction", "status", "setStatus", "console", "log", "handleStatusChange", "event", "newStatus", "target", "checked", "handleApplyChanges", "batchUpdate", "getState", "handleInteractionChange", "newInteraction", "handleClose", "id", "className", "children", "onClick", "size", "type", "onChange", "name", "disabled", "variant"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideDesign/Overlay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Typography, ToggleButton, ToggleButtonGroup, IconButton, FormControlLabel, Switch, <PERSON>ton } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport \"./Canvas.module.css\";\r\n\r\ninterface OverlaySettingsProps {\r\n\tselectedTemplate: string;\r\n\tonStatusChange: (status: boolean) => void;\r\n\tsetOverLays: (status: boolean) => void;\r\n\tanchorEl: HTMLElement | null;\r\n\tsetDesignPopup: (status: boolean) => void;\r\n}\r\n\r\nconst OverlaySettings = ({\r\n\tselectedTemplate,\r\n\tonStatusChange,\r\n\tsetOverLays,\r\n\tanchorEl,\r\n\tsetOverlaySettings,\r\n\tsetDesignPopup,\r\n\tsetMenuPopup\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst { setOverlayEnabled, overlayEnabled, selectedTemplateTour, setIsUnSavedChanges } = useDrawerStore((state) => state);\r\n\tconst { pageinteraction, setPageInteraction } = useDrawerStore((state: DrawerState) => state);\r\n\r\n\t// Initialize local state from store values\r\n\tconst [interaction, setInteraction] = useState<boolean>(pageinteraction);\r\n\tconst [status, setStatus] = useState(overlayEnabled);\r\n\r\n\t// Debug: Log the store values\r\n\tconsole.log('Overlay Component - Store values:', { overlayEnabled, pageinteraction });\r\n\tconsole.log('Overlay Component - Local state:', { status, interaction });\r\n\r\n\t// Keep local state in sync with store when store values change\r\n\tuseEffect(() => {\r\n\t\tconsole.log('Overlay Component - useEffect triggered:', { overlayEnabled, pageinteraction });\r\n\t\tsetStatus(overlayEnabled);\r\n\t\tsetInteraction(pageinteraction);\r\n\t}, [overlayEnabled, pageinteraction]);\r\n\r\n\tconst handleStatusChange = (event: any) => {\r\n\t\tconst newStatus = event.target.checked;\r\n\t\tsetStatus(newStatus);\r\n\r\n\t\t// Implement mutual exclusivity: when overlay changes, page interaction must be opposite\r\n\t\tif (newStatus === false) {\r\n\t\t\t// When overlay is disabled, automatically enable page interaction\r\n\t\t\tsetInteraction(true);\r\n\t\t} else {\r\n\t\t\t// When overlay is enabled, automatically disable page interaction\r\n\t\t\tsetInteraction(false);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Create a batch update function to record a single history entry\r\n\t\tconst batchUpdate = useDrawerStore.getState().batchUpdate;\r\n\r\n\t\t// Use the batch update function to record a single history entry\r\n\t\tbatchUpdate(\r\n\t\t\t() => {\r\n\t\t\t\t// Apply the changes\r\n\t\t\t\tsetOverlayEnabled(status);\r\n\t\t\t\tsetPageInteraction(interaction);\r\n\t\t\t},\r\n\t\t\t'OVERLAY_BATCH_UPDATE',\r\n\t\t\t`Updated overlay settings`\r\n\t\t);\r\n\r\n\t\t// Update UI state\r\n\t\tsetIsOpen(false);\r\n\t\tsetOverlaySettings(false);\r\n\t\tsetMenuPopup(true);\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst handleInteractionChange = (event: any) => {\r\n\t\tconst newInteraction = event.target.checked;\r\n\t\tsetInteraction(newInteraction);\r\n\r\n\t\t// Implement asymmetric mutual exclusivity: only disable overlay when page interaction is enabled\r\n\t\tif (newInteraction === true) {\r\n\t\t\t// When page interaction is enabled, automatically disable overlay\r\n\t\t\tsetStatus(false);\r\n\t\t}\r\n\t\t// When page interaction is disabled, do NOT automatically enable overlay\r\n\t\t// This allows both options to be disabled simultaneously\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetOverlaySettings(false);\r\n\t\tsetMenuPopup(true);\r\n\t};\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-title\"> {(selectedTemplate !== \"Banner\" || selectedTemplateTour !== \"Banner\") ? translate(\"Overlay\") : translate(\"Shadow\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Status Section */}\r\n\t\t\t\t<div\r\n\t\t\t\t\tclassName=\"qadpt-controls\"\r\n\t\t\t\t//style={{ opacity: \"0.5\", height: \"60px\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\" ? translate(\"Enable Shadow\") : translate(\"Enable Overlay\")}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch \">\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\tchecked={status}\r\n\t\t\t\t\t\t\t\t\tonChange={handleStatusChange}\r\n\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\tdisabled={selectedTemplate === \"Tour\"}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{/* Enable Shadow or Enable Overlay */}\r\n\t\t\t\t\t{/* <div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--back-light-color)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div className=\"qadpt-label\">{selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\" ? \"Enable Shadow\" : \"Enable Overlay\"}</div>\r\n\t\t\t\t\t\t<label className=\"toggle-switch \">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={status}\r\n        onChange={handleStatusChange}\r\n        name=\"toggleSwitch\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t</div> */}\r\n\r\n\t\t\t\t\t{(selectedTemplate !== \"Banner\" && selectedTemplateTour !== \"Banner\") && !status && (\r\n\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t{/* Interact with Page */}\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Interact with Page\")}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\tchecked={interaction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleInteractionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled={selectedTemplate === \"Tour\"}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn`}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default OverlaySettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,GAAG,CAA+CC,UAAU,CAA4BC,MAAM,KAAQ,eAAe,CAC9H,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE,MAAO,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAU7B,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAQb,IARc,CACxBC,gBAAgB,CAChBC,cAAc,CACdC,WAAW,CACXC,QAAQ,CACRC,kBAAkB,CAClBC,cAAc,CACdC,YACI,CAAC,CAAAP,IAAA,CACL,KAAM,CAAEQ,CAAC,CAAEC,SAAU,CAAC,CAAGlB,cAAc,CAAC,CAAC,CACzC,KAAM,CAACmB,MAAM,CAAEC,SAAS,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAE4B,iBAAiB,CAAEC,cAAc,CAAEC,oBAAoB,CAAEC,mBAAoB,CAAC,CAAGvB,cAAc,CAAEwB,KAAK,EAAKA,KAAK,CAAC,CACzH,KAAM,CAAEC,eAAe,CAAEC,kBAAmB,CAAC,CAAG1B,cAAc,CAAEwB,KAAkB,EAAKA,KAAK,CAAC,CAE7F;AACA,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGpC,QAAQ,CAAUiC,eAAe,CAAC,CACxE,KAAM,CAACI,MAAM,CAAEC,SAAS,CAAC,CAAGtC,QAAQ,CAAC6B,cAAc,CAAC,CAEpD;AACAU,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAE,CAAEX,cAAc,CAAEI,eAAgB,CAAC,CAAC,CACrFM,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAE,CAAEH,MAAM,CAAEF,WAAY,CAAC,CAAC,CAExE;AACAlC,SAAS,CAAC,IAAM,CACfsC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAE,CAAEX,cAAc,CAAEI,eAAgB,CAAC,CAAC,CAC5FK,SAAS,CAACT,cAAc,CAAC,CACzBO,cAAc,CAACH,eAAe,CAAC,CAChC,CAAC,CAAE,CAACJ,cAAc,CAAEI,eAAe,CAAC,CAAC,CAErC,KAAM,CAAAQ,kBAAkB,CAAIC,KAAU,EAAK,CAC1C,KAAM,CAAAC,SAAS,CAAGD,KAAK,CAACE,MAAM,CAACC,OAAO,CACtCP,SAAS,CAACK,SAAS,CAAC,CAEpB;AACA,GAAIA,SAAS,GAAK,KAAK,CAAE,CACxB;AACAP,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CAAC,CAED,KAAM,CAAAU,kBAAkB,CAAGA,CAAA,GAAM,CAChC;AACA,KAAM,CAAAC,WAAW,CAAGvC,cAAc,CAACwC,QAAQ,CAAC,CAAC,CAACD,WAAW,CAEzD;AACAA,WAAW,CACV,IAAM,CACL;AACAnB,iBAAiB,CAACS,MAAM,CAAC,CACzBH,kBAAkB,CAACC,WAAW,CAAC,CAChC,CAAC,CACD,sBAAsB,CACtB,0BACD,CAAC,CAED;AACAR,SAAS,CAAC,KAAK,CAAC,CAChBN,kBAAkB,CAAC,KAAK,CAAC,CACzBE,YAAY,CAAC,IAAI,CAAC,CAClBQ,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAkB,uBAAuB,CAAIP,KAAU,EAAK,CAC/C,KAAM,CAAAQ,cAAc,CAAGR,KAAK,CAACE,MAAM,CAACC,OAAO,CAC3CT,cAAc,CAACc,cAAc,CAAC,CAE9B;AACA,GAAIA,cAAc,GAAK,IAAI,CAAE,CAC5B;AACAZ,SAAS,CAAC,KAAK,CAAC,CACjB,CACA;AACA;AACD,CAAC,CAED,KAAM,CAAAa,WAAW,CAAGA,CAAA,GAAM,CACzBxB,SAAS,CAAC,KAAK,CAAC,CAChBN,kBAAkB,CAAC,KAAK,CAAC,CACzBE,YAAY,CAAC,IAAI,CAAC,CACnB,CAAC,CACD,GAAI,CAACG,MAAM,CAAE,MAAO,KAAI,CAExB,mBACChB,IAAA,QACC0C,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7B1C,KAAA,QAAKyC,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7B1C,KAAA,QAAKyC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnC5C,IAAA,CAACP,UAAU,EACV,aAAYsB,SAAS,CAAC,OAAO,CAAE,CAC/B8B,OAAO,CAAEJ,WAAY,CAAAG,QAAA,cAErB5C,IAAA,CAACJ,2BAA2B,GAAE,CAAC,CACpB,CAAC,cAEbM,KAAA,QAAKyC,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,GAAC,CAAErC,gBAAgB,GAAK,QAAQ,EAAIa,oBAAoB,GAAK,QAAQ,CAAIL,SAAS,CAAC,SAAS,CAAC,CAAGA,SAAS,CAAC,QAAQ,CAAC,EAAM,CAAC,cACvJf,IAAA,CAACP,UAAU,EACVqD,IAAI,CAAC,OAAO,CACZ,aAAY/B,SAAS,CAAC,OAAO,CAAE,CAC/B8B,OAAO,CAAEJ,WAAY,CAAAG,QAAA,cAErB5C,IAAA,CAACL,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAGNO,KAAA,QACCyC,SAAS,CAAC,gBACX;AAAA,CAAAC,QAAA,eAEC1C,KAAA,CAACV,GAAG,EAACmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC5C,IAAA,QACC2C,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAG9BrC,gBAAgB,GAAK,QAAQ,EAAIa,oBAAoB,GAAK,QAAQ,CAAGL,SAAS,CAAC,eAAe,CAAC,CAAGA,SAAS,CAAC,gBAAgB,CAAC,CAC1H,CAAC,cACNf,IAAA,QAAA4C,QAAA,cACC1C,KAAA,UAAOyC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAChC5C,IAAA,UACC+C,IAAI,CAAC,UAAU,CACfZ,OAAO,CAAER,MAAO,CAChBqB,QAAQ,CAAEjB,kBAAmB,CAC7BkB,IAAI,CAAC,cAAc,CACnBC,QAAQ,CAAE3C,gBAAgB,GAAK,MAAO,CACtC,CAAC,cACFP,IAAA,SAAM2C,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACJ,CAAC,EAEF,CAAC,CAyBJpC,gBAAgB,GAAK,QAAQ,EAAIa,oBAAoB,GAAK,QAAQ,EAAK,CAACO,MAAM,eAC/E3B,IAAA,CAAAI,SAAA,EAAAwC,QAAA,cACC1C,KAAA,CAACV,GAAG,EAACmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAEjC5C,IAAA,QACC2C,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAE9B7B,SAAS,CAAC,oBAAoB,CAAC,CAC5B,CAAC,cACNf,IAAA,QAAA4C,QAAA,cACC1C,KAAA,UAAOyC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAClD5C,IAAA,UACC+C,IAAI,CAAC,UAAU,CACfZ,OAAO,CAAEV,WAAY,CACrBuB,QAAQ,CAAET,uBAAwB,CAClCU,IAAI,CAAC,cAAc,CACnBC,QAAQ,CAAE3C,gBAAgB,GAAK,MAAO,CACtC,CAAC,cACFP,IAAA,SAAM2C,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACJ,CAAC,EACF,CAAC,CACL,CACF,EACG,CAAC,cAEN3C,IAAA,QAAK2C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClC5C,IAAA,CAACN,MAAM,EACNyD,OAAO,CAAC,WAAW,CACnBN,OAAO,CAAET,kBAAmB,CAC5BO,SAAS,CAAE,WAAY,CAAAC,QAAA,CAEtB7B,SAAS,CAAC,OAAO,CAAC,CACZ,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,CAER,CAAC,CAED,cAAe,CAAAV,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}