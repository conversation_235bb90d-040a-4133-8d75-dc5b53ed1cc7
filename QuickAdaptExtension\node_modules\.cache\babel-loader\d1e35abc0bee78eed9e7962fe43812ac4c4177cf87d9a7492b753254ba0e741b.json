{"ast": null, "code": "import React,{useState,useEffect,forwardRef,useRef,memo}from\"react\";import{Box,Tooltip,IconButton}from\"@mui/material\";import useDrawerStore from\"../../../../store/drawerStore\";import{copyicon,deleteicon}from\"../../../../assets/icons/icons\";import{useTranslation}from'react-i18next';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const RTEsection=/*#__PURE__*/forwardRef((_ref,ref)=>{var _toolTipGuideMetaData,_toolTipGuideMetaData2,_toolTipGuideMetaData3,_toolTipGuideMetaData4,_toolTipGuideMetaData5,_toolTipGuideMetaData6,_boxRef$current5,_boxRef$current5$inne,_boxRef$current6,_boxRef$current6$inne;let{items:{id,style,rteBoxValue,placeholder},boxRef,handleFocus,handleeBlur,isPopoverOpen,setIsPopoverOpen,currentRTEFocusedId}=_ref;const{t:translate}=useTranslation();const{setIsUnSavedChanges,setHtmlContent,textvaluess,setTextvaluess,backgroundC,setBackgroundC,Bbordercolor,BborderSize,bpadding,sectionColor,setSectionColor,handleTooltipRTEBlur,handleTooltipRTEValue,handleRTEDeleteSection,handleRTECloneSection,tooltip,currentStep,toolTipGuideMetaData}=useDrawerStore(state=>state);const[anchorEl,setAnchorEl]=useState(null);const[savedRange,setSaveRange]=useState(undefined);const[anchorPosition,setAnchorPosition]=useState({top:300,left:700});const[isPlaceholderVisible,setIsPlaceholderVisible]=useState(true);const[isEditing,setIsEditing]=useState(false);const editorRef=useRef(null);const containerRef=useRef(null);// const handleInput = () => {\n// \t// Update the content state when user types\n// \tif (boxRef.current) {\n// \t\tconst updatedContent = boxRef.current.innerHTML;\n// \t\tsetContent(updatedContent); // Store the content in state\n// \t\tsetHtmlContent(updatedContent); // Update the HTML content\n// \t\tsetIsUnSavedChanges(true);\n// \t\tpreserveCaretPosition();\n// \t}\n// };\nconst preserveCaretPosition=()=>{const selection=document.getSelection();if(selection){const range=selection.getRangeAt(0);// Get the current range (cursor position)\nsetSaveRange(range);// Save the current range for later restoration\n}};const restoreCaretPosition=()=>{if(savedRange&&boxRef.current){const selection=document.getSelection();if(selection){selection.removeAllRanges();selection.addRange(savedRange);// Restore the saved range\n}}};// useEffect(() => {\n// \t// After content update, restore the cursor position\n// \trestoreCaretPosition();\n// }, [boxRef.current?.innerHTML]); // Run when content changes\n// Remove section\n// useEffect(() => {\n// \tif (boxRef.current?.innerHTML?.trim()) {\n// \t\tsetIsUnSavedChanges(true);\n// \t}\n// }, [boxRef.current?.innerHTML?.trim()]);\nuseEffect(()=>{var _boxRef$current,_boxRef$current2,_boxRef$current3;if(rteBoxValue&&(_boxRef$current=boxRef.current)!==null&&_boxRef$current!==void 0&&_boxRef$current.innerHTML&&((_boxRef$current2=boxRef.current)===null||_boxRef$current2===void 0?void 0:_boxRef$current2.innerHTML)!==\"<p><br></p>\"){// @ts-ignore\nboxRef.current.innerHTML=rteBoxValue;setIsPlaceholderVisible(false);}else if(!textvaluess&&(_boxRef$current3=boxRef.current)!==null&&_boxRef$current3!==void 0&&_boxRef$current3.innerHTML){// @ts-ignore\nboxRef.current.innerHTML=\"\";setIsPlaceholderVisible(true);}},[rteBoxValue,boxRef.current]);// Auto-focus the editor when editing mode is activated\nuseEffect(()=>{if(isEditing&&editorRef.current){setTimeout(()=>{editorRef.current.editor.focus();},50);}},[isEditing]);// Handle clicks outside the editor to close editing mode\nuseEffect(()=>{const handleClickOutside=event=>{var _document$querySelect,_document$querySelect2,_document$querySelect3,_document$querySelect4;const isInsideJoditPopupContent=event.target.closest(\".jodit-popup__content\")!==null;const isInsideAltTextPopup=event.target.closest(\".jodit-ui-input\")!==null;const isInsidePopup=(_document$querySelect=document.querySelector(\".jodit-popup\"))===null||_document$querySelect===void 0?void 0:_document$querySelect.contains(event.target);const isInsideJoditPopup=(_document$querySelect2=document.querySelector(\".jodit-wysiwyg\"))===null||_document$querySelect2===void 0?void 0:_document$querySelect2.contains(event.target);const isInsideWorkplacePopup=isInsideJoditPopup||((_document$querySelect3=document.querySelector(\".jodit-dialog__panel\"))===null||_document$querySelect3===void 0?void 0:_document$querySelect3.contains(event.target));const isSelectionMarker=event.target.id.startsWith(\"jodit-selection_marker_\");const isLinkPopup=(_document$querySelect4=document.querySelector(\".jodit-ui-input__input\"))===null||_document$querySelect4===void 0?void 0:_document$querySelect4.contains(event.target);const isInsideToolbarButton=event.target.closest(\".jodit-toolbar-button__button\")!==null;const isInsertButton=event.target.closest(\"button[aria-pressed='false']\")!==null;// Check if the target is inside the editor or related elements\nif(containerRef.current&&!containerRef.current.contains(event.target)&&// Click outside the editor container\n!isInsidePopup&&// Click outside the popup\n!isInsideJoditPopup&&// Click outside the WYSIWYG editor\n!isInsideWorkplacePopup&&// Click outside the workplace popup\n!isSelectionMarker&&// Click outside selection markers\n!isLinkPopup&&// Click outside link input popup\n!isInsideToolbarButton&&// Click outside the toolbar button\n!isInsertButton&&!isInsideJoditPopupContent&&!isInsideAltTextPopup){setIsEditing(false);// Close the editor if clicked outside\n}};if(isEditing){document.addEventListener(\"mousedown\",handleClickOutside);return()=>document.removeEventListener(\"mousedown\",handleClickOutside);}},[isEditing]);return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",position:\"relative\",//padding: 0,\nmargin:0,boxSizing:\"border-box\",transition:\"border 0.2s ease-in-out\",backgroundColor:sectionColor||\"defaultColor\"//border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\n// padding: `${bpadding}px !important` || \"0\",\n},className:\"qadpt-rte\",id:\"rte-box\",children:[/*#__PURE__*/_jsx(Tooltip,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleRTEDeleteSection(id),disabled:((_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData===void 0?void 0:(_toolTipGuideMetaData2=_toolTipGuideMetaData.containers)===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.length)===1,sx:{\"&:hover\":{backgroundColor:\"transparent !important\"},svg:{path:{fill:\"var(--primarycolor)\"}}},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon},style:{opacity:((_toolTipGuideMetaData3=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData3===void 0?void 0:(_toolTipGuideMetaData4=_toolTipGuideMetaData3.containers)===null||_toolTipGuideMetaData4===void 0?void 0:_toolTipGuideMetaData4.length)===1?0.5:1,pointerEvents:'none',height:\"24px\"}})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleRTECloneSection(id),sx:{\"&:hover\":{backgroundColor:\"transparent !important\"},svg:{height:\"24px\",path:{fill:\"var(--primarycolor)\"}}},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon},style:{opacity:((_toolTipGuideMetaData5=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData5===void 0?void 0:(_toolTipGuideMetaData6=_toolTipGuideMetaData5.containers)===null||_toolTipGuideMetaData6===void 0?void 0:_toolTipGuideMetaData6.length)===1?0.5:1,pointerEvents:'none',height:\"24px\"}})})]}),placement:\"top\",slotProps:{tooltip:{sx:{backgroundColor:\"white\",color:\"black\",borderRadius:\"4px\",padding:'0px 4px',border:\"1px dashed var(--primarycolor)\"}}},PopperProps:{modifiers:[{name:\"preventOverflow\",options:{boundary:\"viewport\"// Ensure tooltip doesn't go outside the viewport\n}},{name:\"flip\",options:{enabled:true}}]},children:/*#__PURE__*/_jsx(Box,{contentEditable:true,ref:boxRef,component:\"div\",id:`rt-editor${id}`,onClick:e=>{// Immediately activate editing mode and focus on first click\nhandleFocus(id);setIsPlaceholderVisible(false);// Focus the contentEditable element to make it ready for typing\nsetTimeout(()=>{if(boxRef.current){boxRef.current.focus();}},0);},onFocus:e=>{handleFocus(id);setIsPlaceholderVisible(false);},onBlur:e=>{var _boxRef$current4,_boxRef$current4$inne;handleeBlur(id);if(!((_boxRef$current4=boxRef.current)!==null&&_boxRef$current4!==void 0&&(_boxRef$current4$inne=_boxRef$current4.innerHTML)!==null&&_boxRef$current4$inne!==void 0&&_boxRef$current4$inne.trim())){setIsPlaceholderVisible(true);}}//onInput={handleInput}\n,sx:{height:\"100%\",width:\"100%\",padding:\"8px\",outline:\"none\",overflowY:\"auto\",color:isPlaceholderVisible&&!((_boxRef$current5=boxRef.current)!==null&&_boxRef$current5!==void 0&&(_boxRef$current5$inne=_boxRef$current5.innerHTML)!==null&&_boxRef$current5$inne!==void 0&&_boxRef$current5$inne.trim())?\"transparent\":\"black\",position:\"relative\",cursor:\"text\"// Add cursor pointer to indicate it's clickable\n//backgroundColor: backgroundC || \"defaultColor\",\n},suppressContentEditableWarning:true// Set content via state using dangerouslySetInnerHTML\n,dangerouslySetInnerHTML:{__html:rteBoxValue}})}),isPlaceholderVisible&&!((_boxRef$current6=boxRef.current)!==null&&_boxRef$current6!==void 0&&(_boxRef$current6$inne=_boxRef$current6.innerHTML)!==null&&_boxRef$current6$inne!==void 0&&_boxRef$current6$inne.trim())&&/*#__PURE__*/_jsx(\"span\",{style:{position:\"absolute\",color:\"gray\",pointerEvents:\"none\",userSelect:\"none\",textAlign:\"center\",whiteSpace:\"nowrap\",transform:\"translate(-50%,-50%)\",top:\"50%\",left:\"50%\",padding:\"8px\"// outline: \"none\",\n},id:\"rte-placeholder\",children:translate(placeholder)})]})});});export default/*#__PURE__*/memo(RTEsection);", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useRef", "memo", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "useDrawerStore", "copyicon", "deleteicon", "useTranslation", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "RTEsection", "_ref", "ref", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_boxRef$current5", "_boxRef$current5$inne", "_boxRef$current6", "_boxRef$current6$inne", "items", "id", "style", "rteBoxValue", "placeholder", "boxRef", "handleFocus", "handleeBlur", "isPopoverOpen", "setIsPopoverOpen", "currentRTEFocusedId", "t", "translate", "setIsUnSavedChanges", "setHtmlContent", "textvaluess", "setTextvaluess", "backgroundC", "setBackgroundC", "Bbordercolor", "BborderSize", "bpadding", "sectionColor", "setSectionColor", "handleTooltipRTEBlur", "handleTooltipRTEValue", "handleRTEDeleteSection", "handleRTECloneSection", "tooltip", "currentStep", "toolTipGuideMetaData", "state", "anchorEl", "setAnchorEl", "savedRange", "setSaveRange", "undefined", "anchorPosition", "setAnchorPosition", "top", "left", "isPlaceholderVisible", "setIsPlaceholderVisible", "isEditing", "setIsEditing", "editor<PERSON><PERSON>", "containerRef", "preserveCaretPosition", "selection", "document", "getSelection", "range", "getRangeAt", "restoreCaretPosition", "current", "removeAllRanges", "addRange", "_boxRef$current", "_boxRef$current2", "_boxRef$current3", "innerHTML", "setTimeout", "editor", "focus", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "addEventListener", "removeEventListener", "children", "sx", "display", "alignItems", "position", "margin", "boxSizing", "transition", "backgroundColor", "className", "title", "size", "onClick", "disabled", "containers", "length", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "opacity", "pointerEvents", "height", "placement", "slotProps", "color", "borderRadius", "padding", "border", "PopperProps", "modifiers", "name", "options", "boundary", "enabled", "contentEditable", "component", "e", "onFocus", "onBlur", "_boxRef$current4", "_boxRef$current4$inne", "trim", "width", "outline", "overflowY", "cursor", "suppressContentEditableWarning", "userSelect", "textAlign", "whiteSpace", "transform"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/RTE/RTESection.tsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo } from \"react\";\r\nimport { Box, Popover, Tooltip, Typography, IconButton } from \"@mui/material\";\r\n\r\nimport RTE from \"./RTE\";\r\nimport useDrawerStore, { IRTEContainer, TSectionType } from \"../../../../store/drawerStore\";\r\nimport { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from \"@mui/icons-material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { copyicon, deleteicon } from \"../../../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n\titems: IRTEContainer;\r\n\tboxRef: React.RefObject<HTMLDivElement>;\r\n\thandleFocus: (id: string) => void;\r\n\thandleeBlur: (id: string) => void;\r\n\r\n\tisPopoverOpen: boolean;\r\n\tsetIsPopoverOpen: (params: boolean) => void;\r\n\tcurrentRTEFocusedId: string;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n\t(\r\n\t\t{\r\n\t\t\titems: { id, style, rteBoxValue, placeholder },\r\n\t\t\tboxRef,\r\n\t\t\thandleFocus,\r\n\t\t\thandleeBlur,\r\n\r\n\t\t\tisPopoverOpen,\r\n\t\t\tsetIsPopoverOpen,\r\n\t\t\tcurrentRTEFocusedId,\r\n\t\t},\r\n\t\tref\r\n\t) => {\r\n\t\tconst { t: translate } = useTranslation();\r\n\t\tconst {\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tsetHtmlContent,\r\n\t\t\ttextvaluess,\r\n\t\t\tsetTextvaluess,\r\n\t\t\tbackgroundC,\r\n\t\t\tsetBackgroundC,\r\n\t\t\tBbordercolor,\r\n\t\t\tBborderSize,\r\n\t\t\tbpadding,\r\n\t\t\tsectionColor,\r\n\t\t\tsetSectionColor,\r\n\t\t\thandleTooltipRTEBlur,\r\n\t\t\thandleTooltipRTEValue,\r\n\t\t\thandleRTEDeleteSection,\r\n\t\t\thandleRTECloneSection,\r\n\t\t\ttooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t} = useDrawerStore((state) => state);\r\n\t\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\t\tconst [savedRange, setSaveRange] = useState<Range | undefined>(undefined);\r\n\t\tconst [anchorPosition, setAnchorPosition] = useState<{ top: number; left: number }>({ top: 300, left: 700 });\r\n\t\tconst [isPlaceholderVisible, setIsPlaceholderVisible] = useState(true);\r\n\t\tconst [isEditing, setIsEditing] = useState(false);\r\n\t\tconst editorRef = useRef(null);\r\n\t\tconst containerRef = useRef<HTMLDivElement | null>(null);\r\n\r\n\t\t// const handleInput = () => {\r\n\t\t// \t// Update the content state when user types\r\n\t\t// \tif (boxRef.current) {\r\n\t\t// \t\tconst updatedContent = boxRef.current.innerHTML;\r\n\t\t// \t\tsetContent(updatedContent); // Store the content in state\r\n\t\t// \t\tsetHtmlContent(updatedContent); // Update the HTML content\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t\tpreserveCaretPosition();\r\n\t\t// \t}\r\n\t\t// };\r\n\t\tconst preserveCaretPosition = () => {\r\n\t\t\tconst selection = document.getSelection();\r\n\t\t\tif (selection) {\r\n\t\t\t\tconst range = selection.getRangeAt(0); // Get the current range (cursor position)\r\n\t\t\t\tsetSaveRange(range); // Save the current range for later restoration\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst restoreCaretPosition = () => {\r\n\t\t\tif (savedRange && boxRef.current) {\r\n\t\t\t\tconst selection = document.getSelection();\r\n\t\t\t\tif (selection) {\r\n\t\t\t\t\tselection.removeAllRanges();\r\n\t\t\t\t\tselection.addRange(savedRange); // Restore the saved range\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \t// After content update, restore the cursor position\r\n\t\t// \trestoreCaretPosition();\r\n\t\t// }, [boxRef.current?.innerHTML]); // Run when content changes\r\n\r\n\t\t// Remove section\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \tif (boxRef.current?.innerHTML?.trim()) {\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t}\r\n\t\t// }, [boxRef.current?.innerHTML?.trim()]);\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tif (rteBoxValue && boxRef.current?.innerHTML && boxRef.current?.innerHTML !== \"<p><br></p>\") {\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tboxRef.current.innerHTML = rteBoxValue;\r\n\t\t\t\tsetIsPlaceholderVisible(false);\r\n\t\t\t} else if (!textvaluess && boxRef.current?.innerHTML) {\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tboxRef.current.innerHTML = \"\";\r\n\t\t\t\tsetIsPlaceholderVisible(true);\r\n\t\t\t}\r\n\t\t}, [rteBoxValue, boxRef.current]);\r\n\r\n\t\t// Auto-focus the editor when editing mode is activated\r\n\t\tuseEffect(() => {\r\n\t\t\tif (isEditing && editorRef.current) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t(editorRef.current as any).editor.focus();\r\n\t\t\t\t}, 50);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\t// Handle clicks outside the editor to close editing mode\r\n\t\tuseEffect(() => {\r\n\t\t\tconst handleClickOutside = (event: MouseEvent) => {\r\n\t\t\t\tconst isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n\t\t\t\tconst isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n\t\t\t\tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n\t\t\t\tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n\t\t\t\t// Check if the target is inside the editor or related elements\r\n\t\t\t\tif (\r\n\t\t\t\t\tcontainerRef.current &&\r\n\t\t\t\t\t!containerRef.current.contains(event.target as Node) && // Click outside the editor container\r\n\t\t\t\t\t!isInsidePopup && // Click outside the popup\r\n\t\t\t\t\t!isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n\t\t\t\t\t!isInsideWorkplacePopup && // Click outside the workplace popup\r\n\t\t\t\t\t!isSelectionMarker && // Click outside selection markers\r\n\t\t\t\t\t!isLinkPopup && // Click outside link input popup\r\n\t\t\t\t\t!isInsideToolbarButton &&// Click outside the toolbar button\r\n\t\t\t\t\t!isInsertButton &&\r\n\t\t\t\t\t!isInsideJoditPopupContent &&\r\n\t\t\t\t\t!isInsideAltTextPopup\r\n\t\t\t\t) {\r\n\t\t\t\t\tsetIsEditing(false); // Close the editor if clicked outside\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tif (isEditing) {\r\n\t\t\t\tdocument.addEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t\treturn () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t//padding: 0,\r\n\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\ttransition: \"border 0.2s ease-in-out\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor || \"defaultColor\",\r\n\t\t\t\t\t\t//border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\r\n\t\t\t\t\t\t// padding: `${bpadding}px !important` || \"0\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tclassName=\"qadpt-rte\"\r\n\t\t\t\t\tid=\"rte-box\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\ttitle={\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleRTEDeleteSection(id)}\r\n\t\t\t\t\t\t\t\t\tdisabled={\r\n\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleRTECloneSection(id)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t}}/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tplacement=\"top\"\r\n\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"white\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\tpadding: '0px 4px',\r\n\t\t\t\t\t\t\t\t\tborder: \"1px dashed var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\tmodifiers: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tname: \"preventOverflow\",\r\n\t\t\t\t\t\t\t\t\toptions: {\r\n\t\t\t\t\t\t\t\t\t\tboundary: \"viewport\", // Ensure tooltip doesn't go outside the viewport\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tname: \"flip\",\r\n\t\t\t\t\t\t\t\t\toptions: {\r\n\t\t\t\t\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tcontentEditable\r\n\t\t\t\t\t\t\tref={boxRef}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={`rt-editor${id}`}\r\n\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t// Immediately activate editing mode and focus on first click\r\n\t\t\t\t\t\t\t\thandleFocus(id);\r\n\t\t\t\t\t\t\t\tsetIsPlaceholderVisible(false);\r\n\t\t\t\t\t\t\t\t// Focus the contentEditable element to make it ready for typing\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tif (boxRef.current) {\r\n\t\t\t\t\t\t\t\t\t\tboxRef.current.focus();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}, 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonFocus={(e) => {\r\n\t\t\t\t\t\t\t\thandleFocus(id);\r\n\t\t\t\t\t\t\t\tsetIsPlaceholderVisible(false);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonBlur={(e) => {\r\n\t\t\t\t\t\t\t\thandleeBlur(id);\r\n\t\t\t\t\t\t\t\tif (!boxRef.current?.innerHTML?.trim()) {\r\n\t\t\t\t\t\t\t\t\tsetIsPlaceholderVisible(true);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t//onInput={handleInput}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\toutline: \"none\",\r\n\t\t\t\t\t\t\t\toverflowY: \"auto\",\r\n\t\t\t\t\t\t\t\tcolor: isPlaceholderVisible && !boxRef.current?.innerHTML?.trim() ? \"transparent\" : \"black\",\r\n\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\tcursor: \"text\", // Add cursor pointer to indicate it's clickable\r\n\t\t\t\t\t\t\t\t//backgroundColor: backgroundC || \"defaultColor\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tsuppressContentEditableWarning={true}\r\n\t\t\t\t\t\t\t// Set content via state using dangerouslySetInnerHTML\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: rteBoxValue }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t{isPlaceholderVisible && !boxRef.current?.innerHTML?.trim() && (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\tcolor: \"gray\",\r\n\t\t\t\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\t\t\t\tuserSelect: \"none\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\ttransform: \"translate(-50%,-50%)\",\r\n\t\t\t\t\t\t\t\ttop: \"50%\",\r\n\t\t\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t// outline: \"none\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tid=\"rte-placeholder\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(placeholder)}\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</Box>\r\n\t\t\t</>\r\n\t\t);\r\n\t}\r\n);\r\n\r\nexport default memo(RTEsection);\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,CAAEC,MAAM,CAAaC,IAAI,KAAiB,OAAO,CAChG,OAASC,GAAG,CAAWC,OAAO,CAAcC,UAAU,KAAQ,eAAe,CAG7E,MAAO,CAAAC,cAAc,KAAuC,+BAA+B,CAI3F,OAASC,QAAQ,CAAEC,UAAU,KAAQ,gCAAgC,CACrE,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAa/C,KAAM,CAAAC,UAAqC,cAAGhB,UAAU,CACvD,CAAAiB,IAAA,CAWCC,GAAG,GACC,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,qBAAA,IAXJ,CACCC,KAAK,CAAE,CAAEC,EAAE,CAAEC,KAAK,CAAEC,WAAW,CAAEC,WAAY,CAAC,CAC9CC,MAAM,CACNC,WAAW,CACXC,WAAW,CAEXC,aAAa,CACbC,gBAAgB,CAChBC,mBACD,CAAC,CAAAtB,IAAA,CAGD,KAAM,CAAEuB,CAAC,CAAEC,SAAU,CAAC,CAAGhC,cAAc,CAAC,CAAC,CACzC,KAAM,CACLiC,mBAAmB,CACnBC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,YAAY,CACZC,WAAW,CACXC,QAAQ,CACRC,YAAY,CACZC,eAAe,CACfC,oBAAoB,CACpBC,qBAAqB,CACrBC,sBAAsB,CACtBC,qBAAqB,CACrBC,OAAO,CACPC,WAAW,CACXC,oBACD,CAAC,CAAGrD,cAAc,CAAEsD,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGhE,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAACiE,UAAU,CAAEC,YAAY,CAAC,CAAGlE,QAAQ,CAAoBmE,SAAS,CAAC,CACzE,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGrE,QAAQ,CAAgC,CAAEsE,GAAG,CAAE,GAAG,CAAEC,IAAI,CAAE,GAAI,CAAC,CAAC,CAC5G,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGzE,QAAQ,CAAC,IAAI,CAAC,CACtE,KAAM,CAAC0E,SAAS,CAAEC,YAAY,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAA4E,SAAS,CAAGzE,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAA0E,YAAY,CAAG1E,MAAM,CAAwB,IAAI,CAAC,CAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAA2E,qBAAqB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAAC,SAAS,CAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC,CACzC,GAAIF,SAAS,CAAE,CACd,KAAM,CAAAG,KAAK,CAAGH,SAAS,CAACI,UAAU,CAAC,CAAC,CAAC,CAAE;AACvCjB,YAAY,CAACgB,KAAK,CAAC,CAAE;AACtB,CACD,CAAC,CAED,KAAM,CAAAE,oBAAoB,CAAGA,CAAA,GAAM,CAClC,GAAInB,UAAU,EAAI7B,MAAM,CAACiD,OAAO,CAAE,CACjC,KAAM,CAAAN,SAAS,CAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC,CACzC,GAAIF,SAAS,CAAE,CACdA,SAAS,CAACO,eAAe,CAAC,CAAC,CAC3BP,SAAS,CAACQ,QAAQ,CAACtB,UAAU,CAAC,CAAE;AACjC,CACD,CACD,CAAC,CAED;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEAhE,SAAS,CAAC,IAAM,KAAAuF,eAAA,CAAAC,gBAAA,CAAAC,gBAAA,CACf,GAAIxD,WAAW,GAAAsD,eAAA,CAAIpD,MAAM,CAACiD,OAAO,UAAAG,eAAA,WAAdA,eAAA,CAAgBG,SAAS,EAAI,EAAAF,gBAAA,CAAArD,MAAM,CAACiD,OAAO,UAAAI,gBAAA,iBAAdA,gBAAA,CAAgBE,SAAS,IAAK,aAAa,CAAE,CAC5F;AACAvD,MAAM,CAACiD,OAAO,CAACM,SAAS,CAAGzD,WAAW,CACtCuC,uBAAuB,CAAC,KAAK,CAAC,CAC/B,CAAC,IAAM,IAAI,CAAC3B,WAAW,GAAA4C,gBAAA,CAAItD,MAAM,CAACiD,OAAO,UAAAK,gBAAA,WAAdA,gBAAA,CAAgBC,SAAS,CAAE,CACrD;AACAvD,MAAM,CAACiD,OAAO,CAACM,SAAS,CAAG,EAAE,CAC7BlB,uBAAuB,CAAC,IAAI,CAAC,CAC9B,CACD,CAAC,CAAE,CAACvC,WAAW,CAAEE,MAAM,CAACiD,OAAO,CAAC,CAAC,CAEjC;AACApF,SAAS,CAAC,IAAM,CACf,GAAIyE,SAAS,EAAIE,SAAS,CAACS,OAAO,CAAE,CACnCO,UAAU,CAAC,IAAM,CACfhB,SAAS,CAACS,OAAO,CAASQ,MAAM,CAACC,KAAK,CAAC,CAAC,CAC1C,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CAAC,CAAE,CAACpB,SAAS,CAAC,CAAC,CAEf;AACAzE,SAAS,CAAC,IAAM,CACf,KAAM,CAAA8F,kBAAkB,CAAIC,KAAiB,EAAK,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACjD,KAAM,CAAAC,yBAAyB,CAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,GAAK,IAAI,CACzG,KAAM,CAAAC,oBAAoB,CAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,GAAK,IAAI,CAC9F,KAAM,CAAAE,aAAa,EAAAR,qBAAA,CAAGjB,QAAQ,CAAC0B,aAAa,CAAC,cAAc,CAAC,UAAAT,qBAAA,iBAAtCA,qBAAA,CAAwCU,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC,CAC5F,KAAM,CAAAM,kBAAkB,EAAAV,sBAAA,CAAGlB,QAAQ,CAAC0B,aAAa,CAAC,gBAAgB,CAAC,UAAAR,sBAAA,iBAAxCA,sBAAA,CAA0CS,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC,CACnG,KAAM,CAAAO,sBAAsB,CAAGD,kBAAkB,IAAAT,sBAAA,CAAInB,QAAQ,CAAC0B,aAAa,CAAC,sBAAsB,CAAC,UAAAP,sBAAA,iBAA9CA,sBAAA,CAAgDQ,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC,EACnI,KAAM,CAAAQ,iBAAiB,CAAId,KAAK,CAACM,MAAM,CAAiBtE,EAAE,CAAC+E,UAAU,CAAC,yBAAyB,CAAC,CAChG,KAAM,CAAAC,WAAW,EAAAZ,sBAAA,CAAGpB,QAAQ,CAAC0B,aAAa,CAAC,wBAAwB,CAAC,UAAAN,sBAAA,iBAAhDA,sBAAA,CAAkDO,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC,CACpG,KAAM,CAAAW,qBAAqB,CAAIjB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,GAAK,IAAI,CAC7G,KAAM,CAAAW,cAAc,CAAIlB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,GAAK,IAAI,CAErG;AACA,GACC1B,YAAY,CAACQ,OAAO,EACpB,CAACR,YAAY,CAACQ,OAAO,CAACsB,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC,EAAI;AACxD,CAACG,aAAa,EAAI;AAClB,CAACG,kBAAkB,EAAI;AACvB,CAACC,sBAAsB,EAAI;AAC3B,CAACC,iBAAiB,EAAI;AACtB,CAACE,WAAW,EAAI;AAChB,CAACC,qBAAqB,EAAG;AACzB,CAACC,cAAc,EACf,CAACb,yBAAyB,EAC1B,CAACG,oBAAoB,CACpB,CACD7B,YAAY,CAAC,KAAK,CAAC,CAAE;AACtB,CACD,CAAC,CAED,GAAID,SAAS,CAAE,CACdM,QAAQ,CAACmC,gBAAgB,CAAC,WAAW,CAAEpB,kBAAkB,CAAC,CAC1D,MAAO,IAAMf,QAAQ,CAACoC,mBAAmB,CAAC,WAAW,CAAErB,kBAAkB,CAAC,CAC3E,CACD,CAAC,CAAE,CAACrB,SAAS,CAAC,CAAC,CAEf,mBACC7D,IAAA,CAAAE,SAAA,EAAAsG,QAAA,cACCpG,KAAA,CAACZ,GAAG,EACHiH,EAAE,CAAE,CACHC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,UAAU,CACpB;AACAC,MAAM,CAAE,CAAC,CACTC,SAAS,CAAE,YAAY,CACvBC,UAAU,CAAE,yBAAyB,CACrCC,eAAe,CAAExE,YAAY,EAAI,cACjC;AACA;AACD,CAAE,CACFyE,SAAS,CAAC,WAAW,CACrB9F,EAAE,CAAC,SAAS,CAAAqF,QAAA,eAEZxG,IAAA,CAACP,OAAO,EACPyH,KAAK,cACJ9G,KAAA,CAAAF,SAAA,EAAAsG,QAAA,eACCxG,IAAA,CAACN,UAAU,EACVyH,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAMxE,sBAAsB,CAACzB,EAAE,CAAE,CAC1CkG,QAAQ,CACP,EAAA7G,qBAAA,CAAAwC,oBAAoB,CAACD,WAAW,CAAG,CAAC,CAAC,UAAAvC,qBAAA,kBAAAC,sBAAA,CAArCD,qBAAA,CAAuC8G,UAAU,UAAA7G,sBAAA,iBAAjDA,sBAAA,CAAmD8G,MAAM,IAAK,CAC9D,CACDd,EAAE,CAAE,CACH,SAAS,CAAE,CACVO,eAAe,CAAE,wBAClB,CAAC,CACDQ,GAAG,CAAE,CACJC,IAAI,CAAE,CACLC,IAAI,CAAC,qBACN,CACD,CACD,CAAE,CAAAlB,QAAA,cAEFxG,IAAA,SACC2H,uBAAuB,CAAE,CAAEC,MAAM,CAAE/H,UAAW,CAAE,CAChDuB,KAAK,CAAE,CACNyG,OAAO,CACN,EAAAnH,sBAAA,CAAAsC,oBAAoB,CAACD,WAAW,CAAG,CAAC,CAAC,UAAArC,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuC4G,UAAU,UAAA3G,sBAAA,iBAAjDA,sBAAA,CAAmD4G,MAAM,IAAK,CAAC,CAC5D,GAAG,CACH,CAAC,CACLO,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MACT,CAAE,CACF,CAAC,CACS,CAAC,cACb/H,IAAA,CAACN,UAAU,EACVyH,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAMvE,qBAAqB,CAAC1B,EAAE,CAAE,CACzCsF,EAAE,CAAE,CACH,SAAS,CAAE,CACVO,eAAe,CAAE,wBAClB,CAAC,CACDQ,GAAG,CAAE,CACJO,MAAM,CAAE,MAAM,CACdN,IAAI,CAAE,CACLC,IAAI,CAAC,qBACN,CACD,CACD,CAAE,CAAAlB,QAAA,cAEFxG,IAAA,SAAM2H,uBAAuB,CAAE,CAAEC,MAAM,CAAEhI,QAAS,CAAE,CACpDwB,KAAK,CAAE,CACNyG,OAAO,CACN,EAAAjH,sBAAA,CAAAoC,oBAAoB,CAACD,WAAW,CAAG,CAAC,CAAC,UAAAnC,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuC0G,UAAU,UAAAzG,sBAAA,iBAAjDA,sBAAA,CAAmD0G,MAAM,IAAK,CAAC,CAC5D,GAAG,CACH,CAAC,CACLO,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,MACT,CAAE,CAAC,CAAC,CACO,CAAC,EACZ,CACF,CACDC,SAAS,CAAC,KAAK,CACfC,SAAS,CAAE,CACVnF,OAAO,CAAE,CACR2D,EAAE,CAAE,CACHO,eAAe,CAAE,OAAO,CACxBkB,KAAK,CAAE,OAAO,CACdC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,gCACT,CACD,CACD,CAAE,CACFC,WAAW,CAAE,CACZC,SAAS,CAAE,CACV,CACCC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,CACRC,QAAQ,CAAE,UAAY;AACvB,CACD,CAAC,CACD,CACCF,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,CACRE,OAAO,CAAE,IACV,CACD,CAAC,CAEH,CAAE,CAAAnC,QAAA,cAEFxG,IAAA,CAACR,GAAG,EACHoJ,eAAe,MACfrI,GAAG,CAAEgB,MAAO,CACZsH,SAAS,CAAE,KAAM,CACjB1H,EAAE,CAAE,YAAYA,EAAE,EAAG,CACrBiG,OAAO,CAAG0B,CAAC,EAAK,CACf;AACAtH,WAAW,CAACL,EAAE,CAAC,CACfyC,uBAAuB,CAAC,KAAK,CAAC,CAC9B;AACAmB,UAAU,CAAC,IAAM,CAChB,GAAIxD,MAAM,CAACiD,OAAO,CAAE,CACnBjD,MAAM,CAACiD,OAAO,CAACS,KAAK,CAAC,CAAC,CACvB,CACD,CAAC,CAAE,CAAC,CAAC,CACN,CAAE,CACF8D,OAAO,CAAGD,CAAC,EAAK,CACftH,WAAW,CAACL,EAAE,CAAC,CACfyC,uBAAuB,CAAC,KAAK,CAAC,CAC/B,CAAE,CACFoF,MAAM,CAAGF,CAAC,EAAK,KAAAG,gBAAA,CAAAC,qBAAA,CACdzH,WAAW,CAACN,EAAE,CAAC,CACf,GAAI,GAAA8H,gBAAA,CAAC1H,MAAM,CAACiD,OAAO,UAAAyE,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgBnE,SAAS,UAAAoE,qBAAA,WAAzBA,qBAAA,CAA2BC,IAAI,CAAC,CAAC,EAAE,CACvCvF,uBAAuB,CAAC,IAAI,CAAC,CAC9B,CACD,CACA;AAAA,CACA6C,EAAE,CAAE,CACHsB,MAAM,CAAE,MAAM,CACdqB,KAAK,CAAE,MAAM,CACbhB,OAAO,CAAE,KAAK,CACdiB,OAAO,CAAE,MAAM,CACfC,SAAS,CAAE,MAAM,CACjBpB,KAAK,CAAEvE,oBAAoB,EAAI,GAAA7C,gBAAA,CAACS,MAAM,CAACiD,OAAO,UAAA1D,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgBgE,SAAS,UAAA/D,qBAAA,WAAzBA,qBAAA,CAA2BoI,IAAI,CAAC,CAAC,EAAG,aAAa,CAAG,OAAO,CAC3FvC,QAAQ,CAAE,UAAU,CACpB2C,MAAM,CAAE,MAAQ;AAChB;AACD,CAAE,CACFC,8BAA8B,CAAE,IAChC;AAAA,CACA7B,uBAAuB,CAAE,CAAEC,MAAM,CAAEvG,WAAY,CAAE,CACjD,CAAC,CACM,CAAC,CACTsC,oBAAoB,EAAI,GAAA3C,gBAAA,CAACO,MAAM,CAACiD,OAAO,UAAAxD,gBAAA,YAAAC,qBAAA,CAAdD,gBAAA,CAAgB8D,SAAS,UAAA7D,qBAAA,WAAzBA,qBAAA,CAA2BkI,IAAI,CAAC,CAAC,gBAC1DnJ,IAAA,SACCoB,KAAK,CAAE,CACNwF,QAAQ,CAAE,UAAU,CACpBsB,KAAK,CAAE,MAAM,CACbJ,aAAa,CAAE,MAAM,CACrB2B,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,QAAQ,CACnBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,sBAAsB,CACjCnG,GAAG,CAAE,KAAK,CACVC,IAAI,CAAE,KAAK,CACX0E,OAAO,CAAE,KACT;AACD,CAAE,CACFjH,EAAE,CAAC,iBAAiB,CAAAqF,QAAA,CAEnB1E,SAAS,CAACR,WAAW,CAAC,CAClB,CACN,EACG,CAAC,CACL,CAAC,CAEL,CACD,CAAC,CAED,2BAAe/B,IAAI,CAACc,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}