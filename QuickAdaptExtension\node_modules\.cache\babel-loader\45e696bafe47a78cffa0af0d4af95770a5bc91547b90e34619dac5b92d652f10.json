{"ast": null, "code": "import React,{useState}from'react';import useDrawerStore from'../../store/drawerStore';import{ToursAnnouncementsIcon,ToursBannerIcon,ToursHotspotIcon,ToursTooltipIcon}from'../../assets/icons/icons';import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FeatureSelectionModal=props=>{const{t:translate}=useTranslation();const{isOpen,onClose,guideName,setStepData,stepData,count}=props;const[hoveredItem,setHoveredItem]=useState();const[selectedStepType,setSelectedStepType]=useState(null);// Track selected step type\nconst{setSelectedTemplate,setBannerPopup,setSelectedTemplateTour,setSteps,steps,setTooltipCount,tooltipCount,HotspotGuideDetails,setElementSelected,TooltipGuideDetails,HotspotGuideDetailsNew,setSelectedStepTypeHotspot,selectedTemplate,selectedTemplateTour,createWithAI}=useDrawerStore(state=>state);const[selectedStepStyle,setSelectedStepStyle]=useState({});const features=[{icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:ToursAnnouncementsIcon}}),title:\"Announcement\",description:translate(\"An announcement of any new feature\",{defaultValue:\"An announcement of any new feature\"}),action:()=>{setSelectedStepType(\"Announcement\");setSelectedStepStyle({borderColor:\"#5F9EA0\",background:\"#F6FFFF\"});}},{icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:ToursHotspotIcon}}),title:\"Hotspot\",description:translate(\"Offer users quick tips\",{defaultValue:\"Offer users quick tips\"}),action:()=>{setSelectedStepType(\"Hotspot\");}},{icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:ToursBannerIcon}}),title:\"Banner\",description:translate(\"Create in-line banners that get noticed\",{defaultValue:\"Create in-line banners that get noticed\"}),action:()=>{setSelectedStepType(\"Banner\");}},{icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:ToursTooltipIcon}}),title:\"Tooltip\",description:translate(\"Anchored to selected elements\",{defaultValue:\"Anchored to selected elements\"}),action:()=>{setSelectedStepType(\"Tooltip\");}}];if(!isOpen)return null;const handleNextClick=()=>{if(selectedTemplate===\"Tour\"&&selectedStepType===\"Banner\"){let styleTag=document.getElementById(\"dynamic-body-style\");const bodyElement=document.body;// Add a dynamic class to the body\nbodyElement.classList.add(\"dynamic-body-style\");if(!styleTag){styleTag=document.createElement(\"style\");styleTag.id=\"dynamic-body-style\";// Add styles for body and nested elements\nlet styles=`\n\t\t\t\t\t.dynamic-body-style {\n\t\t\t\t\t\tpadding-top: 50px !important;\n\t\t\t\t\t\tmax-height:calc(100% - 55px);\n\t\t\t\t\t}\n\n\t\t\t\t`;styleTag.innerHTML=styles;document.head.appendChild(styleTag);}}if(selectedStepType){// Based on selectedStepType, navigate and update steps\nif(selectedStepType===\"Announcement\"){TooltipGuideDetails();setSelectedTemplateTour(\"Announcement\");setSelectedTemplate(\"Tour\");setStepData({...stepData,type:\"Announcement\"});}else if(selectedStepType===\"Hotspot\"){HotspotGuideDetails();setSelectedTemplateTour(\"Hotspot\");setSelectedTemplate(\"Tour\");setSelectedStepTypeHotspot(true);setStepData({...stepData,type:\"Hotspot\"});setTooltipCount(tooltipCount+1);setElementSelected(false);}else if(selectedStepType===\"Banner\"){TooltipGuideDetails();setSelectedTemplate(\"Tour\");setSelectedTemplateTour(\"Banner\");setStepData({...stepData,type:\"Banner\"});// Reset all banner canvas settings to defaults for new banner steps\nuseDrawerStore.getState().resetBannerCanvasToDefaults();setBannerPopup(true);}else if(selectedStepType===\"Tooltip\"){TooltipGuideDetails();setSelectedTemplateTour(\"Tooltip\");setSelectedTemplate(\"Tour\");setStepData({...stepData,type:\"Tooltip\"});setTooltipCount(tooltipCount+1);}const updatedSteps=steps.map(step=>({...step,stepType:selectedStepType}));setSteps(updatedSteps);onClose();// Close the modal after proceeding\n}};if(createWithAI){onClose();return null;}const isSelected=title=>selectedStepType===title;const isHovered=index=>hoveredItem===index;return/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-modal\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-tours-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-tour-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-header-content\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-title\",children:guideName}),/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-step-label\",children:\"Step-1\"})]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"13px\",color:\"#B1B1B1\",lineHeight:\"19.5px\",letterSpacing:\"0.3px\"},children:translate(\"Choose Step-1: Tour Type\",{defaultValue:\"Choose Step-1: Tour Type\"})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-tours-content\",children:features.map((feature,index)=>{const isSelected=selectedStepType===feature.title;const isHovered=hoveredItem===index;return/*#__PURE__*/_jsxs(\"div\",{className:`qadpt-feature-card ${isSelected||isHovered?\"qadpt-feature-active\":\"\"}`,onClick:()=>setSelectedStepType(feature.title),onMouseEnter:()=>setHoveredItem(index),onMouseLeave:()=>setHoveredItem(null),children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-feature-icon\",children:feature.icon}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-feature-title\",children:translate(feature.title)}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-feature-description\",children:feature.description})]},index);})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-tours-actions\",children:/*#__PURE__*/_jsx(\"button\",{onClick:handleNextClick,disabled:!selectedStepType,className:`qadpt-next-button ${selectedStepType?\"\":\"qadpt-disabled\"}`,children:translate(\"NEXT\",{defaultValue:\"NEXT\"})})})]})});};export default FeatureSelectionModal;", "map": {"version": 3, "names": ["React", "useState", "useDrawerStore", "ToursAnnouncementsIcon", "ToursBannerIcon", "ToursHotspotIcon", "ToursTooltipIcon", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "FeatureSelectionModal", "props", "t", "translate", "isOpen", "onClose", "guideName", "setStepData", "stepData", "count", "hoveredItem", "setHoveredItem", "selectedStepType", "setSelectedStepType", "setSelectedTemplate", "setBannerPopup", "setSelectedTemplateTour", "setSteps", "steps", "setTooltipCount", "tooltipCount", "HotspotGuideDetails", "setElementSelected", "TooltipGuideDetails", "HotspotGuideDetailsNew", "setSelectedStepTypeHotspot", "selectedTemplate", "selectedTemplateTour", "createWithAI", "state", "selectedStepStyle", "setSelectedStepStyle", "features", "icon", "dangerouslySetInnerHTML", "__html", "title", "description", "defaultValue", "action", "borderColor", "background", "handleNextClick", "styleTag", "document", "getElementById", "bodyElement", "body", "classList", "add", "createElement", "id", "styles", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "type", "getState", "resetBannerCanvasToDefaults", "updatedSteps", "map", "step", "stepType", "isSelected", "isHovered", "index", "className", "children", "style", "fontSize", "color", "lineHeight", "letterSpacing", "feature", "onClick", "onMouseEnter", "onMouseLeave", "disabled"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/tours/tourTemplate.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { Card, CardContent } from \"@mui/material\";\r\nimport { Button } from \"@mui/material\";\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport { ToursAnnouncementsIcon, ToursBannerIcon, ToursHotspotIcon, ToursTooltipIcon } from '../../assets/icons/icons';\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\n\r\nconst FeatureSelectionModal: React.FC<{ isOpen: boolean; onClose: () => void; guideName: any; setStepData: any; stepData:any,count:any}> = (props) => {\r\n  const { t: translate } = useTranslation();\r\n  const { isOpen, onClose, guideName, setStepData, stepData, count } = props;\r\n    const [hoveredItem, setHoveredItem] = useState<Number|null>();\r\n    const [selectedStepType, setSelectedStepType] = useState<string | null>(null); // Track selected step type\r\n    const {\r\n        setSelectedTemplate,\r\n        setBannerPopup,\r\n      setSelectedTemplateTour,\r\n      setSteps,\r\n      steps,\r\n      setTooltipCount,\r\n      tooltipCount,\r\n      HotspotGuideDetails,\r\n      setElementSelected,\r\n      TooltipGuideDetails,\r\n      HotspotGuideDetailsNew,\r\n      setSelectedStepTypeHotspot,\r\n      selectedTemplate,\r\n      selectedTemplateTour,\r\n      createWithAI\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const [selectedStepStyle, setSelectedStepStyle] = useState({});\r\n    const features = [\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursAnnouncementsIcon }} />,\r\n        title: \"Announcement\",\r\n        description: translate(\"An announcement of any new feature\", { defaultValue: \"An announcement of any new feature\" }),\r\n        action: () => {\r\n          setSelectedStepType(\"Announcement\");\r\n          setSelectedStepStyle({\r\n            borderColor: \"#5F9EA0\",\r\n            background: \"#F6FFFF\",\r\n          });\r\n        },\r\n      },\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursHotspotIcon }} />,\r\n        title: \"Hotspot\",\r\n        description: translate(\"Offer users quick tips\", { defaultValue: \"Offer users quick tips\" }),\r\n        action: () => {\r\n          setSelectedStepType(\"Hotspot\");\r\n        },\r\n      },\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursBannerIcon }} />,\r\n        title: \"Banner\",\r\n        description: translate(\"Create in-line banners that get noticed\", { defaultValue: \"Create in-line banners that get noticed\" }),\r\n        action: () => {\r\n          setSelectedStepType(\"Banner\");\r\n        },\r\n      },\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursTooltipIcon }} />,\r\n        title: \"Tooltip\",\r\n        description: translate(\"Anchored to selected elements\", { defaultValue: \"Anchored to selected elements\" }),\r\n        action: () => {\r\n          setSelectedStepType(\"Tooltip\");\r\n        },\r\n      },\r\n    ];\r\n\r\n    if (!isOpen) return null;\r\n  const handleNextClick = () => {\r\n\r\n    if ( (selectedTemplate===\"Tour\" &&(selectedStepType===\"Banner\"))) {\r\n\t\t\tlet styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n\t\t\tconst bodyElement = document.body;\r\n\r\n\t\t\t// Add a dynamic class to the body\r\n\t\t\tbodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n\t\t\tif (!styleTag) {\r\n\t\t\t\tstyleTag = document.createElement(\"style\");\r\n\t\t\t\tstyleTag.id = \"dynamic-body-style\";\r\n\r\n\t\t\t\t// Add styles for body and nested elements\r\n\t\t\t\tlet styles = `\r\n\t\t\t\t\t.dynamic-body-style {\r\n\t\t\t\t\t\tpadding-top: 50px !important;\r\n\t\t\t\t\t\tmax-height:calc(100% - 55px);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t`;\r\n\r\n\t\t\t\tstyleTag.innerHTML = styles;\r\n\t\t\t\tdocument.head.appendChild(styleTag);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n      if (selectedStepType) {\r\n        // Based on selectedStepType, navigate and update steps\r\n        if (selectedStepType === \"Announcement\") {\r\n          TooltipGuideDetails();\r\n          setSelectedTemplateTour(\"Announcement\");\r\n          setSelectedTemplate(\"Tour\");\r\n          setStepData({ ...stepData, type: \"Announcement\" });\r\n        } else if (selectedStepType === \"Hotspot\") {\r\n          HotspotGuideDetails();\r\n          setSelectedTemplateTour(\"Hotspot\");\r\n          setSelectedTemplate(\"Tour\");\r\n          setSelectedStepTypeHotspot(true);\r\n          setStepData({ ...stepData, type: \"Hotspot\" });\r\n          setTooltipCount(tooltipCount + 1);\r\n          setElementSelected(false);\r\n        } else if (selectedStepType === \"Banner\") {\r\n          TooltipGuideDetails();\r\n          setSelectedTemplate(\"Tour\");\r\n          setSelectedTemplateTour(\"Banner\");\r\n          setStepData({ ...stepData, type: \"Banner\" });\r\n          // Reset all banner canvas settings to defaults for new banner steps\r\n          useDrawerStore.getState().resetBannerCanvasToDefaults();\r\n          setBannerPopup(true);\r\n        } else if (selectedStepType === \"Tooltip\") {\r\n          TooltipGuideDetails();\r\n          setSelectedTemplateTour(\"Tooltip\");\r\n          setSelectedTemplate(\"Tour\");\r\n          setStepData({ ...stepData, type: \"Tooltip\" });\r\n          setTooltipCount(tooltipCount + 1);\r\n        }\r\n\r\n        const updatedSteps = steps.map(step => ({\r\n          ...step,\r\n          stepType: selectedStepType,\r\n        }));\r\n\r\n        setSteps(updatedSteps);\r\n        onClose(); // Close the modal after proceeding\r\n      }\r\n    };\r\n    if (createWithAI) {\r\n      onClose();\r\n      return null;\r\n  }\r\n    const isSelected = (title: string) => selectedStepType === title;\r\n    const isHovered = (index: number) => hoveredItem === index;\r\n  return (\r\n      <div className=\"qadpt-modal\">\r\n      <div className=\"qadpt-tours-container\">\r\n        {/* Header Section */}\r\n        <div className=\"qadpt-tour-header\">\r\n          <div className=\"qadpt-header-content\">\r\n            <span className=\"qadpt-title\">{guideName}</span>\r\n            <span className=\"qadpt-step-label\">Step-1</span>\r\n          </div>\r\n          <div\r\n            style={{\r\n              fontSize: \"13px\",\r\n              color: \"#B1B1B1\",\r\n              lineHeight: \"19.5px\",\r\n              letterSpacing: \"0.3px\",\r\n            }}\r\n          >\r\n            {translate(\"Choose Step-1: Tour Type\", { defaultValue: \"Choose Step-1: Tour Type\" })}\r\n          </div>\r\n          </div>\r\n\r\n        {/* Step Selection Section */}\r\n        <div className=\"qadpt-tours-content\">\r\n          {features.map((feature, index) => {\r\n            const isSelected = selectedStepType === feature.title;\r\n            const isHovered = hoveredItem === index;\r\n\r\n              return (\r\n                <div\r\n                  key={index}\r\n                  className={`qadpt-feature-card ${isSelected || isHovered ? \"qadpt-feature-active\" : \"\"}`}\r\n                  onClick={() => setSelectedStepType(feature.title)}\r\n                  onMouseEnter={() => setHoveredItem(index)}\r\n                  onMouseLeave={() => setHoveredItem(null)}\r\n                >\r\n                  <div className=\"qadpt-feature-icon\">{feature.icon}</div>\r\n                  <div className=\"qadpt-feature-title\">{translate(feature.title)}</div>\r\n                  <div className=\"qadpt-feature-description\">{feature.description}</div>\r\n                </div>\r\n              );\r\n            })}\r\n        </div>\r\n\r\n        {/* Footer Action Buttons */}\r\n        <div className=\"qadpt-tours-actions\">\r\n          <button\r\n            onClick={handleNextClick}\r\n            disabled={!selectedStepType}\r\n            className={`qadpt-next-button ${selectedStepType ? \"\" : \"qadpt-disabled\"}`}\r\n          >\r\n            {translate(\"NEXT\", { defaultValue: \"NEXT\" })}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FeatureSelectionModal;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAeC,QAAQ,KAAQ,OAAO,CAGlD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE,OAASC,sBAAsB,CAAEC,eAAe,CAAEC,gBAAgB,CAAEC,gBAAgB,KAAQ,0BAA0B,CACtH,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG/C,KAAM,CAAAC,qBAAkI,CAAIC,KAAK,EAAK,CACpJ,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGR,cAAc,CAAC,CAAC,CACzC,KAAM,CAAES,MAAM,CAAEC,OAAO,CAAEC,SAAS,CAAEC,WAAW,CAAEC,QAAQ,CAAEC,KAAM,CAAC,CAAGR,KAAK,CACxE,KAAM,CAACS,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAc,CAAC,CAC7D,KAAM,CAACuB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxB,QAAQ,CAAgB,IAAI,CAAC,CAAE;AAC/E,KAAM,CACFyB,mBAAmB,CACnBC,cAAc,CAChBC,uBAAuB,CACvBC,QAAQ,CACRC,KAAK,CACLC,eAAe,CACfC,YAAY,CACZC,mBAAmB,CACnBC,kBAAkB,CAClBC,mBAAmB,CACnBC,sBAAsB,CACtBC,0BAA0B,CAC1BC,gBAAgB,CAChBC,oBAAoB,CACpBC,YACJ,CAAC,CAAGtC,cAAc,CAAEuC,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAAA2C,QAAQ,CAAG,CACf,CACEC,IAAI,cAAEpC,IAAA,SAAMqC,uBAAuB,CAAE,CAAEC,MAAM,CAAE5C,sBAAuB,CAAE,CAAE,CAAC,CAC3E6C,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAElC,SAAS,CAAC,oCAAoC,CAAE,CAAEmC,YAAY,CAAE,oCAAqC,CAAC,CAAC,CACpHC,MAAM,CAAEA,CAAA,GAAM,CACZ1B,mBAAmB,CAAC,cAAc,CAAC,CACnCkB,oBAAoB,CAAC,CACnBS,WAAW,CAAE,SAAS,CACtBC,UAAU,CAAE,SACd,CAAC,CAAC,CACJ,CACF,CAAC,CACD,CACER,IAAI,cAAEpC,IAAA,SAAMqC,uBAAuB,CAAE,CAAEC,MAAM,CAAE1C,gBAAiB,CAAE,CAAE,CAAC,CACrE2C,KAAK,CAAE,SAAS,CAChBC,WAAW,CAAElC,SAAS,CAAC,wBAAwB,CAAE,CAAEmC,YAAY,CAAE,wBAAyB,CAAC,CAAC,CAC5FC,MAAM,CAAEA,CAAA,GAAM,CACZ1B,mBAAmB,CAAC,SAAS,CAAC,CAChC,CACF,CAAC,CACD,CACEoB,IAAI,cAAEpC,IAAA,SAAMqC,uBAAuB,CAAE,CAAEC,MAAM,CAAE3C,eAAgB,CAAE,CAAE,CAAC,CACpE4C,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAElC,SAAS,CAAC,yCAAyC,CAAE,CAAEmC,YAAY,CAAE,yCAA0C,CAAC,CAAC,CAC9HC,MAAM,CAAEA,CAAA,GAAM,CACZ1B,mBAAmB,CAAC,QAAQ,CAAC,CAC/B,CACF,CAAC,CACD,CACEoB,IAAI,cAAEpC,IAAA,SAAMqC,uBAAuB,CAAE,CAAEC,MAAM,CAAEzC,gBAAiB,CAAE,CAAE,CAAC,CACrE0C,KAAK,CAAE,SAAS,CAChBC,WAAW,CAAElC,SAAS,CAAC,+BAA+B,CAAE,CAAEmC,YAAY,CAAE,+BAAgC,CAAC,CAAC,CAC1GC,MAAM,CAAEA,CAAA,GAAM,CACZ1B,mBAAmB,CAAC,SAAS,CAAC,CAChC,CACF,CAAC,CACF,CAED,GAAI,CAACT,MAAM,CAAE,MAAO,KAAI,CAC1B,KAAM,CAAAsC,eAAe,CAAGA,CAAA,GAAM,CAE5B,GAAMhB,gBAAgB,GAAG,MAAM,EAAId,gBAAgB,GAAG,QAAS,CAAG,CACnE,GAAI,CAAA+B,QAAQ,CAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB,CAChF,KAAM,CAAAC,WAAW,CAAGF,QAAQ,CAACG,IAAI,CAEjC;AACAD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAE/C,GAAI,CAACN,QAAQ,CAAE,CACdA,QAAQ,CAAGC,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC,CAC1CP,QAAQ,CAACQ,EAAE,CAAG,oBAAoB,CAElC;AACA,GAAI,CAAAC,MAAM,CAAG;AACjB;AACA;AACA;AACA;AACA;AACA,KAAK,CAEDT,QAAQ,CAACU,SAAS,CAAGD,MAAM,CAC3BR,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,QAAQ,CAAC,CACpC,CACD,CAGI,GAAI/B,gBAAgB,CAAE,CACpB;AACA,GAAIA,gBAAgB,GAAK,cAAc,CAAE,CACvCW,mBAAmB,CAAC,CAAC,CACrBP,uBAAuB,CAAC,cAAc,CAAC,CACvCF,mBAAmB,CAAC,MAAM,CAAC,CAC3BP,WAAW,CAAC,CAAE,GAAGC,QAAQ,CAAEgD,IAAI,CAAE,cAAe,CAAC,CAAC,CACpD,CAAC,IAAM,IAAI5C,gBAAgB,GAAK,SAAS,CAAE,CACzCS,mBAAmB,CAAC,CAAC,CACrBL,uBAAuB,CAAC,SAAS,CAAC,CAClCF,mBAAmB,CAAC,MAAM,CAAC,CAC3BW,0BAA0B,CAAC,IAAI,CAAC,CAChClB,WAAW,CAAC,CAAE,GAAGC,QAAQ,CAAEgD,IAAI,CAAE,SAAU,CAAC,CAAC,CAC7CrC,eAAe,CAACC,YAAY,CAAG,CAAC,CAAC,CACjCE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,IAAM,IAAIV,gBAAgB,GAAK,QAAQ,CAAE,CACxCW,mBAAmB,CAAC,CAAC,CACrBT,mBAAmB,CAAC,MAAM,CAAC,CAC3BE,uBAAuB,CAAC,QAAQ,CAAC,CACjCT,WAAW,CAAC,CAAE,GAAGC,QAAQ,CAAEgD,IAAI,CAAE,QAAS,CAAC,CAAC,CAC5C;AACAlE,cAAc,CAACmE,QAAQ,CAAC,CAAC,CAACC,2BAA2B,CAAC,CAAC,CACvD3C,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,IAAIH,gBAAgB,GAAK,SAAS,CAAE,CACzCW,mBAAmB,CAAC,CAAC,CACrBP,uBAAuB,CAAC,SAAS,CAAC,CAClCF,mBAAmB,CAAC,MAAM,CAAC,CAC3BP,WAAW,CAAC,CAAE,GAAGC,QAAQ,CAAEgD,IAAI,CAAE,SAAU,CAAC,CAAC,CAC7CrC,eAAe,CAACC,YAAY,CAAG,CAAC,CAAC,CACnC,CAEA,KAAM,CAAAuC,YAAY,CAAGzC,KAAK,CAAC0C,GAAG,CAACC,IAAI,GAAK,CACtC,GAAGA,IAAI,CACPC,QAAQ,CAAElD,gBACZ,CAAC,CAAC,CAAC,CAEHK,QAAQ,CAAC0C,YAAY,CAAC,CACtBtD,OAAO,CAAC,CAAC,CAAE;AACb,CACF,CAAC,CACD,GAAIuB,YAAY,CAAE,CAChBvB,OAAO,CAAC,CAAC,CACT,MAAO,KAAI,CACf,CACE,KAAM,CAAA0D,UAAU,CAAI3B,KAAa,EAAKxB,gBAAgB,GAAKwB,KAAK,CAChE,KAAM,CAAA4B,SAAS,CAAIC,KAAa,EAAKvD,WAAW,GAAKuD,KAAK,CAC5D,mBACIpE,IAAA,QAAKqE,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC5BpE,KAAA,QAAKmE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAEpCpE,KAAA,QAAKmE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCpE,KAAA,QAAKmE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCtE,IAAA,SAAMqE,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE7D,SAAS,CAAO,CAAC,cAChDT,IAAA,SAAMqE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EAC7C,CAAC,cACNtE,IAAA,QACEuE,KAAK,CAAE,CACLC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,QAAQ,CACpBC,aAAa,CAAE,OACjB,CAAE,CAAAL,QAAA,CAEDhE,SAAS,CAAC,0BAA0B,CAAE,CAAEmC,YAAY,CAAE,0BAA2B,CAAC,CAAC,CACjF,CAAC,EACD,CAAC,cAGRzC,IAAA,QAAKqE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjCnC,QAAQ,CAAC4B,GAAG,CAAC,CAACa,OAAO,CAAER,KAAK,GAAK,CAChC,KAAM,CAAAF,UAAU,CAAGnD,gBAAgB,GAAK6D,OAAO,CAACrC,KAAK,CACrD,KAAM,CAAA4B,SAAS,CAAGtD,WAAW,GAAKuD,KAAK,CAErC,mBACElE,KAAA,QAEEmE,SAAS,CAAE,sBAAsBH,UAAU,EAAIC,SAAS,CAAG,sBAAsB,CAAG,EAAE,EAAG,CACzFU,OAAO,CAAEA,CAAA,GAAM7D,mBAAmB,CAAC4D,OAAO,CAACrC,KAAK,CAAE,CAClDuC,YAAY,CAAEA,CAAA,GAAMhE,cAAc,CAACsD,KAAK,CAAE,CAC1CW,YAAY,CAAEA,CAAA,GAAMjE,cAAc,CAAC,IAAI,CAAE,CAAAwD,QAAA,eAEzCtE,IAAA,QAAKqE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEM,OAAO,CAACxC,IAAI,CAAM,CAAC,cACxDpC,IAAA,QAAKqE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEhE,SAAS,CAACsE,OAAO,CAACrC,KAAK,CAAC,CAAM,CAAC,cACrEvC,IAAA,QAAKqE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEM,OAAO,CAACpC,WAAW,CAAM,CAAC,GARjE4B,KASF,CAAC,CAEV,CAAC,CAAC,CACD,CAAC,cAGNpE,IAAA,QAAKqE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCtE,IAAA,WACE6E,OAAO,CAAEhC,eAAgB,CACzBmC,QAAQ,CAAE,CAACjE,gBAAiB,CAC5BsD,SAAS,CAAE,qBAAqBtD,gBAAgB,CAAG,EAAE,CAAG,gBAAgB,EAAG,CAAAuD,QAAA,CAE1EhE,SAAS,CAAC,MAAM,CAAE,CAAEmC,YAAY,CAAE,MAAO,CAAC,CAAC,CACtC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}