{"ast": null, "code": "import React,{useState}from\"react\";import{Box,Button,IconButton}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import{useTranslation}from'react-i18next';// import Draggable from \"react-draggable\";\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ElementRules=()=>{const{t:translate}=useTranslation();const[isOpen,setIsOpen]=useState(true);const handleClose=()=>{setIsOpen(false);// Close the popup when close button is clicked\n};if(!isOpen)return null;return/*#__PURE__*/(// <Draggable>\n_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":translate(\"close\"),onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Element Rules\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":translate(\"close\"),onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",backgroundColor:\"#f9f7f7\",borderRadius:\"10px\",padding:\"12px\",marginBottom:\"12px\"},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",sx:{textTransform:\"none\",borderRadius:\"8px\",borderColor:\"#d1bcbc\",color:\"#4c9999\",width:\"100%\"},children:translate(\"Choose Element\")})}),/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",justifyContent:\"space-between\",gap:\"12px\",marginBottom:\"12px\"},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",sx:{textTransform:\"none\",borderRadius:\"8px\",borderColor:\"#d1bcbc\",color:\"#4c9999\",flex:1},children:translate(\"Present\")}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",sx:{textTransform:\"none\",borderRadius:\"8px\",borderColor:\"#d1bcbc\",color:\"#4c9999\",flex:1},children:translate(\"Absent\")})]}),/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",sx:{textTransform:\"none\",borderRadius:\"8px\",borderColor:\"#d1bcbc\",color:\"#4c9999\",width:\"100%\",padding:\"12px\"},children:translate(\"Add Element\")})})]})})//</Draggable>\n);};export default ElementRules;", "map": {"version": 3, "names": ["React", "useState", "Box", "<PERSON><PERSON>", "IconButton", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "ElementRules", "t", "translate", "isOpen", "setIsOpen", "handleClose", "id", "className", "children", "onClick", "size", "sx", "display", "justifyContent", "alignItems", "backgroundColor", "borderRadius", "padding", "marginBottom", "variant", "textTransform", "borderColor", "color", "width", "gap", "flex"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/ElementRules.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Box, Button, Typography, IconButton } from \"@mui/material\";\r\nimport ArrowBackIosIcon from \"@mui/icons-material/ArrowBackIos\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n// import Draggable from \"react-draggable\";\r\n\r\nconst ElementRules = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false); // Close the popup when close button is clicked\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\treturn (\r\n\t\t// <Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t{/* Header */}\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Element Rules\")}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Choose Element Button */}\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tbackgroundColor: \"#f9f7f7\",\r\n\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\tmarginBottom: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Choose Element\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t\t{/* Present & Absent Buttons */}\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\tmarginBottom: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Present\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Absent\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t\t{/* Add Element Button */}\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Add Element\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</Box>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ElementRules;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,GAAG,CAAEC,MAAM,CAAcC,UAAU,KAAQ,eAAe,CAEnE,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,OAASC,cAAc,KAAQ,eAAe,CAE9C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGP,cAAc,CAAC,CAAC,CACzC,KAAM,CAACQ,MAAM,CAAEC,SAAS,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAAgB,WAAW,CAAGA,CAAA,GAAM,CACzBD,SAAS,CAAC,KAAK,CAAC,CAAE;AACnB,CAAC,CAED,GAAI,CAACD,MAAM,CAAE,MAAO,KAAI,CACxB,mBACC;AACAN,IAAA,QACCS,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BT,KAAA,QAAKQ,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE7BT,KAAA,QAAKQ,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnCX,IAAA,CAACL,UAAU,EACV,aAAYU,SAAS,CAAC,OAAO,CAAE,CAC/BO,OAAO,CAAEJ,WAAY,CAAAG,QAAA,cAErBX,IAAA,CAACH,2BAA2B,GAAE,CAAC,CAEpB,CAAC,cACbG,IAAA,QAAKU,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEN,SAAS,CAAC,eAAe,CAAC,CAAM,CAAC,cAE/DL,IAAA,CAACL,UAAU,EACVkB,IAAI,CAAC,OAAO,CACZ,aAAYR,SAAS,CAAC,OAAO,CAAE,CAC/BO,OAAO,CAAEJ,WAAY,CAAAG,QAAA,cAErBX,IAAA,CAACJ,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAGNI,IAAA,CAACP,GAAG,EACHqB,EAAE,CAAE,CACHC,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,MACf,CAAE,CAAAV,QAAA,cAEFX,IAAA,CAACN,MAAM,EACN4B,OAAO,CAAC,UAAU,CAClBR,EAAE,CAAE,CACHS,aAAa,CAAE,MAAM,CACrBJ,YAAY,CAAE,KAAK,CACnBK,WAAW,CAAE,SAAS,CACtBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,MACR,CAAE,CAAAf,QAAA,CAEDN,SAAS,CAAC,gBAAgB,CAAC,CACrB,CAAC,CACL,CAAC,cAGNH,KAAA,CAACT,GAAG,EACHqB,EAAE,CAAE,CACHC,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BW,GAAG,CAAE,MAAM,CACXN,YAAY,CAAE,MACf,CAAE,CAAAV,QAAA,eAEFX,IAAA,CAACN,MAAM,EACN4B,OAAO,CAAC,UAAU,CAClBR,EAAE,CAAE,CACHS,aAAa,CAAE,MAAM,CACrBJ,YAAY,CAAE,KAAK,CACnBK,WAAW,CAAE,SAAS,CACtBC,KAAK,CAAE,SAAS,CAChBG,IAAI,CAAE,CACP,CAAE,CAAAjB,QAAA,CAEDN,SAAS,CAAC,SAAS,CAAC,CACd,CAAC,cACTL,IAAA,CAACN,MAAM,EACN4B,OAAO,CAAC,UAAU,CAClBR,EAAE,CAAE,CACHS,aAAa,CAAE,MAAM,CACrBJ,YAAY,CAAE,KAAK,CACnBK,WAAW,CAAE,SAAS,CACtBC,KAAK,CAAE,SAAS,CAChBG,IAAI,CAAE,CACP,CAAE,CAAAjB,QAAA,CAEDN,SAAS,CAAC,QAAQ,CAAC,CACb,CAAC,EACL,CAAC,cAGNL,IAAA,CAACP,GAAG,EAAAkB,QAAA,cACHX,IAAA,CAACN,MAAM,EACN4B,OAAO,CAAC,UAAU,CAClBR,EAAE,CAAE,CACHS,aAAa,CAAE,MAAM,CACrBJ,YAAY,CAAE,KAAK,CACnBK,WAAW,CAAE,SAAS,CACtBC,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,MAAM,CACbN,OAAO,CAAE,MACV,CAAE,CAAAT,QAAA,CAEDN,SAAS,CAAC,aAAa,CAAC,CAClB,CAAC,CACL,CAAC,EACF,CAAC,CACF,CACL;AAAA,EAEF,CAAC,CAED,cAAe,CAAAF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}