{"ast": null, "code": "import React,{useState}from\"react\";import{<PERSON>,Typo<PERSON>,TextField,IconButton,Button,FormControl,Select,MenuItem}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import{InfoFilled,QuestionFill,Reselect,Solid}from\"../../assets/icons/icons\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HotspotSettings=_ref=>{let{currentGuide}=_ref;const{t:translate}=useTranslation();const{setHotspotPopup,setElementSelected,updatehotspots,setDesignPopup,toolTipGuideMetaData,setOpenTooltip,openTooltip,currentStep,setTooltipPositionByXpath,updateCanvasInTooltip,updateTooltipBtnContainer,updateTooltipImageContainer,pulseAnimationsH,setPulseAnimationsH,setIsUnSavedChanges}=useDrawerStore(state=>state);const[hotSpotProperties,setHotSpotProperties]=useState(()=>{var _toolTipGuideMetaData;// Get the current step's hotspot properties\nconst currentStepIndex=currentStep-1;const currentStepHotspots=(_toolTipGuideMetaData=toolTipGuideMetaData[currentStepIndex])===null||_toolTipGuideMetaData===void 0?void 0:_toolTipGuideMetaData.hotspots;// Use the current step's hotspot properties if available, otherwise use default values\nconst initialHotspotProperties=currentStepHotspots||{XPosition:\"4\",YPosition:\"4\",Type:\"Question\",Color:\"yellow\",Size:16,PulseAnimation:pulseAnimationsH,stopAnimationUponInteraction:true,ShowUpon:\"Hovering Hotspot\",ShowByDefault:false};return initialHotspotProperties;});const handleClose=()=>{setHotspotPopup(false);};const handledesignclose=()=>{setDesignPopup(false);};const handleSizeChange=value=>{const sizeInPx=16+(value-1)*4;onPropertyChange(\"Size\",sizeInPx);};const onReselectElement=()=>{// setHotSpotProperties({\n// \tXPosition: \"4\",\n// \tYPosition: \"4\",\n// \tType: \"Question\",\n// \tColor: \"y4ellow\",\n// \tSize: 1,\n// \tPulseAnimation: pulseAnimationsH,\n// \tstopAnimationUponInteraction: true,\n// \tShowUpon: \"Hovering Hotspot\",\n// \tShowByDefault: false,\n// });\nsetElementSelected(false);handledesignclose();//updatehotspots(HOTSPOT_DEFAULT_VALUE);\n// updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n//  updateTooltipBtnContainer(BUTTON_CONT_DEF_VALUE_1);\n//  updateTooltipImageContainer(IMG_CONT_DEF_VALUE);\nconst existingHotspot=document.getElementById(\"hotspotBlinkCreation\");const existingTooltip=document.getElementById(\"Tooltip-unique\");const allElementsWithOutline=document.querySelectorAll(\"[style*='outline']\");allElementsWithOutline.forEach(element=>{element.style.outline=\"\";// Reset the outline (border) color\n});if(existingHotspot){existingHotspot.remove();}// if (existingTooltip)\n// {\n//   existingTooltip.remove();\n// }\nsetHotspotPopup(false);setIsUnSavedChanges(true);};const onPropertyChange=(key,value)=>{setHotSpotProperties(prevState=>({...prevState,[key]:value}));};const handleApplyChanges=()=>{updatehotspots(hotSpotProperties);handleClose();setHotspotPopup(false);setIsUnSavedChanges(true);};return/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":translate(\"Go Back\"),onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Hotspot\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":translate(\"Close\"),onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{cursor:\"pointer\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Reselect Element\")}),/*#__PURE__*/_jsx(\"span\",{onClick:onReselectElement,dangerouslySetInnerHTML:{__html:Reselect},style:{padding:\"5px\",marginRight:\"10px\"}}),\" \"]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-position-grid\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-ctrl-title\",children:translate(\"Position within Element\")}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",style:{padding:\"0px\"},children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{padding:\"0 !important\",marginBottom:\"0 !important\"},children:[/*#__PURE__*/_jsxs(Typography,{className:\"qadpt-control-label\",sx:{fontSize:\"12px !important\",paddingLeft:\"0 !important\",margin:\"3px\"},children:[\"X \",translate(\"Axis Offset\")]}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:hotSpotProperties.XPosition,size:\"small\",className:\"qadpt-control-input\",onChange:e=>onPropertyChange(\"XPosition\",e.target.value),InputProps:{endAdornment:\"%\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}}})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{padding:\"0 !important\",marginBottom:\"0 !important\"},children:[/*#__PURE__*/_jsxs(Typography,{className:\"qadpt-control-label\",sx:{fontSize:\"12px !important\",paddingLeft:\"0 !important\",margin:\"3px\"},children:[\"Y \",translate(\"Axis Offset\")]}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:hotSpotProperties.YPosition,size:\"small\",className:\"qadpt-control-input\",onChange:e=>onPropertyChange(\"YPosition\",e.target.value),InputProps:{endAdornment:\"%\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}}})]})]})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",sx:{flexDirection:\"column\",height:\"auto !important\",padding:\"8px !important\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 5px 0 !important\"},children:translate(\"Type\")}),/*#__PURE__*/_jsxs(Select,{value:hotSpotProperties.Type,onChange:e=>onPropertyChange(\"Type\",e.target.value),displayEmpty:true,className:\"qadpt-control-input\",sx:{width:\"100% !important\",borderRadius:\"12px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}},size:\"small\",renderValue:selected=>/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",justifyContent:\"space-between\",width:\"100%\"},children:[/*#__PURE__*/_jsx(\"span\",{children:translate(selected)}),/*#__PURE__*/_jsxs(\"span\",{children:[selected===\"Info\"&&/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:InfoFilled}}),selected===\"Question\"&&/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:QuestionFill}}),selected===\"Solid\"&&/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:Solid}})]})]}),children:[/*#__PURE__*/_jsxs(MenuItem,{id:\"qadpt-designpopup\",value:\"Info\",children:[/*#__PURE__*/_jsx(\"span\",{children:translate(\"Info\")}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:\"auto\",display:\"flex\",alignItems:\"center\"},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:InfoFilled}})})]}),/*#__PURE__*/_jsxs(MenuItem,{id:\"qadpt-designpopup\",value:\"Question\",children:[/*#__PURE__*/_jsx(\"span\",{children:translate(\"Question\")}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:\"auto\",display:\"flex\",alignItems:\"center\"},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:QuestionFill}})})]}),/*#__PURE__*/_jsxs(MenuItem,{id:\"qadpt-designpopup\",value:\"Solid\",children:[/*#__PURE__*/_jsx(\"span\",{children:translate(\"Solid\")}),/*#__PURE__*/_jsx(\"span\",{style:{marginLeft:\"auto\",display:\"flex\",alignItems:\"center\"},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:Solid}})})]})]})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{id:\"qadpt-designpopup\",className:\"qadpt-control-label\",children:translate(\"Color\")}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:hotSpotProperties.Color,onChange:e=>onPropertyChange(\"Color\",e.target.value),className:\"qadpt-color-input\"})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{id:\"qadpt-designpopup\",className:\"qadpt-control-label\",children:translate(\"Size\")}),/*#__PURE__*/_jsx(FormControl,{id:\"qadpt-designpopup\",variant:\"outlined\",fullWidth:true,className:\"qadpt-control-input\",children:/*#__PURE__*/_jsx(Select,{defaultValue:1,id:\"qadpt-designpopup\",value:(hotSpotProperties.Size-16)/4+1,onChange:e=>handleSizeChange(Number(e.target.value)),sx:{borderRadius:\"12px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}},children:[1,2,3,4,5,6,7,8,9].map(size=>/*#__PURE__*/_jsx(MenuItem,{id:\"qadpt-designpopup\",value:size,children:size},size))})})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",sx:{flexDirection:\"column\",height:\"auto !important\",gap:\"8px\",padding:\"8px !important\"},children:[/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-item\",sx:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",width:\"100%\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{textAlign:\"left\",minWidth:\"130px\",padding:\"0 !important\"},children:translate(\"Pulse Animation\")}),/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:pulseAnimationsH,onChange:e=>{onPropertyChange(\"PulseAnimation\",e.target.checked);setPulseAnimationsH(e.target.checked);},name:\"pulseAnimation\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})]}),pulseAnimationsH&&/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-item\",sx:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",width:\"100%\"},children:[/*#__PURE__*/_jsx(Typography,{id:\"qadpt-designpopup\",className:\"qadpt-control-label\",sx:{textAlign:\"left\",minWidth:\"130px\",width:\"20px\",padding:\"0 !important\"},children:translate(\"Stop Animation Upon Interaction\")}),/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:hotSpotProperties.stopAnimationUponInteraction,onChange:e=>onPropertyChange(\"stopAnimationUponInteraction\",e.target.checked),name:\"stopAnimationUponInteraction\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})]})]}),hotSpotProperties.ShowByDefault===false&&/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",sx:{flexDirection:\"column\",height:\"auto !important\",padding:\"8px !important\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{padding:\"0 0 5px 0 !important\"},children:translate(\"Show Upon\")}),/*#__PURE__*/_jsx(FormControl,{variant:\"outlined\",fullWidth:true,className:\"qadpt-control-input\",sx:{borderRadius:\"12px\",width:\"100% !important\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"&.MuiInputBase-root\":{border:\"1px solid #a8a8a8 !important\"}},children:/*#__PURE__*/_jsxs(Select,{value:hotSpotProperties.ShowUpon// Ensure this value is correctly tied to the state\n,onChange:e=>onPropertyChange(\"ShowUpon\",e.target.value),name:\"ShowUpon\",sx:{width:\"100% !important\",borderRadius:\"12px\"},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"Clicking Hotspot\",children:translate(\"Clicking Hotspot\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"Hovering Hotspot\",children:translate(\"Hovering Hotspot\")})]})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Show by Default\")}),/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:hotSpotProperties.ShowByDefault,onChange:e=>onPropertyChange(\"ShowByDefault\",e.target.checked),name:\"showByDefault\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:\"qadpt-btn\",children:translate(\"Apply\")})})]})});};export default HotspotSettings;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "CloseIcon", "useDrawerStore", "InfoFilled", "QuestionFill", "Reselect", "Solid", "ArrowBackIosNewOutlinedIcon", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "HotspotSettings", "_ref", "currentGuide", "t", "translate", "setHotspotPopup", "setElementSelected", "updatehotspots", "setDesignPopup", "toolTipGuideMetaData", "setOpenTooltip", "openTooltip", "currentStep", "setTooltipPositionByXpath", "updateCanvasInTooltip", "updateTooltipBtnContainer", "updateTooltipImageContainer", "pulseAnimationsH", "setPulseAnimationsH", "setIsUnSavedChanges", "state", "hotSpotProperties", "setHotSpotProperties", "_toolTipGuideMetaData", "currentStepIndex", "currentStepHotspots", "hotspots", "initialHotspotProperties", "XPosition", "YPosition", "Type", "Color", "Size", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "ShowByDefault", "handleClose", "handledesignclose", "handleSizeChange", "value", "sizeInPx", "onPropertyChange", "onReselectElement", "existingHotspot", "document", "getElementById", "existingTooltip", "allElementsWithOutline", "querySelectorAll", "for<PERSON>ach", "element", "style", "outline", "remove", "key", "prevState", "handleApplyChanges", "id", "className", "children", "onClick", "size", "sx", "cursor", "dangerouslySetInnerHTML", "__html", "padding", "marginRight", "marginBottom", "fontSize", "paddingLeft", "margin", "variant", "onChange", "e", "target", "InputProps", "endAdornment", "border", "flexDirection", "height", "displayEmpty", "width", "borderRadius", "renderValue", "selected", "display", "justifyContent", "marginLeft", "alignItems", "type", "fullWidth", "defaultValue", "Number", "map", "gap", "textAlign", "min<PERSON><PERSON><PERSON>", "checked", "name"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/hotspot/HotspotSettings.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n  Solid,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst HotspotSettings = ({ currentGuide }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n    const {\r\n\t\t\tsetHotspotPopup,\r\n\t\t\tsetElementSelected,\r\n\t\t\tupdatehotspots,\r\n\t\t\tsetDesignPopup,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t\tsetOpenTooltip,\r\n\t\t\topenTooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\tsetTooltipPositionByXpath,\r\n\t\t\tupdateCanvasInTooltip,\r\n\t\t\tupdateTooltipBtnContainer,\r\n\t\t\tupdateTooltipImageContainer,\r\n\t\t\tpulseAnimationsH,\r\n\t\tsetPulseAnimationsH,\r\n\t\t\tsetIsUnSavedChanges\r\n\t\t} = useDrawerStore((state: any) => state);\r\n\r\n\t\tconst [hotSpotProperties, setHotSpotProperties] = useState<any>(() => {\r\n\t\t\t// Get the current step's hotspot properties\r\n\t\t\tconst currentStepIndex = currentStep - 1;\r\n\t\t\tconst currentStepHotspots = toolTipGuideMetaData[currentStepIndex]?.hotspots;\r\n\r\n\t\t\t// Use the current step's hotspot properties if available, otherwise use default values\r\n\t\t\tconst initialHotspotProperties = currentStepHotspots || {\r\n\t\t\t\tXPosition: \"4\",\r\n\t\t\t\tYPosition: \"4\",\r\n\t\t\t\tType: \"Question\",\r\n\t\t\t\tColor: \"yellow\",\r\n\t\t\t\tSize: 16,\r\n\t\t\t\tPulseAnimation: pulseAnimationsH,\r\n\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\tShowByDefault: false,\r\n\t\t\t};\r\n\t\t\treturn initialHotspotProperties;\r\n\t\t});\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleSizeChange = (value: number) => {\r\n\t\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t\t};\r\n\r\n\t\tconst onReselectElement = () => {\r\n\t\t\t// setHotSpotProperties({\r\n\t\t\t// \tXPosition: \"4\",\r\n\t\t\t// \tYPosition: \"4\",\r\n\t\t\t// \tType: \"Question\",\r\n\t\t\t// \tColor: \"y4ellow\",\r\n\t\t\t// \tSize: 1,\r\n\t\t\t// \tPulseAnimation: pulseAnimationsH,\r\n\t\t\t// \tstopAnimationUponInteraction: true,\r\n\t\t\t// \tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t// \tShowByDefault: false,\r\n\t\t\t// });\r\n\t\t\tsetElementSelected(false);\r\n\t\t\thandledesignclose();\r\n\t\t\t//updatehotspots(HOTSPOT_DEFAULT_VALUE);\r\n\t\t\t// updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\t\t//  updateTooltipBtnContainer(BUTTON_CONT_DEF_VALUE_1);\r\n\t\t\t//  updateTooltipImageContainer(IMG_CONT_DEF_VALUE);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\t\t\tconst existingTooltip = document.getElementById(\"Tooltip-unique\");\r\n\r\n\t\t\tconst allElementsWithOutline = document.querySelectorAll<HTMLElement>(\"[style*='outline']\");\r\n\t\t\tallElementsWithOutline.forEach((element) => {\r\n\t\t\t\telement.style.outline = \"\"; // Reset the outline (border) color\r\n\t\t\t});\r\n\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t\t// if (existingTooltip)\r\n\t\t\t// {\r\n\t\t\t//   existingTooltip.remove();\r\n\t\t\t// }\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetHotSpotProperties((prevState: any) => ({\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleApplyChanges = () => {\r\n\t\t\tupdatehotspots(hotSpotProperties);\r\n\t\t\thandleClose();\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label={translate(\"Go Back\")}\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Hotspot\")}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label={translate(\"Close\")}\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\" sx={{ cursor: \"pointer\" }}>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Reselect Element\")}</Typography>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Reselect }}\r\n\t\t\t\t\t\t\t\t\tstyle={{  padding: \"5px\", marginRight: \"10px\" }}\r\n\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-position-grid\">\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\">{translate(\"Position within Element\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-controls\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ padding: \"0px\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\",marginBottom:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"12px !important\", paddingLeft: \"0 !important\", margin: \"3px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tX {translate(\"Axis Offset\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.XPosition}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"XPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\",marginBottom:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"12px !important\", paddingLeft: \"0 !important\", margin: \"3px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tY {translate(\"Axis Offset\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.YPosition}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"YPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 5px 0 !important\" }}>{translate(\"Type\")}</Typography>\r\n\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.Type}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"Type\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\twidth :\"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\trenderValue={(selected) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", justifyContent: \"space-between\", width: \"100%\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>{translate(selected)}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Info\" && <span dangerouslySetInnerHTML={{ __html: InfoFilled }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Question\" && <span dangerouslySetInnerHTML={{ __html: QuestionFill }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Solid\" && <span dangerouslySetInnerHTML={{ __html: Solid }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Info\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Info\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: InfoFilled }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Question\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Question\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: QuestionFill }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Solid\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Solid\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: Solid }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Color\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.Color}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"Color\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Size\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={1}\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={(hotSpotProperties.Size - 16) / 4 + 1}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleSizeChange(Number(e.target.value))}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{[1, 2, 3, 4, 5, 6, 7, 8, 9].map((size) => (\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tkey={size}\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={size}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{size}\r\n\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\", gap: \"8px\",padding:\"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{/* Pulse Animation Toggle */}\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-item\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ textAlign: \"left\", minWidth: \"130px\" ,padding:\"0 !important\"}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Pulse Animation\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={pulseAnimationsH}\r\n        onChange={(e) => {\r\n            onPropertyChange(\"PulseAnimation\", e.target.checked);\r\n            setPulseAnimationsH(e.target.checked);\r\n        }}\r\n        name=\"pulseAnimation\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t{pulseAnimationsH && (\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-item\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth:\"100%\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ textAlign: \"left\", minWidth: \"130px\",width:\"20px\",padding:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Stop Animation Upon Interaction\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={hotSpotProperties.stopAnimationUponInteraction}\r\n        onChange={(e) => onPropertyChange(\"stopAnimationUponInteraction\", e.target.checked)}\r\n        name=\"stopAnimationUponInteraction\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t{hotSpotProperties.ShowByDefault === false && (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\" ,padding:\"8px !important\"}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 5px 0 !important\" }}>{translate(\"Show Upon\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t{/* Show Upon Dropdown */}\r\n\t\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{border : \"1px solid #a8a8a8 !important\"}\r\n\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.ShowUpon} // Ensure this value is correctly tied to the state\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e: any) => onPropertyChange(\"ShowUpon\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"ShowUpon\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ width: \"100% !important\", borderRadius: \"12px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Clicking Hotspot\">{translate(\"Clicking Hotspot\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Hovering Hotspot\">{translate(\"Hovering Hotspot\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Show by Default\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t{/* Show by Default Toggle */}\r\n\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={hotSpotProperties.ShowByDefault}\r\n        onChange={(e) => onPropertyChange(\"ShowByDefault\", e.target.checked)}\r\n        name=\"showByDefault\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n};\r\n\r\nexport default HotspotSettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAgBC,QAAQ,KAAkB,OAAO,CAC7D,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAEC,MAAM,CAAkBC,WAAW,CAAcC,MAAM,CAAEC,QAAQ,KAAqD,eAAe,CAC5L,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAA6E,yBAAyB,CAE3H,OACEC,UAAU,CACVC,YAAY,CACZC,QAAQ,CACRC,KAAK,KACA,0BAA0B,CACjC,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,YAAkB,CAAC,CAAAD,IAAA,CAC7C,KAAM,CAAEE,CAAC,CAAEC,SAAU,CAAC,CAAGT,cAAc,CAAC,CAAC,CACtC,KAAM,CACPU,eAAe,CACfC,kBAAkB,CAClBC,cAAc,CACdC,cAAc,CACdC,oBAAoB,CACpBC,cAAc,CACdC,WAAW,CACXC,WAAW,CACXC,yBAAyB,CACzBC,qBAAqB,CACrBC,yBAAyB,CACzBC,2BAA2B,CAC3BC,gBAAgB,CACjBC,mBAAmB,CAClBC,mBACD,CAAC,CAAG9B,cAAc,CAAE+B,KAAU,EAAKA,KAAK,CAAC,CAEzC,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3C,QAAQ,CAAM,IAAM,KAAA4C,qBAAA,CACrE;AACA,KAAM,CAAAC,gBAAgB,CAAGZ,WAAW,CAAG,CAAC,CACxC,KAAM,CAAAa,mBAAmB,EAAAF,qBAAA,CAAGd,oBAAoB,CAACe,gBAAgB,CAAC,UAAAD,qBAAA,iBAAtCA,qBAAA,CAAwCG,QAAQ,CAE5E;AACA,KAAM,CAAAC,wBAAwB,CAAGF,mBAAmB,EAAI,CACvDG,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,QAAQ,CACfC,IAAI,CAAE,EAAE,CACRC,cAAc,CAAEhB,gBAAgB,CAChCiB,4BAA4B,CAAE,IAAI,CAClCC,QAAQ,CAAE,kBAAkB,CAC5BC,aAAa,CAAE,KAChB,CAAC,CACD,MAAO,CAAAT,wBAAwB,CAChC,CAAC,CAAC,CAEF,KAAM,CAAAU,WAAW,CAAGA,CAAA,GAAM,CACzBhC,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CACD,KAAM,CAAAiC,iBAAiB,CAAGA,CAAA,GAAM,CAC/B9B,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CACD,KAAM,CAAA+B,gBAAgB,CAAIC,KAAa,EAAK,CAC3C,KAAM,CAAAC,QAAQ,CAAG,EAAE,CAAG,CAACD,KAAK,CAAG,CAAC,EAAI,CAAC,CACrCE,gBAAgB,CAAC,MAAM,CAAED,QAAQ,CAAC,CACnC,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAGA,CAAA,GAAM,CAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACArC,kBAAkB,CAAC,KAAK,CAAC,CACzBgC,iBAAiB,CAAC,CAAC,CACnB;AACA;AACA;AACA;AACA,KAAM,CAAAM,eAAe,CAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC,CACvE,KAAM,CAAAC,eAAe,CAAGF,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAEjE,KAAM,CAAAE,sBAAsB,CAAGH,QAAQ,CAACI,gBAAgB,CAAc,oBAAoB,CAAC,CAC3FD,sBAAsB,CAACE,OAAO,CAAEC,OAAO,EAAK,CAC3CA,OAAO,CAACC,KAAK,CAACC,OAAO,CAAG,EAAE,CAAE;AAC7B,CAAC,CAAC,CAEF,GAAIT,eAAe,CAAE,CACpBA,eAAe,CAACU,MAAM,CAAC,CAAC,CACzB,CACA;AACA;AACA;AACA;AACAjD,eAAe,CAAC,KAAK,CAAC,CACtBc,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAuB,gBAAgB,CAAGA,CAACa,GAAQ,CAAEf,KAAU,GAAK,CAClDlB,oBAAoB,CAAEkC,SAAc,GAAM,CACzC,GAAGA,SAAS,CACZ,CAACD,GAAG,EAAGf,KACR,CAAC,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAiB,kBAAkB,CAAGA,CAAA,GAAM,CAChClD,cAAc,CAACc,iBAAiB,CAAC,CACjCgB,WAAW,CAAC,CAAC,CACbhC,eAAe,CAAC,KAAK,CAAC,CACtBc,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,mBACCtB,IAAA,QACC6D,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7B7D,KAAA,QAAK4D,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7B7D,KAAA,QAAK4D,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnC/D,IAAA,CAACd,UAAU,EACV,aAAYqB,SAAS,CAAC,SAAS,CAAE,CACjCyD,OAAO,CAAExB,WAAY,CAAAuB,QAAA,cAErB/D,IAAA,CAACH,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbG,IAAA,QAAK8D,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAExD,SAAS,CAAC,SAAS,CAAC,CAAM,CAAC,cACzDP,IAAA,CAACd,UAAU,EACV+E,IAAI,CAAC,OAAO,CACZ,aAAY1D,SAAS,CAAC,OAAO,CAAE,CAC/ByD,OAAO,CAAExB,WAAY,CAAAuB,QAAA,cAErB/D,IAAA,CAACT,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNS,IAAA,QAAK8D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC9B7D,KAAA,QAAK4D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC9B7D,KAAA,CAACnB,GAAG,EAAC+E,SAAS,CAAC,mBAAmB,CAACI,EAAE,CAAE,CAAEC,MAAM,CAAE,SAAU,CAAE,CAAAJ,QAAA,eAC5D/D,IAAA,CAAChB,UAAU,EAAC8E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExD,SAAS,CAAC,kBAAkB,CAAC,CAAa,CAAC,cACxFP,IAAA,SACCgE,OAAO,CAAElB,iBAAkB,CAC3BsB,uBAAuB,CAAE,CAAEC,MAAM,CAAE1E,QAAS,CAAE,CAC9C4D,KAAK,CAAE,CAAGe,OAAO,CAAE,KAAK,CAAEC,WAAW,CAAE,MAAO,CAAE,CAChD,CAAC,CAAC,GAAG,EACF,CAAC,cACNrE,KAAA,CAACnB,GAAG,EAAC+E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnC/D,IAAA,CAAChB,UAAU,EAAC8E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAExD,SAAS,CAAC,yBAAyB,CAAC,CAAa,CAAC,cAE5FL,KAAA,QACC4D,SAAS,CAAC,gBAAgB,CAC1BP,KAAK,CAAE,CAAEe,OAAO,CAAE,KAAM,CAAE,CAAAP,QAAA,eAE1B7D,KAAA,CAACnB,GAAG,EACH+E,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEI,OAAO,CAAE,cAAc,CAACE,YAAY,CAAC,cAAe,CAAE,CAAAT,QAAA,eAE5D7D,KAAA,CAAClB,UAAU,EACV8E,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEO,QAAQ,CAAE,iBAAiB,CAAEC,WAAW,CAAE,cAAc,CAAEC,MAAM,CAAE,KAAM,CAAE,CAAAZ,QAAA,EAChF,IACE,CAACxD,SAAS,CAAC,aAAa,CAAC,EAChB,CAAC,cACbP,IAAA,CAACf,SAAS,EACT2F,OAAO,CAAC,UAAU,CAClBjC,KAAK,CAAEnB,iBAAiB,CAACO,SAAU,CAEnCkC,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/Be,QAAQ,CAAGC,CAAC,EAAKjC,gBAAgB,CAAC,WAAW,CAAEiC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE,CAC/DqC,UAAU,CAAE,CACXC,YAAY,CAAE,GAAG,CACjBf,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEgB,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACF,CAAC,EACE,CAAC,cACNhF,KAAA,CAACnB,GAAG,EACH+E,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEI,OAAO,CAAE,cAAc,CAACE,YAAY,CAAC,cAAe,CAAE,CAAAT,QAAA,eAE5D7D,KAAA,CAAClB,UAAU,EACV8E,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEO,QAAQ,CAAE,iBAAiB,CAAEC,WAAW,CAAE,cAAc,CAAEC,MAAM,CAAE,KAAM,CAAE,CAAAZ,QAAA,EAChF,IACE,CAACxD,SAAS,CAAC,aAAa,CAAC,EAChB,CAAC,cACbP,IAAA,CAACf,SAAS,EACT2F,OAAO,CAAC,UAAU,CAClBjC,KAAK,CAAEnB,iBAAiB,CAACQ,SAAU,CAEnCiC,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/Be,QAAQ,CAAGC,CAAC,EAAKjC,gBAAgB,CAAC,WAAW,CAAEiC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE,CAC/DqC,UAAU,CAAE,CACXC,YAAY,CAAE,GAAG,CACjBf,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEgB,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACF,CAAC,EACE,CAAC,EACF,CAAC,EACF,CAAC,cAENhF,KAAA,CAACnB,GAAG,EACH8E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEiB,aAAa,CAAE,QAAQ,CAAEC,MAAM,CAAE,iBAAiB,CAACd,OAAO,CAAC,gBAAiB,CAAE,CAAAP,QAAA,eAEpF/D,IAAA,CAAChB,UAAU,EAAC8E,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEI,OAAO,CAAE,sBAAuB,CAAE,CAAAP,QAAA,CAAExD,SAAS,CAAC,MAAM,CAAC,CAAa,CAAC,cACrHL,KAAA,CAACb,MAAM,EACNsD,KAAK,CAAEnB,iBAAiB,CAACS,IAAK,CAC9B4C,QAAQ,CAAGC,CAAC,EAAKjC,gBAAgB,CAAC,MAAM,CAAEiC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE,CAC1D0C,YAAY,MACZvB,SAAS,CAAC,qBAAqB,CAE/BI,EAAE,CAAE,CACHoB,KAAK,CAAE,iBAAiB,CACxBC,YAAY,CAAE,MAAM,CACpB,0CAA0C,CAAE,CAAEL,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAE9B,CAAE,CACJjB,IAAI,CAAC,OAAO,CACZuB,WAAW,CAAGC,QAAQ,eACrBvF,KAAA,CAACnB,GAAG,EAACmF,EAAE,CAAE,CAAEwB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEL,KAAK,CAAE,MAAO,CAAE,CAAAvB,QAAA,eAC5E/D,IAAA,SAAA+D,QAAA,CAAOxD,SAAS,CAACkF,QAAQ,CAAC,CAAO,CAAC,cAClCvF,KAAA,SAAA6D,QAAA,EACE0B,QAAQ,GAAK,MAAM,eAAIzF,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE5E,UAAW,CAAE,CAAE,CAAC,CAChFgG,QAAQ,GAAK,UAAU,eAAIzF,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE3E,YAAa,CAAE,CAAE,CAAC,CACtF+F,QAAQ,GAAK,OAAO,eAAIzF,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAEzE,KAAM,CAAE,CAAE,CAAC,EACxE,CAAC,EACH,CACJ,CAAAmE,QAAA,eAEF7D,KAAA,CAACZ,QAAQ,EACRuE,EAAE,CAAC,mBAAmB,CACtBlB,KAAK,CAAC,MAAM,CAAAoB,QAAA,eAEZ/D,IAAA,SAAA+D,QAAA,CAAOxD,SAAS,CAAC,MAAM,CAAC,CAAO,CAAC,cAChCP,IAAA,SAAMuD,KAAK,CAAE,CAAEqC,UAAU,CAAE,MAAM,CAAEF,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAS,CAAE,CAAA9B,QAAA,cAC1E/D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE5E,UAAW,CAAE,CAAE,CAAC,CACpD,CAAC,EACE,CAAC,cACXS,KAAA,CAACZ,QAAQ,EACRuE,EAAE,CAAC,mBAAmB,CACtBlB,KAAK,CAAC,UAAU,CAAAoB,QAAA,eAEhB/D,IAAA,SAAA+D,QAAA,CAAOxD,SAAS,CAAC,UAAU,CAAC,CAAO,CAAC,cACpCP,IAAA,SAAMuD,KAAK,CAAE,CAAEqC,UAAU,CAAE,MAAM,CAAEF,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAS,CAAE,CAAA9B,QAAA,cAC1E/D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE3E,YAAa,CAAE,CAAE,CAAC,CACtD,CAAC,EACE,CAAC,cACXQ,KAAA,CAACZ,QAAQ,EACRuE,EAAE,CAAC,mBAAmB,CACtBlB,KAAK,CAAC,OAAO,CAAAoB,QAAA,eAEb/D,IAAA,SAAA+D,QAAA,CAAOxD,SAAS,CAAC,OAAO,CAAC,CAAO,CAAC,cACjCP,IAAA,SAAMuD,KAAK,CAAE,CAAEqC,UAAU,CAAE,MAAM,CAAEF,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAS,CAAE,CAAA9B,QAAA,cAC1E/D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAEzE,KAAM,CAAE,CAAE,CAAC,CAC/C,CAAC,EACE,CAAC,EACJ,CAAC,EACL,CAAC,cAENM,KAAA,CAACnB,GAAG,EACH8E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7B/D,IAAA,CAAChB,UAAU,EACV6E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAE9BxD,SAAS,CAAC,OAAO,CAAC,CACR,CAAC,cACbP,IAAA,UACC8F,IAAI,CAAC,OAAO,CACZnD,KAAK,CAAEnB,iBAAiB,CAACU,KAAM,CAC/B2C,QAAQ,CAAGC,CAAC,EAAKjC,gBAAgB,CAAC,OAAO,CAAEiC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE,CAC3DmB,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,cAEN5D,KAAA,CAACnB,GAAG,EACH8E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7B/D,IAAA,CAAChB,UAAU,EACV6E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAE9BxD,SAAS,CAAC,MAAM,CAAC,CACP,CAAC,cAEbP,IAAA,CAACZ,WAAW,EACXyE,EAAE,CAAC,mBAAmB,CACtBe,OAAO,CAAC,UAAU,CAClBmB,SAAS,MACTjC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAE/B/D,IAAA,CAACX,MAAM,EACN2G,YAAY,CAAE,CAAE,CAChBnC,EAAE,CAAC,mBAAmB,CACtBlB,KAAK,CAAE,CAACnB,iBAAiB,CAACW,IAAI,CAAG,EAAE,EAAI,CAAC,CAAG,CAAE,CAC7C0C,QAAQ,CAAGC,CAAC,EAAKpC,gBAAgB,CAACuD,MAAM,CAACnB,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,CAAE,CAC1DuB,EAAE,CAAE,CACHqB,YAAY,CAAE,MAAM,CACpB,0CAA0C,CAAE,CAAEL,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAC9B,CAAE,CAAAnB,QAAA,CAEH,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACmC,GAAG,CAAEjC,IAAI,eACrCjE,IAAA,CAACV,QAAQ,EACRuE,EAAE,CAAC,mBAAmB,CAEtBlB,KAAK,CAAEsB,IAAK,CAAAF,QAAA,CAEXE,IAAI,EAHAA,IAII,CACV,CAAC,CACK,CAAC,CACG,CAAC,EACV,CAAC,cACN/D,KAAA,CAACnB,GAAG,EACH8E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEiB,aAAa,CAAE,QAAQ,CAAEC,MAAM,CAAE,iBAAiB,CAAEe,GAAG,CAAE,KAAK,CAAC7B,OAAO,CAAC,gBAAiB,CAAE,CAAAP,QAAA,eAGhG7D,KAAA,CAACnB,GAAG,EACH8E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,oBAAoB,CAC9BI,EAAE,CAAE,CACHwB,OAAO,CAAE,MAAM,CACfG,UAAU,CAAE,QAAQ,CACpBF,cAAc,CAAE,eAAe,CAC/BL,KAAK,CAAE,MACR,CAAE,CAAAvB,QAAA,eAEF/D,IAAA,CAAChB,UAAU,EACV8E,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEkC,SAAS,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAO,CAAE/B,OAAO,CAAC,cAAc,CAAE,CAAAP,QAAA,CAEnExD,SAAS,CAAC,iBAAiB,CAAC,CAClB,CAAC,cACbL,KAAA,UAAO4D,SAAS,CAAC,eAAe,CAAAC,QAAA,eACrC/D,IAAA,UACI8F,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAElF,gBAAiB,CAC1ByD,QAAQ,CAAGC,CAAC,EAAK,CACbjC,gBAAgB,CAAC,gBAAgB,CAAEiC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAC,CACpDjF,mBAAmB,CAACyD,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAC,CACzC,CAAE,CACFC,IAAI,CAAC,gBAAgB,CACxB,CAAC,cACFvG,IAAA,SAAM8D,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC7B,CAAC,EACK,CAAC,CACL1C,gBAAgB,eAChBlB,KAAA,CAACnB,GAAG,EACH8E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,oBAAoB,CAC9BI,EAAE,CAAE,CACHwB,OAAO,CAAE,MAAM,CACfG,UAAU,CAAE,QAAQ,CACpBF,cAAc,CAAE,eAAe,CAC/BL,KAAK,CAAC,MACP,CAAE,CAAAvB,QAAA,eAEF/D,IAAA,CAAChB,UAAU,EACV6E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CAAEkC,SAAS,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAO,CAACf,KAAK,CAAC,MAAM,CAAChB,OAAO,CAAC,cAAe,CAAE,CAAAP,QAAA,CAEhFxD,SAAS,CAAC,iCAAiC,CAAC,CAClC,CAAC,cACbL,KAAA,UAAO4D,SAAS,CAAC,eAAe,CAAAC,QAAA,eACtC/D,IAAA,UACI8F,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAE9E,iBAAiB,CAACa,4BAA6B,CACxDwC,QAAQ,CAAGC,CAAC,EAAKjC,gBAAgB,CAAC,8BAA8B,CAAEiC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE,CACpFC,IAAI,CAAC,8BAA8B,CACtC,CAAC,cACFvG,IAAA,SAAM8D,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC7B,CAAC,EAEM,CACL,EACG,CAAC,CACLtC,iBAAiB,CAACe,aAAa,GAAK,KAAK,eACzCrC,KAAA,CAACnB,GAAG,EACH8E,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEiB,aAAa,CAAE,QAAQ,CAAEC,MAAM,CAAE,iBAAiB,CAAEd,OAAO,CAAC,gBAAgB,CAAE,CAAAP,QAAA,eAEpF/D,IAAA,CAAChB,UAAU,EAAC8E,SAAS,CAAC,qBAAqB,CAACI,EAAE,CAAE,CAAEI,OAAO,CAAE,sBAAuB,CAAE,CAAAP,QAAA,CAAExD,SAAS,CAAC,WAAW,CAAC,CAAa,CAAC,cAG1HP,IAAA,CAACZ,WAAW,EACXwF,OAAO,CAAC,UAAU,CAClBmB,SAAS,MACTjC,SAAS,CAAC,qBAAqB,CAC/BI,EAAE,CAAE,CACHqB,YAAY,CAAE,MAAM,CACpBD,KAAK,CAAE,iBAAiB,CACxB,0CAA0C,CAAE,CAAEJ,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,qBAAqB,CAAC,CAACA,MAAM,CAAG,8BAA8B,CAC7D,CAAE,CAAAnB,QAAA,cAEJ7D,KAAA,CAACb,MAAM,EACNsD,KAAK,CAAEnB,iBAAiB,CAACc,QAAU;AAAA,CACnCuC,QAAQ,CAAGC,CAAM,EAAKjC,gBAAgB,CAAC,UAAU,CAAEiC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE,CACnE4D,IAAI,CAAC,UAAU,CACfrC,EAAE,CAAE,CAAEoB,KAAK,CAAE,iBAAiB,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAxB,QAAA,eAEvD/D,IAAA,CAACV,QAAQ,EAACqD,KAAK,CAAC,kBAAkB,CAAAoB,QAAA,CAAExD,SAAS,CAAC,kBAAkB,CAAC,CAAW,CAAC,cAC7EP,IAAA,CAACV,QAAQ,EAACqD,KAAK,CAAC,kBAAkB,CAAAoB,QAAA,CAAExD,SAAS,CAAC,kBAAkB,CAAC,CAAW,CAAC,EACtE,CAAC,CACG,CAAC,EACV,CACL,cACDL,KAAA,CAACnB,GAAG,EAAC+E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/D,IAAA,CAAChB,UAAU,EAAC8E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExD,SAAS,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAIvFL,KAAA,UAAO4D,SAAS,CAAC,eAAe,CAAAC,QAAA,eACpC/D,IAAA,UACI8F,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAE9E,iBAAiB,CAACe,aAAc,CACzCsC,QAAQ,CAAGC,CAAC,EAAKjC,gBAAgB,CAAC,eAAe,CAAEiC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE,CACrEC,IAAI,CAAC,eAAe,CACvB,CAAC,cACFvG,IAAA,SAAM8D,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC7B,CAAC,EAEI,CAAC,EACF,CAAC,CACF,CAAC,cACN9D,IAAA,QAAK8D,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClC/D,IAAA,CAACb,MAAM,EACNyF,OAAO,CAAC,WAAW,CACnBZ,OAAO,CAAEJ,kBAAmB,CAC5BE,SAAS,CAAC,WAAW,CAAAC,QAAA,CAEpBxD,SAAS,CAAC,OAAO,CAAC,CACZ,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,CAET,CAAC,CAED,cAAe,CAAAJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}