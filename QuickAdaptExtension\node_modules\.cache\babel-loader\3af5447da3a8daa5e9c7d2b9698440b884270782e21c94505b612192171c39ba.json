{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\RTE\\\\RTESection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useRef, memo } from \"react\";\nimport { Box, Tooltip, IconButton } from \"@mui/material\";\nimport useDrawerStore from \"../../../../store/drawerStore\";\nimport { copyicon, deleteicon } from \"../../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  items: {\n    id,\n    style,\n    rteBoxValue,\n    placeholder\n  },\n  boxRef,\n  handleFocus,\n  handleeBlur,\n  isPopoverOpen,\n  setIsPopoverOpen,\n  currentRTEFocusedId\n}, ref) => {\n  var _toolTipGuideMetaData, _toolTipGuideMetaData2, _toolTipGuideMetaData3, _toolTipGuideMetaData4, _toolTipGuideMetaData5, _toolTipGuideMetaData6, _boxRef$current5, _boxRef$current5$inne, _boxRef$current6, _boxRef$current6$inne;\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setIsUnSavedChanges,\n    setHtmlContent,\n    textvaluess,\n    setTextvaluess,\n    backgroundC,\n    setBackgroundC,\n    Bbordercolor,\n    BborderSize,\n    bpadding,\n    sectionColor,\n    setSectionColor,\n    handleTooltipRTEBlur,\n    handleTooltipRTEValue,\n    handleRTEDeleteSection,\n    handleRTECloneSection,\n    tooltip,\n    currentStep,\n    toolTipGuideMetaData\n  } = useDrawerStore(state => state);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [savedRange, setSaveRange] = useState(undefined);\n  const [anchorPosition, setAnchorPosition] = useState({\n    top: 300,\n    left: 700\n  });\n  const [isPlaceholderVisible, setIsPlaceholderVisible] = useState(true);\n  const [isEditing, setIsEditing] = useState(false);\n  const editorRef = useRef(null);\n  const containerRef = useRef(null);\n\n  // const handleInput = () => {\n  // \t// Update the content state when user types\n  // \tif (boxRef.current) {\n  // \t\tconst updatedContent = boxRef.current.innerHTML;\n  // \t\tsetContent(updatedContent); // Store the content in state\n  // \t\tsetHtmlContent(updatedContent); // Update the HTML content\n  // \t\tsetIsUnSavedChanges(true);\n  // \t\tpreserveCaretPosition();\n  // \t}\n  // };\n  const preserveCaretPosition = () => {\n    const selection = document.getSelection();\n    if (selection) {\n      const range = selection.getRangeAt(0); // Get the current range (cursor position)\n      setSaveRange(range); // Save the current range for later restoration\n    }\n  };\n  const restoreCaretPosition = () => {\n    if (savedRange && boxRef.current) {\n      const selection = document.getSelection();\n      if (selection) {\n        selection.removeAllRanges();\n        selection.addRange(savedRange); // Restore the saved range\n      }\n    }\n  };\n\n  // useEffect(() => {\n  // \t// After content update, restore the cursor position\n  // \trestoreCaretPosition();\n  // }, [boxRef.current?.innerHTML]); // Run when content changes\n\n  // Remove section\n\n  // useEffect(() => {\n  // \tif (boxRef.current?.innerHTML?.trim()) {\n  // \t\tsetIsUnSavedChanges(true);\n  // \t}\n  // }, [boxRef.current?.innerHTML?.trim()]);\n\n  useEffect(() => {\n    var _boxRef$current, _boxRef$current2, _boxRef$current3;\n    if (rteBoxValue && (_boxRef$current = boxRef.current) !== null && _boxRef$current !== void 0 && _boxRef$current.innerHTML && ((_boxRef$current2 = boxRef.current) === null || _boxRef$current2 === void 0 ? void 0 : _boxRef$current2.innerHTML) !== \"<p><br></p>\") {\n      // @ts-ignore\n      boxRef.current.innerHTML = rteBoxValue;\n      setIsPlaceholderVisible(false);\n    } else if (!textvaluess && (_boxRef$current3 = boxRef.current) !== null && _boxRef$current3 !== void 0 && _boxRef$current3.innerHTML) {\n      // @ts-ignore\n      boxRef.current.innerHTML = \"\";\n      setIsPlaceholderVisible(true);\n    }\n  }, [rteBoxValue, boxRef.current]);\n\n  // Auto-focus the editor when editing mode is activated\n  useEffect(() => {\n    if (isEditing && editorRef.current) {\n      setTimeout(() => {\n        editorRef.current.editor.focus();\n      }, 50);\n    }\n  }, [isEditing]);\n\n  // Handle clicks outside the editor to close editing mode\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Check if the target is inside the editor or related elements\n      if (containerRef.current && !containerRef.current.contains(event.target) &&\n      // Click outside the editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setIsEditing(false); // Close the editor if clicked outside\n      }\n    };\n    if (isEditing) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n      return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n    }\n  }, [isEditing]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        position: \"relative\",\n        //padding: 0,\n        margin: 0,\n        boxSizing: \"border-box\",\n        transition: \"border 0.2s ease-in-out\",\n        backgroundColor: sectionColor || \"defaultColor\"\n        //border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\n        // padding: `${bpadding}px !important` || \"0\",\n      },\n      className: \"qadpt-rte\",\n      id: \"rte-box\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleRTEDeleteSection(id),\n            disabled: ((_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.length) === 1,\n            sx: {\n              \"&:hover\": {\n                backgroundColor: \"transparent !important\"\n              },\n              svg: {\n                path: {\n                  fill: \"var(--primarycolor)\"\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              },\n              style: {\n                opacity: ((_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.length) === 1 ? 0.5 : 1,\n                pointerEvents: 'none',\n                height: \"24px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => handleRTECloneSection(id),\n            sx: {\n              \"&:hover\": {\n                backgroundColor: \"transparent !important\"\n              },\n              svg: {\n                height: \"24px\",\n                path: {\n                  fill: \"var(--primarycolor)\"\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              },\n              style: {\n                opacity: ((_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.length) === 1 ? 0.5 : 1,\n                pointerEvents: 'none',\n                height: \"24px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true),\n        placement: \"top\",\n        slotProps: {\n          tooltip: {\n            sx: {\n              backgroundColor: \"white\",\n              color: \"black\",\n              borderRadius: \"4px\",\n              padding: '0px 4px',\n              border: \"1px dashed var(--primarycolor)\"\n            }\n          }\n        },\n        PopperProps: {\n          modifiers: [{\n            name: \"preventOverflow\",\n            options: {\n              boundary: \"viewport\" // Ensure tooltip doesn't go outside the viewport\n            }\n          }, {\n            name: \"flip\",\n            options: {\n              enabled: true\n            }\n          }]\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          contentEditable: true,\n          ref: boxRef,\n          component: \"div\",\n          id: `rt-editor${id}`,\n          onClick: e => {\n            // Immediately activate editing mode and focus on first click\n            handleFocus(id);\n            setIsPlaceholderVisible(false);\n            // Focus the contentEditable element to make it ready for typing\n            setTimeout(() => {\n              if (boxRef.current) {\n                boxRef.current.focus();\n              }\n            }, 0);\n          },\n          onFocus: e => {\n            handleFocus(id);\n            setIsPlaceholderVisible(false);\n          },\n          onBlur: e => {\n            var _boxRef$current4, _boxRef$current4$inne;\n            handleeBlur(id);\n            if (!((_boxRef$current4 = boxRef.current) !== null && _boxRef$current4 !== void 0 && (_boxRef$current4$inne = _boxRef$current4.innerHTML) !== null && _boxRef$current4$inne !== void 0 && _boxRef$current4$inne.trim())) {\n              setIsPlaceholderVisible(true);\n            }\n          }\n          //onInput={handleInput}\n          ,\n          sx: {\n            height: \"100%\",\n            width: \"100%\",\n            padding: \"8px\",\n            outline: \"none\",\n            overflowY: \"auto\",\n            color: isPlaceholderVisible && !((_boxRef$current5 = boxRef.current) !== null && _boxRef$current5 !== void 0 && (_boxRef$current5$inne = _boxRef$current5.innerHTML) !== null && _boxRef$current5$inne !== void 0 && _boxRef$current5$inne.trim()) ? \"transparent\" : \"black\",\n            position: \"relative\",\n            cursor: \"text\" // Add cursor pointer to indicate it's clickable\n            //backgroundColor: backgroundC || \"defaultColor\",\n          },\n          suppressContentEditableWarning: true\n          // Set content via state using dangerouslySetInnerHTML\n          ,\n          dangerouslySetInnerHTML: {\n            __html: rteBoxValue\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 6\n      }, this), isPlaceholderVisible && !((_boxRef$current6 = boxRef.current) !== null && _boxRef$current6 !== void 0 && (_boxRef$current6$inne = _boxRef$current6.innerHTML) !== null && _boxRef$current6$inne !== void 0 && _boxRef$current6$inne.trim()) && /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          position: \"absolute\",\n          color: \"gray\",\n          pointerEvents: \"none\",\n          userSelect: \"none\",\n          textAlign: \"center\",\n          whiteSpace: \"nowrap\",\n          transform: \"translate(-50%,-50%)\",\n          top: \"50%\",\n          left: \"50%\",\n          padding: \"8px\"\n          // outline: \"none\",\n        },\n        id: \"rte-placeholder\",\n        children: translate(placeholder)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n}, \"ZJ4Lb8i3s5IMuCZPpnULmDxX3CE=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"ZJ4Lb8i3s5IMuCZPpnULmDxX3CE=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default _c3 = /*#__PURE__*/memo(RTEsection);\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");\n$RefreshReg$(_c3, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useRef", "memo", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "useDrawerStore", "copyicon", "deleteicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "items", "id", "style", "rteBoxValue", "placeholder", "boxRef", "handleFocus", "handleeBlur", "isPopoverOpen", "setIsPopoverOpen", "currentRTEFocusedId", "ref", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_boxRef$current5", "_boxRef$current5$inne", "_boxRef$current6", "_boxRef$current6$inne", "t", "translate", "setIsUnSavedChanges", "setHtmlContent", "textvaluess", "setTextvaluess", "backgroundC", "setBackgroundC", "Bbordercolor", "BborderSize", "bpadding", "sectionColor", "setSectionColor", "handleTooltipRTEBlur", "handleTooltipRTEValue", "handleRTEDeleteSection", "handleRTECloneSection", "tooltip", "currentStep", "toolTipGuideMetaData", "state", "anchorEl", "setAnchorEl", "savedRange", "setSaveRange", "undefined", "anchorPosition", "setAnchorPosition", "top", "left", "isPlaceholderVisible", "setIsPlaceholderVisible", "isEditing", "setIsEditing", "editor<PERSON><PERSON>", "containerRef", "preserveCaretPosition", "selection", "document", "getSelection", "range", "getRangeAt", "restoreCaretPosition", "current", "removeAllRanges", "addRange", "_boxRef$current", "_boxRef$current2", "_boxRef$current3", "innerHTML", "setTimeout", "editor", "focus", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "addEventListener", "removeEventListener", "children", "sx", "display", "alignItems", "position", "margin", "boxSizing", "transition", "backgroundColor", "className", "title", "size", "onClick", "disabled", "containers", "length", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "opacity", "pointerEvents", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placement", "slotProps", "color", "borderRadius", "padding", "border", "PopperProps", "modifiers", "name", "options", "boundary", "enabled", "contentEditable", "component", "e", "onFocus", "onBlur", "_boxRef$current4", "_boxRef$current4$inne", "trim", "width", "outline", "overflowY", "cursor", "suppressContentEditableWarning", "userSelect", "textAlign", "whiteSpace", "transform", "_c2", "_c3", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/RTE/RTESection.tsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo } from \"react\";\r\nimport { Box, Popover, Tooltip, Typography, IconButton } from \"@mui/material\";\r\n\r\nimport RTE from \"./RTE\";\r\nimport useDrawerStore, { IRTEContainer, TSectionType } from \"../../../../store/drawerStore\";\r\nimport { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from \"@mui/icons-material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { copyicon, deleteicon } from \"../../../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n\titems: IRTEContainer;\r\n\tboxRef: React.RefObject<HTMLDivElement>;\r\n\thandleFocus: (id: string) => void;\r\n\thandleeBlur: (id: string) => void;\r\n\r\n\tisPopoverOpen: boolean;\r\n\tsetIsPopoverOpen: (params: boolean) => void;\r\n\tcurrentRTEFocusedId: string;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n\t(\r\n\t\t{\r\n\t\t\titems: { id, style, rteBoxValue, placeholder },\r\n\t\t\tboxRef,\r\n\t\t\thandleFocus,\r\n\t\t\thandleeBlur,\r\n\r\n\t\t\tisPopoverOpen,\r\n\t\t\tsetIsPopoverOpen,\r\n\t\t\tcurrentRTEFocusedId,\r\n\t\t},\r\n\t\tref\r\n\t) => {\r\n\t\tconst { t: translate } = useTranslation();\r\n\t\tconst {\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tsetHtmlContent,\r\n\t\t\ttextvaluess,\r\n\t\t\tsetTextvaluess,\r\n\t\t\tbackgroundC,\r\n\t\t\tsetBackgroundC,\r\n\t\t\tBbordercolor,\r\n\t\t\tBborderSize,\r\n\t\t\tbpadding,\r\n\t\t\tsectionColor,\r\n\t\t\tsetSectionColor,\r\n\t\t\thandleTooltipRTEBlur,\r\n\t\t\thandleTooltipRTEValue,\r\n\t\t\thandleRTEDeleteSection,\r\n\t\t\thandleRTECloneSection,\r\n\t\t\ttooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t} = useDrawerStore((state) => state);\r\n\t\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\t\tconst [savedRange, setSaveRange] = useState<Range | undefined>(undefined);\r\n\t\tconst [anchorPosition, setAnchorPosition] = useState<{ top: number; left: number }>({ top: 300, left: 700 });\r\n\t\tconst [isPlaceholderVisible, setIsPlaceholderVisible] = useState(true);\r\n\t\tconst [isEditing, setIsEditing] = useState(false);\r\n\t\tconst editorRef = useRef(null);\r\n\t\tconst containerRef = useRef<HTMLDivElement | null>(null);\r\n\r\n\t\t// const handleInput = () => {\r\n\t\t// \t// Update the content state when user types\r\n\t\t// \tif (boxRef.current) {\r\n\t\t// \t\tconst updatedContent = boxRef.current.innerHTML;\r\n\t\t// \t\tsetContent(updatedContent); // Store the content in state\r\n\t\t// \t\tsetHtmlContent(updatedContent); // Update the HTML content\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t\tpreserveCaretPosition();\r\n\t\t// \t}\r\n\t\t// };\r\n\t\tconst preserveCaretPosition = () => {\r\n\t\t\tconst selection = document.getSelection();\r\n\t\t\tif (selection) {\r\n\t\t\t\tconst range = selection.getRangeAt(0); // Get the current range (cursor position)\r\n\t\t\t\tsetSaveRange(range); // Save the current range for later restoration\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst restoreCaretPosition = () => {\r\n\t\t\tif (savedRange && boxRef.current) {\r\n\t\t\t\tconst selection = document.getSelection();\r\n\t\t\t\tif (selection) {\r\n\t\t\t\t\tselection.removeAllRanges();\r\n\t\t\t\t\tselection.addRange(savedRange); // Restore the saved range\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \t// After content update, restore the cursor position\r\n\t\t// \trestoreCaretPosition();\r\n\t\t// }, [boxRef.current?.innerHTML]); // Run when content changes\r\n\r\n\t\t// Remove section\r\n\r\n\t\t// useEffect(() => {\r\n\t\t// \tif (boxRef.current?.innerHTML?.trim()) {\r\n\t\t// \t\tsetIsUnSavedChanges(true);\r\n\t\t// \t}\r\n\t\t// }, [boxRef.current?.innerHTML?.trim()]);\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tif (rteBoxValue && boxRef.current?.innerHTML && boxRef.current?.innerHTML !== \"<p><br></p>\") {\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tboxRef.current.innerHTML = rteBoxValue;\r\n\t\t\t\tsetIsPlaceholderVisible(false);\r\n\t\t\t} else if (!textvaluess && boxRef.current?.innerHTML) {\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tboxRef.current.innerHTML = \"\";\r\n\t\t\t\tsetIsPlaceholderVisible(true);\r\n\t\t\t}\r\n\t\t}, [rteBoxValue, boxRef.current]);\r\n\r\n\t\t// Auto-focus the editor when editing mode is activated\r\n\t\tuseEffect(() => {\r\n\t\t\tif (isEditing && editorRef.current) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t(editorRef.current as any).editor.focus();\r\n\t\t\t\t}, 50);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\t// Handle clicks outside the editor to close editing mode\r\n\t\tuseEffect(() => {\r\n\t\t\tconst handleClickOutside = (event: MouseEvent) => {\r\n\t\t\t\tconst isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n\t\t\t\tconst isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n\t\t\t\tconst isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n\t\t\t\tconst isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n\t\t\t\tconst isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n\t\t\t\tconst isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n\t\t\t\tconst isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n\t\t\t\t// Check if the target is inside the editor or related elements\r\n\t\t\t\tif (\r\n\t\t\t\t\tcontainerRef.current &&\r\n\t\t\t\t\t!containerRef.current.contains(event.target as Node) && // Click outside the editor container\r\n\t\t\t\t\t!isInsidePopup && // Click outside the popup\r\n\t\t\t\t\t!isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n\t\t\t\t\t!isInsideWorkplacePopup && // Click outside the workplace popup\r\n\t\t\t\t\t!isSelectionMarker && // Click outside selection markers\r\n\t\t\t\t\t!isLinkPopup && // Click outside link input popup\r\n\t\t\t\t\t!isInsideToolbarButton &&// Click outside the toolbar button\r\n\t\t\t\t\t!isInsertButton &&\r\n\t\t\t\t\t!isInsideJoditPopupContent &&\r\n\t\t\t\t\t!isInsideAltTextPopup\r\n\t\t\t\t) {\r\n\t\t\t\t\tsetIsEditing(false); // Close the editor if clicked outside\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\tif (isEditing) {\r\n\t\t\t\tdocument.addEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t\treturn () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n\t\t\t}\r\n\t\t}, [isEditing]);\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t//padding: 0,\r\n\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\ttransition: \"border 0.2s ease-in-out\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor || \"defaultColor\",\r\n\t\t\t\t\t\t//border: `${BborderSize}px solid ${Bbordercolor} !important` || \"defaultColor\",\r\n\t\t\t\t\t\t// padding: `${bpadding}px !important` || \"0\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tclassName=\"qadpt-rte\"\r\n\t\t\t\t\tid=\"rte-box\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\ttitle={\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleRTEDeleteSection(id)}\r\n\t\t\t\t\t\t\t\t\tdisabled={\r\n\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleRTECloneSection(id)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent !important\",\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"var(--primarycolor)\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\t}}/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tplacement=\"top\"\r\n\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"white\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\tpadding: '0px 4px',\r\n\t\t\t\t\t\t\t\t\tborder: \"1px dashed var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\tmodifiers: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tname: \"preventOverflow\",\r\n\t\t\t\t\t\t\t\t\toptions: {\r\n\t\t\t\t\t\t\t\t\t\tboundary: \"viewport\", // Ensure tooltip doesn't go outside the viewport\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tname: \"flip\",\r\n\t\t\t\t\t\t\t\t\toptions: {\r\n\t\t\t\t\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tcontentEditable\r\n\t\t\t\t\t\t\tref={boxRef}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={`rt-editor${id}`}\r\n\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t// Immediately activate editing mode and focus on first click\r\n\t\t\t\t\t\t\t\thandleFocus(id);\r\n\t\t\t\t\t\t\t\tsetIsPlaceholderVisible(false);\r\n\t\t\t\t\t\t\t\t// Focus the contentEditable element to make it ready for typing\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tif (boxRef.current) {\r\n\t\t\t\t\t\t\t\t\t\tboxRef.current.focus();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}, 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonFocus={(e) => {\r\n\t\t\t\t\t\t\t\thandleFocus(id);\r\n\t\t\t\t\t\t\t\tsetIsPlaceholderVisible(false);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonBlur={(e) => {\r\n\t\t\t\t\t\t\t\thandleeBlur(id);\r\n\t\t\t\t\t\t\t\tif (!boxRef.current?.innerHTML?.trim()) {\r\n\t\t\t\t\t\t\t\t\tsetIsPlaceholderVisible(true);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t//onInput={handleInput}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\toutline: \"none\",\r\n\t\t\t\t\t\t\t\toverflowY: \"auto\",\r\n\t\t\t\t\t\t\t\tcolor: isPlaceholderVisible && !boxRef.current?.innerHTML?.trim() ? \"transparent\" : \"black\",\r\n\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\tcursor: \"text\", // Add cursor pointer to indicate it's clickable\r\n\t\t\t\t\t\t\t\t//backgroundColor: backgroundC || \"defaultColor\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tsuppressContentEditableWarning={true}\r\n\t\t\t\t\t\t\t// Set content via state using dangerouslySetInnerHTML\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: rteBoxValue }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t{isPlaceholderVisible && !boxRef.current?.innerHTML?.trim() && (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\tcolor: \"gray\",\r\n\t\t\t\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\t\t\t\tuserSelect: \"none\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\ttransform: \"translate(-50%,-50%)\",\r\n\t\t\t\t\t\t\t\ttop: \"50%\",\r\n\t\t\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t// outline: \"none\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tid=\"rte-placeholder\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(placeholder)}\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</Box>\r\n\t\t\t</>\r\n\t\t);\r\n\t}\r\n);\r\n\r\nexport default memo(RTEsection);\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAaC,IAAI,QAAiB,OAAO;AAChG,SAASC,GAAG,EAAWC,OAAO,EAAcC,UAAU,QAAQ,eAAe;AAG7E,OAAOC,cAAc,MAAuC,+BAA+B;AAI3F,SAASC,QAAQ,EAAEC,UAAU,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAa/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGf,UAAU,CAAAgB,EAAA,GAAAD,EAAA,CACvD,CACC;EACCE,KAAK,EAAE;IAAEC,EAAE;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAY,CAAC;EAC9CC,MAAM;EACNC,WAAW;EACXC,WAAW;EAEXC,aAAa;EACbC,gBAAgB;EAChBC;AACD,CAAC,EACDC,GAAG,KACC;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAAAvB,EAAA;EACJ,MAAM;IAAEwB,CAAC,EAAEC;EAAU,CAAC,GAAG/B,cAAc,CAAC,CAAC;EACzC,MAAM;IACLgC,mBAAmB;IACnBC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,QAAQ;IACRC,YAAY;IACZC,eAAe;IACfC,oBAAoB;IACpBC,qBAAqB;IACrBC,sBAAsB;IACtBC,qBAAqB;IACrBC,OAAO;IACPC,WAAW;IACXC;EACD,CAAC,GAAGpD,cAAc,CAAEqD,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACgE,UAAU,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAoBkE,SAAS,CAAC;EACzE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAgC;IAAEqE,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAI,CAAC,CAAC;EAC5G,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM2E,SAAS,GAAGxE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMyE,YAAY,GAAGzE,MAAM,CAAwB,IAAI,CAAC;;EAExD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM0E,qBAAqB,GAAGA,CAAA,KAAM;IACnC,MAAMC,SAAS,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC;IACzC,IAAIF,SAAS,EAAE;MACd,MAAMG,KAAK,GAAGH,SAAS,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MACvCjB,YAAY,CAACgB,KAAK,CAAC,CAAC,CAAC;IACtB;EACD,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IAClC,IAAInB,UAAU,IAAIxC,MAAM,CAAC4D,OAAO,EAAE;MACjC,MAAMN,SAAS,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC;MACzC,IAAIF,SAAS,EAAE;QACdA,SAAS,CAACO,eAAe,CAAC,CAAC;QAC3BP,SAAS,CAACQ,QAAQ,CAACtB,UAAU,CAAC,CAAC,CAAC;MACjC;IACD;EACD,CAAC;;EAED;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;;EAEA/D,SAAS,CAAC,MAAM;IAAA,IAAAsF,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;IACf,IAAInE,WAAW,KAAAiE,eAAA,GAAI/D,MAAM,CAAC4D,OAAO,cAAAG,eAAA,eAAdA,eAAA,CAAgBG,SAAS,IAAI,EAAAF,gBAAA,GAAAhE,MAAM,CAAC4D,OAAO,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBE,SAAS,MAAK,aAAa,EAAE;MAC5F;MACAlE,MAAM,CAAC4D,OAAO,CAACM,SAAS,GAAGpE,WAAW;MACtCkD,uBAAuB,CAAC,KAAK,CAAC;IAC/B,CAAC,MAAM,IAAI,CAAC3B,WAAW,KAAA4C,gBAAA,GAAIjE,MAAM,CAAC4D,OAAO,cAAAK,gBAAA,eAAdA,gBAAA,CAAgBC,SAAS,EAAE;MACrD;MACAlE,MAAM,CAAC4D,OAAO,CAACM,SAAS,GAAG,EAAE;MAC7BlB,uBAAuB,CAAC,IAAI,CAAC;IAC9B;EACD,CAAC,EAAE,CAAClD,WAAW,EAAEE,MAAM,CAAC4D,OAAO,CAAC,CAAC;;EAEjC;EACAnF,SAAS,CAAC,MAAM;IACf,IAAIwE,SAAS,IAAIE,SAAS,CAACS,OAAO,EAAE;MACnCO,UAAU,CAAC,MAAM;QACfhB,SAAS,CAACS,OAAO,CAASQ,MAAM,CAACC,KAAK,CAAC,CAAC;MAC1C,CAAC,EAAE,EAAE,CAAC;IACP;EACD,CAAC,EAAE,CAACpB,SAAS,CAAC,CAAC;;EAEf;EACAxE,SAAS,CAAC,MAAM;IACf,MAAM6F,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACjD,MAAMC,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGjB,QAAQ,CAAC0B,aAAa,CAAC,cAAc,CAAC,cAAAT,qBAAA,uBAAtCA,qBAAA,CAAwCU,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMM,kBAAkB,IAAAV,sBAAA,GAAGlB,QAAQ,CAAC0B,aAAa,CAAC,gBAAgB,CAAC,cAAAR,sBAAA,uBAAxCA,sBAAA,CAA0CS,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMO,sBAAsB,GAAGD,kBAAkB,MAAAT,sBAAA,GAAInB,QAAQ,CAAC0B,aAAa,CAAC,sBAAsB,CAAC,cAAAP,sBAAA,uBAA9CA,sBAAA,CAAgDQ,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMQ,iBAAiB,GAAId,KAAK,CAACM,MAAM,CAAiBjF,EAAE,CAAC0F,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAZ,sBAAA,GAAGpB,QAAQ,CAAC0B,aAAa,CAAC,wBAAwB,CAAC,cAAAN,sBAAA,uBAAhDA,sBAAA,CAAkDO,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMW,qBAAqB,GAAIjB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMW,cAAc,GAAIlB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,IACC1B,YAAY,CAACQ,OAAO,IACpB,CAACR,YAAY,CAACQ,OAAO,CAACsB,QAAQ,CAACX,KAAK,CAACM,MAAc,CAAC;MAAI;MACxD,CAACG,aAAa;MAAI;MAClB,CAACG,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACE,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAG;MACzB,CAACC,cAAc,IACf,CAACb,yBAAyB,IAC1B,CAACG,oBAAoB,EACpB;QACD7B,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;MACtB;IACD,CAAC;IAED,IAAID,SAAS,EAAE;MACdM,QAAQ,CAACmC,gBAAgB,CAAC,WAAW,EAAEpB,kBAAkB,CAAC;MAC1D,OAAO,MAAMf,QAAQ,CAACoC,mBAAmB,CAAC,WAAW,EAAErB,kBAAkB,CAAC;IAC3E;EACD,CAAC,EAAE,CAACrB,SAAS,CAAC,CAAC;EAEf,oBACC5D,OAAA,CAAAE,SAAA;IAAAqG,QAAA,eACCvG,OAAA,CAACR,GAAG;MACHgH,EAAE,EAAE;QACHC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,UAAU;QACpB;QACAC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,UAAU,EAAE,yBAAyB;QACrCC,eAAe,EAAExE,YAAY,IAAI;QACjC;QACA;MACD,CAAE;MACFyE,SAAS,EAAC,WAAW;MACrBzG,EAAE,EAAC,SAAS;MAAAgG,QAAA,gBAEZvG,OAAA,CAACP,OAAO;QACPwH,KAAK,eACJjH,OAAA,CAAAE,SAAA;UAAAqG,QAAA,gBACCvG,OAAA,CAACN,UAAU;YACVwH,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMxE,sBAAsB,CAACpC,EAAE,CAAE;YAC1C6G,QAAQ,EACP,EAAAlG,qBAAA,GAAA6B,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAA5B,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuCmG,UAAU,cAAAlG,sBAAA,uBAAjDA,sBAAA,CAAmDmG,MAAM,MAAK,CAC9D;YACDd,EAAE,EAAE;cACH,SAAS,EAAE;gBACVO,eAAe,EAAE;cAClB,CAAC;cACDQ,GAAG,EAAE;gBACJC,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YAAAlB,QAAA,eAEFvG,OAAA;cACC0H,uBAAuB,EAAE;gBAAEC,MAAM,EAAE9H;cAAW,CAAE;cAChDW,KAAK,EAAE;gBACNoH,OAAO,EACN,EAAAxG,sBAAA,GAAA2B,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAA1B,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCiG,UAAU,cAAAhG,sBAAA,uBAAjDA,sBAAA,CAAmDiG,MAAM,MAAK,CAAC,GAC5D,GAAG,GACH,CAAC;gBACLO,aAAa,EAAE,MAAM;gBACrBC,MAAM,EAAE;cACT;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACblI,OAAA,CAACN,UAAU;YACVwH,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMvE,qBAAqB,CAACrC,EAAE,CAAE;YACzCiG,EAAE,EAAE;cACH,SAAS,EAAE;gBACVO,eAAe,EAAE;cAClB,CAAC;cACDQ,GAAG,EAAE;gBACJO,MAAM,EAAE,MAAM;gBACdN,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YAAAlB,QAAA,eAEFvG,OAAA;cAAM0H,uBAAuB,EAAE;gBAAEC,MAAM,EAAE/H;cAAS,CAAE;cACpDY,KAAK,EAAE;gBACNoH,OAAO,EACN,EAAAtG,sBAAA,GAAAyB,oBAAoB,CAACD,WAAW,GAAG,CAAC,CAAC,cAAAxB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuC+F,UAAU,cAAA9F,sBAAA,uBAAjDA,sBAAA,CAAmD+F,MAAM,MAAK,CAAC,GAC5D,GAAG,GACH,CAAC;gBACLO,aAAa,EAAE,MAAM;gBACrBC,MAAM,EAAE;cACT;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA,eACZ,CACF;QACDC,SAAS,EAAC,KAAK;QACfC,SAAS,EAAE;UACVvF,OAAO,EAAE;YACR2D,EAAE,EAAE;cACHO,eAAe,EAAE,OAAO;cACxBsB,KAAK,EAAE,OAAO;cACdC,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,SAAS;cAClBC,MAAM,EAAE;YACT;UACD;QACD,CAAE;QACFC,WAAW,EAAE;UACZC,SAAS,EAAE,CACV;YACCC,IAAI,EAAE,iBAAiB;YACvBC,OAAO,EAAE;cACRC,QAAQ,EAAE,UAAU,CAAE;YACvB;UACD,CAAC,EACD;YACCF,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE;cACRE,OAAO,EAAE;YACV;UACD,CAAC;QAEH,CAAE;QAAAvC,QAAA,eAEFvG,OAAA,CAACR,GAAG;UACHuJ,eAAe;UACf9H,GAAG,EAAEN,MAAO;UACZqI,SAAS,EAAE,KAAM;UACjBzI,EAAE,EAAE,YAAYA,EAAE,EAAG;UACrB4G,OAAO,EAAG8B,CAAC,IAAK;YACf;YACArI,WAAW,CAACL,EAAE,CAAC;YACfoD,uBAAuB,CAAC,KAAK,CAAC;YAC9B;YACAmB,UAAU,CAAC,MAAM;cAChB,IAAInE,MAAM,CAAC4D,OAAO,EAAE;gBACnB5D,MAAM,CAAC4D,OAAO,CAACS,KAAK,CAAC,CAAC;cACvB;YACD,CAAC,EAAE,CAAC,CAAC;UACN,CAAE;UACFkE,OAAO,EAAGD,CAAC,IAAK;YACfrI,WAAW,CAACL,EAAE,CAAC;YACfoD,uBAAuB,CAAC,KAAK,CAAC;UAC/B,CAAE;UACFwF,MAAM,EAAGF,CAAC,IAAK;YAAA,IAAAG,gBAAA,EAAAC,qBAAA;YACdxI,WAAW,CAACN,EAAE,CAAC;YACf,IAAI,GAAA6I,gBAAA,GAACzI,MAAM,CAAC4D,OAAO,cAAA6E,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvE,SAAS,cAAAwE,qBAAA,eAAzBA,qBAAA,CAA2BC,IAAI,CAAC,CAAC,GAAE;cACvC3F,uBAAuB,CAAC,IAAI,CAAC;YAC9B;UACD;UACA;UAAA;UACA6C,EAAE,EAAE;YACHsB,MAAM,EAAE,MAAM;YACdyB,KAAK,EAAE,MAAM;YACbhB,OAAO,EAAE,KAAK;YACdiB,OAAO,EAAE,MAAM;YACfC,SAAS,EAAE,MAAM;YACjBpB,KAAK,EAAE3E,oBAAoB,IAAI,GAAAlC,gBAAA,GAACb,MAAM,CAAC4D,OAAO,cAAA/C,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBqD,SAAS,cAAApD,qBAAA,eAAzBA,qBAAA,CAA2B6H,IAAI,CAAC,CAAC,IAAG,aAAa,GAAG,OAAO;YAC3F3C,QAAQ,EAAE,UAAU;YACpB+C,MAAM,EAAE,MAAM,CAAE;YAChB;UACD,CAAE;UACFC,8BAA8B,EAAE;UAChC;UAAA;UACAjC,uBAAuB,EAAE;YAAEC,MAAM,EAAElH;UAAY;QAAE;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EACTxE,oBAAoB,IAAI,GAAAhC,gBAAA,GAACf,MAAM,CAAC4D,OAAO,cAAA7C,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBmD,SAAS,cAAAlD,qBAAA,eAAzBA,qBAAA,CAA2B2H,IAAI,CAAC,CAAC,kBAC1DtJ,OAAA;QACCQ,KAAK,EAAE;UACNmG,QAAQ,EAAE,UAAU;UACpB0B,KAAK,EAAE,MAAM;UACbR,aAAa,EAAE,MAAM;UACrB+B,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,QAAQ;UACnBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,sBAAsB;UACjCvG,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACX8E,OAAO,EAAE;UACT;QACD,CAAE;QACFhI,EAAE,EAAC,iBAAiB;QAAAgG,QAAA,EAEnB1E,SAAS,CAACnB,WAAW;MAAC;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC,gBACL,CAAC;AAEL,CAAC;EAAA,QA5SyBpI,cAAc,EAoBnCH,cAAc;AAAA,EAyRpB,CAAC;EAAA,QA7S0BG,cAAc,EAoBnCH,cAAc;AAAA,EAyRnB;AAACqK,GAAA,GA3TI7J,UAAqC;AA6T3C,eAAA8J,GAAA,gBAAe1K,IAAI,CAACY,UAAU,CAAC;AAAC,IAAAE,EAAA,EAAA2J,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAA7J,EAAA;AAAA6J,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}