{"ast": null, "code": "import React,{useState}from\"react\";import\"../../guideDesign/Canvas.module.css\";import{Box,Typography,TextField,IconButton,Button}from\"@mui/material\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../../store/drawerStore\";import{warning}from\"../../../assets/icons/icons\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PageInteractions=_ref=>{let{setShowCanvasSettings,resetHeightofBanner}=_ref;const{t:translate}=useTranslation();const{selectedTemplate,padding,setPadding,position,setPosition,radius,setRadius,borderSize,setBorderSize,setBorderColor,borderColor,setBackgroundColor,backgroundColor,backgroundC,setBackgroundC,Bposition,setBposition,bpadding,setbPadding,Bbordercolor,setBBorderColor,BborderSize,setBBorderSize,zindeex,setZindeex,sectionColor,setSectionColor,setBannerCanvasSetting,setOverlayEnabled,setIsUnSavedChanges}=useDrawerStore(state=>state);const handlePositionChange=pos=>{setPosition(pos);};const[tempPadding,setTempPadding]=useState(bpadding);const[tempBorderSize,setTempBorderSize]=useState(BborderSize);const[tempZIndex,setTempZIndex]=useState(zindeex);const[tempBorderColor,setTempBorderColor]=useState(Bbordercolor);const[tempBackgroundColor,setTempBackgroundColor]=useState(backgroundC);const[tempSectionColor,setTempSectionColor]=useState(sectionColor);const[tempPosition,setTempPosition]=useState(Bposition||\"Cover Top\");const[isOpen,setIsOpen]=useState(true);const[isColorChangesDone,setColorChangesDone]=useState(false);const[isPaddingChangeDone,setPaddingChangeDone]=useState(false);const[paddingError,setPaddingError]=useState(false);const[borderSizeError,setBorderSizeError]=useState(false);const handleApplyChanges=()=>{if(paddingError||borderSizeError){return;}// Update position\nsetBposition(tempPosition);// Apply padding changes if they were made\nconst paddingToUse=isPaddingChangeDone?bpadding:tempPadding;// Reset height of banner with the correct padding value\nresetHeightofBanner(tempPosition,parseInt(paddingToUse||\"12\"),parseInt(BborderSize||\"2\"),true,parseInt(tempPadding||\"0\"));// Create canvas settings object with all properties\nconst canvasSettings={Position:tempPosition,BackgroundColor:isColorChangesDone?backgroundC:undefined,Padding:paddingToUse||\"12\",BorderColor:Bbordercolor,BorderSize:BborderSize||\"2\",Zindex:9999,sectionColor:sectionColor};// Remove undefined properties\nObject.keys(canvasSettings).forEach(key=>{if(canvasSettings[key]===undefined){delete canvasSettings[key];}});// Apply canvas settings\nsetBannerCanvasSetting(canvasSettings);// Close the panel and mark changes as unsaved\nhandleClose();setIsUnSavedChanges(true);};const handleClose=()=>{setIsOpen(false);setShowCanvasSettings(false);};if(!isOpen)return null;const buttonStyle=isSelected=>({border:isSelected?`1px solid var(--primarycolor)`:\"none\",borderRadius:\"20px\",backgroundColor:isSelected?\"#d3d9d9\":\"#f1ecec\",color:\"#000\",cursor:\"pointer\",//isSelected ? \"pointer\" : \"not-allowed\",\nboxShadow:isSelected?\"0 0 2px rgba(0,0,0,0.2)\":\"none\",opacity:isSelected?\"1\":\"0.5\",padding:\"5px\",lineHeight:\"15px\"});return/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Canvas\",{defaultValue:\"Canvas\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{alignItems:\"center\",backgroundColor:\"var(--back-light-color)\",borderRadius:\"var(--button-border-radius)\",height:\"auto\",margin:\"0 15px 5px\"},children:[/*#__PURE__*/_jsx(\"label\",{style:{fontWeight:\"600\"},children:translate(\"Position\",{defaultValue:\"Position\"})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",padding:\"8px\",placeContent:\"center\",gap:\"5px\"},children:[/*#__PURE__*/_jsx(\"button\",{style:buttonStyle(tempPosition!==\"Cover Top\"),onClick:()=>{setTempPosition(\"Push Down\");// When changing position, mark padding as changed to ensure it's applied\nsetPaddingChangeDone(true);},children:translate(\"Push Down\",{defaultValue:\"Push Down\"})}),/*#__PURE__*/_jsx(\"button\",{style:buttonStyle(tempPosition===\"Cover Top\"),onClick:()=>{setTempPosition(\"Cover Top\");// When changing position, mark padding as changed to ensure it's applied\nsetPaddingChangeDone(true);},children:translate(\"Cover Top\",{defaultValue:\"Cover Top\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Padding\",{defaultValue:\"Padding\"})}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:bpadding,fullWidth:true,size:\"small\",className:\"qadpt-control-input\",onChange:e=>{let value=e.target.value;if(value===''){value='0';}if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate padding between 0px and 20px\nif(inputValue<0||inputValue>20){setPaddingError(true);}else{setPaddingError(false);}// Update both the current padding and temp padding\nsetbPadding(value);setTempPadding(value);setPaddingChangeDone(true);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:paddingError})]}),paddingError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"padding_range_error\",{defaultValue:\"Value must be between 0px and 20px.\"})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Border Size\",{defaultValue:\"Border Size\"})}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:BborderSize,fullWidth:true,size:\"small\",className:\"qadpt-control-input\",onChange:e=>{let value=e.target.value;// If the input is empty, set value to '0'\nif(value===''){value='0';}// Only allow numeric input\nif(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate border size between 0px and 10px\nif(inputValue<0||inputValue>10){setBorderSizeError(true);}else{setBorderSizeError(false);}setBBorderSize(value);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:borderSizeError})]}),borderSizeError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"border_size_range_error\",{defaultValue:\"Value must be between 0px and 10px.\"})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Border\",{defaultValue:\"Border\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:Bbordercolor,onChange:e=>setBBorderColor(e.target.value),className:\"qadpt-color-input\"})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Background\",{defaultValue:\"Background\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:backgroundC,onChange:e=>{setColorChangesDone(true);setBackgroundC(e.target.value);},className:\"qadpt-color-input\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn ${paddingError||borderSizeError?\"disabled\":\"\"}`,disabled:paddingError||borderSizeError,children:translate(\"Apply\",{defaultValue:\"Apply\"})})})]})});};export default PageInteractions;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "ArrowBackIosNewOutlinedIcon", "CloseIcon", "useDrawerStore", "warning", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "PageInteractions", "_ref", "setShowCanvasSettings", "resetHeightofBanner", "t", "translate", "selectedTemplate", "padding", "setPadding", "position", "setPosition", "radius", "setRadius", "borderSize", "setBorderSize", "setBorderColor", "borderColor", "setBackgroundColor", "backgroundColor", "backgroundC", "setBackgroundC", "Bposition", "setBposition", "bpadding", "setbPadding", "Bbordercolor", "setBBorderColor", "BborderSize", "setBBorderSize", "zindeex", "setZindeex", "sectionColor", "setSectionColor", "setBannerCanvasSetting", "setOverlayEnabled", "setIsUnSavedChanges", "state", "handlePositionChange", "pos", "tempPadding", "setTempPadding", "tempBorderSize", "setTempBorderSize", "tempZIndex", "setTempZIndex", "tempBorderColor", "setTempBorderColor", "tempBackgroundColor", "setTempBackgroundColor", "tempSectionColor", "setTempSectionColor", "tempPosition", "setTempPosition", "isOpen", "setIsOpen", "isColorChangesDone", "setColorChangesDone", "isPaddingChangeDone", "setPaddingChangeDone", "paddingError", "setPaddingError", "borderSizeError", "setBorderSizeError", "handleApplyChanges", "paddingToUse", "parseInt", "canvasSettings", "Position", "BackgroundColor", "undefined", "Padding", "BorderColor", "BorderSize", "Zindex", "Object", "keys", "for<PERSON>ach", "key", "handleClose", "buttonStyle", "isSelected", "border", "borderRadius", "color", "cursor", "boxShadow", "opacity", "lineHeight", "className", "children", "onClick", "defaultValue", "size", "style", "alignItems", "height", "margin", "fontWeight", "display", "place<PERSON><PERSON>nt", "gap", "variant", "value", "fullWidth", "onChange", "e", "target", "test", "inputValue", "InputProps", "endAdornment", "sx", "error", "fontSize", "textAlign", "top", "left", "marginBottom", "marginRight", "dangerouslySetInnerHTML", "__html", "type", "disabled"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/PageInteraction.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"../../guideDesign/Canvas.module.css\";\r\nimport { Box, Typography, TextField, Grid, IconButton, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@mui/material\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { warning } from \"../../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst PageInteractions = ({ setShowCanvasSettings,resetHeightofBanner }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tselectedTemplate,\r\n\t\tpadding,\r\n\t\tsetPadding,\r\n\t\tposition,\r\n\t\tsetPosition,\r\n\t\tradius,\r\n\t\tsetRadius,\r\n\t\tborderSize,\r\n\t\tsetBorderSize,\r\n\t\tsetBorderColor,\r\n\t\tborderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tbackgroundColor,\r\n\t\tbackgroundC,\r\n\t\tsetBackgroundC,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tbpadding,\r\n\t\tsetbPadding,\r\n\t\tBbordercolor,\r\n\t\tsetBBorderColor,\r\n\t\tBborderSize,\r\n\t\tsetBBorderSize,\r\n\t\tzindeex,\r\n\t\tsetZindeex,\r\n\t\tsectionColor,\r\n\t\tsetSectionColor,\r\n\t\tsetBannerCanvasSetting,\r\n\t\tsetOverlayEnabled,\r\n\t\tsetIsUnSavedChanges\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst handlePositionChange = (pos: any) => {\r\n\t\tsetPosition(pos);\r\n\t};\r\n\tconst [tempPadding, setTempPadding] = useState(bpadding);\r\n\tconst [tempBorderSize, setTempBorderSize] = useState(BborderSize);\r\n\tconst [tempZIndex, setTempZIndex] = useState(zindeex);\r\n\tconst [tempBorderColor, setTempBorderColor] = useState(Bbordercolor);\r\n\tconst [tempBackgroundColor, setTempBackgroundColor] = useState(backgroundC);\r\n\tconst [tempSectionColor, setTempSectionColor] = useState(sectionColor);\r\n\tconst [tempPosition, setTempPosition] = useState(Bposition || \"Cover Top\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [isColorChangesDone, setColorChangesDone] = useState(false);\r\n\tconst [isPaddingChangeDone, setPaddingChangeDone] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\tif (paddingError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t// Update position\r\n\t\tsetBposition(tempPosition);\r\n\r\n\t\t// Apply padding changes if they were made\r\n\t\tconst paddingToUse = isPaddingChangeDone ? bpadding : tempPadding;\r\n\r\n\t\t// Reset height of banner with the correct padding value\r\n\t\tresetHeightofBanner(\r\n\t\t\ttempPosition,\r\n\t\t\tparseInt(paddingToUse || \"12\"),\r\n\t\t\tparseInt(BborderSize || \"2\"),\r\n\t\t\ttrue,\r\n\t\t\tparseInt(tempPadding || \"0\")\r\n\t\t);\r\n\r\n\t\t// Create canvas settings object with all properties\r\n\t\tconst canvasSettings: Record<string, any> = {\r\n\t\t\tPosition: tempPosition,\r\n\t\t\tBackgroundColor: isColorChangesDone ? backgroundC : undefined,\r\n\t\t\tPadding: paddingToUse || \"12\",\r\n\t\t\tBorderColor: Bbordercolor,\r\n\t\t\tBorderSize: BborderSize || \"2\",\r\n\t\t\tZindex: 9999,\r\n\t\t\tsectionColor: sectionColor,\r\n\t\t};\r\n\r\n\t\t// Remove undefined properties\r\n\t\tObject.keys(canvasSettings).forEach((key) => {\r\n\t\t\tif (canvasSettings[key] === undefined) {\r\n\t\t\t\tdelete canvasSettings[key];\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t// Apply canvas settings\r\n\t\tsetBannerCanvasSetting(canvasSettings);\r\n\r\n\t\t// Close the panel and mark changes as unsaved\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowCanvasSettings(false);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst buttonStyle = (isSelected: boolean) => ({\r\n\t\tborder: isSelected ? `1px solid var(--primarycolor)` : \"none\",\r\n\t\tborderRadius: \"20px\",\r\n\t\tbackgroundColor: isSelected ? \"#d3d9d9\" : \"#f1ecec\",\r\n\t\tcolor: \"#000\",\r\n\t\tcursor: \"pointer\", //isSelected ? \"pointer\" : \"not-allowed\",\r\n\t\tboxShadow: isSelected ? \"0 0 2px rgba(0,0,0,0.2)\" : \"none\",\r\n\t\topacity: isSelected ? \"1\" : \"0.5\",\r\n\t\tpadding: \"5px\",\r\n\t\tlineHeight : \"15px\",\r\n\t});\r\n\r\n\treturn (\r\n\t\t<div className=\"qadpt-designpopup\">\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Canvas\", { defaultValue: \"Canvas\" })}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tbackgroundColor: \"var(--back-light-color)\",\r\n\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\tmargin: \"0 15px 5px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<label style={{ fontWeight: \"600\" }}>{translate(\"Position\", { defaultValue: \"Position\" })}</label>\r\n\t\t\t\t\t<div style={{ display: \"flex\", padding: \"8px\", placeContent: \"center\", gap: \"5px\" }}>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tstyle={buttonStyle(tempPosition !== \"Cover Top\")}\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tsetTempPosition(\"Push Down\");\r\n\t\t\t\t\t\t\t\t// When changing position, mark padding as changed to ensure it's applied\r\n\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Push Down\", { defaultValue: \"Push Down\" })}\r\n\t\t\t\t\t\t</button>\r\n\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tstyle={buttonStyle(tempPosition === \"Cover Top\")}\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tsetTempPosition(\"Cover Top\");\r\n\t\t\t\t\t\t\t\t// When changing position, mark padding as changed to ensure it's applied\r\n\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Cover Top\", { defaultValue: \"Cover Top\" })}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Padding\", { defaultValue: \"Padding\" })}</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={bpadding}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n    \t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\r\n\t\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tvalue = '0';\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t// Update both the current padding and temp padding\r\n\t\t\t\t\t\t\t\t\tsetbPadding(value);\r\n\t\t\t\t\t\t\t\t\tsetTempPadding(value);\r\n\t\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t{translate(\"padding_range_error\", { defaultValue: \"Value must be between 0px and 20px.\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Size\", { defaultValue: \"Border Size\" })}</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={BborderSize}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\r\n\t\t\t\t\t\t\t\t// If the input is empty, set value to '0'\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\tvalue = '0';\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border size between 0px and 10px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 10) {\r\n\t\t\t\t\t\t\t\tsetBorderSizeError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tsetBorderSizeError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetBBorderSize(value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderSizeError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{borderSizeError && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t{translate(\"border_size_range_error\", { defaultValue: \"Value must be between 0px and 10px.\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Z-Index</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={tempZIndex}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => setTempZIndex(parseInt(e.target.value) || 0)}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\", { defaultValue: \"Border\" })}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={Bbordercolor}\r\n\t\t\t\t\t\t\tonChange={(e) => setBBorderColor(e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\", { defaultValue: \"Background\" })}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={backgroundC}\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetColorChangesDone(true);\r\n\t\t\t\t\t\t\t\tsetBackgroundC(e.target.value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Section Color</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempSectionColor}\r\n\t\t\t\t\t\t\tonChange={(e) => setTempSectionColor(e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${paddingError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={paddingError || borderSizeError}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\", { defaultValue: \"Apply\" })}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default PageInteractions;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,qCAAqC,CAC5C,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAWC,MAAM,KAAQ,eAAe,CAC7F,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CACvD,OAASC,OAAO,KAAQ,6BAA6B,CACrD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAAwD,IAAvD,CAAEC,qBAAqB,CAACC,mBAAyB,CAAC,CAAAF,IAAA,CAC3E,KAAM,CAAEG,CAAC,CAAEC,SAAU,CAAC,CAAGV,cAAc,CAAC,CAAC,CACzC,KAAM,CACLW,gBAAgB,CAChBC,OAAO,CACPC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,MAAM,CACNC,SAAS,CACTC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,WAAW,CACXC,kBAAkB,CAClBC,eAAe,CACfC,WAAW,CACXC,cAAc,CACdC,SAAS,CACTC,YAAY,CACZC,QAAQ,CACRC,WAAW,CACXC,YAAY,CACZC,eAAe,CACfC,WAAW,CACXC,cAAc,CACdC,OAAO,CACPC,UAAU,CACVC,YAAY,CACZC,eAAe,CACfC,sBAAsB,CACtBC,iBAAiB,CACjBC,mBACD,CAAC,CAAG1C,cAAc,CAAE2C,KAAK,EAAKA,KAAK,CAAC,CAEpC,KAAM,CAAAC,oBAAoB,CAAIC,GAAQ,EAAK,CAC1C5B,WAAW,CAAC4B,GAAG,CAAC,CACjB,CAAC,CACD,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGvD,QAAQ,CAACsC,QAAQ,CAAC,CACxD,KAAM,CAACkB,cAAc,CAAEC,iBAAiB,CAAC,CAAGzD,QAAQ,CAAC0C,WAAW,CAAC,CACjE,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAG3D,QAAQ,CAAC4C,OAAO,CAAC,CACrD,KAAM,CAACgB,eAAe,CAAEC,kBAAkB,CAAC,CAAG7D,QAAQ,CAACwC,YAAY,CAAC,CACpE,KAAM,CAACsB,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG/D,QAAQ,CAACkC,WAAW,CAAC,CAC3E,KAAM,CAAC8B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjE,QAAQ,CAAC8C,YAAY,CAAC,CACtE,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAACoC,SAAS,EAAI,WAAW,CAAC,CAC1E,KAAM,CAACgC,MAAM,CAAEC,SAAS,CAAC,CAAGrE,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACsE,kBAAkB,CAAEC,mBAAmB,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACwE,mBAAmB,CAAEC,oBAAoB,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC0E,YAAY,CAAEC,eAAe,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4E,eAAe,CAAEC,kBAAkB,CAAC,CAAG7E,QAAQ,CAAC,KAAK,CAAC,CAE7D,KAAM,CAAA8E,kBAAkB,CAAGA,CAAA,GAAM,CAChC,GAAIJ,YAAY,EAAIE,eAAe,CAAE,CACpC,OACD,CACA;AACAvC,YAAY,CAAC6B,YAAY,CAAC,CAE1B;AACA,KAAM,CAAAa,YAAY,CAAGP,mBAAmB,CAAGlC,QAAQ,CAAGgB,WAAW,CAEjE;AACApC,mBAAmB,CAClBgD,YAAY,CACZc,QAAQ,CAACD,YAAY,EAAI,IAAI,CAAC,CAC9BC,QAAQ,CAACtC,WAAW,EAAI,GAAG,CAAC,CAC5B,IAAI,CACJsC,QAAQ,CAAC1B,WAAW,EAAI,GAAG,CAC5B,CAAC,CAED;AACA,KAAM,CAAA2B,cAAmC,CAAG,CAC3CC,QAAQ,CAAEhB,YAAY,CACtBiB,eAAe,CAAEb,kBAAkB,CAAGpC,WAAW,CAAGkD,SAAS,CAC7DC,OAAO,CAAEN,YAAY,EAAI,IAAI,CAC7BO,WAAW,CAAE9C,YAAY,CACzB+C,UAAU,CAAE7C,WAAW,EAAI,GAAG,CAC9B8C,MAAM,CAAE,IAAI,CACZ1C,YAAY,CAAEA,YACf,CAAC,CAED;AACA2C,MAAM,CAACC,IAAI,CAACT,cAAc,CAAC,CAACU,OAAO,CAAEC,GAAG,EAAK,CAC5C,GAAIX,cAAc,CAACW,GAAG,CAAC,GAAKR,SAAS,CAAE,CACtC,MAAO,CAAAH,cAAc,CAACW,GAAG,CAAC,CAC3B,CACD,CAAC,CAAC,CAEF;AACA5C,sBAAsB,CAACiC,cAAc,CAAC,CAEtC;AACAY,WAAW,CAAC,CAAC,CACb3C,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAA2C,WAAW,CAAGA,CAAA,GAAM,CACzBxB,SAAS,CAAC,KAAK,CAAC,CAChBpD,qBAAqB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED,GAAI,CAACmD,MAAM,CAAE,MAAO,KAAI,CAExB,KAAM,CAAA0B,WAAW,CAAIC,UAAmB,GAAM,CAC7CC,MAAM,CAAED,UAAU,CAAG,+BAA+B,CAAG,MAAM,CAC7DE,YAAY,CAAE,MAAM,CACpBhE,eAAe,CAAE8D,UAAU,CAAG,SAAS,CAAG,SAAS,CACnDG,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,SAAS,CAAE;AACnBC,SAAS,CAAEL,UAAU,CAAG,yBAAyB,CAAG,MAAM,CAC1DM,OAAO,CAAEN,UAAU,CAAG,GAAG,CAAG,KAAK,CACjCzE,OAAO,CAAE,KAAK,CACdgF,UAAU,CAAG,MACd,CAAC,CAAC,CAEF,mBACC1F,IAAA,QAAK2F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cACjC1F,KAAA,QAAKyF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7B1F,KAAA,QAAKyF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnC5F,IAAA,CAACR,UAAU,EACV,aAAW,OAAO,CAClBqG,OAAO,CAAEZ,WAAY,CAAAW,QAAA,cAErB5F,IAAA,CAACN,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbM,IAAA,QAAK2F,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEpF,SAAS,CAAC,QAAQ,CAAE,CAAEsF,YAAY,CAAE,QAAS,CAAC,CAAC,CAAM,CAAC,cACpF9F,IAAA,CAACR,UAAU,EACVuG,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBF,OAAO,CAAEZ,WAAY,CAAAW,QAAA,cAErB5F,IAAA,CAACL,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAENO,KAAA,QACC8F,KAAK,CAAE,CACNC,UAAU,CAAE,QAAQ,CACpB5E,eAAe,CAAE,yBAAyB,CAC1CgE,YAAY,CAAE,6BAA6B,CAC3Ca,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,YACT,CAAE,CAAAP,QAAA,eAEF5F,IAAA,UAAOgG,KAAK,CAAE,CAAEI,UAAU,CAAE,KAAM,CAAE,CAAAR,QAAA,CAAEpF,SAAS,CAAC,UAAU,CAAE,CAAEsF,YAAY,CAAE,UAAW,CAAC,CAAC,CAAQ,CAAC,cAClG5F,KAAA,QAAK8F,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAE3F,OAAO,CAAE,KAAK,CAAE4F,YAAY,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAM,CAAE,CAAAX,QAAA,eACnF5F,IAAA,WACCgG,KAAK,CAAEd,WAAW,CAAC5B,YAAY,GAAK,WAAW,CAAE,CACjDuC,OAAO,CAAEA,CAAA,GAAM,CACdtC,eAAe,CAAC,WAAW,CAAC,CAC5B;AACAM,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAE,CAAA+B,QAAA,CAEDpF,SAAS,CAAC,WAAW,CAAE,CAAEsF,YAAY,CAAE,WAAY,CAAC,CAAC,CAC/C,CAAC,cAET9F,IAAA,WACCgG,KAAK,CAAEd,WAAW,CAAC5B,YAAY,GAAK,WAAW,CAAE,CACjDuC,OAAO,CAAEA,CAAA,GAAM,CACdtC,eAAe,CAAC,WAAW,CAAC,CAC5B;AACAM,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAE,CAAA+B,QAAA,CAEDpF,SAAS,CAAC,WAAW,CAAE,CAAEsF,YAAY,CAAE,WAAY,CAAC,CAAC,CAC/C,CAAC,EACL,CAAC,EACF,CAAC,cAEN5F,KAAA,QAAKyF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC9B1F,KAAA,CAACb,GAAG,EAACsG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC5F,IAAA,CAACV,UAAU,EAACqG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpF,SAAS,CAAC,SAAS,CAAE,CAAEsF,YAAY,CAAE,SAAU,CAAC,CAAC,CAAa,CAAC,cAC5G9F,IAAA,CAACT,SAAS,EACTiH,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAE/E,QAAS,CAChBgF,SAAS,MACTX,IAAI,CAAC,OAAO,CACZJ,SAAS,CAAC,qBAAqB,CAC/BgB,QAAQ,CAAGC,CAAC,EAAK,CACZ,GAAI,CAAAH,KAAK,CAAGG,CAAC,CAACC,MAAM,CAACJ,KAAK,CAE7B,GAAIA,KAAK,GAAK,EAAE,CAAE,CAClBA,KAAK,CAAG,GAAG,CACX,CAGA,GAAI,CAAC,SAAS,CAACK,IAAI,CAACL,KAAK,CAAC,CAAE,CAC5B,OACA,CAEA,KAAM,CAAAM,UAAU,CAAG3C,QAAQ,CAACqC,KAAK,CAAC,EAAI,CAAC,CAEvC;AACA,GAAIM,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACvChD,eAAe,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACPA,eAAe,CAAC,KAAK,CAAC,CACtB,CAEA;AACApC,WAAW,CAAC8E,KAAK,CAAC,CAClB9D,cAAc,CAAC8D,KAAK,CAAC,CACrB5C,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAE,CACFmD,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CACJ,0CAA0C,CAAE,CAAE9B,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAC/B,CACD,CAAE,CACF+B,KAAK,CAAErD,YAAa,CACnB,CAAC,EACA,CAAC,CACLA,YAAY,eACZ5D,KAAA,CAACZ,UAAU,EACV0G,KAAK,CAAE,CACNoB,QAAQ,CAAE,MAAM,CAChB9B,KAAK,CAAE,SAAS,CAChB+B,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBnB,OAAO,CAAE,MACV,CAAE,CAAAT,QAAA,eACA5F,IAAA,SAAMgG,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEe,QAAQ,CAAE,MAAM,CAAEnB,UAAU,CAAE,QAAQ,CAAEwB,WAAW,CAAC,KAAM,CAAE,CAE9FC,uBAAuB,CAAE,CAAEC,MAAM,CAAE9H,OAAQ,CAAE,CAC7C,CAAC,CACAW,SAAS,CAAC,qBAAqB,CAAE,CAAEsF,YAAY,CAAE,qCAAsC,CAAC,CAAC,EAC/E,CACZ,cACD5F,KAAA,CAACb,GAAG,EAACsG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC5F,IAAA,CAACV,UAAU,EAACqG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpF,SAAS,CAAC,aAAa,CAAE,CAAEsF,YAAY,CAAE,aAAc,CAAC,CAAC,CAAa,CAAC,cACpH9F,IAAA,CAACT,SAAS,EACTiH,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAE3E,WAAY,CACnB4E,SAAS,MACTX,IAAI,CAAC,OAAO,CACZJ,SAAS,CAAC,qBAAqB,CAC/BgB,QAAQ,CAAGC,CAAC,EAAK,CAChB,GAAI,CAAAH,KAAK,CAAGG,CAAC,CAACC,MAAM,CAACJ,KAAK,CAE1B;AACA,GAAIA,KAAK,GAAK,EAAE,CAAE,CAClBA,KAAK,CAAG,GAAG,CACX,CAEA;AACA,GAAI,CAAC,SAAS,CAACK,IAAI,CAACL,KAAK,CAAC,CAAE,CAC5B,OACA,CAEA,KAAM,CAAAM,UAAU,CAAG3C,QAAQ,CAACqC,KAAK,CAAC,EAAI,CAAC,CAEvC;AACA,GAAIM,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACvC9C,kBAAkB,CAAC,IAAI,CAAC,CACxB,CAAC,IAAM,CACPA,kBAAkB,CAAC,KAAK,CAAC,CACzB,CAEAlC,cAAc,CAAC0E,KAAK,CAAC,CACtB,CAAE,CACFO,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CACJ,0CAA0C,CAAE,CAAE9B,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAC/B,CACD,CAAE,CACF+B,KAAK,CAAEnD,eAAgB,CACtB,CAAC,EACQ,CAAC,CACLA,eAAe,eACf9D,KAAA,CAACZ,UAAU,EACX0G,KAAK,CAAE,CACNoB,QAAQ,CAAE,MAAM,CAChB9B,KAAK,CAAE,SAAS,CAChB+B,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBnB,OAAO,CAAE,MACV,CAAE,CAAAT,QAAA,eACA5F,IAAA,SAAMgG,KAAK,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEe,QAAQ,CAAE,MAAM,CAAEnB,UAAU,CAAE,QAAQ,CAAEwB,WAAW,CAAC,KAAM,CAAE,CAE9FC,uBAAuB,CAAE,CAAEC,MAAM,CAAE9H,OAAQ,CAAE,CAC7C,CAAC,CACNW,SAAS,CAAC,yBAAyB,CAAE,CAAEsF,YAAY,CAAE,qCAAsC,CAAC,CAAC,EAC5E,CACZ,cAaR5F,KAAA,CAACb,GAAG,EAACsG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC5F,IAAA,CAACV,UAAU,EAACqG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpF,SAAS,CAAC,QAAQ,CAAE,CAAEsF,YAAY,CAAE,QAAS,CAAC,CAAC,CAAa,CAAC,cAC1G9F,IAAA,UACC4H,IAAI,CAAC,OAAO,CACZnB,KAAK,CAAE7E,YAAa,CACpB+E,QAAQ,CAAGC,CAAC,EAAK/E,eAAe,CAAC+E,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE,CACjDd,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,cACNzF,KAAA,CAACb,GAAG,EAACsG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC5F,IAAA,CAACV,UAAU,EAACqG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpF,SAAS,CAAC,YAAY,CAAE,CAAEsF,YAAY,CAAE,YAAa,CAAC,CAAC,CAAa,CAAC,cAClH9F,IAAA,UACC4H,IAAI,CAAC,OAAO,CACZnB,KAAK,CAAEnF,WAAY,CACnBqF,QAAQ,CAAGC,CAAC,EAAK,CAChBjD,mBAAmB,CAAC,IAAI,CAAC,CACzBpC,cAAc,CAACqF,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC,CAC/B,CAAE,CACFd,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,EAUF,CAAC,cACN3F,IAAA,QAAK2F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjC5F,IAAA,CAACP,MAAM,EACN+G,OAAO,CAAC,WAAW,CACnBX,OAAO,CAAE3B,kBAAmB,CAC5ByB,SAAS,CAAE,aAAa7B,YAAY,EAAIE,eAAe,CAAG,UAAU,CAAG,EAAE,EAAG,CAC5E6D,QAAQ,CAAE/D,YAAY,EAAIE,eAAgB,CAAA4B,QAAA,CAE1CpF,SAAS,CAAC,OAAO,CAAE,CAAEsF,YAAY,CAAE,OAAQ,CAAC,CAAC,CACtC,CAAC,CACN,CAAC,EACF,CAAC,CACF,CAAC,CAER,CAAC,CAED,cAAe,CAAA3F,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}