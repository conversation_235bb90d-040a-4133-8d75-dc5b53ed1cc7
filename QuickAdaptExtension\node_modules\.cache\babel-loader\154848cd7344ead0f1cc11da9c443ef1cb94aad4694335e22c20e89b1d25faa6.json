{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\AI\\\\StopScrapingButton.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport { stopScraping } from '../../services/ScrapingService';\nimport './EnableAIButton.css';\nimport { AccountContext } from '../../components/login/AccountContext';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StopScrapingButton = ({\n  onClick\n}) => {\n  _s();\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const {\n    t: translate\n  } = useTranslation();\n  const handleClick = async () => {\n    stopScraping(accountId);\n    onClick();\n  };\n  ;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stop-scraping-button-container\",\n    id: \"stop-scraping-button\",\n    children: /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"enable-ai-button stop-scraping-button\",\n      onClick: handleClick,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"enable-ai-text\",\n        children: translate(\"Stop Training\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(StopScrapingButton, \"BxeC5S43QUO6g+/wwvfCAY2C7ZI=\", false, function () {\n  return [useTranslation];\n});\n_c = StopScrapingButton;\nexport default StopScrapingButton;\nvar _c;\n$RefreshReg$(_c, \"StopScrapingButton\");", "map": {"version": 3, "names": ["React", "useContext", "stopScraping", "AccountContext", "useTranslation", "jsxDEV", "_jsxDEV", "StopScrapingButton", "onClick", "_s", "accountId", "t", "translate", "handleClick", "className", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AI/StopScrapingButton.tsx"], "sourcesContent": ["import React, { useContext } from 'react';\r\nimport { stopScraping } from '../../services/ScrapingService';\r\nimport './EnableAIButton.css';\r\nimport { AccountContext } from '../../components/login/AccountContext';\r\nimport { useTranslation } from 'react-i18next';\r\ninterface StopScrapingButtonProps {\r\n  onClick: () => void;\r\n}\r\n\r\nconst StopScrapingButton: React.FC<StopScrapingButtonProps> = ({ onClick }) => {\r\n  const { accountId } = useContext(AccountContext);\r\n  const { t: translate } = useTranslation()\r\n  const handleClick = async () => {\r\n    \r\n    stopScraping(accountId);\r\n    onClick();\r\n  };\r\n  \r\n  ;\r\n  \r\n  \r\n\r\n  return (\r\n    <div className='stop-scraping-button-container' id='stop-scraping-button'>\r\n      <button className=\"enable-ai-button stop-scraping-button\" onClick={handleClick}>\r\n        <span className=\"enable-ai-text\">{translate(\"Stop Training\")}</span>\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StopScrapingButton;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,OAAO,sBAAsB;AAC7B,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAK/C,MAAMC,kBAAqD,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM;IAAEC;EAAU,CAAC,GAAGT,UAAU,CAACE,cAAc,CAAC;EAChD,MAAM;IAAEQ,CAAC,EAAEC;EAAU,CAAC,GAAGR,cAAc,CAAC,CAAC;EACzC,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;IAE9BX,YAAY,CAACQ,SAAS,CAAC;IACvBF,OAAO,CAAC,CAAC;EACX,CAAC;EAED;EAIA,oBACEF,OAAA;IAAKQ,SAAS,EAAC,gCAAgC;IAACC,EAAE,EAAC,sBAAsB;IAAAC,QAAA,eACvEV,OAAA;MAAQQ,SAAS,EAAC,uCAAuC;MAACN,OAAO,EAAEK,WAAY;MAAAG,QAAA,eAC7EV,OAAA;QAAMQ,SAAS,EAAC,gBAAgB;QAAAE,QAAA,EAAEJ,SAAS,CAAC,eAAe;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACX,EAAA,CApBIF,kBAAqD;EAAA,QAEhCH,cAAc;AAAA;AAAAiB,EAAA,GAFnCd,kBAAqD;AAsB3D,eAAeA,kBAAkB;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}