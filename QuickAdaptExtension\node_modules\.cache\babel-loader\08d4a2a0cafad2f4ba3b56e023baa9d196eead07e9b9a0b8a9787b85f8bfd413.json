{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{Box,Typography,IconButton,Grid,Button}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import useDrawerStore from\"../../store/drawerStore\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Elementssettings=_ref=>{let{setShowElementsSettings,setDesignPopup,setMenuPopup,resetHeightofBanner}=_ref;const{t:translate}=useTranslation();const{setDismissData,dismissData,selectedOption,setSelectedOption,selectedTemplate,setTooltipElementOptions,toolTipGuideMetaData,currentStep,updateprogressclick,progress,setProgress,dismiss,setDismiss,selectedTemplateTour,setIsUnSavedChanges,ProgressColor,setProgressColor,updateDesignelementInTooltip,Bposition}=useDrawerStore(state=>state);const[tempDismiss,setTempDismiss]=useState(dismiss);// Temporary state\nconst[colorChange,setColorChange]=useState(ProgressColor);const[isOpen,setIsOpen]=useState(true);const[displayType,setDisplayType]=useState(\"Cross Icon\");const[dontShowAgain,setDontShowAgain]=useState(true);const[colors,setColors]=useState(\"#ff0000\");// Initialize local state with values from global state\nconst[elementSettings,setElementsSettings]=useState({isProgress:progress,progressSelectedOption:Number(selectedOption)||1// Default to 1 if not set\n});// Add useEffect to synchronize local state with global state\nuseEffect(()=>{// Update local state when global state changes\nsetElementsSettings({isProgress:progress,progressSelectedOption:Number(selectedOption)||1});setTempDismiss(dismiss);setColorChange(ProgressColor);},[progress,selectedOption,dismiss,ProgressColor]);// Update handlers for each dismiss option\nconst handleOptionSelect=option=>{// Update only the local state\nconst numOption=Number(option);setElementsSettings(eleSettings=>{return{...eleSettings,progressSelectedOption:numOption};});// Don't update global state until Apply is clicked\n};const handleDisplayTypeChange=value=>{setDisplayType(value);};const handleBorderColorChange=e=>{const color=e.target.value;setColors(color);};const handleDontShowAgainChange=(event,checked)=>{setDontShowAgain(checked);};const handleApplyChanges=()=>{// Store the previous progress state for comparison\nconst previousProgressState=progress;const hasProgressChanged=previousProgressState!==elementSettings.isProgress;// Create a complete element settings object\nconst updatedElement={progress:elementSettings.isProgress,progressSelectedOption:elementSettings.progressSelectedOption,dismiss:tempDismiss,progressColor:colorChange};// Create a batch update function to record a single history entry\nconst batchUpdate=useDrawerStore.getState().batchUpdate;const drawerStore=useDrawerStore.getState();// First update the progress state in the store\n// This ensures resetHeightofBanner will use the correct value\nif(hasProgressChanged){// console.log(\"Setting progress state in store before batch update:\", elementSettings.isProgress);\ndrawerStore.setProgress(elementSettings.isProgress);}// Always update dismissData when Apply is clicked, regardless of whether it changed\n// This ensures the dismiss icon state is updated only when Apply is clicked\nsetDismissData({...(dismissData||{Actions:\"\",DisplayType:\"Cross Icon\",Color:\"#000000\",DontShowAgain:true}),dismisssel:tempDismiss});// Use the batch update function to record a single history entry\nbatchUpdate(()=>{// Apply all changes at once\nupdateDesignelementInTooltip(updatedElement);// Also update the individual state values to ensure everything is in sync\ndrawerStore.setProgress(elementSettings.isProgress);drawerStore.setSelectedOption(elementSettings.progressSelectedOption);drawerStore.setDismiss(tempDismiss);drawerStore.setProgressColor(colorChange);// For AI-created tours, immediately apply global progress bar synchronization\n// This ensures all steps are updated when Apply is clicked\nif(drawerStore.createWithAI&&drawerStore.selectedTemplate===\"Tour\"){var _drawerStore$interact,_drawerStore$interact2;console.log(\"🔄 handleApplyChanges: Applying global progress bar synchronization for AI tour\",{isProgress:elementSettings.isProgress,progressSelectedOption:elementSettings.progressSelectedOption,dismiss:tempDismiss,progressColor:colorChange,currentStep:drawerStore.currentStep,totalSteps:((_drawerStore$interact=drawerStore.interactionData)===null||_drawerStore$interact===void 0?void 0:(_drawerStore$interact2=_drawerStore$interact.GuideStep)===null||_drawerStore$interact2===void 0?void 0:_drawerStore$interact2.length)||0});// Apply the changes to all steps immediately\ndrawerStore.syncGlobalProgressBarStateForAITour();}},'ELEMENT_BATCH_UPDATE',`Updated element settings`);// When progress setting changes are applied, update the banner height if needed\nif(selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"&&Bposition===\"Push Down\"&&resetHeightofBanner){// Always update the height when Apply is clicked, regardless of whether progress changed\n// This ensures consistent behavior and fixes any potential state inconsistencies\n// console.log(\"Applying banner height changes. Progress state:\", elementSettings.isProgress);\n// Get the current padding and border values from the store\nconst currentPadding=drawerStore.bpadding||\"12\";const currentBorder=drawerStore.BborderSize||\"2\";// Force a complete recalculation of the banner height\n// The isFromApply=true parameter ensures all dimensions are included in the calculation\nresetHeightofBanner(\"Push Down\",parseInt(currentPadding),parseInt(currentBorder),true,// isFromApply\n0,// oldPadding\n55// top\n);}// Update the UI state\nsetIsOpen(false);setShowElementsSettings(false);setDesignPopup(true);setMenuPopup(true);setIsUnSavedChanges(true);};useEffect(()=>{if(dismissData!==null&&dismissData!==void 0&&dismissData.dismisssel){setDismiss(true);setColors(dismissData.Color);}},[dismissData===null||dismissData===void 0?void 0:dismissData.dismisssel]);// Early return if popup is closed\nif(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>{setIsOpen(false);setShowElementsSettings(false);setMenuPopup(true);},children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Elements\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:()=>{setIsOpen(false);setShowElementsSettings(false);setDesignPopup(true);setMenuPopup(true);},children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\"//style={{ opacity: \"0.5\", height: \"60px\" }}\n,children:[selectedTemplate!==\"Hotspot\"&&selectedTemplate!==\"Banner\"&&/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\"//sx={{ opacity: \"0.5\" }}\n,children:translate(\"Progress\")}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:elementSettings.isProgress,onChange:e=>{const newValue=e.target.checked;// Update only local state\nsetElementsSettings(eleSettings=>{return{...eleSettings,isProgress:newValue};});// Don't update global state until Apply is clicked\n},className:\"qadpt-progress-switch\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),elementSettings.isProgress&&selectedTemplate!==\"Hotspot\"&&selectedTemplate!==\"Banner\"&&/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:\"#EAE2E2\",borderRadius:\"8px\",padding:\"10px\",marginBottom:\"5px\"},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",padding:\"5px 10px\",borderRadius:\"6px\",cursor:\"pointer\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Progress Color\")}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:colorChange,onChange:e=>{const newColor=e.target.value;// Update only local state\nsetColorChange(newColor);// Don't update global state until Apply is clicked\n},className:\"qadpt-color-input\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{onClick:()=>handleOptionSelect(1),sx:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",padding:\"5px 10px\",backgroundColor:Number(elementSettings.progressSelectedOption)===1?\"#5f9ea01a\":\"#e5dada\",borderRadius:\"6px\",cursor:\"pointer\",border:Number(elementSettings.progressSelectedOption)===1?\"1px solid var(--primarycolor)\":\"transparent\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{color:\"var(--primarycolor)\"},children:translate(\"Dots\")}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",gap:\"4px\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:\"8px\",height:\"8px\",backgroundColor:\"var(--primarycolor)\",borderRadius:\"50%\"}}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"8px\",height:\"8px\",backgroundColor:\"var(--primarycolor)\",borderRadius:\"50%\"}}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"8px\",height:\"8px\",backgroundColor:\"var(--primarycolor)\",borderRadius:\"50%\"}})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sx:{paddingTop:\"6px !important\"},children:/*#__PURE__*/_jsxs(Box,{onClick:()=>handleOptionSelect(2),sx:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",padding:\"5px 10px\",backgroundColor:Number(elementSettings.progressSelectedOption)===2?\"#5f9ea01a\":\"#e5dada\",borderRadius:\"6px\",cursor:\"pointer\",border:Number(elementSettings.progressSelectedOption)===2?\"1px solid var(--primarycolor)\":\"transparent\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{color:\"var(--primarycolor)\"},children:translate(\"Linear Progress\")}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"40px\",height:\"4px\",backgroundColor:\"#5f96a0\"}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sx:{paddingTop:\"6px !important\"},children:/*#__PURE__*/_jsxs(Box,{onClick:()=>handleOptionSelect(3),sx:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",padding:\"5px 10px\",backgroundColor:Number(elementSettings.progressSelectedOption)===3?\"#5f9ea01a\":\"#e5dada\",borderRadius:\"6px\",cursor:\"pointer\",border:Number(elementSettings.progressSelectedOption)===3?\"1px solid var(--primarycolor)\":\"transparent\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{color:\"var(--primarycolor)\"},children:translate(\"Bread Crumbs\")}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",gap:\"4px\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:\"var(--primarycolor)\",borderRadius:\"100px\"}}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:\"var(--primarycolor)\",borderRadius:\"100px\"}}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:\"var(--primarycolor)\",borderRadius:\"100px\"}})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sx:{paddingTop:\"6px !important\"},children:/*#__PURE__*/_jsxs(Box,{onClick:()=>handleOptionSelect(4),sx:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",padding:\"5px 10px\",backgroundColor:Number(elementSettings.progressSelectedOption)===4?\"#5f9ea01a\":\"#e5dada\",borderRadius:\"6px\",cursor:\"pointer\",border:Number(elementSettings.progressSelectedOption)===4?\"1px solid var(--primarycolor)\":\"transparent\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{color:\"var(--primarycolor)\"},children:translate(\"Numbers\")}),/*#__PURE__*/_jsx(Typography,{sx:{color:\"var(--primarycolor)\"},children:translate(\"1 of 4\")})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sx:{paddingTop:\"6px !important\"},children:/*#__PURE__*/_jsx(Box//onClick={() => handleOptionSelect(4)}\n,{sx:{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",padding:\"5px 10px\",backgroundColor:Number(elementSettings.progressSelectedOption)===5?\"#5f9ea01a\":\"#e5dada\",borderRadius:\"6px\",cursor:\"pointer\",border:Number(elementSettings.progressSelectedOption)===5?\"1px solid var(--primarycolor)\":\"transparent\"},children:/*#__PURE__*/_jsx(Typography,{sx:{color:\"var(--primarycolor)\"},children:translate(\"CheckList\")})})})]})}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Dismiss\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:tempDismiss,onChange:e=>setTempDismiss(e.target.checked),name:\"tempDismiss\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),dismiss&&/*#__PURE__*/_jsx(\"div\",{})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn`,children:translate(\"Apply\")})})]})});};export default Elementssettings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "IconButton", "Grid", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Elementssettings", "_ref", "setShowElementsSettings", "setDesignPopup", "setMenuPopup", "resetHeightofBanner", "t", "translate", "setDismissData", "dismissData", "selectedOption", "setSelectedOption", "selectedTemplate", "setTooltipElementOptions", "toolTipGuideMetaData", "currentStep", "updateprogressclick", "progress", "setProgress", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "selectedTemplateTour", "setIsUnSavedChanges", "ProgressColor", "setProgressColor", "updateDesignelementInTooltip", "Bposition", "state", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "colorChange", "setColorChange", "isOpen", "setIsOpen", "displayType", "setDisplayType", "dontShowAgain", "setDontShowAgain", "colors", "setColors", "elementSettings", "setElementsSettings", "isProgress", "progressSelectedOption", "Number", "handleOptionSelect", "option", "numOption", "eleSettings", "handleDisplayTypeChange", "value", "handleBorderColorChange", "e", "color", "target", "handleDontShowAgainChange", "event", "checked", "handleApplyChanges", "previousProgressState", "hasProgressChanged", "updatedElement", "progressColor", "batchUpdate", "getState", "drawerStore", "Actions", "DisplayType", "Color", "DontShowAgain", "dismisssel", "createWithAI", "_drawerStore$interact", "_drawerStore$interact2", "console", "log", "totalSteps", "interactionData", "GuideStep", "length", "syncGlobalProgressBarStateForAITour", "currentPadding", "bpadding", "currentBorder", "BborderSize", "parseInt", "id", "className", "children", "onClick", "size", "type", "onChange", "newValue", "sx", "backgroundColor", "borderRadius", "padding", "marginBottom", "container", "spacing", "item", "xs", "display", "justifyContent", "alignItems", "cursor", "newColor", "border", "style", "gap", "width", "height", "paddingTop", "name", "variant"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/ElementsSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n\tBox,\r\n\tTypography,\r\n\tIconButton,\r\n\tSwitch,\r\n\tGrid,\r\n\tButton,\r\n\tSelect,\r\n\tMenuItem,\r\n\tFormControl,\r\n\tTooltip,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface ElementsSettingsProps {\r\n\tonDismissDataChange: (data: {\r\n\t\tActions: string | number | null;\r\n\t\tDisplayType: string;\r\n\t\tColor: string;\r\n\t\tDontShowAgain: boolean;\r\n\t\tdismisssel: boolean;\r\n\t}) => void;\r\n}\r\nconst Elementssettings = ({ setShowElementsSettings, setDesignPopup, setMenuPopup, resetHeightofBanner }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\tconst {\r\n\t\tsetDismissData,\r\n\t\tdismissData,\r\n\t\tselectedOption,\r\n\t\tsetSelectedOption,\r\n\t\tselectedTemplate,\r\n\t\tsetTooltipElementOptions,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tupdateprogressclick,\r\n\t\tprogress,\r\n\t\tsetProgress,\r\n\t\tdismiss,\r\n\t\tsetDismiss,\r\n\t\tselectedTemplateTour,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\tBposition\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [tempDismiss, setTempDismiss] = useState(dismiss); // Temporary state\r\n\tconst [colorChange, setColorChange] = useState(ProgressColor);\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [displayType, setDisplayType] = useState(\"Cross Icon\");\r\n\tconst [dontShowAgain, setDontShowAgain] = useState(true);\r\n\tconst [colors, setColors] = useState(\"#ff0000\");\r\n\t// Initialize local state with values from global state\r\n\tconst [elementSettings, setElementsSettings] = useState({\r\n\t\tisProgress: progress,\r\n\t\tprogressSelectedOption: Number(selectedOption) || 1, // Default to 1 if not set\r\n\t});\r\n\r\n\t// Add useEffect to synchronize local state with global state\r\n\tuseEffect(() => {\r\n\t\t// Update local state when global state changes\r\n\t\tsetElementsSettings({\r\n\t\t\tisProgress: progress,\r\n\t\t\tprogressSelectedOption: Number(selectedOption) || 1,\r\n\t\t});\r\n\t\tsetTempDismiss(dismiss);\r\n\t\tsetColorChange(ProgressColor);\r\n\t}, [progress, selectedOption, dismiss, ProgressColor]);\r\n\r\n\t// Update handlers for each dismiss option\r\n\tconst handleOptionSelect = (option: string | number) => {\r\n\t\t// Update only the local state\r\n\t\tconst numOption = Number(option);\r\n\t\tsetElementsSettings(eleSettings => {\r\n\t\t\treturn { ...eleSettings, progressSelectedOption: numOption }\r\n\t\t});\r\n\r\n\t\t// Don't update global state until Apply is clicked\r\n\t};\r\n\r\n\tconst handleDisplayTypeChange = (value: string) => {\r\n\t\tsetDisplayType(value);\r\n\t};\r\n\r\n\tconst handleBorderColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst color = e.target.value;\r\n\t\tsetColors(color);\r\n\t};\r\n\r\n\tconst handleDontShowAgainChange = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {\r\n\t\tsetDontShowAgain(checked);\r\n\t};\r\n\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Store the previous progress state for comparison\r\n\t\tconst previousProgressState = progress;\r\n\t\tconst hasProgressChanged = previousProgressState !== elementSettings.isProgress;\r\n\t\t// Create a complete element settings object\r\n\t\tconst updatedElement = {\r\n\t\t\tprogress: elementSettings.isProgress,\r\n        progressSelectedOption: elementSettings.progressSelectedOption,\r\n        dismiss: tempDismiss,\r\n        progressColor: colorChange\r\n\t\t};\r\n\r\n\t\t// Create a batch update function to record a single history entry\r\n\t\tconst batchUpdate = useDrawerStore.getState().batchUpdate;\r\n\t\tconst drawerStore = useDrawerStore.getState();\r\n\r\n\t\t// First update the progress state in the store\r\n\t\t// This ensures resetHeightofBanner will use the correct value\r\n\t\tif (hasProgressChanged) {\r\n\t\t\t// console.log(\"Setting progress state in store before batch update:\", elementSettings.isProgress);\r\n\t\t\tdrawerStore.setProgress(elementSettings.isProgress);\r\n\t\t}\r\n\r\n\t\t// Always update dismissData when Apply is clicked, regardless of whether it changed\r\n\t\t// This ensures the dismiss icon state is updated only when Apply is clicked\r\n\t\tsetDismissData({\r\n\t\t\t...(dismissData || { Actions: \"\", DisplayType: \"Cross Icon\", Color: \"#000000\", DontShowAgain: true }),\r\n\t\t\tdismisssel: tempDismiss\r\n\t\t});\r\n\r\n\t\t// Use the batch update function to record a single history entry\r\n\t\tbatchUpdate(\r\n\t\t\t() => {\r\n\t\t\t\t// Apply all changes at once\r\n\t\t\t\tupdateDesignelementInTooltip(updatedElement);\r\n\r\n\t\t\t\t// Also update the individual state values to ensure everything is in sync\r\n\t\t\t\tdrawerStore.setProgress(elementSettings.isProgress);\r\n\t\t\t\tdrawerStore.setSelectedOption(elementSettings.progressSelectedOption);\r\n\t\t\t\tdrawerStore.setDismiss(tempDismiss);\r\n\t\t\t\tdrawerStore.setProgressColor(colorChange);\r\n\r\n\t\t\t\t// For AI-created tours, immediately apply global progress bar synchronization\r\n\t\t\t\t// This ensures all steps are updated when Apply is clicked\r\n\t\t\t\tif (drawerStore.createWithAI && drawerStore.selectedTemplate === \"Tour\") {\r\n\t\t\t\t\tconsole.log(\"🔄 handleApplyChanges: Applying global progress bar synchronization for AI tour\", {\r\n\t\t\t\t\t\tisProgress: elementSettings.isProgress,\r\n\t\t\t\t\t\tprogressSelectedOption: elementSettings.progressSelectedOption,\r\n\t\t\t\t\t\tdismiss: tempDismiss,\r\n\t\t\t\t\t\tprogressColor: colorChange,\r\n\t\t\t\t\t\tcurrentStep: drawerStore.currentStep,\r\n\t\t\t\t\t\ttotalSteps: drawerStore.interactionData?.GuideStep?.length || 0\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Apply the changes to all steps immediately\r\n\t\t\t\t\tdrawerStore.syncGlobalProgressBarStateForAITour();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'ELEMENT_BATCH_UPDATE',\r\n\t\t\t`Updated element settings`\r\n\t\t);\r\n\t\t// When progress setting changes are applied, update the banner height if needed\r\n\t\tif (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" && Bposition === \"Push Down\" && resetHeightofBanner) {\r\n\t\t\t// Always update the height when Apply is clicked, regardless of whether progress changed\r\n\t\t\t// This ensures consistent behavior and fixes any potential state inconsistencies\r\n\t\t\t// console.log(\"Applying banner height changes. Progress state:\", elementSettings.isProgress);\r\n\r\n\t\t\t// Get the current padding and border values from the store\r\n\t\t\tconst currentPadding = drawerStore.bpadding || \"12\";\r\n\t\t\tconst currentBorder = drawerStore.BborderSize || \"2\";\r\n\r\n\t\t\t// Force a complete recalculation of the banner height\r\n\t\t\t// The isFromApply=true parameter ensures all dimensions are included in the calculation\r\n\t\t\tresetHeightofBanner(\r\n\t\t\t\t\"Push Down\",\r\n\t\t\t\tparseInt(currentPadding),\r\n\t\t\t\tparseInt(currentBorder),\r\n\t\t\t\ttrue, // isFromApply\r\n\t\t\t\t0,    // oldPadding\r\n\t\t\t\t55    // top\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t// Update the UI state\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowElementsSettings(false);\r\n\t\tsetDesignPopup(true);\r\n\t\tsetMenuPopup(true);\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (dismissData?.dismisssel) {\r\n\t\t\tsetDismiss(true);\r\n\t\t\tsetColors(dismissData.Color);\r\n\t\t}\r\n\t}, [dismissData?.dismisssel]);\r\n\r\n\r\n\t// Early return if popup is closed\r\n\tif (!isOpen) return null;\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\tsetIsOpen(false);\r\n\t\t\t\t\t\t\tsetShowElementsSettings(false);\r\n\t\t\t\t\t\t\tsetMenuPopup(true);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Elements\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\tsetIsOpen(false);\r\n\t\t\t\t\t\t\tsetShowElementsSettings(false);\r\n\t\t\t\t\t\t\tsetDesignPopup(true);\r\n\t\t\t\t\t\t\tsetMenuPopup(true);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\tclassName=\"qadpt-controls\"\r\n\t\t\t\t\t//style={{ opacity: \"0.5\", height: \"60px\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t{(selectedTemplate!== \"Hotspot\" && selectedTemplate!== \"Banner\" ) && (\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t//sx={{ opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Progress\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={elementSettings.isProgress}\r\n        onChange={(e) => {\r\n\t\t\tconst newValue = e.target.checked;\r\n\t\t\t// Update only local state\r\n\t\t\tsetElementsSettings(eleSettings => {\r\n\t\t\t\treturn { ...eleSettings, isProgress: newValue }\r\n\t\t\t});\r\n\r\n\t\t\t// Don't update global state until Apply is clicked\r\n        }}\r\n        className=\"qadpt-progress-switch\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t{elementSettings.isProgress && (selectedTemplate!== \"Hotspot\" && selectedTemplate!== \"Banner\" ) && (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tpadding: \"10px\",\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\tcontainer\r\n\t\t\t\t\t\t\tspacing={2}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box sx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\"\t\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Progress Color\")}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={colorChange}\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst newColor = e.target.value;\r\n\t\t\t\t\t\t\t\t// Update only local state\r\n\t\t\t\t\t\t\t\tsetColorChange(newColor);\r\n\t\t\t\t\t\t\t\t// Don't update global state until Apply is clicked\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(1)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 1 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 1 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Dots\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", gap: \"4px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"8px\", height: \"8px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"50%\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"8px\", height: \"8px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"50%\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"8px\", height: \"8px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"50%\" }}></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(2)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 2 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 2 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Linear Progress\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<div style={{ width: \"40px\", height: \"4px\", backgroundColor: \"#5f96a0\" }}></div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(3)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 3 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 3 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Bread Crumbs\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", gap: \"4px\" }}>\r\n\t\t\t\t\t\t\t\t\t<div style={{width: \"14px\", height: \"4px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"100px\" }}></div>\r\n\t\t\t\t\t\t\t\t\t<div style={{width: \"14px\", height: \"4px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"100px\" }}></div>\r\n\t\t\t\t\t\t\t\t\t<div style={{width: \"14px\", height: \"4px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"100px\" }}></div>\r\n\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(4)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 4 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 4 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Numbers\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"1 of 4\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t//onClick={() => handleOptionSelect(4)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 5 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 5 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"CheckList\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t)}\r\n\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Dismiss\")}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={tempDismiss}\r\n        onChange={(e) => setTempDismiss(e.target.checked)}\r\n        name=\"tempDismiss\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t{dismiss && (\r\n\t\t\t\t\t<div\r\n\r\n\t\t\t\t>\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ marginBottom: \"8px\" }}>Actions</Typography> */}\r\n\t\t\t\t\t\t{/* <Box sx={{ display: \"flex\", justifyContent: \"space-between\", marginBottom: \"16px\" }}>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedOption === \"dismiss\" ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(\"dismiss\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tDismiss\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedOption === \"snooze\" ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(\"snooze\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tSnooze\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ marginBottom: \"8px\", color: \"rgba(0, 0, 0, 0.38)\" }}>Display Type</Typography> */}\r\n\t\t\t\t\t\t{/* <Tooltip\r\n\t\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t{\" \"}\r\n\r\n\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"16px\" }}\r\n\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tvalue={displayType}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleDisplayTypeChange(e.target.value as string)}\r\n\t\t\t\t\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\t\t\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\t\t\t\t\t\t\tdisablePortal: true,\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#F6EEEE\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": { padding: \"8px\" },\r\n\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"Cross Icon\"\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tCross Icon\r\n\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"Close\"\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tClose\r\n\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</Tooltip> */}\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ marginBottom: \"8px\" }}>Color</Typography>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#F6EEEE\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\tmarginBottom: \"16px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography>Fill</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={colors}\r\n\t\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\twidth: \"30px\",\r\n\t\t\t\t\t\t\t\t\theight: \"30px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t{/* <Box sx={{ marginTop: \"16px\" }}>\r\n\t\t\t\t\t\t\t<Typography sx={{ marginBottom: \"8px\", color: \"rgba(0, 0, 0, 0.38)\" }}>Don't show again</Typography>\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", justifyContent: \"space-between\" }}>\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tvariant={dontShowAgain ? \"contained\" : \"outlined\"}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: dontShowAgain ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDontShowAgainChange(true)}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tvariant={!dontShowAgain ? \"contained\" : \"outlined\"}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: !dontShowAgain ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDontShowAgainChange(false)}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tDon't show again\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\tchecked={dontShowAgain}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={() => { setDontShowAgain(!dontShowAgain) }}\r\n\t\t\t\t\t\t\t\t\t\tdisabled={translaterue}//For temporary we are disableing for turn off\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div> */}\r\n\t\t\t\t\t\t{/* <Switch\r\n      checked={dontShowAgain}\r\n      onChange={handleDontShowAgainChange}\r\n      value={dontShowAgain}\r\n      sx={{\r\n        color: dontShowAgain ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n      }}\r\n    /> */}\r\n\t\t\t\t\t{/* </Box> */}\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn`}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t</Button>\r\n\r\n\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default Elementssettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACCC,GAAG,CACHC,UAAU,CACVC,UAAU,CAEVC,IAAI,CACJC,MAAM,KAKA,eAAe,CACtB,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAW/C,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAAyF,IAAxF,CAAEC,uBAAuB,CAAEC,cAAc,CAAEC,YAAY,CAAEC,mBAAyB,CAAC,CAAAJ,IAAA,CAC5G,KAAM,CAAEK,CAAC,CAAEC,SAAU,CAAC,CAAGZ,cAAc,CAAC,CAAC,CAEzC,KAAM,CACLa,cAAc,CACdC,WAAW,CACXC,cAAc,CACdC,iBAAiB,CACjBC,gBAAgB,CAChBC,wBAAwB,CACxBC,oBAAoB,CACpBC,WAAW,CACXC,mBAAmB,CACnBC,QAAQ,CACRC,WAAW,CACXC,OAAO,CACPC,UAAU,CACVC,oBAAoB,CACpBC,mBAAmB,CACnBC,aAAa,CACbC,gBAAgB,CAChBC,4BAA4B,CAC5BC,SACD,CAAC,CAAGhC,cAAc,CAAEiC,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAACkC,OAAO,CAAC,CAAE;AACzD,KAAM,CAACW,WAAW,CAAEC,cAAc,CAAC,CAAG9C,QAAQ,CAACsC,aAAa,CAAC,CAC7D,KAAM,CAACS,MAAM,CAAEC,SAAS,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACiD,WAAW,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAC,YAAY,CAAC,CAC5D,KAAM,CAACmD,aAAa,CAAEC,gBAAgB,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACqD,MAAM,CAAEC,SAAS,CAAC,CAAGtD,QAAQ,CAAC,SAAS,CAAC,CAC/C;AACA,KAAM,CAACuD,eAAe,CAAEC,mBAAmB,CAAC,CAAGxD,QAAQ,CAAC,CACvDyD,UAAU,CAAEzB,QAAQ,CACpB0B,sBAAsB,CAAEC,MAAM,CAAClC,cAAc,CAAC,EAAI,CAAG;AACtD,CAAC,CAAC,CAEF;AACAxB,SAAS,CAAC,IAAM,CACf;AACAuD,mBAAmB,CAAC,CACnBC,UAAU,CAAEzB,QAAQ,CACpB0B,sBAAsB,CAAEC,MAAM,CAAClC,cAAc,CAAC,EAAI,CACnD,CAAC,CAAC,CACFmB,cAAc,CAACV,OAAO,CAAC,CACvBY,cAAc,CAACR,aAAa,CAAC,CAC9B,CAAC,CAAE,CAACN,QAAQ,CAAEP,cAAc,CAAES,OAAO,CAAEI,aAAa,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAsB,kBAAkB,CAAIC,MAAuB,EAAK,CACvD;AACA,KAAM,CAAAC,SAAS,CAAGH,MAAM,CAACE,MAAM,CAAC,CAChCL,mBAAmB,CAACO,WAAW,EAAI,CAClC,MAAO,CAAE,GAAGA,WAAW,CAAEL,sBAAsB,CAAEI,SAAU,CAAC,CAC7D,CAAC,CAAC,CAEF;AACD,CAAC,CAED,KAAM,CAAAE,uBAAuB,CAAIC,KAAa,EAAK,CAClDf,cAAc,CAACe,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAC,uBAAuB,CAAIC,CAAsC,EAAK,CAC3E,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAC5BX,SAAS,CAACc,KAAK,CAAC,CACjB,CAAC,CAED,KAAM,CAAAE,yBAAyB,CAAGA,CAACC,KAA0C,CAAEC,OAAgB,GAAK,CACnGpB,gBAAgB,CAACoB,OAAO,CAAC,CAC1B,CAAC,CAGD,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAChC;AACA,KAAM,CAAAC,qBAAqB,CAAG1C,QAAQ,CACtC,KAAM,CAAA2C,kBAAkB,CAAGD,qBAAqB,GAAKnB,eAAe,CAACE,UAAU,CAC/E;AACA,KAAM,CAAAmB,cAAc,CAAG,CACtB5C,QAAQ,CAAEuB,eAAe,CAACE,UAAU,CAC/BC,sBAAsB,CAAEH,eAAe,CAACG,sBAAsB,CAC9DxB,OAAO,CAAES,WAAW,CACpBkC,aAAa,CAAEhC,WACrB,CAAC,CAED;AACA,KAAM,CAAAiC,WAAW,CAAGrE,cAAc,CAACsE,QAAQ,CAAC,CAAC,CAACD,WAAW,CACzD,KAAM,CAAAE,WAAW,CAAGvE,cAAc,CAACsE,QAAQ,CAAC,CAAC,CAE7C;AACA;AACA,GAAIJ,kBAAkB,CAAE,CACvB;AACAK,WAAW,CAAC/C,WAAW,CAACsB,eAAe,CAACE,UAAU,CAAC,CACpD,CAEA;AACA;AACAlC,cAAc,CAAC,CACd,IAAIC,WAAW,EAAI,CAAEyD,OAAO,CAAE,EAAE,CAAEC,WAAW,CAAE,YAAY,CAAEC,KAAK,CAAE,SAAS,CAAEC,aAAa,CAAE,IAAK,CAAC,CAAC,CACrGC,UAAU,CAAE1C,WACb,CAAC,CAAC,CAEF;AACAmC,WAAW,CACV,IAAM,CACL;AACAtC,4BAA4B,CAACoC,cAAc,CAAC,CAE5C;AACAI,WAAW,CAAC/C,WAAW,CAACsB,eAAe,CAACE,UAAU,CAAC,CACnDuB,WAAW,CAACtD,iBAAiB,CAAC6B,eAAe,CAACG,sBAAsB,CAAC,CACrEsB,WAAW,CAAC7C,UAAU,CAACQ,WAAW,CAAC,CACnCqC,WAAW,CAACzC,gBAAgB,CAACM,WAAW,CAAC,CAEzC;AACA;AACA,GAAImC,WAAW,CAACM,YAAY,EAAIN,WAAW,CAACrD,gBAAgB,GAAK,MAAM,CAAE,KAAA4D,qBAAA,CAAAC,sBAAA,CACxEC,OAAO,CAACC,GAAG,CAAC,iFAAiF,CAAE,CAC9FjC,UAAU,CAAEF,eAAe,CAACE,UAAU,CACtCC,sBAAsB,CAAEH,eAAe,CAACG,sBAAsB,CAC9DxB,OAAO,CAAES,WAAW,CACpBkC,aAAa,CAAEhC,WAAW,CAC1Bf,WAAW,CAAEkD,WAAW,CAAClD,WAAW,CACpC6D,UAAU,CAAE,EAAAJ,qBAAA,CAAAP,WAAW,CAACY,eAAe,UAAAL,qBAAA,kBAAAC,sBAAA,CAA3BD,qBAAA,CAA6BM,SAAS,UAAAL,sBAAA,iBAAtCA,sBAAA,CAAwCM,MAAM,GAAI,CAC/D,CAAC,CAAC,CAEF;AACAd,WAAW,CAACe,mCAAmC,CAAC,CAAC,CAClD,CACD,CAAC,CACD,sBAAsB,CACtB,0BACD,CAAC,CACD;AACA,GAAIpE,gBAAgB,GAAK,MAAM,EAAIS,oBAAoB,GAAK,QAAQ,EAAIK,SAAS,GAAK,WAAW,EAAIrB,mBAAmB,CAAE,CACzH;AACA;AACA;AAEA;AACA,KAAM,CAAA4E,cAAc,CAAGhB,WAAW,CAACiB,QAAQ,EAAI,IAAI,CACnD,KAAM,CAAAC,aAAa,CAAGlB,WAAW,CAACmB,WAAW,EAAI,GAAG,CAEpD;AACA;AACA/E,mBAAmB,CAClB,WAAW,CACXgF,QAAQ,CAACJ,cAAc,CAAC,CACxBI,QAAQ,CAACF,aAAa,CAAC,CACvB,IAAI,CAAE;AACN,CAAC,CAAK;AACN,EAAM;AACP,CAAC,CACF,CAEA;AACAlD,SAAS,CAAC,KAAK,CAAC,CAChB/B,uBAAuB,CAAC,KAAK,CAAC,CAC9BC,cAAc,CAAC,IAAI,CAAC,CACpBC,YAAY,CAAC,IAAI,CAAC,CAClBkB,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CACDpC,SAAS,CAAC,IAAM,CACf,GAAIuB,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAE6D,UAAU,CAAE,CAC5BlD,UAAU,CAAC,IAAI,CAAC,CAChBmB,SAAS,CAAC9B,WAAW,CAAC2D,KAAK,CAAC,CAC7B,CACD,CAAC,CAAE,CAAC3D,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE6D,UAAU,CAAC,CAAC,CAG7B;AACA,GAAI,CAACtC,MAAM,CAAE,MAAO,KAAI,CACxB,mBACCnC,IAAA,QACCyF,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BzF,KAAA,QAAKwF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BzF,KAAA,QAAKwF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnC3F,IAAA,CAACR,UAAU,EACV,aAAW,OAAO,CAClBoG,OAAO,CAAEA,CAAA,GAAM,CACdxD,SAAS,CAAC,KAAK,CAAC,CAChB/B,uBAAuB,CAAC,KAAK,CAAC,CAC9BE,YAAY,CAAC,IAAI,CAAC,CACnB,CAAE,CAAAoF,QAAA,cAEF3F,IAAA,CAACJ,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbI,IAAA,QAAK0F,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEjF,SAAS,CAAC,UAAU,CAAC,CAAM,CAAC,cAC1DV,IAAA,CAACR,UAAU,EACVqG,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBD,OAAO,CAAEA,CAAA,GAAM,CACdxD,SAAS,CAAC,KAAK,CAAC,CAChB/B,uBAAuB,CAAC,KAAK,CAAC,CAC9BC,cAAc,CAAC,IAAI,CAAC,CACpBC,YAAY,CAAC,IAAI,CAAC,CACnB,CAAE,CAAAoF,QAAA,cAEF3F,IAAA,CAACL,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAENO,KAAA,QACCwF,SAAS,CAAC,gBACV;AAAA,CAAAC,QAAA,EAEE5E,gBAAgB,GAAI,SAAS,EAAIA,gBAAgB,GAAI,QAAQ,eAC9Db,KAAA,CAACZ,GAAG,EAACoG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC3F,IAAA,CAACT,UAAU,EACVmG,SAAS,CAAC,qBACX;AAAA,CAAAC,QAAA,CAEEjF,SAAS,CAAC,UAAU,CAAC,CACX,CAAC,cAEbV,IAAA,SAAA2F,QAAA,cACAzF,KAAA,UAAOwF,SAAS,CAAC,eAAe,CAAAC,QAAA,eACnC3F,IAAA,UACI8F,IAAI,CAAC,UAAU,CACflC,OAAO,CAAEjB,eAAe,CAACE,UAAW,CACpCkD,QAAQ,CAAGxC,CAAC,EAAK,CACtB,KAAM,CAAAyC,QAAQ,CAAGzC,CAAC,CAACE,MAAM,CAACG,OAAO,CACjC;AACAhB,mBAAmB,CAACO,WAAW,EAAI,CAClC,MAAO,CAAE,GAAGA,WAAW,CAAEN,UAAU,CAAEmD,QAAS,CAAC,CAChD,CAAC,CAAC,CAEF;AACK,CAAE,CACFN,SAAS,CAAC,uBAAuB,CACpC,CAAC,cACF1F,IAAA,SAAM0F,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC7B,CAAC,CAEK,CAAC,EACH,CAEL,CAED/C,eAAe,CAACE,UAAU,EAAK9B,gBAAgB,GAAI,SAAS,EAAIA,gBAAgB,GAAI,QAAU,eAC9Ff,IAAA,CAACV,GAAG,EACH2G,EAAE,CAAE,CACHC,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,KACf,CAAE,CAAAV,QAAA,cAEFzF,KAAA,CAACT,IAAI,EACJ6G,SAAS,MACTC,OAAO,CAAE,CAAE,CAAAZ,QAAA,eAEX3F,IAAA,CAACP,IAAI,EACJ+G,IAAI,MACHC,EAAE,CAAE,EAAG,CAAAd,QAAA,cAERzF,KAAA,CAACZ,GAAG,EAAC2G,EAAE,CAAE,CACPS,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBR,OAAO,CAAE,UAAU,CACnBD,YAAY,CAAE,KAAK,CACnBU,MAAM,CAAE,SAAkB,CAAE,CAAAlB,QAAA,eAC5B3F,IAAA,CAACT,UAAU,EAACmG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEjF,SAAS,CAAC,gBAAgB,CAAC,CAAa,CAAC,cAC1FV,IAAA,UACC8F,IAAI,CAAC,OAAO,CACZzC,KAAK,CAAEpB,WAAY,CACnB8D,QAAQ,CAAGxC,CAAC,EAAK,CAChB,KAAM,CAAAuD,QAAQ,CAAGvD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAC/B;AACAnB,cAAc,CAAC4E,QAAQ,CAAC,CACxB;AACD,CAAE,CACFpB,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,CACE,CAAC,cACP1F,IAAA,CAACP,IAAI,EACJ+G,IAAI,MACHC,EAAE,CAAE,EAAG,CAAAd,QAAA,cAERzF,KAAA,CAACZ,GAAG,EACHsG,OAAO,CAAEA,CAAA,GAAM5C,kBAAkB,CAAC,CAAC,CAAE,CACrCiD,EAAE,CAAE,CACHS,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBR,OAAO,CAAE,UAAU,CACnBF,eAAe,CAAEnD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,WAAW,CAAG,SAAS,CAC/FqD,YAAY,CAAE,KAAK,CACnBU,MAAM,CAAE,SAAS,CACjBE,MAAM,CAAChE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,+BAA+B,CAAE,aAChG,CAAE,CAAA6C,QAAA,eAED3F,IAAA,CAACT,UAAU,EAAC0G,EAAE,CAAE,CAAEzC,KAAK,CAAE,qBAAsB,CAAE,CAAAmC,QAAA,CAAEjF,SAAS,CAAC,MAAM,CAAC,CAAa,CAAC,cACnFR,KAAA,QAAK8G,KAAK,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEO,GAAG,CAAE,KAAM,CAAE,CAAAtB,QAAA,eAC3C3F,IAAA,QAAKgH,KAAK,CAAE,CAAEE,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,KAAK,CAAEjB,eAAe,CAAE,qBAAqB,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAM,CAAC,cAChHnG,IAAA,QAAKgH,KAAK,CAAE,CAAEE,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,KAAK,CAAEjB,eAAe,CAAE,qBAAqB,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAM,CAAC,cAChHnG,IAAA,QAAKgH,KAAK,CAAE,CAAEE,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,KAAK,CAAEjB,eAAe,CAAE,qBAAqB,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAM,CAAC,EAC5G,CAAC,EACF,CAAC,CACD,CAAC,cAEPnG,IAAA,CAACP,IAAI,EACJ+G,IAAI,MACHC,EAAE,CAAE,EAAG,CACPR,EAAE,CAAE,CAACmB,UAAU,CAAC,gBAAgB,CAAE,CAAAzB,QAAA,cAEnCzF,KAAA,CAACZ,GAAG,EACHsG,OAAO,CAAEA,CAAA,GAAM5C,kBAAkB,CAAC,CAAC,CAAE,CACrCiD,EAAE,CAAE,CACHS,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBR,OAAO,CAAE,UAAU,CACnBF,eAAe,CAAEnD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,WAAW,CAAG,SAAS,CAC/FqD,YAAY,CAAE,KAAK,CACnBU,MAAM,CAAE,SAAS,CACjBE,MAAM,CAAChE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,+BAA+B,CAAE,aAChG,CAAE,CAAA6C,QAAA,eAED3F,IAAA,CAACT,UAAU,EAAC0G,EAAE,CAAE,CAAEzC,KAAK,CAAE,qBAAsB,CAAE,CAAAmC,QAAA,CAAEjF,SAAS,CAAC,iBAAiB,CAAC,CAAa,CAAC,cAC9FV,IAAA,QAAKgH,KAAK,CAAE,CAAEE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEjB,eAAe,CAAE,SAAU,CAAE,CAAM,CAAC,EAC5E,CAAC,CACD,CAAC,cACPlG,IAAA,CAACP,IAAI,EACJ+G,IAAI,MACHC,EAAE,CAAE,EAAG,CACPR,EAAE,CAAE,CAACmB,UAAU,CAAC,gBAAgB,CAAE,CAAAzB,QAAA,cAEnCzF,KAAA,CAACZ,GAAG,EACHsG,OAAO,CAAEA,CAAA,GAAM5C,kBAAkB,CAAC,CAAC,CAAE,CACrCiD,EAAE,CAAE,CACHS,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBR,OAAO,CAAE,UAAU,CACnBF,eAAe,CAAEnD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,WAAW,CAAG,SAAS,CAC/FqD,YAAY,CAAE,KAAK,CACnBU,MAAM,CAAE,SAAS,CACjBE,MAAM,CAAChE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,+BAA+B,CAAE,aAChG,CAAE,CAAA6C,QAAA,eAED3F,IAAA,CAACT,UAAU,EAAC0G,EAAE,CAAE,CAAEzC,KAAK,CAAE,qBAAsB,CAAE,CAAAmC,QAAA,CAAEjF,SAAS,CAAC,cAAc,CAAC,CAAa,CAAC,cAC3FR,KAAA,QAAK8G,KAAK,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEO,GAAG,CAAE,KAAM,CAAE,CAAAtB,QAAA,eAC5C3F,IAAA,QAAKgH,KAAK,CAAE,CAACE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEjB,eAAe,CAAE,qBAAqB,CAAEC,YAAY,CAAE,OAAQ,CAAE,CAAM,CAAC,cAClHnG,IAAA,QAAKgH,KAAK,CAAE,CAACE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEjB,eAAe,CAAE,qBAAqB,CAAEC,YAAY,CAAE,OAAQ,CAAE,CAAM,CAAC,cAClHnG,IAAA,QAAKgH,KAAK,CAAE,CAACE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,KAAK,CAAEjB,eAAe,CAAE,qBAAqB,CAAEC,YAAY,CAAE,OAAQ,CAAE,CAAM,CAAC,EAE7G,CAAC,EACF,CAAC,CACD,CAAC,cAEPnG,IAAA,CAACP,IAAI,EACJ+G,IAAI,MACHC,EAAE,CAAE,EAAG,CACPR,EAAE,CAAE,CAACmB,UAAU,CAAC,gBAAgB,CAAE,CAAAzB,QAAA,cAGnCzF,KAAA,CAACZ,GAAG,EACHsG,OAAO,CAAEA,CAAA,GAAM5C,kBAAkB,CAAC,CAAC,CAAE,CACrCiD,EAAE,CAAE,CACHS,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBR,OAAO,CAAE,UAAU,CACnBF,eAAe,CAAEnD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,WAAW,CAAG,SAAS,CAC/FqD,YAAY,CAAE,KAAK,CACnBU,MAAM,CAAE,SAAS,CACjBE,MAAM,CAAChE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,+BAA+B,CAAE,aAChG,CAAE,CAAA6C,QAAA,eAED3F,IAAA,CAACT,UAAU,EAAC0G,EAAE,CAAE,CAAEzC,KAAK,CAAE,qBAAsB,CAAE,CAAAmC,QAAA,CAAEjF,SAAS,CAAC,SAAS,CAAC,CAAa,CAAC,cACrFV,IAAA,CAACT,UAAU,EAAC0G,EAAE,CAAE,CAAEzC,KAAK,CAAE,qBAAsB,CAAE,CAAAmC,QAAA,CAAEjF,SAAS,CAAC,QAAQ,CAAC,CAAa,CAAC,EACjF,CAAC,CACD,CAAC,cACPV,IAAA,CAACP,IAAI,EACJ+G,IAAI,MACHC,EAAE,CAAE,EAAG,CACPR,EAAE,CAAE,CAACmB,UAAU,CAAC,gBAAgB,CAAE,CAAAzB,QAAA,cAGnC3F,IAAA,CAACV,GACA;AAAA,EACA2G,EAAE,CAAE,CACHS,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBR,OAAO,CAAE,UAAU,CACnBF,eAAe,CAAEnD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,WAAW,CAAG,SAAS,CAC/FqD,YAAY,CAAE,KAAK,CACnBU,MAAM,CAAE,SAAS,CACjBE,MAAM,CAAChE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,GAAK,CAAC,CAAG,+BAA+B,CAAE,aAChG,CAAE,CAAA6C,QAAA,cAGD3F,IAAA,CAACT,UAAU,EAAC0G,EAAE,CAAE,CAAEzC,KAAK,CAAE,qBAAsB,CAAE,CAAAmC,QAAA,CAAEjF,SAAS,CAAC,WAAW,CAAC,CAAa,CAAC,CACpF,CAAC,CACD,CAAC,EACF,CAAC,CACH,CACJ,cAIDR,KAAA,CAACZ,GAAG,EAACoG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC3F,IAAA,QACC0F,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAG9BjF,SAAS,CAAC,SAAS,CAAC,CACjB,CAAC,cACNV,IAAA,QAAA2F,QAAA,cACAzF,KAAA,UAAOwF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAClC3F,IAAA,UACI8F,IAAI,CAAC,UAAU,CACflC,OAAO,CAAE7B,WAAY,CACrBgE,QAAQ,CAAGxC,CAAC,EAAKvB,cAAc,CAACuB,CAAC,CAACE,MAAM,CAACG,OAAO,CAAE,CAClDyD,IAAI,CAAC,aAAa,CACrB,CAAC,cACFrH,IAAA,SAAM0F,SAAS,CAAC,QAAQ,CAAO,CAAC,EACtB,CAAC,CACH,CAAC,EAEH,CAAC,CAGNpE,OAAO,eACPtB,IAAA,SA6IM,CACL,EA8BI,CAAC,cACPA,IAAA,QAAK0F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClC3F,IAAA,CAACN,MAAM,EACN4H,OAAO,CAAC,WAAW,CACnB1B,OAAO,CAAE/B,kBAAmB,CAC5B6B,SAAS,CAAE,WAAY,CAAAC,QAAA,CAEtBjF,SAAS,CAAC,OAAO,CAAC,CACZ,CAAC,CAEN,CAAC,EACD,CAAC,CACD,CAAC,CAET,CAAC,CAED,cAAe,CAAAP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}