{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\LauncherSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button, Tooltip } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { chkicn1, chkicn2, chkicn3, chkicn4, chkicn5, chkicn6, launlftun, launrgtse, launlftse, launrgtun } from \"../../assets/icons/icons\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LauncherSettings = ({\n  currentGuide\n}) => {\n  _s();\n  var _checklistLauncherPro, _checklistLauncherPro2;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    titlePopup,\n    setTitlePopup,\n    setDesignPopup,\n    titleColor,\n    setTitleColor,\n    launcherColor,\n    setLauncherColor,\n    iconColor,\n    setIconColor,\n    setShowLauncherSettings,\n    checklistGuideMetaData,\n    updateChecklistLauncher,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const encodeToBase64 = svgString => {\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\n  };\n  const [icons, setIcons] = useState(() => {\n    return [{\n      id: 1,\n      base64: encodeToBase64(chkicn1),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn1\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 58\n      }, this),\n      selected: false\n    }, {\n      id: 2,\n      base64: encodeToBase64(chkicn2),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn2\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 58\n      }, this),\n      selected: false\n    }, {\n      id: 3,\n      base64: encodeToBase64(chkicn3),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn3\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 58\n      }, this),\n      selected: false\n    }, {\n      id: 4,\n      base64: encodeToBase64(chkicn4),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn4\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 58\n      }, this),\n      selected: false\n    }, {\n      id: 5,\n      base64: encodeToBase64(chkicn5),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn5\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 58\n      }, this),\n      selected: false\n    }, {\n      id: 6,\n      base64: encodeToBase64(chkicn6),\n      component: /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: chkicn6\n        },\n        style: {\n          zoom: 1,\n          display: \"flex\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 58\n      }, this),\n      selected: false\n    }];\n  });\n  const [checklistLauncherProperties, setChecklistLauncherProperties] = useState(() => {\n    var _checklistGuideMetaDa;\n    const initialchecklistLauncherProperties = ((_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : _checklistGuideMetaDa.launcher) || {\n      type: \"Icon\",\n      icon: \"\",\n      text: \"Get Started\",\n      iconColor: \"#fff\",\n      textColor: \"#fff\",\n      launcherColor: \"var(--primarycolor)\",\n      launcherposition: {\n        left: false,\n        right: true,\n        xaxisOffset: \"10\",\n        yaxisOffset: \"10\"\n      },\n      notificationBadge: false,\n      notificationBadgeColor: \"red\",\n      notificationTextColor: \"#fff\"\n    };\n    return initialchecklistLauncherProperties;\n  });\n\n  // State for tracking changes and apply button\n  const [isDisabled, setIsDisabled] = useState(true);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [initialState, setInitialState] = useState(() => {\n    var _checklistGuideMetaDa2;\n    // Use the actual data from store if available, otherwise use the default\n    return ((_checklistGuideMetaDa2 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa2 === void 0 ? void 0 : _checklistGuideMetaDa2.launcher) || checklistLauncherProperties;\n  });\n\n  // Sync local state with store data only when component mounts (not on every store change)\n  useEffect(() => {\n    var _checklistGuideMetaDa3;\n    if ((_checklistGuideMetaDa3 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa3 !== void 0 && _checklistGuideMetaDa3.launcher) {\n      const newLauncher = checklistGuideMetaData[0].launcher;\n      setChecklistLauncherProperties(newLauncher);\n      setInitialState(newLauncher);\n      setHasChanges(false);\n      setIsDisabled(true);\n      // Clear any validation errors on mount\n      setTextError(\"\");\n    } else {\n      // If no launcher data exists, ensure default icon is set in initialState\n      const defaultIcon = icons[0];\n      if (defaultIcon) {\n        var _defaultIcon$componen;\n        const svgElement = (_defaultIcon$componen = defaultIcon.component.props.dangerouslySetInnerHTML) === null || _defaultIcon$componen === void 0 ? void 0 : _defaultIcon$componen.__html;\n        if (svgElement) {\n          const base64Icon = encodeToBase64(svgElement);\n          const defaultProperties = {\n            ...checklistLauncherProperties,\n            icon: base64Icon\n          };\n          setInitialState(defaultProperties);\n        }\n      }\n    }\n  }, []); // Empty dependency array - only run on mount\n\n  // State for tracking validation errors\n  const [textError, setTextError] = useState(\"\");\n  // Function to check if the Apply button should be enabled\n  const updateApplyButtonState = (changed, hasErrors = false) => {\n    setIsDisabled(!changed || hasErrors);\n  };\n\n  // Effect to check for any changes compared to initial state\n  useEffect(() => {\n    // Compare current properties with initial state\n    const hasAnyChanges = JSON.stringify(checklistLauncherProperties) !== JSON.stringify(initialState);\n    setHasChanges(hasAnyChanges);\n\n    // Check for validation errors\n    const hasValidationErrors = !!textError;\n    updateApplyButtonState(hasAnyChanges, hasValidationErrors);\n  }, [checklistLauncherProperties, initialState, textError]);\n  const handleTitleColorChange = e => setTitleColor(e.target.value);\n  useEffect(() => {\n    if (checklistLauncherProperties.icon) {\n      setIcons(prevIcons => prevIcons.map(icon => ({\n        ...icon,\n        selected: icon.base64 === checklistLauncherProperties.icon // Compare Base64 strings directly\n      })));\n    }\n  }, [checklistLauncherProperties.icon]);\n  const handleClose = () => {\n    setShowLauncherSettings(false);\n  };\n  const handledesignclose = () => {\n    setDesignPopup(false);\n  };\n  const handleSizeChange = value => {\n    const sizeInPx = 16 + (value - 1) * 4;\n    onPropertyChange(\"Size\", sizeInPx);\n  };\n  const onReselectElement = () => {};\n  const onPropertyChange = (key, value) => {\n    // Validate text input\n    if (key === \"text\") {\n      let errorMessage = \"\";\n      if (value.length < 2) {\n        errorMessage = translate(\"Min: 2 Characters\");\n      } else if (value.length > 20) {\n        errorMessage = translate(\"Max: 20 Characters\");\n      }\n      setTextError(errorMessage);\n    }\n    setChecklistLauncherProperties(prevState => {\n      let newState;\n      // Handle nested launcherposition properties\n      if (key === \"xaxisOffset\" || key === \"yaxisOffset\") {\n        newState = {\n          ...prevState,\n          launcherposition: {\n            ...prevState.launcherposition,\n            [key]: value\n          }\n        };\n      } else {\n        // Handle other properties normally\n        newState = {\n          ...prevState,\n          [key]: value\n        };\n      }\n      // Mark that changes have been made\n      setHasChanges(true);\n      return newState;\n    });\n  };\n  const handleIconColorChange = e => {\n    setIconColor(e.target.value);\n  };\n  const handleLauncherColorChange = e => {\n    setLauncherColor(e.target.value);\n  };\n  const handleApplyChanges = () => {\n    updateChecklistLauncher(checklistLauncherProperties);\n    // Update the initial state to the current state after applying changes\n    setInitialState({\n      ...checklistLauncherProperties\n    });\n    // Reset the changes flag\n    setHasChanges(false);\n    // Disable the Apply button\n    setIsDisabled(true);\n    setIsUnSavedChanges(true);\n    handleClose();\n  };\n  const [type, setType] = useState('Text');\n  const [text, setText] = useState('Get Started');\n  const [textColor, setTextColor] = useState('#ffffff');\n  const handleTypeChange = newType => {\n    setType(newType);\n    onPropertyChange(\"type\", newType);\n\n    // // Reset icon selection when type changes\n    // setIcons(prevIcons => prevIcons.map(icon => ({ ...icon, selected: false })));\n\n    // // Also reset the selected icon in checklistLauncherProperties\n    // setChecklistLauncherProperties((prev:any) => ({\n    // \t...prev,\n    // \ticon: null, // Clear the selected icon\n    // }));\n  };\n  const [error, setError] = useState(null);\n  const [icon, setIcon] = useState();\n\n  // Helper function to convert SVG to Base64\n  const svgToBase64 = svgString => {\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\n  };\n  useEffect(() => {\n    const defaultIcon = icons[0];\n    if (defaultIcon && !checklistLauncherProperties.icon) {\n      var _defaultIcon$componen2;\n      const svgElement = (_defaultIcon$componen2 = defaultIcon.component.props.dangerouslySetInnerHTML) === null || _defaultIcon$componen2 === void 0 ? void 0 : _defaultIcon$componen2.__html;\n      if (svgElement) {\n        const base64Icon = encodeToBase64(svgElement);\n        const appliedIconColorBase64Icon = modifySVGColor(base64Icon, checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.iconColor);\n        setIcon(base64Icon);\n\n        // Create the updated properties with default icon\n        const updatedProperties = {\n          ...checklistLauncherProperties,\n          icon: base64Icon\n        };\n\n        // Update the state\n        setChecklistLauncherProperties(updatedProperties);\n\n        // IMPORTANT: Also update the initialState to include the default icon\n        // This prevents the change detection from thinking there's a user change\n        setInitialState(updatedProperties);\n\n        // Update the store\n        updateChecklistLauncher(updatedProperties);\n      }\n    }\n  }, []);\n  const modifySVGColor = (base64SVG, color) => {\n    if (!base64SVG) {\n      return \"\";\n    }\n    try {\n      // Check if the string is a valid base64 SVG\n      if (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\n        return base64SVG; // Return the original if it's not an SVG\n      }\n      const decodedSVG = atob(base64SVG.split(\",\")[1]);\n\n      // Check if this is primarily a stroke-based or fill-based icon\n      const hasStroke = decodedSVG.includes('stroke=\"');\n      const hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\n      let modifiedSVG = decodedSVG;\n      if (hasStroke && !hasColoredFill) {\n        // This is a stroke-based icon (like chkicn2-6) - only change stroke color\n        modifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\n      } else if (hasColoredFill) {\n        // This is a fill-based icon (like chkicn1) - only change fill color\n        modifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\n      } else {\n        // No existing fill or stroke, add fill to make it visible\n        modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\n        modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\n      }\n      const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\n      return modifiedBase64;\n    } catch (error) {\n      // console.error(\"Error modifying SVG color:\", error);\n      return base64SVG; // Return the original if there's an error\n    }\n  };\n  const handleIconClick = async id => {\n    setIcons(prevIcons => prevIcons.map(icon => ({\n      ...icon,\n      selected: icon.id === id\n    })));\n    const selectedIcon = icons.find(icon => icon.id === id);\n    if (selectedIcon) {\n      var _selectedIcon$compone;\n      const svgElement = (_selectedIcon$compone = selectedIcon.component.props.dangerouslySetInnerHTML) === null || _selectedIcon$compone === void 0 ? void 0 : _selectedIcon$compone.__html;\n      if (svgElement) {\n        const base64Icon = svgToBase64(svgElement);\n        const appliedIconColorBase64Icon = modifySVGColor(base64Icon, checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.iconColor);\n        setIcon(base64Icon);\n        setChecklistLauncherProperties(prevState => ({\n          ...prevState,\n          // Copy previous state\n          icon: base64Icon // Update icon property\n        }));\n        setHasChanges(true);\n      }\n    }\n  };\n  const [setPositionLeft, setSetPositionLeft] = useState(false);\n  const [launlft, setLaunlft] = useState(launlftun);\n  const [launrgt, setLaunrgt] = useState(launrgtse);\n  const handleLauncherLeft = () => {\n    setChecklistLauncherProperties(prev => ({\n      ...prev,\n      launcherposition: {\n        ...prev.launcherposition,\n        left: true,\n        right: false\n      }\n    }));\n    setHasChanges(true);\n    setLaunlft(launlftse);\n    setLaunrgt(launrgtun);\n  };\n  useEffect(() => {\n    const position = checklistLauncherProperties.launcherposition;\n    if (position.left) {\n      setLaunlft(launlftse);\n      setLaunrgt(launrgtun);\n    } else if (position.right) {\n      setLaunlft(launlftun);\n      setLaunrgt(launrgtse);\n    }\n  }, [checklistLauncherProperties.launcherposition]);\n  const handleLauncherRight = () => {\n    setChecklistLauncherProperties(prev => ({\n      ...prev,\n      launcherposition: {\n        ...prev.launcherposition,\n        left: false,\n        right: true\n      }\n    }));\n    setHasChanges(true);\n    setLaunlft(launlftun);\n    setLaunrgt(launrgtse);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": translate('back', {\n            defaultValue: 'back'\n          }),\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate('Launcher', {\n            defaultValue: 'Launcher'\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": translate('close', {\n            defaultValue: 'close'\n          }),\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-control-box\",\n            style: {\n              height: \"auto\",\n              flexDirection: \"column\",\n              padding: \"0\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate('Type', {\n                defaultValue: 'Type'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"inline-block\",\n                gap: \"5px\",\n                padding: \"0 8px 8px 8px\",\n                textAlign: \"left\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `qadpt-type-option ${checklistLauncherProperties.type === 'Icon' ? 'selected' : ''}`,\n                onClick: () => handleTypeChange('Icon'),\n                children: translate('Icon', {\n                  defaultValue: 'Icon'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `qadpt-type-option ${checklistLauncherProperties.type === 'Text' ? 'selected' : ''}`,\n                onClick: () => handleTypeChange('Text'),\n                children: translate('Text', {\n                  defaultValue: 'Text'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `qadpt-type-option ${checklistLauncherProperties.type === 'Icon+Txt' ? 'selected' : ''}`,\n                onClick: () => handleTypeChange('Icon+Txt'),\n                children: translate('Icon+Text', {\n                  defaultValue: 'Icon+Text'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 7\n          }, this), checklistLauncherProperties.type === \"Text\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box\",\n              sx: {\n                flexDirection: \"column\",\n                height: \"auto !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Text', {\n                  defaultValue: 'Text'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate('Step Title', {\n                  defaultValue: 'Step Title'\n                }),\n                className: \"qadpt-control-input\",\n                style: {\n                  width: \"calc(100% - 13px)\",\n                  padding: \"0 8px 8px 8px\",\n                  margin: \"0\"\n                },\n                value: checklistLauncherProperties.text,\n                onChange: e => onPropertyChange(\"text\", e.target.value),\n                error: Boolean(textError),\n                helperText: textError,\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate('Text Color', {\n                  defaultValue: 'Text Color'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.textColor,\n                onChange: e => onPropertyChange(\"textColor\", e.target.value),\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true), checklistLauncherProperties.type === \"Icon\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box\",\n              sx: {\n                flexDirection: \"column\",\n                height: \"auto !important\",\n                padding: \"0 !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate('Icon', {\n                  defaultValue: 'Icon'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  gap: 1,\n                  alignItems: \"center\",\n                  width: \"-webkit-fill-available\",\n                  flexWrap: \"wrap\",\n                  padding: \"0 8px 8px 8px\"\n                },\n                children: icons.map(icon => /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate('Select Icon', {\n                    defaultValue: 'Select Icon'\n                  }),\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleIconClick(icon.id),\n                    sx: {\n                      border: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\n                      borderRadius: \"8px\",\n                      padding: \"8px\",\n                      background: \"#F1ECEC\"\n                    },\n                    children: icon.component\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 11\n                  }, this)\n                }, icon.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 12\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate('Icon Color', {\n                  defaultValue: 'Icon Color'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.iconColor,\n                onChange: e => onPropertyChange(\"iconColor\", e.target.value),\n                className: \"qadpt-color-input\",\n                style: {\n                  backgroundColor: '#fff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true), checklistLauncherProperties.type === \"Icon+Txt\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box\",\n              sx: {\n                flexDirection: \"column\",\n                height: \"auto !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Text', {\n                  defaultValue: 'Text'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate('Step Title', {\n                  defaultValue: 'Step Title'\n                }),\n                className: \"qadpt-control-input\",\n                style: {\n                  width: \"calc(100% - 13px)\",\n                  padding: \"0 8px 8px 8px\",\n                  margin: \"0\"\n                },\n                value: checklistLauncherProperties.text,\n                onChange: e => onPropertyChange(\"text\", e.target.value),\n                error: Boolean(textError),\n                helperText: textError,\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box\",\n              sx: {\n                flexDirection: \"column\",\n                height: \"auto !important\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Icon', {\n                  defaultValue: 'Icon'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  gap: 1,\n                  alignItems: \"center\",\n                  width: \"-webkit-fill-available\",\n                  flexWrap: \"wrap\",\n                  padding: \"0 8px 8px 8px\"\n                },\n                children: icons.map(icon => /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate('Select Icon', {\n                    defaultValue: 'Select Icon'\n                  }),\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleIconClick(icon.id),\n                    sx: {\n                      border: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\n                      borderRadius: \"8px\",\n                      padding: \"8px\",\n                      background: \"#F1ECEC\"\n                    },\n                    children: icon.component\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 11\n                  }, this)\n                }, icon.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 12\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate('Icon Color', {\n                  defaultValue: 'Icon Color'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.iconColor,\n                onChange: e => onPropertyChange(\"iconColor\", e.target.value),\n                className: \"qadpt-color-input\",\n                style: {\n                  backgroundColor: '#fff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate('Text Color', {\n                  defaultValue: 'Text Color'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.textColor,\n                onChange: e => onPropertyChange(\"textColor\", e.target.value),\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate('Launcher Color', {\n                defaultValue: 'Launcher Color'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.launcherColor,\n                onChange: e => onPropertyChange(\"launcherColor\", e.target.value),\n                className: \"qadpt-color-input\",\n                style: {\n                  backgroundColor: '#5F9EA0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            sx: {\n              height: \"auto !important\",\n              flexDirection: \"column !important\",\n              padding: \"0 !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate('Position', {\n                defaultValue: 'Position'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: \"0 8px 8px 8px\",\n                display: \"flex\",\n                gap: \"4px\",\n                cursor: \"pointer\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: launlft\n                },\n                onClick: handleLauncherLeft,\n                style: {\n                  zoom: \"0.95\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: launrgt\n                },\n                onClick: handleLauncherRight,\n                style: {\n                  zoom: \"0.95\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                paddingRight: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"qadpt-control-label\",\n                style: {\n                  whiteSpace: \"normal\",\n                  wordBreak: \"break-word\"\n                },\n                children: [\"X \", translate('Axis Offset', {\n                  defaultValue: 'X Axis Offset'\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"qadpt-chkoffset\",\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: ((_checklistLauncherPro = checklistLauncherProperties.launcherposition) === null || _checklistLauncherPro === void 0 ? void 0 : _checklistLauncherPro.xaxisOffset) || \"10\",\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => onPropertyChange(\"xaxisOffset\", e.target.value),\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 8\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                paddingRight: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                style: {\n                  whiteSpace: \"normal\",\n                  wordBreak: \"break-word\"\n                },\n                children: [\"Y \", translate('Axis Offset', {\n                  defaultValue: 'Y Axis Offset'\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-chkoffset\",\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: ((_checklistLauncherPro2 = checklistLauncherProperties.launcherposition) === null || _checklistLauncherPro2 === void 0 ? void 0 : _checklistLauncherPro2.yaxisOffset) || \"10\",\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => onPropertyChange(\"yaxisOffset\", e.target.value),\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 8\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate('Notification Badge', {\n                defaultValue: 'Notification Badge'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: checklistLauncherProperties.notificationBadge,\n                  onChange: e => onPropertyChange(\"notificationBadge\", e.target.checked),\n                  name: \"showByDefault\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 1\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 7\n          }, this), checklistLauncherProperties.notificationBadge && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Badge Color', {\n                  defaultValue: 'Badge Color'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.notificationBadgeColor,\n                  onChange: e => onPropertyChange(\"notificationBadgeColor\", e.target.value),\n                  className: \"qadpt-color-input\",\n                  style: {\n                    backgroundColor: 'red'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Text Color', {\n                  defaultValue: 'Text Color'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistLauncherProperties === null || checklistLauncherProperties === void 0 ? void 0 : checklistLauncherProperties.notificationTextColor,\n                  onChange: e => onPropertyChange(\"notificationTextColor\", e.target.value),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`,\n          disabled: isDisabled,\n          children: translate('Apply', {\n            defaultValue: 'Apply'\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 393,\n    columnNumber: 3\n  }, this);\n};\n_s(LauncherSettings, \"K9MaKxjo0JMYTPRfErGSYgFP23Q=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = LauncherSettings;\nexport default LauncherSettings;\nvar _c;\n$RefreshReg$(_c, \"LauncherSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CloseIcon", "useDrawerStore", "chkicn1", "chkicn2", "chkicn3", "chkicn4", "chkicn5", "chkicn6", "<PERSON><PERSON><PERSON><PERSON>", "laun<PERSON>tse", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "la<PERSON><PERSON><PERSON>", "ArrowBackIosNewOutlinedIcon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LauncherSettings", "currentGuide", "_s", "_checklistLauncherPro", "_checklistLauncherPro2", "t", "translate", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "launcherColor", "setLauncherColor", "iconColor", "setIconColor", "setShowLauncherSettings", "checklistGuideMetaData", "updateChecklistLauncher", "setIsUnSavedChanges", "state", "encodeToBase64", "svgString", "btoa", "icons", "setIcons", "id", "base64", "component", "dangerouslySetInnerHTML", "__html", "style", "zoom", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selected", "checklistLauncherProperties", "setChecklistLauncherProperties", "_checklistGuideMetaDa", "initialchecklistLauncherProperties", "launcher", "type", "icon", "text", "textColor", "launcherposition", "left", "right", "xaxisOffset", "yaxisOffset", "notificationBadge", "notificationBadgeColor", "notificationTextColor", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "initialState", "setInitialState", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "newLauncher", "setTextError", "defaultIcon", "_defaultIcon$componen", "svgElement", "props", "base64Icon", "defaultProperties", "textError", "updateApplyButtonState", "changed", "hasErrors", "hasAnyChanges", "JSON", "stringify", "hasValidationErrors", "handleTitleColorChange", "e", "target", "value", "prevIcons", "map", "handleClose", "handledesignclose", "handleSizeChange", "sizeInPx", "onPropertyChange", "onReselectElement", "key", "errorMessage", "length", "prevState", "newState", "handleIconColorChange", "handleLauncherColorChange", "handleApplyChanges", "setType", "setText", "setTextColor", "handleTypeChange", "newType", "error", "setError", "setIcon", "svgToBase64", "_defaultIcon$componen2", "appliedIconColorBase64Icon", "modifySVGColor", "updatedProperties", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "handleIconClick", "selectedIcon", "find", "_selectedIcon$compone", "setPositionLeft", "setSetPositionLeft", "la<PERSON><PERSON><PERSON>", "setLaunlft", "la<PERSON><PERSON>t", "setLaunrgt", "handleLauncherLeft", "prev", "position", "handleLauncherRight", "className", "children", "defaultValue", "onClick", "size", "height", "flexDirection", "padding", "gap", "textAlign", "sx", "variant", "placeholder", "width", "margin", "onChange", "Boolean", "helperText", "InputProps", "endAdornment", "border", "paddingLeft", "alignItems", "flexWrap", "title", "borderRadius", "background", "backgroundColor", "cursor", "justifyContent", "paddingRight", "whiteSpace", "wordBreak", "checked", "name", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/LauncherSettings.tsx"], "sourcesContent": ["import React, { useReducer, useState, useEffect } from \"react\";\r\nimport { <PERSON>, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, ToggleButton, ToggleButtonGroup, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n\tchkicn1,\r\n\tchkicn2,\r\n\tchkicn3,\r\n\tchkicn4,\r\n\tchkicn5,\r\n\tchkicn6,\r\n\tInfoFilled,\r\n\tQuestionFill,\r\n\tReselect,\r\n\tediticon,\r\n\tSolid,\r\n\tlaunlftun,\r\n\tlaunrgtse,\r\n\tlaun<PERSON>tse,\r\n\tlaunrgtun,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport AddCircleOutlineIcon from \"@mui/icons-material/AddCircleOutline\";\r\nimport InsertPhotoIcon from \"@mui/icons-material/InsertPhoto\";\r\nimport PersonIcon from \"@mui/icons-material/Person\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\r\nimport { position } from \"jodit/esm/core/helpers\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n\r\nconst LauncherSettings = ({ currentGuide }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\t\r\n\tconst {\r\n\r\n\t\ttitlePopup,\r\n\t\tsetTitlePopup,\r\n\t\tsetDesignPopup,\r\n\t\ttitleColor,\r\n\t\tsetTitleColor,\r\n\t\tlauncherColor,\r\n\t\tsetLauncherColor,\r\n\t\ticonColor,\r\n\t\tsetIconColor,\r\n\t\tsetShowLauncherSettings,\r\n\t\tchecklistGuideMetaData,\r\n\t\tupdateChecklistLauncher,\r\n\t\tsetIsUnSavedChanges,\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst encodeToBase64 = (svgString: string) => {\r\n\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t  };\r\n\t  \r\n\t  const [icons, setIcons] = useState<any[]>(() => {\r\n\t\treturn [\r\n\t\t  { id: 1, base64: encodeToBase64(chkicn1), component: <span dangerouslySetInnerHTML={{ __html: chkicn1 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 2, base64: encodeToBase64(chkicn2), component: <span dangerouslySetInnerHTML={{ __html: chkicn2 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 3, base64: encodeToBase64(chkicn3), component: <span dangerouslySetInnerHTML={{ __html: chkicn3 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 4, base64: encodeToBase64(chkicn4), component: <span dangerouslySetInnerHTML={{ __html: chkicn4 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 5, base64: encodeToBase64(chkicn5), component: <span dangerouslySetInnerHTML={{ __html: chkicn5 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 6, base64: encodeToBase64(chkicn6), component: <span dangerouslySetInnerHTML={{ __html: chkicn6 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t];\r\n\t  });\r\n\tconst [\r\n\t\tchecklistLauncherProperties, setChecklistLauncherProperties] = useState<any>(() => {\r\n\t\tconst initialchecklistLauncherProperties = checklistGuideMetaData[0]?.launcher || {\r\n\t\t\ttype: \"Icon\",\r\n\t\t\ticon: \"\",\r\n\t\t\ttext: \"Get Started\",\r\n\t\t\ticonColor: \"#fff\",\r\n\t\t\ttextColor: \"#fff\",\r\n\t\t\tlauncherColor: \"var(--primarycolor)\",\r\n\t\t\tlauncherposition: {\r\n\t\t\t\tleft: false,\r\n\t\t\t\tright: true,\r\n\t\t\t\txaxisOffset: \"10\",\r\n\t\t\t\tyaxisOffset: \"10\",\r\n\t\t\t},\r\n\t\t\tnotificationBadge: false,\r\n\t\t\tnotificationBadgeColor: \"red\",\r\n\t\t\tnotificationTextColor: \"#fff\",\r\n\r\n\t\t};\r\n\t\treturn initialchecklistLauncherProperties;\r\n\t});\r\n\r\n\t// State for tracking changes and apply button\r\n\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\tconst [initialState, setInitialState] = useState(() => {\r\n\t\t// Use the actual data from store if available, otherwise use the default\r\n\t\treturn checklistGuideMetaData[0]?.launcher || checklistLauncherProperties;\r\n\t});\r\n\r\n\t// Sync local state with store data only when component mounts (not on every store change)\r\n\tuseEffect(() => {\r\n\t\tif (checklistGuideMetaData[0]?.launcher) {\r\n\t\t\tconst newLauncher = checklistGuideMetaData[0].launcher;\r\n\t\t\tsetChecklistLauncherProperties(newLauncher);\r\n\t\t\tsetInitialState(newLauncher);\r\n\t\t\tsetHasChanges(false);\r\n\t\t\tsetIsDisabled(true);\r\n\t\t\t// Clear any validation errors on mount\r\n\t\t\tsetTextError(\"\");\r\n\t\t} else {\r\n\t\t\t// If no launcher data exists, ensure default icon is set in initialState\r\n\t\t\tconst defaultIcon = icons[0];\r\n\t\t\tif (defaultIcon) {\r\n\t\t\t\tconst svgElement = defaultIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\t\t\t\tif (svgElement) {\r\n\t\t\t\t\tconst base64Icon = encodeToBase64(svgElement);\r\n\t\t\t\t\tconst defaultProperties = {\r\n\t\t\t\t\t\t...checklistLauncherProperties,\r\n\t\t\t\t\t\ticon: base64Icon\r\n\t\t\t\t\t};\r\n\t\t\t\t\tsetInitialState(defaultProperties);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}, []); // Empty dependency array - only run on mount\r\n\r\n\t// State for tracking validation errors\r\n\tconst [textError, setTextError] = useState(\"\");\r\n\t// Function to check if the Apply button should be enabled\r\n\tconst updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {\r\n\t\tsetIsDisabled(!changed || hasErrors);\r\n\t};\r\n\r\n\t// Effect to check for any changes compared to initial state\r\n\tuseEffect(() => {\r\n\t\t// Compare current properties with initial state\r\n\t\tconst hasAnyChanges = JSON.stringify(checklistLauncherProperties) !== JSON.stringify(initialState);\r\n\t\tsetHasChanges(hasAnyChanges);\r\n\r\n\t\t// Check for validation errors\r\n\t\tconst hasValidationErrors = !!textError;\r\n\r\n\t\tupdateApplyButtonState(hasAnyChanges, hasValidationErrors);\r\n\t}, [checklistLauncherProperties, initialState, textError]);\r\n\tconst handleTitleColorChange = (e: any) => setTitleColor(e.target.value);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (checklistLauncherProperties.icon) {\r\n\t\t  setIcons((prevIcons) =>\r\n\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t  ...icon,\r\n\t\t\t  selected: icon.base64 === checklistLauncherProperties.icon, // Compare Base64 strings directly\r\n\t\t\t}))\r\n\t\t  );\r\n\t\t}\r\n\t}, [checklistLauncherProperties.icon]);\r\n\r\n\t\r\n\tconst handleClose = () => {\r\n\t\tsetShowLauncherSettings(false);\r\n\t};\r\n\tconst handledesignclose = () => {\r\n\t\tsetDesignPopup(false);\r\n\t};\r\n\tconst handleSizeChange = (value: number) => {\r\n\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t};\r\n\r\n\tconst onReselectElement = () => {\r\n\r\n\t};\r\n\r\n\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t// Validate text input\r\n\t\tif (key === \"text\") {\r\n\t\t\tlet errorMessage = \"\";\r\n\t\t\tif (value.length < 2) {\r\n\t\t\t\terrorMessage = translate(\"Min: 2 Characters\");\r\n\t\t\t} else if (value.length > 20) {\r\n\t\t\t\terrorMessage = translate(\"Max: 20 Characters\");\r\n\t\t\t}\r\n\t\t\tsetTextError(errorMessage);\r\n\t\t}\r\n\r\n\r\n\t\tsetChecklistLauncherProperties((prevState: any) => {\r\n\t\t\tlet newState;\r\n\t\t\t// Handle nested launcherposition properties\r\n\t\t\tif (key === \"xaxisOffset\" || key === \"yaxisOffset\") {\r\n\t\t\t\tnewState = {\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\tlauncherposition: {\r\n\t\t\t\t\t\t...prevState.launcherposition,\r\n\t\t\t\t\t\t[key]: value,\r\n\t\t\t\t\t},\r\n\t\t\t\t};\r\n\t\t\t} else {\r\n\t\t\t\t// Handle other properties normally\r\n\t\t\t\tnewState = {\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\t[key]: value,\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\t// Mark that changes have been made\r\n\t\t\tsetHasChanges(true);\r\n\t\t\treturn newState;\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleIconColorChange = (e: any) => {\r\n\t\tsetIconColor(e.target.value);\r\n\t}\r\n\tconst handleLauncherColorChange = (e: any) => { setLauncherColor(e.target.value) }\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\tupdateChecklistLauncher(checklistLauncherProperties);\r\n\t\t// Update the initial state to the current state after applying changes\r\n\t\tsetInitialState({ ...checklistLauncherProperties });\r\n\t\t// Reset the changes flag\r\n\t\tsetHasChanges(false);\r\n\t\t// Disable the Apply button\r\n\t\tsetIsDisabled(true);\r\n\t\tsetIsUnSavedChanges(true);\r\n\t\thandleClose();\r\n\t};\r\n\tconst [type, setType] = useState('Text');\r\n\tconst [text, setText] = useState('Get Started');\r\n\tconst [textColor, setTextColor] = useState('#ffffff');\r\n\r\n\tconst handleTypeChange = (newType: any) => {\r\n\t\tsetType(newType);\r\n\t\tonPropertyChange(\"type\", newType);\r\n\t\r\n\t\t// // Reset icon selection when type changes\r\n\t\t// setIcons(prevIcons => prevIcons.map(icon => ({ ...icon, selected: false })));\r\n\t\r\n\t\t// // Also reset the selected icon in checklistLauncherProperties\r\n\t\t// setChecklistLauncherProperties((prev:any) => ({\r\n\t\t// \t...prev,\r\n\t\t// \ticon: null, // Clear the selected icon\r\n\t\t// }));\r\n\t};\r\n\t\r\n\tconst [error, setError] = useState<string | null>(null);\r\n\t\r\n\tconst [icon, setIcon] = useState<any>();\r\n\r\n\r\n\t\t// Helper function to convert SVG to Base64\r\n\t\tconst svgToBase64 = (svgString: string): string => {\r\n\t\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t\t};\r\n\t\r\nuseEffect(() => {\r\n\tconst defaultIcon = icons[0];\r\n\tif (defaultIcon && !checklistLauncherProperties.icon) {\r\n\t\tconst svgElement = defaultIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\t\tif (svgElement) {\r\n\t\t\tconst base64Icon = encodeToBase64(svgElement);\r\n\t\t\tconst appliedIconColorBase64Icon = modifySVGColor(base64Icon, checklistLauncherProperties?.iconColor);\r\n\t\t\tsetIcon(base64Icon);\r\n\r\n\t\t\t// Create the updated properties with default icon\r\n\t\t\tconst updatedProperties = {\r\n\t\t\t\t...checklistLauncherProperties, \r\n\t\t\t\ticon: base64Icon, \r\n\t\t\t};\r\n\r\n\t\t\t// Update the state\r\n\t\t\tsetChecklistLauncherProperties(updatedProperties);\r\n\t\t\t\r\n\t\t\t// IMPORTANT: Also update the initialState to include the default icon\r\n\t\t\t// This prevents the change detection from thinking there's a user change\r\n\t\t\tsetInitialState(updatedProperties);\r\n\t\t\t\r\n\t\t\t// Update the store\r\n\t\t\tupdateChecklistLauncher(updatedProperties);\r\n\t\t}\r\n\t}\r\n}, [])\r\n\r\n\tconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\t\tif (!base64SVG) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\ttry {\r\n\t\t\t// Check if the string is a valid base64 SVG\r\n\t\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t\t}\r\n\r\n\t\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t\t} else if (hasColoredFill) {\r\n\t\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t\t} else {\r\n\t\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t\t}\r\n\r\n\t\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\t\treturn modifiedBase64;\r\n\t\t} catch (error) {\r\n\t\t\t// console.error(\"Error modifying SVG color:\", error);\r\n\t\t\treturn base64SVG; // Return the original if there's an error\r\n\t\t}\r\n\t};\r\n\t\tconst handleIconClick = async (id: number) => {\r\n\t\t\tsetIcons((prevIcons) =>\r\n\t\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t\t\t...icon,\r\n\t\t\t\t\tselected: icon.id === id,\r\n\t\t\t\t}))\r\n\t\t\t);\r\n\t\t\r\n\t\t\tconst selectedIcon = icons.find((icon) => icon.id === id);\r\n\t\t\tif (selectedIcon) {\r\n\t\t\t\tconst svgElement = selectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\t\t\t\tif (svgElement) {\r\n\t\t\t\t\tconst base64Icon = svgToBase64(svgElement);\r\n\t\t\t\t\tconst appliedIconColorBase64Icon=modifySVGColor(base64Icon, checklistLauncherProperties?.iconColor);\r\n\t\t\t\t\tsetIcon(base64Icon);\r\n\t\t\r\n\r\n\t\t\t\t\tsetChecklistLauncherProperties((prevState:any) => ({\r\n\t\t\t\t\t\t...prevState, // Copy previous state\r\n\t\t\t\t\t\ticon: base64Icon, // Update icon property\r\n\t\t\t\t\t}));\r\n\t\t\t\t\tsetHasChanges(true);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\t\t\r\n\t\tconst [setPositionLeft, setSetPositionLeft] = useState(false);\r\n  const [launlft, setLaunlft] = useState(launlftun); \r\n  const [launrgt, setLaunrgt] = useState(launrgtse); \r\n\r\n  const handleLauncherLeft = () => {\r\n\tsetChecklistLauncherProperties((prev:any) => ({\r\n\t  ...prev,\r\n\t  launcherposition: {\r\n\t\t...prev.launcherposition,\r\n\t\tleft: true, \r\n\t\tright: false, \r\n\t  },\r\n\t}));\r\n\tsetHasChanges(true);\r\n\tsetLaunlft(launlftse); \r\n\tsetLaunrgt(launrgtun); \r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst position = checklistLauncherProperties.launcherposition;\r\n\t\tif (position.left) {\r\n\t\t  setLaunlft(launlftse); \r\n\t\t  setLaunrgt(launrgtun); \r\n\t\t} else if (position.right) {\r\n\t\t  setLaunlft(launlftun); \r\n\t\t  setLaunrgt(launrgtse); \r\n\t\t}\r\n\t  }, [checklistLauncherProperties.launcherposition]); \r\n  \r\n\r\n  const handleLauncherRight = () => {\r\n\tsetChecklistLauncherProperties((prev:any) => ({\r\n\t\t...prev,\r\n\t\tlauncherposition: {\r\n\t\t  ...prev.launcherposition, \r\n\t\t  left: false, \r\n\t\t  right: true, \r\n\t\t},\r\n\t  }));\r\n\tsetHasChanges(true);\r\n\tsetLaunlft(launlftun);\r\n    setLaunrgt(launrgtse); \r\n  };\r\n\t  \r\n\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label={translate('back', { defaultValue: 'back' })}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate('Launcher', { defaultValue: 'Launcher' })}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate('close', { defaultValue: 'close' })}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\r\n\t\t\t\t\t\t<div className=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{height: \"auto\", flexDirection:\"column\" ,padding:\"0\"}}>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Type', { defaultValue: 'Type' })}</div>\r\n\t\t\t\t\t\t\t<div style={{ display:\"inline-block\",gap:\"5px\",padding:\"0 8px 8px 8px\",textAlign:\"left\"}\r\n\t\t\t\t\t\t\t}>\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tclassName={`qadpt-type-option ${checklistLauncherProperties.type === 'Icon' ? 'selected' : ''}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleTypeChange('Icon')}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Icon', { defaultValue: 'Icon' })}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tclassName={`qadpt-type-option ${checklistLauncherProperties.type === 'Text' ? 'selected' : ''}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleTypeChange('Text')}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Text', { defaultValue: 'Text' })}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tclassName={`qadpt-type-option ${checklistLauncherProperties.type === 'Icon+Txt' ? 'selected' : ''}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleTypeChange('Icon+Txt')}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Icon+Text', { defaultValue: 'Icon+Text' })}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.type === \"Text\" && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" >{translate('Text', { defaultValue: 'Text' })}</div>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate('Step Title', { defaultValue: 'Step Title' })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{width: \"calc(100% - 13px)\",padding: \"0 8px 8px 8px\", margin:\"0\"}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.text}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"text\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\terror={Boolean(textError)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thelperText={textError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Text Color', { defaultValue: 'Text Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.textColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"textColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.type === \"Icon\" && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Box id=\"qadpt-designpopup\" className=\"qadpt-control-box\" sx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"0 !important\" }}>\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Icon', { defaultValue: 'Icon' })}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", gap: 1, alignItems: \"center\", width: \"-webkit-fill-available\", flexWrap: \"wrap\", padding: \"0 8px 8px 8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{icons.map(icon => (\r\n\t\t\t\t\t\t\t\t\t\t\t<Tooltip key={icon.id} title={translate('Select Icon', { defaultValue: 'Select Icon' })}>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleIconClick(icon.id)}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground:\"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{icon.component}\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Icon Color', { defaultValue: 'Icon Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.iconColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"iconColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#fff'}}\r\n\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</>\r\n\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.type === \"Icon+Txt\" && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" >{translate('Text', { defaultValue: 'Text' })}</div>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate('Step Title', { defaultValue: 'Step Title' })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{width: \"calc(100% - 13px)\",padding: \"0 8px 8px 8px\", margin:\"0\"}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.text}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"text\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\terror={Boolean(textError)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thelperText={textError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<Box id=\"qadpt-designpopup\" className=\"qadpt-control-box\" sx={{ flexDirection: \"column\", height: \"auto !important\" }}>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" >{translate('Icon', { defaultValue: 'Icon' })}</div>\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", gap: 1, alignItems: \"center\", width: \"-webkit-fill-available\", flexWrap: \"wrap\", padding: \"0 8px 8px 8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{icons.map(icon => (\r\n\t\t\t\t\t\t\t\t\t\t\t<Tooltip key={icon.id} title={translate('Select Icon', { defaultValue: 'Select Icon' })}>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleIconClick(icon.id)}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground:\"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{icon.component}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\r\n\t\t\t\t\t\t\t\t\t{/* Upload New Icon */}\r\n\t\t\t\t\t\t\t\t\t{/* <input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\taccept=\".ico\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"icon-upload\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleFileUpload}\r\n\t\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Icon Color', { defaultValue: 'Icon Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.iconColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"iconColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#fff'}}\r\n\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Text Color', { defaultValue: 'Text Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.textColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"textColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Launcher Color', { defaultValue: 'Launcher Color' })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.launcherColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"launcherColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#5F9EA0'}}\r\n\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\" sx={{height:\"auto !important\",flexDirection:\"column !important\",padding:\"0 !important\"}}>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Position', { defaultValue: 'Position' })}</div>\r\n\t\t\t\t\t\t\t<div style={{    padding: \"0 8px 8px 8px\",display: \"flex\",gap: \"4px\",cursor:\"pointer\"}}>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: launlft }} onClick={handleLauncherLeft} style={{ zoom: \"0.95\" }} />\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html:  launrgt}} onClick={handleLauncherRight} style={{ zoom: \"0.95\" }} />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div style={{display : \"flex\", alignItems : \"center\",justifyContent : \"space-between\",paddingRight : \"8px\"}}>\r\n\t\t\t\t\t\t\t\t<span className=\"qadpt-control-label\" style={{whiteSpace: \"normal\",\r\n\t\t\t\t\t\t\t\t\twordBreak: \"break-word\"\r\n\t\t\t\t\t\t\t\t}}>X {translate('Axis Offset', { defaultValue: 'X Axis Offset' })}</span>\r\n\t\t\t\t\t\t\t<span className=\"qadpt-chkoffset\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.launcherposition?.xaxisOffset || \"10\"}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"xaxisOffset\", e.target.value)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div style={{display : \"flex\", alignItems : \"center\",justifyContent : \"space-between\",paddingRight : \"8px\"}}>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" style={{whiteSpace: \"normal\",\r\n\t\t\t\t\t\t\t\t\twordBreak: \"break-word\"\r\n\t\t\t\t\t\t\t\t}}>Y {translate('Axis Offset', { defaultValue: 'Y Axis Offset' })}</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-chkoffset\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.launcherposition?.yaxisOffset || \"10\"}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"yaxisOffset\", e.target.value)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Notification Badge', { defaultValue: 'Notification Badge' })}</div>\r\n\r\n\t\t\t\t\t\t\t{/* Show by Default Toggle */}\r\n<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\tchecked={checklistLauncherProperties.notificationBadge}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"notificationBadge\", e.target.checked)}\r\n\r\n\t\t\t\t\t\t\t\t\tname=\"showByDefault\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.notificationBadge && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Badge Color', { defaultValue: 'Badge Color' })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.notificationBadgeColor}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"notificationBadgeColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'red'}}\r\n\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Text Color', { defaultValue: 'Text Color' })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.notificationTextColor}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"notificationTextColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\r\n\r\n\r\n\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate('Apply', { defaultValue: 'Apply' })}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default LauncherSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,EAA2IC,OAAO,QAAQ,eAAe;AACtO,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAA6E,yBAAyB;AAE3H,SACCC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EAMPC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,SAAS,QACH,0BAA0B;AACjC,OAAOC,2BAA2B,MAAM,6CAA6C;AAQrF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG/C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACnD,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGX,cAAc,CAAC,CAAC;EAGzC,MAAM;IAELY,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC,SAAS;IACTC,YAAY;IACZC,uBAAuB;IACvBC,sBAAsB;IACtBC,uBAAuB;IACvBC;EACD,CAAC,GAAGpC,cAAc,CAAEqC,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAMC,cAAc,GAAIC,SAAiB,IAAK;IAC7C,OAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE;EACpD,CAAC;EAED,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAQ,MAAM;IACjD,OAAO,CACL;MAAEoD,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAEN,cAAc,CAACrC,OAAO,CAAC;MAAE4C,SAAS,eAAE/B,OAAA;QAAMgC,uBAAuB,EAAE;UAAEC,MAAM,EAAE9C;QAAQ,CAAE;QAAC+C,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACnK;MAAEZ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAEN,cAAc,CAACpC,OAAO,CAAC;MAAE2C,SAAS,eAAE/B,OAAA;QAAMgC,uBAAuB,EAAE;UAAEC,MAAM,EAAE7C;QAAQ,CAAE;QAAC8C,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACnK;MAAEZ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAEN,cAAc,CAACnC,OAAO,CAAC;MAAE0C,SAAS,eAAE/B,OAAA;QAAMgC,uBAAuB,EAAE;UAAEC,MAAM,EAAE5C;QAAQ,CAAE;QAAC6C,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACnK;MAAEZ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAEN,cAAc,CAAClC,OAAO,CAAC;MAAEyC,SAAS,eAAE/B,OAAA;QAAMgC,uBAAuB,EAAE;UAAEC,MAAM,EAAE3C;QAAQ,CAAE;QAAC4C,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACnK;MAAEZ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAEN,cAAc,CAACjC,OAAO,CAAC;MAAEwC,SAAS,eAAE/B,OAAA;QAAMgC,uBAAuB,EAAE;UAAEC,MAAM,EAAE1C;QAAQ,CAAE;QAAC2C,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,EACnK;MAAEZ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAEN,cAAc,CAAChC,OAAO,CAAC;MAAEuC,SAAS,eAAE/B,OAAA;QAAMgC,uBAAuB,EAAE;UAAEC,MAAM,EAAEzC;QAAQ,CAAE;QAAC0C,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,CACpK;EACA,CAAC,CAAC;EACJ,MAAM,CACLC,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGlE,QAAQ,CAAM,MAAM;IAAA,IAAAmE,qBAAA;IACnF,MAAMC,kCAAkC,GAAG,EAAAD,qBAAA,GAAAxB,sBAAsB,CAAC,CAAC,CAAC,cAAAwB,qBAAA,uBAAzBA,qBAAA,CAA2BE,QAAQ,KAAI;MACjFC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,aAAa;MACnBhC,SAAS,EAAE,MAAM;MACjBiC,SAAS,EAAE,MAAM;MACjBnC,aAAa,EAAE,qBAAqB;MACpCoC,gBAAgB,EAAE;QACjBC,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,IAAI;QACjBC,WAAW,EAAE;MACd,CAAC;MACDC,iBAAiB,EAAE,KAAK;MACxBC,sBAAsB,EAAE,KAAK;MAC7BC,qBAAqB,EAAE;IAExB,CAAC;IACD,OAAOb,kCAAkC;EAC1C,CAAC,CAAC;;EAEF;EACA,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,MAAM;IAAA,IAAAwF,sBAAA;IACtD;IACA,OAAO,EAAAA,sBAAA,GAAA7C,sBAAsB,CAAC,CAAC,CAAC,cAAA6C,sBAAA,uBAAzBA,sBAAA,CAA2BnB,QAAQ,KAAIJ,2BAA2B;EAC1E,CAAC,CAAC;;EAEF;EACAhE,SAAS,CAAC,MAAM;IAAA,IAAAwF,sBAAA;IACf,KAAAA,sBAAA,GAAI9C,sBAAsB,CAAC,CAAC,CAAC,cAAA8C,sBAAA,eAAzBA,sBAAA,CAA2BpB,QAAQ,EAAE;MACxC,MAAMqB,WAAW,GAAG/C,sBAAsB,CAAC,CAAC,CAAC,CAAC0B,QAAQ;MACtDH,8BAA8B,CAACwB,WAAW,CAAC;MAC3CH,eAAe,CAACG,WAAW,CAAC;MAC5BL,aAAa,CAAC,KAAK,CAAC;MACpBF,aAAa,CAAC,IAAI,CAAC;MACnB;MACAQ,YAAY,CAAC,EAAE,CAAC;IACjB,CAAC,MAAM;MACN;MACA,MAAMC,WAAW,GAAG1C,KAAK,CAAC,CAAC,CAAC;MAC5B,IAAI0C,WAAW,EAAE;QAAA,IAAAC,qBAAA;QAChB,MAAMC,UAAU,IAAAD,qBAAA,GAAGD,WAAW,CAACtC,SAAS,CAACyC,KAAK,CAACxC,uBAAuB,cAAAsC,qBAAA,uBAAnDA,qBAAA,CAAqDrC,MAAM;QAC9E,IAAIsC,UAAU,EAAE;UACf,MAAME,UAAU,GAAGjD,cAAc,CAAC+C,UAAU,CAAC;UAC7C,MAAMG,iBAAiB,GAAG;YACzB,GAAGhC,2BAA2B;YAC9BM,IAAI,EAAEyB;UACP,CAAC;UACDT,eAAe,CAACU,iBAAiB,CAAC;QACnC;MACD;IACD;EACD,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAM,CAACC,SAAS,EAAEP,YAAY,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC9C;EACA,MAAMmG,sBAAsB,GAAGA,CAACC,OAAgB,EAAEC,SAAkB,GAAG,KAAK,KAAK;IAChFlB,aAAa,CAAC,CAACiB,OAAO,IAAIC,SAAS,CAAC;EACrC,CAAC;;EAED;EACApG,SAAS,CAAC,MAAM;IACf;IACA,MAAMqG,aAAa,GAAGC,IAAI,CAACC,SAAS,CAACvC,2BAA2B,CAAC,KAAKsC,IAAI,CAACC,SAAS,CAAClB,YAAY,CAAC;IAClGD,aAAa,CAACiB,aAAa,CAAC;;IAE5B;IACA,MAAMG,mBAAmB,GAAG,CAAC,CAACP,SAAS;IAEvCC,sBAAsB,CAACG,aAAa,EAAEG,mBAAmB,CAAC;EAC3D,CAAC,EAAE,CAACxC,2BAA2B,EAAEqB,YAAY,EAAEY,SAAS,CAAC,CAAC;EAC1D,MAAMQ,sBAAsB,GAAIC,CAAM,IAAKtE,aAAa,CAACsE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAExE5G,SAAS,CAAC,MAAM;IACf,IAAIgE,2BAA2B,CAACM,IAAI,EAAE;MACpCpB,QAAQ,CAAE2D,SAAS,IACpBA,SAAS,CAACC,GAAG,CAAExC,IAAI,KAAM;QACvB,GAAGA,IAAI;QACPP,QAAQ,EAAEO,IAAI,CAAClB,MAAM,KAAKY,2BAA2B,CAACM,IAAI,CAAE;MAC9D,CAAC,CAAC,CACD,CAAC;IACH;EACD,CAAC,EAAE,CAACN,2BAA2B,CAACM,IAAI,CAAC,CAAC;EAGtC,MAAMyC,WAAW,GAAGA,CAAA,KAAM;IACzBtE,uBAAuB,CAAC,KAAK,CAAC;EAC/B,CAAC;EACD,MAAMuE,iBAAiB,GAAGA,CAAA,KAAM;IAC/B9E,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAM+E,gBAAgB,GAAIL,KAAa,IAAK;IAC3C,MAAMM,QAAQ,GAAG,EAAE,GAAG,CAACN,KAAK,GAAG,CAAC,IAAI,CAAC;IACrCO,gBAAgB,CAAC,MAAM,EAAED,QAAQ,CAAC;EACnC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM,CAEhC,CAAC;EAED,MAAMD,gBAAgB,GAAGA,CAACE,GAAQ,EAAET,KAAU,KAAK;IAClD;IACA,IAAIS,GAAG,KAAK,MAAM,EAAE;MACnB,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIV,KAAK,CAACW,MAAM,GAAG,CAAC,EAAE;QACrBD,YAAY,GAAGvF,SAAS,CAAC,mBAAmB,CAAC;MAC9C,CAAC,MAAM,IAAI6E,KAAK,CAACW,MAAM,GAAG,EAAE,EAAE;QAC7BD,YAAY,GAAGvF,SAAS,CAAC,oBAAoB,CAAC;MAC/C;MACA2D,YAAY,CAAC4B,YAAY,CAAC;IAC3B;IAGArD,8BAA8B,CAAEuD,SAAc,IAAK;MAClD,IAAIC,QAAQ;MACZ;MACA,IAAIJ,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,aAAa,EAAE;QACnDI,QAAQ,GAAG;UACV,GAAGD,SAAS;UACZ/C,gBAAgB,EAAE;YACjB,GAAG+C,SAAS,CAAC/C,gBAAgB;YAC7B,CAAC4C,GAAG,GAAGT;UACR;QACD,CAAC;MACF,CAAC,MAAM;QACN;QACAa,QAAQ,GAAG;UACV,GAAGD,SAAS;UACZ,CAACH,GAAG,GAAGT;QACR,CAAC;MACF;MACA;MACAxB,aAAa,CAAC,IAAI,CAAC;MACnB,OAAOqC,QAAQ;IAChB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,qBAAqB,GAAIhB,CAAM,IAAK;IACzClE,YAAY,CAACkE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EACD,MAAMe,yBAAyB,GAAIjB,CAAM,IAAK;IAAEpE,gBAAgB,CAACoE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAAC,CAAC;EAElF,MAAMgB,kBAAkB,GAAGA,CAAA,KAAM;IAChCjF,uBAAuB,CAACqB,2BAA2B,CAAC;IACpD;IACAsB,eAAe,CAAC;MAAE,GAAGtB;IAA4B,CAAC,CAAC;IACnD;IACAoB,aAAa,CAAC,KAAK,CAAC;IACpB;IACAF,aAAa,CAAC,IAAI,CAAC;IACnBtC,mBAAmB,CAAC,IAAI,CAAC;IACzBmE,WAAW,CAAC,CAAC;EACd,CAAC;EACD,MAAM,CAAC1C,IAAI,EAAEwD,OAAO,CAAC,GAAG9H,QAAQ,CAAC,MAAM,CAAC;EACxC,MAAM,CAACwE,IAAI,EAAEuD,OAAO,CAAC,GAAG/H,QAAQ,CAAC,aAAa,CAAC;EAC/C,MAAM,CAACyE,SAAS,EAAEuD,YAAY,CAAC,GAAGhI,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAMiI,gBAAgB,GAAIC,OAAY,IAAK;IAC1CJ,OAAO,CAACI,OAAO,CAAC;IAChBd,gBAAgB,CAAC,MAAM,EAAEc,OAAO,CAAC;;IAEjC;IACA;;IAEA;IACA;IACA;IACA;IACA;EACD,CAAC;EAED,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpI,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAM,CAACuE,IAAI,EAAE8D,OAAO,CAAC,GAAGrI,QAAQ,CAAM,CAAC;;EAGtC;EACA,MAAMsI,WAAW,GAAItF,SAAiB,IAAa;IAClD,OAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE;EACtD,CAAC;EAEH/C,SAAS,CAAC,MAAM;IACf,MAAM2F,WAAW,GAAG1C,KAAK,CAAC,CAAC,CAAC;IAC5B,IAAI0C,WAAW,IAAI,CAAC3B,2BAA2B,CAACM,IAAI,EAAE;MAAA,IAAAgE,sBAAA;MACrD,MAAMzC,UAAU,IAAAyC,sBAAA,GAAG3C,WAAW,CAACtC,SAAS,CAACyC,KAAK,CAACxC,uBAAuB,cAAAgF,sBAAA,uBAAnDA,sBAAA,CAAqD/E,MAAM;MAC9E,IAAIsC,UAAU,EAAE;QACf,MAAME,UAAU,GAAGjD,cAAc,CAAC+C,UAAU,CAAC;QAC7C,MAAM0C,0BAA0B,GAAGC,cAAc,CAACzC,UAAU,EAAE/B,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEzB,SAAS,CAAC;QACrG6F,OAAO,CAACrC,UAAU,CAAC;;QAEnB;QACA,MAAM0C,iBAAiB,GAAG;UACzB,GAAGzE,2BAA2B;UAC9BM,IAAI,EAAEyB;QACP,CAAC;;QAED;QACA9B,8BAA8B,CAACwE,iBAAiB,CAAC;;QAEjD;QACA;QACAnD,eAAe,CAACmD,iBAAiB,CAAC;;QAElC;QACA9F,uBAAuB,CAAC8F,iBAAiB,CAAC;MAC3C;IACD;EACD,CAAC,EAAE,EAAE,CAAC;EAEL,MAAMD,cAAc,GAAGA,CAACE,SAAc,EAAEC,KAAU,KAAK;IACtD,IAAI,CAACD,SAAS,EAAE;MACf,OAAO,EAAE;IACV;IAEA,IAAI;MACH;MACA,IAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QACtD,OAAOF,SAAS,CAAC,CAAC;MACnB;MAEA,MAAMG,UAAU,GAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhD;MACA,MAAMC,SAAS,GAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC;MACjD,MAAMK,cAAc,GAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC;MAE/D,IAAIM,WAAW,GAAGN,UAAU;MAE5B,IAAIG,SAAS,IAAI,CAACC,cAAc,EAAE;QACjC;QACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,EAAE,WAAWT,KAAK,GAAG,CAAC;MAC1E,CAAC,MAAM,IAAIM,cAAc,EAAE;QAC1B;QACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,EAAE,SAAST,KAAK,GAAG,CAAC;MAC9E,CAAC,MAAM;QACN;QACAQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,EAAE,eAAeT,KAAK,GAAG,CAAC;QAClFQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,EAAE,cAAcT,KAAK,GAAG,CAAC;MACjF;MAEA,MAAMU,cAAc,GAAG,6BAA6BrG,IAAI,CAACmG,WAAW,CAAC,EAAE;MACvE,OAAOE,cAAc;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACf;MACA,OAAOQ,SAAS,CAAC,CAAC;IACnB;EACD,CAAC;EACA,MAAMY,eAAe,GAAG,MAAOnG,EAAU,IAAK;IAC7CD,QAAQ,CAAE2D,SAAS,IAClBA,SAAS,CAACC,GAAG,CAAExC,IAAI,KAAM;MACxB,GAAGA,IAAI;MACPP,QAAQ,EAAEO,IAAI,CAACnB,EAAE,KAAKA;IACvB,CAAC,CAAC,CACH,CAAC;IAED,MAAMoG,YAAY,GAAGtG,KAAK,CAACuG,IAAI,CAAElF,IAAI,IAAKA,IAAI,CAACnB,EAAE,KAAKA,EAAE,CAAC;IACzD,IAAIoG,YAAY,EAAE;MAAA,IAAAE,qBAAA;MACjB,MAAM5D,UAAU,IAAA4D,qBAAA,GAAGF,YAAY,CAAClG,SAAS,CAACyC,KAAK,CAACxC,uBAAuB,cAAAmG,qBAAA,uBAApDA,qBAAA,CAAsDlG,MAAM;MAC/E,IAAIsC,UAAU,EAAE;QACf,MAAME,UAAU,GAAGsC,WAAW,CAACxC,UAAU,CAAC;QAC1C,MAAM0C,0BAA0B,GAACC,cAAc,CAACzC,UAAU,EAAE/B,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEzB,SAAS,CAAC;QACnG6F,OAAO,CAACrC,UAAU,CAAC;QAGnB9B,8BAA8B,CAAEuD,SAAa,KAAM;UAClD,GAAGA,SAAS;UAAE;UACdlD,IAAI,EAAEyB,UAAU,CAAE;QACnB,CAAC,CAAC,CAAC;QACHX,aAAa,CAAC,IAAI,CAAC;MACpB;IACD;EACD,CAAC;EAED,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAG5J,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6J,OAAO,EAAEC,UAAU,CAAC,GAAG9J,QAAQ,CAACgB,SAAS,CAAC;EACjD,MAAM,CAAC+I,OAAO,EAAEC,UAAU,CAAC,GAAGhK,QAAQ,CAACiB,SAAS,CAAC;EAEjD,MAAMgJ,kBAAkB,GAAGA,CAAA,KAAM;IAClC/F,8BAA8B,CAAEgG,IAAQ,KAAM;MAC5C,GAAGA,IAAI;MACPxF,gBAAgB,EAAE;QACnB,GAAGwF,IAAI,CAACxF,gBAAgB;QACxBC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE;MACN;IACF,CAAC,CAAC,CAAC;IACHS,aAAa,CAAC,IAAI,CAAC;IACnByE,UAAU,CAAC5I,SAAS,CAAC;IACrB8I,UAAU,CAAC7I,SAAS,CAAC;EACrB,CAAC;EACDlB,SAAS,CAAC,MAAM;IACf,MAAMkK,QAAQ,GAAGlG,2BAA2B,CAACS,gBAAgB;IAC7D,IAAIyF,QAAQ,CAACxF,IAAI,EAAE;MACjBmF,UAAU,CAAC5I,SAAS,CAAC;MACrB8I,UAAU,CAAC7I,SAAS,CAAC;IACvB,CAAC,MAAM,IAAIgJ,QAAQ,CAACvF,KAAK,EAAE;MACzBkF,UAAU,CAAC9I,SAAS,CAAC;MACrBgJ,UAAU,CAAC/I,SAAS,CAAC;IACvB;EACC,CAAC,EAAE,CAACgD,2BAA2B,CAACS,gBAAgB,CAAC,CAAC;EAGnD,MAAM0F,mBAAmB,GAAGA,CAAA,KAAM;IACnClG,8BAA8B,CAAEgG,IAAQ,KAAM;MAC7C,GAAGA,IAAI;MACPxF,gBAAgB,EAAE;QAChB,GAAGwF,IAAI,CAACxF,gBAAgB;QACxBC,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE;MACT;IACC,CAAC,CAAC,CAAC;IACLS,aAAa,CAAC,IAAI,CAAC;IACnByE,UAAU,CAAC9I,SAAS,CAAC;IAClBgJ,UAAU,CAAC/I,SAAS,CAAC;EACvB,CAAC;EAIF,oBACCM,OAAA;IACC6B,EAAE,EAAC,mBAAmB;IACtBiH,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7B/I,OAAA;MAAK8I,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7B/I,OAAA;QAAK8I,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC/I,OAAA,CAAClB,UAAU;UACV,cAAY2B,SAAS,CAAC,MAAM,EAAE;YAAEuI,YAAY,EAAE;UAAO,CAAC,CAAE;UACxDC,OAAO,EAAExD,WAAY;UAAAsD,QAAA,eAErB/I,OAAA,CAACH,2BAA2B;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbxC,OAAA;UAAK8I,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEtI,SAAS,CAAC,UAAU,EAAE;YAAEuI,YAAY,EAAE;UAAW,CAAC;QAAC;UAAA3G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxFxC,OAAA,CAAClB,UAAU;UACVoK,IAAI,EAAC,OAAO;UACZ,cAAYzI,SAAS,CAAC,OAAO,EAAE;YAAEuI,YAAY,EAAE;UAAQ,CAAC,CAAE;UAC1DC,OAAO,EAAExD,WAAY;UAAAsD,QAAA,eAErB/I,OAAA,CAACf,SAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNxC,OAAA;QAAK8I,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9B/I,OAAA;UAAK8I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE9B/I,OAAA;YAAK8I,SAAS,EAAC,mBAAmB;YACtB5G,KAAK,EAAE;cAACiH,MAAM,EAAE,MAAM;cAAEC,aAAa,EAAC,QAAQ;cAAEC,OAAO,EAAC;YAAG,CAAE;YAAAN,QAAA,gBACxE/I,OAAA;cAAK8I,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEtI,SAAS,CAAC,MAAM,EAAE;gBAAEuI,YAAY,EAAE;cAAO,CAAC;YAAC;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxFxC,OAAA;cAAKkC,KAAK,EAAE;gBAAEE,OAAO,EAAC,cAAc;gBAACkH,GAAG,EAAC,KAAK;gBAACD,OAAO,EAAC,eAAe;gBAACE,SAAS,EAAC;cAAM,CACtF;cAAAR,QAAA,gBACA/I,OAAA;gBACC8I,SAAS,EAAE,qBAAqBpG,2BAA2B,CAACK,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAChGkG,OAAO,EAAEA,CAAA,KAAMvC,gBAAgB,CAAC,MAAM,CAAE;gBAAAqC,QAAA,EAEvCtI,SAAS,CAAC,MAAM,EAAE;kBAAEuI,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACTxC,OAAA;gBACC8I,SAAS,EAAE,qBAAqBpG,2BAA2B,CAACK,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAChGkG,OAAO,EAAEA,CAAA,KAAMvC,gBAAgB,CAAC,MAAM,CAAE;gBAAAqC,QAAA,EAEvCtI,SAAS,CAAC,MAAM,EAAE;kBAAEuI,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACTxC,OAAA;gBACC8I,SAAS,EAAE,qBAAqBpG,2BAA2B,CAACK,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;gBACpGkG,OAAO,EAAEA,CAAA,KAAMvC,gBAAgB,CAAC,UAAU,CAAE;gBAAAqC,QAAA,EAE3CtI,SAAS,CAAC,WAAW,EAAE;kBAAEuI,YAAY,EAAE;gBAAY,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGF,CAAC,EAELE,2BAA2B,CAACK,IAAI,KAAK,MAAM,iBAC3C/C,OAAA,CAAAE,SAAA;YAAA6I,QAAA,gBAEE/I,OAAA,CAACrB,GAAG;cACKkD,EAAE,EAAC,mBAAmB;cACtBiH,SAAS,EAAC,mBAAmB;cAC7BU,EAAE,EAAE;gBAAEJ,aAAa,EAAE,QAAQ;gBAAED,MAAM,EAAE;cAAkB,CAAE;cAAAJ,QAAA,gBAEpE/I,OAAA;gBAAK8I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAGtI,SAAS,CAAC,MAAM,EAAE;kBAAEuI,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAEhFxC,OAAA,CAACnB,SAAS;gBACR4K,OAAO,EAAC,UAAU;gBAClBP,IAAI,EAAC,OAAO;gBACtBQ,WAAW,EAAEjJ,SAAS,CAAC,YAAY,EAAE;kBAAEuI,YAAY,EAAE;gBAAa,CAAC,CAAE;gBAC3DF,SAAS,EAAC,qBAAqB;gBAC/B5G,KAAK,EAAE;kBAACyH,KAAK,EAAE,mBAAmB;kBAACN,OAAO,EAAE,eAAe;kBAAEO,MAAM,EAAC;gBAAG,CAAE;gBACzEtE,KAAK,EAAE5C,2BAA2B,CAACO,IAAK;gBACxC4G,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,MAAM,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC1DsB,KAAK,EAAEkD,OAAO,CAACnF,SAAS,CAAE;gBAC1BoF,UAAU,EAAEpF,SAAU;gBACtBqF,UAAU,EAAE;kBACXC,YAAY,EAAE,EAAE;kBAChBT,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEU,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM,CAAC;oBAC5B,SAAS,EAAE;sBAAEX,SAAS,EAAE,iBAAiB;sBAAEY,WAAW,EAAC;oBAAiB,CAAC;oBACzE,qBAAqB,EAAC;sBAAChB,MAAM,EAAC;oBAAiB;kBAChD;gBACD;cAAE;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEdxC,OAAA,CAACrB,GAAG;cAACmK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC/I,OAAA,CAACpB,UAAU;gBAACkK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtI,SAAS,CAAC,YAAY,EAAE;kBAAEuI,YAAY,EAAE;gBAAa,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpHxC,OAAA;gBACC+C,IAAI,EAAC,OAAO;gBACZuC,KAAK,EAAE5C,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEQ,SAAU;gBAC9C2G,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,WAAW,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC/DwD,SAAS,EAAC;cAAmB;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,eACF,CACH,EAGAE,2BAA2B,CAACK,IAAI,KAAK,MAAM,iBAC3C/C,OAAA,CAAAE,SAAA;YAAA6I,QAAA,gBAEM/I,OAAA,CAACrB,GAAG;cAACkD,EAAE,EAAC,mBAAmB;cAACiH,SAAS,EAAC,mBAAmB;cAACU,EAAE,EAAE;gBAAEJ,aAAa,EAAE,QAAQ;gBAAED,MAAM,EAAE,iBAAiB;gBAACE,OAAO,EAAC;cAAe,CAAE;cAAAN,QAAA,gBAChJ/I,OAAA,CAACpB,UAAU;gBAACkK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtI,SAAS,CAAC,MAAM,EAAE;kBAAEuI,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrGxC,OAAA,CAACrB,GAAG;gBAAC6K,EAAE,EAAE;kBAAEpH,OAAO,EAAE,MAAM;kBAAEkH,GAAG,EAAE,CAAC;kBAAEc,UAAU,EAAE,QAAQ;kBAAET,KAAK,EAAE,wBAAwB;kBAAEU,QAAQ,EAAE,MAAM;kBAAEhB,OAAO,EAAE;gBAAgB,CAAE;gBAAAN,QAAA,EACvIpH,KAAK,CAAC6D,GAAG,CAACxC,IAAI,iBACdhD,OAAA,CAAChB,OAAO;kBAAesL,KAAK,EAAE7J,SAAS,CAAC,aAAa,EAAE;oBAAEuI,YAAY,EAAE;kBAAc,CAAC,CAAE;kBAAAD,QAAA,eACzF/I,OAAA,CAAClB,UAAU;oBACVmK,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAAChF,IAAI,CAACnB,EAAE,CAAE;oBACxC2H,EAAE,EAAE;sBACHU,MAAM,EAAElH,IAAI,CAACP,QAAQ,GAAG,+BAA+B,GAAG,MAAM;sBAC5D8H,YAAY,EAAE,KAAK;sBACnBlB,OAAO,EAAE,KAAK;sBACdmB,UAAU,EAAC;oBAChB,CAAE;oBAAAzB,QAAA,EAED/F,IAAI,CAACjB;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GAXEQ,IAAI,CAACnB,EAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYd,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGLxC,OAAA,CAACrB,GAAG;cAACmK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAClC/I,OAAA,CAACpB,UAAU;gBAACkK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtI,SAAS,CAAC,YAAY,EAAE;kBAAEuI,YAAY,EAAE;gBAAa,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpHxC,OAAA;gBACC+C,IAAI,EAAC,OAAO;gBACZuC,KAAK,EAAE5C,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEzB,SAAU;gBAC9C4I,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,WAAW,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC7DwD,SAAS,EAAC,mBAAmB;gBAC7B5G,KAAK,EAAE;kBAACuI,eAAe,EAAC;gBAAM;cAAE;gBAAApI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAElC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,eAIF,CAEH,EAGAE,2BAA2B,CAACK,IAAI,KAAK,UAAU,iBAC/C/C,OAAA,CAAAE,SAAA;YAAA6I,QAAA,gBAEC/I,OAAA,CAACrB,GAAG;cACMkD,EAAE,EAAC,mBAAmB;cACtBiH,SAAS,EAAC,mBAAmB;cAC7BU,EAAE,EAAE;gBAAEJ,aAAa,EAAE,QAAQ;gBAAED,MAAM,EAAE;cAAkB,CAAE;cAAAJ,QAAA,gBAEpE/I,OAAA;gBAAK8I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAGtI,SAAS,CAAC,MAAM,EAAE;kBAAEuI,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAEhFxC,OAAA,CAACnB,SAAS;gBACR4K,OAAO,EAAC,UAAU;gBAClBP,IAAI,EAAC,OAAO;gBACtBQ,WAAW,EAAEjJ,SAAS,CAAC,YAAY,EAAE;kBAAEuI,YAAY,EAAE;gBAAa,CAAC,CAAE;gBAC3DF,SAAS,EAAC,qBAAqB;gBAC/B5G,KAAK,EAAE;kBAACyH,KAAK,EAAE,mBAAmB;kBAACN,OAAO,EAAE,eAAe;kBAAEO,MAAM,EAAC;gBAAG,CAAE;gBACzEtE,KAAK,EAAE5C,2BAA2B,CAACO,IAAK;gBACxC4G,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,MAAM,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC1DsB,KAAK,EAAEkD,OAAO,CAACnF,SAAS,CAAE;gBAC1BoF,UAAU,EAAEpF,SAAU;gBACtBqF,UAAU,EAAE;kBACXC,YAAY,EAAE,EAAE;kBAChBT,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEU,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM,CAAC;oBAC5B,SAAS,EAAE;sBAAEX,SAAS,EAAE,iBAAiB;sBAAEY,WAAW,EAAC;oBAAiB,CAAC;oBACzE,qBAAqB,EAAC;sBAAChB,MAAM,EAAC;oBAAiB;kBAChD;gBACD;cAAE;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEdxC,OAAA,CAACrB,GAAG;cAACkD,EAAE,EAAC,mBAAmB;cAACiH,SAAS,EAAC,mBAAmB;cAACU,EAAE,EAAE;gBAAEJ,aAAa,EAAE,QAAQ;gBAAED,MAAM,EAAE;cAAkB,CAAE;cAAAJ,QAAA,gBACpH/I,OAAA;gBAAK8I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAGtI,SAAS,CAAC,MAAM,EAAE;kBAAEuI,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxFxC,OAAA,CAACrB,GAAG;gBAAC6K,EAAE,EAAE;kBAAEpH,OAAO,EAAE,MAAM;kBAAEkH,GAAG,EAAE,CAAC;kBAAEc,UAAU,EAAE,QAAQ;kBAAET,KAAK,EAAE,wBAAwB;kBAAEU,QAAQ,EAAE,MAAM;kBAAEhB,OAAO,EAAE;gBAAgB,CAAE;gBAAAN,QAAA,EACvIpH,KAAK,CAAC6D,GAAG,CAACxC,IAAI,iBACdhD,OAAA,CAAChB,OAAO;kBAAesL,KAAK,EAAE7J,SAAS,CAAC,aAAa,EAAE;oBAAEuI,YAAY,EAAE;kBAAc,CAAC,CAAE;kBAAAD,QAAA,eACzF/I,OAAA,CAAClB,UAAU;oBACVmK,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAAChF,IAAI,CAACnB,EAAE,CAAE;oBACxC2H,EAAE,EAAE;sBACHU,MAAM,EAAElH,IAAI,CAACP,QAAQ,GAAG,+BAA+B,GAAG,MAAM;sBAC5D8H,YAAY,EAAE,KAAK;sBACnBlB,OAAO,EAAE,KAAK;sBACdmB,UAAU,EAAC;oBAChB,CAAE;oBAAAzB,QAAA,EAEA/F,IAAI,CAACjB;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GAXCQ,IAAI,CAACnB,EAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYb,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGD,CAAC,eAGNxC,OAAA,CAACrB,GAAG;cAACmK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC/I,OAAA,CAACpB,UAAU;gBAACkK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtI,SAAS,CAAC,YAAY,EAAE;kBAAEuI,YAAY,EAAE;gBAAa,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpHxC,OAAA;gBACC+C,IAAI,EAAC,OAAO;gBACZuC,KAAK,EAAE5C,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEzB,SAAU;gBAC9C4I,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,WAAW,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC7DwD,SAAS,EAAC,mBAAmB;gBAC7B5G,KAAK,EAAE;kBAACuI,eAAe,EAAC;gBAAM;cAAE;gBAAApI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAElC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACNxC,OAAA,CAACrB,GAAG;cAACmK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC/I,OAAA,CAACpB,UAAU;gBAACkK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtI,SAAS,CAAC,YAAY,EAAE;kBAAEuI,YAAY,EAAE;gBAAa,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpHxC,OAAA;gBACC+C,IAAI,EAAC,OAAO;gBACZuC,KAAK,EAAE5C,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEQ,SAAU;gBAC9C2G,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,WAAW,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC/DwD,SAAS,EAAC;cAAmB;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,eACH,CACF,eAKDxC,OAAA,CAACrB,GAAG;YAACmK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjC/I,OAAA;cAAK8I,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEtI,SAAS,CAAC,gBAAgB,EAAE;gBAAEuI,YAAY,EAAE;cAAiB,CAAC;YAAC;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5GxC,OAAA;cAAA+I,QAAA,eACA/I,OAAA;gBACC+C,IAAI,EAAC,OAAO;gBACZuC,KAAK,EAAE5C,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAE3B,aAAc;gBAClD8I,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,eAAe,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAClEwD,SAAS,EAAC,mBAAmB;gBAC7B5G,KAAK,EAAE;kBAACuI,eAAe,EAAC;gBAAS;cAAE;gBAAApI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAINxC,OAAA,CAACrB,GAAG;YAACmK,SAAS,EAAC,mBAAmB;YAACU,EAAE,EAAE;cAACL,MAAM,EAAC,iBAAiB;cAACC,aAAa,EAAC,mBAAmB;cAACC,OAAO,EAAC;YAAc,CAAE;YAAAN,QAAA,gBAC1H/I,OAAA;cAAK8I,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEtI,SAAS,CAAC,UAAU,EAAE;gBAAEuI,YAAY,EAAE;cAAW,CAAC;YAAC;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChGxC,OAAA;cAAKkC,KAAK,EAAE;gBAAKmH,OAAO,EAAE,eAAe;gBAACjH,OAAO,EAAE,MAAM;gBAACkH,GAAG,EAAE,KAAK;gBAACoB,MAAM,EAAC;cAAS,CAAE;cAAA3B,QAAA,gBAEtF/I,OAAA;gBAAMgC,uBAAuB,EAAE;kBAAEC,MAAM,EAAEqG;gBAAQ,CAAE;gBAACW,OAAO,EAAEP,kBAAmB;gBAACxG,KAAK,EAAE;kBAAEC,IAAI,EAAE;gBAAO;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE5GxC,OAAA;gBAAMgC,uBAAuB,EAAE;kBAAEC,MAAM,EAAGuG;gBAAO,CAAE;gBAACS,OAAO,EAAEJ,mBAAoB;gBAAC3G,KAAK,EAAE;kBAAEC,IAAI,EAAE;gBAAO;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC,eACNxC,OAAA;cAAKkC,KAAK,EAAE;gBAACE,OAAO,EAAG,MAAM;gBAAEgI,UAAU,EAAG,QAAQ;gBAACO,cAAc,EAAG,eAAe;gBAACC,YAAY,EAAG;cAAK,CAAE;cAAA7B,QAAA,gBAC3G/I,OAAA;gBAAM8I,SAAS,EAAC,qBAAqB;gBAAC5G,KAAK,EAAE;kBAAC2I,UAAU,EAAE,QAAQ;kBACjEC,SAAS,EAAE;gBACZ,CAAE;gBAAA/B,QAAA,GAAC,IAAE,EAACtI,SAAS,CAAC,aAAa,EAAE;kBAAEuI,YAAY,EAAE;gBAAgB,CAAC,CAAC;cAAA;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1ExC,OAAA;gBAAM8I,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eACjC/I,OAAA,CAACnB,SAAS;kBACT4K,OAAO,EAAC,UAAU;kBAClBnE,KAAK,EAAE,EAAAhF,qBAAA,GAAAoC,2BAA2B,CAACS,gBAAgB,cAAA7C,qBAAA,uBAA5CA,qBAAA,CAA8CgD,WAAW,KAAI,IAAK;kBAEzE4F,IAAI,EAAC,OAAO;kBACZJ,SAAS,EAAC,qBAAqB;kBAC/Be,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,aAAa,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBACjE0E,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBT,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEU,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAE;wBAAEA,MAAM,EAAE;sBAAO;oBAEhC;kBACD;gBAAE;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAED;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNxC,OAAA;cAAKkC,KAAK,EAAE;gBAACE,OAAO,EAAG,MAAM;gBAAEgI,UAAU,EAAG,QAAQ;gBAACO,cAAc,EAAG,eAAe;gBAACC,YAAY,EAAG;cAAK,CAAE;cAAA7B,QAAA,gBAC1G/I,OAAA;gBAAK8I,SAAS,EAAC,qBAAqB;gBAAC5G,KAAK,EAAE;kBAAC2I,UAAU,EAAE,QAAQ;kBAChEC,SAAS,EAAE;gBACZ,CAAE;gBAAA/B,QAAA,GAAC,IAAE,EAACtI,SAAS,CAAC,aAAa,EAAE;kBAAEuI,YAAY,EAAE;gBAAgB,CAAC,CAAC;cAAA;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzExC,OAAA;gBAAK8I,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAChC/I,OAAA,CAACnB,SAAS;kBACT4K,OAAO,EAAC,UAAU;kBAClBnE,KAAK,EAAE,EAAA/E,sBAAA,GAAAmC,2BAA2B,CAACS,gBAAgB,cAAA5C,sBAAA,uBAA5CA,sBAAA,CAA8CgD,WAAW,KAAI,IAAK;kBAEzE2F,IAAI,EAAC,OAAO;kBACZJ,SAAS,EAAC,qBAAqB;kBAC/Be,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,aAAa,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBACjE0E,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBT,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEU,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAE;wBAAEA,MAAM,EAAE;sBAAO;oBAEhC;kBACD;gBAAE;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAED;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAERxC,OAAA,CAACrB,GAAG;YAACmK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjC/I,OAAA;cAAK8I,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEtI,SAAS,CAAC,oBAAoB,EAAE;gBAAEuI,YAAY,EAAE;cAAqB,CAAC;YAAC;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAG3HxC,OAAA;cAAA+I,QAAA,eACO/I,OAAA;gBAAO8I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC/B/I,OAAA;kBACC+C,IAAI,EAAC,UAAU;kBACfgI,OAAO,EAAErI,2BAA2B,CAACc,iBAAkB;kBACvDqG,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,mBAAmB,EAAET,CAAC,CAACC,MAAM,CAAC0F,OAAO,CAAE;kBAEzEC,IAAI,EAAC;gBAAe;kBAAA3I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACFxC,OAAA;kBAAM8I,SAAS,EAAC;gBAAQ;kBAAAzG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAELE,2BAA2B,CAACc,iBAAiB,iBAC7CxD,OAAA,CAAAE,SAAA;YAAA6I,QAAA,gBACC/I,OAAA,CAACrB,GAAG;cAACmK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC/I,OAAA;gBAAK8I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtI,SAAS,CAAC,aAAa,EAAE;kBAAEuI,YAAY,EAAE;gBAAc,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtGxC,OAAA;gBAAA+I,QAAA,eACA/I,OAAA;kBACC+C,IAAI,EAAC,OAAO;kBACZuC,KAAK,EAAE5C,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEe,sBAAuB;kBAC3DoG,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,wBAAwB,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBAC3EwD,SAAS,EAAC,mBAAmB;kBAC7B5G,KAAK,EAAE;oBAACuI,eAAe,EAAC;kBAAK;gBAAE;kBAAApI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxC,OAAA,CAACrB,GAAG;cAACmK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC/I,OAAA;gBAAK8I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtI,SAAS,CAAC,YAAY,EAAE;kBAAEuI,YAAY,EAAE;gBAAa,CAAC;cAAC;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpGxC,OAAA;gBAAA+I,QAAA,eACA/I,OAAA;kBACC+C,IAAI,EAAC,OAAO;kBACZuC,KAAK,EAAE5C,2BAA2B,aAA3BA,2BAA2B,uBAA3BA,2BAA2B,CAAEgB,qBAAsB;kBAC1DmG,QAAQ,EAAGzE,CAAC,IAAKS,gBAAgB,CAAC,uBAAuB,EAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBAC3EwD,SAAS,EAAC;gBAAmB;kBAAAzG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACL,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKF,CAAC,eACNxC,OAAA;QAAK8I,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClC/I,OAAA,CAACjB,MAAM;UACN0K,OAAO,EAAC,WAAW;UACnBR,OAAO,EAAE3C,kBAAmB;UAC5BwC,SAAS,EAAE,aAAanF,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UACvDsH,QAAQ,EAAEtH,UAAW;UAAAoF,QAAA,EAEpBtI,SAAS,CAAC,OAAO,EAAE;YAAEuI,YAAY,EAAE;UAAQ,CAAC;QAAC;UAAA3G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAACnC,EAAA,CApuBIF,gBAAgB;EAAA,QACIL,cAAc,EAkBnCZ,cAAc;AAAA;AAAAgM,EAAA,GAnBb/K,gBAAgB;AAsuBtB,eAAeA,gBAAgB;AAAC,IAAA+K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}