{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\ImageSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\nimport { useTranslation } from \"react-i18next\";\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, copyicon, deleteicon, sectionheight, Settings, CrossIcon } from \"../../../assets/icons/icons\";\nimport useDrawerStore, { IMG_CONTAINER_DEFAULT_HEIGHT, IMG_CONTAINER_MAX_HEIGHT, IMG_CONTAINER_MIN_HEIGHT, IMG_OBJECT_FIT, IMG_STEP_VALUE } from \"../../../store/drawerStore\";\nimport { ChromePicker } from \"react-color\";\nimport \"../../guideSetting/PopupSections/PopupSections.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSection = ({\n  items: imagesContainer,\n  isCloneDisabled\n}) => {\n  _s();\n  var _document$getElementB3, _document$getElementB4, _document$getElementB5, _document$getElementB6, _useDrawerStore$, _useDrawerStore$$cont, _useDrawerStore$2, _useDrawerStore$2$con;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    uploadTooltipImage: uploadImage,\n    imageAnchorEl,\n    setImageAnchorEl,\n    replaceTooltipImage: replaceImage,\n    cloneTooltipImage: cloneImageContainer,\n    deleteTooltipImageContainer: deleteImageContainer,\n    updateTooltipImageContainer: updateImageContainer,\n    // TODO\n    toggleTooltipImageFit: toggleFit,\n    //TODO\n    setImageSrc: storeImageSrc,\n    tooltip\n  } = useDrawerStore(state => state);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('info');\n  const [snackbarKey, setSnackbarKey] = useState(0);\n  const openSnackbar = () => {\n    setSnackbarKey(prev => prev + 1);\n    setSnackbarOpen(true);\n  };\n  const closeSnackbar = () => {\n    setSnackbarOpen(false);\n  };\n  const [showHyperlinkInput, setShowHyperlinkInput] = useState({\n    currentContainerId: \"\",\n    isOpen: false\n  });\n  const [imageLink, setImageLink] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState({\n    currentContainerId: \"\",\n    isImage: false,\n    height: IMG_CONTAINER_DEFAULT_HEIGHT\n  });\n  const [selectedAction, setSelectedAction] = useState(\"none\");\n  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"\");\n  const [isModelOpen, setModelOpen] = useState(false);\n  const [formOfUpload, setFormOfUpload] = useState(\"\");\n  const [isReplaceImage, setReplaceImage] = useState(false);\n  const openSettingsPopover = Boolean(settingsAnchorEl);\n  const handleActionChange = event => {\n    setSelectedAction(event.target.value);\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchorEl(event.currentTarget);\n  };\n  const handleCloseSettingsPopover = () => {\n    setSettingsAnchorEl(null);\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: `${imagesContainer.style.height}px`,\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\",\n    marginTop: \"10px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"100%\"\n  };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    let urll;\n    if (file) {\n      const parts = file.name.split('.');\n      const extension = parts.pop();\n      // Check for double extensions (e.g. file.html.png) or missing/invalid extension\n      if (parts.length > 1 || !extension) {\n        setSnackbarMessage(\"Uploaded file name should not contain any special character\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      if (file.name.length > 128) {\n        setSnackbarMessage(\"File name should not exceed 128 characters\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      // setImageName(event.target.files?.[0].name);\n\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        const base64Image = reader.result;\n        storeImageSrc(base64Image);\n        // urll = base64Image;\n        uploadImage(imagesContainer.id, {\n          altText: file.name,\n          id: crypto.randomUUID(),\n          url: base64Image,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleImageUploadFormApp = file => {\n    if (file) {\n      storeImageSrc(file.Url);\n      if (isReplaceImage) {\n        replaceImage(imagesContainer.id, {\n          altText: file.FileName,\n          url: file.Url,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n        setReplaceImage(false);\n      } else {\n        uploadImage(imagesContainer.id, {\n          altText: file.FileName,\n          id: crypto.randomUUID(),\n          // Use existing ID\n          url: file.Url,\n          // Directly use the URL\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      }\n    }\n    setModelOpen(false);\n  };\n  const handleReplaceImage = event => {\n    var _event$target$files2;\n    const file = (_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        replaceImage(imagesContainer.id, {\n          altText: file.name,\n          url: reader.result,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = (event, containerId, imageId, isImage, currentHeight) => {\n    // @ts-ignore\n    if ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\n    setImageAnchorEl({\n      buttonId: imageId,\n      containerId: containerId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    setSettingsAnchorEl(null);\n    setCurrentImageSectionInfo({\n      currentContainerId: containerId,\n      isImage,\n      height: currentHeight\n    });\n    setShowHyperlinkInput({\n      currentContainerId: \"\",\n      isOpen: false\n    });\n  };\n  const handleClose = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n    setSettingsAnchorEl(null);\n  };\n  const open = Boolean(imageAnchorEl.value && imagesContainer.id === imageAnchorEl.containerId);\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = prevHeight => {\n    if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\n    const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\n    updateImageContainer(imagesContainer.id, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const handleDecreaseHeight = prevHeight => {\n    if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\n    const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\n    updateImageContainer(imagesContainer.id, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    // setReplaceImage(true);\n    // setModelOpen(true);\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n  };\n\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n    setSettingsAnchorEl(null);\n    deleteImageContainer(imagesContainer.id);\n  };\n  const handleHyperlinkClick = (cId, isOpen) => {\n    setShowHyperlinkInput({\n      currentContainerId: cId,\n      isOpen\n    });\n  };\n  const handleLinkSubmit = event => {\n    if (event.key === \"Enter\" && imageLink) {\n      uploadImage(imageAnchorEl.containerId, {\n        altText: \"New Image\",\n        id: crypto.randomUUID(),\n        url: imageLink,\n        backgroundColor: \"transparent\",\n        objectFit: IMG_OBJECT_FIT\n      });\n      setShowHyperlinkInput({\n        currentContainerId: \"\",\n        isOpen: false\n      });\n    }\n  };\n  const handleCloneImgContainer = () => {\n    cloneImageContainer(imagesContainer.id);\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    updateImageContainer(imagesContainer.id, \"style\", {\n      backgroundColor: color.hex\n    });\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n\n  // console.log({ imageAnchorEl, tooltip });\n\n  useEffect(() => {\n    if (!tooltip.visible) {\n      setImageAnchorEl({\n        buttonId: \"\",\n        containerId: \"\",\n        // @ts-ignore\n        value: null\n      });\n      setSettingsAnchorEl(null);\n    }\n  }, [tooltip.visible]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box\n    // key={id}\n    , {\n      sx: {\n        width: \"100%\",\n        height: \"100%\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"flex-start\",\n        alignItems: \"center\",\n        // padding: \"5px\",\n        margin: \"0px\",\n        overflow: \"auto\"\n      },\n      children: !imagesContainer.images.length ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          ...imageContainerStyle,\n          backgroundColor: imagesContainer.style.backgroundColor,\n          height: `${imagesContainer.style.height}px`,\n          textAlign: \"center\",\n          width: \"100%\",\n          // height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"center\"\n        },\n        onClick: e => {\n          var _e$target;\n          // prevent to open toolbar when upload file clicked\n          // @ts-ignore\n          if (!((_e$target = e.target) !== null && _e$target !== void 0 && _e$target.id.startsWith(\"file-upload\"))) {\n            var _imagesContainer$styl;\n            handleClick(e, imagesContainer.id, \"\", false, (imagesContainer === null || imagesContainer === void 0 ? void 0 : (_imagesContainer$styl = imagesContainer.style) === null || _imagesContainer$styl === void 0 ? void 0 : _imagesContainer$styl.height) || IMG_CONTAINER_DEFAULT_HEIGHT);\n          }\n        },\n        id: imagesContainer.id,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: iconTextStyle,\n          component: \"div\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: uploadfile\n            },\n            style: {\n              display: \"inline-block\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            align: \"center\",\n            color: \"textSecondary\",\n            sx: {\n              fontSize: \"14px\",\n              fontWeight: \"600\"\n            },\n            children: translate(\"Upload file\", {\n              defaultValue: \"Upload file\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          align: \"center\",\n          color: \"textSecondary\",\n          sx: {\n            fontSize: \"14px\"\n          },\n          children: translate(\"Drag & Drop to upload file\", {\n            defaultValue: \"Drag & Drop to upload file\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          align: \"center\",\n          color: \"textSecondary\",\n          sx: {\n            marginTop: \"8px\",\n            fontSize: \"14px\"\n          },\n          children: translate(\"Or\", {\n            defaultValue: \"Or\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 7\n        }, this), showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? /*#__PURE__*/_jsxDEV(TextField, {\n          value: imageLink,\n          onChange: e => setImageLink(e.target.value),\n          onKeyDown: handleLinkSubmit,\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 8\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: iconRowStyle,\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Coming soon\", {\n              defaultValue: \"Coming soon\"\n            }),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                pointerEvents: \"auto\",\n                cursor: \"pointer\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: hyperlink\n                },\n                style: {\n                  color: \"black\",\n                  cursor: \"pointer\",\n                  fontSize: \"32px\",\n                  opacity: \"0.5\",\n                  pointerEvents: \"none\"\n                },\n                id: \"hyperlink\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 10\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Coming soon\", {\n              defaultValue: \"Coming soon\"\n            }),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: () => {\n                //setModelOpen(true);\n              },\n              dangerouslySetInnerHTML: {\n                __html: files\n              },\n              style: {\n                color: \"black\",\n                cursor: \"pointer\",\n                fontSize: \"32px\",\n                opacity: \"0.5\"\n              },\n              id: \"folder\"\n              //title=\"Coming Soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 10\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Upload File\", {\n              defaultValue: \"Upload File\"\n            }),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: event => {\n                var _document$getElementB2;\n                event === null || event === void 0 ? void 0 : event.stopPropagation();\n                (_document$getElementB2 = document.getElementById(`file-upload-${imagesContainer.id}`)) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n              },\n              id: \"file-upload1\",\n              dangerouslySetInnerHTML: {\n                __html: uploadicon\n              },\n              style: {\n                color: \"black\",\n                cursor: \"pointer\",\n                fontSize: \"32px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 10\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            id: `file-upload-${imagesContainer.id}`,\n            style: {\n              display: \"none\"\n            },\n            accept: \"image/*\",\n            onChange: handleImageUpload\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n            open: snackbarOpen,\n            autoHideDuration: 3000,\n            onClose: closeSnackbar,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              onClose: closeSnackbar,\n              severity: snackbarSeverity,\n              sx: {\n                width: '100%'\n              },\n              children: snackbarMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 6\n      }, this) : imagesContainer.images.map(item => {\n        var _imagesContainer$styl2;\n        const imageSrc = item === null || item === void 0 ? void 0 : item.url;\n        const imageId = item === null || item === void 0 ? void 0 : item.id;\n        const objectFit = (item === null || item === void 0 ? void 0 : item.objectFit) || IMG_OBJECT_FIT;\n        const currentSecHeight = (imagesContainer === null || imagesContainer === void 0 ? void 0 : (_imagesContainer$styl2 = imagesContainer.style) === null || _imagesContainer$styl2 === void 0 ? void 0 : _imagesContainer$styl2.height) || IMG_CONTAINER_DEFAULT_HEIGHT;\n        const id = imagesContainer.id;\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...imageContainerStyle,\n            backgroundColor: imagesContainer.style.backgroundColor,\n            height: `${imagesContainer.style.height}px`\n          },\n          onClick: e => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight),\n          component: \"div\",\n          id: id,\n          onMouseOver: () => {\n            setImageAnchorEl({\n              buttonId: imageId,\n              containerId: id,\n              value: null\n            });\n            setSettingsAnchorEl(null);\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageSrc,\n            alt: \"Uploaded\",\n            style: {\n              ...imageStyle,\n              objectFit\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 8\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Popover\n    // className=\"qadpt-imgsec-popover\"\n    , {\n      id: \"image-popover\",\n      open: open\n      // anchorEl={document.getElementById(\"Tooltip-unique\")}\n      ,\n      anchorReference: \"anchorPosition\",\n      anchorPosition: {\n        left: ((_document$getElementB3 = document.getElementById(\"Tooltip-unique\")) === null || _document$getElementB3 === void 0 ? void 0 : (_document$getElementB4 = _document$getElementB3.getBoundingClientRect()) === null || _document$getElementB4 === void 0 ? void 0 : _document$getElementB4.x) || 150,\n        top: ((_document$getElementB5 = document.getElementById(\"Tooltip-unique\")) === null || _document$getElementB5 === void 0 ? void 0 : (_document$getElementB6 = _document$getElementB5.getBoundingClientRect()) === null || _document$getElementB6 === void 0 ? void 0 : _document$getElementB6.y) || 80\n      },\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"right\"\n      },\n      transformOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"left\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box\n      // className=\"qadpt-tool-btn\"\n      , {\n        sx: {\n          display: \"flex\",\n          // justifyContent: \"space-between\",\n          alignItems: \"center\",\n          gap: \"15px\",\n          height: \"100%\",\n          padding: \"0 10px\",\n          fontSize: \"12px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\"\n          },\n          children: currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId && currentImageSectionInfo.isImage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: replaceimageicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: \"12px\",\n              marginLeft: \"5px\",\n              onClick: triggerImageUpload,\n              children: translate(\"Replace Image\", {\n                defaultValue: \"Replace Image\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"replace-upload\",\n              style: {\n                display: \"none\"\n              },\n              accept: \"image/*\",\n              onChange: handleReplaceImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: sectionheight\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\", {\n              defaultValue: \"Minimum height reached\"\n            }) : translate(\"Decrease height\", {\n              defaultValue: \"Decrease height\"\n            }),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDecreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            children: currentImageSectionInfo.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\", {\n              defaultValue: \"Maximum height reached\"\n            }) : translate(\"Increase height\", {\n              defaultValue: \"Increase height\"\n            }),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleIncreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\", {\n            defaultValue: \"Settings\"\n          }),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-tool-items\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSettingsClick,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: Settings\n                  },\n                  style: {\n                    color: \"black\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Popover, {\n              open: openSettingsPopover,\n              anchorEl: settingsAnchorEl,\n              id: \"image-properties\",\n              onClose: handleCloseSettingsPopover,\n              anchorOrigin: {\n                vertical: \"center\",\n                horizontal: \"right\"\n              },\n              disablePortal: true,\n              transformOrigin: {\n                vertical: \"center\",\n                horizontal: \"left\"\n              },\n              slotProps: {\n                root: {\n                  sx: {\n                    zIndex: theme => theme.zIndex.tooltip + 2000\n                  }\n                }\n              },\n              PaperProps: {\n                sx: {\n                  mt: 12,\n                  ml: 20,\n                  width: \"205px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      color: \"rgba(95, 158, 160, 1)\"\n                    },\n                    children: translate(\"Image Properties\", {\n                      defaultValue: \"Image Properties\"\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleCloseSettingsPopover,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: CrossIcon\n                      },\n                      style: {\n                        color: \"black\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\", {\n                    defaultValue: \"Coming soon\"\n                  }),\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    mt: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      sx: {\n                        marginBottom: \"10px\"\n                      },\n                      children: translate(\"Image Actions\", {\n                        defaultValue: \"Image Actions\"\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 12\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      select: true,\n                      fullWidth: true,\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: selectedAction,\n                      onChange: handleActionChange,\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderColor: \"rgba(246, 238, 238, 1)\"\n                        }\n                      },\n                      disabled: true,\n                      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"none\",\n                        children: translate(\"None\", {\n                          defaultValue: \"None\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"specificStep\",\n                        children: translate(\"Specific Step\", {\n                          defaultValue: \"Specific Step\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 704,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"openUrl\",\n                        children: translate(\"Open URL\", {\n                          defaultValue: \"Open URL\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 705,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"clickElement\",\n                        children: translate(\"Click Element\", {\n                          defaultValue: \"Click Element\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startTour\",\n                        children: translate(\"Start Tour\", {\n                          defaultValue: \"Start Tour\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startMicroSurvey\",\n                        children: translate(\"Start Micro Survey\", {\n                          defaultValue: \"Start Micro Survey\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 708,\n                        columnNumber: 13\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 689,\n                      columnNumber: 12\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 11\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  component: \"div\",\n                  id: \"toggle-fit\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: translate(\"Image Formatting\", {\n                      defaultValue: \"Image Formatting\"\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    mt: 1,\n                    children: [\"Fill\", \"Fit\"].map(item => {\n                      // Get current image's objectFit to determine selected state\n                      // imagesContainer is a single object, not an array\n                      const currentImage = imagesContainer.images.find(img => img.id === imageAnchorEl.buttonId);\n                      const currentObjectFit = (currentImage === null || currentImage === void 0 ? void 0 : currentImage.objectFit) || IMG_OBJECT_FIT;\n\n                      // Determine if this button should be selected\n                      const isSelected = item === \"Fill\" && currentObjectFit === \"cover\" || item === \"Fit\" && currentObjectFit === \"contain\";\n                      return /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => toggleFit(imagesContainer.id, item),\n                        variant: \"outlined\",\n                        size: \"small\",\n                        sx: {\n                          width: \"88.5px\",\n                          height: \"41px\",\n                          padding: \"10px 12px\",\n                          gap: \"12px\",\n                          borderRadius: \"6px 6px 6px 6px\",\n                          border: isSelected ? \"1px solid rgba(95, 158, 160, 1)\" : \"1px solid rgba(246, 238, 238, 1)\",\n                          backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\n                          backgroundBlendMode: \"multiply\",\n                          color: \"black\",\n                          \"&:hover\": {\n                            backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\"\n                          }\n                        },\n                        children: translate(item, {\n                          defaultValue: item\n                        })\n                      }, item, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 14\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\", {\n            defaultValue: \"Background Color\"\n          }),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackgroundColorClick,\n              size: \"small\",\n              sx: {\n                height: \"20px\",\n                width: \"20px\",\n                backgroundColor: imagesContainer.style.backgroundColor,\n                border: \"2px solid black\",\n                marginTop: \"-3px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\", {\n            defaultValue: \"Maximum limit of 3 Image sections reached\"\n          }) : translate(\"Clone Section\", {\n            defaultValue: \"Clone Section\"\n          }),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCloneImgContainer,\n              size: \"small\",\n              disabled: isCloneDisabled,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Section\", {\n            defaultValue: \"Delete Section\"\n          }),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDeleteSection,\n              size: \"small\",\n              disabled: ((_useDrawerStore$ = useDrawerStore(state => state.toolTipGuideMetaData)[0]) === null || _useDrawerStore$ === void 0 ? void 0 : (_useDrawerStore$$cont = _useDrawerStore$.containers) === null || _useDrawerStore$$cont === void 0 ? void 0 : _useDrawerStore$$cont.length) === 1,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  opacity: ((_useDrawerStore$2 = useDrawerStore(state => state.toolTipGuideMetaData)[0]) === null || _useDrawerStore$2 === void 0 ? void 0 : (_useDrawerStore$2$con = _useDrawerStore$2.containers) === null || _useDrawerStore$2$con === void 0 ? void 0 : _useDrawerStore$2$con.length) === 1 ? 0.5 : 1,\n                  pointerEvents: 'none'\n                }\n                // style={{ marginTop: \"-3px\" }}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      id: \"color-popover\",\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      slotProps: {\n        root: {\n          sx: {\n            // zIndex: (theme) => theme.zIndex.tooltip + 1101,\n            zIndex: \"9999\"\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: imagesContainer.style.backgroundColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 839,\n      columnNumber: 4\n    }, this), isModelOpen && /*#__PURE__*/_jsxDEV(SelectImageFromApplication, {\n      isOpen: isModelOpen,\n      handleModelClose: () => setModelOpen(false),\n      onImageSelect: handleImageUploadFormApp\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 876,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n};\n_s(ImageSection, \"JmGHu7KOee/wD92FDuA3Z7jvxjU=\", false, function () {\n  return [useTranslation, useDrawerStore, useDrawerStore, useDrawerStore];\n});\n_c = ImageSection;\nexport default ImageSection;\nvar _c;\n$RefreshReg$(_c, \"ImageSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "RemoveIcon", "AddIcon", "SelectImageFromApplication", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSection", "items", "imagesContainer", "isCloneDisabled", "_s", "_document$getElementB3", "_document$getElementB4", "_document$getElementB5", "_document$getElementB6", "_useDrawerStore$", "_useDrawerStore$$cont", "_useDrawerStore$2", "_useDrawerStore$2$con", "t", "translate", "uploadTooltipImage", "uploadImage", "imageAnchorEl", "setImageAnchorEl", "replaceTooltipImage", "replaceImage", "cloneTooltipImage", "cloneImageContainer", "deleteTooltipImageContainer", "deleteImageContainer", "updateTooltipImageContainer", "updateImageContainer", "toggleTooltipImageFit", "toggleFit", "setImageSrc", "storeImageSrc", "tooltip", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "isReplaceImage", "setReplaceImage", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "style", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "urll", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "id", "altText", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "FileName", "handleReplaceImage", "_event$target$files2", "handleClick", "containerId", "imageId", "currentHeight", "includes", "buttonId", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "handleDeleteSection", "handleHyperlinkClick", "cId", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "visible", "children", "sx", "images", "textAlign", "onClick", "e", "_e$target", "startsWith", "_imagesContainer$styl", "component", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "align", "fontSize", "fontWeight", "defaultValue", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "_document$getElementB2", "stopPropagation", "type", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "map", "item", "_imagesContainer$styl2", "imageSrc", "currentSecHeight", "onMouseOver", "src", "alt", "anchorReference", "anchorPosition", "left", "getBoundingClientRect", "x", "top", "y", "transform<PERSON><PERSON>in", "marginLeft", "className", "size", "disabled", "anchorEl", "disable<PERSON><PERSON><PERSON>", "slotProps", "root", "zIndex", "theme", "PaperProps", "mt", "ml", "p", "marginBottom", "select", "fullWidth", "borderColor", "currentImage", "find", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "toolTipGuideMetaData", "containers", "handleModelClose", "onImageSelect", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/ImageSection.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DriveFolderUploadIcon from \"@mui/icons-material/DriveFolderUpload\";\r\nimport BackupIcon from \"@mui/icons-material/Backup\";\r\nimport Modal from \"@mui/material/Modal\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_EXPONENT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n\tTImageContainer,\r\n} from \"../../../store/drawerStore\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport \"../../guideSetting/PopupSections/PopupSections.css\";\r\nimport { getAllFiles } from \"../../../services/FileService\";\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nconst ImageSection: React.FC<{ items: TImageContainer & { type: \"image\" }; isCloneDisabled?: boolean; }> = ({ items: imagesContainer, isCloneDisabled }) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadTooltipImage: uploadImage,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceTooltipImage: replaceImage,\r\n\t\tcloneTooltipImage: cloneImageContainer,\r\n\t\tdeleteTooltipImageContainer: deleteImageContainer,\r\n\t\tupdateTooltipImageContainer: updateImageContainer, // TODO\r\n\t\ttoggleTooltipImageFit: toggleFit, //TODO\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t\ttooltip,\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\t\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\t\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\t\r\n\t\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\t\r\n\t\tconst openSnackbar = () => {\r\n\t\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\t\tsetSnackbarOpen(true);\r\n\t\t};\r\n\t\tconst closeSnackbar = () => {\r\n\t\t\tsetSnackbarOpen(false);\r\n\t\t};\r\n\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: `${imagesContainer.style.height}px`,\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tlet urll: any;\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\t\t\t// Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\t// setImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\t// urll = base64Image;\r\n\t\t\t\tuploadImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value && imagesContainer.id === imageAnchorEl.containerId);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imagesContainer.id, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imagesContainer.id, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\t// setReplaceImage(true);\r\n\t\t// setModelOpen(true);\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t};\r\n\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\r\n\t\tdeleteImageContainer(imagesContainer.id);\r\n\t};\r\n\tconst handleHyperlinkClick = (cId: string, isOpen: boolean) => {\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: cId,\r\n\t\t\tisOpen,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\t\tcloneImageContainer(imagesContainer.id);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imagesContainer.id, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\t// console.log({ imageAnchorEl, tooltip });\r\n\r\n\tuseEffect(() => {\r\n\t\tif (!tooltip.visible) {\r\n\t\t\tsetImageAnchorEl({\r\n\t\t\t\tbuttonId: \"\",\r\n\t\t\t\tcontainerId: \"\",\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: null,\r\n\t\t\t});\r\n\t\t\tsetSettingsAnchorEl(null);\r\n\t\t}\r\n\t}, [tooltip.visible]);\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Box\r\n\t\t\t\t// key={id}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t// padding: \"5px\",\r\n\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t{!imagesContainer.images.length ? (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\theight: `${imagesContainer.style.height}px`,\r\n\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t// height: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t// prevent to open toolbar when upload file clicked\r\n\t\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\t\tif (!e.target?.id.startsWith(\"file-upload\")) {\r\n\t\t\t\t\t\t\t\thandleClick(\r\n\t\t\t\t\t\t\t\t\te,\r\n\t\t\t\t\t\t\t\t\timagesContainer.id,\r\n\t\t\t\t\t\t\t\t\t\"\",\r\n\t\t\t\t\t\t\t\t\tfalse,\r\n\t\t\t\t\t\t\t\t\t(imagesContainer?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tid={imagesContainer.id}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Upload file\", { defaultValue: \"Upload file\" })}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\", { defaultValue: \"Drag & Drop to upload file\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Or\", { defaultValue: \"Or\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\", { defaultValue: \"Coming soon\" })}>\r\n\t\t\t\t\t\t\t\t\t<div style={{ pointerEvents: \"auto\", cursor: \"pointer\" }}>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: hyperlink }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"32px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\", { defaultValue: \"Coming soon\" })}>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\", { defaultValue: \"Upload File\" })}>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(`file-upload-${imagesContainer.id}`)?.click();\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\tid={`file-upload-${imagesContainer.id}`}\r\n\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t) : (\r\n\t\t\t\t\timagesContainer.images.map((item: any) => {\r\n\t\t\t\t\t\tconst imageSrc = item?.url;\r\n\t\t\t\t\t\tconst imageId = item?.id;\r\n\t\t\t\t\t\tconst objectFit = item?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\t\t\tconst currentSecHeight = (imagesContainer?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\t\t\tconst id = imagesContainer.id;\r\n\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\theight: `${imagesContainer.style.height}px`,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tsetSettingsAnchorEl(null);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t})\r\n\t\t\t\t)}\r\n\t\t\t</Box>\r\n\r\n\t\t\t<Popover\r\n\t\t\t\t// className=\"qadpt-imgsec-popover\"\r\n\t\t\t\tid={\"image-popover\"}\r\n\t\t\t\topen={open}\r\n\t\t\t\t// anchorEl={document.getElementById(\"Tooltip-unique\")}\r\n\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\tanchorPosition={{\r\n\t\t\t\t\tleft: document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.x || 150,\r\n\t\t\t\t\ttop: document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.y || 80,\r\n\t\t\t\t}}\r\n\t\t\t\tonClose={handleClose}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"right\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\t\r\n\t\t\t>\r\n\t\t\t\t<Box\r\n\t\t\t\t\t// className=\"qadpt-tool-btn\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t// justifyContent: \"space-between\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tgap: \"15px\",\r\n\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box sx={{ display: \"flex\" }}>\r\n\t\t\t\t\t\t{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&\r\n\t\t\t\t\t\tcurrentImageSectionInfo.isImage ? (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tfontSize=\"12px\"\r\n\t\t\t\t\t\t\t\t\tmarginLeft={\"5px\"}\r\n\t\t\t\t\t\t\t\t\tonClick={triggerImageUpload}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\", { defaultValue: \"Replace Image\" })}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleReplaceImage}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\", { defaultValue: \"Minimum height reached\" }) : translate(\"Decrease height\", { defaultValue: \"Decrease height\" })}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\", { defaultValue: \"Maximum height reached\" }) : translate(\"Increase height\", { defaultValue: \"Increase height\" })}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t<Tooltip title={translate(\"Settings\", { defaultValue: \"Settings\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={settingsAnchorEl}\r\n\t\t\t\t\t\t\t\tid=\"image-properties\"\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"right\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisablePortal\r\n\t\t\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 2000,\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tPaperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tmt: 12,\r\n\t\t\t\t\t\t\t\t\t\tml: 20,\r\n\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\", { defaultValue: \"Image Properties\" })}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\", { defaultValue: \"Coming soon\" })}>\r\n\t\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\", { defaultValue: \"Image Actions\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\", { defaultValue: \"None\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\", { defaultValue: \"Specific Step\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\", { defaultValue: \"Open URL\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\", { defaultValue: \"Click Element\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\", { defaultValue: \"Start Tour\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\", { defaultValue: \"Start Micro Survey\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tmt={2}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t\tid=\"toggle-fit\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\", { defaultValue: \"Image Formatting\" })}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\t// imagesContainer is a single object, not an array\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = imagesContainer.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => toggleFit(imagesContainer.id, item as \"Fit\" | \"Fill\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item, { defaultValue: item })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip title={translate(\"Background Color\", { defaultValue: \"Background Color\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\tborder: \"2px solid black\",\r\n\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{/* <span\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\", { defaultValue: \"Maximum limit of 3 Image sections reached\" }) : translate(\"Clone Section\", { defaultValue: \"Clone Section\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t{/* cloneImageContainer */}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip title={translate(\"Delete Section\", { defaultValue: \"Delete Section\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={\r\n\t\t\t\t\t\t\t\t\tuseDrawerStore((state) => state.toolTipGuideMetaData)[0]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\tuseDrawerStore((state) => state.toolTipGuideMetaData)[0]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t// style={{ marginTop: \"-3px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t\t<Popover\r\n\t\t\t\tid=\"color-popover\"\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t// zIndex: (theme) => theme.zIndex.tooltip + 1101,\r\n\t\t\t\t\t\t\tzIndex: \"9999\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={imagesContainer.style.backgroundColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t\t{isModelOpen && (\r\n\t\t\t\t<SelectImageFromApplication\r\n\t\t\t\t\tisOpen={isModelOpen}\r\n\t\t\t\t\thandleModelClose={() => setModelOpen(false)}\r\n\t\t\t\t\tonImageSelect={handleImageUploadFormApp}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,eAAe;AAC3H,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAI7C,OAAOC,0BAA0B,MAAM,yCAAyC;AAChF,SAASC,cAAc,QAAQ,eAAe;AAE9C,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,QACH,6BAA6B;AACpC,OAAOC,cAAc,IACpBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,wBAAwB,EAExBC,cAAc,EACdC,cAAc,QAER,4BAA4B;AACnC,SAASC,YAAY,QAAqB,aAAa;AACvD,OAAO,oDAAoD;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI5D,MAAMC,YAAkG,GAAGA,CAAC;EAAEC,KAAK,EAAEC,eAAe;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;EAC3J,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGpC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLqC,kBAAkB,EAAEC,WAAW;IAC/BC,aAAa;IACbC,gBAAgB;IAChBC,mBAAmB,EAAEC,YAAY;IACjCC,iBAAiB,EAAEC,mBAAmB;IACtCC,2BAA2B,EAAEC,oBAAoB;IACjDC,2BAA2B,EAAEC,oBAAoB;IAAE;IACnDC,qBAAqB,EAAEC,SAAS;IAAE;IAClCC,WAAW,EAAEC,aAAa;IAC1BC;EACD,CAAC,GAAG1C,cAAc,CAAE2C,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACtD,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1E,QAAQ,CAA2C,MAAM,CAAC;EAE1G,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAS,CAAC,CAAC;EAEzD,MAAM6E,YAAY,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAACE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChCR,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC3BT,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EAEF,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjF,QAAQ,CAAkD;IAC7GkF,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrF,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAACsF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvF,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAACwF,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGzF,QAAQ,CAInE;IAAEkF,kBAAkB,EAAE,EAAE;IAAEQ,OAAO,EAAE,KAAK;IAAEC,MAAM,EAAEjE;EAA6B,CAAC,CAAC;EAEpF,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAG7F,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAAC8F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAqB,IAAI,CAAC;EAClF,MAAM,CAACgG,aAAa,EAAEC,gBAAgB,CAAC,GAAGjG,QAAQ,CAAS,EAAE,CAAC;EAC9D,MAAM,CAACkG,WAAW,EAAEC,YAAY,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACsG,cAAc,EAAEC,eAAe,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMwG,mBAAmB,GAAGC,OAAO,CAACX,gBAAgB,CAAC;EACrD,MAAMY,kBAAkB,GAAIC,KAAU,IAAK;IAC1Cd,iBAAiB,CAACc,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EACD,MAAMC,mBAAmB,GAAIH,KAAoC,IAAK;IACrEZ,mBAAmB,CAACY,KAAK,CAACI,aAAa,CAAC;EACzC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxCjB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMkB,mBAAwC,GAAG;IAChDC,KAAK,EAAE,MAAM;IACbvB,MAAM,EAAE,GAAGrD,eAAe,CAAC6E,KAAK,CAACxB,MAAM,IAAI;IAC3CyB,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCR,KAAK,EAAE,MAAM;IACbvB,MAAM,EAAE,MAAM;IACd6B,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCR,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBQ,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE;EACZ,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1CX,OAAO,EAAE,MAAM;IACfY,aAAa,EAAE,QAAQ;IACvBV,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBH,KAAK,EAAE;EACR,CAAC;EAED,MAAMe,iBAAiB,GAAItB,KAA0C,IAAK;IAAA,IAAAuB,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGvB,KAAK,CAACC,MAAM,CAAC3F,KAAK,cAAAiH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIE,IAAS;IACb,IAAID,IAAI,EAAE;MACT,MAAME,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAC;MAChC;MACI,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACF,SAAS,EAAG;QACvChE,kBAAkB,CAAC,6DAA6D,CAAC;QAC5EE,mBAAmB,CAAC,OAAO,CAAC;QAClCJ,eAAe,CAAC,IAAI,CAAC;QACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MAEF;MACH,IAAGsB,IAAI,CAACG,IAAI,CAACI,MAAM,GAAG,GAAG,EAAC;QAC1BlE,kBAAkB,CAAC,4CAA4C,CAAC;QAC1DE,mBAAmB,CAAC,OAAO,CAAC;QACjCJ,eAAe,CAAC,IAAI,CAAC;QACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MACN;MACD;;MAEA,MAAM8B,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB,MAAMC,WAAW,GAAGH,MAAM,CAACI,MAAgB;QAC3C7E,aAAa,CAAC4E,WAAW,CAAC;QAC1B;QACA1F,WAAW,CAACd,eAAe,CAAC0G,EAAE,EAAE;UAC/BC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBU,EAAE,EAAEE,MAAM,CAACC,UAAU,CAAC,CAAC;UACvBC,GAAG,EAAEN,WAAW;UAChBO,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEzH;QACZ,CAAC,CAAC;MACH,CAAC;MACD8G,MAAM,CAACY,aAAa,CAACpB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMqB,wBAAwB,GAAIrB,IAAgB,IAAK;IACtD,IAAIA,IAAI,EAAE;MACTjE,aAAa,CAACiE,IAAI,CAACsB,GAAG,CAAC;MACvB,IAAInD,cAAc,EAAE;QACnB9C,YAAY,CAAClB,eAAe,CAAC0G,EAAE,EAAE;UAChCC,OAAO,EAAEd,IAAI,CAACuB,QAAQ;UACtBN,GAAG,EAAEjB,IAAI,CAACsB,GAAG;UACbJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEzH;QACZ,CAAC,CAAC;QACF0E,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACNnD,WAAW,CAACd,eAAe,CAAC0G,EAAE,EAAE;UAC/BC,OAAO,EAAEd,IAAI,CAACuB,QAAQ;UACtBV,EAAE,EAAEE,MAAM,CAACC,UAAU,CAAC,CAAC;UAAE;UACzBC,GAAG,EAAEjB,IAAI,CAACsB,GAAG;UAAE;UACfJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEzH;QACZ,CAAC,CAAC;MACH;IACD;IACAsE,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMwD,kBAAkB,GAAIhD,KAA0C,IAAK;IAAA,IAAAiD,oBAAA;IAC1E,MAAMzB,IAAI,IAAAyB,oBAAA,GAAGjD,KAAK,CAACC,MAAM,CAAC3F,KAAK,cAAA2I,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIzB,IAAI,EAAE;MACT,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxBrF,YAAY,CAAClB,eAAe,CAAC0G,EAAE,EAAE;UAChCC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBc,GAAG,EAAET,MAAM,CAACI,MAAM;UAClBM,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAEzH;QACZ,CAAC,CAAC;MACH,CAAC;MACD8G,MAAM,CAACY,aAAa,CAACpB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAM0B,WAAW,GAAGA,CACnBlD,KAAoC,EACpCmD,WAAmB,EACnBC,OAAe,EACfrE,OAAgB,EAChBsE,aAAqB,KACjB;IACJ;IACA,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACoC,EAAE,CAAC,EAAE;IAC5D1F,gBAAgB,CAAC;MAChB4G,QAAQ,EAAEH,OAAO;MACjBD,WAAW,EAAEA,WAAW;MACxB;MACAjD,KAAK,EAAEF,KAAK,CAACI;IACd,CAAC,CAAC;IACFhB,mBAAmB,CAAC,IAAI,CAAC;IACzBN,0BAA0B,CAAC;MAC1BP,kBAAkB,EAAE4E,WAAW;MAC/BpE,OAAO;MACPC,MAAM,EAAEqE;IACT,CAAC,CAAC;IACF/E,qBAAqB,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE;IACT,CAAC,CAAC;EACH,CAAC;EAED,MAAMgF,WAAW,GAAGA,CAAA,KAAM;IACzB7G,gBAAgB,CAAC;MAChB4G,QAAQ,EAAE,EAAE;MACZJ,WAAW,EAAE,EAAE;MACf;MACAjD,KAAK,EAAE;IACR,CAAC,CAAC;IACFd,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMqE,IAAI,GAAG3D,OAAO,CAACpD,aAAa,CAACwD,KAAK,IAAIvE,eAAe,CAAC0G,EAAE,KAAK3F,aAAa,CAACyG,WAAW,CAAC;EAC7F,MAAMO,eAAe,GAAG5D,OAAO,CAACnB,mBAAmB,CAAC;EAEpD,MAAM0D,EAAE,GAAGoB,IAAI,GAAG,eAAe,GAAGE,SAAS;EAE7C,MAAMC,oBAAoB,GAAIC,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAI7I,wBAAwB,EAAE;IAC5C,MAAM8I,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,GAAG1I,cAAc,EAAEH,wBAAwB,CAAC;IACjFmC,oBAAoB,CAACxB,eAAe,CAAC0G,EAAE,EAAE,OAAO,EAAE;MACjDrD,MAAM,EAAE8E;IACT,CAAC,CAAC;IACFhF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAE8E;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAI5I,wBAAwB,EAAE;IAC5C,MAAM6I,SAAS,GAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,GAAG1I,cAAc,EAAEF,wBAAwB,CAAC;IACjFkC,oBAAoB,CAACxB,eAAe,CAAC0G,EAAE,EAAE,OAAO,EAAE;MACjDrD,MAAM,EAAE8E;IACT,CAAC,CAAC;IACFhF,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAE8E;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC;IACA;IACA,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IACjC7H,gBAAgB,CAAC;MAChB4G,QAAQ,EAAE,EAAE;MACZJ,WAAW,EAAE,EAAE;MACf;MACAjD,KAAK,EAAE;IACR,CAAC,CAAC;IACFd,mBAAmB,CAAC,IAAI,CAAC;IAEzBnC,oBAAoB,CAACtB,eAAe,CAAC0G,EAAE,CAAC;EACzC,CAAC;EACD,MAAMoC,oBAAoB,GAAGA,CAACC,GAAW,EAAElG,MAAe,KAAK;IAC9DF,qBAAqB,CAAC;MACrBC,kBAAkB,EAAEmG,GAAG;MACvBlG;IACD,CAAC,CAAC;EACH,CAAC;EAED,MAAMmG,gBAAgB,GAAI3E,KAA4C,IAAK;IAC1E,IAAIA,KAAK,CAAC4E,GAAG,KAAK,OAAO,IAAInG,SAAS,EAAE;MACvChC,WAAW,CAACC,aAAa,CAACyG,WAAW,EAAE;QACtCb,OAAO,EAAE,WAAW;QACpBD,EAAE,EAAEE,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,GAAG,EAAEhE,SAAS;QACdiE,eAAe,EAAE,aAAa;QAC9BC,SAAS,EAAEzH;MACZ,CAAC,CAAC;MACFoD,qBAAqB,CAAC;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE;MACT,CAAC,CAAC;IACH;EACD,CAAC;EAED,MAAMqG,uBAAuB,GAAGA,CAAA,KAAM;IACrC9H,mBAAmB,CAACpB,eAAe,CAAC0G,EAAE,CAAC;EACxC,CAAC;EAED,MAAMyC,sBAAsB,GAAGA,CAAA,KAAM;IACpClG,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMmG,iBAAiB,GAAIC,KAAkB,IAAK;IACjD1F,gBAAgB,CAAC0F,KAAK,CAACC,GAAG,CAAC;IAC3B9H,oBAAoB,CAACxB,eAAe,CAAC0G,EAAE,EAAE,OAAO,EAAE;MACjDK,eAAe,EAAEsC,KAAK,CAACC;IACxB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,0BAA0B,GAAIlF,KAAoC,IAAK;IAC5EpB,sBAAsB,CAACoB,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;;EAED;;EAEAhH,SAAS,CAAC,MAAM;IACf,IAAI,CAACoE,OAAO,CAAC2H,OAAO,EAAE;MACrBxI,gBAAgB,CAAC;QAChB4G,QAAQ,EAAE,EAAE;QACZJ,WAAW,EAAE,EAAE;QACf;QACAjD,KAAK,EAAE;MACR,CAAC,CAAC;MACFd,mBAAmB,CAAC,IAAI,CAAC;IAC1B;EACD,CAAC,EAAE,CAAC5B,OAAO,CAAC2H,OAAO,CAAC,CAAC;EAErB,oBACC7J,OAAA,CAAAE,SAAA;IAAA4J,QAAA,gBACC9J,OAAA,CAAChC;IACA;IAAA;MACA+L,EAAE,EAAE;QACH9E,KAAK,EAAE,MAAM;QACbvB,MAAM,EAAE,MAAM;QACdyB,OAAO,EAAE,MAAM;QACfY,aAAa,EAAE,QAAQ;QACvBX,cAAc,EAAE,YAAY;QAC5BC,UAAU,EAAE,QAAQ;QACpB;QACAE,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE;MACX,CAAE;MAAAsE,QAAA,EAED,CAACzJ,eAAe,CAAC2J,MAAM,CAACvD,MAAM,gBAC9BzG,OAAA,CAAChC,GAAG;QACH+L,EAAE,EAAE;UACH,GAAG/E,mBAAmB;UACtBoC,eAAe,EAAE/G,eAAe,CAAC6E,KAAK,CAACkC,eAAe;UACtD1D,MAAM,EAAE,GAAGrD,eAAe,CAAC6E,KAAK,CAACxB,MAAM,IAAI;UAC3CuG,SAAS,EAAE,QAAQ;UACnBhF,KAAK,EAAE,MAAM;UACb;UACAE,OAAO,EAAE,MAAM;UACfY,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE;QACjB,CAAE;QACF8E,OAAO,EAAGC,CAAC,IAAK;UAAA,IAAAC,SAAA;UACf;UACA;UACA,IAAI,GAAAA,SAAA,GAACD,CAAC,CAACxF,MAAM,cAAAyF,SAAA,eAARA,SAAA,CAAUrD,EAAE,CAACsD,UAAU,CAAC,aAAa,CAAC,GAAE;YAAA,IAAAC,qBAAA;YAC5C1C,WAAW,CACVuC,CAAC,EACD9J,eAAe,CAAC0G,EAAE,EAClB,EAAE,EACF,KAAK,EACL,CAAC1G,eAAe,aAAfA,eAAe,wBAAAiK,qBAAA,GAAfjK,eAAe,CAAE6E,KAAK,cAAAoF,qBAAA,uBAAtBA,qBAAA,CAAwB5G,MAAM,KAAejE,4BAC/C,CAAC;UACF;QACD,CAAE;QACFsH,EAAE,EAAE1G,eAAe,CAAC0G,EAAG;QAAA+C,QAAA,gBAEvB9J,OAAA,CAAChC,GAAG;UACH+L,EAAE,EAAEjE,aAAc;UAClByE,SAAS,EAAE,KAAM;UAAAT,QAAA,gBAEjB9J,OAAA;YACCwK,uBAAuB,EAAE;cAAEC,MAAM,EAAE3L;YAAW,CAAE;YAChDoG,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAe;UAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACF7K,OAAA,CAAC/B,UAAU;YACV6M,OAAO,EAAC,IAAI;YACZC,KAAK,EAAC,QAAQ;YACdrB,KAAK,EAAC,eAAe;YACrBK,EAAE,EAAE;cAAEiB,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAnB,QAAA,EAE3C7I,SAAS,CAAC,aAAa,EAAE;cAAEiK,YAAY,EAAE;YAAc,CAAC;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN7K,OAAA,CAAC/B,UAAU;UACV6M,OAAO,EAAC,OAAO;UACfC,KAAK,EAAC,QAAQ;UACdrB,KAAK,EAAC,eAAe;UACrBK,EAAE,EAAE;YAAEiB,QAAQ,EAAE;UAAO,CAAE;UAAAlB,QAAA,EAExB7I,SAAS,CAAC,4BAA4B,EAAE;YAAEiK,YAAY,EAAE;UAA6B,CAAC;QAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACb7K,OAAA,CAAC/B,UAAU;UACV6M,OAAO,EAAC,OAAO;UACfC,KAAK,EAAC,QAAQ;UACdrB,KAAK,EAAC,eAAe;UACrBK,EAAE,EAAE;YAAElE,SAAS,EAAE,KAAK;YAAEmF,QAAQ,EAAE;UAAO,CAAE;UAAAlB,QAAA,EAE1C7I,SAAS,CAAC,IAAI,EAAE;YAAEiK,YAAY,EAAE;UAAK,CAAC;QAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACZ9H,kBAAkB,CAACG,MAAM,IAAIH,kBAAkB,CAACE,kBAAkB,KAAK8D,EAAE,gBACzE/G,OAAA,CAAC5B,SAAS;UACTwG,KAAK,EAAEzB,SAAU;UACjBgI,QAAQ,EAAGhB,CAAC,IAAK/G,YAAY,CAAC+G,CAAC,CAACxF,MAAM,CAACC,KAAK,CAAE;UAC9CwG,SAAS,EAAE/B,gBAAiB;UAC5BgC,SAAS;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,gBAEF7K,OAAA,CAAChC,GAAG;UAAC+L,EAAE,EAAEpE,YAAa;UAAAmE,QAAA,gBACpB9J,OAAA,CAACzB,OAAO;YAAC+M,KAAK,EAAErK,SAAS,CAAC,aAAa,EAAE;cAAEiK,YAAY,EAAE;YAAc,CAAC,CAAE;YAAApB,QAAA,eAC1E9J,OAAA;cAAKkF,KAAK,EAAE;gBAAEqG,aAAa,EAAE,MAAM;gBAAEC,MAAM,EAAE;cAAU,CAAE;cAAA1B,QAAA,eACxD9J,OAAA;gBACCwK,uBAAuB,EAAE;kBAAEC,MAAM,EAAE1L;gBAAU,CAAE;gBAC/CmG,KAAK,EAAE;kBACNwE,KAAK,EAAE,OAAO;kBACd8B,MAAM,EAAE,SAAS;kBACjBR,QAAQ,EAAE,MAAM;kBAChBS,OAAO,EAAE,KAAK;kBACdF,aAAa,EAAE;gBAChB,CAAE;gBACFxE,EAAE,EAAC;cAAW;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAET7K,OAAA,CAACzB,OAAO;YAAC+M,KAAK,EAAErK,SAAS,CAAC,aAAa,EAAE;cAAEiK,YAAY,EAAE;YAAc,CAAC,CAAE;YAAApB,QAAA,eAC1E9J,OAAA;cACCkK,OAAO,EAAEA,CAAA,KAAM;gBACd;cAAA,CACC;cACFM,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzL;cAAM,CAAE;cAC3CkG,KAAK,EAAE;gBAAEwE,KAAK,EAAE,OAAO;gBAAE8B,MAAM,EAAE,SAAS;gBAAER,QAAQ,EAAE,MAAM;gBAAES,OAAO,EAAE;cAAM,CAAE;cAC/E1E,EAAE,EAAC;cACH;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACT7K,OAAA,CAACzB,OAAO;YAAC+M,KAAK,EAAErK,SAAS,CAAC,aAAa,EAAE;cAAEiK,YAAY,EAAE;YAAc,CAAC,CAAE;YAAApB,QAAA,eAC1E9J,OAAA;cACCkK,OAAO,EAAGxF,KAAK,IAAK;gBAAA,IAAAgH,sBAAA;gBACnBhH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiH,eAAe,CAAC,CAAC;gBACxB,CAAAD,sBAAA,GAAA3C,QAAQ,CAACC,cAAc,CAAC,eAAe3I,eAAe,CAAC0G,EAAE,EAAE,CAAC,cAAA2E,sBAAA,uBAA5DA,sBAAA,CAA8DzC,KAAK,CAAC,CAAC;cACtE,CAAE;cACFlC,EAAE,EAAC,cAAc;cACjByD,uBAAuB,EAAE;gBAAEC,MAAM,EAAExL;cAAW,CAAE;cAChDiG,KAAK,EAAE;gBAAEwE,KAAK,EAAE,OAAO;gBAAE8B,MAAM,EAAE,SAAS;gBAAER,QAAQ,EAAE;cAAO;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACV7K,OAAA;YACC4L,IAAI,EAAC,MAAM;YACX7E,EAAE,EAAE,eAAe1G,eAAe,CAAC0G,EAAE,EAAG;YACxC7B,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAO,CAAE;YAC3B0G,MAAM,EAAC,SAAS;YAChBV,QAAQ,EAAEnF;UAAkB;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF7K,OAAA,CAACxB,QAAQ;YAAC2J,IAAI,EAAE/F,YAAa;YAAC0J,gBAAgB,EAAE,IAAK;YAACC,OAAO,EAAEjJ,aAAc;YAACkJ,YAAY,EAAE;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAApC,QAAA,eACvI9J,OAAA,CAACvB,KAAK;cAACsN,OAAO,EAAEjJ,aAAc;cAACqJ,QAAQ,EAAE3J,gBAAiB;cAACuH,EAAE,EAAE;gBAAE9E,KAAK,EAAE;cAAO,CAAE;cAAA6E,QAAA,EAC/ExH;YAAe;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,GAENxK,eAAe,CAAC2J,MAAM,CAACoC,GAAG,CAAEC,IAAS,IAAK;QAAA,IAAAC,sBAAA;QACzC,MAAMC,QAAQ,GAAGF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAElF,GAAG;QAC1B,MAAMW,OAAO,GAAGuE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEtF,EAAE;QACxB,MAAMM,SAAS,GAAG,CAAAgF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhF,SAAS,KAAIzH,cAAc;QACnD,MAAM4M,gBAAgB,GAAG,CAACnM,eAAe,aAAfA,eAAe,wBAAAiM,sBAAA,GAAfjM,eAAe,CAAE6E,KAAK,cAAAoH,sBAAA,uBAAtBA,sBAAA,CAAwB5I,MAAM,KAAejE,4BAA4B;QACnG,MAAMsH,EAAE,GAAG1G,eAAe,CAAC0G,EAAE;QAC7B,oBACC/G,OAAA,CAAChC,GAAG;UACH+L,EAAE,EAAE;YACH,GAAG/E,mBAAmB;YACtBoC,eAAe,EAAE/G,eAAe,CAAC6E,KAAK,CAACkC,eAAe;YACtD1D,MAAM,EAAE,GAAGrD,eAAe,CAAC6E,KAAK,CAACxB,MAAM;UACxC,CAAE;UACFwG,OAAO,EAAGC,CAAC,IAAKvC,WAAW,CAACuC,CAAC,EAAEpD,EAAE,EAAEe,OAAO,EAAEyE,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEC,gBAAgB,CAAE;UACvFjC,SAAS,EAAE,KAAM;UACjBxD,EAAE,EAAEA,EAAG;UACP0F,WAAW,EAAEA,CAAA,KAAM;YAClBpL,gBAAgB,CAAC;cAChB4G,QAAQ,EAAEH,OAAO;cACjBD,WAAW,EAAEd,EAAE;cACfnC,KAAK,EAAE;YACR,CAAC,CAAC;YACFd,mBAAmB,CAAC,IAAI,CAAC;UAC1B,CAAE;UAAAgG,QAAA,eAEF9J,OAAA;YACC0M,GAAG,EAAEH,QAAS;YACdI,GAAG,EAAC,UAAU;YACdzH,KAAK,EAAE;cAAE,GAAGO,UAAU;cAAE4B;YAAU;UAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAER,CAAC;IACD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEN7K,OAAA,CAAC9B;IACA;IAAA;MACA6I,EAAE,EAAE,eAAgB;MACpBoB,IAAI,EAAEA;MACN;MAAA;MACAyE,eAAe,EAAC,gBAAgB;MAChCC,cAAc,EAAE;QACfC,IAAI,EAAE,EAAAtM,sBAAA,GAAAuI,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAxI,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CuM,qBAAqB,CAAC,CAAC,cAAAtM,sBAAA,uBAAlEA,sBAAA,CAAoEuM,CAAC,KAAI,GAAG;QAClFC,GAAG,EAAE,EAAAvM,sBAAA,GAAAqI,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAtI,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CqM,qBAAqB,CAAC,CAAC,cAAApM,sBAAA,uBAAlEA,sBAAA,CAAoEuM,CAAC,KAAI;MAC/E,CAAE;MACFnB,OAAO,EAAE7D,WAAY;MACrB8D,YAAY,EAAE;QACbC,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFiB,eAAe,EAAE;QAChBlB,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MAAApC,QAAA,eAGF9J,OAAA,CAAChC;MACA;MAAA;QACA+L,EAAE,EAAE;UACH5E,OAAO,EAAE,MAAM;UACf;UACAE,UAAU,EAAE,QAAQ;UACpBO,GAAG,EAAE,MAAM;UACXlC,MAAM,EAAE,MAAM;UACd4B,OAAO,EAAE,QAAQ;UACjB0F,QAAQ,EAAE;QACX,CAAE;QAAAlB,QAAA,gBAEF9J,OAAA,CAAChC,GAAG;UAAC+L,EAAE,EAAE;YAAE5E,OAAO,EAAE;UAAO,CAAE;UAAA2E,QAAA,EAC3BvG,uBAAuB,CAACN,kBAAkB,KAAK7B,aAAa,CAACyG,WAAW,IACzEtE,uBAAuB,CAACE,OAAO,gBAC9BzD,OAAA,CAAAE,SAAA;YAAA4J,QAAA,gBACC9J,OAAA;cAAMwK,uBAAuB,EAAE;gBAAEC,MAAM,EAAEvL;cAAiB;YAAE;cAAAwL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D7K,OAAA,CAAC/B,UAAU;cACV+M,QAAQ,EAAC,MAAM;cACfoC,UAAU,EAAE,KAAM;cAClBlD,OAAO,EAAErB,kBAAmB;cAAAiB,QAAA,EAE1B7I,SAAS,CAAC,eAAe,EAAE;gBAAEiK,YAAY,EAAE;cAAgB,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACb7K,OAAA;cACC4L,IAAI,EAAC,MAAM;cACX7E,EAAE,EAAC,gBAAgB;cACnB7B,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAC3B0G,MAAM,EAAC,SAAS;cAChBV,QAAQ,EAAEzD;YAAmB;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA,eACD,CAAC,GACA;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7K,OAAA,CAAChC,GAAG;UACFqP,SAAS,EAAC,kBAAkB;UAC5BtD,EAAE,EAAE;YAAE5E,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAyE,QAAA,gBAE9C9J,OAAA;YAAMwK,uBAAuB,EAAE;cAAEC,MAAM,EAAEpL;YAAc;UAAE;YAAAqL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D7K,OAAA,CAACzB,OAAO;YAAC+M,KAAK,EAAE/H,uBAAuB,CAACG,MAAM,IAAI/D,wBAAwB,GAAGsB,SAAS,CAAC,wBAAwB,EAAE;cAAEiK,YAAY,EAAE;YAAyB,CAAC,CAAC,GAAGjK,SAAS,CAAC,iBAAiB,EAAE;cAAEiK,YAAY,EAAE;YAAkB,CAAC,CAAE;YAAApB,QAAA,eAC/N9J,OAAA;cAAA8J,QAAA,eACC9J,OAAA,CAAC7B,UAAU;gBACV+L,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAACpF,uBAAuB,CAACG,MAAM,CAAE;gBACpE4J,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAEhK,uBAAuB,CAACG,MAAM,IAAI/D,wBAAyB;gBACrEoK,EAAE,EAAE;kBACH0B,OAAO,EAAElI,uBAAuB,CAACG,MAAM,IAAI/D,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E6L,MAAM,EAAEjI,uBAAuB,CAACG,MAAM,IAAI/D,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAmK,QAAA,eAEF9J,OAAA,CAACtB,UAAU;kBAACsM,QAAQ,EAAC;gBAAO;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACV7K,OAAA,CAAC/B,UAAU;YAAC+M,QAAQ,EAAC,MAAM;YAAAlB,QAAA,EAAEvG,uBAAuB,CAACG;UAAM;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1E7K,OAAA,CAACzB,OAAO;YAAC+M,KAAK,EAAE/H,uBAAuB,CAACG,MAAM,IAAIhE,wBAAwB,GAAGuB,SAAS,CAAC,wBAAwB,EAAE;cAAEiK,YAAY,EAAE;YAAyB,CAAC,CAAC,GAAGjK,SAAS,CAAC,iBAAiB,EAAE;cAAEiK,YAAY,EAAE;YAAkB,CAAC,CAAE;YAAApB,QAAA,eAC/N9J,OAAA;cAAA8J,QAAA,eACC9J,OAAA,CAAC7B,UAAU;gBACV+L,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAAC/E,uBAAuB,CAACG,MAAM,CAAE;gBACpE4J,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAEhK,uBAAuB,CAACG,MAAM,IAAIhE,wBAAyB;gBACrEqK,EAAE,EAAE;kBACH0B,OAAO,EAAElI,uBAAuB,CAACG,MAAM,IAAIhE,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7E8L,MAAM,EAAEjI,uBAAuB,CAACG,MAAM,IAAIhE,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAoK,QAAA,eAEF9J,OAAA,CAACrB,OAAO;kBAACqM,QAAQ,EAAC;gBAAO;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACP7K,OAAA,CAACzB,OAAO;UAAC+M,KAAK,EAAErK,SAAS,CAAC,UAAU,EAAE;YAAEiK,YAAY,EAAE;UAAW,CAAC,CAAE;UAAApB,QAAA,eACnE9J,OAAA,CAAChC,GAAG;YAACqP,SAAS,EAAC,kBAAkB;YAAAvD,QAAA,gBAChC9J,OAAA,CAAChC,GAAG;cAACqP,SAAS,EAAC,kBAAkB;cAAAvD,QAAA,eAChC9J,OAAA,CAAC7B,UAAU;gBACVmP,IAAI,EAAC,OAAO;gBACZpD,OAAO,EAAErF,mBAAoB;gBAAAiF,QAAA,eAE7B9J,OAAA;kBACCwK,uBAAuB,EAAE;oBAAEC,MAAM,EAAEnL;kBAAS,CAAE;kBAC9C4F,KAAK,EAAE;oBAAEwE,KAAK,EAAE;kBAAQ;gBAAE;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN7K,OAAA,CAAC9B,OAAO;cACPiK,IAAI,EAAE5D,mBAAoB;cAC1BiJ,QAAQ,EAAE3J,gBAAiB;cAC3BkD,EAAE,EAAC,kBAAkB;cACrBgF,OAAO,EAAEhH,0BAA2B;cACpCiH,YAAY,EAAE;gBACbC,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACb,CAAE;cACFuB,aAAa;cACbN,eAAe,EAAE;gBAChBlB,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACb,CAAE;cACFwB,SAAS,EAAE;gBACVC,IAAI,EAAE;kBACL5D,EAAE,EAAE;oBACH6D,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAAC1L,OAAO,GAAG;kBAC3C;gBACD;cACD,CAAE;cACF4L,UAAU,EAAE;gBACX/D,EAAE,EAAE;kBACHgE,EAAE,EAAE,EAAE;kBACNC,EAAE,EAAE,EAAE;kBACN/I,KAAK,EAAE;gBACR;cACD,CAAE;cAAA6E,QAAA,eAEF9J,OAAA,CAAChC,GAAG;gBAACiQ,CAAC,EAAE,CAAE;gBAAAnE,QAAA,gBACT9J,OAAA,CAAChC,GAAG;kBACHmH,OAAO,EAAC,MAAM;kBACdC,cAAc,EAAC,eAAe;kBAC9BC,UAAU,EAAC,QAAQ;kBAAAyE,QAAA,gBAEnB9J,OAAA,CAAC/B,UAAU;oBACV6M,OAAO,EAAC,WAAW;oBACnBf,EAAE,EAAE;sBAAEL,KAAK,EAAE;oBAAwB,CAAE;oBAAAI,QAAA,EAEtC7I,SAAS,CAAC,kBAAkB,EAAE;sBAAEiK,YAAY,EAAE;oBAAmB,CAAC;kBAAC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACb7K,OAAA,CAAC7B,UAAU;oBACVmP,IAAI,EAAC,OAAO;oBACZpD,OAAO,EAAEnF,0BAA2B;oBAAA+E,QAAA,eAEpC9J,OAAA;sBACCwK,uBAAuB,EAAE;wBAAEC,MAAM,EAAElL;sBAAU,CAAE;sBAC/C2F,KAAK,EAAE;wBAAEwE,KAAK,EAAE;sBAAQ;oBAAE;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACN7K,OAAA,CAACzB,OAAO;kBAAC+M,KAAK,EAAErK,SAAS,CAAC,aAAa,EAAE;oBAAEiK,YAAY,EAAE;kBAAc,CAAC,CAAE;kBAAApB,QAAA,eACzE9J,OAAA,CAAChC,GAAG;oBAAC+P,EAAE,EAAE,CAAE;oBAAAjE,QAAA,gBACV9J,OAAA,CAAC/B,UAAU;sBACV6M,OAAO,EAAC,OAAO;sBACfpB,KAAK,EAAC,eAAe;sBACrBK,EAAE,EAAE;wBAAEmE,YAAY,EAAE;sBAAO,CAAE;sBAAApE,QAAA,EAE5B7I,SAAS,CAAC,eAAe,EAAE;wBAAEiK,YAAY,EAAE;sBAAgB,CAAC;oBAAC;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACb7K,OAAA,CAAC5B,SAAS;sBACT+P,MAAM;sBACNC,SAAS;sBACTtD,OAAO,EAAC,UAAU;sBAClBwC,IAAI,EAAC,OAAO;sBACZ1I,KAAK,EAAEjB,cAAe;sBACtBwH,QAAQ,EAAE1G,kBAAmB;sBAC7BsF,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAC3BsE,WAAW,EAAE;wBACd;sBACD,CAAE;sBACFd,QAAQ;sBAAAzD,QAAA,gBAER9J,OAAA,CAAC3B,QAAQ;wBAACuG,KAAK,EAAC,MAAM;wBAAAkF,QAAA,EAAE7I,SAAS,CAAC,MAAM,EAAE;0BAAEiK,YAAY,EAAE;wBAAO,CAAC;sBAAC;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC/E7K,OAAA,CAAC3B,QAAQ;wBAACuG,KAAK,EAAC,cAAc;wBAAAkF,QAAA,EAAE7I,SAAS,CAAC,eAAe,EAAE;0BAAEiK,YAAY,EAAE;wBAAgB,CAAC;sBAAC;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACzG7K,OAAA,CAAC3B,QAAQ;wBAACuG,KAAK,EAAC,SAAS;wBAAAkF,QAAA,EAAE7I,SAAS,CAAC,UAAU,EAAE;0BAAEiK,YAAY,EAAE;wBAAW,CAAC;sBAAC;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC1F7K,OAAA,CAAC3B,QAAQ;wBAACuG,KAAK,EAAC,cAAc;wBAAAkF,QAAA,EAAE7I,SAAS,CAAC,eAAe,EAAE;0BAAEiK,YAAY,EAAE;wBAAgB,CAAC;sBAAC;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACzG7K,OAAA,CAAC3B,QAAQ;wBAACuG,KAAK,EAAC,WAAW;wBAAAkF,QAAA,EAAE7I,SAAS,CAAC,YAAY,EAAE;0BAAEiK,YAAY,EAAE;wBAAa,CAAC;sBAAC;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAChG7K,OAAA,CAAC3B,QAAQ;wBAACuG,KAAK,EAAC,kBAAkB;wBAAAkF,QAAA,EAAE7I,SAAS,CAAC,oBAAoB,EAAE;0BAAEiK,YAAY,EAAE;wBAAqB,CAAC;sBAAC;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7G,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACV7K,OAAA,CAAChC,GAAG;kBACH+P,EAAE,EAAE,CAAE;kBACNxD,SAAS,EAAE,KAAM;kBACjBxD,EAAE,EAAC,YAAY;kBAAA+C,QAAA,gBAEf9J,OAAA,CAAC/B,UAAU;oBACV6M,OAAO,EAAC,OAAO;oBACfpB,KAAK,EAAC,eAAe;oBAAAI,QAAA,EAEpB7I,SAAS,CAAC,kBAAkB,EAAE;sBAAEiK,YAAY,EAAE;oBAAmB,CAAC;kBAAC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACb7K,OAAA,CAAChC,GAAG;oBACHmH,OAAO,EAAC,MAAM;oBACdS,GAAG,EAAE,CAAE;oBACPmI,EAAE,EAAE,CAAE;oBAAAjE,QAAA,EAEL,CAAC,MAAM,EAAE,KAAK,CAAC,CAACsC,GAAG,CAAEC,IAAI,IAAK;sBAC9B;sBACA;sBACA,MAAMiC,YAAY,GAAGjO,eAAe,CAAC2J,MAAM,CAACuE,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACzH,EAAE,KAAK3F,aAAa,CAAC6G,QAAQ,CAAC;sBAC5F,MAAMwG,gBAAgB,GAAG,CAAAH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEjH,SAAS,KAAIzH,cAAc;;sBAElE;sBACA,MAAM8O,UAAU,GAAIrC,IAAI,KAAK,MAAM,IAAIoC,gBAAgB,KAAK,OAAO,IAC5DpC,IAAI,KAAK,KAAK,IAAIoC,gBAAgB,KAAK,SAAU;sBACxD,oBACCzO,OAAA,CAAC1B,MAAM;wBAEN4L,OAAO,EAAEA,CAAA,KAAMnI,SAAS,CAAC1B,eAAe,CAAC0G,EAAE,EAAEsF,IAAsB,CAAE;wBACrEvB,OAAO,EAAC,UAAU;wBAClBwC,IAAI,EAAC,OAAO;wBACZvD,EAAE,EAAE;0BACH9E,KAAK,EAAE,QAAQ;0BACfvB,MAAM,EAAE,MAAM;0BACd4B,OAAO,EAAE,WAAW;0BACpBM,GAAG,EAAE,MAAM;0BACXF,YAAY,EAAE,iBAAiB;0BAC/BiJ,MAAM,EACLD,UAAU,GACP,iCAAiC,GACjC,kCAAkC;0BACtCtH,eAAe,EACdsH,UAAU,GAAG,yBAAyB,GAAG,0BAA0B;0BACpEE,mBAAmB,EAAE,UAAU;0BAC/BlF,KAAK,EAAE,OAAO;0BACd,SAAS,EAAE;4BACVtC,eAAe,EACdsH,UAAU,GAAG,yBAAyB,GAAG;0BAC3C;wBACD,CAAE;wBAAA5E,QAAA,EAED7I,SAAS,CAACoL,IAAI,EAAE;0BAAEnB,YAAY,EAAEmB;wBAAK,CAAC;sBAAC,GAxBnCA,IAAI;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAyBF,CAAC;oBAEX,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV7K,OAAA,CAACzB,OAAO;UAAC+M,KAAK,EAAErK,SAAS,CAAC,kBAAkB,EAAE;YAAEiK,YAAY,EAAE;UAAmB,CAAC,CAAE;UAAApB,QAAA,eACnF9J,OAAA,CAAChC,GAAG;YAACqP,SAAS,EAAC,kBAAkB;YAAAvD,QAAA,eAChC9J,OAAA,CAAC7B,UAAU;cACV+L,OAAO,EAAEN,0BAA2B;cACpC0D,IAAI,EAAC,OAAO;cACZvD,EAAE,EAAE;gBACHrG,MAAM,EAAE,MAAM;gBACduB,KAAK,EAAE,MAAM;gBACbmC,eAAe,EAAE/G,eAAe,CAAC6E,KAAK,CAACkC,eAAe;gBACtDuH,MAAM,EAAE,iBAAiB;gBACzB9I,SAAS,EAAE;cACZ;YAAE;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV7K,OAAA,CAACzB,OAAO;UAAC+M,KAAK,EAAEhL,eAAe,GAAGW,SAAS,CAAC,2CAA2C,EAAE;YAAEiK,YAAY,EAAE;UAA4C,CAAC,CAAC,GAAGjK,SAAS,CAAC,eAAe,EAAE;YAAEiK,YAAY,EAAE;UAAgB,CAAC,CAAE;UAAApB,QAAA,eACvN9J,OAAA,CAAChC,GAAG;YAACqP,SAAS,EAAC,kBAAkB;YAAAvD,QAAA,eAChC9J,OAAA,CAAC7B,UAAU;cACV+L,OAAO,EAAEX,uBAAwB;cACjC+D,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAEjN,eAAgB;cAAAwJ,QAAA,eAE1B9J,OAAA;gBACCwK,uBAAuB,EAAE;kBAAEC,MAAM,EAAEtL;gBAAS,CAAE;gBAC9C+F,KAAK,EAAE;kBAAEuG,OAAO,EAAEnL,eAAe,GAAG,GAAG,GAAG;gBAAE;cAAE;gBAAAoK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAET;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACV7K,OAAA,CAACzB,OAAO;UAAC+M,KAAK,EAAErK,SAAS,CAAC,gBAAgB,EAAE;YAAEiK,YAAY,EAAE;UAAiB,CAAC,CAAE;UAAApB,QAAA,eAC/E9J,OAAA,CAAChC,GAAG;YAACqP,SAAS,EAAC,kBAAkB;YAAAvD,QAAA,eAChC9J,OAAA,CAAC7B,UAAU;cACV+L,OAAO,EAAEhB,mBAAoB;cAC7BoE,IAAI,EAAC,OAAO;cACZC,QAAQ,EACP,EAAA3M,gBAAA,GAAApB,cAAc,CAAE2C,KAAK,IAAKA,KAAK,CAAC0M,oBAAoB,CAAC,CAAC,CAAC,CAAC,cAAAjO,gBAAA,wBAAAC,qBAAA,GAAxDD,gBAAA,CAA0DkO,UAAU,cAAAjO,qBAAA,uBAApEA,qBAAA,CAAsE4F,MAAM,MAAK,CACjF;cAAAqD,QAAA,eAED9J,OAAA;gBACCwK,uBAAuB,EAAE;kBAAEC,MAAM,EAAErL;gBAAW,CAAE;gBAChD8F,KAAK,EAAE;kBACNuG,OAAO,EACN,EAAA3K,iBAAA,GAAAtB,cAAc,CAAE2C,KAAK,IAAKA,KAAK,CAAC0M,oBAAoB,CAAC,CAAC,CAAC,CAAC,cAAA/N,iBAAA,wBAAAC,qBAAA,GAAxDD,iBAAA,CAA0DgO,UAAU,cAAA/N,qBAAA,uBAApEA,qBAAA,CAAsE0F,MAAM,MAAK,CAAC,GAC/E,GAAG,GACH,CAAC;kBACL8E,aAAa,EAAE;gBAChB;gBACA;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACV7K,OAAA,CAAC9B,OAAO;MACP6I,EAAE,EAAC,eAAe;MAClBoB,IAAI,EAAEC,eAAgB;MACtBoF,QAAQ,EAAEnK,mBAAoB;MAC9B0I,OAAO,EAAEvC,sBAAuB;MAChCwC,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFiB,eAAe,EAAE;QAChBlB,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFwB,SAAS,EAAE;QACVC,IAAI,EAAE;UACL5D,EAAE,EAAE;YACH;YACA6D,MAAM,EAAE;UACT;QACD;MACD,CAAE;MAAA9D,QAAA,eAEF9J,OAAA,CAAChC,GAAG;QAAA8L,QAAA,gBACH9J,OAAA,CAACF,YAAY;UACZ4J,KAAK,EAAErJ,eAAe,CAAC6E,KAAK,CAACkC,eAAgB;UAC7C+D,QAAQ,EAAE1B;QAAkB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACF7K,OAAA;UAAA8J,QAAA,EACE;AACP;AACA;AACA;AACA;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACT5G,WAAW,iBACXjE,OAAA,CAACpB,0BAA0B;MAC1BsE,MAAM,EAAEe,WAAY;MACpB8K,gBAAgB,EAAEA,CAAA,KAAM7K,YAAY,CAAC,KAAK,CAAE;MAC5C8K,aAAa,EAAEzH;IAAyB;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CACD;EAAA,eACA,CAAC;AAEL,CAAC;AAACtK,EAAA,CA/0BIJ,YAAkG;EAAA,QAC9EtB,cAAc,EAYnCW,cAAc,EAkwBVA,cAAc,EAOZA,cAAc;AAAA;AAAAyP,EAAA,GAtxBnB9O,YAAkG;AAi1BxG,eAAeA,YAAY;AAAC,IAAA8O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}