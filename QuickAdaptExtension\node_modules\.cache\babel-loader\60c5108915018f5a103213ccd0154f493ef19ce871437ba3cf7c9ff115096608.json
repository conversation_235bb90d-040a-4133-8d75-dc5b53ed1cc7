{"ast": null, "code": "import React from\"react\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const VideoPlayer=_ref=>{let{videoFile,isMaximized}=_ref;const{t:translate}=useTranslation();if(!videoFile)return null;return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"video\",{width:\"100%\",height:\"100%\",controls:true,style:{borderRadius:\"8px\",objectFit:isMaximized?\"contain\":\"cover\"},children:[/*#__PURE__*/_jsx(\"source\",{src:videoFile,type:\"video/mp4\"}),translate(\"Your browser does not support the video tag.\",{defaultValue:\"Your browser does not support the video tag.\"})]})});};export default VideoPlayer;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "VideoPlayer", "_ref", "videoFile", "isMaximized", "t", "translate", "children", "width", "height", "controls", "style", "borderRadius", "objectFit", "src", "type", "defaultValue"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/VideoPlayer.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst VideoPlayer = ({ videoFile ,isMaximized}: { videoFile: string,isMaximized :any }) => {\r\n  const { t: translate } = useTranslation();\r\n  if (!videoFile) return null;\r\n\r\n  return (\r\n    <div>\r\n      <video\r\n        width=\"100%\"\r\n        height=\"100%\"\r\n        controls\r\n        style={{ borderRadius: \"8px\", objectFit: isMaximized?\"contain\":\"cover\" }}\r\n      >\r\n        <source src={videoFile} type=\"video/mp4\" />\r\n        {translate(\"Your browser does not support the video tag.\", { defaultValue: \"Your browser does not support the video tag.\" })}\r\n      </video>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoPlayer;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAuE,IAAtE,CAAEC,SAAS,CAAEC,WAAmD,CAAC,CAAAF,IAAA,CACpF,KAAM,CAAEG,CAAC,CAAEC,SAAU,CAAC,CAAGV,cAAc,CAAC,CAAC,CACzC,GAAI,CAACO,SAAS,CAAE,MAAO,KAAI,CAE3B,mBACEL,IAAA,QAAAS,QAAA,cACEP,KAAA,UACEQ,KAAK,CAAC,MAAM,CACZC,MAAM,CAAC,MAAM,CACbC,QAAQ,MACRC,KAAK,CAAE,CAAEC,YAAY,CAAE,KAAK,CAAEC,SAAS,CAAET,WAAW,CAAC,SAAS,CAAC,OAAQ,CAAE,CAAAG,QAAA,eAEzET,IAAA,WAAQgB,GAAG,CAAEX,SAAU,CAACY,IAAI,CAAC,WAAW,CAAE,CAAC,CAC1CT,SAAS,CAAC,8CAA8C,CAAE,CAAEU,YAAY,CAAE,8CAA+C,CAAC,CAAC,EACvH,CAAC,CACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAf,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}