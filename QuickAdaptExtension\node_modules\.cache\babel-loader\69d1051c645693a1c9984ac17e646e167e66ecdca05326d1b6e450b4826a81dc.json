{"ast": null, "code": "import React,{useContext}from'react';import{stopScraping}from'../../services/ScrapingService';import'./EnableAIButton.css';import{AccountContext}from'../../components/login/AccountContext';import{useTranslation}from'react-i18next';import{jsx as _jsx}from\"react/jsx-runtime\";const StopScrapingButton=_ref=>{let{onClick}=_ref;const{accountId}=useContext(AccountContext);const{t:translate}=useTranslation();const handleClick=async()=>{stopScraping(accountId);onClick();};;return/*#__PURE__*/_jsx(\"div\",{className:\"stop-scraping-button-container\",id:\"stop-scraping-button\",children:/*#__PURE__*/_jsx(\"button\",{className:\"enable-ai-button stop-scraping-button\",onClick:handleClick,children:/*#__PURE__*/_jsx(\"span\",{className:\"enable-ai-text\",children:translate(\"Stop Training\")})})});};export default StopScrapingButton;", "map": {"version": 3, "names": ["React", "useContext", "stopScraping", "AccountContext", "useTranslation", "jsx", "_jsx", "StopScrapingButton", "_ref", "onClick", "accountId", "t", "translate", "handleClick", "className", "id", "children"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AI/StopScrapingButton.tsx"], "sourcesContent": ["import React, { useContext } from 'react';\r\nimport { stopScraping } from '../../services/ScrapingService';\r\nimport './EnableAIButton.css';\r\nimport { AccountContext } from '../../components/login/AccountContext';\r\nimport { useTranslation } from 'react-i18next';\r\ninterface StopScrapingButtonProps {\r\n  onClick: () => void;\r\n}\r\n\r\nconst StopScrapingButton: React.FC<StopScrapingButtonProps> = ({ onClick }) => {\r\n  const { accountId } = useContext(AccountContext);\r\n  const { t: translate } = useTranslation()\r\n  const handleClick = async () => {\r\n    \r\n    stopScraping(accountId);\r\n    onClick();\r\n  };\r\n  \r\n  ;\r\n  \r\n  \r\n\r\n  return (\r\n    <div className='stop-scraping-button-container' id='stop-scraping-button'>\r\n      <button className=\"enable-ai-button stop-scraping-button\" onClick={handleClick}>\r\n        <span className=\"enable-ai-text\">{translate(\"Stop Training\")}</span>\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StopScrapingButton;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,KAAQ,OAAO,CACzC,OAASC,YAAY,KAAQ,gCAAgC,CAC7D,MAAO,sBAAsB,CAC7B,OAASC,cAAc,KAAQ,uCAAuC,CACtE,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAK/C,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CACxE,KAAM,CAAEE,SAAU,CAAC,CAAGT,UAAU,CAACE,cAAc,CAAC,CAChD,KAAM,CAAEQ,CAAC,CAAEC,SAAU,CAAC,CAAGR,cAAc,CAAC,CAAC,CACzC,KAAM,CAAAS,WAAW,CAAG,KAAAA,CAAA,GAAY,CAE9BX,YAAY,CAACQ,SAAS,CAAC,CACvBD,OAAO,CAAC,CAAC,CACX,CAAC,CAED,CAIA,mBACEH,IAAA,QAAKQ,SAAS,CAAC,gCAAgC,CAACC,EAAE,CAAC,sBAAsB,CAAAC,QAAA,cACvEV,IAAA,WAAQQ,SAAS,CAAC,uCAAuC,CAACL,OAAO,CAAEI,WAAY,CAAAG,QAAA,cAC7EV,IAAA,SAAMQ,SAAS,CAAC,gBAAgB,CAAAE,QAAA,CAAEJ,SAAS,CAAC,eAAe,CAAC,CAAO,CAAC,CAC9D,CAAC,CACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}