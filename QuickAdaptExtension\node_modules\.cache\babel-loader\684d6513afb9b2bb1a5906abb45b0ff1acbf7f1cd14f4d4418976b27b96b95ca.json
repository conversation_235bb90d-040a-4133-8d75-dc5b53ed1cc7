{"ast": null, "code": "import React,{useEffect,useState,useRef,useCallback}from\"react\";import{Button,Tooltip,Box,LinearProgress,Typography,tooltipClasses,MobileStepper,IconButton}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import{styled}from\"@mui/material/styles\";import useDrawerStore from\"../../../../store/drawerStore\";import PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CustomWidthTooltip=styled(_ref=>{let{className,canvasStyle,hasOnlyButtons,hasOnlyText,dynamicWidth,...props}=_ref;return/*#__PURE__*/_jsx(Tooltip,{...props,classes:{popper:className},id:\"Tooltip-unique\"});})(_ref2=>{let{canvasStyle,hasOnlyButtons,hasOnlyText,dynamicWidth}=_ref2;return{[`& .${tooltipClasses.tooltip}`]:{backgroundColor:(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BackgroundColor)||\"#ffffff\",// color: \"black\", // Set static text color\n//fontSize: \"14px\", // Font size\npadding:hasOnlyButtons?\"0px\":canvasStyle.Padding,// padding: \"0px !important\",\nborderRadius:(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.Radius)||(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderRadius)||\"10px\",boxShadow:\"0px 4px 12px rgba(0, 0, 0, 0.2)\",border:canvasStyle!==null&&canvasStyle!==void 0&&canvasStyle.BorderSize&&(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderSize)!==\"0px\"?`${canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderSize} solid ${(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderColor)||\"transparent\"}`:\"none\",// width: (hasOnlyButtons ) ? \"auto !important\" :\n// \t   canvasStyle?.Width ? `${canvasStyle.Width} !important` : \"300px\",\nwidth:'auto !important',maxWidth:canvasStyle!==null&&canvasStyle!==void 0&&canvasStyle.Width?`${canvasStyle.Width} !important`:\"300px\",left:`${(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.XAxisOffset)||\"auto\"} !important`,bottom:`${(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.YAxisOffset)||\"auto\"} !important`},[`& .${tooltipClasses.arrow}`]:{color:(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BackgroundColor)||\"#ffffff\",fontSize:\"16px\",\"&:before\":{outlineWidth:canvasStyle!==null&&canvasStyle!==void 0&&canvasStyle.BorderSize&&(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderSize)!==\"0px\"?`${canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderSize}`:\"0px\",// This controls the width of the border of the arrow\noutlineColor:(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderColor)||\"transparent\",// This controls the color of the border of the arrow\noutlineStyle:\"solid\"// Ensure the border is applied properly\n}},[`&.MuiTooltip-popper`]:{zIndex:(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.Zindex)||11000// Tooltip z-index\n}};});const TooltipGuide=_ref3=>{var _currentStepData$moda,_currentStepData$butt3,_currentStepData$imag6,_currentStepData$imag7,_currentStepData$butt4,_currentStepData$butt12,_currentStepData$butt13;let{steps,currentUrl,onClose,tooltipConfig,startStepIndex,data}=_ref3;let rect;const[currentStepIndex,setCurrentStepIndex]=useState(startStepIndex||1);const contentRef=useRef(null);const buttonContainerRef=useRef(null);// Auto-scroll related refs and state\nconst scrollOperationQueue=useRef(Promise.resolve());// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const scrollbarRef=useRef(null);useEffect(()=>{if(startStepIndex!==undefined){setCurrentStepIndex(startStepIndex);}},[startStepIndex]);const{setCurrentStep,selectedTemplate,currentStep,ProgressColor,createWithAI,pageinteraction}=useDrawerStore(state=>state);const currentStepData=steps[currentStepIndex-1];const[targetElement,setTargetElement]=useState(null);const[tooltipPosition,setTooltipPosition]=useState({top:0,left:0});const[tooltipPlacement,setTooltipPlacement]=useState(\"top\");const[isElementVisible,setIsElementVisible]=useState(false);const observerRef=useRef(null);// Add refs to store previous position values to prevent unnecessary updates\nconst prevPositionRef=useRef({top:0,left:0});const prevPlacementRef=useRef(\"top\");const calculateBestPosition=element=>{const rect=element.getBoundingClientRect();const viewportWidth=window.innerWidth;const viewportHeight=window.innerHeight;const spaceTop=rect.top;const spaceBottom=viewportHeight-rect.bottom;const spaceLeft=rect.left;const spaceRight=viewportWidth-rect.right;const maxSpace=Math.max(spaceTop,spaceBottom,spaceLeft,spaceRight);if(maxSpace===spaceTop)return\"top\";if(maxSpace===spaceBottom)return\"bottom\";if(maxSpace===spaceLeft)return\"left\";return\"right\";};// Removed unused isElementInViewport function\nconst validateElementPosition=element=>{const rect=element.getBoundingClientRect();return rect.width>0&&rect.height>0&&rect.top!==0&&rect.left!==0&&!Number.isNaN(rect.top)&&!Number.isNaN(rect.left);};const getElementByXPath=(xpath,PossibleElementPath)=>{// Validate XPath before using it\nif(!xpath||xpath.trim()===\"\"){console.log(\"XPath is empty or undefined, trying PossibleElementPath:\",PossibleElementPath);if(PossibleElementPath&&PossibleElementPath.trim()!==\"\"){try{const result=document.evaluate(PossibleElementPath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;}}catch(error){console.error(\"Error evaluating PossibleElementPath:\",PossibleElementPath,error);}}return null;}try{const query=`${xpath}[not(ancestor::div[@id='quickAdopt_banner']) and not(@id='quickAdopt_banner')]`;console.log(\"Evaluating XPath query:\",query);const result=document.evaluate(query,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;}else if(node===null){// Try PossibleElementPath as fallback\nif(PossibleElementPath&&PossibleElementPath.trim()!==\"\"){console.log(\"Primary XPath failed, trying PossibleElementPath:\",PossibleElementPath);const fallbackResult=document.evaluate(PossibleElementPath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const fallbackNode=fallbackResult.singleNodeValue;if(fallbackNode instanceof HTMLElement){return fallbackNode;}else if(fallbackNode!==null&&fallbackNode!==void 0&&fallbackNode.parentElement){return fallbackNode.parentElement;}}return null;}else{return null;}}catch(error){console.error(\"Error evaluating XPath:\",xpath,error);// Try PossibleElementPath as fallback\nif(PossibleElementPath&&PossibleElementPath.trim()!==\"\"){try{console.log(\"XPath evaluation failed, trying PossibleElementPath:\",PossibleElementPath);const result=document.evaluate(PossibleElementPath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;}}catch(fallbackError){console.error(\"Error evaluating PossibleElementPath:\",PossibleElementPath,fallbackError);}}return null;}};// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\nconst smoothScrollTo=function(element,targetTop){let duration=arguments.length>2&&arguments[2]!==undefined?arguments[2]:300;// Ensure targetTop is within valid bounds\nconst maxScroll=element.scrollHeight-element.clientHeight;const clampedTargetTop=Math.max(0,Math.min(targetTop,maxScroll));// Method 1: Try native smooth scrolling first\ntry{if('scrollTo'in element&&typeof element.scrollTo==='function'){element.scrollTo({top:clampedTargetTop,behavior:'smooth'});return;}}catch(error){console.log(\"Native smooth scrollTo failed, trying animation fallback\");}// Method 2: Manual animation fallback\ntry{const startTop=element.scrollTop;const distance=clampedTargetTop-startTop;const startTime=performance.now();const animateScroll=currentTime=>{const elapsed=currentTime-startTime;const progress=Math.min(elapsed/duration,1);// Easing function for smooth animation\nconst easeInOutCubic=t=>t<0.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1;const easedProgress=easeInOutCubic(progress);element.scrollTop=startTop+distance*easedProgress;if(progress<1){requestAnimationFrame(animateScroll);}};requestAnimationFrame(animateScroll);}catch(error){console.log(\"RequestAnimationFrame failed, using direct assignment\");// Method 3: Direct assignment as final fallback\nelement.scrollTop=clampedTargetTop;}};// Enhanced reusable element polling function with exponential backoff and better timing\nconst pollForElement=useCallback(function(xpath,possibleElementPath,onElementFound){let maxAttempts=arguments.length>3&&arguments[3]!==undefined?arguments[3]:30;let initialIntervalMs=arguments.length>4&&arguments[4]!==undefined?arguments[4]:16;let logPrefix=arguments.length>5&&arguments[5]!==undefined?arguments[5]:\"Element\";let onElementNotFound=arguments.length>6?arguments[6]:undefined;let elementCheckTimeout=null;let attempts=0;let currentInterval=initialIntervalMs;let isCleanedUp=false;const cleanup=()=>{if(elementCheckTimeout){clearTimeout(elementCheckTimeout);elementCheckTimeout=null;}isCleanedUp=true;};const checkForElement=()=>{if(isCleanedUp)return;attempts++;console.log(`${logPrefix}: Polling attempt ${attempts}/${maxAttempts} (interval: ${currentInterval}ms)`);const element=getElementByXPath(xpath,possibleElementPath);if(element&&validateElementPosition(element)){console.log(`${logPrefix}: Found and validated after ${attempts} attempts`);cleanup();onElementFound(element);return;}if(attempts>=maxAttempts){console.log(`${logPrefix}: Max attempts (${maxAttempts}) reached, element not found`);cleanup();if(onElementNotFound){onElementNotFound();}return;}// Exponential backoff with jitter to prevent thundering herd\nconst jitter=Math.random()*0.1;// 10% jitter\ncurrentInterval=Math.min(currentInterval*(1.2+jitter),1000);// Cap at 1 second\nelementCheckTimeout=setTimeout(checkForElement,currentInterval);};// Start the polling\ncheckForElement();// Return cleanup function\nreturn cleanup;},[]);// Enhanced cross-environment scrolling function\nconst universalScrollTo=(element,options)=>{const isWindow=element===window;const targetElement=isWindow?document.documentElement:element;// Method 1: Try native scrollTo if available and not blocked\nif(!isWindow&&'scrollTo'in element&&typeof element.scrollTo==='function'){try{element.scrollTo(options);return true;}catch(error){console.log(\"Native scrollTo blocked or failed:\",error);}}// Method 2: Try window.scrollTo for window element\nif(isWindow&&options.behavior==='smooth'){try{window.scrollTo(options);return true;}catch(error){console.log(\"Window scrollTo failed:\",error);}}// Method 3: Try smooth scrolling with custom animation\nif(options.behavior==='smooth'&&options.top!==undefined){try{smoothScrollTo(targetElement,options.top);return true;}catch(error){console.log(\"Smooth scroll animation failed:\",error);}}// Method 4: Direct property assignment (final fallback)\ntry{if(options.top!==undefined){targetElement.scrollTop=options.top;}if(options.left!==undefined){targetElement.scrollLeft=options.left;}return true;}catch(error){console.log(\"Direct property assignment failed:\",error);return false;}};const scrollToTargetElement=useCallback(async(targetElement,placement,stepData)=>{if(!targetElement){console.log(\"ScrollToTargetElement: No target element provided\");return;}console.log(\"🎯 Starting enhanced auto-scroll to target element:\",{element:targetElement,tagName:targetElement.tagName,className:targetElement.className,id:targetElement.id,placement:placement});try{// Add the scroll operation to the queue to prevent conflicts\nscrollOperationQueue.current=scrollOperationQueue.current.then(async()=>{const rect=targetElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;// Calculate optimal scroll position based on placement and viewport\nlet targetScrollTop=window.scrollY;let targetScrollLeft=window.scrollX;// Enhanced positioning logic based on tooltip placement\nswitch(placement){case\"top\":// Position element in lower third of viewport to leave room for tooltip above\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.7;break;case\"bottom\":// Position element in upper third of viewport to leave room for tooltip below\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.3;break;case\"left\":// Position element towards right side to leave room for tooltip on left\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.5;targetScrollLeft=window.scrollX+rect.left-viewportWidth*0.7;break;case\"right\":// Position element towards left side to leave room for tooltip on right\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.5;targetScrollLeft=window.scrollX+rect.left-viewportWidth*0.3;break;default:// Default: center the element vertically\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.5;}// Ensure scroll positions are within valid bounds\nconst maxScrollTop=Math.max(0,document.documentElement.scrollHeight-viewportHeight);const maxScrollLeft=Math.max(0,document.documentElement.scrollWidth-viewportWidth);targetScrollTop=Math.max(0,Math.min(targetScrollTop,maxScrollTop));targetScrollLeft=Math.max(0,Math.min(targetScrollLeft,maxScrollLeft));console.log(\"📍 Calculated scroll position:\",{targetScrollTop,targetScrollLeft,currentScrollY:window.scrollY,currentScrollX:window.scrollX,elementRect:rect});// Perform the scroll with multiple fallback methods\nlet scrollSuccess=false;// Method 1: Try smooth scrolling\ntry{scrollSuccess=universalScrollTo(window,{top:targetScrollTop,left:targetScrollLeft,behavior:'smooth'});console.log(\"✅ Universal scroll success:\",scrollSuccess);}catch(error){console.log(\"❌ Universal scroll failed:\",error);}// Method 2: Fallback to immediate scroll if smooth scroll failed\nif(!scrollSuccess){try{window.scrollTo(targetScrollLeft,targetScrollTop);scrollSuccess=true;console.log(\"✅ Fallback scroll successful\");}catch(error){console.log(\"❌ Fallback scroll failed:\",error);}}// Method 3: Final fallback using direct property assignment\nif(!scrollSuccess){try{document.documentElement.scrollTop=targetScrollTop;document.documentElement.scrollLeft=targetScrollLeft;document.body.scrollTop=targetScrollTop;document.body.scrollLeft=targetScrollLeft;console.log(\"✅ Direct property assignment completed\");}catch(error){console.log(\"❌ Direct property assignment failed:\",error);}}// Small delay to allow scroll to complete\nawait new Promise(resolve=>setTimeout(resolve,100));console.log(\"🏁 Auto-scroll operation completed\");});await scrollOperationQueue.current;}catch(error){console.error(\"❌ ScrollToTargetElement error:\",error);}},[smoothScrollTo,universalScrollTo]);const progress=currentStepIndex/steps.length*100;const interactWithPage=tooltipConfig===null||tooltipConfig===void 0?void 0:tooltipConfig.InteractWithPage;useEffect(()=>{if(currentStep&&currentStepIndex>=0&&interactWithPage===false){console.log('[Tooltips.tsx] Setting document.body.style.pointerEvents =',selectedTemplate===\"Tour\"?\"auto\":\"none\");document.body.style.pointerEvents=selectedTemplate===\"Tour\"?\"auto\":\"none\";const style=document.createElement('style');style.innerHTML=`.qadpt-editor  { pointer-events: auto !important; }`;document.head.appendChild(style);return()=>{console.log('[Tooltips.tsx] Resetting document.body.style.pointerEvents = auto');document.body.style.pointerEvents=\"auto\";document.head.removeChild(style);};}return()=>{// In case there's no active step or tooltip is closed\nconsole.log('[Tooltips.tsx] Cleanup: Resetting document.body.style.pointerEvents = auto');document.body.style.pointerEvents=\"auto\";// Enable interactions\n};},[currentStepIndex,interactWithPage]);// Handle overflow hidden based on overlay and page interaction settings\nuseEffect(()=>{if(currentStep&&currentStepIndex>=0&&currentStepData!==null&&currentStepData!==void 0&&currentStepData.overlay&&interactWithPage===false){document.body.style.overflow=\"hidden\";}else{document.body.style.overflow=\"\";}// Cleanup on unmount\nreturn()=>{document.body.style.overflow=\"\";};},[currentStepData===null||currentStepData===void 0?void 0:currentStepData.overlay,interactWithPage]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStepData,currentStep]);const updateTargetAndPosition=()=>{// if (currentUrl !== currentStep?.targetUrl) {\n// \tsetTargetElement(null);\n// \tsetIsElementVisible(false);\n// \treturn;\n// }\n// Debug logging for XPath data\nconsole.log(\"Tooltip updateTargetAndPosition - currentStepData:\",{xpath:currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath,PossibleElementPath:currentStepData===null||currentStepData===void 0?void 0:currentStepData.PossibleElementPath,stepIndex:currentStepIndex,createWithAI:createWithAI});const element=getElementByXPath(currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath,currentStepData===null||currentStepData===void 0?void 0:currentStepData.PossibleElementPath);if(!element){console.log(\"Tooltip element not found for XPath:\",currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath,\"PossibleElementPath:\",currentStepData===null||currentStepData===void 0?void 0:currentStepData.PossibleElementPath);setTargetElement(null);setIsElementVisible(false);return;}const isValid=validateElementPosition(element);//const isVisible = isElementInViewport(element);\nif(!isValid){setTargetElement(null);setIsElementVisible(false);return;}const rect=element.getBoundingClientRect();const xOffset=parseFloat((currentStepData===null||currentStepData===void 0?void 0:currentStepData.positionXAxisOffset)||\"0\");const yOffset=parseFloat((currentStepData===null||currentStepData===void 0?void 0:currentStepData.positionYAxisOffset)||\"0\");setTargetElement(element);setIsElementVisible(true);// Calculate placement\nlet newPlacement;if(currentStepData!==null&&currentStepData!==void 0&&currentStepData.autoposition){newPlacement=calculateBestPosition(element);}else{var _currentStepData$canv;const validPlacements=[\"top\",\"left\",\"right\",\"bottom\"];const placement=(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$canv=currentStepData.canvas)===null||_currentStepData$canv===void 0?void 0:_currentStepData$canv.Position)||\"bottom\";newPlacement=validPlacements.includes(placement)?placement:\"bottom\";}if(prevPlacementRef.current!==newPlacement){prevPlacementRef.current=newPlacement;setTooltipPlacement(newPlacement);}const newPosition={top:Math.round(rect.top+window.scrollY+yOffset),left:Math.round(rect.left+window.scrollX+xOffset)};const positionChanged=Math.abs(prevPositionRef.current.top-newPosition.top)>1||Math.abs(prevPositionRef.current.left-newPosition.left)>1;if(positionChanged){prevPositionRef.current=newPosition;setTooltipPosition(newPosition);}};// Enhanced auto-scroll navigation system for tooltip next functionality\nconst handleNext=useCallback(async()=>{if(selectedTemplate!==\"Tour\"){// Calculate the next index first (currentStepIndex is 1-based, so we need to check against steps.length)\nconst nextIndex=currentStepIndex+1;if(nextIndex<=steps.length){// Get the next step data for scrolling (convert to 0-based for array access)\nconst nextStepData=steps[nextIndex-1];console.log(\"🔍 Starting navigation to next step:\",{currentIndex:currentStepIndex,nextIndex:nextIndex,xpath:nextStepData===null||nextStepData===void 0?void 0:nextStepData.xpath,stepTitle:`Step ${nextIndex}`});// Smart auto-scroll: Only scroll if element is not reasonably visible\nif(nextStepData!==null&&nextStepData!==void 0&&nextStepData.xpath){console.log(\"🔍 Checking auto-scroll for next element:\",{xpath:nextStepData.xpath,stepIndex:nextIndex,stepTitle:`Step ${nextIndex}`});// Create a promise to handle element finding and scrolling\nconst scrollPromise=new Promise(resolve=>{// Use polling to find the element\npollForElement(nextStepData.xpath||\"\",nextStepData.PossibleElementPath||\"\",async foundElement=>{console.log(\"✅ Next element found:\",{element:foundElement,tagName:foundElement.tagName,className:foundElement.className,id:foundElement.id});// Check if element needs scrolling\nconst rect=foundElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;// More generous visibility check - element should be reasonably visible\nconst isReasonablyVisible=rect.top>=-50&&// Allow some element to be above viewport\nrect.left>=-50&&// Allow some element to be left of viewport\nrect.bottom<=viewportHeight+50&&// Allow some element below viewport\nrect.right<=viewportWidth+50&&// Allow some element right of viewport\nrect.width>0&&rect.height>0;if(isReasonablyVisible){console.log(\"✅ Element is reasonably visible, minimal adjustment\");// Element is mostly visible, just ensure it's well positioned\ntry{foundElement.scrollIntoView({behavior:'smooth',block:'nearest',// Don't force center if already visible\ninline:'nearest'});}catch(error){console.log(\"Minimal scroll adjustment failed:\",error);}}else{console.log(\"🎯 Element not visible, performing auto-scroll\");// Element is not visible, scroll to bring it into view\ntry{foundElement.scrollIntoView({behavior:'smooth',block:'center',// Center it for better visibility\ninline:'nearest'});console.log(\"✅ Auto-scroll completed successfully\");}catch(scrollError){console.error(\"❌ Auto-scroll failed:\",scrollError);}}resolve();},20,// Reasonable maxAttempts\n30,// Reasonable initial interval\n\"Next step element\",()=>{console.log(\"❌ Next element not found after polling, continuing without scroll\");resolve();});});try{await scrollPromise;console.log(\"✅ Element finding and scroll check completed\");}catch(error){console.error(\"❌ Scroll promise failed:\",error);}}else{console.log(\"ℹ️ No xpath provided for next step, skipping auto-scroll\");}// Update the step index AFTER scrolling is complete\nconsole.log(\"🔄 Updating step index from\",currentStepIndex,\"to\",nextIndex);setCurrentStepIndex(nextIndex);setCurrentStep(currentStep+1);// Small delay to allow DOM to update after step change\nawait new Promise(resolve=>setTimeout(resolve,50));}else{console.log(\"🏁 Reached end of tooltip steps\",{currentStepIndex,nextIndex,totalSteps:steps.length});// onClose(); // Close tooltip if no more steps\nreturn;}}else{// Tour template logic (consistent with 1-based indexing)\nif(currentStep<(steps===null||steps===void 0?void 0:steps.length)){setCurrentStepIndex(prev=>Math.max(prev+1,1));setCurrentStep(currentStep+1);}}},[currentStepIndex,steps,selectedTemplate,currentStep,pollForElement,calculateBestPosition,scrollToTargetElement,setCurrentStep,setCurrentStepIndex]);// Enhanced handlePrevious with auto-scroll functionality\nconst handlePrevious=useCallback(async()=>{if(selectedTemplate!==\"Tour\"){const prevIndex=Math.max(currentStepIndex-1,1);if(prevIndex>=1){console.log(\"🔙 Navigating to previous step:\",{currentIndex:currentStepIndex,prevIndex:prevIndex});// Smart previous navigation scrolling\nif(prevIndex===1){console.log(\"HandlePrevious: Going back to first step, scroll to top\");try{window.scrollTo({top:0,behavior:'smooth'});}catch(error){console.log(\"HandlePrevious: Scroll to top failed:\",error);}}else{// For other steps, check if previous step element needs scrolling\nconst prevStepData=steps[prevIndex-1];if(prevStepData!==null&&prevStepData!==void 0&&prevStepData.xpath){console.log(\"HandlePrevious: Checking if previous step element needs scrolling\");setTimeout(()=>{const prevElement=getElementByXPath(prevStepData.xpath,prevStepData.PossibleElementPath||\"\");if(prevElement){const rect=prevElement.getBoundingClientRect();const isOutOfView=rect.bottom<0||rect.top>window.innerHeight||rect.right<0||rect.left>window.innerWidth;if(isOutOfView){console.log(\"HandlePrevious: Previous element out of view, scrolling\");try{prevElement.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});}catch(error){console.log(\"HandlePrevious: Element scroll failed:\",error);}}}},100);}}// Update step index\nsetCurrentStepIndex(prevIndex);setCurrentStep(currentStep-1);// Small delay to allow DOM to update\nawait new Promise(resolve=>setTimeout(resolve,50));}}else{setCurrentStep(currentStep-1);}},[currentStepIndex,selectedTemplate,currentStep,universalScrollTo,setCurrentStep,setCurrentStepIndex]);useEffect(()=>{var _currentStepData$elem,_currentStepData$elem2,_currentStepData$elem3,_currentStepData$elem4;// Debug logging for AI tooltip button click functionality\nconsole.log(\"🔍 Tooltip useEffect - Element click setup:\",{currentStepIndex,elementclick:currentStepData===null||currentStepData===void 0?void 0:currentStepData.elementclick,NextStep:currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem=currentStepData.elementclick)===null||_currentStepData$elem===void 0?void 0:_currentStepData$elem.NextStep,Id:currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem2=currentStepData.elementclick)===null||_currentStepData$elem2===void 0?void 0:_currentStepData$elem2.Id,xpath:currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath});if((currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem3=currentStepData.elementclick)===null||_currentStepData$elem3===void 0?void 0:_currentStepData$elem3.NextStep)===\"element\"||(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem4=currentStepData.elementclick)===null||_currentStepData$elem4===void 0?void 0:_currentStepData$elem4.NextStep)===\"button\"){const element=getElementByXPath(currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath,currentStepData===null||currentStepData===void 0?void 0:currentStepData.PossibleElementPath);if(element){console.log(\"✅ Element found for click handler:\",element);const handleClick=()=>{console.log(\"🖱️ Element clicked - advancing to next step\");handleNext();};element.addEventListener(\"click\",handleClick);return()=>{element.removeEventListener(\"click\",handleClick);};}else{console.log(\"❌ Element not found for xpath:\",currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath);}}else{var _currentStepData$elem5;console.log(\"ℹ️ No element click setup - NextStep:\",currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem5=currentStepData.elementclick)===null||_currentStepData$elem5===void 0?void 0:_currentStepData$elem5.NextStep);}},[currentStepData,handleNext]);useEffect(()=>{const handleDOMChanges=()=>{requestAnimationFrame(updateTargetAndPosition);};observerRef.current=new MutationObserver(handleDOMChanges);const targetNode=document.body;observerRef.current.observe(targetNode,{childList:true,subtree:true,attributes:true,characterData:true});updateTargetAndPosition();return()=>{var _observerRef$current;(_observerRef$current=observerRef.current)===null||_observerRef$current===void 0?void 0:_observerRef$current.disconnect();};},[currentStepData,currentUrl]);useEffect(()=>{const handleViewportChanges=()=>{requestAnimationFrame(updateTargetAndPosition);};window.addEventListener(\"scroll\",handleViewportChanges);window.addEventListener(\"resize\",handleViewportChanges);return()=>{window.removeEventListener(\"scroll\",handleViewportChanges);window.removeEventListener(\"resize\",handleViewportChanges);};},[currentStepData,currentUrl]);useEffect(()=>{updateTargetAndPosition();},[currentStepData,currentUrl,rect]);// Ensure all dependencies are included\n// Smart auto-scroll for current step changes - only when element is not visible\nuseEffect(()=>{if(!(currentStepData!==null&&currentStepData!==void 0&&currentStepData.xpath)){return;}// Small delay to allow DOM to settle\nconst timeoutId=setTimeout(()=>{const currentElement=getElementByXPath(currentStepData.xpath,currentStepData.PossibleElementPath||\"\");if(currentElement){const rect=currentElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;// Check if element is completely out of view\nconst isCompletelyOutOfView=rect.bottom<0||// Completely above viewport\nrect.top>viewportHeight||// Completely below viewport\nrect.right<0||// Completely left of viewport\nrect.left>viewportWidth// Completely right of viewport\n;if(isCompletelyOutOfView){console.log(\"🔄 Current step element is out of view, gentle auto-scroll\");try{currentElement.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});}catch(error){console.log(\"Current step auto-scroll failed:\",error);}}}},100);return()=>{clearTimeout(timeoutId);};},[currentStepData]);const canvasStyle=(currentStepData===null||currentStepData===void 0?void 0:currentStepData.canvas)||{};// Assuming canvas is an array, take the first item\nconst enableProgress=tooltipConfig.EnableProgress||false;function getProgressTemplate(tooltipConfig){if((tooltipConfig===null||tooltipConfig===void 0?void 0:tooltipConfig.ProgressTemplate)===\"1\"){return\"dots\";}else if((tooltipConfig===null||tooltipConfig===void 0?void 0:tooltipConfig.ProgressTemplate)===\"2\"){return\"linear\";}else if((tooltipConfig===null||tooltipConfig===void 0?void 0:tooltipConfig.ProgressTemplate)===\"3\"){return\"BreadCrumbs\";}else{return\"breadcrumbs\";}}const progressTemplate=getProgressTemplate(tooltipConfig);const enabelCross=currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$moda=currentStepData.modal)===null||_currentStepData$moda===void 0?void 0:_currentStepData$moda.DismissOption;const renderContent=()=>{var _currentStepData$imag,_currentStepData$imag2,_currentStepData$imag3,_currentStepData$imag4,_currentStepData$imag5;const hasImage=(currentStepData===null||currentStepData===void 0?void 0:currentStepData.imageUrl.startsWith(\"data:image/\"))||(currentStepData===null||currentStepData===void 0?void 0:currentStepData.imageUrl.startsWith(\"http\"));const hasText=Array.isArray(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)?currentStepData.content.some(item=>(item===null||item===void 0?void 0:item.Text)&&typeof item.Text===\"string\"&&item.Text.trim()!==\"\"):typeof(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)===\"string\"||/*#__PURE__*/React.isValidElement(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content);const textStyle={fontSize:\"14px\",lineHeight:\"1.5\",whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",color:\"black\"};return/*#__PURE__*/_jsxs(Box,{children:[hasImage&&/*#__PURE__*/_jsx(Box,{component:\"img\",src:currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$imag=currentStepData.imageproperties)===null||_currentStepData$imag===void 0?void 0:_currentStepData$imag.Url,alt:(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$imag2=currentStepData.imageproperties)===null||_currentStepData$imag2===void 0?void 0:_currentStepData$imag2.AltText)||\"Step Image\",sx:{backgroundColor:(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$imag3=currentStepData.imageproperties)===null||_currentStepData$imag3===void 0?void 0:_currentStepData$imag3.BackgroundColor)||\"transparent\",objectFit:(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$imag4=currentStepData.imageproperties)===null||_currentStepData$imag4===void 0?void 0:_currentStepData$imag4.Fit)||\"cover\",maxHeight:(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$imag5=currentStepData.imageproperties)===null||_currentStepData$imag5===void 0?void 0:_currentStepData$imag5.SectionHeight)||\"auto\",width:\"100%\"}}),hasText&&/*#__PURE__*/_jsx(Box,{className:\"qadpt-preview\",sx:{margin:\"0 !important\",...textStyle,\"& p\":{margin:\"4px 0\"}},dangerouslySetInnerHTML:{__html:Array.isArray(currentStepData.content)?currentStepData.content.map(item=>item.Text.replace(/<a /g,'<a target=\"_blank\" rel=\"noopener noreferrer\" ')).join(\"<br/>\"):typeof currentStepData.content===\"string\"?currentStepData.content.replace(/<a /g,'<a target=\"_blank\" rel=\"noopener noreferrer\" '):\"\"}})]});};// Helper function to check if tooltip has only buttons (no text or images)\nconst hasOnlyButtons=()=>{var _currentStepData$butt;const hasImage=(currentStepData===null||currentStepData===void 0?void 0:currentStepData.imageUrl.startsWith(\"data:image/\"))||(currentStepData===null||currentStepData===void 0?void 0:currentStepData.imageUrl.startsWith(\"http\"));const hasText=Array.isArray(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)?currentStepData.content.some(item=>(item===null||item===void 0?void 0:item.Text)&&typeof item.Text===\"string\"&&item.Text.trim()!==\"\"):typeof(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)===\"string\"||/*#__PURE__*/React.isValidElement(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content);const hasButtons=(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt=currentStepData.buttonData)===null||_currentStepData$butt===void 0?void 0:_currentStepData$butt.length)>0;return hasButtons&&!hasImage&&!hasText;};// Helper function to check if tooltip has only text (no buttons or images)\nconst hasOnlyText=()=>{var _currentStepData$butt2;const hasImage=(currentStepData===null||currentStepData===void 0?void 0:currentStepData.imageUrl.startsWith(\"data:image/\"))||(currentStepData===null||currentStepData===void 0?void 0:currentStepData.imageUrl.startsWith(\"http\"));const hasText=Array.isArray(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)?currentStepData.content.some(item=>(item===null||item===void 0?void 0:item.Text)&&typeof item.Text===\"string\"&&item.Text.trim()!==\"\"):typeof(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)===\"string\"||/*#__PURE__*/React.isValidElement(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content);const hasButtons=(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt2=currentStepData.buttonData)===null||_currentStepData$butt2===void 0?void 0:_currentStepData$butt2.length)>0;return hasText&&!hasImage&&!hasButtons;};const hasHtmlMeaningfulContent=htmlContent=>{if(!htmlContent||htmlContent.trim()===''){return false;}// Clean up common empty HTML patterns before checking\nlet cleanedContent=htmlContent;// Remove empty paragraph tags\ncleanedContent=cleanedContent.replace(/<p>\\s*(&nbsp;)*\\s*<\\/p>/gi,'');// Remove empty div tags\ncleanedContent=cleanedContent.replace(/<div>\\s*(&nbsp;)*\\s*<\\/div>/gi,'');// Remove empty span tags\ncleanedContent=cleanedContent.replace(/<span>\\s*(&nbsp;)*\\s*<\\/span>/gi,'');// Remove <br> tags\ncleanedContent=cleanedContent.replace(/<br\\s*\\/?>/gi,'');// Remove &nbsp; entities\ncleanedContent=cleanedContent.replace(/&nbsp;/gi,' ');// If after cleaning there's no content left, return false\nif(cleanedContent.trim()===''){return false;}// Create a temporary div to parse the cleaned HTML content\nconst tempDiv=document.createElement('div');tempDiv.innerHTML=cleanedContent;// Get the text content (strips all HTML tags)\nconst textContent=tempDiv.textContent||tempDiv.innerText;// Check if there's any non-whitespace text content\nif(textContent===null||textContent.trim()===''){return false;}// Additional check for common empty HTML patterns\n// This handles cases like \"<div><br></div>\" or \"<p>&nbsp;</p>\" that might appear non-empty\nconst lowerContent=cleanedContent.toLowerCase();const emptyPatterns=['<div><br></div>','<p><br></p>','<div></div>','<p></p>','<span></span>','<p>&nbsp;</p>','<div>&nbsp;</div>','<p> </p>','<div> </div>'];if(emptyPatterns.some(pattern=>lowerContent.includes(pattern))&&textContent.trim().length<=1){return false;}return true;};const hasValidTextContent=typeof(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)===\"string\"&&hasHtmlMeaningfulContent(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)||/*#__PURE__*/React.isValidElement(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)||Array.isArray(currentStepData===null||currentStepData===void 0?void 0:currentStepData.content)&&currentStepData.content.some(item=>(item===null||item===void 0?void 0:item.Text)&&typeof item.Text===\"string\"&&hasHtmlMeaningfulContent(item.Text));// Check if there are buttons\nconst hasButtons=(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt3=currentStepData.buttonData)===null||_currentStepData$butt3===void 0?void 0:_currentStepData$butt3.length)>0;//Check if there's a valid image\nconst hasValidImage=(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$imag6=currentStepData.imageUrl)===null||_currentStepData$imag6===void 0?void 0:_currentStepData$imag6.startsWith(\"data:image/\"))||(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$imag7=currentStepData.imageUrl)===null||_currentStepData$imag7===void 0?void 0:_currentStepData$imag7.startsWith(\"http\"));// Check if there's only text content (no images or buttons)\nconst hasOnlyTextContent=hasValidTextContent&&!hasValidImage&&!hasButtons;// Check if there's only a button (no text or images)\nconst hasOnlyButton=hasButtons&&!hasValidTextContent&&!hasValidImage&&(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt4=currentStepData.buttonData)===null||_currentStepData$butt4===void 0?void 0:_currentStepData$butt4.length)===1;// Check if there's any meaningful content to display\nconst hasValidContent=hasValidTextContent||hasValidImage;//Function to determine padding based on content and buttons\nconst getPadding=()=>{var _currentStepData$butt5,_currentStepData$butt6,_currentStepData$butt7,_currentStepData$butt8,_currentStepData$butt9;// Check if we have exactly one button and it's a previous button\nconst hasPreviousButton=(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt5=currentStepData.buttonData)===null||_currentStepData$butt5===void 0?void 0:_currentStepData$butt5.length)===1&&(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt6=currentStepData.buttonData)===null||_currentStepData$butt6===void 0?void 0:(_currentStepData$butt7=_currentStepData$butt6[0])===null||_currentStepData$butt7===void 0?void 0:(_currentStepData$butt8=_currentStepData$butt7.ButtonAction)===null||_currentStepData$butt8===void 0?void 0:(_currentStepData$butt9=_currentStepData$butt8.Action)===null||_currentStepData$butt9===void 0?void 0:_currentStepData$butt9.toLocaleLowerCase())===\"previous\";// Special case for previous button\nif(hasPreviousButton){return\"0px\";}// Original logic\nif(!hasValidContent){var _currentStepData$butt10;return(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt10=currentStepData.buttonData)===null||_currentStepData$butt10===void 0?void 0:_currentStepData$butt10.length)===1?\"0px\":\"4px\";}else{return\"0px\";}};// Function to calculate the optimal width based on content and buttons\n// const calculateOptimalWidth = () => {\n// \t// If we have a fixed width from canvas settings and not a compact tooltip, use that\n// \tif (canvasStyle?.Width && !hasOnlyButtons() && !hasOnlyText()) {\n// \t\treturn `${canvasStyle.Width}`;\n// \t}\n// \t// For tooltips with only buttons or only text, use auto width\n// \tif (hasOnlyButtons() || hasOnlyText()) {\n// \t\treturn \"auto\";\n// \t}\n// \t// Get the width of content and button container\n// \tconst contentWidth = contentRef.current?.scrollWidth || 0;\n// \tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\n// \t// Use the larger of the two, with some minimum and maximum constraints\n// \tconst optimalWidth = Math.max(contentWidth, buttonWidth);\n// \t// Add some padding to ensure text has room to wrap naturally\n// \tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\n// \t// Ensure width is between reasonable bounds\n// \tconst minWidth = 250; // Minimum width\n// \tconst maxWidth = 800; // Maximum width\n// \tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\n// \treturn `${finalWidth}px`;\n// };\n// Update dynamic width when content or buttons change\n// useEffect(() => {\n// \t// Use requestAnimationFrame to ensure DOM has been updated\n// \trequestAnimationFrame(() => {\n// \t\tconst newWidth = calculateOptimalWidth();\n// \t\tsetDynamicWidth(newWidth);\n// \t});\n// }, [currentStepData, currentStepIndex]);\nconst renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:steps.length,position:\"static\",activeStep:currentStepIndex-1,sx:{backgroundColor:\"transparent\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n},placeContent:\"center\",padding:\"2px  !important\",\"& .MuiMobileStepper-dot\":{width:\"6px !important\",height:\"6px !important\"}},backButton:/*#__PURE__*/_jsx(Button,{style:{display:\"none\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{display:\"none\"}})});}// if (progressTemplate === \"breadcrumbs\") {\n// \treturn (\n// \t\t<Breadcrumbs\n// \t\t\taria-label=\"breadcrumb\"\n// \t\t\tsx={{ marginTop: \"10px\" }}\n// \t\t>\n// \t\t\t{steps.map((_, index) => (\n// \t\t\t\t<Typography\n// \t\t\t\t\tkey={index}\n// \t\t\t\t\tcolor={index === currentStepIndex ? \"primary\" : \"text.secondary\"}\n// \t\t\t\t>\n// \t\t\t\t\tStep {index + 1} of {steps.length}\n// \t\t\t\t</Typography>\n// \t\t\t))}\n// \t\t</Breadcrumbs>\n// \t);\n// }\nif(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\",padding:\"8px\"},children:Array.from({length:steps.length}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:'14px',height:'4px',backgroundColor:index===currentStep-1?ProgressColor:'#e0e0e0',// Active color and inactive color\nborderRadius:'100px'}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{paddingTop:\"8px\"},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{padding:\"8px\",color:ProgressColor},children:[\"Step \",currentStepIndex,\" of \",steps.length]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{sx:{padding:hasOnlyButtons()?\"8px\":\"0\"},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:\"6px\",borderRadius:\"20px\",margin:hasOnlyButtons()?\"0\":\"6px 10px\",'& .MuiLinearProgress-bar':{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};const renderButtons=()=>{var _currentStepData$butt11;return(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt11=currentStepData.buttonData)===null||_currentStepData$butt11===void 0?void 0:_currentStepData$butt11.length)>0?currentStepData.buttonData.map((button,index)=>{const buttonStyle={backgroundColor:button.ButtonProperties.ButtonBackgroundColor,color:button.ButtonProperties.ButtonTextColor,border:button.ButtonProperties.ButtonBorderColor,padding:\"4px 8px !important\",lineHeight:\"normal\",width:\"auto\",fontSize:\"12px\",fontFamily:\"Poppins\",borderRadius:\"8px\",textTransform:\"none\",minWidth:\"fit-content\",boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{boxShadow:\"none !important\",// Remove box shadow in hover state\nbackgroundColor:button.ButtonProperties.ButtonBackgroundColor,// Keep the same background color on hover\nopacity:0.9// Slightly reduce opacity on hover for visual feedback\n}};const handleClick=()=>{var _currentStepData$elem6,_currentStepData$elem7,_currentStepData$elem8,_currentStepData$elem9,_currentStepData$elem10,_currentStepData$elem11;console.log(\"🔍 Button clicked:\",{buttonId:button.Id,buttonAction:button.ButtonAction.Action,elementclick:currentStepData===null||currentStepData===void 0?void 0:currentStepData.elementclick,NextStep:currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem6=currentStepData.elementclick)===null||_currentStepData$elem6===void 0?void 0:_currentStepData$elem6.NextStep,expectedButtonId:currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem7=currentStepData.elementclick)===null||_currentStepData$elem7===void 0?void 0:_currentStepData$elem7.Id});if(button.ButtonAction.Action.toLocaleLowerCase()===\"close\"){//onClose();\n}else if(button.ButtonAction.Action.toLocaleLowerCase()===\"next\"&&(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem8=currentStepData.elementclick)===null||_currentStepData$elem8===void 0?void 0:_currentStepData$elem8.NextStep)!==\"button\"){console.log(\"🚀 Regular next button - advancing step\");handleNext();}else if(button.ButtonAction.Action.toLocaleLowerCase()===\"next\"&&(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem9=currentStepData.elementclick)===null||_currentStepData$elem9===void 0?void 0:_currentStepData$elem9.NextStep)===\"button\"&&(button.Id===(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem10=currentStepData.elementclick)===null||_currentStepData$elem10===void 0?void 0:_currentStepData$elem10.ButtonId)||button.Id===(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem11=currentStepData.elementclick)===null||_currentStepData$elem11===void 0?void 0:_currentStepData$elem11.Id))){var _currentStepData$elem12,_currentStepData$elem13;console.log(\"🎯 Button click with element interaction - clicking element and advancing\",{buttonId:button.Id,expectedButtonId:currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem12=currentStepData.elementclick)===null||_currentStepData$elem12===void 0?void 0:_currentStepData$elem12.ButtonId,expectedId:currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$elem13=currentStepData.elementclick)===null||_currentStepData$elem13===void 0?void 0:_currentStepData$elem13.Id,elementclick:currentStepData===null||currentStepData===void 0?void 0:currentStepData.elementclick,stepIndex:currentStepIndex,currentStepData:currentStepData,xpath:currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath,possibleElementPath:currentStepData===null||currentStepData===void 0?void 0:currentStepData.PossibleElementPath});const element=getElementByXPath(currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath,currentStepData===null||currentStepData===void 0?void 0:currentStepData.PossibleElementPath);if(element){console.log(\"✅ Element found, clicking it:\",element);element.click();handleNext();}else{console.log(\"❌ Element not found for button click interaction\",{xpath:currentStepData===null||currentStepData===void 0?void 0:currentStepData.xpath,possibleElementPath:currentStepData===null||currentStepData===void 0?void 0:currentStepData.PossibleElementPath,stepIndex:currentStepIndex});}}else if(button.ButtonAction.Action===\"previous\"){handlePrevious();}else if(button.ButtonAction.Action===\"restart\"||button.ButtonAction.Action===\"Restart\"){var _steps$;// Enhanced restart functionality with auto-scroll\nconsole.log(\"🔄 Restarting tooltip guide\");// Reset to the first step (1-based indexing)\nsetCurrentStepIndex(1);setCurrentStep(1);// Check if we need to navigate to a different page (multi-page) or stay on current page (single-page)\nif((_steps$=steps[0])!==null&&_steps$!==void 0&&_steps$.targetUrl&&steps[0].targetUrl.trim()!==window.location.href.trim()){// Multi-page: Navigate to the first step's URL\nwindow.location.href=steps[0].targetUrl;}else{var _steps$2;// Single-page: Gentle scroll to first step element or top\nif((_steps$2=steps[0])!==null&&_steps$2!==void 0&&_steps$2.xpath){var _steps$3;console.log(\"🎯 Gentle scroll to first step element on restart\");// Check if first step element exists and use gentle scroll\nconst firstElement=getElementByXPath(steps[0].xpath,((_steps$3=steps[0])===null||_steps$3===void 0?void 0:_steps$3.PossibleElementPath)||\"\");if(firstElement){try{firstElement.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});console.log(\"✅ Gentle restart scroll completed\");}catch(error){console.log(\"❌ Gentle restart scroll failed, scrolling to top\");window.scrollTo({top:0,behavior:'smooth'});}}else{console.log(\"❌ First step element not found, scrolling to top\");window.scrollTo({top:0,behavior:'smooth'});}}else{// No xpath available, just scroll to top\nconsole.log(\"ℹ️ No xpath for first step, scrolling to top\");window.scrollTo({top:0,behavior:'smooth'});}}}else if(button.ButtonAction.Action===\"open-url\"&&button.ButtonAction.ActionValue===\"new-tab\"){window.open(button.ButtonAction.TargetUrl,\"_blank\");}else if(button.ButtonAction.Action===\"open-url\"&&button.ButtonAction.ActionValue===\"same-tab\"){window.location.href=button.ButtonAction.TargetUrl;}else{//onClose();\n}};return/*#__PURE__*/_jsx(Button,{variant:\"contained\",sx:buttonStyle,onClick:handleClick,children:button.ButtonName},index);}):null;};const TooltipContent=/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:enabelCross&&/*#__PURE__*/_jsx(IconButton,{sx:{position:\"absolute\",boxShadow:\"rgba(0, 0, 0, 0.06) 0px 4px 8px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999\",borderRadius:\"50px\",padding:\"1px !important\",float:\"right\",top:\"-12px\",right:\"-12px\",margin:canvasStyle!==null&&canvasStyle!==void 0&&canvasStyle.BorderSize&&(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderSize)!==\"0px\"?`-${parseInt(canvasStyle===null||canvasStyle===void 0?void 0:canvasStyle.BorderSize)-3}px`:\"0px\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:\"1\",color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:\"270px\"},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsx(\"div\",{ref:contentRef,style:{// maxHeight: \"270px\",\noverflow:\"hidden\",borderRadius:\"4px\",padding:getPadding(),position:\"relative\",zIndex:\"999\"// border: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || \"transparent\"}` : \"none\",\n},children:/*#__PURE__*/_jsxs(Box,{children:[!hasOnlyButtons()&&/*#__PURE__*/_jsx(Box,{display:\"flex\",flexDirection:\"column\",alignItems:hasOnlyText()?\"flex-start\":\"center\",sx:{width:hasOnlyText()?\"auto\":\"100%\",padding:hasOnlyText()?\"0\":undefined},children:renderContent()}),(currentStepData===null||currentStepData===void 0?void 0:(_currentStepData$butt12=currentStepData.buttonData)===null||_currentStepData$butt12===void 0?void 0:_currentStepData$butt12.length)>0&&/*#__PURE__*/_jsx(Box,{ref:buttonContainerRef,display:\"flex\",sx:{placeContent:\"center\",// padding: hasOnlyButtons() ? \"4px\" : \"10px\",\ngap:\"4px\",backgroundColor:(_currentStepData$butt13=currentStepData.buttonData[0])===null||_currentStepData$butt13===void 0?void 0:_currentStepData$butt13.BackgroundColor,width:hasOnlyButtons()?\"auto\":\"100%\",alignItems:\"center\"},children:renderButtons()})]})})},`scrollbar-${needsScrolling}`),enableProgress&&steps.length>1&&selectedTemplate!==\"Hotspot\"&&/*#__PURE__*/_jsx(Box,{children:renderProgress()}),\" \"]});//const overlay = currentStep?.overlay || \"\";\nconst overlayStyle={position:\"fixed\",top:0,left:0,width:\"100vw\",height:\"100vh\",backgroundColor:\"transparent\",pointerEvents:\"none\",zIndex:9999};const getOverlaySections=()=>{if(!targetElement)return null;const rect=targetElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;const sections={top:{position:\"fixed\",top:0,left:0,width:\"100%\",height:`${rect.top}px`,backgroundColor:\"rgba(0, 0, 0, 0.5)\",pointerEvents:\"auto\"},bottom:{position:\"fixed\",top:`${rect.bottom}px`,left:0,width:\"100%\",height:`${viewportHeight-rect.bottom}px`,backgroundColor:\"rgba(0, 0, 0, 0.5)\",pointerEvents:\"auto\"},left:{position:\"fixed\",top:`${rect.top}px`,left:0,width:`${rect.left}px`,height:`${rect.height}px`,backgroundColor:\"rgba(0, 0, 0, 0.5)\",pointerEvents:\"auto\"},right:{position:\"fixed\",top:`${rect.top}px`,left:`${rect.right}px`,width:`${viewportWidth-rect.right}px`,height:`${rect.height}px`,backgroundColor:\"rgba(0, 0, 0, 0.5)\",pointerEvents:\"auto\"}};return sections;};const highlightBoxStyle=targetElement?{position:\"fixed\",top:`${targetElement.getBoundingClientRect().top}px`,left:`${targetElement.getBoundingClientRect().left}px`,width:`${targetElement.getBoundingClientRect().width}px`,height:`${targetElement.getBoundingClientRect().height}px`,// border: '2px solid #fff',\nborderRadius:\"4px\",pointerEvents:interactWithPage?\"auto\":\"none\",zIndex:0}:{};return/*#__PURE__*/_jsxs(_Fragment,{children:[(currentStepData===null||currentStepData===void 0?void 0:currentStepData.overlay)&&targetElement&&isElementVisible&&selectedTemplate!==\"Tour\"&&/*#__PURE__*/_jsxs(Box,{sx:overlayStyle,children:[Object.entries(getOverlaySections()||{}).map(_ref4=>{let[key,style]=_ref4;return/*#__PURE__*/_jsx(Box,{sx:style},key);}),/*#__PURE__*/_jsx(Box,{sx:highlightBoxStyle})]}),targetElement&&isElementVisible&&/*#__PURE__*/_jsx(CustomWidthTooltip,{open:true,title:TooltipContent,placement:tooltipPlacement,arrow:true,PopperProps:{anchorEl:targetElement,modifiers:[{name:\"preventOverflow\",options:{boundary:window,altAxis:true,padding:10}},{name:\"computeStyles\",options:{// Disable adaptive positioning to prevent micro-adjustments\nadaptive:false,// Round positions to prevent sub-pixel rendering\nroundOffsets:_ref5=>{let{x,y}=_ref5;return{x:Math.round(x),y:Math.round(y)};}}}]},canvasStyle:canvasStyle,hasOnlyButtons:hasOnlyButtons(),hasOnlyText:hasOnlyText()//dynamicWidth={dynamicWidth}\n,children:/*#__PURE__*/_jsx(Box,{sx:{position:\"absolute\",top:targetElement.offsetTop,left:targetElement.offsetLeft,width:targetElement.offsetWidth,height:targetElement.offsetHeight}})})]});};export default TooltipGuide;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useCallback", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Box", "LinearProgress", "Typography", "tooltipClasses", "MobileStepper", "IconButton", "CloseIcon", "styled", "useDrawerStore", "PerfectScrollbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CustomWidthTooltip", "_ref", "className", "canvasStyle", "hasOnlyButtons", "hasOnlyText", "dynamicWidth", "props", "classes", "popper", "id", "_ref2", "tooltip", "backgroundColor", "BackgroundColor", "padding", "Padding", "borderRadius", "<PERSON><PERSON>", "BorderRadius", "boxShadow", "border", "BorderSize", "BorderColor", "width", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "left", "XAxisOffset", "bottom", "YAxisOffset", "arrow", "color", "fontSize", "outlineWidth", "outlineColor", "outlineStyle", "zIndex", "Zindex", "TooltipGuide", "_ref3", "_currentStepData$moda", "_currentStepData$butt3", "_currentStepData$imag6", "_currentStepData$imag7", "_currentStepData$butt4", "_currentStepData$butt12", "_currentStepData$butt13", "steps", "currentUrl", "onClose", "tooltipConfig", "startStepIndex", "data", "rect", "currentStepIndex", "setCurrentStepIndex", "contentRef", "buttonContainerRef", "scrollOperationQueue", "Promise", "resolve", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "undefined", "setCurrentStep", "selectedTemplate", "currentStep", "ProgressColor", "createWithAI", "pageinteraction", "state", "currentStepData", "targetElement", "setTargetElement", "tooltipPosition", "setTooltipPosition", "top", "tooltipPlacement", "setTooltipPlacement", "isElementVisible", "setIsElementVisible", "observerRef", "prevPositionRef", "prevPlacementRef", "calculateBestPosition", "element", "getBoundingClientRect", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "spaceTop", "spaceBottom", "spaceLeft", "spaceRight", "right", "maxSpace", "Math", "max", "validateElementPosition", "height", "Number", "isNaN", "getElementByXPath", "xpath", "PossibleElementPath", "trim", "console", "log", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "error", "query", "fallback<PERSON><PERSON><PERSON>", "fallbackNode", "fallback<PERSON><PERSON>r", "smoothScrollTo", "targetTop", "duration", "arguments", "length", "maxScroll", "scrollHeight", "clientHeight", "clampedTargetTop", "min", "scrollTo", "behavior", "startTop", "scrollTop", "distance", "startTime", "performance", "now", "animateScroll", "currentTime", "elapsed", "progress", "easeInOutCubic", "t", "easedProgress", "requestAnimationFrame", "pollForElement", "possibleElementPath", "onElementFound", "maxAttempts", "initialIntervalMs", "logPrefix", "onElementNotFound", "elementCheckTimeout", "attempts", "currentInterval", "isCleanedUp", "cleanup", "clearTimeout", "checkForElement", "jitter", "random", "setTimeout", "universalScrollTo", "options", "isWindow", "documentElement", "scrollLeft", "scrollToTargetElement", "placement", "stepData", "tagName", "current", "then", "targetScrollTop", "scrollY", "targetScrollLeft", "scrollX", "maxScrollTop", "maxScrollLeft", "scrollWidth", "currentScrollY", "currentScrollX", "elementRect", "scrollSuccess", "body", "interactWithPage", "InteractWithPage", "style", "pointerEvents", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "overlay", "overflow", "checkScrollNeeded", "contentHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "disconnect", "updateTargetAndPosition", "stepIndex", "<PERSON><PERSON><PERSON><PERSON>", "xOffset", "parseFloat", "positionXAxisOffset", "yOffset", "positionYAxisOffset", "newPlacement", "autoposition", "_currentStepData$canv", "validPlacements", "canvas", "Position", "includes", "newPosition", "round", "positionChanged", "abs", "handleNext", "nextIndex", "nextStepData", "currentIndex", "step<PERSON>itle", "scrollPromise", "foundElement", "isReasonablyVisible", "scrollIntoView", "block", "inline", "scrollError", "totalSteps", "prev", "handlePrevious", "prevIndex", "prevStepData", "prevElement", "isOutOfView", "_currentStepData$elem", "_currentStepData$elem2", "_currentStepData$elem3", "_currentStepData$elem4", "elementclick", "NextStep", "Id", "handleClick", "addEventListener", "removeEventListener", "_currentStepData$elem5", "handleDOMChanges", "targetNode", "characterData", "_observerRef$current", "handleViewportChanges", "timeoutId", "currentElement", "isCompletelyOutOfView", "enableProgress", "EnableProgress", "getProgressTemplate", "ProgressTemplate", "progressTemplate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modal", "DismissOption", "renderContent", "_currentStepData$imag", "_currentStepData$imag2", "_currentStepData$imag3", "_currentStepData$imag4", "_currentStepData$imag5", "hasImage", "imageUrl", "startsWith", "hasText", "Array", "isArray", "content", "some", "item", "Text", "isValidElement", "textStyle", "lineHeight", "whiteSpace", "wordBreak", "children", "component", "src", "imageproperties", "Url", "alt", "AltText", "sx", "objectFit", "Fit", "maxHeight", "SectionHeight", "margin", "dangerouslySetInnerHTML", "__html", "map", "replace", "join", "_currentStepData$butt", "hasButtons", "buttonData", "_currentStepData$butt2", "hasHtmlMeaningfulContent", "htmlContent", "cleanedContent", "tempDiv", "textContent", "innerText", "lowerContent", "toLowerCase", "emptyPatterns", "pattern", "hasValidTextContent", "hasValidImage", "hasOnlyTextContent", "hasOnlyButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getPadding", "_currentStepData$butt5", "_currentStepData$butt6", "_currentStepData$butt7", "_currentStepData$butt8", "_currentStepData$butt9", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ButtonAction", "Action", "toLocaleLowerCase", "_currentStepData$butt10", "renderProgress", "variant", "position", "activeStep", "place<PERSON><PERSON>nt", "backButton", "display", "nextButton", "alignItems", "gap", "from", "_", "index", "paddingTop", "value", "renderButtons", "_currentStepData$butt11", "button", "buttonStyle", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "fontFamily", "textTransform", "min<PERSON><PERSON><PERSON>", "opacity", "_currentStepData$elem6", "_currentStepData$elem7", "_currentStepData$elem8", "_currentStepData$elem9", "_currentStepData$elem10", "_currentStepData$elem11", "buttonId", "buttonAction", "expectedButtonId", "ButtonId", "_currentStepData$elem12", "_currentStepData$elem13", "expectedId", "click", "_steps$", "targetUrl", "location", "href", "_steps$2", "_steps$3", "firstElement", "ActionValue", "open", "TargetUrl", "onClick", "ButtonName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "background", "float", "parseInt", "zoom", "ref", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "flexDirection", "overlayStyle", "getOverlaySections", "sections", "highlightBoxStyle", "Object", "entries", "_ref4", "key", "title", "PopperProps", "anchorEl", "modifiers", "name", "boundary", "altAxis", "adaptive", "roundOffsets", "_ref5", "x", "y", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/tooltippreview/Tooltips/Tooltips.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport {\r\n  Button,\r\n  Tooltip,\r\n  Box,\r\n  LinearProgress,\r\n  Typography,\r\n  tooltipClasses,\r\n\tTooltipProps,\r\n  MobileStepper,\r\n\tBreadcrumbs,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport { CustomIconButton } from \"../../Button\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport useDrawerStore, { DrawerState } from \"../../../../store/drawerStore\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\ninterface ButtonAction {\r\n\tAction: string;\r\n\tActionValue: string;\r\n\tTargetUrl: string;\r\n}\r\ninterface ButtonProperties {\r\n\tPadding: number;\r\n\tWidth: number;\r\n\tFont: number;\r\n\tFontSize: number;\r\n\tButtonTextColor: string;\r\n\tButtonBackgroundColor: string;\r\n\tButtonBorderColor: string;\r\n}\r\ninterface Canvas {\r\n\tBackgroundColor: string;\r\n\tBorderColor: string;\r\n\tBorderSize: string;\r\n\tPadding: string;\r\n\tPosition: string;\r\n\tRadius: string;\r\n\tWidth: string;\r\n\tZindex: string;\r\n}\r\ninterface Modal {\r\n\tDismissOption: boolean;\r\n\tIncludeRequisiteButtons: boolean;\r\n\tInteractionWithPopup: boolean;\r\n\tModalPlacedOn: string;\r\n}\r\n\r\ninterface ButtonData {\r\n\tButtonStyle: string;\r\n\tButtonName: string;\r\n\tAlignment: string;\r\n\tBackgroundColor: string;\r\n\tButtonAction: ButtonAction;\r\n  Padding: {\r\n\t\tTop: number;\r\n\t\tRight: number;\r\n\t\tBottom: number;\r\n\t\tLeft: number;\r\n\t};\r\n\tButtonProperties: ButtonProperties;\r\n\tId: string;\r\n  }\r\ninterface CustomImage {\r\n\tAltText: string;\r\n\tBackgroundColor: string;\r\n\tFill: string;\r\n\tFit: string;\r\n\tSectionHeight: string;\r\n\tUrl: string;\r\n}\r\ninterface Design {\r\n\tNextStep: string;\r\n\tButtonName: string;\r\n\tElementPath: string;\r\n\tId: string;\r\n\tButtonId: string;\r\n}\r\n\r\ninterface Step {\r\n\txpath: string;\r\n\tcontent: string | JSX.Element;\r\n\ttargetUrl: string;\r\n\timageUrl: string;\r\n\tbuttonData: ButtonData[];\r\n\toverlay: boolean;\r\n\tpositionXAxisOffset: string;\r\n\tpositionYAxisOffset: string;\r\n\tcanvas: Canvas;\r\n\tmodal: Modal;\r\n\timageproperties: any;\r\n\tautoposition: any;\r\n\telementclick: Design;\r\n  PossibleElementPath: string\r\n}\r\ninterface TooltipGuideProps {\r\n\tsteps: Step[];\r\n\tcurrentUrl: string;\r\n\tonClose: () => void;\r\n\ttooltipConfig: any;\r\n\tstartStepIndex: any;\r\n\tdata: any;\r\n}\r\nconst CustomWidthTooltip = styled(({ className, canvasStyle, hasOnlyButtons, hasOnlyText, dynamicWidth, ...props }: TooltipProps & {\r\n\tcanvasStyle?: any,\r\n\thasOnlyButtons?: boolean,\r\n\thasOnlyText?: boolean,\r\n    dynamicWidth?: string | null\r\n}) => (\r\n\t<Tooltip\r\n\t\t{...props}\r\n\t\tclasses={{ popper: className }}\r\n\t\tid=\"Tooltip-unique\"\r\n\t/>\r\n))(({ canvasStyle, hasOnlyButtons, hasOnlyText, dynamicWidth }: {\r\n\tcanvasStyle: any,\r\n\thasOnlyButtons?: boolean,\r\n\thasOnlyText?: boolean,\r\n    dynamicWidth?: string | null\r\n  }) => ({\r\n    [`& .${tooltipClasses.tooltip}`]: {\r\n      backgroundColor: canvasStyle?.BackgroundColor || \"#ffffff\",\r\n\t\t// color: \"black\", // Set static text color\r\n\t\t//fontSize: \"14px\", // Font size\r\n      padding: hasOnlyButtons ? \"0px\" : canvasStyle.Padding,\r\n\t\t// padding: \"0px !important\",\r\n      borderRadius: canvasStyle?.Radius || canvasStyle?.BorderRadius||\"10px\",\r\n      boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\r\n\t\tborder: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || \"transparent\"}` : \"none\",\r\n\t\t// width: (hasOnlyButtons ) ? \"auto !important\" :\r\n\t\t// \t   canvasStyle?.Width ? `${canvasStyle.Width} !important` : \"300px\",\r\n\t\twidth : 'auto !important',\r\n      maxWidth: canvasStyle?.Width ? `${canvasStyle.Width} !important` : \"300px\",\r\n      left: `${canvasStyle?.XAxisOffset || \"auto\"} !important`,\r\n      bottom: `${canvasStyle?.YAxisOffset || \"auto\"} !important`,\r\n    },\r\n    [`& .${tooltipClasses.arrow}`]: {\r\n      color: canvasStyle?.BackgroundColor || \"#ffffff\",\r\n      fontSize: \"16px\",\r\n      \"&:before\": {\r\n\t\t\toutlineWidth: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize}` : \"0px\", // This controls the width of the border of the arrow\r\n\t\t\toutlineColor: canvasStyle?.BorderColor || \"transparent\", // This controls the color of the border of the arrow\r\n\t\t\toutlineStyle: \"solid\", // Ensure the border is applied properly\r\n      },\r\n    },\r\n    [`&.MuiTooltip-popper`]: {\r\n\t\tzIndex: canvasStyle?.Zindex || 11000, // Tooltip z-index\r\n    },\r\n  }),\r\n)\r\n\r\nconst TooltipGuide: React.FC<TooltipGuideProps> = ({\r\n  steps,\r\n  currentUrl,\r\n  onClose,\r\n  tooltipConfig,\r\n  startStepIndex,\r\n  data,\r\n}) => {\r\n  let rect: any\r\n  const [currentStepIndex, setCurrentStepIndex] = useState(startStepIndex || 1)\r\n  const contentRef = useRef<HTMLDivElement>(null)\r\n  const buttonContainerRef = useRef<HTMLDivElement>(null)\r\n\r\n  // Auto-scroll related refs and state\r\n  const scrollOperationQueue = useRef<Promise<void>>(Promise.resolve())\r\n\r\n  // State to track if scrolling is needed\r\n  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n  const scrollbarRef = useRef<any>(null);\r\n\r\n  useEffect(() => {\r\n    if (startStepIndex !== undefined) {\r\n\t\t\tsetCurrentStepIndex(startStepIndex);\r\n    }\r\n\t}, [startStepIndex]);\r\n\tconst { setCurrentStep, selectedTemplate, currentStep, ProgressColor, createWithAI, pageinteraction } = useDrawerStore((state: DrawerState) => state);\r\n\r\n\tconst currentStepData = steps[currentStepIndex - 1];\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\tconst [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });\r\n\tconst [tooltipPlacement, setTooltipPlacement] = useState<\"top\" | \"left\" | \"right\" | \"bottom\">(\"top\");\r\n\tconst [isElementVisible, setIsElementVisible] = useState(false);\r\n  const observerRef = useRef<MutationObserver | null>(null)\r\n\r\n  // Add refs to store previous position values to prevent unnecessary updates\r\n  const prevPositionRef = useRef({ top: 0, left: 0 })\r\n\tconst prevPlacementRef = useRef<\"top\" | \"left\" | \"right\" | \"bottom\">(\"top\")\r\n\r\n\r\n  const calculateBestPosition = (element: HTMLElement): \"top\" | \"left\" | \"right\" | \"bottom\" => {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\tconst spaceTop = rect.top;\r\n\t\tconst spaceBottom = viewportHeight - rect.bottom;\r\n\t\tconst spaceLeft = rect.left;\r\n\t\tconst spaceRight = viewportWidth - rect.right;\r\n\r\n\t\tconst maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);\r\n\r\n\t\tif (maxSpace === spaceTop) return \"top\";\r\n\t\tif (maxSpace === spaceBottom) return \"bottom\";\r\n\t\tif (maxSpace === spaceLeft) return \"left\";\r\n\t\treturn \"right\";\r\n\t};\r\n\t// Removed unused isElementInViewport function\r\n\r\n  const validateElementPosition = (element: HTMLElement) => {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n    return (\r\n      rect.width > 0 &&\r\n      rect.height > 0 &&\r\n      rect.top !== 0 &&\r\n      rect.left !== 0 &&\r\n      !Number.isNaN(rect.top) &&\r\n      !Number.isNaN(rect.left)\r\n\t\t);\r\n\t};\r\n  const getElementByXPath = (xpath: string, PossibleElementPath: string): HTMLElement | null => {\r\n\t\t// Validate XPath before using it\r\n    if (!xpath || xpath.trim() === \"\") {\r\n\t\t\tconsole.log(\"XPath is empty or undefined, trying PossibleElementPath:\", PossibleElementPath);\r\n      if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\r\n        try {\r\n\t\t\t\t\tconst result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\t\t\tconst node = result.singleNodeValue;\r\n          if (node instanceof HTMLElement) {\r\n\t\t\t\t\t\treturn node;\r\n          } else if (node?.parentElement) {\r\n\t\t\t\t\t\treturn node.parentElement;\r\n          }\r\n        } catch (error) {\r\n\t\t\t\t\tconsole.error(\"Error evaluating PossibleElementPath:\", PossibleElementPath, error);\r\n        }\r\n      }\r\n\t\t\treturn null;\r\n    }\r\n\r\n    try {\r\n\t\t\tconst query = `${xpath}[not(ancestor::div[@id='quickAdopt_banner']) and not(@id='quickAdopt_banner')]`;\r\n\t\t\tconsole.log(\"Evaluating XPath query:\", query);\r\n\t\t\tconst result = document.evaluate(query, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\tconst node = result.singleNodeValue;\r\n      if (node instanceof HTMLElement) {\r\n\t\t\t\treturn node;\r\n      } else if (node?.parentElement) {\r\n\t\t\t\treturn node.parentElement;\r\n      } else if (node === null) {\r\n\t\t\t\t// Try PossibleElementPath as fallback\r\n        if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\r\n\t\t\t\t\tconsole.log(\"Primary XPath failed, trying PossibleElementPath:\", PossibleElementPath);\r\n\t\t\t\t\tconst fallbackResult = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\t\t\tconst fallbackNode = fallbackResult.singleNodeValue;\r\n          if (fallbackNode instanceof HTMLElement) {\r\n\t\t\t\t\t\treturn fallbackNode;\r\n          } else if (fallbackNode?.parentElement) {\r\n\t\t\t\t\t\treturn fallbackNode.parentElement;\r\n          }\r\n        }\r\n\t\t\t\treturn null;\r\n      } else {\r\n\t\t\t\treturn null;\r\n      }\r\n    } catch (error) {\r\n\t\t\tconsole.error(\"Error evaluating XPath:\", xpath, error);\r\n\t\t\t// Try PossibleElementPath as fallback\r\n      if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\r\n        try {\r\n\t\t\t\t\tconsole.log(\"XPath evaluation failed, trying PossibleElementPath:\", PossibleElementPath);\r\n\t\t\t\t\tconst result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\t\t\tconst node = result.singleNodeValue;\r\n          if (node instanceof HTMLElement) {\r\n\t\t\t\t\t\treturn node;\r\n          } else if (node?.parentElement) {\r\n\t\t\t\t\t\treturn node.parentElement;\r\n          }\r\n        } catch (fallbackError) {\r\n\t\t\t\t\tconsole.error(\"Error evaluating PossibleElementPath:\", PossibleElementPath, fallbackError);\r\n        }\r\n      }\r\n\t\t\treturn null;\r\n    }\r\n\t};\r\n\r\n\t// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\r\n\tconst smoothScrollTo = (element: HTMLElement, targetTop: number, duration: number = 300) => {\r\n\t\t// Ensure targetTop is within valid bounds\r\n\t\tconst maxScroll = element.scrollHeight - element.clientHeight;\r\n\t\tconst clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));\r\n\r\n\t\t// Method 1: Try native smooth scrolling first\r\n\t\ttry {\r\n\t\t\tif ('scrollTo' in element && typeof element.scrollTo === 'function') {\r\n\t\t\t\telement.scrollTo({\r\n\t\t\t\t\ttop: clampedTargetTop,\r\n\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Native smooth scrollTo failed, trying animation fallback\");\r\n\t\t}\r\n\r\n\t\t// Method 2: Manual animation fallback\r\n\t\ttry {\r\n\t\t\tconst startTop = element.scrollTop;\r\n\t\t\tconst distance = clampedTargetTop - startTop;\r\n\t\t\tconst startTime = performance.now();\r\n\r\n\t\t\tconst animateScroll = (currentTime: number) => {\r\n\t\t\t\tconst elapsed = currentTime - startTime;\r\n\t\t\t\tconst progress = Math.min(elapsed / duration, 1);\r\n\r\n\t\t\t\t// Easing function for smooth animation\r\n\t\t\t\tconst easeInOutCubic = (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\r\n\t\t\t\tconst easedProgress = easeInOutCubic(progress);\r\n\r\n\t\t\t\telement.scrollTop = startTop + (distance * easedProgress);\r\n\r\n\t\t\t\tif (progress < 1) {\r\n\t\t\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"RequestAnimationFrame failed, using direct assignment\");\r\n\t\t\t// Method 3: Direct assignment as final fallback\r\n\t\t\telement.scrollTop = clampedTargetTop;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced reusable element polling function with exponential backoff and better timing\r\n\tconst pollForElement = useCallback((\r\n\t\txpath: string,\r\n\t\tpossibleElementPath: string,\r\n\t\tonElementFound: (element: HTMLElement) => void,\r\n\t\tmaxAttempts: number = 30,\r\n\t\tinitialIntervalMs: number = 16, // Start with one frame\r\n\t\tlogPrefix: string = \"Element\",\r\n\t\tonElementNotFound?: () => void\r\n\t) => {\r\n\t\tlet elementCheckTimeout: NodeJS.Timeout | null = null;\r\n\t\tlet attempts = 0;\r\n\t\tlet currentInterval = initialIntervalMs;\r\n\t\tlet isCleanedUp = false;\r\n\r\n\t\tconst cleanup = () => {\r\n\t\t\tif (elementCheckTimeout) {\r\n\t\t\t\tclearTimeout(elementCheckTimeout);\r\n\t\t\t\telementCheckTimeout = null;\r\n\t\t\t}\r\n\t\t\tisCleanedUp = true;\r\n\t\t};\r\n\r\n\t\tconst checkForElement = () => {\r\n\t\t\tif (isCleanedUp) return;\r\n\r\n\t\t\tattempts++;\r\n\t\t\tconsole.log(`${logPrefix}: Polling attempt ${attempts}/${maxAttempts} (interval: ${currentInterval}ms)`);\r\n\r\n\t\t\tconst element = getElementByXPath(xpath, possibleElementPath);\r\n\r\n\t\t\tif (element && validateElementPosition(element)) {\r\n\t\t\t\tconsole.log(`${logPrefix}: Found and validated after ${attempts} attempts`);\r\n\t\t\t\tcleanup();\r\n\t\t\t\tonElementFound(element);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (attempts >= maxAttempts) {\r\n\t\t\t\tconsole.log(`${logPrefix}: Max attempts (${maxAttempts}) reached, element not found`);\r\n\t\t\t\tcleanup();\r\n\t\t\t\tif (onElementNotFound) {\r\n\t\t\t\t\tonElementNotFound();\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Exponential backoff with jitter to prevent thundering herd\r\n\t\t\tconst jitter = Math.random() * 0.1; // 10% jitter\r\n\t\t\tcurrentInterval = Math.min(currentInterval * (1.2 + jitter), 1000); // Cap at 1 second\r\n\r\n\t\t\telementCheckTimeout = setTimeout(checkForElement, currentInterval);\r\n\t\t};\r\n\r\n\t\t// Start the polling\r\n\t\tcheckForElement();\r\n\r\n\t\t// Return cleanup function\r\n\t\treturn cleanup;\r\n\t}, []);\r\n\r\n\t// Enhanced cross-environment scrolling function\r\n\tconst universalScrollTo = (element: HTMLElement | Window, options: { top?: number; left?: number; behavior?: 'smooth' | 'auto' }) => {\r\n\t\tconst isWindow = element === window;\r\n\t\tconst targetElement = isWindow ? document.documentElement : element as HTMLElement;\r\n\r\n\t\t// Method 1: Try native scrollTo if available and not blocked\r\n\t\tif (!isWindow && 'scrollTo' in element && typeof (element as any).scrollTo === 'function') {\r\n\t\t\ttry {\r\n\t\t\t\t(element as any).scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Native scrollTo blocked or failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 2: Try window.scrollTo for window element\r\n\t\tif (isWindow && options.behavior === 'smooth') {\r\n\t\t\ttry {\r\n\t\t\t\twindow.scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Window scrollTo failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 3: Try smooth scrolling with custom animation\r\n\t\tif (options.behavior === 'smooth' && options.top !== undefined) {\r\n\t\t\ttry {\r\n\t\t\t\tsmoothScrollTo(targetElement, options.top);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Smooth scroll animation failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 4: Direct property assignment (final fallback)\r\n\t\ttry {\r\n\t\t\tif (options.top !== undefined) {\r\n\t\t\t\ttargetElement.scrollTop = options.top;\r\n\t\t\t}\r\n\t\t\tif (options.left !== undefined) {\r\n\t\t\t\ttargetElement.scrollLeft = options.left;\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Direct property assignment failed:\", error);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t};\r\n\r\n\tconst scrollToTargetElement = useCallback(async (targetElement: HTMLElement, placement: \"top\" | \"left\" | \"right\" | \"bottom\", stepData?: Step) => {\r\n\t\tif (!targetElement) {\r\n\t\t\tconsole.log(\"ScrollToTargetElement: No target element provided\");\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconsole.log(\"🎯 Starting enhanced auto-scroll to target element:\", {\r\n\t\t\telement: targetElement,\r\n\t\t\ttagName: targetElement.tagName,\r\n\t\t\tclassName: targetElement.className,\r\n\t\t\tid: targetElement.id,\r\n\t\t\tplacement: placement\r\n\t\t});\r\n\r\n\t\ttry {\r\n\t\t\t// Add the scroll operation to the queue to prevent conflicts\r\n\t\t\tscrollOperationQueue.current = scrollOperationQueue.current.then(async () => {\r\n\t\t\t\tconst rect = targetElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Calculate optimal scroll position based on placement and viewport\r\n\t\t\t\tlet targetScrollTop = window.scrollY;\r\n\t\t\t\tlet targetScrollLeft = window.scrollX;\r\n\r\n\t\t\t\t// Enhanced positioning logic based on tooltip placement\r\n\t\t\t\tswitch (placement) {\r\n\t\t\t\t\tcase \"top\":\r\n\t\t\t\t\t\t// Position element in lower third of viewport to leave room for tooltip above\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\t\t// Position element in upper third of viewport to leave room for tooltip below\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"left\":\r\n\t\t\t\t\t\t// Position element towards right side to leave room for tooltip on left\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"right\":\r\n\t\t\t\t\t\t// Position element towards left side to leave room for tooltip on right\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t// Default: center the element vertically\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Ensure scroll positions are within valid bounds\r\n\t\t\t\tconst maxScrollTop = Math.max(0, document.documentElement.scrollHeight - viewportHeight);\r\n\t\t\t\tconst maxScrollLeft = Math.max(0, document.documentElement.scrollWidth - viewportWidth);\r\n\r\n\t\t\t\ttargetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));\r\n\t\t\t\ttargetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));\r\n\r\n\t\t\t\tconsole.log(\"📍 Calculated scroll position:\", {\r\n\t\t\t\t\ttargetScrollTop,\r\n\t\t\t\t\ttargetScrollLeft,\r\n\t\t\t\t\tcurrentScrollY: window.scrollY,\r\n\t\t\t\t\tcurrentScrollX: window.scrollX,\r\n\t\t\t\t\telementRect: rect\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Perform the scroll with multiple fallback methods\r\n\t\t\t\tlet scrollSuccess = false;\r\n\r\n\t\t\t\t// Method 1: Try smooth scrolling\r\n\t\t\t\ttry {\r\n\t\t\t\t\tscrollSuccess = universalScrollTo(window, {\r\n\t\t\t\t\t\ttop: targetScrollTop,\r\n\t\t\t\t\t\tleft: targetScrollLeft,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log(\"✅ Universal scroll success:\", scrollSuccess);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log(\"❌ Universal scroll failed:\", error);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 2: Fallback to immediate scroll if smooth scroll failed\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\twindow.scrollTo(targetScrollLeft, targetScrollTop);\r\n\t\t\t\t\t\tscrollSuccess = true;\r\n\t\t\t\t\t\tconsole.log(\"✅ Fallback scroll successful\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Fallback scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 3: Final fallback using direct property assignment\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tdocument.documentElement.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.documentElement.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tdocument.body.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.body.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tconsole.log(\"✅ Direct property assignment completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Direct property assignment failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Small delay to allow scroll to complete\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n\t\t\t\tconsole.log(\"🏁 Auto-scroll operation completed\");\r\n\t\t\t});\r\n\r\n\t\t\tawait scrollOperationQueue.current;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"❌ ScrollToTargetElement error:\", error);\r\n\t\t}\r\n\t}, [smoothScrollTo, universalScrollTo]);\r\n\r\n\tconst progress = ((currentStepIndex ) / steps.length) * 100;\r\n\tconst interactWithPage = tooltipConfig?.InteractWithPage;\r\n\r\n  useEffect(() => {\r\n    if (currentStep && currentStepIndex >= 0 && interactWithPage === false) {\r\n      console.log('[Tooltips.tsx] Setting document.body.style.pointerEvents =', selectedTemplate === \"Tour\" ? \"auto\" : \"none\");\r\n      document.body.style.pointerEvents = selectedTemplate === \"Tour\" ? \"auto\" : \"none\";\r\n      const style = document.createElement('style');\r\n      style.innerHTML = `.qadpt-editor  { pointer-events: auto !important; }`;\r\n      document.head.appendChild(style);\r\n      return () => {\r\n        console.log('[Tooltips.tsx] Resetting document.body.style.pointerEvents = auto');\r\n        document.body.style.pointerEvents = \"auto\";\r\n        document.head.removeChild(style);\r\n      };\r\n    }\r\n    return () => {\r\n      // In case there's no active step or tooltip is closed\r\n      console.log('[Tooltips.tsx] Cleanup: Resetting document.body.style.pointerEvents = auto');\r\n      document.body.style.pointerEvents = \"auto\"; // Enable interactions\r\n    };\r\n  }, [currentStepIndex, interactWithPage]);\r\n\r\n\t// Handle overflow hidden based on overlay and page interaction settings\r\n  useEffect(() => {\r\n    if (currentStep && currentStepIndex >= 0 && currentStepData?.overlay && interactWithPage === false) {\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n    } else {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n    }\r\n\r\n\t\t// Cleanup on unmount\r\n    return () => {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t};\r\n  }, [currentStepData?.overlay, interactWithPage]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStepData, currentStep]);\r\n\r\n  const updateTargetAndPosition = () => {\r\n\t\t// if (currentUrl !== currentStep?.targetUrl) {\r\n\t\t// \tsetTargetElement(null);\r\n\t\t// \tsetIsElementVisible(false);\r\n\t\t// \treturn;\r\n\t\t// }\r\n\r\n\t\t// Debug logging for XPath data\r\n    console.log(\"Tooltip updateTargetAndPosition - currentStepData:\", {\r\n      xpath: currentStepData?.xpath,\r\n      PossibleElementPath: currentStepData?.PossibleElementPath,\r\n      stepIndex: currentStepIndex,\r\n\t\t\tcreateWithAI: createWithAI\r\n\t\t});\r\n\r\n\t\tconst element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);\r\n    if (!element) {\r\n\t\t\tconsole.log(\"Tooltip element not found for XPath:\", currentStepData?.xpath, \"PossibleElementPath:\", currentStepData?.PossibleElementPath);\r\n\t\t\tsetTargetElement(null);\r\n\t\t\tsetIsElementVisible(false);\r\n\t\t\treturn;\r\n    }\r\n\r\n\t\tconst isValid = validateElementPosition(element);\r\n\t\t//const isVisible = isElementInViewport(element);\r\n\r\n    if (!isValid) {\r\n\t\t\tsetTargetElement(null);\r\n\t\t\tsetIsElementVisible(false);\r\n\t\t\treturn;\r\n    }\r\n\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\tconst xOffset = parseFloat(currentStepData?.positionXAxisOffset || \"0\");\r\n\t\tconst yOffset = parseFloat(currentStepData?.positionYAxisOffset || \"0\");\r\n\r\n\t\tsetTargetElement(element);\r\n\t\tsetIsElementVisible(true);\r\n\r\n    // Calculate placement\r\n    let newPlacement: \"top\" | \"left\" | \"right\" | \"bottom\"\r\n    if (currentStepData?.autoposition) {\r\n      newPlacement = calculateBestPosition(element)\r\n    } else {\r\n      const validPlacements = [\"top\", \"left\", \"right\", \"bottom\"] as const\r\n      const placement = currentStepData?.canvas?.Position || \"bottom\"\r\n      newPlacement = validPlacements.includes(placement as any)\r\n        ? (placement as \"top\" | \"left\" | \"right\" | \"bottom\")\r\n        : \"bottom\"\r\n    }\r\n\r\n\r\n    if (prevPlacementRef.current !== newPlacement) {\r\n      prevPlacementRef.current = newPlacement\r\n      setTooltipPlacement(newPlacement)\r\n    }\r\n\r\n\r\n    const newPosition = {\r\n      top: Math.round(rect.top + window.scrollY + yOffset),\r\n      left: Math.round(rect.left + window.scrollX + xOffset),\r\n    }\r\n\r\n\r\n    const positionChanged =\r\n      Math.abs(prevPositionRef.current.top - newPosition.top) > 1 ||\r\n      Math.abs(prevPositionRef.current.left - newPosition.left) > 1\r\n\r\n    if (positionChanged) {\r\n      prevPositionRef.current = newPosition\r\n      setTooltipPosition(newPosition)\r\n    }\r\n  }\r\n\r\n  // Enhanced auto-scroll navigation system for tooltip next functionality\r\n\tconst handleNext = useCallback(async () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\t// Calculate the next index first (currentStepIndex is 1-based, so we need to check against steps.length)\r\n\t\t\tconst nextIndex = currentStepIndex + 1;\r\n\r\n\t\t\tif (nextIndex <= steps.length) {\r\n\t\t\t\t// Get the next step data for scrolling (convert to 0-based for array access)\r\n\t\t\t\tconst nextStepData = steps[nextIndex - 1];\r\n\r\n\t\t\t\tconsole.log(\"🔍 Starting navigation to next step:\", {\r\n\t\t\t\t\tcurrentIndex: currentStepIndex,\r\n\t\t\t\t\tnextIndex: nextIndex,\r\n\t\t\t\t\txpath: nextStepData?.xpath,\r\n\t\t\t\t\tstepTitle: `Step ${nextIndex}`\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Smart auto-scroll: Only scroll if element is not reasonably visible\r\n\t\t\t\tif (nextStepData?.xpath) {\r\n\t\t\t\t\tconsole.log(\"🔍 Checking auto-scroll for next element:\", {\r\n\t\t\t\t\t\txpath: nextStepData.xpath,\r\n\t\t\t\t\t\tstepIndex: nextIndex,\r\n\t\t\t\t\t\tstepTitle: `Step ${nextIndex}`\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Create a promise to handle element finding and scrolling\r\n\t\t\t\t\tconst scrollPromise = new Promise<void>((resolve) => {\r\n\t\t\t\t\t\t// Use polling to find the element\r\n\t\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\t\tnextStepData.xpath || \"\",\r\n\t\t\t\t\t\t\tnextStepData.PossibleElementPath || \"\",\r\n\t\t\t\t\t\t\tasync (foundElement) => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Next element found:\", {\r\n\t\t\t\t\t\t\t\t\telement: foundElement,\r\n\t\t\t\t\t\t\t\t\ttagName: foundElement.tagName,\r\n\t\t\t\t\t\t\t\t\tclassName: foundElement.className,\r\n\t\t\t\t\t\t\t\t\tid: foundElement.id\r\n\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t// Check if element needs scrolling\r\n\t\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\t\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t\t\t\t\t// More generous visibility check - element should be reasonably visible\r\n\t\t\t\t\t\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\t\t\t\t\t\trect.top >= -50 && // Allow some element to be above viewport\r\n\t\t\t\t\t\t\t\t\trect.left >= -50 && // Allow some element to be left of viewport\r\n\t\t\t\t\t\t\t\t\trect.bottom <= viewportHeight + 50 && // Allow some element below viewport\r\n\t\t\t\t\t\t\t\t\trect.right <= viewportWidth + 50 && // Allow some element right of viewport\r\n\t\t\t\t\t\t\t\t\trect.width > 0 &&\r\n\t\t\t\t\t\t\t\t\trect.height > 0\r\n\t\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\t\tif (isReasonablyVisible) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"Minimal scroll adjustment failed:\", error);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"🎯 Element not visible, performing auto-scroll\");\r\n\t\t\t\t\t\t\t\t\t// Element is not visible, scroll to bring it into view\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'center', // Center it for better visibility\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Auto-scroll completed successfully\");\r\n\t\t\t\t\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error(\"❌ Auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t20, // Reasonable maxAttempts\r\n\t\t\t\t\t\t\t30, // Reasonable initial interval\r\n\t\t\t\t\t\t\t\"Next step element\",\r\n\t\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"❌ Next element not found after polling, continuing without scroll\");\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tawait scrollPromise;\r\n\t\t\t\t\t\tconsole.log(\"✅ Element finding and scroll check completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error(\"❌ Scroll promise failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log(\"ℹ️ No xpath provided for next step, skipping auto-scroll\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Update the step index AFTER scrolling is complete\r\n\t\t\t\tconsole.log(\"🔄 Updating step index from\", currentStepIndex, \"to\", nextIndex);\r\n\t\t\t\tsetCurrentStepIndex(nextIndex);\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\r\n\t\t\t\t// Small delay to allow DOM to update after step change\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 50));\r\n\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log(\"🏁 Reached end of tooltip steps\", {\r\n\t\t\t\t\tcurrentStepIndex,\r\n\t\t\t\t\tnextIndex,\r\n\t\t\t\t\ttotalSteps: steps.length\r\n\t\t\t\t});\r\n\t\t\t\t// onClose(); // Close tooltip if no more steps\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// Tour template logic (consistent with 1-based indexing)\r\n\t\t\tif (currentStep < steps?.length) {\r\n\t\t\t\tsetCurrentStepIndex((prev: any) => Math.max(prev + 1, 1));\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, calculateBestPosition, scrollToTargetElement, setCurrentStep, setCurrentStepIndex]);\r\n\r\n  // Enhanced handlePrevious with auto-scroll functionality\r\n\tconst handlePrevious = useCallback(async () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tconst prevIndex = Math.max(currentStepIndex - 1, 1);\r\n\r\n\t\t\tif (prevIndex >= 1) {\r\n\t\t\t\tconsole.log(\"🔙 Navigating to previous step:\", {\r\n\t\t\t\t\tcurrentIndex: currentStepIndex,\r\n\t\t\t\t\tprevIndex: prevIndex\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Smart previous navigation scrolling\r\n\t\t\t\tif (prevIndex === 1) {\r\n\t\t\t\t\tconsole.log(\"HandlePrevious: Going back to first step, scroll to top\");\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\twindow.scrollTo({\r\n\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"HandlePrevious: Scroll to top failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// For other steps, check if previous step element needs scrolling\r\n\t\t\t\t\tconst prevStepData = steps[prevIndex - 1];\r\n\t\t\t\t\tif (prevStepData?.xpath) {\r\n\t\t\t\t\t\tconsole.log(\"HandlePrevious: Checking if previous step element needs scrolling\");\r\n\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tconst prevElement = getElementByXPath(prevStepData.xpath, prevStepData.PossibleElementPath || \"\");\r\n\t\t\t\t\t\t\tif (prevElement) {\r\n\t\t\t\t\t\t\t\tconst rect = prevElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\t\tconst isOutOfView = (\r\n\t\t\t\t\t\t\t\t\trect.bottom < 0 ||\r\n\t\t\t\t\t\t\t\t\trect.top > window.innerHeight ||\r\n\t\t\t\t\t\t\t\t\trect.right < 0 ||\r\n\t\t\t\t\t\t\t\t\trect.left > window.innerWidth\r\n\t\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\t\tif (isOutOfView) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Previous element out of view, scrolling\");\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tprevElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Element scroll failed:\", error);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}, 100);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Update step index\r\n\t\t\t\tsetCurrentStepIndex(prevIndex);\r\n\t\t\t\tsetCurrentStep(currentStep - 1);\r\n\r\n\t\t\t\t// Small delay to allow DOM to update\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 50));\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t}\r\n\t}, [currentStepIndex, selectedTemplate, currentStep, universalScrollTo, setCurrentStep, setCurrentStepIndex]);\r\n  useEffect(() => {\r\n    // Debug logging for AI tooltip button click functionality\r\n    console.log(\"🔍 Tooltip useEffect - Element click setup:\", {\r\n      currentStepIndex,\r\n      elementclick: currentStepData?.elementclick,\r\n      NextStep: currentStepData?.elementclick?.NextStep,\r\n      Id: currentStepData?.elementclick?.Id,\r\n      xpath: currentStepData?.xpath\r\n    });\r\n\r\n    if (currentStepData?.elementclick?.NextStep === \"element\" || currentStepData?.elementclick?.NextStep === \"button\") {\r\n\t\t\tconst element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);\r\n      if (element) {\r\n        console.log(\"✅ Element found for click handler:\", element);\r\n        const handleClick = () => {\r\n\t\t\t\t\tconsole.log(\"🖱️ Element clicked - advancing to next step\");\r\n\t\t\t\t\thandleNext();\r\n\t\t\t\t};\r\n\r\n\t\t\t\telement.addEventListener(\"click\", handleClick);\r\n        return () => {\r\n\t\t\t\t\telement.removeEventListener(\"click\", handleClick);\r\n\t\t\t\t};\r\n        } else {\r\n        console.log(\"❌ Element not found for xpath:\", currentStepData?.xpath);\r\n      }\r\n\t\t} else {\r\n\t\t  console.log(\"ℹ️ No element click setup - NextStep:\", currentStepData?.elementclick?.NextStep);\r\n\t\t}\r\n\t}, [currentStepData, handleNext]);\r\n\r\n  useEffect(() => {\r\n    const handleDOMChanges = () => {\r\n\t\t\trequestAnimationFrame(updateTargetAndPosition);\r\n\t\t};\r\n\t\tobserverRef.current = new MutationObserver(handleDOMChanges);\r\n\t\tconst targetNode = document.body;\r\n    observerRef.current.observe(targetNode, {\r\n      childList: true,\r\n      subtree: true,\r\n      attributes: true,\r\n      characterData: true,\r\n\t\t});\r\n\t\tupdateTargetAndPosition();\r\n    return () => {\r\n\t\t\tobserverRef.current?.disconnect();\r\n\t\t};\r\n\t}, [currentStepData, currentUrl]);\r\n\r\n  useEffect(() => {\r\n\t\tconst handleViewportChanges = () => {\r\n\t\t\trequestAnimationFrame(updateTargetAndPosition);\r\n\t\t};\r\n\r\n\t\twindow.addEventListener(\"scroll\", handleViewportChanges);\r\n\t\twindow.addEventListener(\"resize\", handleViewportChanges);\r\n\r\n    return () => {\r\n\t\t\twindow.removeEventListener(\"scroll\", handleViewportChanges);\r\n\t\t\twindow.removeEventListener(\"resize\", handleViewportChanges);\r\n\t\t};\r\n\t}, [currentStepData, currentUrl]);\r\n  useEffect(() => {\r\n\t\tupdateTargetAndPosition();\r\n\t}, [currentStepData, currentUrl, rect]); // Ensure all dependencies are included\r\n\r\n\t// Smart auto-scroll for current step changes - only when element is not visible\r\n\tuseEffect(() => {\r\n\t\tif (!currentStepData?.xpath) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Small delay to allow DOM to settle\r\n\t\tconst timeoutId = setTimeout(() => {\r\n\t\t\tconst currentElement = getElementByXPath(currentStepData.xpath, currentStepData.PossibleElementPath || \"\");\r\n\r\n\t\t\tif (currentElement) {\r\n\t\t\t\tconst rect = currentElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Check if element is completely out of view\r\n\t\t\t\tconst isCompletelyOutOfView = (\r\n\t\t\t\t\trect.bottom < 0 || // Completely above viewport\r\n\t\t\t\t\trect.top > viewportHeight || // Completely below viewport\r\n\t\t\t\t\trect.right < 0 || // Completely left of viewport\r\n\t\t\t\t\trect.left > viewportWidth // Completely right of viewport\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (isCompletelyOutOfView) {\r\n\t\t\t\t\tconsole.log(\"🔄 Current step element is out of view, gentle auto-scroll\");\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tcurrentElement.scrollIntoView({\r\n\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"Current step auto-scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 100);\r\n\r\n\t\treturn () => {\r\n\t\t\tclearTimeout(timeoutId);\r\n\t\t};\r\n\t}, [currentStepData]);\r\n\tconst canvasStyle = currentStepData?.canvas || {}; // Assuming canvas is an array, take the first item\r\n\tconst enableProgress = tooltipConfig.EnableProgress || false;\r\n  function getProgressTemplate(tooltipConfig: any) {\r\n    if (tooltipConfig?.ProgressTemplate === \"1\") {\r\n\t\t\treturn \"dots\";\r\n    } else if (tooltipConfig?.ProgressTemplate === \"2\") {\r\n\t\t\treturn \"linear\";\r\n    } else if (tooltipConfig?.ProgressTemplate === \"3\") {\r\n\t\t\treturn \"BreadCrumbs\";\r\n    } else {\r\n\t\t\treturn \"breadcrumbs\";\r\n    }\r\n  }\r\n\tconst progressTemplate = getProgressTemplate(tooltipConfig);\r\n\r\n\tconst enabelCross = currentStepData?.modal?.DismissOption;\r\n  const renderContent = () => {\r\n\t\tconst hasImage =\r\n\t\t\tcurrentStepData?.imageUrl.startsWith(\"data:image/\") || currentStepData?.imageUrl.startsWith(\"http\");\r\n    const hasText = Array.isArray(currentStepData?.content)\r\n      ? currentStepData.content.some((item) => item?.Text && typeof item.Text === \"string\" && item.Text.trim() !== \"\")\r\n\t\t\t: typeof currentStepData?.content === \"string\" || React.isValidElement(currentStepData?.content);\r\n    const textStyle = {\r\n      fontSize: \"14px\",\r\n      lineHeight: \"1.5\",\r\n      whiteSpace: \"pre-wrap\",\r\n      wordBreak: \"break-word\",\r\n      color: \"black\",\r\n\t\t};\r\n    return (\r\n      <Box>\r\n        {hasImage && (\r\n          <Box\r\n            component=\"img\"\r\n            src={currentStepData?.imageproperties?.Url}\r\n            alt={currentStepData?.imageproperties?.AltText || \"Step Image\"}\r\n            sx={{\r\n              backgroundColor: currentStepData?.imageproperties?.BackgroundColor || \"transparent\",\r\n              objectFit: currentStepData?.imageproperties?.Fit || \"cover\",\r\n              maxHeight: currentStepData?.imageproperties?.SectionHeight || \"auto\",\r\n              width: \"100%\",\r\n            }}\r\n          />\r\n        )}\r\n        {hasText && (\r\n          <Box\r\n            className=\"qadpt-preview\"\r\n\t\t\t\t\tsx={{ margin: \"0 !important\", ...textStyle ,\"& p\": {\r\n\t\t\t\t\t\tmargin: \"4px 0\",\r\n\t\t\t\t\t  },}}\t\t\t\t\tdangerouslySetInnerHTML={{\r\n              __html: Array.isArray(currentStepData.content)\r\n                ? currentStepData.content\r\n\t\t\t\t\t\t\t\t\t.map((item: any) =>\r\n\t\t\t\t\t\t\t\t\t\titem.Text.replace(/<a /g, '<a target=\"_blank\" rel=\"noopener noreferrer\" ')\r\n\t\t\t\t\t\t\t\t\t)\r\n                    .join(\"<br/>\")\r\n                : typeof currentStepData.content === \"string\"\r\n                  ? currentStepData.content.replace(/<a /g, '<a target=\"_blank\" rel=\"noopener noreferrer\" ')\r\n                  : \"\",\r\n            }}\r\n          />\r\n\r\n\t\t\t\t)}\r\n\t\t\t</Box>\r\n\t\t);\r\n\t};\r\n\r\n\t// Helper function to check if tooltip has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage =\r\n\t\t\tcurrentStepData?.imageUrl.startsWith(\"data:image/\") || currentStepData?.imageUrl.startsWith(\"http\");\r\n\t\tconst hasText = Array.isArray(currentStepData?.content)\r\n\t\t\t? currentStepData.content.some((item) => item?.Text && typeof item.Text === \"string\" && item.Text.trim() !== \"\")\r\n\t\t\t: typeof currentStepData?.content === \"string\" || React.isValidElement(currentStepData?.content);\r\n\t\tconst hasButtons = currentStepData?.buttonData?.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if tooltip has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage =\r\n\t\t\tcurrentStepData?.imageUrl.startsWith(\"data:image/\") || currentStepData?.imageUrl.startsWith(\"http\");\r\n\t\tconst hasText = Array.isArray(currentStepData?.content)\r\n\t\t\t? currentStepData.content.some((item) => item?.Text && typeof item.Text === \"string\" && item.Text.trim() !== \"\")\r\n\t\t\t: typeof currentStepData?.content === \"string\" || React.isValidElement(currentStepData?.content);\r\n\t\tconst hasButtons = currentStepData?.buttonData?.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\tconst hasHtmlMeaningfulContent = (htmlContent: string): boolean => {\r\n\t\tif (!htmlContent || htmlContent.trim() === '') {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t// Clean up common empty HTML patterns before checking\r\n\t\tlet cleanedContent = htmlContent;\r\n\r\n\t\t// Remove empty paragraph tags\r\n\t\tcleanedContent = cleanedContent.replace(/<p>\\s*(&nbsp;)*\\s*<\\/p>/gi, '');\r\n\r\n\t\t// Remove empty div tags\r\n\t\tcleanedContent = cleanedContent.replace(/<div>\\s*(&nbsp;)*\\s*<\\/div>/gi, '');\r\n\r\n\t\t// Remove empty span tags\r\n\t\tcleanedContent = cleanedContent.replace(/<span>\\s*(&nbsp;)*\\s*<\\/span>/gi, '');\r\n\r\n\t\t// Remove <br> tags\r\n\t\tcleanedContent = cleanedContent.replace(/<br\\s*\\/?>/gi, '');\r\n\r\n\t\t// Remove &nbsp; entities\r\n\t\tcleanedContent = cleanedContent.replace(/&nbsp;/gi, ' ');\r\n\r\n\t\t// If after cleaning there's no content left, return false\r\n\t\tif (cleanedContent.trim() === '') {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t// Create a temporary div to parse the cleaned HTML content\r\n\t\tconst tempDiv = document.createElement('div');\r\n\t\ttempDiv.innerHTML = cleanedContent;\r\n\r\n\t\t// Get the text content (strips all HTML tags)\r\n\t\tconst textContent = tempDiv.textContent || tempDiv.innerText;\r\n\r\n\t\t// Check if there's any non-whitespace text content\r\n\t\tif (textContent === null || textContent.trim() === '') {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t// Additional check for common empty HTML patterns\r\n\t\t// This handles cases like \"<div><br></div>\" or \"<p>&nbsp;</p>\" that might appear non-empty\r\n\t\tconst lowerContent = cleanedContent.toLowerCase();\r\n\t\tconst emptyPatterns = [\r\n\t\t\t'<div><br></div>',\r\n\t\t\t'<p><br></p>',\r\n\t\t\t'<div></div>',\r\n\t\t\t'<p></p>',\r\n\t\t\t'<span></span>',\r\n\t\t\t'<p>&nbsp;</p>',\r\n\t\t\t'<div>&nbsp;</div>',\r\n\t\t\t'<p> </p>',\r\n\t\t\t'<div> </div>'\r\n\t\t];\r\n\r\n\t\tif (emptyPatterns.some(pattern => lowerContent.includes(pattern)) && textContent.trim().length <= 1) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t};\r\n\r\n\tconst hasValidTextContent =\r\n\t\t(typeof currentStepData?.content === \"string\" && hasHtmlMeaningfulContent(currentStepData?.content)) ||\r\n\t\tReact.isValidElement(currentStepData?.content) ||\r\n\t\t(Array.isArray(currentStepData?.content) && currentStepData.content.some(\r\n\t\t\t(item: any) => item?.Text && typeof item.Text === \"string\" && hasHtmlMeaningfulContent(item.Text)\r\n\t\t));\r\n\t // Check if there are buttons\r\n\tconst hasButtons = currentStepData?.buttonData?.length > 0;\r\n\r\n\t//Check if there's a valid image\r\n\tconst hasValidImage =\r\n\t\tcurrentStepData?.imageUrl?.startsWith(\"data:image/\") ||\r\n\t\tcurrentStepData?.imageUrl?.startsWith(\"http\");\r\n\t// Check if there's only text content (no images or buttons)\r\n\tconst hasOnlyTextContent = hasValidTextContent && !hasValidImage && !hasButtons;\r\n\r\n\t// Check if there's only a button (no text or images)\r\n\tconst hasOnlyButton = hasButtons && !hasValidTextContent && !hasValidImage && currentStepData?.buttonData?.length === 1;\r\n\r\n\t// Check if there's any meaningful content to display\r\n\tconst hasValidContent = hasValidTextContent || hasValidImage;\r\n\t\t//Function to determine padding based on content and buttons\r\n\t\tconst getPadding = () => {\r\n\t\t\t// Check if we have exactly one button and it's a previous button\r\n\t\t\tconst hasPreviousButton = currentStepData?.buttonData?.length === 1 &&\r\n\t\t\t\tcurrentStepData?.buttonData?.[0]?.ButtonAction?.Action?.toLocaleLowerCase() === \"previous\";\r\n\r\n\t\t\t// Special case for previous button\r\n\t\t\tif (hasPreviousButton) {\r\n\t\t\t\treturn \"0px\";\r\n\t\t\t}\r\n\r\n\t\t\t// Original logic\r\n\t\t\tif (!hasValidContent) {\r\n\t\t\t\treturn currentStepData?.buttonData?.length === 1 ? \"0px\" : \"4px\";\r\n\t\t\t} else {\r\n\t\t\t\treturn \"0px\";\r\n    }\r\n\t\t};\r\n\r\n\r\n\t// Function to calculate the optimal width based on content and buttons\r\n\t// const calculateOptimalWidth = () => {\r\n\t// \t// If we have a fixed width from canvas settings and not a compact tooltip, use that\r\n\t// \tif (canvasStyle?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t// \t\treturn `${canvasStyle.Width}`;\r\n\t// \t}\r\n\r\n\t// \t// For tooltips with only buttons or only text, use auto width\r\n\t// \tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t// \t\treturn \"auto\";\r\n\t// \t}\r\n\r\n\t// \t// Get the width of content and button container\r\n\t// \tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t// \tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t// \t// Use the larger of the two, with some minimum and maximum constraints\r\n\t// \tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t// \t// Add some padding to ensure text has room to wrap naturally\r\n\t// \tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\r\n\r\n\t// \t// Ensure width is between reasonable bounds\r\n\t// \tconst minWidth = 250; // Minimum width\r\n\t// \tconst maxWidth = 800; // Maximum width\r\n\r\n\t// \tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t// \treturn `${finalWidth}px`;\r\n\t// };\r\n\r\n\t// Update dynamic width when content or buttons change\r\n\t// useEffect(() => {\r\n\t// \t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t// \trequestAnimationFrame(() => {\r\n\t// \t\tconst newWidth = calculateOptimalWidth();\r\n\t// \t\tsetDynamicWidth(newWidth);\r\n\t// \t});\r\n\t// }, [currentStepData, currentStepIndex]);\r\n  const renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n    if (progressTemplate === \"dots\") {\r\n      return (\r\n        <MobileStepper\r\n          variant=\"dots\"\r\n          steps={steps.length}\r\n          position=\"static\"\r\n          activeStep={currentStepIndex - 1}\r\n          sx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\", \"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n            },\r\n            placeContent: \"center\",\r\n            padding: \"2px  !important\",\r\n            \"& .MuiMobileStepper-dot\": {\r\n              width: \"6px !important\",\r\n              height: \"6px !important\",\r\n\t\t\t\t\t\t}\r\n          }}\r\n          backButton={<Button style={{ display: \"none\" }} />}\r\n          nextButton={<Button style={{ display: \"none\" }} />}\r\n        />\r\n\t\t\t);\r\n    }\r\n\r\n\t\t// if (progressTemplate === \"breadcrumbs\") {\r\n\t\t// \treturn (\r\n\t\t// \t\t<Breadcrumbs\r\n\t\t// \t\t\taria-label=\"breadcrumb\"\r\n\t\t// \t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t// \t\t>\r\n\t\t// \t\t\t{steps.map((_, index) => (\r\n\t\t// \t\t\t\t<Typography\r\n\t\t// \t\t\t\t\tkey={index}\r\n\t\t// \t\t\t\t\tcolor={index === currentStepIndex ? \"primary\" : \"text.secondary\"}\r\n\t\t// \t\t\t\t>\r\n\t\t// \t\t\t\t\tStep {index + 1} of {steps.length}\r\n\t\t// \t\t\t\t</Typography>\r\n\t\t// \t\t\t))}\r\n\t\t// \t\t</Breadcrumbs>\r\n\t\t// \t);\r\n\t\t// }\r\n    if (progressTemplate === \"BreadCrumbs\") {\r\n      return (\r\n                <Box sx={{display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"5px\",padding:\"8px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n          {Array.from({ length: steps.length }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n                          borderRadius: '100px',\r\n              }}\r\n            />\r\n          ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{paddingTop:\"8px\"}}>\r\n\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\tsx={{ padding: \"8px\", color: ProgressColor}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t Step {currentStepIndex} of {steps.length}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{padding: hasOnlyButtons() ? \"8px\" : \"0\",}}>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : \"6px 10px\",\r\n\t\t\t\t\t\t\t\t'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\r\n\tconst renderButtons = () => {\r\n\t\treturn currentStepData?.buttonData?.length > 0\r\n\t\t\t? currentStepData.buttonData.map((button: any, index: any) => {\r\n\t\t\t\t\tconst buttonStyle = {\r\n\t\t\t\t\t\tbackgroundColor: button.ButtonProperties.ButtonBackgroundColor,\r\n\t\t\t\t\t\tcolor: button.ButtonProperties.ButtonTextColor,\r\n\t\t\t\t\t\tborder: button.ButtonProperties.ButtonBorderColor,\r\n\t\t\t\t\t\tpadding: \"4px 8px !important\",\r\n\t\t\t\t\t\tlineHeight: \"normal\",\r\n\t\t\t\t\t\twidth: \"auto\",\r\n\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\tfontFamily: \"Poppins\",\r\n\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\tminWidth: \"fit-content\",\r\n\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties.ButtonBackgroundColor, // Keep the same background color on hover\r\n\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t};\r\n\t\t\t\t\tconst handleClick = () => {\r\n\t\t\t\t\t\tconsole.log(\"🔍 Button clicked:\", {\r\n\t\t\t\t\t\t\tbuttonId: button.Id,\r\n\t\t\t\t\t\t\tbuttonAction: button.ButtonAction.Action,\r\n\t\t\t\t\t\t\telementclick: currentStepData?.elementclick,\r\n\t\t\t\t\t\t\tNextStep: currentStepData?.elementclick?.NextStep,\r\n\t\t\t\t\t\t\texpectedButtonId: currentStepData?.elementclick?.Id\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tif (button.ButtonAction.Action.toLocaleLowerCase() === \"close\") {\r\n\t\t\t\t\t\t\t//onClose();\r\n\t\t\t\t\t\t} else if (\r\n\t\t\t\t\t\t\tbutton.ButtonAction.Action.toLocaleLowerCase() === \"next\" &&\r\n\t\t\t\t\t\t\tcurrentStepData?.elementclick?.NextStep !== \"button\"\r\n\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\tconsole.log(\"🚀 Regular next button - advancing step\");\r\n\t\t\t\t\t\t\thandleNext();\r\n\t\t\t\t\t\t} else if (\r\n\t\t\t\t\t\t\tbutton.ButtonAction.Action.toLocaleLowerCase() === \"next\" &&\r\n\t\t\t\t\t\t\tcurrentStepData?.elementclick?.NextStep === \"button\" &&\r\n\t\t\t\t\t\t\t(button.Id === currentStepData?.elementclick?.ButtonId ||\r\n\t\t\t\t\t\t\t button.Id === currentStepData?.elementclick?.Id)\r\n\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\tconsole.log(\"🎯 Button click with element interaction - clicking element and advancing\", {\r\n\t\t\t\t\t\t\t\tbuttonId: button.Id,\r\n\t\t\t\t\t\t\t\texpectedButtonId: currentStepData?.elementclick?.ButtonId,\r\n\t\t\t\t\t\t\t\texpectedId: currentStepData?.elementclick?.Id,\r\n\t\t\t\t\t\t\t\telementclick: currentStepData?.elementclick,\r\n\t\t\t\t\t\t\t\tstepIndex: currentStepIndex,\r\n\t\t\t\t\t\t\t\tcurrentStepData: currentStepData,\r\n\t\t\t\t\t\t\t\txpath: currentStepData?.xpath,\r\n\t\t\t\t\t\t\t\tpossibleElementPath: currentStepData?.PossibleElementPath\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tconst element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);\r\n              if (element) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Element found, clicking it:\", element);\r\n\t\t\t\t\t\t\t\telement.click();\r\n\t\t\t\t\t\t\t\thandleNext();\r\n              } else {\r\n              \tconsole.log(\"❌ Element not found for button click interaction\", {\r\n\t\t\t\t\t\t\t\t\txpath: currentStepData?.xpath,\r\n\t\t\t\t\t\t\t\t\tpossibleElementPath: currentStepData?.PossibleElementPath,\r\n\t\t\t\t\t\t\t\t\tstepIndex: currentStepIndex\r\n\t\t\t\t\t\t\t\t});\r\n              }\r\n            } else if (button.ButtonAction.Action === \"previous\") {\r\n\t\t\t\t\t\t\thandlePrevious();\r\n            } else if (button.ButtonAction.Action === \"restart\" || button.ButtonAction.Action === \"Restart\") {\r\n\t\t\t\t\t\t\t// Enhanced restart functionality with auto-scroll\r\n\t\t\t\t\t\t\tconsole.log(\"🔄 Restarting tooltip guide\");\r\n\r\n\t\t\t\t\t\t\t// Reset to the first step (1-based indexing)\r\n\t\t\t\t\t\t\tsetCurrentStepIndex(1);\r\n\t\t\t\t\t\t\tsetCurrentStep(1);\r\n\r\n\t\t\t\t\t\t\t// Check if we need to navigate to a different page (multi-page) or stay on current page (single-page)\r\n\t\t\t\t\t\t\tif (steps[0]?.targetUrl && steps[0].targetUrl.trim() !== window.location.href.trim()) {\r\n\t\t\t\t\t\t\t\t// Multi-page: Navigate to the first step's URL\r\n\t\t\t\t\t\t\t\twindow.location.href = steps[0].targetUrl;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// Single-page: Gentle scroll to first step element or top\r\n\t\t\t\t\t\t\t\tif (steps[0]?.xpath) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"🎯 Gentle scroll to first step element on restart\");\r\n\r\n\t\t\t\t\t\t\t\t\t// Check if first step element exists and use gentle scroll\r\n\t\t\t\t\t\t\t\t\tconst firstElement = getElementByXPath(steps[0].xpath, steps[0]?.PossibleElementPath || \"\");\r\n\t\t\t\t\t\t\t\t\tif (firstElement) {\r\n\t\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\t\tfirstElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Gentle restart scroll completed\");\r\n\t\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(\"❌ Gentle restart scroll failed, scrolling to top\");\r\n\t\t\t\t\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"❌ First step element not found, scrolling to top\");\r\n\t\t\t\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t// No xpath available, just scroll to top\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"ℹ️ No xpath for first step, scrolling to top\");\r\n\t\t\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n            } else if (button.ButtonAction.Action === \"open-url\" && button.ButtonAction.ActionValue === \"new-tab\") {\r\n\t\t\t\t\t\t\twindow.open(button.ButtonAction.TargetUrl, \"_blank\");\r\n            } else if (button.ButtonAction.Action === \"open-url\" && button.ButtonAction.ActionValue === \"same-tab\") {\r\n\t\t\t\t\t\t\twindow.location.href = button.ButtonAction.TargetUrl;\r\n            } else {\r\n\t\t\t\t\t\t\t//onClose();\r\n            }\r\n\t\t\t\t\t};\r\n          return (\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tsx={buttonStyle}\r\n\t\t\t\t\t\t\tonClick={handleClick}\r\n\t\t\t\t\t\t>\r\n              {button.ButtonName}\r\n            </Button>\r\n\t\t\t\t\t);\r\n        })\r\n\t\t\t: null;\r\n\t};\r\n\r\n  const TooltipContent = (\r\n    <>\r\n      <div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n        {enabelCross && (\r\n          <IconButton \r\n            sx={{\r\n              position: \"absolute\",\r\n              boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n              background: \"#fff !important\",\r\n              border: \"1px solid #ccc\",\r\n              zIndex: \"999\",\r\n              borderRadius: \"50px\",\r\n              padding: \"1px !important\",\r\n              float: \"right\",\r\n              top: \"-12px\",\r\n              right: \"-12px\",\r\n\t\t\t\t\t\t\tmargin: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `-${parseInt(canvasStyle?.BorderSize) - 3}px` : \"0px\",\r\n\t\t\t\t\t  }}\r\n\t\t\t\t\t  \r\n          >\r\n            <CloseIcon sx={{ zoom: \"1\", color: \"#000\" }} />\r\n          </IconButton>\r\n        )}\r\n      </div>\r\n\t  <PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"270px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t<div ref={contentRef}\r\n\t\t\tstyle={{\r\n\t\t\t\t// maxHeight: \"270px\",\r\n            overflow: \"hidden\",\r\n            borderRadius: \"4px\",\r\n            padding: getPadding(),\r\n            position: \"relative\",\r\n            zIndex: \"999\",\r\n\t\t\t\t// border: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || \"transparent\"}` : \"none\",\r\n\r\n\t\t\t}}>\r\n          <Box>\r\n\t\t\t\t\t{(!hasOnlyButtons()) && (\r\n              <Box\r\n                display=\"flex\"\r\n                flexDirection=\"column\"\r\n                alignItems={hasOnlyText() ? \"flex-start\" : \"center\"}\r\n                sx={{\r\n                  width: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n                }}\r\n              >\r\n                {renderContent()}\r\n              </Box>\r\n            )}\r\n            {currentStepData?.buttonData?.length > 0 && (\r\n              <Box\r\n                ref={buttonContainerRef}\r\n                display=\"flex\"\r\n                sx={{\r\n                  placeContent: \"center\",\r\n\t\t\t\t\t\t\t\t// padding: hasOnlyButtons() ? \"4px\" : \"10px\",\r\n                  gap: \"4px\",\r\n                  backgroundColor: currentStepData.buttonData[0]?.BackgroundColor,\r\n                  width: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n                  alignItems: \"center\",\r\n                }}\r\n              >\r\n                {renderButtons()}\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        </div>\r\n      </PerfectScrollbar>\r\n      {enableProgress && steps.length>1 && selectedTemplate !== \"Hotspot\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n    </>\r\n\t);\r\n\r\n\t//const overlay = currentStep?.overlay || \"\";\r\n  const overlayStyle = {\r\n    position: \"fixed\" as const,\r\n    top: 0,\r\n    left: 0,\r\n    width: \"100vw\",\r\n    height: \"100vh\",\r\n    backgroundColor: \"transparent\",\r\n    pointerEvents: \"none\",\r\n    zIndex: 9999,\r\n\t};\r\n\r\n  const getOverlaySections = () => {\r\n\t\tif (!targetElement) return null;\r\n\r\n\t\tconst rect = targetElement.getBoundingClientRect();\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\r\n    const sections = {\r\n      top: {\r\n        position: \"fixed\" as const,\r\n        top: 0,\r\n        left: 0,\r\n        width: \"100%\",\r\n        height: `${rect.top}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n      bottom: {\r\n        position: \"fixed\" as const,\r\n        top: `${rect.bottom}px`,\r\n        left: 0,\r\n        width: \"100%\",\r\n        height: `${viewportHeight - rect.bottom}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n      left: {\r\n        position: \"fixed\" as const,\r\n        top: `${rect.top}px`,\r\n        left: 0,\r\n        width: `${rect.left}px`,\r\n        height: `${rect.height}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n      right: {\r\n        position: \"fixed\" as const,\r\n        top: `${rect.top}px`,\r\n        left: `${rect.right}px`,\r\n        width: `${viewportWidth - rect.right}px`,\r\n        height: `${rect.height}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n\t\t};\r\n\r\n\t\treturn sections;\r\n\t};\r\n  const highlightBoxStyle = targetElement\r\n    ? {\r\n        position: \"fixed\" as const,\r\n        top: `${targetElement.getBoundingClientRect().top}px`,\r\n        left: `${targetElement.getBoundingClientRect().left}px`,\r\n        width: `${targetElement.getBoundingClientRect().width}px`,\r\n        height: `${targetElement.getBoundingClientRect().height}px`,\r\n\t\t\t\t// border: '2px solid #fff',\r\n        borderRadius: \"4px\",\r\n        pointerEvents: interactWithPage ? \"auto\" : \"none\",\r\n        zIndex: 0,\r\n      }\r\n    : {}\r\n\r\n  return (\r\n    <>\r\n      {currentStepData?.overlay && targetElement && isElementVisible && selectedTemplate !== \"Tour\" && (\r\n        <Box sx={overlayStyle}>\r\n          {Object.entries(getOverlaySections() || {}).map(([key, style]) => (\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tkey={key}\r\n\t\t\t\t\t\t\tsx={style}\r\n\t\t\t\t\t\t/>\r\n          ))}\r\n\r\n          <Box sx={highlightBoxStyle} />\r\n        </Box>\r\n      )}\r\n      {targetElement && isElementVisible && (\r\n\r\n        <CustomWidthTooltip\r\n          open\r\n          title={TooltipContent}\r\n          placement={tooltipPlacement}\r\n          arrow\r\n          PopperProps={{\r\n            anchorEl: targetElement,\r\n            modifiers: [\r\n              {\r\n                name: \"preventOverflow\",\r\n                options: {\r\n                  boundary: window,\r\n                  altAxis: true,\r\n                  padding: 10,\r\n                },\r\n              },\r\n              {\r\n                name: \"computeStyles\",\r\n                options: {\r\n                  // Disable adaptive positioning to prevent micro-adjustments\r\n                  adaptive: false,\r\n                  // Round positions to prevent sub-pixel rendering\r\n                  roundOffsets: ({ x, y }: { x: number; y: number }) => ({\r\n                    x: Math.round(x),\r\n                    y: Math.round(y),\r\n                  }),\r\n                },\r\n              },\r\n            ],\r\n          }}\r\n          canvasStyle={canvasStyle}\r\n          hasOnlyButtons={hasOnlyButtons()}\r\n          hasOnlyText={hasOnlyText()}\r\n\t\t\t\t\t//dynamicWidth={dynamicWidth}\r\n        >\r\n          <Box\r\n            sx={{\r\n              position: \"absolute\",\r\n              top: targetElement.offsetTop,\r\n              left: targetElement.offsetLeft,\r\n              width: targetElement.offsetWidth,\r\n              height: targetElement.offsetHeight,\r\n            }}\r\n          />\r\n        </CustomWidthTooltip>\r\n      )}\r\n    </>\r\n\t);\r\n};\r\n\r\nexport default TooltipGuide;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,OACEC,MAAM,CACNC,OAAO,CACPC,GAAG,CACHC,cAAc,CACdC,UAAU,CACVC,cAAc,CAEdC,aAAa,CAEbC,UAAU,KACL,eAAe,CAEtB,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,OAASC,MAAM,KAAQ,sBAAsB,CAC7C,MAAO,CAAAC,cAAc,KAAuB,+BAA+B,CAC3E,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAwFrD,KAAM,CAAAC,kBAAkB,CAAGT,MAAM,CAACU,IAAA,MAAC,CAAEC,SAAS,CAAEC,WAAW,CAAEC,cAAc,CAAEC,WAAW,CAAEC,YAAY,CAAE,GAAGC,KAK3G,CAAC,CAAAN,IAAA,oBACAN,IAAA,CAACZ,OAAO,KACHwB,KAAK,CACTC,OAAO,CAAE,CAAEC,MAAM,CAAEP,SAAU,CAAE,CAC/BQ,EAAE,CAAC,gBAAgB,CACnB,CAAC,EACF,CAAC,CAACC,KAAA,MAAC,CAAER,WAAW,CAAEC,cAAc,CAAEC,WAAW,CAAEC,YAK9C,CAAC,CAAAK,KAAA,OAAM,CACL,CAAC,MAAMxB,cAAc,CAACyB,OAAO,EAAE,EAAG,CAChCC,eAAe,CAAE,CAAAV,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEW,eAAe,GAAI,SAAS,CAC9D;AACA;AACIC,OAAO,CAAEX,cAAc,CAAG,KAAK,CAAGD,WAAW,CAACa,OAAO,CACzD;AACIC,YAAY,CAAE,CAAAd,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEe,MAAM,IAAIf,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEgB,YAAY,GAAE,MAAM,CACtEC,SAAS,CAAE,iCAAiC,CAChDC,MAAM,CAAElB,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEmB,UAAU,EAAI,CAAAnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmB,UAAU,IAAK,KAAK,CAAG,GAAGnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmB,UAAU,UAAU,CAAAnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoB,WAAW,GAAI,aAAa,EAAE,CAAG,MAAM,CAC/J;AACA;AACAC,KAAK,CAAG,iBAAiB,CACrBC,QAAQ,CAAEtB,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEuB,KAAK,CAAG,GAAGvB,WAAW,CAACuB,KAAK,aAAa,CAAG,OAAO,CAC1EC,IAAI,CAAE,GAAG,CAAAxB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEyB,WAAW,GAAI,MAAM,aAAa,CACxDC,MAAM,CAAE,GAAG,CAAA1B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE2B,WAAW,GAAI,MAAM,aAC/C,CAAC,CACD,CAAC,MAAM3C,cAAc,CAAC4C,KAAK,EAAE,EAAG,CAC9BC,KAAK,CAAE,CAAA7B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEW,eAAe,GAAI,SAAS,CAChDmB,QAAQ,CAAE,MAAM,CAChB,UAAU,CAAE,CACfC,YAAY,CAAE/B,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEmB,UAAU,EAAI,CAAAnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmB,UAAU,IAAK,KAAK,CAAG,GAAGnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmB,UAAU,EAAE,CAAG,KAAK,CAAE;AACnHa,YAAY,CAAE,CAAAhC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoB,WAAW,GAAI,aAAa,CAAE;AACzDa,YAAY,CAAE,OAAS;AACpB,CACF,CAAC,CACD,CAAC,qBAAqB,EAAG,CAC3BC,MAAM,CAAE,CAAAlC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmC,MAAM,GAAI,KAAO;AACpC,CACF,CAAC,EACH,CAAC,CAED,KAAM,CAAAC,YAAyC,CAAGC,KAAA,EAO5C,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IAP6C,CACjDC,KAAK,CACLC,UAAU,CACVC,OAAO,CACPC,aAAa,CACbC,cAAc,CACdC,IACF,CAAC,CAAAb,KAAA,CACC,GAAI,CAAAc,IAAS,CACb,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7E,QAAQ,CAACyE,cAAc,EAAI,CAAC,CAAC,CAC7E,KAAM,CAAAK,UAAU,CAAG7E,MAAM,CAAiB,IAAI,CAAC,CAC/C,KAAM,CAAA8E,kBAAkB,CAAG9E,MAAM,CAAiB,IAAI,CAAC,CAEvD;AACA,KAAM,CAAA+E,oBAAoB,CAAG/E,MAAM,CAAgBgF,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAErE;AACA,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGpF,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAqF,YAAY,CAAGpF,MAAM,CAAM,IAAI,CAAC,CAEtCF,SAAS,CAAC,IAAM,CACd,GAAI0E,cAAc,GAAKa,SAAS,CAAE,CACnCT,mBAAmB,CAACJ,cAAc,CAAC,CAClC,CACH,CAAC,CAAE,CAACA,cAAc,CAAC,CAAC,CACpB,KAAM,CAAEc,cAAc,CAAEC,gBAAgB,CAAEC,WAAW,CAAEC,aAAa,CAAEC,YAAY,CAAEC,eAAgB,CAAC,CAAG/E,cAAc,CAAEgF,KAAkB,EAAKA,KAAK,CAAC,CAErJ,KAAM,CAAAC,eAAe,CAAGzB,KAAK,CAACO,gBAAgB,CAAG,CAAC,CAAC,CACnD,KAAM,CAACmB,aAAa,CAAEC,gBAAgB,CAAC,CAAGhG,QAAQ,CAAqB,IAAI,CAAC,CAC5E,KAAM,CAACiG,eAAe,CAAEC,kBAAkB,CAAC,CAAGlG,QAAQ,CAAC,CAAEmG,GAAG,CAAE,CAAC,CAAEnD,IAAI,CAAE,CAAE,CAAC,CAAC,CAC3E,KAAM,CAACoD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrG,QAAQ,CAAsC,KAAK,CAAC,CACpG,KAAM,CAACsG,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvG,QAAQ,CAAC,KAAK,CAAC,CAC9D,KAAM,CAAAwG,WAAW,CAAGvG,MAAM,CAA0B,IAAI,CAAC,CAEzD;AACA,KAAM,CAAAwG,eAAe,CAAGxG,MAAM,CAAC,CAAEkG,GAAG,CAAE,CAAC,CAAEnD,IAAI,CAAE,CAAE,CAAC,CAAC,CACpD,KAAM,CAAA0D,gBAAgB,CAAGzG,MAAM,CAAsC,KAAK,CAAC,CAG1E,KAAM,CAAA0G,qBAAqB,CAAIC,OAAoB,EAA0C,CAC7F,KAAM,CAAAjC,IAAI,CAAGiC,OAAO,CAACC,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAC,aAAa,CAAGC,MAAM,CAACC,UAAU,CACvC,KAAM,CAAAC,cAAc,CAAGF,MAAM,CAACG,WAAW,CAEzC,KAAM,CAAAC,QAAQ,CAAGxC,IAAI,CAACwB,GAAG,CACzB,KAAM,CAAAiB,WAAW,CAAGH,cAAc,CAAGtC,IAAI,CAACzB,MAAM,CAChD,KAAM,CAAAmE,SAAS,CAAG1C,IAAI,CAAC3B,IAAI,CAC3B,KAAM,CAAAsE,UAAU,CAAGR,aAAa,CAAGnC,IAAI,CAAC4C,KAAK,CAE7C,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACP,QAAQ,CAAEC,WAAW,CAAEC,SAAS,CAAEC,UAAU,CAAC,CAEvE,GAAIE,QAAQ,GAAKL,QAAQ,CAAE,MAAO,KAAK,CACvC,GAAIK,QAAQ,GAAKJ,WAAW,CAAE,MAAO,QAAQ,CAC7C,GAAII,QAAQ,GAAKH,SAAS,CAAE,MAAO,MAAM,CACzC,MAAO,OAAO,CACf,CAAC,CACD;AAEC,KAAM,CAAAM,uBAAuB,CAAIf,OAAoB,EAAK,CAC1D,KAAM,CAAAjC,IAAI,CAAGiC,OAAO,CAACC,qBAAqB,CAAC,CAAC,CAC1C,MACE,CAAAlC,IAAI,CAAC9B,KAAK,CAAG,CAAC,EACd8B,IAAI,CAACiD,MAAM,CAAG,CAAC,EACfjD,IAAI,CAACwB,GAAG,GAAK,CAAC,EACdxB,IAAI,CAAC3B,IAAI,GAAK,CAAC,EACf,CAAC6E,MAAM,CAACC,KAAK,CAACnD,IAAI,CAACwB,GAAG,CAAC,EACvB,CAAC0B,MAAM,CAACC,KAAK,CAACnD,IAAI,CAAC3B,IAAI,CAAC,CAE7B,CAAC,CACA,KAAM,CAAA+E,iBAAiB,CAAGA,CAACC,KAAa,CAAEC,mBAA2B,GAAyB,CAC9F;AACE,GAAI,CAACD,KAAK,EAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACpCC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAEH,mBAAmB,CAAC,CACzF,GAAIA,mBAAmB,EAAIA,mBAAmB,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC5D,GAAI,CACP,KAAM,CAAAG,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACN,mBAAmB,CAAEK,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAChH,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CAC9B,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CACrC,MAAO,CAAAF,IAAI,CACP,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CACpC,MAAO,CAAAH,IAAI,CAACG,aAAa,CACrB,CACF,CAAE,MAAOC,KAAK,CAAE,CACnBX,OAAO,CAACW,KAAK,CAAC,uCAAuC,CAAEb,mBAAmB,CAAEa,KAAK,CAAC,CAC/E,CACF,CACH,MAAO,KAAI,CACV,CAEA,GAAI,CACL,KAAM,CAAAC,KAAK,CAAG,GAAGf,KAAK,gFAAgF,CACtGG,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEW,KAAK,CAAC,CAC7C,KAAM,CAAAV,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACQ,KAAK,CAAET,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAClG,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CAChC,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CACnC,MAAO,CAAAF,IAAI,CACT,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CAClC,MAAO,CAAAH,IAAI,CAACG,aAAa,CACvB,CAAC,IAAM,IAAIH,IAAI,GAAK,IAAI,CAAE,CAC5B;AACI,GAAIT,mBAAmB,EAAIA,mBAAmB,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjEC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAEH,mBAAmB,CAAC,CACrF,KAAM,CAAAe,cAAc,CAAGV,QAAQ,CAACC,QAAQ,CAACN,mBAAmB,CAAEK,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CACxH,KAAM,CAAAQ,YAAY,CAAGD,cAAc,CAACL,eAAe,CAC9C,GAAIM,YAAY,WAAY,CAAAL,WAAW,CAAE,CAC7C,MAAO,CAAAK,YAAY,CACf,CAAC,IAAM,IAAIA,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEJ,aAAa,CAAE,CAC5C,MAAO,CAAAI,YAAY,CAACJ,aAAa,CAC7B,CACF,CACJ,MAAO,KAAI,CACT,CAAC,IAAM,CACT,MAAO,KAAI,CACT,CACF,CAAE,MAAOC,KAAK,CAAE,CACjBX,OAAO,CAACW,KAAK,CAAC,yBAAyB,CAAEd,KAAK,CAAEc,KAAK,CAAC,CACtD;AACG,GAAIb,mBAAmB,EAAIA,mBAAmB,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC5D,GAAI,CACPC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAEH,mBAAmB,CAAC,CACxF,KAAM,CAAAI,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACN,mBAAmB,CAAEK,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAChH,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CAC9B,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CACrC,MAAO,CAAAF,IAAI,CACP,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CACpC,MAAO,CAAAH,IAAI,CAACG,aAAa,CACrB,CACF,CAAE,MAAOK,aAAa,CAAE,CAC3Bf,OAAO,CAACW,KAAK,CAAC,uCAAuC,CAAEb,mBAAmB,CAAEiB,aAAa,CAAC,CACvF,CACF,CACH,MAAO,KAAI,CACV,CACH,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,QAAAA,CAACvC,OAAoB,CAAEwC,SAAiB,CAA6B,IAA3B,CAAAC,QAAgB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAhE,SAAA,CAAAgE,SAAA,IAAG,GAAG,CACtF;AACA,KAAM,CAAAE,SAAS,CAAG5C,OAAO,CAAC6C,YAAY,CAAG7C,OAAO,CAAC8C,YAAY,CAC7D,KAAM,CAAAC,gBAAgB,CAAGlC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACmC,GAAG,CAACR,SAAS,CAAEI,SAAS,CAAC,CAAC,CAEpE;AACA,GAAI,CACH,GAAI,UAAU,EAAI,CAAA5C,OAAO,EAAI,MAAO,CAAAA,OAAO,CAACiD,QAAQ,GAAK,UAAU,CAAE,CACpEjD,OAAO,CAACiD,QAAQ,CAAC,CAChB1D,GAAG,CAAEwD,gBAAgB,CACrBG,QAAQ,CAAE,QACX,CAAC,CAAC,CACF,OACD,CACD,CAAE,MAAOhB,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC,CACxE,CAEA;AACA,GAAI,CACH,KAAM,CAAA2B,QAAQ,CAAGnD,OAAO,CAACoD,SAAS,CAClC,KAAM,CAAAC,QAAQ,CAAGN,gBAAgB,CAAGI,QAAQ,CAC5C,KAAM,CAAAG,SAAS,CAAGC,WAAW,CAACC,GAAG,CAAC,CAAC,CAEnC,KAAM,CAAAC,aAAa,CAAIC,WAAmB,EAAK,CAC9C,KAAM,CAAAC,OAAO,CAAGD,WAAW,CAAGJ,SAAS,CACvC,KAAM,CAAAM,QAAQ,CAAG/C,IAAI,CAACmC,GAAG,CAACW,OAAO,CAAGlB,QAAQ,CAAE,CAAC,CAAC,CAEhD;AACA,KAAM,CAAAoB,cAAc,CAAIC,CAAS,EAAKA,CAAC,CAAG,GAAG,CAAG,CAAC,CAAGA,CAAC,CAAGA,CAAC,CAAGA,CAAC,CAAG,CAACA,CAAC,CAAG,CAAC,GAAK,CAAC,CAAGA,CAAC,CAAG,CAAC,CAAC,EAAI,CAAC,CAAGA,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CACvG,KAAM,CAAAC,aAAa,CAAGF,cAAc,CAACD,QAAQ,CAAC,CAE9C5D,OAAO,CAACoD,SAAS,CAAGD,QAAQ,CAAIE,QAAQ,CAAGU,aAAc,CAEzD,GAAIH,QAAQ,CAAG,CAAC,CAAE,CACjBI,qBAAqB,CAACP,aAAa,CAAC,CACrC,CACD,CAAC,CAEDO,qBAAqB,CAACP,aAAa,CAAC,CACrC,CAAE,MAAOvB,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CACpE;AACAxB,OAAO,CAACoD,SAAS,CAAGL,gBAAgB,CACrC,CACD,CAAC,CAED;AACA,KAAM,CAAAkB,cAAc,CAAG3K,WAAW,CAAC,SAClC8H,KAAa,CACb8C,mBAA2B,CAC3BC,cAA8C,CAK1C,IAJJ,CAAAC,WAAmB,CAAA1B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAhE,SAAA,CAAAgE,SAAA,IAAG,EAAE,IACxB,CAAA2B,iBAAyB,CAAA3B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAhE,SAAA,CAAAgE,SAAA,IAAG,EAAE,IAC9B,CAAA4B,SAAiB,CAAA5B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAhE,SAAA,CAAAgE,SAAA,IAAG,SAAS,IAC7B,CAAA6B,iBAA8B,CAAA7B,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAhE,SAAA,CAE9B,GAAI,CAAA8F,mBAA0C,CAAG,IAAI,CACrD,GAAI,CAAAC,QAAQ,CAAG,CAAC,CAChB,GAAI,CAAAC,eAAe,CAAGL,iBAAiB,CACvC,GAAI,CAAAM,WAAW,CAAG,KAAK,CAEvB,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACrB,GAAIJ,mBAAmB,CAAE,CACxBK,YAAY,CAACL,mBAAmB,CAAC,CACjCA,mBAAmB,CAAG,IAAI,CAC3B,CACAG,WAAW,CAAG,IAAI,CACnB,CAAC,CAED,KAAM,CAAAG,eAAe,CAAGA,CAAA,GAAM,CAC7B,GAAIH,WAAW,CAAE,OAEjBF,QAAQ,EAAE,CACVlD,OAAO,CAACC,GAAG,CAAC,GAAG8C,SAAS,qBAAqBG,QAAQ,IAAIL,WAAW,eAAeM,eAAe,KAAK,CAAC,CAExG,KAAM,CAAA1E,OAAO,CAAGmB,iBAAiB,CAACC,KAAK,CAAE8C,mBAAmB,CAAC,CAE7D,GAAIlE,OAAO,EAAIe,uBAAuB,CAACf,OAAO,CAAC,CAAE,CAChDuB,OAAO,CAACC,GAAG,CAAC,GAAG8C,SAAS,+BAA+BG,QAAQ,WAAW,CAAC,CAC3EG,OAAO,CAAC,CAAC,CACTT,cAAc,CAACnE,OAAO,CAAC,CACvB,OACD,CAEA,GAAIyE,QAAQ,EAAIL,WAAW,CAAE,CAC5B7C,OAAO,CAACC,GAAG,CAAC,GAAG8C,SAAS,mBAAmBF,WAAW,8BAA8B,CAAC,CACrFQ,OAAO,CAAC,CAAC,CACT,GAAIL,iBAAiB,CAAE,CACtBA,iBAAiB,CAAC,CAAC,CACpB,CACA,OACD,CAEA;AACA,KAAM,CAAAQ,MAAM,CAAGlE,IAAI,CAACmE,MAAM,CAAC,CAAC,CAAG,GAAG,CAAE;AACpCN,eAAe,CAAG7D,IAAI,CAACmC,GAAG,CAAC0B,eAAe,EAAI,GAAG,CAAGK,MAAM,CAAC,CAAE,IAAI,CAAC,CAAE;AAEpEP,mBAAmB,CAAGS,UAAU,CAACH,eAAe,CAAEJ,eAAe,CAAC,CACnE,CAAC,CAED;AACAI,eAAe,CAAC,CAAC,CAEjB;AACA,MAAO,CAAAF,OAAO,CACf,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAM,iBAAiB,CAAGA,CAAClF,OAA6B,CAAEmF,OAAsE,GAAK,CACpI,KAAM,CAAAC,QAAQ,CAAGpF,OAAO,GAAKG,MAAM,CACnC,KAAM,CAAAhB,aAAa,CAAGiG,QAAQ,CAAG1D,QAAQ,CAAC2D,eAAe,CAAGrF,OAAsB,CAElF;AACA,GAAI,CAACoF,QAAQ,EAAI,UAAU,EAAI,CAAApF,OAAO,EAAI,MAAQ,CAAAA,OAAO,CAASiD,QAAQ,GAAK,UAAU,CAAE,CAC1F,GAAI,CACFjD,OAAO,CAASiD,QAAQ,CAACkC,OAAO,CAAC,CAClC,MAAO,KAAI,CACZ,CAAE,MAAOjD,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEU,KAAK,CAAC,CACzD,CACD,CAEA;AACA,GAAIkD,QAAQ,EAAID,OAAO,CAACjC,QAAQ,GAAK,QAAQ,CAAE,CAC9C,GAAI,CACH/C,MAAM,CAAC8C,QAAQ,CAACkC,OAAO,CAAC,CACxB,MAAO,KAAI,CACZ,CAAE,MAAOjD,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEU,KAAK,CAAC,CAC9C,CACD,CAEA;AACA,GAAIiD,OAAO,CAACjC,QAAQ,GAAK,QAAQ,EAAIiC,OAAO,CAAC5F,GAAG,GAAKb,SAAS,CAAE,CAC/D,GAAI,CACH6D,cAAc,CAACpD,aAAa,CAAEgG,OAAO,CAAC5F,GAAG,CAAC,CAC1C,MAAO,KAAI,CACZ,CAAE,MAAO2C,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAEU,KAAK,CAAC,CACtD,CACD,CAEA;AACA,GAAI,CACH,GAAIiD,OAAO,CAAC5F,GAAG,GAAKb,SAAS,CAAE,CAC9BS,aAAa,CAACiE,SAAS,CAAG+B,OAAO,CAAC5F,GAAG,CACtC,CACA,GAAI4F,OAAO,CAAC/I,IAAI,GAAKsC,SAAS,CAAE,CAC/BS,aAAa,CAACmG,UAAU,CAAGH,OAAO,CAAC/I,IAAI,CACxC,CACA,MAAO,KAAI,CACZ,CAAE,MAAO8F,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEU,KAAK,CAAC,CACxD,MAAO,MAAK,CACb,CACD,CAAC,CAED,KAAM,CAAAqD,qBAAqB,CAAGjM,WAAW,CAAC,MAAO6F,aAA0B,CAAEqG,SAA8C,CAAEC,QAAe,GAAK,CAChJ,GAAI,CAACtG,aAAa,CAAE,CACnBoC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAChE,OACD,CAEAD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAE,CAClExB,OAAO,CAAEb,aAAa,CACtBuG,OAAO,CAAEvG,aAAa,CAACuG,OAAO,CAC9B/K,SAAS,CAAEwE,aAAa,CAACxE,SAAS,CAClCQ,EAAE,CAAEgE,aAAa,CAAChE,EAAE,CACpBqK,SAAS,CAAEA,SACZ,CAAC,CAAC,CAEF,GAAI,CACH;AACApH,oBAAoB,CAACuH,OAAO,CAAGvH,oBAAoB,CAACuH,OAAO,CAACC,IAAI,CAAC,SAAY,CAC5E,KAAM,CAAA7H,IAAI,CAAGoB,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAClD,KAAM,CAAAI,cAAc,CAAGF,MAAM,CAACG,WAAW,CACzC,KAAM,CAAAJ,aAAa,CAAGC,MAAM,CAACC,UAAU,CAEvC;AACA,GAAI,CAAAyF,eAAe,CAAG1F,MAAM,CAAC2F,OAAO,CACpC,GAAI,CAAAC,gBAAgB,CAAG5F,MAAM,CAAC6F,OAAO,CAErC;AACA,OAAQR,SAAS,EAChB,IAAK,KAAK,CACT;AACAK,eAAe,CAAG1F,MAAM,CAAC2F,OAAO,CAAG/H,IAAI,CAACwB,GAAG,CAAIc,cAAc,CAAG,GAAI,CACpE,MACD,IAAK,QAAQ,CACZ;AACAwF,eAAe,CAAG1F,MAAM,CAAC2F,OAAO,CAAG/H,IAAI,CAACwB,GAAG,CAAIc,cAAc,CAAG,GAAI,CACpE,MACD,IAAK,MAAM,CACV;AACAwF,eAAe,CAAG1F,MAAM,CAAC2F,OAAO,CAAG/H,IAAI,CAACwB,GAAG,CAAIc,cAAc,CAAG,GAAI,CACpE0F,gBAAgB,CAAG5F,MAAM,CAAC6F,OAAO,CAAGjI,IAAI,CAAC3B,IAAI,CAAI8D,aAAa,CAAG,GAAI,CACrE,MACD,IAAK,OAAO,CACX;AACA2F,eAAe,CAAG1F,MAAM,CAAC2F,OAAO,CAAG/H,IAAI,CAACwB,GAAG,CAAIc,cAAc,CAAG,GAAI,CACpE0F,gBAAgB,CAAG5F,MAAM,CAAC6F,OAAO,CAAGjI,IAAI,CAAC3B,IAAI,CAAI8D,aAAa,CAAG,GAAI,CACrE,MACD,QACC;AACA2F,eAAe,CAAG1F,MAAM,CAAC2F,OAAO,CAAG/H,IAAI,CAACwB,GAAG,CAAIc,cAAc,CAAG,GAAI,CACtE,CAEA;AACA,KAAM,CAAA4F,YAAY,CAAGpF,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEY,QAAQ,CAAC2D,eAAe,CAACxC,YAAY,CAAGxC,cAAc,CAAC,CACxF,KAAM,CAAA6F,aAAa,CAAGrF,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEY,QAAQ,CAAC2D,eAAe,CAACc,WAAW,CAAGjG,aAAa,CAAC,CAEvF2F,eAAe,CAAGhF,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACmC,GAAG,CAAC6C,eAAe,CAAEI,YAAY,CAAC,CAAC,CACtEF,gBAAgB,CAAGlF,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACmC,GAAG,CAAC+C,gBAAgB,CAAEG,aAAa,CAAC,CAAC,CAEzE3E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAE,CAC7CqE,eAAe,CACfE,gBAAgB,CAChBK,cAAc,CAAEjG,MAAM,CAAC2F,OAAO,CAC9BO,cAAc,CAAElG,MAAM,CAAC6F,OAAO,CAC9BM,WAAW,CAAEvI,IACd,CAAC,CAAC,CAEF;AACA,GAAI,CAAAwI,aAAa,CAAG,KAAK,CAEzB;AACA,GAAI,CACHA,aAAa,CAAGrB,iBAAiB,CAAC/E,MAAM,CAAE,CACzCZ,GAAG,CAAEsG,eAAe,CACpBzJ,IAAI,CAAE2J,gBAAgB,CACtB7C,QAAQ,CAAE,QACX,CAAC,CAAC,CACF3B,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAE+E,aAAa,CAAC,CAC1D,CAAE,MAAOrE,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEU,KAAK,CAAC,CACjD,CAEA;AACA,GAAI,CAACqE,aAAa,CAAE,CACnB,GAAI,CACHpG,MAAM,CAAC8C,QAAQ,CAAC8C,gBAAgB,CAAEF,eAAe,CAAC,CAClDU,aAAa,CAAG,IAAI,CACpBhF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC5C,CAAE,MAAOU,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEU,KAAK,CAAC,CAChD,CACD,CAEA;AACA,GAAI,CAACqE,aAAa,CAAE,CACnB,GAAI,CACH7E,QAAQ,CAAC2D,eAAe,CAACjC,SAAS,CAAGyC,eAAe,CACpDnE,QAAQ,CAAC2D,eAAe,CAACC,UAAU,CAAGS,gBAAgB,CACtDrE,QAAQ,CAAC8E,IAAI,CAACpD,SAAS,CAAGyC,eAAe,CACzCnE,QAAQ,CAAC8E,IAAI,CAAClB,UAAU,CAAGS,gBAAgB,CAC3CxE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACtD,CAAE,MAAOU,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEU,KAAK,CAAC,CAC3D,CACD,CAEA;AACA,KAAM,IAAI,CAAA7D,OAAO,CAACC,OAAO,EAAI2G,UAAU,CAAC3G,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtDiD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CAClD,CAAC,CAAC,CAEF,KAAM,CAAApD,oBAAoB,CAACuH,OAAO,CACnC,CAAE,MAAOzD,KAAK,CAAE,CACfX,OAAO,CAACW,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACvD,CACD,CAAC,CAAE,CAACK,cAAc,CAAE2C,iBAAiB,CAAC,CAAC,CAEvC,KAAM,CAAAtB,QAAQ,CAAK5F,gBAAgB,CAAKP,KAAK,CAACkF,MAAM,CAAI,GAAG,CAC3D,KAAM,CAAA8D,gBAAgB,CAAG7I,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE8I,gBAAgB,CAEvDvN,SAAS,CAAC,IAAM,CACd,GAAI0F,WAAW,EAAIb,gBAAgB,EAAI,CAAC,EAAIyI,gBAAgB,GAAK,KAAK,CAAE,CACtElF,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAE5C,gBAAgB,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CAAC,CACxH8C,QAAQ,CAAC8E,IAAI,CAACG,KAAK,CAACC,aAAa,CAAGhI,gBAAgB,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CACjF,KAAM,CAAA+H,KAAK,CAAGjF,QAAQ,CAACmF,aAAa,CAAC,OAAO,CAAC,CAC7CF,KAAK,CAACG,SAAS,CAAG,qDAAqD,CACvEpF,QAAQ,CAACqF,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC,CAChC,MAAO,IAAM,CACXpF,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC,CAChFE,QAAQ,CAAC8E,IAAI,CAACG,KAAK,CAACC,aAAa,CAAG,MAAM,CAC1ClF,QAAQ,CAACqF,IAAI,CAACE,WAAW,CAACN,KAAK,CAAC,CAClC,CAAC,CACH,CACA,MAAO,IAAM,CACX;AACApF,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC,CACzFE,QAAQ,CAAC8E,IAAI,CAACG,KAAK,CAACC,aAAa,CAAG,MAAM,CAAE;AAC9C,CAAC,CACH,CAAC,CAAE,CAAC5I,gBAAgB,CAAEyI,gBAAgB,CAAC,CAAC,CAEzC;AACCtN,SAAS,CAAC,IAAM,CACd,GAAI0F,WAAW,EAAIb,gBAAgB,EAAI,CAAC,EAAIkB,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEgI,OAAO,EAAIT,gBAAgB,GAAK,KAAK,CAAE,CACrG/E,QAAQ,CAAC8E,IAAI,CAACG,KAAK,CAACQ,QAAQ,CAAG,QAAQ,CACtC,CAAC,IAAM,CACRzF,QAAQ,CAAC8E,IAAI,CAACG,KAAK,CAACQ,QAAQ,CAAG,EAAE,CAChC,CAEF;AACE,MAAO,IAAM,CACdzF,QAAQ,CAAC8E,IAAI,CAACG,KAAK,CAACQ,QAAQ,CAAG,EAAE,CAClC,CAAC,CACD,CAAC,CAAE,CAACjI,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgI,OAAO,CAAET,gBAAgB,CAAC,CAAC,CACjD;AACAtN,SAAS,CAAC,IAAM,CACf,KAAM,CAAAiO,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAIlJ,UAAU,CAACyH,OAAO,CAAE,CACvB;AACAzH,UAAU,CAACyH,OAAO,CAACgB,KAAK,CAAC3F,MAAM,CAAG,MAAM,CACxC,KAAM,CAAAqG,aAAa,CAAGnJ,UAAU,CAACyH,OAAO,CAAC9C,YAAY,CACrD,KAAM,CAAAyE,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGF,aAAa,CAAGC,eAAe,CAGpD9I,iBAAiB,CAAC+I,YAAY,CAAC,CAE/B;AACA,GAAI9I,YAAY,CAACkH,OAAO,CAAE,CACzB;AACA,GAAIlH,YAAY,CAACkH,OAAO,CAAC6B,YAAY,CAAE,CACtC/I,YAAY,CAACkH,OAAO,CAAC6B,YAAY,CAAC,CAAC,CACpC,CACA;AACAvC,UAAU,CAAC,IAAM,CAChB,GAAIxG,YAAY,CAACkH,OAAO,EAAIlH,YAAY,CAACkH,OAAO,CAAC6B,YAAY,CAAE,CAC9D/I,YAAY,CAACkH,OAAO,CAAC6B,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDJ,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAK,QAAQ,CAAG,CAChBxC,UAAU,CAACmC,iBAAiB,CAAE,EAAE,CAAC,CACjCnC,UAAU,CAACmC,iBAAiB,CAAE,GAAG,CAAC,CAClCnC,UAAU,CAACmC,iBAAiB,CAAE,GAAG,CAAC,CAClCnC,UAAU,CAACmC,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAM,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAIzJ,UAAU,CAACyH,OAAO,EAAIxF,MAAM,CAACyH,cAAc,CAAE,CAChDF,cAAc,CAAG,GAAI,CAAAE,cAAc,CAAC,IAAM,CACzC3C,UAAU,CAACmC,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFM,cAAc,CAACG,OAAO,CAAC3J,UAAU,CAACyH,OAAO,CAAC,CAC3C,CAGA,GAAIzH,UAAU,CAACyH,OAAO,EAAIxF,MAAM,CAAC2H,gBAAgB,CAAE,CAClDH,gBAAgB,CAAG,GAAI,CAAAG,gBAAgB,CAAC,IAAM,CAC7C7C,UAAU,CAACmC,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFO,gBAAgB,CAACE,OAAO,CAAC3J,UAAU,CAACyH,OAAO,CAAE,CAC5CoC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZT,QAAQ,CAACU,OAAO,CAACtD,YAAY,CAAC,CAC9B,GAAI6C,cAAc,CAAE,CACnBA,cAAc,CAACU,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIT,gBAAgB,CAAE,CACrBA,gBAAgB,CAACS,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAAClJ,eAAe,CAAEL,WAAW,CAAC,CAAC,CAEjC,KAAM,CAAAwJ,uBAAuB,CAAGA,CAAA,GAAM,CACtC;AACA;AACA;AACA;AACA;AAEA;AACE9G,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAE,CAChEJ,KAAK,CAAElC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAC7BC,mBAAmB,CAAEnC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmC,mBAAmB,CACzDiH,SAAS,CAAEtK,gBAAgB,CAC9Be,YAAY,CAAEA,YACf,CAAC,CAAC,CAEF,KAAM,CAAAiB,OAAO,CAAGmB,iBAAiB,CAACjC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAAClC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmC,mBAAmB,CAAC,CAC5F,GAAI,CAACrB,OAAO,CAAE,CACfuB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEtC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAAE,sBAAsB,CAAElC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmC,mBAAmB,CAAC,CACzIjC,gBAAgB,CAAC,IAAI,CAAC,CACtBO,mBAAmB,CAAC,KAAK,CAAC,CAC1B,OACC,CAEF,KAAM,CAAA4I,OAAO,CAAGxH,uBAAuB,CAACf,OAAO,CAAC,CAChD;AAEE,GAAI,CAACuI,OAAO,CAAE,CACfnJ,gBAAgB,CAAC,IAAI,CAAC,CACtBO,mBAAmB,CAAC,KAAK,CAAC,CAC1B,OACC,CAEF,KAAM,CAAA5B,IAAI,CAAGiC,OAAO,CAACC,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAuI,OAAO,CAAGC,UAAU,CAAC,CAAAvJ,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwJ,mBAAmB,GAAI,GAAG,CAAC,CACvE,KAAM,CAAAC,OAAO,CAAGF,UAAU,CAAC,CAAAvJ,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE0J,mBAAmB,GAAI,GAAG,CAAC,CAEvExJ,gBAAgB,CAACY,OAAO,CAAC,CACzBL,mBAAmB,CAAC,IAAI,CAAC,CAEvB;AACA,GAAI,CAAAkJ,YAAiD,CACrD,GAAI3J,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAE4J,YAAY,CAAE,CACjCD,YAAY,CAAG9I,qBAAqB,CAACC,OAAO,CAAC,CAC/C,CAAC,IAAM,KAAA+I,qBAAA,CACL,KAAM,CAAAC,eAAe,CAAG,CAAC,KAAK,CAAE,MAAM,CAAE,OAAO,CAAE,QAAQ,CAAU,CACnE,KAAM,CAAAxD,SAAS,CAAG,CAAAtG,eAAe,SAAfA,eAAe,kBAAA6J,qBAAA,CAAf7J,eAAe,CAAE+J,MAAM,UAAAF,qBAAA,iBAAvBA,qBAAA,CAAyBG,QAAQ,GAAI,QAAQ,CAC/DL,YAAY,CAAGG,eAAe,CAACG,QAAQ,CAAC3D,SAAgB,CAAC,CACpDA,SAAS,CACV,QAAQ,CACd,CAGA,GAAI1F,gBAAgB,CAAC6F,OAAO,GAAKkD,YAAY,CAAE,CAC7C/I,gBAAgB,CAAC6F,OAAO,CAAGkD,YAAY,CACvCpJ,mBAAmB,CAACoJ,YAAY,CAAC,CACnC,CAGA,KAAM,CAAAO,WAAW,CAAG,CAClB7J,GAAG,CAAEsB,IAAI,CAACwI,KAAK,CAACtL,IAAI,CAACwB,GAAG,CAAGY,MAAM,CAAC2F,OAAO,CAAG6C,OAAO,CAAC,CACpDvM,IAAI,CAAEyE,IAAI,CAACwI,KAAK,CAACtL,IAAI,CAAC3B,IAAI,CAAG+D,MAAM,CAAC6F,OAAO,CAAGwC,OAAO,CACvD,CAAC,CAGD,KAAM,CAAAc,eAAe,CACnBzI,IAAI,CAAC0I,GAAG,CAAC1J,eAAe,CAAC8F,OAAO,CAACpG,GAAG,CAAG6J,WAAW,CAAC7J,GAAG,CAAC,CAAG,CAAC,EAC3DsB,IAAI,CAAC0I,GAAG,CAAC1J,eAAe,CAAC8F,OAAO,CAACvJ,IAAI,CAAGgN,WAAW,CAAChN,IAAI,CAAC,CAAG,CAAC,CAE/D,GAAIkN,eAAe,CAAE,CACnBzJ,eAAe,CAAC8F,OAAO,CAAGyD,WAAW,CACrC9J,kBAAkB,CAAC8J,WAAW,CAAC,CACjC,CACF,CAAC,CAED;AACD,KAAM,CAAAI,UAAU,CAAGlQ,WAAW,CAAC,SAAY,CAC1C,GAAIsF,gBAAgB,GAAK,MAAM,CAAE,CAChC;AACA,KAAM,CAAA6K,SAAS,CAAGzL,gBAAgB,CAAG,CAAC,CAEtC,GAAIyL,SAAS,EAAIhM,KAAK,CAACkF,MAAM,CAAE,CAC9B;AACA,KAAM,CAAA+G,YAAY,CAAGjM,KAAK,CAACgM,SAAS,CAAG,CAAC,CAAC,CAEzClI,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAE,CACnDmI,YAAY,CAAE3L,gBAAgB,CAC9ByL,SAAS,CAAEA,SAAS,CACpBrI,KAAK,CAAEsI,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEtI,KAAK,CAC1BwI,SAAS,CAAE,QAAQH,SAAS,EAC7B,CAAC,CAAC,CAEF;AACA,GAAIC,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEtI,KAAK,CAAE,CACxBG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAE,CACxDJ,KAAK,CAAEsI,YAAY,CAACtI,KAAK,CACzBkH,SAAS,CAAEmB,SAAS,CACpBG,SAAS,CAAE,QAAQH,SAAS,EAC7B,CAAC,CAAC,CAEF;AACA,KAAM,CAAAI,aAAa,CAAG,GAAI,CAAAxL,OAAO,CAAQC,OAAO,EAAK,CACpD;AACA2F,cAAc,CACbyF,YAAY,CAACtI,KAAK,EAAI,EAAE,CACxBsI,YAAY,CAACrI,mBAAmB,EAAI,EAAE,CACtC,KAAO,CAAAyI,YAAY,EAAK,CACvBvI,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE,CACpCxB,OAAO,CAAE8J,YAAY,CACrBpE,OAAO,CAAEoE,YAAY,CAACpE,OAAO,CAC7B/K,SAAS,CAAEmP,YAAY,CAACnP,SAAS,CACjCQ,EAAE,CAAE2O,YAAY,CAAC3O,EAClB,CAAC,CAAC,CAEF;AACA,KAAM,CAAA4C,IAAI,CAAG+L,YAAY,CAAC7J,qBAAqB,CAAC,CAAC,CACjD,KAAM,CAAAI,cAAc,CAAGF,MAAM,CAACG,WAAW,CACzC,KAAM,CAAAJ,aAAa,CAAGC,MAAM,CAACC,UAAU,CAEvC;AACA,KAAM,CAAA2J,mBAAmB,CACxBhM,IAAI,CAACwB,GAAG,EAAI,CAAC,EAAE,EAAI;AACnBxB,IAAI,CAAC3B,IAAI,EAAI,CAAC,EAAE,EAAI;AACpB2B,IAAI,CAACzB,MAAM,EAAI+D,cAAc,CAAG,EAAE,EAAI;AACtCtC,IAAI,CAAC4C,KAAK,EAAIT,aAAa,CAAG,EAAE,EAAI;AACpCnC,IAAI,CAAC9B,KAAK,CAAG,CAAC,EACd8B,IAAI,CAACiD,MAAM,CAAG,CACd,CAED,GAAI+I,mBAAmB,CAAE,CACxBxI,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC,CAClE;AACA,GAAI,CACHsI,YAAY,CAACE,cAAc,CAAC,CAC3B9G,QAAQ,CAAE,QAAQ,CAClB+G,KAAK,CAAE,SAAS,CAAE;AAClBC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAE,MAAOhI,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEU,KAAK,CAAC,CACxD,CACD,CAAC,IAAM,CACNX,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC,CAC7D;AACA,GAAI,CACHsI,YAAY,CAACE,cAAc,CAAC,CAC3B9G,QAAQ,CAAE,QAAQ,CAClB+G,KAAK,CAAE,QAAQ,CAAE;AACjBC,MAAM,CAAE,SACT,CAAC,CAAC,CACF3I,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CACpD,CAAE,MAAO2I,WAAW,CAAE,CACrB5I,OAAO,CAACW,KAAK,CAAC,uBAAuB,CAAEiI,WAAW,CAAC,CACpD,CACD,CAEA7L,OAAO,CAAC,CAAC,CACV,CAAC,CACD,EAAE,CAAE;AACJ,EAAE,CAAE;AACJ,mBAAmB,CACnB,IAAM,CACLiD,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC,CAChFlD,OAAO,CAAC,CAAC,CACV,CACD,CAAC,CACF,CAAC,CAAC,CAEF,GAAI,CACH,KAAM,CAAAuL,aAAa,CACnBtI,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAC5D,CAAE,MAAOU,KAAK,CAAE,CACfX,OAAO,CAACW,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CACjD,CACD,CAAC,IAAM,CACNX,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC,CACxE,CAEA;AACAD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAExD,gBAAgB,CAAE,IAAI,CAAEyL,SAAS,CAAC,CAC7ExL,mBAAmB,CAACwL,SAAS,CAAC,CAC9B9K,cAAc,CAACE,WAAW,CAAG,CAAC,CAAC,CAE/B;AACA,KAAM,IAAI,CAAAR,OAAO,CAACC,OAAO,EAAI2G,UAAU,CAAC3G,OAAO,CAAE,EAAE,CAAC,CAAC,CAEtD,CAAC,IAAM,CACNiD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE,CAC9CxD,gBAAgB,CAChByL,SAAS,CACTW,UAAU,CAAE3M,KAAK,CAACkF,MACnB,CAAC,CAAC,CACF;AACA,OACD,CACD,CAAC,IAAM,CACN;AACA,GAAI9D,WAAW,EAAGpB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEkF,MAAM,EAAE,CAChC1E,mBAAmB,CAAEoM,IAAS,EAAKxJ,IAAI,CAACC,GAAG,CAACuJ,IAAI,CAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CACzD1L,cAAc,CAACE,WAAW,CAAG,CAAC,CAAC,CAChC,CACD,CACD,CAAC,CAAE,CAACb,gBAAgB,CAAEP,KAAK,CAAEmB,gBAAgB,CAAEC,WAAW,CAAEoF,cAAc,CAAElE,qBAAqB,CAAEwF,qBAAqB,CAAE5G,cAAc,CAAEV,mBAAmB,CAAC,CAAC,CAE9J;AACD,KAAM,CAAAqM,cAAc,CAAGhR,WAAW,CAAC,SAAY,CAC9C,GAAIsF,gBAAgB,GAAK,MAAM,CAAE,CAChC,KAAM,CAAA2L,SAAS,CAAG1J,IAAI,CAACC,GAAG,CAAC9C,gBAAgB,CAAG,CAAC,CAAE,CAAC,CAAC,CAEnD,GAAIuM,SAAS,EAAI,CAAC,CAAE,CACnBhJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE,CAC9CmI,YAAY,CAAE3L,gBAAgB,CAC9BuM,SAAS,CAAEA,SACZ,CAAC,CAAC,CAEF;AACA,GAAIA,SAAS,GAAK,CAAC,CAAE,CACpBhJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CACtE,GAAI,CACHrB,MAAM,CAAC8C,QAAQ,CAAC,CACf1D,GAAG,CAAE,CAAC,CACN2D,QAAQ,CAAE,QACX,CAAC,CAAC,CACH,CAAE,MAAOhB,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEU,KAAK,CAAC,CAC5D,CACD,CAAC,IAAM,CACN;AACA,KAAM,CAAAsI,YAAY,CAAG/M,KAAK,CAAC8M,SAAS,CAAG,CAAC,CAAC,CACzC,GAAIC,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEpJ,KAAK,CAAE,CACxBG,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC,CAEhFyD,UAAU,CAAC,IAAM,CAChB,KAAM,CAAAwF,WAAW,CAAGtJ,iBAAiB,CAACqJ,YAAY,CAACpJ,KAAK,CAAEoJ,YAAY,CAACnJ,mBAAmB,EAAI,EAAE,CAAC,CACjG,GAAIoJ,WAAW,CAAE,CAChB,KAAM,CAAA1M,IAAI,CAAG0M,WAAW,CAACxK,qBAAqB,CAAC,CAAC,CAChD,KAAM,CAAAyK,WAAW,CAChB3M,IAAI,CAACzB,MAAM,CAAG,CAAC,EACfyB,IAAI,CAACwB,GAAG,CAAGY,MAAM,CAACG,WAAW,EAC7BvC,IAAI,CAAC4C,KAAK,CAAG,CAAC,EACd5C,IAAI,CAAC3B,IAAI,CAAG+D,MAAM,CAACC,UACnB,CAED,GAAIsK,WAAW,CAAE,CAChBnJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CACtE,GAAI,CACHiJ,WAAW,CAACT,cAAc,CAAC,CAC1B9G,QAAQ,CAAE,QAAQ,CAClB+G,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAE,MAAOhI,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAEU,KAAK,CAAC,CAC7D,CACD,CACD,CACD,CAAC,CAAE,GAAG,CAAC,CACR,CACD,CAEA;AACAjE,mBAAmB,CAACsM,SAAS,CAAC,CAC9B5L,cAAc,CAACE,WAAW,CAAG,CAAC,CAAC,CAE/B;AACA,KAAM,IAAI,CAAAR,OAAO,CAACC,OAAO,EAAI2G,UAAU,CAAC3G,OAAO,CAAE,EAAE,CAAC,CAAC,CACtD,CACD,CAAC,IAAM,CACNK,cAAc,CAACE,WAAW,CAAG,CAAC,CAAC,CAChC,CACD,CAAC,CAAE,CAACb,gBAAgB,CAAEY,gBAAgB,CAAEC,WAAW,CAAEqG,iBAAiB,CAAEvG,cAAc,CAAEV,mBAAmB,CAAC,CAAC,CAC5G9E,SAAS,CAAC,IAAM,KAAAwR,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACd;AACAvJ,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAE,CACzDxD,gBAAgB,CAChB+M,YAAY,CAAE7L,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE6L,YAAY,CAC3CC,QAAQ,CAAE9L,eAAe,SAAfA,eAAe,kBAAAyL,qBAAA,CAAfzL,eAAe,CAAE6L,YAAY,UAAAJ,qBAAA,iBAA7BA,qBAAA,CAA+BK,QAAQ,CACjDC,EAAE,CAAE/L,eAAe,SAAfA,eAAe,kBAAA0L,sBAAA,CAAf1L,eAAe,CAAE6L,YAAY,UAAAH,sBAAA,iBAA7BA,sBAAA,CAA+BK,EAAE,CACrC7J,KAAK,CAAElC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAC1B,CAAC,CAAC,CAEF,GAAI,CAAAlC,eAAe,SAAfA,eAAe,kBAAA2L,sBAAA,CAAf3L,eAAe,CAAE6L,YAAY,UAAAF,sBAAA,iBAA7BA,sBAAA,CAA+BG,QAAQ,IAAK,SAAS,EAAI,CAAA9L,eAAe,SAAfA,eAAe,kBAAA4L,sBAAA,CAAf5L,eAAe,CAAE6L,YAAY,UAAAD,sBAAA,iBAA7BA,sBAAA,CAA+BE,QAAQ,IAAK,QAAQ,CAAE,CACpH,KAAM,CAAAhL,OAAO,CAAGmB,iBAAiB,CAACjC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAAClC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmC,mBAAmB,CAAC,CAC3F,GAAIrB,OAAO,CAAE,CACXuB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAExB,OAAO,CAAC,CAC1D,KAAM,CAAAkL,WAAW,CAAGA,CAAA,GAAM,CAC7B3J,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAC3DgI,UAAU,CAAC,CAAC,CACb,CAAC,CAEDxJ,OAAO,CAACmL,gBAAgB,CAAC,OAAO,CAAED,WAAW,CAAC,CAC1C,MAAO,IAAM,CAChBlL,OAAO,CAACoL,mBAAmB,CAAC,OAAO,CAAEF,WAAW,CAAC,CAClD,CAAC,CACG,CAAC,IAAM,CACP3J,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEtC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAAC,CACvE,CACJ,CAAC,IAAM,KAAAiK,sBAAA,CACL9J,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEtC,eAAe,SAAfA,eAAe,kBAAAmM,sBAAA,CAAfnM,eAAe,CAAE6L,YAAY,UAAAM,sBAAA,iBAA7BA,sBAAA,CAA+BL,QAAQ,CAAC,CAC/F,CACD,CAAC,CAAE,CAAC9L,eAAe,CAAEsK,UAAU,CAAC,CAAC,CAEhCrQ,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmS,gBAAgB,CAAGA,CAAA,GAAM,CAChCtH,qBAAqB,CAACqE,uBAAuB,CAAC,CAC/C,CAAC,CACDzI,WAAW,CAAC+F,OAAO,CAAG,GAAI,CAAAmC,gBAAgB,CAACwD,gBAAgB,CAAC,CAC5D,KAAM,CAAAC,UAAU,CAAG7J,QAAQ,CAAC8E,IAAI,CAC9B5G,WAAW,CAAC+F,OAAO,CAACkC,OAAO,CAAC0D,UAAU,CAAE,CACtCxD,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBuD,aAAa,CAAE,IACnB,CAAC,CAAC,CACFnD,uBAAuB,CAAC,CAAC,CACvB,MAAO,IAAM,KAAAoD,oBAAA,CACd,CAAAA,oBAAA,CAAA7L,WAAW,CAAC+F,OAAO,UAAA8F,oBAAA,iBAAnBA,oBAAA,CAAqBrD,UAAU,CAAC,CAAC,CAClC,CAAC,CACF,CAAC,CAAE,CAAClJ,eAAe,CAAExB,UAAU,CAAC,CAAC,CAEhCvE,SAAS,CAAC,IAAM,CAChB,KAAM,CAAAuS,qBAAqB,CAAGA,CAAA,GAAM,CACnC1H,qBAAqB,CAACqE,uBAAuB,CAAC,CAC/C,CAAC,CAEDlI,MAAM,CAACgL,gBAAgB,CAAC,QAAQ,CAAEO,qBAAqB,CAAC,CACxDvL,MAAM,CAACgL,gBAAgB,CAAC,QAAQ,CAAEO,qBAAqB,CAAC,CAEtD,MAAO,IAAM,CACdvL,MAAM,CAACiL,mBAAmB,CAAC,QAAQ,CAAEM,qBAAqB,CAAC,CAC3DvL,MAAM,CAACiL,mBAAmB,CAAC,QAAQ,CAAEM,qBAAqB,CAAC,CAC5D,CAAC,CACF,CAAC,CAAE,CAACxM,eAAe,CAAExB,UAAU,CAAC,CAAC,CAChCvE,SAAS,CAAC,IAAM,CAChBkP,uBAAuB,CAAC,CAAC,CAC1B,CAAC,CAAE,CAACnJ,eAAe,CAAExB,UAAU,CAAEK,IAAI,CAAC,CAAC,CAAE;AAEzC;AACA5E,SAAS,CAAC,IAAM,CACf,GAAI,EAAC+F,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEkC,KAAK,EAAE,CAC5B,OACD,CAEA;AACA,KAAM,CAAAuK,SAAS,CAAG1G,UAAU,CAAC,IAAM,CAClC,KAAM,CAAA2G,cAAc,CAAGzK,iBAAiB,CAACjC,eAAe,CAACkC,KAAK,CAAElC,eAAe,CAACmC,mBAAmB,EAAI,EAAE,CAAC,CAE1G,GAAIuK,cAAc,CAAE,CACnB,KAAM,CAAA7N,IAAI,CAAG6N,cAAc,CAAC3L,qBAAqB,CAAC,CAAC,CACnD,KAAM,CAAAI,cAAc,CAAGF,MAAM,CAACG,WAAW,CACzC,KAAM,CAAAJ,aAAa,CAAGC,MAAM,CAACC,UAAU,CAEvC;AACA,KAAM,CAAAyL,qBAAqB,CAC1B9N,IAAI,CAACzB,MAAM,CAAG,CAAC,EAAI;AACnByB,IAAI,CAACwB,GAAG,CAAGc,cAAc,EAAI;AAC7BtC,IAAI,CAAC4C,KAAK,CAAG,CAAC,EAAI;AAClB5C,IAAI,CAAC3B,IAAI,CAAG8D,aAAc;AAC1B,CAED,GAAI2L,qBAAqB,CAAE,CAC1BtK,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CACzE,GAAI,CACHoK,cAAc,CAAC5B,cAAc,CAAC,CAC7B9G,QAAQ,CAAE,QAAQ,CAClB+G,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAE,MAAOhI,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEU,KAAK,CAAC,CACvD,CACD,CACD,CACD,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACZ2C,YAAY,CAAC8G,SAAS,CAAC,CACxB,CAAC,CACF,CAAC,CAAE,CAACzM,eAAe,CAAC,CAAC,CACrB,KAAM,CAAAtE,WAAW,CAAG,CAAAsE,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+J,MAAM,GAAI,CAAC,CAAC,CAAE;AACnD,KAAM,CAAA6C,cAAc,CAAGlO,aAAa,CAACmO,cAAc,EAAI,KAAK,CAC3D,QAAS,CAAAC,mBAAmBA,CAACpO,aAAkB,CAAE,CAC/C,GAAI,CAAAA,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEqO,gBAAgB,IAAK,GAAG,CAAE,CAC9C,MAAO,MAAM,CACZ,CAAC,IAAM,IAAI,CAAArO,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEqO,gBAAgB,IAAK,GAAG,CAAE,CACrD,MAAO,QAAQ,CACd,CAAC,IAAM,IAAI,CAAArO,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEqO,gBAAgB,IAAK,GAAG,CAAE,CACrD,MAAO,aAAa,CACnB,CAAC,IAAM,CACR,MAAO,aAAa,CACnB,CACF,CACD,KAAM,CAAAC,gBAAgB,CAAGF,mBAAmB,CAACpO,aAAa,CAAC,CAE3D,KAAM,CAAAuO,WAAW,CAAGjN,eAAe,SAAfA,eAAe,kBAAAhC,qBAAA,CAAfgC,eAAe,CAAEkN,KAAK,UAAAlP,qBAAA,iBAAtBA,qBAAA,CAAwBmP,aAAa,CACxD,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC5B,KAAM,CAAAC,QAAQ,CACb,CAAA1N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2N,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC,IAAI5N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2N,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAClG,KAAM,CAAAC,OAAO,CAAGC,KAAK,CAACC,OAAO,CAAC/N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,CACnDhO,eAAe,CAACgO,OAAO,CAACC,IAAI,CAAEC,IAAI,EAAK,CAAAA,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEC,IAAI,GAAI,MAAO,CAAAD,IAAI,CAACC,IAAI,GAAK,QAAQ,EAAID,IAAI,CAACC,IAAI,CAAC/L,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CACjH,OAAOpC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,IAAK,QAAQ,eAAIhU,KAAK,CAACoU,cAAc,CAACpO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,CAC/F,KAAM,CAAAK,SAAS,CAAG,CAChB7Q,QAAQ,CAAE,MAAM,CAChB8Q,UAAU,CAAE,KAAK,CACjBC,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvBjR,KAAK,CAAE,OACX,CAAC,CACC,mBACEnC,KAAA,CAACb,GAAG,EAAAkU,QAAA,EACDf,QAAQ,eACPxS,IAAA,CAACX,GAAG,EACFmU,SAAS,CAAC,KAAK,CACfC,GAAG,CAAE3O,eAAe,SAAfA,eAAe,kBAAAqN,qBAAA,CAAfrN,eAAe,CAAE4O,eAAe,UAAAvB,qBAAA,iBAAhCA,qBAAA,CAAkCwB,GAAI,CAC3CC,GAAG,CAAE,CAAA9O,eAAe,SAAfA,eAAe,kBAAAsN,sBAAA,CAAftN,eAAe,CAAE4O,eAAe,UAAAtB,sBAAA,iBAAhCA,sBAAA,CAAkCyB,OAAO,GAAI,YAAa,CAC/DC,EAAE,CAAE,CACF5S,eAAe,CAAE,CAAA4D,eAAe,SAAfA,eAAe,kBAAAuN,sBAAA,CAAfvN,eAAe,CAAE4O,eAAe,UAAArB,sBAAA,iBAAhCA,sBAAA,CAAkClR,eAAe,GAAI,aAAa,CACnF4S,SAAS,CAAE,CAAAjP,eAAe,SAAfA,eAAe,kBAAAwN,sBAAA,CAAfxN,eAAe,CAAE4O,eAAe,UAAApB,sBAAA,iBAAhCA,sBAAA,CAAkC0B,GAAG,GAAI,OAAO,CAC3DC,SAAS,CAAE,CAAAnP,eAAe,SAAfA,eAAe,kBAAAyN,sBAAA,CAAfzN,eAAe,CAAE4O,eAAe,UAAAnB,sBAAA,iBAAhCA,sBAAA,CAAkC2B,aAAa,GAAI,MAAM,CACpErS,KAAK,CAAE,MACT,CAAE,CACH,CACF,CACA8Q,OAAO,eACN3S,IAAA,CAACX,GAAG,EACFkB,SAAS,CAAC,eAAe,CAChCuT,EAAE,CAAE,CAAEK,MAAM,CAAE,cAAc,CAAE,GAAGhB,SAAS,CAAE,KAAK,CAAE,CAClDgB,MAAM,CAAE,OACP,CAAE,CAAE,CAAKC,uBAAuB,CAAE,CAC3BC,MAAM,CAAEzB,KAAK,CAACC,OAAO,CAAC/N,eAAe,CAACgO,OAAO,CAAC,CAC1ChO,eAAe,CAACgO,OAAO,CAC/BwB,GAAG,CAAEtB,IAAS,EACdA,IAAI,CAACC,IAAI,CAACsB,OAAO,CAAC,MAAM,CAAE,+CAA+C,CAC1E,CAAC,CACWC,IAAI,CAAC,OAAO,CAAC,CAChB,MAAO,CAAA1P,eAAe,CAACgO,OAAO,GAAK,QAAQ,CACzChO,eAAe,CAACgO,OAAO,CAACyB,OAAO,CAAC,MAAM,CAAE,+CAA+C,CAAC,CACxF,EACR,CAAE,CACH,CAEN,EACG,CAAC,CAER,CAAC,CAED;AACA,KAAM,CAAA9T,cAAc,CAAGA,CAAA,GAAM,KAAAgU,qBAAA,CAC5B,KAAM,CAAAjC,QAAQ,CACb,CAAA1N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2N,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC,IAAI5N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2N,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EACpG,KAAM,CAAAC,OAAO,CAAGC,KAAK,CAACC,OAAO,CAAC/N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,CACpDhO,eAAe,CAACgO,OAAO,CAACC,IAAI,CAAEC,IAAI,EAAK,CAAAA,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEC,IAAI,GAAI,MAAO,CAAAD,IAAI,CAACC,IAAI,GAAK,QAAQ,EAAID,IAAI,CAACC,IAAI,CAAC/L,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC9G,OAAOpC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,IAAK,QAAQ,eAAIhU,KAAK,CAACoU,cAAc,CAACpO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,CACjG,KAAM,CAAA4B,UAAU,CAAG,CAAA5P,eAAe,SAAfA,eAAe,kBAAA2P,qBAAA,CAAf3P,eAAe,CAAE6P,UAAU,UAAAF,qBAAA,iBAA3BA,qBAAA,CAA6BlM,MAAM,EAAG,CAAC,CAE1D,MAAO,CAAAmM,UAAU,EAAI,CAAClC,QAAQ,EAAI,CAACG,OAAO,CAC3C,CAAC,CAED;AACA,KAAM,CAAAjS,WAAW,CAAGA,CAAA,GAAM,KAAAkU,sBAAA,CACzB,KAAM,CAAApC,QAAQ,CACb,CAAA1N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2N,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC,IAAI5N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE2N,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EACpG,KAAM,CAAAC,OAAO,CAAGC,KAAK,CAACC,OAAO,CAAC/N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,CACpDhO,eAAe,CAACgO,OAAO,CAACC,IAAI,CAAEC,IAAI,EAAK,CAAAA,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEC,IAAI,GAAI,MAAO,CAAAD,IAAI,CAACC,IAAI,GAAK,QAAQ,EAAID,IAAI,CAACC,IAAI,CAAC/L,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC9G,OAAOpC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,IAAK,QAAQ,eAAIhU,KAAK,CAACoU,cAAc,CAACpO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,CACjG,KAAM,CAAA4B,UAAU,CAAG,CAAA5P,eAAe,SAAfA,eAAe,kBAAA8P,sBAAA,CAAf9P,eAAe,CAAE6P,UAAU,UAAAC,sBAAA,iBAA3BA,sBAAA,CAA6BrM,MAAM,EAAG,CAAC,CAE1D,MAAO,CAAAoK,OAAO,EAAI,CAACH,QAAQ,EAAI,CAACkC,UAAU,CAC3C,CAAC,CACD,KAAM,CAAAG,wBAAwB,CAAIC,WAAmB,EAAc,CAClE,GAAI,CAACA,WAAW,EAAIA,WAAW,CAAC5N,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC9C,MAAO,MAAK,CACb,CAEA;AACA,GAAI,CAAA6N,cAAc,CAAGD,WAAW,CAEhC;AACAC,cAAc,CAAGA,cAAc,CAACR,OAAO,CAAC,2BAA2B,CAAE,EAAE,CAAC,CAExE;AACAQ,cAAc,CAAGA,cAAc,CAACR,OAAO,CAAC,+BAA+B,CAAE,EAAE,CAAC,CAE5E;AACAQ,cAAc,CAAGA,cAAc,CAACR,OAAO,CAAC,iCAAiC,CAAE,EAAE,CAAC,CAE9E;AACAQ,cAAc,CAAGA,cAAc,CAACR,OAAO,CAAC,cAAc,CAAE,EAAE,CAAC,CAE3D;AACAQ,cAAc,CAAGA,cAAc,CAACR,OAAO,CAAC,UAAU,CAAE,GAAG,CAAC,CAExD;AACA,GAAIQ,cAAc,CAAC7N,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjC,MAAO,MAAK,CACb,CAEA;AACA,KAAM,CAAA8N,OAAO,CAAG1N,QAAQ,CAACmF,aAAa,CAAC,KAAK,CAAC,CAC7CuI,OAAO,CAACtI,SAAS,CAAGqI,cAAc,CAElC;AACA,KAAM,CAAAE,WAAW,CAAGD,OAAO,CAACC,WAAW,EAAID,OAAO,CAACE,SAAS,CAE5D;AACA,GAAID,WAAW,GAAK,IAAI,EAAIA,WAAW,CAAC/N,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACtD,MAAO,MAAK,CACb,CAEA;AACA;AACA,KAAM,CAAAiO,YAAY,CAAGJ,cAAc,CAACK,WAAW,CAAC,CAAC,CACjD,KAAM,CAAAC,aAAa,CAAG,CACrB,iBAAiB,CACjB,aAAa,CACb,aAAa,CACb,SAAS,CACT,eAAe,CACf,eAAe,CACf,mBAAmB,CACnB,UAAU,CACV,cAAc,CACd,CAED,GAAIA,aAAa,CAACtC,IAAI,CAACuC,OAAO,EAAIH,YAAY,CAACpG,QAAQ,CAACuG,OAAO,CAAC,CAAC,EAAIL,WAAW,CAAC/N,IAAI,CAAC,CAAC,CAACqB,MAAM,EAAI,CAAC,CAAE,CACpG,MAAO,MAAK,CACb,CAEA,MAAO,KAAI,CACZ,CAAC,CAED,KAAM,CAAAgN,mBAAmB,CACvB,OAAOzQ,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,IAAK,QAAQ,EAAI+B,wBAAwB,CAAC/P,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,eACnGhU,KAAK,CAACoU,cAAc,CAACpO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,EAC7CF,KAAK,CAACC,OAAO,CAAC/N,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgO,OAAO,CAAC,EAAIhO,eAAe,CAACgO,OAAO,CAACC,IAAI,CACtEC,IAAS,EAAK,CAAAA,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEC,IAAI,GAAI,MAAO,CAAAD,IAAI,CAACC,IAAI,GAAK,QAAQ,EAAI4B,wBAAwB,CAAC7B,IAAI,CAACC,IAAI,CACjG,CAAE,CACF;AACD,KAAM,CAAAyB,UAAU,CAAG,CAAA5P,eAAe,SAAfA,eAAe,kBAAA/B,sBAAA,CAAf+B,eAAe,CAAE6P,UAAU,UAAA5R,sBAAA,iBAA3BA,sBAAA,CAA6BwF,MAAM,EAAG,CAAC,CAE1D;AACA,KAAM,CAAAiN,aAAa,CAClB,CAAA1Q,eAAe,SAAfA,eAAe,kBAAA9B,sBAAA,CAAf8B,eAAe,CAAE2N,QAAQ,UAAAzP,sBAAA,iBAAzBA,sBAAA,CAA2B0P,UAAU,CAAC,aAAa,CAAC,IACpD5N,eAAe,SAAfA,eAAe,kBAAA7B,sBAAA,CAAf6B,eAAe,CAAE2N,QAAQ,UAAAxP,sBAAA,iBAAzBA,sBAAA,CAA2ByP,UAAU,CAAC,MAAM,CAAC,EAC9C;AACA,KAAM,CAAA+C,kBAAkB,CAAGF,mBAAmB,EAAI,CAACC,aAAa,EAAI,CAACd,UAAU,CAE/E;AACA,KAAM,CAAAgB,aAAa,CAAGhB,UAAU,EAAI,CAACa,mBAAmB,EAAI,CAACC,aAAa,EAAI,CAAA1Q,eAAe,SAAfA,eAAe,kBAAA5B,sBAAA,CAAf4B,eAAe,CAAE6P,UAAU,UAAAzR,sBAAA,iBAA3BA,sBAAA,CAA6BqF,MAAM,IAAK,CAAC,CAEvH;AACA,KAAM,CAAAoN,eAAe,CAAGJ,mBAAmB,EAAIC,aAAa,CAC3D;AACA,KAAM,CAAAI,UAAU,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACxB;AACA,KAAM,CAAAC,iBAAiB,CAAG,CAAApR,eAAe,SAAfA,eAAe,kBAAA+Q,sBAAA,CAAf/Q,eAAe,CAAE6P,UAAU,UAAAkB,sBAAA,iBAA3BA,sBAAA,CAA6BtN,MAAM,IAAK,CAAC,EAClE,CAAAzD,eAAe,SAAfA,eAAe,kBAAAgR,sBAAA,CAAfhR,eAAe,CAAE6P,UAAU,UAAAmB,sBAAA,kBAAAC,sBAAA,CAA3BD,sBAAA,CAA8B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAAhCD,sBAAA,CAAkCI,YAAY,UAAAH,sBAAA,kBAAAC,sBAAA,CAA9CD,sBAAA,CAAgDI,MAAM,UAAAH,sBAAA,iBAAtDA,sBAAA,CAAwDI,iBAAiB,CAAC,CAAC,IAAK,UAAU,CAE3F;AACA,GAAIH,iBAAiB,CAAE,CACtB,MAAO,KAAK,CACb,CAEA;AACA,GAAI,CAACP,eAAe,CAAE,KAAAW,uBAAA,CACrB,MAAO,CAAAxR,eAAe,SAAfA,eAAe,kBAAAwR,uBAAA,CAAfxR,eAAe,CAAE6P,UAAU,UAAA2B,uBAAA,iBAA3BA,uBAAA,CAA6B/N,MAAM,IAAK,CAAC,CAAG,KAAK,CAAG,KAAK,CACjE,CAAC,IAAM,CACN,MAAO,KAAK,CACZ,CACF,CAAC,CAGF;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACC,KAAM,CAAAgO,cAAc,CAAGA,CAAA,GAAM,CAC7B,GAAI,CAAC7E,cAAc,CAAE,MAAO,KAAI,CAE9B,GAAII,gBAAgB,GAAK,MAAM,CAAE,CAC/B,mBACE9R,IAAA,CAACP,aAAa,EACZ+W,OAAO,CAAC,MAAM,CACdnT,KAAK,CAAEA,KAAK,CAACkF,MAAO,CACpBkO,QAAQ,CAAC,QAAQ,CACjBC,UAAU,CAAE9S,gBAAgB,CAAG,CAAE,CACjCkQ,EAAE,CAAE,CACR5S,eAAe,CAAE,aAAa,CAAE,+BAA+B,CAAE,CAChEA,eAAe,CAAEwD,aAAe;AAC3B,CAAC,CACDiS,YAAY,CAAE,QAAQ,CACtBvV,OAAO,CAAE,iBAAiB,CAC1B,yBAAyB,CAAE,CACzBS,KAAK,CAAE,gBAAgB,CACvB+E,MAAM,CAAE,gBAChB,CACI,CAAE,CACFgQ,UAAU,cAAE5W,IAAA,CAACb,MAAM,EAACoN,KAAK,CAAE,CAAEsK,OAAO,CAAE,MAAO,CAAE,CAAE,CAAE,CACnDC,UAAU,cAAE9W,IAAA,CAACb,MAAM,EAACoN,KAAK,CAAE,CAAEsK,OAAO,CAAE,MAAO,CAAE,CAAE,CAAE,CACpD,CAAC,CAEN,CAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,GAAI/E,gBAAgB,GAAK,aAAa,CAAE,CACtC,mBACU9R,IAAA,CAACX,GAAG,EAACyU,EAAE,CAAE,CAAC+C,OAAO,CAAE,MAAM,CACpCE,UAAU,CAAE,QAAQ,CACpBJ,YAAY,CAAE,QAAQ,CACtBK,GAAG,CAAE,KAAK,CAAC5V,OAAO,CAAC,KAAK,CAAE,CAAAmS,QAAA,CAGpBX,KAAK,CAACqE,IAAI,CAAC,CAAE1O,MAAM,CAAElF,KAAK,CAACkF,MAAO,CAAC,CAAC,CAAC+L,GAAG,CAAC,CAAC4C,CAAC,CAAEC,KAAK,gBACjDnX,IAAA,QAEEuM,KAAK,CAAE,CACK1K,KAAK,CAAE,MAAM,CACb+E,MAAM,CAAE,KAAK,CACb1F,eAAe,CAAEiW,KAAK,GAAK1S,WAAW,CAAG,CAAC,CAAGC,aAAa,CAAG,SAAS,CAAE;AACxEpD,YAAY,CAAE,OAC1B,CAAE,EANG6V,KAON,CACF,CAAC,CAES,CAAC,CAEpB,CAEA,GAAIrF,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACC9R,IAAA,CAACX,GAAG,EAACyU,EAAE,CAAE,CAACsD,UAAU,CAAC,KAAK,CAAE,CAAA7D,QAAA,cAC3BrT,KAAA,CAACX,UAAU,EACViX,OAAO,CAAC,OAAO,CACf1C,EAAE,CAAE,CAAE1S,OAAO,CAAE,KAAK,CAAEiB,KAAK,CAAEqC,aAAa,CAAE,CAAA6O,QAAA,EAC5C,OACM,CAAC3P,gBAAgB,CAAC,MAAI,CAACP,KAAK,CAACkF,MAAM,EAC9B,CAAC,CACT,CAAC,CAER,CAEA,GAAIuJ,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACC9R,IAAA,CAACX,GAAG,EAACyU,EAAE,CAAE,CAAC1S,OAAO,CAAEX,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,GAAI,CAAE,CAAA8S,QAAA,cACnDvT,IAAA,CAACT,UAAU,EAACiX,OAAO,CAAC,OAAO,CAAAjD,QAAA,cAC1BvT,IAAA,CAACV,cAAc,EACdkX,OAAO,CAAC,aAAa,CACrBa,KAAK,CAAE7N,QAAS,CAChBsK,EAAE,CAAE,CACHlN,MAAM,CAAE,KAAK,CACXtF,YAAY,CAAE,MAAM,CACpB6S,MAAM,CAAE1T,cAAc,CAAC,CAAC,CAAG,GAAG,CAAG,UAAU,CAC7C,0BAA0B,CAAE,CACJS,eAAe,CAAEwD,aAAe;AAClC,CAAE,CAAE,CAC3B,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CAED,KAAM,CAAA4S,aAAa,CAAGA,CAAA,GAAM,KAAAC,uBAAA,CAC3B,MAAO,CAAAzS,eAAe,SAAfA,eAAe,kBAAAyS,uBAAA,CAAfzS,eAAe,CAAE6P,UAAU,UAAA4C,uBAAA,iBAA3BA,uBAAA,CAA6BhP,MAAM,EAAG,CAAC,CAC3CzD,eAAe,CAAC6P,UAAU,CAACL,GAAG,CAAC,CAACkD,MAAW,CAAEL,KAAU,GAAK,CAC5D,KAAM,CAAAM,WAAW,CAAG,CACnBvW,eAAe,CAAEsW,MAAM,CAACE,gBAAgB,CAACC,qBAAqB,CAC9DtV,KAAK,CAAEmV,MAAM,CAACE,gBAAgB,CAACE,eAAe,CAC9ClW,MAAM,CAAE8V,MAAM,CAACE,gBAAgB,CAACG,iBAAiB,CACjDzW,OAAO,CAAE,oBAAoB,CAC7BgS,UAAU,CAAE,QAAQ,CACpBvR,KAAK,CAAE,MAAM,CACbS,QAAQ,CAAE,MAAM,CAChBwV,UAAU,CAAE,SAAS,CACrBxW,YAAY,CAAE,KAAK,CACnByW,aAAa,CAAE,MAAM,CACrBC,QAAQ,CAAE,aAAa,CACvBvW,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACVA,SAAS,CAAE,iBAAiB,CAAE;AAC9BP,eAAe,CAAEsW,MAAM,CAACE,gBAAgB,CAACC,qBAAqB,CAAE;AAChEM,OAAO,CAAE,GAAK;AACf,CACD,CAAC,CACD,KAAM,CAAAnH,WAAW,CAAGA,CAAA,GAAM,KAAAoH,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACzBpR,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE,CACjCoR,QAAQ,CAAEhB,MAAM,CAAC3G,EAAE,CACnB4H,YAAY,CAAEjB,MAAM,CAACrB,YAAY,CAACC,MAAM,CACxCzF,YAAY,CAAE7L,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE6L,YAAY,CAC3CC,QAAQ,CAAE9L,eAAe,SAAfA,eAAe,kBAAAoT,sBAAA,CAAfpT,eAAe,CAAE6L,YAAY,UAAAuH,sBAAA,iBAA7BA,sBAAA,CAA+BtH,QAAQ,CACjD8H,gBAAgB,CAAE5T,eAAe,SAAfA,eAAe,kBAAAqT,sBAAA,CAAfrT,eAAe,CAAE6L,YAAY,UAAAwH,sBAAA,iBAA7BA,sBAAA,CAA+BtH,EAClD,CAAC,CAAC,CAEF,GAAI2G,MAAM,CAACrB,YAAY,CAACC,MAAM,CAACC,iBAAiB,CAAC,CAAC,GAAK,OAAO,CAAE,CAC/D;AAAA,CACA,IAAM,IACNmB,MAAM,CAACrB,YAAY,CAACC,MAAM,CAACC,iBAAiB,CAAC,CAAC,GAAK,MAAM,EACzD,CAAAvR,eAAe,SAAfA,eAAe,kBAAAsT,sBAAA,CAAftT,eAAe,CAAE6L,YAAY,UAAAyH,sBAAA,iBAA7BA,sBAAA,CAA+BxH,QAAQ,IAAK,QAAQ,CACnD,CACDzJ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC,CACtDgI,UAAU,CAAC,CAAC,CACb,CAAC,IAAM,IACNoI,MAAM,CAACrB,YAAY,CAACC,MAAM,CAACC,iBAAiB,CAAC,CAAC,GAAK,MAAM,EACzD,CAAAvR,eAAe,SAAfA,eAAe,kBAAAuT,sBAAA,CAAfvT,eAAe,CAAE6L,YAAY,UAAA0H,sBAAA,iBAA7BA,sBAAA,CAA+BzH,QAAQ,IAAK,QAAQ,GACnD4G,MAAM,CAAC3G,EAAE,IAAK/L,eAAe,SAAfA,eAAe,kBAAAwT,uBAAA,CAAfxT,eAAe,CAAE6L,YAAY,UAAA2H,uBAAA,iBAA7BA,uBAAA,CAA+BK,QAAQ,GACrDnB,MAAM,CAAC3G,EAAE,IAAK/L,eAAe,SAAfA,eAAe,kBAAAyT,uBAAA,CAAfzT,eAAe,CAAE6L,YAAY,UAAA4H,uBAAA,iBAA7BA,uBAAA,CAA+B1H,EAAE,EAAC,CAChD,KAAA+H,uBAAA,CAAAC,uBAAA,CACD1R,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAE,CACxFoR,QAAQ,CAAEhB,MAAM,CAAC3G,EAAE,CACnB6H,gBAAgB,CAAE5T,eAAe,SAAfA,eAAe,kBAAA8T,uBAAA,CAAf9T,eAAe,CAAE6L,YAAY,UAAAiI,uBAAA,iBAA7BA,uBAAA,CAA+BD,QAAQ,CACzDG,UAAU,CAAEhU,eAAe,SAAfA,eAAe,kBAAA+T,uBAAA,CAAf/T,eAAe,CAAE6L,YAAY,UAAAkI,uBAAA,iBAA7BA,uBAAA,CAA+BhI,EAAE,CAC7CF,YAAY,CAAE7L,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE6L,YAAY,CAC3CzC,SAAS,CAAEtK,gBAAgB,CAC3BkB,eAAe,CAAEA,eAAe,CAChCkC,KAAK,CAAElC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAC7B8C,mBAAmB,CAAEhF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmC,mBACvC,CAAC,CAAC,CACF,KAAM,CAAArB,OAAO,CAAGmB,iBAAiB,CAACjC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAAClC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmC,mBAAmB,CAAC,CACvF,GAAIrB,OAAO,CAAE,CACnBuB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAExB,OAAO,CAAC,CACrDA,OAAO,CAACmT,KAAK,CAAC,CAAC,CACf3J,UAAU,CAAC,CAAC,CACN,CAAC,IAAM,CACNjI,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAE,CACtEJ,KAAK,CAAElC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,KAAK,CAC7B8C,mBAAmB,CAAEhF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmC,mBAAmB,CACzDiH,SAAS,CAAEtK,gBACZ,CAAC,CAAC,CACI,CACF,CAAC,IAAM,IAAI4T,MAAM,CAACrB,YAAY,CAACC,MAAM,GAAK,UAAU,CAAE,CAC3DlG,cAAc,CAAC,CAAC,CACX,CAAC,IAAM,IAAIsH,MAAM,CAACrB,YAAY,CAACC,MAAM,GAAK,SAAS,EAAIoB,MAAM,CAACrB,YAAY,CAACC,MAAM,GAAK,SAAS,CAAE,KAAA4C,OAAA,CACtG;AACA7R,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAE1C;AACAvD,mBAAmB,CAAC,CAAC,CAAC,CACtBU,cAAc,CAAC,CAAC,CAAC,CAEjB;AACA,GAAI,CAAAyU,OAAA,CAAA3V,KAAK,CAAC,CAAC,CAAC,UAAA2V,OAAA,WAARA,OAAA,CAAUC,SAAS,EAAI5V,KAAK,CAAC,CAAC,CAAC,CAAC4V,SAAS,CAAC/R,IAAI,CAAC,CAAC,GAAKnB,MAAM,CAACmT,QAAQ,CAACC,IAAI,CAACjS,IAAI,CAAC,CAAC,CAAE,CACrF;AACAnB,MAAM,CAACmT,QAAQ,CAACC,IAAI,CAAG9V,KAAK,CAAC,CAAC,CAAC,CAAC4V,SAAS,CAC1C,CAAC,IAAM,KAAAG,QAAA,CACN;AACA,IAAAA,QAAA,CAAI/V,KAAK,CAAC,CAAC,CAAC,UAAA+V,QAAA,WAARA,QAAA,CAAUpS,KAAK,CAAE,KAAAqS,QAAA,CACpBlS,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAEhE;AACA,KAAM,CAAAkS,YAAY,CAAGvS,iBAAiB,CAAC1D,KAAK,CAAC,CAAC,CAAC,CAAC2D,KAAK,CAAE,EAAAqS,QAAA,CAAAhW,KAAK,CAAC,CAAC,CAAC,UAAAgW,QAAA,iBAARA,QAAA,CAAUpS,mBAAmB,GAAI,EAAE,CAAC,CAC3F,GAAIqS,YAAY,CAAE,CACjB,GAAI,CACHA,YAAY,CAAC1J,cAAc,CAAC,CAC3B9G,QAAQ,CAAE,QAAQ,CAClB+G,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SACT,CAAC,CAAC,CACF3I,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CACjD,CAAE,MAAOU,KAAK,CAAE,CACfX,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAC/DrB,MAAM,CAAC8C,QAAQ,CAAC,CAAE1D,GAAG,CAAE,CAAC,CAAE2D,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChD,CACD,CAAC,IAAM,CACN3B,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAC/DrB,MAAM,CAAC8C,QAAQ,CAAC,CAAE1D,GAAG,CAAE,CAAC,CAAE2D,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChD,CACD,CAAC,IAAM,CACN;AACA3B,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAC3DrB,MAAM,CAAC8C,QAAQ,CAAC,CAAE1D,GAAG,CAAE,CAAC,CAAE2D,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChD,CACD,CACK,CAAC,IAAM,IAAI0O,MAAM,CAACrB,YAAY,CAACC,MAAM,GAAK,UAAU,EAAIoB,MAAM,CAACrB,YAAY,CAACoD,WAAW,GAAK,SAAS,CAAE,CAC5GxT,MAAM,CAACyT,IAAI,CAAChC,MAAM,CAACrB,YAAY,CAACsD,SAAS,CAAE,QAAQ,CAAC,CAC/C,CAAC,IAAM,IAAIjC,MAAM,CAACrB,YAAY,CAACC,MAAM,GAAK,UAAU,EAAIoB,MAAM,CAACrB,YAAY,CAACoD,WAAW,GAAK,UAAU,CAAE,CAC7GxT,MAAM,CAACmT,QAAQ,CAACC,IAAI,CAAG3B,MAAM,CAACrB,YAAY,CAACsD,SAAS,CAC/C,CAAC,IAAM,CACZ;AAAA,CAEF,CAAC,CACI,mBACJzZ,IAAA,CAACb,MAAM,EAENqX,OAAO,CAAC,WAAW,CACnB1C,EAAE,CAAE2D,WAAY,CAChBiC,OAAO,CAAE5I,WAAY,CAAAyC,QAAA,CAEbiE,MAAM,CAACmC,UAAU,EALpBxC,KAMQ,CAAC,CAEb,CAAC,CAAC,CACL,IAAI,CACR,CAAC,CAEA,KAAM,CAAAyC,cAAc,cAClB1Z,KAAA,CAAAE,SAAA,EAAAmT,QAAA,eACEvT,IAAA,QAAKuM,KAAK,CAAE,CAAEoK,YAAY,CAAE,KAAK,CAAEE,OAAO,CAAE,MAAO,CAAE,CAAAtD,QAAA,CAClDxB,WAAW,eACV/R,IAAA,CAACN,UAAU,EACToU,EAAE,CAAE,CACF2C,QAAQ,CAAE,UAAU,CACpBhV,SAAS,CAAE,iCAAiC,CAC5CoY,UAAU,CAAE,iBAAiB,CAC7BnY,MAAM,CAAE,gBAAgB,CACxBgB,MAAM,CAAE,KAAK,CACbpB,YAAY,CAAE,MAAM,CACpBF,OAAO,CAAE,gBAAgB,CACzB0Y,KAAK,CAAE,OAAO,CACd3U,GAAG,CAAE,OAAO,CACZoB,KAAK,CAAE,OAAO,CACrB4N,MAAM,CAAE3T,WAAW,SAAXA,WAAW,WAAXA,WAAW,CAAEmB,UAAU,EAAI,CAAAnB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmB,UAAU,IAAK,KAAK,CAAG,IAAIoY,QAAQ,CAACvZ,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmB,UAAU,CAAC,CAAG,CAAC,IAAI,CAAG,KACvH,CAAE,CAAA4R,QAAA,cAGGvT,IAAA,CAACL,SAAS,EAACmU,EAAE,CAAE,CAAEkG,IAAI,CAAE,GAAG,CAAE3X,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACrC,CACb,CACE,CAAC,cACTrC,IAAA,CAACF,gBAAgB,EAEhBma,GAAG,CAAE5V,YAAa,CAClBkI,KAAK,CAAE,CAAE0H,SAAS,CAAE,OAAQ,CAAE,CAC9BlJ,OAAO,CAAE,CACRmP,eAAe,CAAE,CAAC/V,cAAc,CAChCgW,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAAjH,QAAA,cAEHvT,IAAA,QAAKia,GAAG,CAAEnW,UAAW,CACrByI,KAAK,CAAE,CACN;AACQQ,QAAQ,CAAE,QAAQ,CAClBzL,YAAY,CAAE,KAAK,CACnBF,OAAO,CAAEwU,UAAU,CAAC,CAAC,CACrBa,QAAQ,CAAE,UAAU,CACpB/T,MAAM,CAAE,KAChB;AAED,CAAE,CAAA6Q,QAAA,cACKrT,KAAA,CAACb,GAAG,EAAAkU,QAAA,EACP,CAAC9S,cAAc,CAAC,CAAC,eACVT,IAAA,CAACX,GAAG,EACFwX,OAAO,CAAC,MAAM,CACd4D,aAAa,CAAC,QAAQ,CACtB1D,UAAU,CAAErW,WAAW,CAAC,CAAC,CAAG,YAAY,CAAG,QAAS,CACpDoT,EAAE,CAAE,CACFjS,KAAK,CAAEnB,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CAChDU,OAAO,CAAEV,WAAW,CAAC,CAAC,CAAG,GAAG,CAAG4D,SACvB,CAAE,CAAAiP,QAAA,CAEDrB,aAAa,CAAC,CAAC,CACb,CACN,CACA,CAAApN,eAAe,SAAfA,eAAe,kBAAA3B,uBAAA,CAAf2B,eAAe,CAAE6P,UAAU,UAAAxR,uBAAA,iBAA3BA,uBAAA,CAA6BoF,MAAM,EAAG,CAAC,eACtCvI,IAAA,CAACX,GAAG,EACF4a,GAAG,CAAElW,kBAAmB,CACxB8S,OAAO,CAAC,MAAM,CACd/C,EAAE,CAAE,CACF6C,YAAY,CAAE,QAAQ,CAChC;AACUK,GAAG,CAAE,KAAK,CACV9V,eAAe,EAAAkC,uBAAA,CAAE0B,eAAe,CAAC6P,UAAU,CAAC,CAAC,CAAC,UAAAvR,uBAAA,iBAA7BA,uBAAA,CAA+BjC,eAAe,CAC/DU,KAAK,CAAEpB,cAAc,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CACzCsW,UAAU,CAAE,QACd,CAAE,CAAAxD,QAAA,CAED+D,aAAa,CAAC,CAAC,CACb,CACN,EACE,CAAC,CACH,CAAC,EAvDL,aAAanT,cAAc,EAwDZ,CAAC,CAClBuN,cAAc,EAAIrO,KAAK,CAACkF,MAAM,CAAC,CAAC,EAAI/D,gBAAgB,GAAK,SAAS,eAAIxE,IAAA,CAACX,GAAG,EAAAkU,QAAA,CAAEgD,cAAc,CAAC,CAAC,CAAM,CAAC,CAAE,GAAG,EACzG,CACJ,CAED;AACC,KAAM,CAAAmE,YAAY,CAAG,CACnBjE,QAAQ,CAAE,OAAgB,CAC1BtR,GAAG,CAAE,CAAC,CACNnD,IAAI,CAAE,CAAC,CACPH,KAAK,CAAE,OAAO,CACd+E,MAAM,CAAE,OAAO,CACf1F,eAAe,CAAE,aAAa,CAC9BsL,aAAa,CAAE,MAAM,CACrB9J,MAAM,CAAE,IACX,CAAC,CAEA,KAAM,CAAAiY,kBAAkB,CAAGA,CAAA,GAAM,CACjC,GAAI,CAAC5V,aAAa,CAAE,MAAO,KAAI,CAE/B,KAAM,CAAApB,IAAI,CAAGoB,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAClD,KAAM,CAAAI,cAAc,CAAGF,MAAM,CAACG,WAAW,CACzC,KAAM,CAAAJ,aAAa,CAAGC,MAAM,CAACC,UAAU,CAErC,KAAM,CAAA4U,QAAQ,CAAG,CACfzV,GAAG,CAAE,CACHsR,QAAQ,CAAE,OAAgB,CAC1BtR,GAAG,CAAE,CAAC,CACNnD,IAAI,CAAE,CAAC,CACPH,KAAK,CAAE,MAAM,CACb+E,MAAM,CAAE,GAAGjD,IAAI,CAACwB,GAAG,IAAI,CACvBjE,eAAe,CAAE,oBAAoB,CACrCsL,aAAa,CAAE,MACjB,CAAC,CACDtK,MAAM,CAAE,CACNuU,QAAQ,CAAE,OAAgB,CAC1BtR,GAAG,CAAE,GAAGxB,IAAI,CAACzB,MAAM,IAAI,CACvBF,IAAI,CAAE,CAAC,CACPH,KAAK,CAAE,MAAM,CACb+E,MAAM,CAAE,GAAGX,cAAc,CAAGtC,IAAI,CAACzB,MAAM,IAAI,CAC3ChB,eAAe,CAAE,oBAAoB,CACrCsL,aAAa,CAAE,MACjB,CAAC,CACDxK,IAAI,CAAE,CACJyU,QAAQ,CAAE,OAAgB,CAC1BtR,GAAG,CAAE,GAAGxB,IAAI,CAACwB,GAAG,IAAI,CACpBnD,IAAI,CAAE,CAAC,CACPH,KAAK,CAAE,GAAG8B,IAAI,CAAC3B,IAAI,IAAI,CACvB4E,MAAM,CAAE,GAAGjD,IAAI,CAACiD,MAAM,IAAI,CAC1B1F,eAAe,CAAE,oBAAoB,CACrCsL,aAAa,CAAE,MACjB,CAAC,CACDjG,KAAK,CAAE,CACLkQ,QAAQ,CAAE,OAAgB,CAC1BtR,GAAG,CAAE,GAAGxB,IAAI,CAACwB,GAAG,IAAI,CACpBnD,IAAI,CAAE,GAAG2B,IAAI,CAAC4C,KAAK,IAAI,CACvB1E,KAAK,CAAE,GAAGiE,aAAa,CAAGnC,IAAI,CAAC4C,KAAK,IAAI,CACxCK,MAAM,CAAE,GAAGjD,IAAI,CAACiD,MAAM,IAAI,CAC1B1F,eAAe,CAAE,oBAAoB,CACrCsL,aAAa,CAAE,MACjB,CACJ,CAAC,CAED,MAAO,CAAAoO,QAAQ,CAChB,CAAC,CACA,KAAM,CAAAC,iBAAiB,CAAG9V,aAAa,CACnC,CACE0R,QAAQ,CAAE,OAAgB,CAC1BtR,GAAG,CAAE,GAAGJ,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAACV,GAAG,IAAI,CACrDnD,IAAI,CAAE,GAAG+C,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAAC7D,IAAI,IAAI,CACvDH,KAAK,CAAE,GAAGkD,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAAChE,KAAK,IAAI,CACzD+E,MAAM,CAAE,GAAG7B,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAACe,MAAM,IAAI,CAC/D;AACItF,YAAY,CAAE,KAAK,CACnBkL,aAAa,CAAEH,gBAAgB,CAAG,MAAM,CAAG,MAAM,CACjD3J,MAAM,CAAE,CACV,CAAC,CACD,CAAC,CAAC,CAEN,mBACExC,KAAA,CAAAE,SAAA,EAAAmT,QAAA,EACG,CAAAzO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgI,OAAO,GAAI/H,aAAa,EAAIO,gBAAgB,EAAId,gBAAgB,GAAK,MAAM,eAC3FtE,KAAA,CAACb,GAAG,EAACyU,EAAE,CAAE4G,YAAa,CAAAnH,QAAA,EACnBuH,MAAM,CAACC,OAAO,CAACJ,kBAAkB,CAAC,CAAC,EAAI,CAAC,CAAC,CAAC,CAACrG,GAAG,CAAC0G,KAAA,MAAC,CAACC,GAAG,CAAE1O,KAAK,CAAC,CAAAyO,KAAA,oBACjEhb,IAAA,CAACX,GAAG,EAEHyU,EAAE,CAAEvH,KAAM,EADL0O,GAEL,CAAC,EACG,CAAC,cAEFjb,IAAA,CAACX,GAAG,EAACyU,EAAE,CAAE+G,iBAAkB,CAAE,CAAC,EAC3B,CACN,CACA9V,aAAa,EAAIO,gBAAgB,eAEhCtF,IAAA,CAACK,kBAAkB,EACjBmZ,IAAI,MACJ0B,KAAK,CAAEtB,cAAe,CACtBxO,SAAS,CAAEhG,gBAAiB,CAC5BhD,KAAK,MACL+Y,WAAW,CAAE,CACXC,QAAQ,CAAErW,aAAa,CACvBsW,SAAS,CAAE,CACT,CACEC,IAAI,CAAE,iBAAiB,CACvBvQ,OAAO,CAAE,CACPwQ,QAAQ,CAAExV,MAAM,CAChByV,OAAO,CAAE,IAAI,CACbpa,OAAO,CAAE,EACX,CACF,CAAC,CACD,CACEka,IAAI,CAAE,eAAe,CACrBvQ,OAAO,CAAE,CACP;AACA0Q,QAAQ,CAAE,KAAK,CACf;AACAC,YAAY,CAAEC,KAAA,MAAC,CAAEC,CAAC,CAAEC,CAA4B,CAAC,CAAAF,KAAA,OAAM,CACrDC,CAAC,CAAEnV,IAAI,CAACwI,KAAK,CAAC2M,CAAC,CAAC,CAChBC,CAAC,CAAEpV,IAAI,CAACwI,KAAK,CAAC4M,CAAC,CACjB,CAAC,EACH,CACF,CAAC,CAEL,CAAE,CACFrb,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAc,CAAC,CAAE,CACjCC,WAAW,CAAEA,WAAW,CAAC,CAC9B;AAAA,CAAA6S,QAAA,cAEKvT,IAAA,CAACX,GAAG,EACFyU,EAAE,CAAE,CACF2C,QAAQ,CAAE,UAAU,CACpBtR,GAAG,CAAEJ,aAAa,CAAC+W,SAAS,CAC5B9Z,IAAI,CAAE+C,aAAa,CAACgX,UAAU,CAC9Bla,KAAK,CAAEkD,aAAa,CAACiX,WAAW,CAChCpV,MAAM,CAAE7B,aAAa,CAACkX,YACxB,CAAE,CACH,CAAC,CACgB,CACrB,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAArZ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}