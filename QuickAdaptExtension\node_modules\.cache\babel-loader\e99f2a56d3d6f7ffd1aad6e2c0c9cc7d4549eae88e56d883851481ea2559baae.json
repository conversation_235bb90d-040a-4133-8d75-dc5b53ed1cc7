{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\VideoSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Popover, IconButton } from '@mui/material';\nimport RemoveIcon from '@mui/icons-material/Remove';\nimport AddIcon from '@mui/icons-material/Add';\nimport { uploadfile, hyperlink, uploadicon, replaceimageicon, backgroundcoloricon, copyicon, deleteicon, sectionheight } from '../../../assets/icons/icons';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoSection = () => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [videoSrc, setVideoSrc] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [videoHeight, setVideoHeight] = useState(335);\n  const [showSection, setShowSection] = useState(true);\n  const containerStyle = {\n    width: '100%',\n    height: '100%',\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    alignItems: 'center',\n    padding: 0,\n    margin: 0,\n    overflow: 'hidden'\n  };\n  const videoContainerStyle = {\n    width: '100%',\n    height: `${videoHeight}px`,\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 0,\n    margin: 0,\n    overflow: 'hidden',\n    backgroundColor: '#f0f0f0'\n  };\n  const videoStyle = {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    margin: 0,\n    padding: 0,\n    borderRadius: '0'\n  };\n  const iconRowStyle = {\n    display: 'flex',\n    justifyContent: 'center',\n    gap: '16px'\n  };\n  const iconTextStyle = {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '8px',\n    width: '100%'\n  };\n  const handleVideoUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setVideoSrc(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const open = Boolean(anchorEl);\n  const id = open ? 'video-popover' : undefined;\n  const handleIncreaseHeight = () => {\n    setVideoHeight(prevHeight => prevHeight + 10);\n  };\n  const handleDecreaseHeight = () => {\n    setVideoHeight(prevHeight => prevHeight > 10 ? prevHeight - 10 : prevHeight);\n  };\n  const triggerVideoUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById('replace-video-upload')) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n  };\n\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setShowSection(false); // Hide the section by updating the state\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: showSection && /*#__PURE__*/_jsxDEV(Box, {\n      sx: containerStyle,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: videoContainerStyle,\n        onClick: handleClick,\n        children: videoSrc ? /*#__PURE__*/_jsxDEV(\"video\", {\n          src: videoSrc,\n          controls: true,\n          style: videoStyle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: iconTextStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: uploadfile\n              },\n              style: {\n                fontSize: '48px',\n                display: 'inline-block'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              align: \"center\",\n              children: translate(\"Upload Video\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            align: \"center\",\n            color: \"textSecondary\",\n            children: translate(\"Drag & Drop to upload video\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            align: \"center\",\n            color: \"textSecondary\",\n            sx: {\n              marginTop: '8px'\n            },\n            children: translate(\"Or\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: iconRowStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: hyperlink\n              },\n              style: {\n                color: 'black',\n                cursor: 'pointer',\n                fontSize: '32px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: () => {\n                var _document$getElementB2;\n                return (_document$getElementB2 = document.getElementById('video-file-upload')) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n              },\n              dangerouslySetInnerHTML: {\n                __html: uploadicon\n              },\n              style: {\n                color: 'black',\n                cursor: 'pointer',\n                fontSize: '32px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"video-file-upload\",\n              style: {\n                display: 'none'\n              },\n              accept: \"video/*\",\n              onChange: handleVideoUpload\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popover, {\n        id: id,\n        open: open,\n        anchorEl: anchorEl,\n        onClose: handleClose,\n        anchorOrigin: {\n          vertical: 'top',\n          horizontal: 'center'\n        },\n        transformOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        PaperProps: {\n          style: {\n            height: '44px',\n            width: '500px',\n            marginLeft: 'auto',\n            marginRight: 'auto',\n            marginTop: '-10px'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            height: '100%',\n            paddingLeft: '10px',\n            paddingRight: '10px',\n            fontSize: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: replaceimageicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: \"12px\",\n              onClick: triggerVideoUpload,\n              sx: {\n                cursor: 'pointer'\n              },\n              children: translate(\"Replace Video\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"replace-video-upload\",\n              style: {\n                display: 'none'\n              },\n              accept: \"video/*\",\n              onChange: handleVideoUpload\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: sectionheight\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDecreaseHeight,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: \"12px\",\n              children: videoHeight\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleIncreaseHeight,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: backgroundcoloricon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px',\n              cursor: 'pointer'\n            },\n            onClick: handleDeleteSection,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(VideoSection, \"A5ik6pD1OV2JlHOfUKzzv4AifMI=\", false, function () {\n  return [useTranslation];\n});\n_c = VideoSection;\nexport default VideoSection;\nvar _c;\n$RefreshReg$(_c, \"VideoSection\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "IconButton", "RemoveIcon", "AddIcon", "uploadfile", "hyperlink", "uploadicon", "replaceimageicon", "backgroundcoloricon", "copyicon", "deleteicon", "sectionheight", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoSection", "_s", "t", "translate", "videoSrc", "setVideoSrc", "anchorEl", "setAnchorEl", "videoHeight", "setVideoHeight", "showSection", "setShowSection", "containerStyle", "width", "height", "display", "flexDirection", "justifyContent", "alignItems", "padding", "margin", "overflow", "videoContainerStyle", "backgroundColor", "videoStyle", "objectFit", "borderRadius", "iconRowStyle", "gap", "iconTextStyle", "handleVideoUpload", "event", "_event$target$files", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleClick", "currentTarget", "handleClose", "open", "Boolean", "id", "undefined", "handleIncreaseHeight", "prevHeight", "handleDecreaseHeight", "triggerVideoUpload", "_document$getElementB", "document", "getElementById", "click", "handleDeleteSection", "children", "sx", "onClick", "src", "controls", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "dangerouslySetInnerHTML", "__html", "fontSize", "variant", "align", "color", "marginTop", "cursor", "_document$getElementB2", "type", "accept", "onChange", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "marginLeft", "marginRight", "paddingLeft", "paddingRight", "size", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/VideoSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Box, Typography, Popover, IconButton } from '@mui/material';\r\nimport RemoveIcon from '@mui/icons-material/Remove';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport { uploadfile, hyperlink, uploadicon, replaceimageicon, backgroundcoloricon, copyicon, deleteicon, sectionheight } from  '../../../assets/icons/icons';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst VideoSection: React.FC = () => {\r\n  const { t: translate } = useTranslation();\r\n  const [videoSrc, setVideoSrc] = useState<string | null>(null);\r\n  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n  const [videoHeight, setVideoHeight] = useState<number>(335);\r\n  const [showSection, setShowSection] = useState<boolean>(true);\r\n\r\n  const containerStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    height: '100%',\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    justifyContent: 'flex-start',\r\n    alignItems: 'center',\r\n    padding: 0,\r\n    margin: 0,\r\n    overflow: 'hidden',\r\n  };\r\n\r\n  const videoContainerStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    height: `${videoHeight}px`,\r\n    display: 'flex',\r\n    justifyContent: 'center',\r\n    alignItems: 'center',\r\n    padding: 0,\r\n    margin: 0,\r\n    overflow: 'hidden',\r\n    backgroundColor: '#f0f0f0',\r\n  };\r\n\r\n  const videoStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    height: '100%',\r\n    objectFit: 'cover',\r\n    margin: 0,\r\n    padding: 0,\r\n    borderRadius: '0',\r\n  };\r\n\r\n  const iconRowStyle: React.CSSProperties = {\r\n    display: 'flex',\r\n    justifyContent: 'center',\r\n    gap: '16px',\r\n  };\r\n\r\n  const iconTextStyle: React.CSSProperties = {\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    gap: '8px',\r\n    width: '100%',\r\n  };\r\n\r\n  const handleVideoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setVideoSrc(reader.result as string);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleClick = (event: React.MouseEvent<HTMLElement>) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setAnchorEl(null);\r\n  };\r\n\r\n  const open = Boolean(anchorEl);\r\n  const id = open ? 'video-popover' : undefined;\r\n\r\n  const handleIncreaseHeight = () => {\r\n    setVideoHeight((prevHeight) => prevHeight + 10);\r\n  };\r\n\r\n  const handleDecreaseHeight = () => {\r\n    setVideoHeight((prevHeight) => (prevHeight > 10 ? prevHeight - 10 : prevHeight));\r\n  };\r\n\r\n  const triggerVideoUpload = () => {\r\n    document.getElementById('replace-video-upload')?.click();\r\n  };\r\n\r\n  // Function to delete the section\r\n  const handleDeleteSection = () => {\r\n    setShowSection(false); // Hide the section by updating the state\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showSection && (\r\n        <Box sx={containerStyle}>\r\n          <Box sx={videoContainerStyle} onClick={handleClick}>\r\n            {videoSrc ? (\r\n              <video src={videoSrc} controls style={videoStyle} />\r\n            ) : (\r\n              <Box sx={{ textAlign: 'center', width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\r\n                <Box sx={iconTextStyle}>\r\n                  <span\r\n                    dangerouslySetInnerHTML={{ __html: uploadfile }}\r\n                    style={{ fontSize: '48px', display: 'inline-block' }}\r\n                  />\r\n                  <Typography variant=\"h6\" align=\"center\">\r\n                      {translate(\"Upload Video\")}\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Typography variant=\"body2\" align=\"center\" color=\"textSecondary\">\r\n                    {translate(\"Drag & Drop to upload video\")}\r\n                </Typography>\r\n\r\n                <Typography variant=\"body2\" align=\"center\" color=\"textSecondary\" sx={{ marginTop: '8px' }}>\r\n                    {translate(\"Or\")}\r\n                </Typography>\r\n\r\n                <Box sx={iconRowStyle}>\r\n                  <span\r\n                    dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n                    style={{ color: 'black', cursor: 'pointer', fontSize: '32px' }}\r\n                  />\r\n                  <span\r\n                    onClick={() => document.getElementById('video-file-upload')?.click()}\r\n                    dangerouslySetInnerHTML={{ __html: uploadicon }}\r\n                    style={{ color: 'black', cursor: 'pointer', fontSize: '32px' }}\r\n                  />\r\n                  <input\r\n                    type=\"file\"\r\n                    id=\"video-file-upload\"\r\n                    style={{ display: 'none' }}\r\n                    accept=\"video/*\"\r\n                    onChange={handleVideoUpload}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n          <Popover\r\n            id={id}\r\n            open={open}\r\n            anchorEl={anchorEl}\r\n            onClose={handleClose}\r\n            anchorOrigin={{\r\n              vertical: 'top',\r\n              horizontal: 'center',\r\n            }}\r\n            transformOrigin={{\r\n              vertical: 'bottom',\r\n              horizontal: 'center',\r\n            }}\r\n            PaperProps={{\r\n              style: {\r\n                height: '44px',\r\n                width: '500px',\r\n                marginLeft: 'auto',\r\n                marginRight: 'auto',\r\n                marginTop: '-10px',\r\n              },\r\n            }}\r\n          >\r\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '100%', paddingLeft: '10px', paddingRight: '10px', fontSize: '12px' }}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n                <Typography fontSize=\"12px\" onClick={triggerVideoUpload} sx={{ cursor: 'pointer' }}>\r\n                  {translate(\"Replace Video\")}\r\n                </Typography>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"replace-video-upload\"\r\n                  style={{ display: 'none' }}\r\n                  accept=\"video/*\"\r\n                  onChange={handleVideoUpload}\r\n                />\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n                <IconButton onClick={handleDecreaseHeight} size=\"small\">\r\n                  <RemoveIcon fontSize=\"small\" />\r\n                </IconButton>\r\n                <Typography fontSize=\"12px\">{videoHeight}</Typography>\r\n                <IconButton onClick={handleIncreaseHeight} size=\"small\">\r\n                  <AddIcon fontSize=\"small\" />\r\n                </IconButton>\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: backgroundcoloricon }} />\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: copyicon }} />\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px', cursor: 'pointer' }} onClick={handleDeleteSection}>\r\n                <span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n              </Box>\r\n            </Box>\r\n          </Popover>\r\n        </Box>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VideoSection;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AACpE,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,aAAa,QAAS,6BAA6B;AAC5J,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGR,cAAc,CAAC,CAAC;EACzC,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAS,GAAG,CAAC;EAC3D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAU,IAAI,CAAC;EAE7D,MAAMgC,cAAmC,GAAG;IAC1CC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,mBAAwC,GAAG;IAC/CT,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,GAAGN,WAAW,IAAI;IAC1BO,OAAO,EAAE,MAAM;IACfE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,QAAQ;IAClBE,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAA+B,GAAG;IACtCX,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdW,SAAS,EAAE,OAAO;IAClBL,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVO,YAAY,EAAE;EAChB,CAAC;EAED,MAAMC,YAAiC,GAAG;IACxCZ,OAAO,EAAE,MAAM;IACfE,cAAc,EAAE,QAAQ;IACxBW,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,aAAkC,GAAG;IACzCd,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBE,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBW,GAAG,EAAE,KAAK;IACVf,KAAK,EAAE;EACT,CAAC;EAED,MAAMiB,iBAAiB,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACxE,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACR,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBjC,WAAW,CAAC+B,MAAM,CAACG,MAAgB,CAAC;MACtC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMQ,WAAW,GAAIV,KAAoC,IAAK;IAC5DxB,WAAW,CAACwB,KAAK,CAACW,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBpC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMqC,IAAI,GAAGC,OAAO,CAACvC,QAAQ,CAAC;EAC9B,MAAMwC,EAAE,GAAGF,IAAI,GAAG,eAAe,GAAGG,SAAS;EAE7C,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCvC,cAAc,CAAEwC,UAAU,IAAKA,UAAU,GAAG,EAAE,CAAC;EACjD,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCzC,cAAc,CAAEwC,UAAU,IAAMA,UAAU,GAAG,EAAE,GAAGA,UAAU,GAAG,EAAE,GAAGA,UAAW,CAAC;EAClF,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC/B,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC,cAAAF,qBAAA,uBAA/CA,qBAAA,CAAiDG,KAAK,CAAC,CAAC;EAC1D,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC7C,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,oBACEd,OAAA,CAAAE,SAAA;IAAA0D,QAAA,EACG/C,WAAW,iBACVb,OAAA,CAAChB,GAAG;MAAC6E,EAAE,EAAE9C,cAAe;MAAA6C,QAAA,gBACtB5D,OAAA,CAAChB,GAAG;QAAC6E,EAAE,EAAEpC,mBAAoB;QAACqC,OAAO,EAAElB,WAAY;QAAAgB,QAAA,EAChDrD,QAAQ,gBACPP,OAAA;UAAO+D,GAAG,EAAExD,QAAS;UAACyD,QAAQ;UAACC,KAAK,EAAEtC;QAAW;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpDrE,OAAA,CAAChB,GAAG;UAAC6E,EAAE,EAAE;YAAES,SAAS,EAAE,QAAQ;YAAEtD,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAwC,QAAA,gBAClI5D,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE7B,aAAc;YAAA4B,QAAA,gBACrB5D,OAAA;cACEuE,uBAAuB,EAAE;gBAAEC,MAAM,EAAElF;cAAW,CAAE;cAChD2E,KAAK,EAAE;gBAAEQ,QAAQ,EAAE,MAAM;gBAAEvD,OAAO,EAAE;cAAe;YAAE;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACFrE,OAAA,CAACf,UAAU;cAACyF,OAAO,EAAC,IAAI;cAACC,KAAK,EAAC,QAAQ;cAAAf,QAAA,EAClCtD,SAAS,CAAC,cAAc;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENrE,OAAA,CAACf,UAAU;YAACyF,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,QAAQ;YAACC,KAAK,EAAC,eAAe;YAAAhB,QAAA,EAC3DtD,SAAS,CAAC,6BAA6B;UAAC;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAEbrE,OAAA,CAACf,UAAU;YAACyF,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,QAAQ;YAACC,KAAK,EAAC,eAAe;YAACf,EAAE,EAAE;cAAEgB,SAAS,EAAE;YAAM,CAAE;YAAAjB,QAAA,EACrFtD,SAAS,CAAC,IAAI;UAAC;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEbrE,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE/B,YAAa;YAAA8B,QAAA,gBACpB5D,OAAA;cACEuE,uBAAuB,EAAE;gBAAEC,MAAM,EAAEjF;cAAU,CAAE;cAC/C0E,KAAK,EAAE;gBAAEW,KAAK,EAAE,OAAO;gBAAEE,MAAM,EAAE,SAAS;gBAAEL,QAAQ,EAAE;cAAO;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACFrE,OAAA;cACE8D,OAAO,EAAEA,CAAA;gBAAA,IAAAiB,sBAAA;gBAAA,QAAAA,sBAAA,GAAMvB,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,cAAAsB,sBAAA,uBAA5CA,sBAAA,CAA8CrB,KAAK,CAAC,CAAC;cAAA,CAAC;cACrEa,uBAAuB,EAAE;gBAAEC,MAAM,EAAEhF;cAAW,CAAE;cAChDyE,KAAK,EAAE;gBAAEW,KAAK,EAAE,OAAO;gBAAEE,MAAM,EAAE,SAAS;gBAAEL,QAAQ,EAAE;cAAO;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACFrE,OAAA;cACEgF,IAAI,EAAC,MAAM;cACX/B,EAAE,EAAC,mBAAmB;cACtBgB,KAAK,EAAE;gBAAE/C,OAAO,EAAE;cAAO,CAAE;cAC3B+D,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAEjD;YAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNrE,OAAA,CAACd,OAAO;QACN+D,EAAE,EAAEA,EAAG;QACPF,IAAI,EAAEA,IAAK;QACXtC,QAAQ,EAAEA,QAAS;QACnB0E,OAAO,EAAErC,WAAY;QACrBsC,YAAY,EAAE;UACZC,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE;QACd,CAAE;QACFC,eAAe,EAAE;UACfF,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE;QACd,CAAE;QACFE,UAAU,EAAE;UACVvB,KAAK,EAAE;YACLhD,MAAM,EAAE,MAAM;YACdD,KAAK,EAAE,OAAO;YACdyE,UAAU,EAAE,MAAM;YAClBC,WAAW,EAAE,MAAM;YACnBb,SAAS,EAAE;UACb;QACF,CAAE;QAAAjB,QAAA,eAEF5D,OAAA,CAAChB,GAAG;UAAC6E,EAAE,EAAE;YAAE3C,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEJ,MAAM,EAAE,MAAM;YAAE0E,WAAW,EAAE,MAAM;YAAEC,YAAY,EAAE,MAAM;YAAEnB,QAAQ,EAAE;UAAO,CAAE;UAAAb,QAAA,gBAC/J5D,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE;cAAE3C,OAAO,EAAE,MAAM;cAAEG,UAAU,EAAE,QAAQ;cAAEU,GAAG,EAAE,KAAK;cAAE0C,QAAQ,EAAE;YAAO,CAAE;YAAAb,QAAA,gBAC/E5D,OAAA;cAAMuE,uBAAuB,EAAE;gBAAEC,MAAM,EAAE/E;cAAiB;YAAE;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DrE,OAAA,CAACf,UAAU;cAACwF,QAAQ,EAAC,MAAM;cAACX,OAAO,EAAER,kBAAmB;cAACO,EAAE,EAAE;gBAAEiB,MAAM,EAAE;cAAU,CAAE;cAAAlB,QAAA,EAChFtD,SAAS,CAAC,eAAe;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACbrE,OAAA;cACEgF,IAAI,EAAC,MAAM;cACX/B,EAAE,EAAC,sBAAsB;cACzBgB,KAAK,EAAE;gBAAE/C,OAAO,EAAE;cAAO,CAAE;cAC3B+D,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAEjD;YAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENrE,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE;cAAE3C,OAAO,EAAE,MAAM;cAAEG,UAAU,EAAE,QAAQ;cAAEU,GAAG,EAAE,KAAK;cAAE0C,QAAQ,EAAE;YAAO,CAAE;YAAAb,QAAA,gBAC/E5D,OAAA;cAAMuE,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3E;cAAc;YAAE;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DrE,OAAA,CAACb,UAAU;cAAC2E,OAAO,EAAET,oBAAqB;cAACwC,IAAI,EAAC,OAAO;cAAAjC,QAAA,eACrD5D,OAAA,CAACZ,UAAU;gBAACqF,QAAQ,EAAC;cAAO;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACbrE,OAAA,CAACf,UAAU;cAACwF,QAAQ,EAAC,MAAM;cAAAb,QAAA,EAAEjD;YAAW;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtDrE,OAAA,CAACb,UAAU;cAAC2E,OAAO,EAAEX,oBAAqB;cAAC0C,IAAI,EAAC,OAAO;cAAAjC,QAAA,eACrD5D,OAAA,CAACX,OAAO;gBAACoF,QAAQ,EAAC;cAAO;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENrE,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE;cAAE3C,OAAO,EAAE,MAAM;cAAEG,UAAU,EAAE,QAAQ;cAAEU,GAAG,EAAE,KAAK;cAAE0C,QAAQ,EAAE;YAAO,CAAE;YAAAb,QAAA,eAC/E5D,OAAA;cAAMuE,uBAAuB,EAAE;gBAAEC,MAAM,EAAE9E;cAAoB;YAAE;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eAENrE,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE;cAAE3C,OAAO,EAAE,MAAM;cAAEG,UAAU,EAAE,QAAQ;cAAEU,GAAG,EAAE,KAAK;cAAE0C,QAAQ,EAAE;YAAO,CAAE;YAAAb,QAAA,eAC/E5D,OAAA;cAAMuE,uBAAuB,EAAE;gBAAEC,MAAM,EAAE7E;cAAS;YAAE;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eAENrE,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE;cAAE3C,OAAO,EAAE,MAAM;cAAEG,UAAU,EAAE,QAAQ;cAAEU,GAAG,EAAE,KAAK;cAAE0C,QAAQ,EAAE,MAAM;cAAEK,MAAM,EAAE;YAAU,CAAE;YAAChB,OAAO,EAAEH,mBAAoB;YAAAC,QAAA,eAChI5D,OAAA;cAAMuE,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5E;cAAW;YAAE;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EACN,gBACD,CAAC;AAEP,CAAC;AAACjE,EAAA,CAhNID,YAAsB;EAAA,QACDL,cAAc;AAAA;AAAAgG,EAAA,GADnC3F,YAAsB;AAkN5B,eAAeA,YAAY;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}