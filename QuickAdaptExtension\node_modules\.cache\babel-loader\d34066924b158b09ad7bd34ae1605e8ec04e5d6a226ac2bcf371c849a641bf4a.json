{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useTranslation}from'react-i18next';import useDrawerStore from\"../../store/drawerStore\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ImageCarousel=_ref=>{let{images,selectedItem,activeItem,isMaximized}=_ref;const{t:translate}=useTranslation();const[currentIndex,setCurrentIndex]=useState(0);const{checklistGuideMetaData}=useDrawerStore(state=>state);useEffect(()=>{setCurrentIndex(0);},[activeItem]);// Function to change the image when clicking a progress dot\nconst goToImage=index=>{setCurrentIndex(index);};return/*#__PURE__*/_jsxs(\"div\",{style:{width:\"-webkit-fill-available\",height:\"244px\"},className:\"qadpt-imgsec\",children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',alignItems:'center',height:'100%',width:'100%'},children:selectedItem.supportingMedia.length>0&&/*#__PURE__*/_jsx(\"img\",{src:images[currentIndex]// Show selected image\n,alt:translate('Image {{index}}',{index:currentIndex,defaultValue:`Image ${currentIndex}`}),style:{width:\"100%\",height:\"100%\",borderRadius:\"10px\",transition:\"opacity 0.5s ease-in-out\",// Smooth transition effect\nobjectFit:isMaximized?\"contain\":\"initial\"}})}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:\"5px\"},children:images.map((_,index)=>{var _checklistGuideMetaDa,_checklistGuideMetaDa2;return/*#__PURE__*/_jsx(\"span\",{onClick:()=>goToImage(index)// Set index correctly\n,style:{height:\"6px\",width:\"6px\",margin:\"3px\",display:\"inline-block\",backgroundColor:currentIndex===index?(_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:(_checklistGuideMetaDa2=_checklistGuideMetaDa.canvas)===null||_checklistGuideMetaDa2===void 0?void 0:_checklistGuideMetaDa2.primaryColor:\"gray\",borderRadius:\"50%\",cursor:\"pointer\",transition:\"background-color 0.3s\"}},index);})})]});};export default ImageCarousel;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useTranslation", "useDrawerStore", "jsx", "_jsx", "jsxs", "_jsxs", "ImageCarousel", "_ref", "images", "selectedItem", "activeItem", "isMaximized", "t", "translate", "currentIndex", "setCurrentIndex", "checklistGuideMetaData", "state", "goToImage", "index", "style", "width", "height", "className", "children", "display", "justifyContent", "alignItems", "supportingMedia", "length", "src", "alt", "defaultValue", "borderRadius", "transition", "objectFit", "marginTop", "map", "_", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "onClick", "margin", "backgroundColor", "canvas", "primaryColor", "cursor"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/ImageCarousel.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\n\r\ninterface ImageCarouselProps {\r\n  images: string[];\r\n  selectedItem: any;\r\n  activeItem: any;\r\n  isMaximized: any;\r\n}\r\n\r\nconst ImageCarousel: React.FC<ImageCarouselProps> = ({ images, selectedItem,activeItem,isMaximized }) => {\r\n  const { t: translate } = useTranslation();\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const {\r\n    checklistGuideMetaData,\r\n\r\n  } = useDrawerStore((state: any) => state);\r\n  useEffect(() =>\r\n  {\r\n    setCurrentIndex(0);\r\n},[activeItem])\r\n  // Function to change the image when clicking a progress dot\r\n  const goToImage = (index: number) => {\r\n    setCurrentIndex(index);\r\n  };\r\n\r\n  return (\r\n    <div style={{\r\n      width: \"-webkit-fill-available\",\r\n      height: \"244px\" ,\r\n    }} className=\"qadpt-imgsec\">\r\n      {/* 🖼️ Display Current Image */}\r\n      <div style={{\r\n  display: 'flex',\r\n  justifyContent: 'center',\r\n  alignItems: 'center',\r\n  height: '100%',\r\n  width: '100%'\r\n}}\r\n>\r\n        {selectedItem.supportingMedia.length > 0 && (\r\n          <img\r\n            src={images[currentIndex]} // Show selected image\r\n            alt={translate('Image {{index}}', { index: currentIndex, defaultValue: `Image ${currentIndex}` })}\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"100%\",\r\n              borderRadius: \"10px\",\r\n              transition: \"opacity 0.5s ease-in-out\", // Smooth transition effect\r\n              objectFit:isMaximized ? \"contain\" :\"initial\",\r\n            }}\r\n          />\r\n        )}\r\n       \r\n      </div>\r\n\r\n      {/* 🔵 Progress Dots */}\r\n      <div style={{ marginTop: \"5px\" }}>\r\n        {images.map((_, index) => (\r\n          <span\r\n            key={index}\r\n            onClick={() => goToImage(index)} // Set index correctly\r\n            style={{\r\n              height: \"6px\",\r\n              width: \"6px\",\r\n              margin: \"3px\",\r\n              display: \"inline-block\",\r\n              backgroundColor: currentIndex === index ? checklistGuideMetaData[0]?.canvas?.primaryColor : \"gray\",\r\n              borderRadius: \"50%\",\r\n              cursor: \"pointer\",\r\n              transition: \"background-color 0.3s\",\r\n            }}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageCarousel;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASrD,KAAM,CAAAC,aAA2C,CAAGC,IAAA,EAAqD,IAApD,CAAEC,MAAM,CAAEC,YAAY,CAACC,UAAU,CAACC,WAAY,CAAC,CAAAJ,IAAA,CAClG,KAAM,CAAEK,CAAC,CAAEC,SAAU,CAAC,CAAGb,cAAc,CAAC,CAAC,CACzC,KAAM,CAACc,YAAY,CAAEC,eAAe,CAAC,CAAGhB,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CACJiB,sBAEF,CAAC,CAAGf,cAAc,CAAEgB,KAAU,EAAKA,KAAK,CAAC,CACzCnB,SAAS,CAAC,IACV,CACEiB,eAAe,CAAC,CAAC,CAAC,CACtB,CAAC,CAAC,CAACL,UAAU,CAAC,CAAC,CACb;AACA,KAAM,CAAAQ,SAAS,CAAIC,KAAa,EAAK,CACnCJ,eAAe,CAACI,KAAK,CAAC,CACxB,CAAC,CAED,mBACEd,KAAA,QAAKe,KAAK,CAAE,CACVC,KAAK,CAAE,wBAAwB,CAC/BC,MAAM,CAAE,OACV,CAAE,CAACC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAEzBrB,IAAA,QAAKiB,KAAK,CAAE,CAChBK,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBL,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,MACT,CAAE,CAAAG,QAAA,CAEOf,YAAY,CAACmB,eAAe,CAACC,MAAM,CAAG,CAAC,eACtC1B,IAAA,QACE2B,GAAG,CAAEtB,MAAM,CAACM,YAAY,CAAG;AAAA,CAC3BiB,GAAG,CAAElB,SAAS,CAAC,iBAAiB,CAAE,CAAEM,KAAK,CAAEL,YAAY,CAAEkB,YAAY,CAAE,SAASlB,YAAY,EAAG,CAAC,CAAE,CAClGM,KAAK,CAAE,CACLC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdW,YAAY,CAAE,MAAM,CACpBC,UAAU,CAAE,0BAA0B,CAAE;AACxCC,SAAS,CAACxB,WAAW,CAAG,SAAS,CAAE,SACrC,CAAE,CACH,CACF,CAEE,CAAC,cAGNR,IAAA,QAAKiB,KAAK,CAAE,CAAEgB,SAAS,CAAE,KAAM,CAAE,CAAAZ,QAAA,CAC9BhB,MAAM,CAAC6B,GAAG,CAAC,CAACC,CAAC,CAAEnB,KAAK,QAAAoB,qBAAA,CAAAC,sBAAA,oBACnBrC,IAAA,SAEEsC,OAAO,CAAEA,CAAA,GAAMvB,SAAS,CAACC,KAAK,CAAG;AAAA,CACjCC,KAAK,CAAE,CACLE,MAAM,CAAE,KAAK,CACbD,KAAK,CAAE,KAAK,CACZqB,MAAM,CAAE,KAAK,CACbjB,OAAO,CAAE,cAAc,CACvBkB,eAAe,CAAE7B,YAAY,GAAKK,KAAK,EAAAoB,qBAAA,CAAGvB,sBAAsB,CAAC,CAAC,CAAC,UAAAuB,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA2BK,MAAM,UAAAJ,sBAAA,iBAAjCA,sBAAA,CAAmCK,YAAY,CAAG,MAAM,CAClGZ,YAAY,CAAE,KAAK,CACnBa,MAAM,CAAE,SAAS,CACjBZ,UAAU,CAAE,uBACd,CAAE,EAXGf,KAYN,CAAC,EACH,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}