{"ast": null, "code": "import React,{useState}from'react';import{Dialog,DialogActions,DialogContent,DialogTitle,TextField,Button,IconButton}from'@mui/material';import CloseIcon from'@mui/icons-material/Close';import{CheckGuideNameExists,CopyGuide}from'../../../services/GuideListServices';import'./GuideMenuOptions.css';import{useSnackbar}from'./SnackbarContext';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CloneInteractionDialog=_ref=>{let{open,handleClose,initialName,onCloneSuccess,name}=_ref;const{t:translate}=useTranslation();const[announcementName,setAnnouncementName]=useState(`${initialName.Name}-Copy`);const[error,setError]=useState(null);const{openSnackbar}=useSnackbar();const handleNameChange=e=>{const newValue=e.target.value;setAnnouncementName(newValue);// Validate the length of the new value immediately while typing\nif(newValue.trim()===\"\"){setError(translate(\"Name is required.\"));}else if(newValue.trim().length<3){setError(translate(\"Guide Name must be at least 3 characters.\"));}else if(newValue.trim().length>50){setError(translate(\"Guide Name should not exceed 50 characters.\"));}else{setError(null);// Clear the error if the input is valid\n}};const handleClone=async()=>{// Final validation before cloning\nif(error){return;// Exit if there's an error\n}const guideName=announcementName;const accountId=initialName.AccountId;const organizationId=initialName.OrganizationId;const existsResponse=await CheckGuideNameExists(guideName,accountId,name);if(existsResponse===true){setError(translate(\"A guide with this name already exists.\"));}else{const guideId=initialName.GuideId;const guideStatus=initialName.GuideStatus;const guideType=initialName.GuideType;const copyResponse=await CopyGuide(guideId,organizationId,announcementName,accountId,guideType);if(copyResponse.Success){const prefix=`${announcementName} ${translate(guideType)} `;const message=`${prefix} ${translate(\"Copied Successfully. Please Check The Drafts\")}`;openSnackbar(message,\"success\");onCloneSuccess();handleClose();}else{setError(copyResponse.ErrorMessage);}}};const isCloneDisabled=!!error||announcementName.trim().length<3;const paraGraph=`${translate(\"Clone Guide\")} ${initialName.Name}`;return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:handleClose,className:\"qadpt-webclonepopup\",children:[/*#__PURE__*/_jsx(DialogTitle,{className:\"qadpt-title\",style:{whiteSpace:'nowrap',overflow:'hidden',textOverflow:'ellipsis',maxWidth:'100%'},children:paraGraph.length>15?paraGraph.slice(0,15)+'...':paraGraph}),/*#__PURE__*/_jsx(IconButton,{edge:\"end\",color:\"inherit\",onClick:handleClose,\"aria-label\":\"close\",className:\"qadpt-close\",children:/*#__PURE__*/_jsx(CloseIcon,{})}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,className:\"name-fld\",variant:\"outlined\",label:translate('Name'),value:announcementName,onChange:handleNameChange,slotProps:{htmlInput:{maxLength:50}},error:!!error,helperText:error,margin:\"dense\"})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleClone,disabled:isCloneDisabled,variant:\"contained\",children:translate('Clone')})})]});};export default CloneInteractionDialog;", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "TextField", "<PERSON><PERSON>", "IconButton", "CloseIcon", "CheckGuideNameExists", "CopyGuide", "useSnackbar", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "CloneInteractionDialog", "_ref", "open", "handleClose", "initialName", "onCloneSuccess", "name", "t", "translate", "announcementName", "setAnnouncementName", "Name", "error", "setError", "openSnackbar", "handleNameChange", "e", "newValue", "target", "value", "trim", "length", "handleClone", "guideName", "accountId", "AccountId", "organizationId", "OrganizationId", "existsResponse", "guideId", "GuideId", "guideStatus", "GuideStatus", "guideType", "GuideType", "copyResponse", "Success", "prefix", "message", "ErrorMessage", "isCloneDisabled", "paraGraph", "onClose", "className", "children", "style", "whiteSpace", "overflow", "textOverflow", "max<PERSON><PERSON><PERSON>", "slice", "edge", "color", "onClick", "fullWidth", "variant", "label", "onChange", "slotProps", "htmlInput", "max<PERSON><PERSON><PERSON>", "helperText", "margin", "disabled"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/CloneGuidePopUp.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON><PERSON>, <PERSON>alogA<PERSON>, <PERSON>alogContent, DialogTitle, TextField, Button, IconButton, Typography } from '@mui/material';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport { CheckGuideNameExists, CopyGuide } from '../../../services/GuideListServices';\r\nimport './GuideMenuOptions.css';\r\nimport { useSnackbar } from './SnackbarContext';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface Announcement {\r\n    AccountId: string;\r\n    Content: string;\r\n    CreatedBy: string;\r\n    CreatedDate: string;\r\n    Frequency: string;\r\n    GuideId: string;\r\n    GuideStatus: string;\r\n    GuideType: string;\r\n    Name: string;\r\n    OrganizationId: string;\r\n    Segment: string;\r\n    TargetUrl: string;\r\n    TemplateId: string;\r\n    UpdatedBy: string;\r\n    UpdatedDate: string;\r\n}\r\n\r\ninterface CloneInteractionDialogProps {\r\n    open: boolean;\r\n    handleClose: () => void;\r\n    initialName: Announcement; \r\n    onCloneSuccess: () => void;\r\n    name: string;\r\n}\r\n\r\nconst CloneInteractionDialog: React.FC<CloneInteractionDialogProps> = ({ open, handleClose, initialName, onCloneSuccess,name }) => {\r\n    const { t: translate } = useTranslation();\r\n    const [announcementName, setAnnouncementName] = useState(`${initialName.Name}-Copy`);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const { openSnackbar } = useSnackbar();\r\n\r\n    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newValue = e.target.value;\r\n        setAnnouncementName(newValue);\r\n\r\n        // Validate the length of the new value immediately while typing\r\n        if (newValue.trim() === \"\") {\r\n            setError(translate(\"Name is required.\"));\r\n        } else if (newValue.trim().length < 3) {\r\n            setError(translate(\"Guide Name must be at least 3 characters.\"));        \r\n        }\r\n        else if (newValue.trim().length > 50) {\r\n            setError(translate(\"Guide Name should not exceed 50 characters.\"));\r\n        }\r\n        else {\r\n            setError(null); // Clear the error if the input is valid\r\n        }\r\n    };\r\n\r\n    \r\n    const handleClone = async () => {\r\n        // Final validation before cloning\r\n        if (error) {\r\n            return; // Exit if there's an error\r\n        }\r\n        \r\n        const guideName = announcementName; \r\n        const accountId = initialName.AccountId;\r\n        const organizationId = initialName.OrganizationId;\r\n\r\n        const existsResponse = await CheckGuideNameExists(guideName, accountId, name);\r\n        \r\n        if (existsResponse === true) {\r\n            setError(translate(\"A guide with this name already exists.\"));\r\n        } else {\r\n            const guideId = initialName.GuideId; \r\n            const guideStatus = initialName.GuideStatus; \r\n            const guideType = initialName.GuideType;\r\n            const copyResponse = await CopyGuide(guideId, organizationId, announcementName, accountId, guideType);\r\n            if (copyResponse.Success) {\r\n                const prefix = `${announcementName} ${translate(guideType)} `;\r\n                const message = `${prefix} ${translate(\"Copied Successfully. Please Check The Drafts\")}`;\r\n\r\n                openSnackbar(message, \"success\");\r\n                onCloneSuccess();\r\n                handleClose();\r\n            } else {\r\n                setError(copyResponse.ErrorMessage);\r\n            }\r\n        }\r\n    };\r\n    const isCloneDisabled = !!error || announcementName.trim().length < 3;\r\n    const paraGraph = `${translate(\"Clone Guide\")} ${initialName.Name}`;\r\n    return (\r\n        <Dialog open={open} onClose={handleClose} className='qadpt-webclonepopup'>\r\n            <DialogTitle   className='qadpt-title' \r\n        style={{\r\n            whiteSpace: 'nowrap', \r\n            overflow: 'hidden', \r\n            textOverflow: 'ellipsis', \r\n            maxWidth: '100%'\r\n        }}\r\n    >\r\n        {paraGraph.length > 15 ? paraGraph.slice(0, 15) + '...' : paraGraph}</DialogTitle>\r\n            <IconButton \r\n                edge=\"end\" \r\n                color=\"inherit\" \r\n                onClick={handleClose} \r\n                aria-label=\"close\" \r\n                className='qadpt-close'\r\n            >\r\n                <CloseIcon />\r\n            </IconButton>\r\n          \r\n            <DialogContent>\r\n                <TextField\r\n                     fullWidth\r\n                     className=\"name-fld\"\r\n                   \r\n                    variant=\"outlined\"\r\n                    label={translate('Name')}\r\n                    value={announcementName}\r\n                    onChange={handleNameChange}\r\n                    slotProps={{\r\n                        htmlInput: {\r\n                            maxLength: 50, \r\n                        },\r\n                    }}\r\n                    error={!!error}\r\n                    helperText={error}\r\n                    margin=\"dense\"\r\n                />\r\n            </DialogContent>\r\n            <DialogActions>\r\n                <Button onClick={handleClone} disabled={isCloneDisabled} variant=\"contained\" >\r\n                    {translate('Clone')}\r\n                </Button>\r\n            </DialogActions>\r\n        </Dialog>\r\n    );\r\n};\r\n\r\nexport default CloneInteractionDialog;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,aAAa,CAAEC,aAAa,CAAEC,WAAW,CAAEC,SAAS,CAAEC,MAAM,CAAEC,UAAU,KAAoB,eAAe,CAC5H,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,OAASC,oBAAoB,CAAEC,SAAS,KAAQ,qCAAqC,CACrF,MAAO,wBAAwB,CAC/B,OAASC,WAAW,KAAQ,mBAAmB,CAC/C,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA4B/C,KAAM,CAAAC,sBAA6D,CAAGC,IAAA,EAA6D,IAA5D,CAAEC,IAAI,CAAEC,WAAW,CAAEC,WAAW,CAAEC,cAAc,CAACC,IAAK,CAAC,CAAAL,IAAA,CAC1H,KAAM,CAAEM,CAAC,CAAEC,SAAU,CAAC,CAAGb,cAAc,CAAC,CAAC,CACzC,KAAM,CAACc,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3B,QAAQ,CAAC,GAAGqB,WAAW,CAACO,IAAI,OAAO,CAAC,CACpF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAG9B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAE+B,YAAa,CAAC,CAAGpB,WAAW,CAAC,CAAC,CAEtC,KAAM,CAAAqB,gBAAgB,CAAIC,CAAsC,EAAK,CACjE,KAAM,CAAAC,QAAQ,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAC/BT,mBAAmB,CAACO,QAAQ,CAAC,CAE7B;AACA,GAAIA,QAAQ,CAACG,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACxBP,QAAQ,CAACL,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAC5C,CAAC,IAAM,IAAIS,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,MAAM,CAAG,CAAC,CAAE,CACnCR,QAAQ,CAACL,SAAS,CAAC,2CAA2C,CAAC,CAAC,CACpE,CAAC,IACI,IAAIS,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,MAAM,CAAG,EAAE,CAAE,CAClCR,QAAQ,CAACL,SAAS,CAAC,6CAA6C,CAAC,CAAC,CACtE,CAAC,IACI,CACDK,QAAQ,CAAC,IAAI,CAAC,CAAE;AACpB,CACJ,CAAC,CAGD,KAAM,CAAAS,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B;AACA,GAAIV,KAAK,CAAE,CACP,OAAQ;AACZ,CAEA,KAAM,CAAAW,SAAS,CAAGd,gBAAgB,CAClC,KAAM,CAAAe,SAAS,CAAGpB,WAAW,CAACqB,SAAS,CACvC,KAAM,CAAAC,cAAc,CAAGtB,WAAW,CAACuB,cAAc,CAEjD,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAApC,oBAAoB,CAAC+B,SAAS,CAAEC,SAAS,CAAElB,IAAI,CAAC,CAE7E,GAAIsB,cAAc,GAAK,IAAI,CAAE,CACzBf,QAAQ,CAACL,SAAS,CAAC,wCAAwC,CAAC,CAAC,CACjE,CAAC,IAAM,CACH,KAAM,CAAAqB,OAAO,CAAGzB,WAAW,CAAC0B,OAAO,CACnC,KAAM,CAAAC,WAAW,CAAG3B,WAAW,CAAC4B,WAAW,CAC3C,KAAM,CAAAC,SAAS,CAAG7B,WAAW,CAAC8B,SAAS,CACvC,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAA1C,SAAS,CAACoC,OAAO,CAAEH,cAAc,CAAEjB,gBAAgB,CAAEe,SAAS,CAAES,SAAS,CAAC,CACrG,GAAIE,YAAY,CAACC,OAAO,CAAE,CACtB,KAAM,CAAAC,MAAM,CAAG,GAAG5B,gBAAgB,IAAID,SAAS,CAACyB,SAAS,CAAC,GAAG,CAC7D,KAAM,CAAAK,OAAO,CAAG,GAAGD,MAAM,IAAI7B,SAAS,CAAC,8CAA8C,CAAC,EAAE,CAExFM,YAAY,CAACwB,OAAO,CAAE,SAAS,CAAC,CAChCjC,cAAc,CAAC,CAAC,CAChBF,WAAW,CAAC,CAAC,CACjB,CAAC,IAAM,CACHU,QAAQ,CAACsB,YAAY,CAACI,YAAY,CAAC,CACvC,CACJ,CACJ,CAAC,CACD,KAAM,CAAAC,eAAe,CAAG,CAAC,CAAC5B,KAAK,EAAIH,gBAAgB,CAACW,IAAI,CAAC,CAAC,CAACC,MAAM,CAAG,CAAC,CACrE,KAAM,CAAAoB,SAAS,CAAG,GAAGjC,SAAS,CAAC,aAAa,CAAC,IAAIJ,WAAW,CAACO,IAAI,EAAE,CACnE,mBACIZ,KAAA,CAACf,MAAM,EAACkB,IAAI,CAAEA,IAAK,CAACwC,OAAO,CAAEvC,WAAY,CAACwC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACrE/C,IAAA,CAACV,WAAW,EAAGwD,SAAS,CAAC,aAAa,CAC1CE,KAAK,CAAE,CACHC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,UAAU,CACxBC,QAAQ,CAAE,MACd,CAAE,CAAAL,QAAA,CAEDH,SAAS,CAACpB,MAAM,CAAG,EAAE,CAAGoB,SAAS,CAACS,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGT,SAAS,CAAc,CAAC,cAC9E5C,IAAA,CAACP,UAAU,EACP6D,IAAI,CAAC,KAAK,CACVC,KAAK,CAAC,SAAS,CACfC,OAAO,CAAElD,WAAY,CACrB,aAAW,OAAO,CAClBwC,SAAS,CAAC,aAAa,CAAAC,QAAA,cAEvB/C,IAAA,CAACN,SAAS,GAAE,CAAC,CACL,CAAC,cAEbM,IAAA,CAACX,aAAa,EAAA0D,QAAA,cACV/C,IAAA,CAACT,SAAS,EACLkE,SAAS,MACTX,SAAS,CAAC,UAAU,CAErBY,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAEhD,SAAS,CAAC,MAAM,CAAE,CACzBW,KAAK,CAAEV,gBAAiB,CACxBgD,QAAQ,CAAE1C,gBAAiB,CAC3B2C,SAAS,CAAE,CACPC,SAAS,CAAE,CACPC,SAAS,CAAE,EACf,CACJ,CAAE,CACFhD,KAAK,CAAE,CAAC,CAACA,KAAM,CACfiD,UAAU,CAAEjD,KAAM,CAClBkD,MAAM,CAAC,OAAO,CACjB,CAAC,CACS,CAAC,cAChBjE,IAAA,CAACZ,aAAa,EAAA2D,QAAA,cACV/C,IAAA,CAACR,MAAM,EAACgE,OAAO,CAAE/B,WAAY,CAACyC,QAAQ,CAAEvB,eAAgB,CAACe,OAAO,CAAC,WAAW,CAAAX,QAAA,CACvEpC,SAAS,CAAC,OAAO,CAAC,CACf,CAAC,CACE,CAAC,EACZ,CAAC,CAEjB,CAAC,CAED,cAAe,CAAAR,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}