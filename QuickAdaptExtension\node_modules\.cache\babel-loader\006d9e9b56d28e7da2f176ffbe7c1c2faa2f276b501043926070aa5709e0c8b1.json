{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\ImageCarousel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useTranslation } from 'react-i18next';\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageCarousel = ({\n  images,\n  selectedItem,\n  activeItem,\n  isMaximized\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const {\n    checklistGuideMetaData\n  } = useDrawerStore(state => state);\n  useEffect(() => {\n    setCurrentIndex(0);\n  }, [activeItem]);\n  // Function to change the image when clicking a progress dot\n  const goToImage = index => {\n    setCurrentIndex(index);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: \"-webkit-fill-available\",\n      height: \"244px\"\n    },\n    className: \"qadpt-imgsec\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100%',\n        width: '100%'\n      },\n      children: selectedItem.supportingMedia.length > 0 && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: images[currentIndex] // Show selected image\n        ,\n        alt: translate('Image {{index}}', {\n          index: currentIndex,\n          defaultValue: `Image ${currentIndex}`\n        }),\n        style: {\n          width: \"100%\",\n          height: \"100%\",\n          borderRadius: \"10px\",\n          transition: \"opacity 0.5s ease-in-out\",\n          // Smooth transition effect\n          objectFit: isMaximized ? \"contain\" : \"initial\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: \"5px\"\n      },\n      children: images.map((_, index) => {\n        var _checklistGuideMetaDa, _checklistGuideMetaDa2;\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: () => goToImage(index) // Set index correctly\n          ,\n          style: {\n            height: \"6px\",\n            width: \"6px\",\n            margin: \"3px\",\n            display: \"inline-block\",\n            backgroundColor: currentIndex === index ? (_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : (_checklistGuideMetaDa2 = _checklistGuideMetaDa.canvas) === null || _checklistGuideMetaDa2 === void 0 ? void 0 : _checklistGuideMetaDa2.primaryColor : \"gray\",\n            borderRadius: \"50%\",\n            cursor: \"pointer\",\n            transition: \"background-color 0.3s\"\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageCarousel, \"MNJQ4l/EmORWNDFCAyZJyQz8QHU=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ImageCarousel;\nexport default ImageCarousel;\nvar _c;\n$RefreshReg$(_c, \"ImageCarousel\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useTranslation", "useDrawerStore", "jsxDEV", "_jsxDEV", "ImageCarousel", "images", "selectedItem", "activeItem", "isMaximized", "_s", "t", "translate", "currentIndex", "setCurrentIndex", "checklistGuideMetaData", "state", "goToImage", "index", "style", "width", "height", "className", "children", "display", "justifyContent", "alignItems", "supportingMedia", "length", "src", "alt", "defaultValue", "borderRadius", "transition", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "map", "_", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "onClick", "margin", "backgroundColor", "canvas", "primaryColor", "cursor", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/ImageCarousel.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\n\r\ninterface ImageCarouselProps {\r\n  images: string[];\r\n  selectedItem: any;\r\n  activeItem: any;\r\n  isMaximized: any;\r\n}\r\n\r\nconst ImageCarousel: React.FC<ImageCarouselProps> = ({ images, selectedItem,activeItem,isMaximized }) => {\r\n  const { t: translate } = useTranslation();\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const {\r\n    checklistGuideMetaData,\r\n\r\n  } = useDrawerStore((state: any) => state);\r\n  useEffect(() =>\r\n  {\r\n    setCurrentIndex(0);\r\n},[activeItem])\r\n  // Function to change the image when clicking a progress dot\r\n  const goToImage = (index: number) => {\r\n    setCurrentIndex(index);\r\n  };\r\n\r\n  return (\r\n    <div style={{\r\n      width: \"-webkit-fill-available\",\r\n      height: \"244px\" ,\r\n    }} className=\"qadpt-imgsec\">\r\n      {/* 🖼️ Display Current Image */}\r\n      <div style={{\r\n  display: 'flex',\r\n  justifyContent: 'center',\r\n  alignItems: 'center',\r\n  height: '100%',\r\n  width: '100%'\r\n}}\r\n>\r\n        {selectedItem.supportingMedia.length > 0 && (\r\n          <img\r\n            src={images[currentIndex]} // Show selected image\r\n            alt={translate('Image {{index}}', { index: currentIndex, defaultValue: `Image ${currentIndex}` })}\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"100%\",\r\n              borderRadius: \"10px\",\r\n              transition: \"opacity 0.5s ease-in-out\", // Smooth transition effect\r\n              objectFit:isMaximized ? \"contain\" :\"initial\",\r\n            }}\r\n          />\r\n        )}\r\n       \r\n      </div>\r\n\r\n      {/* 🔵 Progress Dots */}\r\n      <div style={{ marginTop: \"5px\" }}>\r\n        {images.map((_, index) => (\r\n          <span\r\n            key={index}\r\n            onClick={() => goToImage(index)} // Set index correctly\r\n            style={{\r\n              height: \"6px\",\r\n              width: \"6px\",\r\n              margin: \"3px\",\r\n              display: \"inline-block\",\r\n              backgroundColor: currentIndex === index ? checklistGuideMetaData[0]?.canvas?.primaryColor : \"gray\",\r\n              borderRadius: \"50%\",\r\n              cursor: \"pointer\",\r\n              transition: \"background-color 0.3s\",\r\n            }}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageCarousel;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASrD,MAAMC,aAA2C,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAACC,UAAU;EAACC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACvG,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGX,cAAc,CAAC,CAAC;EACzC,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM;IACJe;EAEF,CAAC,GAAGb,cAAc,CAAEc,KAAU,IAAKA,KAAK,CAAC;EACzCjB,SAAS,CAAC,MACV;IACEe,eAAe,CAAC,CAAC,CAAC;EACtB,CAAC,EAAC,CAACN,UAAU,CAAC,CAAC;EACb;EACA,MAAMS,SAAS,GAAIC,KAAa,IAAK;IACnCJ,eAAe,CAACI,KAAK,CAAC;EACxB,CAAC;EAED,oBACEd,OAAA;IAAKe,KAAK,EAAE;MACVC,KAAK,EAAE,wBAAwB;MAC/BC,MAAM,EAAE;IACV,CAAE;IAACC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAEzBnB,OAAA;MAAKe,KAAK,EAAE;QAChBK,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBL,MAAM,EAAE,MAAM;QACdD,KAAK,EAAE;MACT,CAAE;MAAAG,QAAA,EAEOhB,YAAY,CAACoB,eAAe,CAACC,MAAM,GAAG,CAAC,iBACtCxB,OAAA;QACEyB,GAAG,EAAEvB,MAAM,CAACO,YAAY,CAAE,CAAC;QAAA;QAC3BiB,GAAG,EAAElB,SAAS,CAAC,iBAAiB,EAAE;UAAEM,KAAK,EAAEL,YAAY;UAAEkB,YAAY,EAAE,SAASlB,YAAY;QAAG,CAAC,CAAE;QAClGM,KAAK,EAAE;UACLC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdW,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UAAE;UACxCC,SAAS,EAACzB,WAAW,GAAG,SAAS,GAAE;QACrC;MAAE;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEE,CAAC,eAGNlC,OAAA;MAAKe,KAAK,EAAE;QAAEoB,SAAS,EAAE;MAAM,CAAE;MAAAhB,QAAA,EAC9BjB,MAAM,CAACkC,GAAG,CAAC,CAACC,CAAC,EAAEvB,KAAK;QAAA,IAAAwB,qBAAA,EAAAC,sBAAA;QAAA,oBACnBvC,OAAA;UAEEwC,OAAO,EAAEA,CAAA,KAAM3B,SAAS,CAACC,KAAK,CAAE,CAAC;UAAA;UACjCC,KAAK,EAAE;YACLE,MAAM,EAAE,KAAK;YACbD,KAAK,EAAE,KAAK;YACZyB,MAAM,EAAE,KAAK;YACbrB,OAAO,EAAE,cAAc;YACvBsB,eAAe,EAAEjC,YAAY,KAAKK,KAAK,IAAAwB,qBAAA,GAAG3B,sBAAsB,CAAC,CAAC,CAAC,cAAA2B,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BK,MAAM,cAAAJ,sBAAA,uBAAjCA,sBAAA,CAAmCK,YAAY,GAAG,MAAM;YAClGhB,YAAY,EAAE,KAAK;YACnBiB,MAAM,EAAE,SAAS;YACjBhB,UAAU,EAAE;UACd;QAAE,GAXGf,KAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CAAC;MAAA,CACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAnEIL,aAA2C;EAAA,QACtBJ,cAAc,EAKnCC,cAAc;AAAA;AAAAgD,EAAA,GANd7C,aAA2C;AAqEjD,eAAeA,aAAa;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}