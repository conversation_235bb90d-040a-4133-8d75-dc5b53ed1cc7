{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\AIAgent\\\\ModernChatWindow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useContext } from 'react';\nimport './ModernChatWindow.css';\nimport { AccountContext } from '../../components/login/AccountContext';\nimport { ai, airobot, upload, upload_hover, send } from \"../../assets/icons/icons\";\nimport CloseIcon from '@mui/icons-material/Close';\n// Import services\nimport { CreateInteraction } from '../../services/AIService';\nimport { startSpeechRecognition, stopSpeechRecognition, isSpeechRecognitionSupported } from '../../services/SpeechRecognitionService';\nimport useDrawerStore from '../../store/drawerStore';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Chat message type\n\nconst ModernChatWindow = ({\n  onClose,\n  updatedGuideData,\n  setStepData,\n  setIsPopupOpen,\n  setCurrentGuideId,\n  setIsLoggedIn,\n  setIsTourPopupOpen,\n  setIsDrawerClosed,\n  setShowBannerenduser,\n  setIsAIChatOpen\n}) => {\n  _s();\n  // Extract all needed functions from the drawer store\n  const {\n    setSelectedTemplate,\n    setSelectedTemplateTour,\n    setSteps,\n    SetGuideName,\n    setBannerPopup,\n    setCurrentStep,\n    TooltipGuideDetails,\n    HotspotGuideDetails,\n    setTooltipCount,\n    tooltipCount,\n    setElementSelected,\n    setSelectedStepTypeHotspot,\n    setCreateWithAI,\n    setIsAIGuidePersisted,\n    setInteractionData,\n    generateSteps,\n    setBannerButtonSelected,\n    syncAITooltipDataForPreview,\n    syncAIAnnouncementDataForPreview,\n    setIsUnSavedChanges,\n    setOpenWarning\n  } = useDrawerStore(state => state);\n  const [isChatOpen, setIsChatOpen] = useState(false); // New state for chat visibility\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const [inputText, setInputText] = useState('');\n  const [messages, setMessages] = useState([{\n    text: \"Hello There! Need an banner, tour, or anything else to engage your users? Let me know, and I’ll get it done fast!\",\n    isUser: false,\n    timestamp: new Date()\n  }]);\n  const [isLoading, setIsLoading] = useState(false);\n  // State for UI and error handling\n  const [error, setError] = useState(null);\n  const [isMicHovered, setIsMicHovered] = useState(false);\n  const [isUploadHovered, setIsUploadHovered] = useState(false);\n  const [showScrollButton, setShowScrollButton] = useState(false);\n  const [isListening, setIsListening] = useState(false);\n  const [speechSupported, setSpeechSupported] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const chatContainerRef = useRef(null);\n  useEffect(() => {\n    if (chatContainerRef.current) {\n      // Scroll to the bottom of the chat container\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n    }\n    setShowScrollButton(false);\n  }, [messages, isLoading]);\n  const handleScroll = () => {\n    if (chatContainerRef.current) {\n      const {\n        scrollTop,\n        scrollHeight,\n        clientHeight\n      } = chatContainerRef.current;\n\n      // Show scroll button when not at the bottom\n      const isAtBottom = scrollHeight - scrollTop - clientHeight < 20;\n      setShowScrollButton(!isAtBottom);\n    }\n  };\n  const scrollToBottom = () => {\n    if (chatContainerRef.current) {\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n      setShowScrollButton(false);\n    }\n  };\n\n  // Focus input on component mount\n  useEffect(() => {\n    var _inputRef$current;\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();\n  }, []);\n\n  // Check if speech recognition is supported\n  useEffect(() => {\n    setSpeechSupported(isSpeechRecognitionSupported());\n  }, []);\n  const handleInputChange = e => {\n    setInputText(e.target.value);\n    setError(null);\n\n    // Auto-resize the textarea\n    if (inputRef.current) {\n      inputRef.current.style.height = 'auto'; // Reset height first\n      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 80)}px`; // Grow to scrollHeight, max 100px\n    }\n  };\n  useEffect(() => {\n    if (inputRef.current && inputText === '') {\n      inputRef.current.style.height = '45px';\n    }\n  }, [inputText]);\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleCreateInteraction(inputText);\n    }\n  };\n\n  // Check if browser is Microsoft Edge\n  const isEdgeBrowser = () => {\n    return navigator.userAgent.indexOf(\"Edg\") !== -1;\n  };\n\n  // Handle speech recognition\n  const handleSpeechRecognition = () => {\n    if (isListening) {\n      stopSpeechRecognition();\n      setIsListening(false);\n      return;\n    }\n\n    // Get the current text in the input field\n    const initialText = inputText.trim();\n\n    // Show Edge-specific message if needed\n    if (isEdgeBrowser() && !speechSupported) {\n      setError(\"Speech recognition may not work in this version of Edge. Try using Chrome for better results.\");\n      setTimeout(() => setError(null), 5000);\n    }\n    setIsListening(true);\n    startSpeechRecognition({\n      pauseDuration: 5000,\n      // 5 seconds pause before stopping\n      onStart: () => {\n        setIsListening(true);\n        setError(null); // Clear any previous errors\n      },\n      onResult: (text, isFinal) => {\n        // The text parameter now contains the full accumulated transcript\n        // If there was already text in the input field, append the speech recognition result\n        // Otherwise, just set the text directly\n        if (initialText) {\n          setInputText(initialText + ' ' + text);\n        } else {\n          setInputText(text);\n        }\n      },\n      onEnd: () => {\n        setIsListening(false);\n      },\n      onError: error => {\n        console.error(\"Speech recognition error:\", error);\n        setIsListening(false);\n\n        // Provide more helpful error messages\n        if (isEdgeBrowser()) {\n          if (error === 'not-allowed') {\n            setError(\"Microphone access denied. Please allow microphone access in your browser settings.\");\n          } else if (error === 'network') {\n            setError(\"Network error occurred. Speech recognition might not work well in Edge. Try Chrome for better results.\");\n          } else {\n            setError(`Speech recognition failed in Edge: ${error}. Try using Chrome for better results.`);\n          }\n        } else {\n          setError(`Speech recognition failed: ${error}. Please try again.`);\n        }\n\n        // Clear error after 5 seconds\n        setTimeout(() => setError(null), 5000);\n      }\n    });\n  };\n\n  // Removed unused test function\n\n  //   const handleSendMessage = async () => {\n  //     if (!inputText.trim()) return;\n\n  //     // Add user message to chat\n  //     const userMessage: ChatMessage = {\n  //       text: inputText,\n  //       isUser: true,\n  //       timestamp: new Date()\n  //     };\n\n  //     setMessages(prev => [...prev, userMessage]);\n  //     setInputText('');\n  //     setIsLoading(true);\n\n  //     // Reset textarea height\n  //     if (inputRef.current) {\n  //       inputRef.current.style.height = 'auto';\n  //     }\n\n  //     try {\n  //       // For testing purposes, if the message is \"test\", add multiple messages\n  //       if (inputText.toLowerCase() === \"test\") {\n  //         addTestMessages();\n  //         return;\n  //       }\n\n  //       // Add user message to conversation history\n  //       const updatedConversation = [...conversation, { role: 'user' as const, content: inputText }];\n  //       setConversation(updatedConversation);\n\n  //       // Call OpenAI API\n  //       const response = await callOpenAI(updatedConversation);\n\n  //       // Add AI response to chat\n  //       const aiResponse: ChatMessage = {\n  //         text: response,\n  //         isUser: false,\n  //         timestamp: new Date()\n  //       };\n\n  //       // Add AI response to conversation history\n  //       setConversation([...updatedConversation, { role: 'assistant' as const, content: response }]);\n\n  //       // Update UI with AI response\n  //       setMessages(prev => [...prev, aiResponse]);\n  //       setIsLoading(false);\n\n  //     } catch (err) {\n  //       console.error(\"Error creating interaction:\", err);\n  //       setIsLoading(false);\n\n  //       // Show error message in chat\n  //       const errorMessage: ChatMessage = {\n  //         text: \"Sorry, I encountered an error while processing your request.\",\n  //         isUser: false,\n  //         timestamp: new Date()\n  //       };\n\n  //       setMessages(prev => [...prev, errorMessage]);\n  //     }\n  //   };\n  // Function to check if the user prompt matches any of the tour creation patterns\n  const isTourCreationPrompt = prompt => {\n    const tourPatterns = [/create\\s+a\\s+tour\\s+with\\s+steps\\s*:/i, /generate\\s+a\\s+tour\\s+with\\s*:/i, /create\\s+a\\s+user\\s+onboarding\\s+tour\\s+with/i, /make\\s+a\\s+training\\s+tour\\s+with/i, /create\\s+a\\s+tour/i, /generate\\s+a\\s+tour/i, /create\\s+tour\\s+with\\s+step/i, /create\\s+tour\\s+having\\s+step/i];\n    return tourPatterns.some(pattern => pattern.test(prompt));\n  };\n\n  // Function to parse the user prompt and determine the steps to include in the tour\n  const parseTourSteps = prompt => {\n    const steps = [];\n\n    // Check for specific step patterns with stepType explicitly mentioned\n    // First pattern: step1 as stepType having \"Announcement\"\n    const stepTypePattern1 = /step\\s*(\\d+)\\s*(?:as|having)\\s*(?:stepType|step type)\\s*(?:as|having|with)?\\s*[\"']?([a-zA-Z]+)[\"']?/gi;\n    // Second pattern: step1 as announcement\n    const stepTypePattern2 = /step\\s*(\\d+)\\s*(?:as|having|with)\\s*[\"']?([a-zA-Z]+)[\"']?/gi;\n    let match;\n\n    // Try the first pattern\n    while ((match = stepTypePattern1.exec(prompt)) !== null) {\n      const stepNumber = parseInt(match[1]);\n      let stepType = match[2].trim();\n\n      // Normalize step type (handle misspellings like \"Annoucement\")\n      if (stepType.toLowerCase().includes(\"announ\") || stepType.toLowerCase().includes(\"annoucement\")) {\n        stepType = \"Announcement\";\n      } else if (stepType.toLowerCase().includes(\"bann\")) {\n        stepType = \"Banner\";\n      } else if (stepType.toLowerCase().includes(\"tool\")) {\n        stepType = \"Tooltip\";\n      } else if (stepType.toLowerCase().includes(\"hot\")) {\n        stepType = \"Hotspot\";\n      } else {\n        // Default to Announcement if type is not recognized\n        stepType = \"Announcement\";\n      }\n\n      // Ensure first letter is capitalized\n      stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);\n      steps.push({\n        stepType: stepType,\n        title: `Step ${stepNumber}`,\n        content: `<p>step ${stepNumber}</p>`\n      });\n    }\n\n    // If no matches found with the first pattern, try the second pattern\n    if (steps.length === 0) {\n      while ((match = stepTypePattern2.exec(prompt)) !== null) {\n        const stepNumber = parseInt(match[1]);\n        let stepType = match[2].trim();\n\n        // Normalize step type (handle misspellings like \"Annoucement\")\n        if (stepType.toLowerCase().includes(\"announ\") || stepType.toLowerCase().includes(\"annoucement\")) {\n          stepType = \"Announcement\";\n        } else if (stepType.toLowerCase().includes(\"bann\")) {\n          stepType = \"Banner\";\n        } else if (stepType.toLowerCase().includes(\"tool\")) {\n          stepType = \"Tooltip\";\n        } else if (stepType.toLowerCase().includes(\"hot\")) {\n          stepType = \"Hotspot\";\n        } else {\n          // Default to Announcement if type is not recognized\n          stepType = \"Announcement\";\n        }\n\n        // Ensure first letter is capitalized\n        stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);\n        steps.push({\n          stepType: stepType,\n          title: `Step ${stepNumber}`,\n          content: `<p>step ${stepNumber}</p>`\n        });\n      }\n    }\n\n    // Check for specific step patterns like \"1. Announcement titled 'Welcome!'\"\n    if (steps.length === 0) {\n      const numberedStepPattern = /(\\d+)\\s*\\.\\s*(announcement|banner|tooltip|hotspot)\\s*(?:titled|saying)?\\s*['\"]?([^'\"]+)['\"]?/gi;\n      while ((match = numberedStepPattern.exec(prompt)) !== null) {\n        steps.push({\n          stepType: match[2].charAt(0).toUpperCase() + match[2].slice(1),\n          title: match[3].trim(),\n          content: `<p>${match[3].trim()}</p>`\n        });\n      }\n    }\n\n    // Check for button properties and text content\n    const buttonPattern = /step\\s*(\\d+).*?button\\s*(?:as|having)\\s*buttonName\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']\\s*and\\s*button\\s*actions\\s*as\\s*([a-zA-Z]+)\\s*and\\s*actionvalue\\s*as\\s*[\"']?([^\"']+)[\"']?/gi;\n    let buttonMatch;\n    while ((buttonMatch = buttonPattern.exec(prompt)) !== null) {\n      const stepNumber = parseInt(buttonMatch[1]);\n      const buttonName = buttonMatch[2].trim();\n      const buttonAction = buttonMatch[3].trim().toLowerCase();\n      const buttonActionValue = buttonMatch[4].trim();\n\n      // Find the step with this number\n      const stepIndex = steps.findIndex(step => {\n        const stepTitle = step.title || '';\n        return stepTitle.includes(`${stepNumber}`);\n      });\n      if (stepIndex !== -1) {\n        // Update existing step with button properties\n        steps[stepIndex].buttonName = buttonName;\n        steps[stepIndex].buttonAction = buttonAction;\n        steps[stepIndex].buttonActionValue = buttonActionValue;\n      }\n    }\n\n    // Check for text content\n    const textPattern = /step\\s*(\\d+).*?(?:with|having)\\s*text\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']/gi;\n    let textMatch;\n    while ((textMatch = textPattern.exec(prompt)) !== null) {\n      const stepNumber = parseInt(textMatch[1]);\n      const textContent = textMatch[2].trim();\n\n      // Find the step with this number\n      const stepIndex = steps.findIndex(step => {\n        const stepTitle = step.title || '';\n        return stepTitle.includes(`${stepNumber}`);\n      });\n      if (stepIndex !== -1) {\n        // Update existing step with text content\n        steps[stepIndex].content = `<p>${textContent}</p>`;\n      }\n    }\n\n    // If no specific steps found, check for general patterns\n    if (steps.length === 0) {\n      // Check for \"Announcement, Banner, Announcement\" pattern\n      const stepTypesPattern = /(announcement|banner|tooltip|hotspot)(?:\\s*,\\s*|\\s+and\\s+|\\s+)/gi;\n      const stepTypes = [];\n      while ((match = stepTypesPattern.exec(prompt)) !== null) {\n        stepTypes.push(match[1].charAt(0).toUpperCase() + match[1].slice(1));\n      }\n      if (stepTypes.length > 0) {\n        stepTypes.forEach((type, index) => {\n          steps.push({\n            stepType: type,\n            title: `Step ${index + 1}`,\n            content: `<p>step ${index + 1}</p>`\n          });\n        });\n      } else {\n        // Default to 3 steps: Announcement, Banner, Announcement\n        steps.push({\n          stepType: \"Announcement\",\n          title: \"Step 1\",\n          content: \"<p>step1</p>\"\n        }, {\n          stepType: \"Banner\",\n          title: \"Step 2\",\n          content: \"<p>step 2 banner</p>\"\n        }, {\n          stepType: \"Announcement\",\n          title: \"Step 3\",\n          content: \"<p>step 3</p>\"\n        });\n      }\n    }\n\n    // Sort steps by step number if they have numeric titles\n    steps.sort((a, b) => {\n      const aNum = a.title ? parseInt(a.title.replace(/\\D/g, '')) : 0;\n      const bNum = b.title ? parseInt(b.title.replace(/\\D/g, '')) : 0;\n      return aNum - bNum;\n    });\n    return steps;\n  };\n  const [dataNew, setDataNew] = useState();\n  const [stepDataNew, setStepDataNew] = useState();\n  useEffect(() => {\n    setElementSelected(false);\n  }, []);\n  const handleCreateInteraction = async value => {\n    setIsUnSavedChanges(true);\n    setError(null);\n    setCreateWithAI(true);\n    setIsAIGuidePersisted(false);\n    if (value.length < 10) {\n      setError(\"Text must be at least 10 characters long\");\n    } else {\n      // Add user message to chat\n      const userMessage = {\n        text: value,\n        isUser: true,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, userMessage]);\n      setInputText('');\n      setIsLoading(true);\n      try {\n        var _data$GuideStep$, _data$GuideStep$2, _data$GuideStep$2$But;\n        // Check if the prompt is for creating a tour\n\n        const aiResponse = {\n          text: \"Creating your interaction...\",\n          isUser: false,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiResponse]);\n        const data = await CreateInteraction(value, accountId, window.location.href);\n        setCurrentGuideId(data === null || data === void 0 ? void 0 : data.GuideId);\n        data.TargetUrl = window.location.href;\n        if ((data === null || data === void 0 ? void 0 : (_data$GuideStep$ = data.GuideStep[0]) === null || _data$GuideStep$ === void 0 ? void 0 : _data$GuideStep$.StepType) === \"Banner\" && (data === null || data === void 0 ? void 0 : (_data$GuideStep$2 = data.GuideStep[0]) === null || _data$GuideStep$2 === void 0 ? void 0 : (_data$GuideStep$2$But = _data$GuideStep$2.ButtonSection) === null || _data$GuideStep$2$But === void 0 ? void 0 : _data$GuideStep$2$But.length) > 0) {\n          setBannerButtonSelected(true);\n        }\n        setInteractionData(data);\n        setOpenWarning(false); // Reset openWarning when starting new AI creation\n        updatedGuideData = data;\n        generateSteps((data === null || data === void 0 ? void 0 : data.GuideStep) || []);\n        setInputText(\"\");\n        if (onClose) onClose();\n        if (data) {\n          setDataNew(data);\n          if ((data === null || data === void 0 ? void 0 : data.GuideType.toLowerCase()) === \"announcement\") {\n            setIsPopupOpen(true);\n            setCurrentGuideId(data === null || data === void 0 ? void 0 : data.GuideId);\n            setSelectedTemplate(\"Announcement\");\n            setBannerPopup(false);\n            SetGuideName(data === null || data === void 0 ? void 0 : data.Name);\n            if (setStepData) setStepData({\n              type: \"Announcement\"\n            });\n            TooltipGuideDetails();\n\n            // Synchronize AI announcement data with announcementGuideMetaData\n            setTimeout(() => {\n              syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup\n            }, 100);\n          } else if ((data === null || data === void 0 ? void 0 : data.GuideType.toLowerCase()) === \"banner\") {\n            setSelectedTemplate(\"Banner\", true);\n            setBannerPopup(true);\n            setIsPopupOpen(false);\n            setCurrentGuideId(data === null || data === void 0 ? void 0 : data.GuideId);\n            SetGuideName(data === null || data === void 0 ? void 0 : data.Name);\n            if (setStepData) setStepData({\n              type: \"Banner\"\n            });\n            // Ensure the builder screen is displayed\n            TooltipGuideDetails();\n          } else if ((data === null || data === void 0 ? void 0 : data.GuideType.toLowerCase()) === \"hotspot\") {\n            setElementSelected(false);\n            setSelectedTemplate(\"Hotspot\", true);\n            setIsPopupOpen(false);\n            SetGuideName(data === null || data === void 0 ? void 0 : data.Name);\n            setIsLoggedIn(true);\n            setIsTourPopupOpen(false);\n            setIsDrawerClosed(true);\n            setShowBannerenduser(false);\n            if (setStepData) setStepData({\n              type: \"Hotspot\"\n            });\n            // Ensure the builder screen is displayed\n            HotspotGuideDetails();\n          } else if ((data === null || data === void 0 ? void 0 : data.GuideType.toLowerCase()) === \"tooltip\") {\n            setElementSelected(false);\n            setSelectedTemplate(\"Tooltip\", true);\n            setIsPopupOpen(false);\n            SetGuideName(data === null || data === void 0 ? void 0 : data.Name);\n            setIsLoggedIn(true);\n            setIsTourPopupOpen(false);\n            setIsDrawerClosed(true);\n            setShowBannerenduser(false);\n            if (setStepData) setStepData({\n              type: \"Tooltip\"\n            });\n            // Ensure the builder screen is displayed\n            TooltipGuideDetails();\n            setTooltipCount(tooltipCount + 1);\n          } else if ((data === null || data === void 0 ? void 0 : data.GuideType.toLowerCase()) === \"tour\") {\n            setSelectedTemplate(\"Tour\", true);\n            setCurrentGuideId(data === null || data === void 0 ? void 0 : data.GuideId);\n            SetGuideName(data === null || data === void 0 ? void 0 : data.Name);\n            setIsPopupOpen(true);\n            setBannerPopup(false);\n            setIsLoggedIn(true);\n            setIsTourPopupOpen(true);\n            setIsDrawerClosed(false);\n            setShowBannerenduser(false);\n\n            // Generate steps for the drawer\n            if (data !== null && data !== void 0 && data.GuideStep && data.GuideStep.length > 0) {\n              const stepsData = data.GuideStep.map((step, index) => ({\n                id: step.StepId,\n                name: step.StepTitle || `Step ${index + 1}`,\n                stepType: step.StepType,\n                stepCount: index + 1\n              }));\n              setSteps(stepsData);\n\n              // Set the current step to the first step\n              if (stepsData.length > 0) {\n                setCurrentStep(1);\n\n                // Handle different step types based on the first step\n                const firstStep = data.GuideStep[0];\n                if (firstStep && firstStep.StepType) {\n                  const stepType = firstStep.StepType;\n                  setStepDataNew(stepType);\n\n                  // Set the selected template tour to the first step's type\n                  setSelectedTemplateTour(stepType, true); // BUGFIX: Preserve overlay settings\n\n                  // Handle specific step types\n                  if (stepType.toLowerCase() === \"announcement\") {\n                    TooltipGuideDetails();\n                    setSelectedTemplateTour(\"Announcement\", true); // BUGFIX: Preserve overlay settings\n                    setSelectedTemplate(\"Tour\", true);\n\n                    // Synchronize AI announcement data with announcementGuideMetaData\n                    setTimeout(() => {\n                      syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup\n                    }, 100);\n                    if (setStepData) setStepData({\n                      type: \"Announcement\"\n                    });\n                  } else if (stepType.toLowerCase() === \"banner\") {\n                    TooltipGuideDetails();\n                    setSelectedTemplate(\"Tour\", true);\n                    setSelectedTemplateTour(\"Banner\", true); // BUGFIX: Preserve overlay settings\n                    if (setStepData) setStepData({\n                      type: \"Banner\"\n                    });\n                    setBannerPopup(true);\n                  } else if (stepType.toLowerCase() === \"tooltip\") {\n                    setElementSelected(false);\n                    TooltipGuideDetails();\n                    setSelectedTemplateTour(\"Tooltip\", true); // BUGFIX: Preserve overlay settings\n                    setSelectedTemplate(\"Tour\", true);\n\n                    // Synchronize AI tooltip data with tooltipguidemetadata\n                    setTimeout(() => {\n                      syncAITooltipDataForPreview();\n                    }, 100);\n                    if (setStepData) setStepData({\n                      type: \"Tooltip\"\n                    });\n                    setTooltipCount(tooltipCount + 1);\n                  } else if (stepType.toLowerCase() === \"hotspot\") {\n                    setElementSelected(false);\n                    HotspotGuideDetails();\n                    setSelectedTemplateTour(\"Hotspot\");\n                    setSelectedTemplate(\"Tour\", true);\n                    setSelectedStepTypeHotspot(true);\n                    if (setStepData) setStepData({\n                      type: \"Hotspot\"\n                    });\n                    setTooltipCount(tooltipCount + 1);\n                  }\n                }\n              }\n            }\n          } else if ((data === null || data === void 0 ? void 0 : data.GuideType.toLowerCase()) === \"checklist\") {\n            // Handle checklist type if needed\n            setSelectedTemplate(\"Checklist\", true);\n            setCurrentGuideId(data === null || data === void 0 ? void 0 : data.GuideId);\n            SetGuideName(data === null || data === void 0 ? void 0 : data.Name);\n            if (setStepData) setStepData({\n              type: \"Checklist\"\n            });\n          }\n        }\n      } catch (error) {\n        console.error(\"Error creating interaction:\", error);\n        const errorMessage = {\n          text: \"Sorry, I encountered an error while processing your request.\",\n          isUser: false,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, errorMessage]);\n      } finally {\n        setIsLoading(false);\n        setError(null);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-chat-window\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-chat-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-chat-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"qadpt-chat-icon\",\n            dangerouslySetInnerHTML: {\n              __html: ai\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"AI Chatbot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"qadpt-close-btn\",\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-chat-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-messages\",\n          ref: chatContainerRef,\n          onScroll: handleScroll,\n          children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `qadpt-message ${message.isUser ? 'user-message' : 'ai-message'}`,\n            children: [!message.isUser && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-avatar\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: airobot\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: message.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-chat-message ai-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-avatar\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: airobot\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"typing-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 11\n          }, this), showScrollButton && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"scroll-to-bottom\",\n            onClick: scrollToBottom,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 16L6 10L7.4 8.6L12 13.2L16.6 8.6L18 10L12 16Z\",\n                fill: \"currentColor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-input\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-with-icons\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-txtcont\",\n            children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n              ref: inputRef,\n              value: inputText,\n              onChange: handleInputChange,\n              onKeyDown: handleKeyDown,\n              placeholder: \"Enter your prompt....\",\n              disabled: isLoading,\n              rows: 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-icons\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"icon-btn upload-btn\",\n                onMouseEnter: () => setIsUploadHovered(true),\n                onMouseLeave: () => setIsUploadHovered(false),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: isUploadHovered ? upload_hover : upload\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"icon-btn send-btn\",\n              onClick: () => handleCreateInteraction(inputText),\n              disabled: isLoading || !inputText.trim(),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: send\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n};\n_s(ModernChatWindow, \"ewAXU2GtfToRjnNayNSzqEgHftM=\", false, function () {\n  return [useDrawerStore];\n});\n_c = ModernChatWindow;\nexport default ModernChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ModernChatWindow\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useContext", "AccountContext", "ai", "airobot", "upload", "upload_hover", "send", "CloseIcon", "CreateInteraction", "startSpeechRecognition", "stopSpeechRecognition", "isSpeechRecognitionSupported", "useDrawerStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernChatWindow", "onClose", "updatedGuideData", "setStepData", "setIsPopupOpen", "setCurrentGuideId", "setIsLoggedIn", "setIsTourPopupOpen", "setIsDrawerClosed", "setShowBannerenduser", "setIsAIChatOpen", "_s", "setSelectedTemplate", "setSelectedTemplateTour", "setSteps", "SetGuideName", "setBannerPopup", "setCurrentStep", "TooltipGuideDetails", "HotspotGuideDetails", "setTooltipCount", "tooltipCount", "setElementSelected", "setSelectedStepTypeHotspot", "setCreateWithAI", "setIsAIGuidePersisted", "setInteractionData", "generateSteps", "setBannerButtonSelected", "syncAITooltipDataForPreview", "syncAIAnnouncementDataForPreview", "setIsUnSavedChanges", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "isChatOpen", "setIsChatOpen", "accountId", "inputText", "setInputText", "messages", "setMessages", "text", "isUser", "timestamp", "Date", "isLoading", "setIsLoading", "error", "setError", "isMicHovered", "setIsMicHovered", "isUploadHovered", "setIsUploadHovered", "showScrollButton", "setShowScrollButton", "isListening", "setIsListening", "speechSupported", "setSpeechSupported", "messagesEndRef", "inputRef", "chatContainerRef", "current", "scrollTop", "scrollHeight", "handleScroll", "clientHeight", "isAtBottom", "scrollToBottom", "_inputRef$current", "focus", "handleInputChange", "e", "target", "value", "style", "height", "Math", "min", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "handleCreateInteraction", "isEdge<PERSON><PERSON>er", "navigator", "userAgent", "indexOf", "handleSpeechRecognition", "initialText", "trim", "setTimeout", "pauseDuration", "onStart", "onResult", "isFinal", "onEnd", "onError", "console", "isTourCreationPrompt", "prompt", "tourPatterns", "some", "pattern", "test", "parseTourSteps", "steps", "stepTypePattern1", "stepTypePattern2", "match", "exec", "<PERSON><PERSON><PERSON><PERSON>", "parseInt", "stepType", "toLowerCase", "includes", "char<PERSON>t", "toUpperCase", "slice", "push", "title", "content", "length", "numberedStepPattern", "buttonPattern", "buttonMatch", "buttonName", "buttonAction", "buttonActionValue", "stepIndex", "findIndex", "step", "step<PERSON>itle", "textPattern", "textMatch", "textContent", "stepTypesPattern", "stepTypes", "for<PERSON>ach", "type", "index", "sort", "a", "b", "aNum", "replace", "bNum", "dataNew", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setStepDataNew", "userMessage", "prev", "_data$GuideStep$", "_data$GuideStep$2", "_data$GuideStep$2$But", "aiResponse", "data", "window", "location", "href", "GuideId", "TargetUrl", "GuideStep", "StepType", "ButtonSection", "GuideType", "Name", "stepsData", "map", "id", "StepId", "name", "<PERSON><PERSON><PERSON><PERSON>", "stepCount", "firstStep", "errorMessage", "children", "className", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fontSize", "ref", "onScroll", "message", "width", "viewBox", "fill", "xmlns", "d", "onChange", "onKeyDown", "placeholder", "disabled", "rows", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AIAgent/ModernChatWindow.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useContext } from 'react';\r\nimport './ModernChatWindow.css';\r\nimport { AccountContext } from '../../components/login/AccountContext';\r\nimport { ai, airobot, micicon, micicon_hover, upload, upload_hover, send } from \"../../assets/icons/icons\";\r\nimport CloseIcon from '@mui/icons-material/Close';\r\n// Import services\r\nimport { CreateInteraction } from '../../services/AIService';\r\nimport { startSpeechRecognition, stopSpeechRecognition, isSpeechRecognitionSupported } from '../../services/SpeechRecognitionService';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\ninterface ModernChatWindowProps {\r\n    onClose: () => void;\r\n    setIsPopupOpen: any;\r\n    setCurrentGuideId: any;\r\n    setIsLoggedIn: any;\r\n    setIsTourPopupOpen: any;\r\n    setIsDrawerClosed: any;\r\n    setShowBannerenduser: any;\r\n  setIsAIChatOpen?: any; // Make this optional\r\n  setStepData?: any; // Optional function to set step data\r\n  updatedGuideData?: any;\r\n}\r\n\r\ninterface ChatMessage {\r\n  text: string;\r\n  isUser: boolean;\r\n  timestamp: Date;\r\n}\r\n\r\n// Chat message type\r\n\r\nconst ModernChatWindow: React.FC<ModernChatWindowProps> = ({ onClose,updatedGuideData, setStepData, setIsPopupOpen, setCurrentGuideId, setIsLoggedIn, setIsTourPopupOpen, setIsDrawerClosed, setShowBannerenduser, setIsAIChatOpen }) => {\r\n    // Extract all needed functions from the drawer store\r\n    const {\r\n\t\tsetSelectedTemplate,\r\n\t\tsetSelectedTemplateTour,\r\n\t\tsetSteps,\r\n\t\tSetGuideName,\r\n\t\tsetBannerPopup,\r\n\t\tsetCurrentStep,\r\n\t\tTooltipGuideDetails,\r\n\t\tHotspotGuideDetails,\r\n\t\tsetTooltipCount,\r\n\t\ttooltipCount,\r\n\t\tsetElementSelected,\r\n      setSelectedStepTypeHotspot,\r\n      setCreateWithAI,\r\n      setIsAIGuidePersisted,\r\n      setInteractionData,\r\n      generateSteps,\r\n      setBannerButtonSelected,\r\n      syncAITooltipDataForPreview,\r\n      syncAIAnnouncementDataForPreview,\r\n      setIsUnSavedChanges,\r\n      setOpenWarning\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n    const [isChatOpen, setIsChatOpen] = useState(false);  // New state for chat visibility\r\n    const { accountId } = useContext(AccountContext);\r\n    const [inputText, setInputText] = useState('');\r\n    const [messages, setMessages] = useState<ChatMessage[]>([\r\n        {\r\n        text: \"Hello There! Need an banner, tour, or anything else to engage your users? Let me know, and I’ll get it done fast!\",\r\n        isUser: false,\r\n        timestamp: new Date()\r\n        }\r\n    ]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  // State for UI and error handling\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isMicHovered, setIsMicHovered] = useState(false);\r\n  const [isUploadHovered, setIsUploadHovered] = useState(false);\r\n  const [showScrollButton, setShowScrollButton] = useState(false);\r\n  const [isListening, setIsListening] = useState(false);\r\n  const [speechSupported, setSpeechSupported] = useState(false);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLTextAreaElement>(null);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (chatContainerRef.current) {\r\n      // Scroll to the bottom of the chat container\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n\r\n    setShowScrollButton(false);\r\n  }, [messages, isLoading]);\r\n\r\n\r\n  const handleScroll = () => {\r\n    if (chatContainerRef.current) {\r\n      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;\r\n\r\n      // Show scroll button when not at the bottom\r\n      const isAtBottom = scrollHeight - scrollTop - clientHeight < 20;\r\n      setShowScrollButton(!isAtBottom);\r\n    }\r\n  };\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n      setShowScrollButton(false);\r\n    }\r\n  };\r\n\r\n  // Focus input on component mount\r\n  useEffect(() => {\r\n    inputRef.current?.focus();\r\n  }, []);\r\n\r\n  // Check if speech recognition is supported\r\n  useEffect(() => {\r\n    setSpeechSupported(isSpeechRecognitionSupported());\r\n  }, []);\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setInputText(e.target.value);\r\n    setError(null);\r\n\r\n    // Auto-resize the textarea\r\n    if (inputRef.current) {\r\n      inputRef.current.style.height = 'auto'; // Reset height first\r\n      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 80)}px`; // Grow to scrollHeight, max 100px\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    if (inputRef.current && inputText === '') {\r\n      inputRef.current.style.height = '45px';\r\n    }\r\n  }, [inputText]);\r\n\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleCreateInteraction(inputText);\r\n    }\r\n  };\r\n\r\n  // Check if browser is Microsoft Edge\r\n  const isEdgeBrowser = (): boolean => {\r\n    return navigator.userAgent.indexOf(\"Edg\") !== -1;\r\n  };\r\n\r\n  // Handle speech recognition\r\n  const handleSpeechRecognition = () => {\r\n    if (isListening) {\r\n      stopSpeechRecognition();\r\n      setIsListening(false);\r\n      return;\r\n    }\r\n\r\n    // Get the current text in the input field\r\n    const initialText = inputText.trim();\r\n\r\n    // Show Edge-specific message if needed\r\n    if (isEdgeBrowser() && !speechSupported) {\r\n      setError(\"Speech recognition may not work in this version of Edge. Try using Chrome for better results.\");\r\n      setTimeout(() => setError(null), 5000);\r\n    }\r\n\r\n    setIsListening(true);\r\n    startSpeechRecognition({\r\n      pauseDuration: 5000, // 5 seconds pause before stopping\r\n      onStart: () => {\r\n        setIsListening(true);\r\n        setError(null); // Clear any previous errors\r\n      },\r\n      onResult: (text: string, isFinal: boolean) => {\r\n        // The text parameter now contains the full accumulated transcript\r\n        // If there was already text in the input field, append the speech recognition result\r\n        // Otherwise, just set the text directly\r\n        if (initialText) {\r\n          setInputText(initialText + ' ' + text);\r\n        } else {\r\n          setInputText(text);\r\n        }\r\n      },\r\n      onEnd: () => {\r\n        setIsListening(false);\r\n      },\r\n      onError: (error: any) => {\r\n        console.error(\"Speech recognition error:\", error);\r\n        setIsListening(false);\r\n\r\n        // Provide more helpful error messages\r\n        if (isEdgeBrowser()) {\r\n          if (error === 'not-allowed') {\r\n            setError(\"Microphone access denied. Please allow microphone access in your browser settings.\");\r\n          } else if (error === 'network') {\r\n            setError(\"Network error occurred. Speech recognition might not work well in Edge. Try Chrome for better results.\");\r\n          } else {\r\n            setError(`Speech recognition failed in Edge: ${error}. Try using Chrome for better results.`);\r\n          }\r\n        } else {\r\n          setError(`Speech recognition failed: ${error}. Please try again.`);\r\n        }\r\n\r\n        // Clear error after 5 seconds\r\n        setTimeout(() => setError(null), 5000);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Removed unused test function\r\n\r\n//   const handleSendMessage = async () => {\r\n//     if (!inputText.trim()) return;\r\n\r\n//     // Add user message to chat\r\n//     const userMessage: ChatMessage = {\r\n//       text: inputText,\r\n//       isUser: true,\r\n//       timestamp: new Date()\r\n//     };\r\n\r\n//     setMessages(prev => [...prev, userMessage]);\r\n//     setInputText('');\r\n//     setIsLoading(true);\r\n\r\n//     // Reset textarea height\r\n//     if (inputRef.current) {\r\n//       inputRef.current.style.height = 'auto';\r\n//     }\r\n\r\n//     try {\r\n//       // For testing purposes, if the message is \"test\", add multiple messages\r\n//       if (inputText.toLowerCase() === \"test\") {\r\n//         addTestMessages();\r\n//         return;\r\n//       }\r\n\r\n//       // Add user message to conversation history\r\n//       const updatedConversation = [...conversation, { role: 'user' as const, content: inputText }];\r\n//       setConversation(updatedConversation);\r\n\r\n//       // Call OpenAI API\r\n//       const response = await callOpenAI(updatedConversation);\r\n\r\n//       // Add AI response to chat\r\n//       const aiResponse: ChatMessage = {\r\n//         text: response,\r\n//         isUser: false,\r\n//         timestamp: new Date()\r\n//       };\r\n\r\n//       // Add AI response to conversation history\r\n//       setConversation([...updatedConversation, { role: 'assistant' as const, content: response }]);\r\n\r\n//       // Update UI with AI response\r\n//       setMessages(prev => [...prev, aiResponse]);\r\n//       setIsLoading(false);\r\n\r\n//     } catch (err) {\r\n//       console.error(\"Error creating interaction:\", err);\r\n//       setIsLoading(false);\r\n\r\n//       // Show error message in chat\r\n//       const errorMessage: ChatMessage = {\r\n//         text: \"Sorry, I encountered an error while processing your request.\",\r\n//         isUser: false,\r\n//         timestamp: new Date()\r\n//       };\r\n\r\n//       setMessages(prev => [...prev, errorMessage]);\r\n//     }\r\n//   };\r\n  // Function to check if the user prompt matches any of the tour creation patterns\r\n  const isTourCreationPrompt = (prompt: string): boolean => {\r\n    const tourPatterns = [\r\n      /create\\s+a\\s+tour\\s+with\\s+steps\\s*:/i,\r\n      /generate\\s+a\\s+tour\\s+with\\s*:/i,\r\n      /create\\s+a\\s+user\\s+onboarding\\s+tour\\s+with/i,\r\n      /make\\s+a\\s+training\\s+tour\\s+with/i,\r\n      /create\\s+a\\s+tour/i,\r\n      /generate\\s+a\\s+tour/i,\r\n      /create\\s+tour\\s+with\\s+step/i,\r\n      /create\\s+tour\\s+having\\s+step/i\r\n    ];\r\n\r\n    return tourPatterns.some(pattern => pattern.test(prompt));\r\n  };\r\n\r\n  // Function to parse the user prompt and determine the steps to include in the tour\r\n  const parseTourSteps = (prompt: string): { stepType: string, title?: string, content?: string, buttonName?: string, buttonAction?: string, buttonActionValue?: string }[] => {\r\n    const steps: { stepType: string, title?: string, content?: string, buttonName?: string, buttonAction?: string, buttonActionValue?: string }[] = [];\r\n\r\n    // Check for specific step patterns with stepType explicitly mentioned\r\n    // First pattern: step1 as stepType having \"Announcement\"\r\n    const stepTypePattern1 = /step\\s*(\\d+)\\s*(?:as|having)\\s*(?:stepType|step type)\\s*(?:as|having|with)?\\s*[\"']?([a-zA-Z]+)[\"']?/gi;\r\n    // Second pattern: step1 as announcement\r\n    const stepTypePattern2 = /step\\s*(\\d+)\\s*(?:as|having|with)\\s*[\"']?([a-zA-Z]+)[\"']?/gi;\r\n\r\n    let match;\r\n\r\n    // Try the first pattern\r\n    while ((match = stepTypePattern1.exec(prompt)) !== null) {\r\n      const stepNumber = parseInt(match[1]);\r\n      let stepType = match[2].trim();\r\n\r\n      // Normalize step type (handle misspellings like \"Annoucement\")\r\n      if (stepType.toLowerCase().includes(\"announ\") || stepType.toLowerCase().includes(\"annoucement\")) {\r\n        stepType = \"Announcement\";\r\n      } else if (stepType.toLowerCase().includes(\"bann\")) {\r\n        stepType = \"Banner\";\r\n      } else if (stepType.toLowerCase().includes(\"tool\")) {\r\n        stepType = \"Tooltip\";\r\n      } else if (stepType.toLowerCase().includes(\"hot\")) {\r\n        stepType = \"Hotspot\";\r\n      } else {\r\n        // Default to Announcement if type is not recognized\r\n        stepType = \"Announcement\";\r\n      }\r\n\r\n      // Ensure first letter is capitalized\r\n      stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);\r\n\r\n      steps.push({\r\n        stepType: stepType,\r\n        title: `Step ${stepNumber}`,\r\n        content: `<p>step ${stepNumber}</p>`\r\n      });\r\n    }\r\n\r\n    // If no matches found with the first pattern, try the second pattern\r\n    if (steps.length === 0) {\r\n      while ((match = stepTypePattern2.exec(prompt)) !== null) {\r\n        const stepNumber = parseInt(match[1]);\r\n        let stepType = match[2].trim();\r\n\r\n        // Normalize step type (handle misspellings like \"Annoucement\")\r\n        if (stepType.toLowerCase().includes(\"announ\") || stepType.toLowerCase().includes(\"annoucement\")) {\r\n          stepType = \"Announcement\";\r\n        } else if (stepType.toLowerCase().includes(\"bann\")) {\r\n          stepType = \"Banner\";\r\n        } else if (stepType.toLowerCase().includes(\"tool\")) {\r\n          stepType = \"Tooltip\";\r\n        } else if (stepType.toLowerCase().includes(\"hot\")) {\r\n          stepType = \"Hotspot\";\r\n        } else {\r\n          // Default to Announcement if type is not recognized\r\n          stepType = \"Announcement\";\r\n        }\r\n\r\n        // Ensure first letter is capitalized\r\n        stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);\r\n\r\n        steps.push({\r\n          stepType: stepType,\r\n          title: `Step ${stepNumber}`,\r\n          content: `<p>step ${stepNumber}</p>`\r\n        });\r\n      }\r\n    }\r\n\r\n    // Check for specific step patterns like \"1. Announcement titled 'Welcome!'\"\r\n    if (steps.length === 0) {\r\n      const numberedStepPattern = /(\\d+)\\s*\\.\\s*(announcement|banner|tooltip|hotspot)\\s*(?:titled|saying)?\\s*['\"]?([^'\"]+)['\"]?/gi;\r\n\r\n      while ((match = numberedStepPattern.exec(prompt)) !== null) {\r\n        steps.push({\r\n          stepType: match[2].charAt(0).toUpperCase() + match[2].slice(1),\r\n          title: match[3].trim(),\r\n          content: `<p>${match[3].trim()}</p>`\r\n        });\r\n      }\r\n    }\r\n\r\n    // Check for button properties and text content\r\n    const buttonPattern = /step\\s*(\\d+).*?button\\s*(?:as|having)\\s*buttonName\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']\\s*and\\s*button\\s*actions\\s*as\\s*([a-zA-Z]+)\\s*and\\s*actionvalue\\s*as\\s*[\"']?([^\"']+)[\"']?/gi;\r\n    let buttonMatch;\r\n    while ((buttonMatch = buttonPattern.exec(prompt)) !== null) {\r\n      const stepNumber = parseInt(buttonMatch[1]);\r\n      const buttonName = buttonMatch[2].trim();\r\n      const buttonAction = buttonMatch[3].trim().toLowerCase();\r\n      const buttonActionValue = buttonMatch[4].trim();\r\n\r\n      // Find the step with this number\r\n      const stepIndex = steps.findIndex(step => {\r\n        const stepTitle = step.title || '';\r\n        return stepTitle.includes(`${stepNumber}`);\r\n      });\r\n\r\n      if (stepIndex !== -1) {\r\n        // Update existing step with button properties\r\n        steps[stepIndex].buttonName = buttonName;\r\n        steps[stepIndex].buttonAction = buttonAction;\r\n        steps[stepIndex].buttonActionValue = buttonActionValue;\r\n      }\r\n    }\r\n\r\n    // Check for text content\r\n    const textPattern = /step\\s*(\\d+).*?(?:with|having)\\s*text\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']/gi;\r\n    let textMatch;\r\n    while ((textMatch = textPattern.exec(prompt)) !== null) {\r\n      const stepNumber = parseInt(textMatch[1]);\r\n      const textContent = textMatch[2].trim();\r\n\r\n      // Find the step with this number\r\n      const stepIndex = steps.findIndex(step => {\r\n        const stepTitle = step.title || '';\r\n        return stepTitle.includes(`${stepNumber}`);\r\n      });\r\n\r\n      if (stepIndex !== -1) {\r\n        // Update existing step with text content\r\n        steps[stepIndex].content = `<p>${textContent}</p>`;\r\n      }\r\n    }\r\n\r\n    // If no specific steps found, check for general patterns\r\n    if (steps.length === 0) {\r\n      // Check for \"Announcement, Banner, Announcement\" pattern\r\n      const stepTypesPattern = /(announcement|banner|tooltip|hotspot)(?:\\s*,\\s*|\\s+and\\s+|\\s+)/gi;\r\n      const stepTypes: string[] = [];\r\n\r\n      while ((match = stepTypesPattern.exec(prompt)) !== null) {\r\n        stepTypes.push(match[1].charAt(0).toUpperCase() + match[1].slice(1));\r\n      }\r\n\r\n      if (stepTypes.length > 0) {\r\n        stepTypes.forEach((type, index) => {\r\n          steps.push({\r\n            stepType: type,\r\n            title: `Step ${index + 1}`,\r\n            content: `<p>step ${index + 1}</p>`\r\n          });\r\n        });\r\n      } else {\r\n        // Default to 3 steps: Announcement, Banner, Announcement\r\n        steps.push(\r\n          { stepType: \"Announcement\", title: \"Step 1\", content: \"<p>step1</p>\" },\r\n          { stepType: \"Banner\", title: \"Step 2\", content: \"<p>step 2 banner</p>\" },\r\n          { stepType: \"Announcement\", title: \"Step 3\", content: \"<p>step 3</p>\" }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Sort steps by step number if they have numeric titles\r\n    steps.sort((a, b) => {\r\n      const aNum = a.title ? parseInt(a.title.replace(/\\D/g, '')) : 0;\r\n      const bNum = b.title ? parseInt(b.title.replace(/\\D/g, '')) : 0;\r\n      return aNum - bNum;\r\n    });\r\n\r\n    return steps;\r\n  };\r\n\r\n\r\n  const [dataNew, setDataNew] = useState<any>();\r\n  const[stepDataNew,setStepDataNew]=useState<any>();\r\n  useEffect(() =>\r\n  {\r\n      setElementSelected(false);\r\n\r\n},[])\r\n  const handleCreateInteraction = async (value: string) => {\r\n    setIsUnSavedChanges(true);\r\n    setError(null);\r\n    setCreateWithAI(true);\r\n    setIsAIGuidePersisted(false);\r\n    if (value.length < 10) {\r\n        setError(\"Text must be at least 10 characters long\");\r\n    } else {\r\n        // Add user message to chat\r\n        const userMessage: ChatMessage = {\r\n          text: value,\r\n          isUser: true,\r\n          timestamp: new Date()\r\n        };\r\n        setMessages(prev => [...prev, userMessage]);\r\n        setInputText('');\r\n        setIsLoading(true);\r\n        try {\r\n          // Check if the prompt is for creating a tour\r\n\r\n\r\n          const aiResponse: ChatMessage = {\r\n            text: \"Creating your interaction...\",\r\n            isUser: false,\r\n            timestamp: new Date()\r\n          };\r\n          setMessages(prev => [...prev, aiResponse]);\r\n\r\n          const data = await CreateInteraction(value, accountId, window.location.href);\r\n\r\n          setCurrentGuideId(data?.GuideId);\r\n          data.TargetUrl = window.location.href;\r\n          if(data?.GuideStep[0]?.StepType === \"Banner\" && data?.GuideStep[0]?.ButtonSection?.length>0){\r\n            setBannerButtonSelected(true);\r\n          }\r\n          setInteractionData(data);\r\n          setOpenWarning(false); // Reset openWarning when starting new AI creation\r\n          updatedGuideData = data;\r\n\r\n          generateSteps(data?.GuideStep || []);\r\n          setInputText(\"\");\r\n\r\n          if (onClose) onClose();\r\n\r\n          if (data) {\r\n            setDataNew(data);\r\n            if (data?.GuideType.toLowerCase() === \"announcement\") {\r\n              setIsPopupOpen(true);\r\n              setCurrentGuideId(data?.GuideId);\r\n              setSelectedTemplate(\"Announcement\");\r\n              setBannerPopup(false);\r\n              SetGuideName(data?.Name);\r\n              if (setStepData) setStepData({ type: \"Announcement\" });\r\n              TooltipGuideDetails();\r\n\r\n              // Synchronize AI announcement data with announcementGuideMetaData\r\n              setTimeout(() => {\r\n                syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup\r\n              }, 100);\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"banner\") {\r\n              setSelectedTemplate(\"Banner\", true);\r\n              setBannerPopup(true);\r\n              setIsPopupOpen(false);\r\n              setCurrentGuideId(data?.GuideId);\r\n              SetGuideName(data?.Name);\r\n              if (setStepData) setStepData({ type: \"Banner\" });\r\n              // Ensure the builder screen is displayed\r\n              TooltipGuideDetails();\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"hotspot\") {\r\n              setElementSelected(false);\r\n              setSelectedTemplate(\"Hotspot\", true);\r\n              setIsPopupOpen(false);\r\n              SetGuideName(data?.Name);\r\n              setIsLoggedIn(true);\r\n              setIsTourPopupOpen(false);\r\n              setIsDrawerClosed(true);\r\n              setShowBannerenduser(false);\r\n              if (setStepData) setStepData({ type: \"Hotspot\" });\r\n              // Ensure the builder screen is displayed\r\n              HotspotGuideDetails();\r\n\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"tooltip\") {\r\n              setElementSelected(false);\r\n              setSelectedTemplate(\"Tooltip\", true);\r\n              setIsPopupOpen(false);\r\n              SetGuideName(data?.Name);\r\n              setIsLoggedIn(true);\r\n              setIsTourPopupOpen(false);\r\n              setIsDrawerClosed(true);\r\n              setShowBannerenduser(false);\r\n              if (setStepData) setStepData({ type: \"Tooltip\" });\r\n              // Ensure the builder screen is displayed\r\n              TooltipGuideDetails();\r\n              setTooltipCount(tooltipCount + 1);\r\n\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"tour\") {\r\n              setSelectedTemplate(\"Tour\", true);\r\n              setCurrentGuideId(data?.GuideId);\r\n              SetGuideName(data?.Name);\r\n              setIsPopupOpen(true);\r\n              setBannerPopup(false);\r\n              setIsLoggedIn(true);\r\n              setIsTourPopupOpen(true);\r\n              setIsDrawerClosed(false);\r\n              setShowBannerenduser(false);\r\n\r\n              // Generate steps for the drawer\r\n              if (data?.GuideStep && data.GuideStep.length > 0) {\r\n                const stepsData = data.GuideStep.map((step: any, index: number) => ({\r\n                  id: step.StepId,\r\n                  name: step.StepTitle || `Step ${index + 1}`,\r\n                  stepType: step.StepType,\r\n                  stepCount: index + 1\r\n                }));\r\n                setSteps(stepsData);\r\n\r\n                // Set the current step to the first step\r\n                if (stepsData.length > 0) {\r\n                  setCurrentStep(1);\r\n\r\n                  // Handle different step types based on the first step\r\n                  const firstStep = data.GuideStep[0];\r\n                  if (firstStep && firstStep.StepType) {\r\n                    const stepType = firstStep.StepType;\r\n                    setStepDataNew(stepType);\r\n\r\n                    // Set the selected template tour to the first step's type\r\n                    setSelectedTemplateTour(stepType, true); // BUGFIX: Preserve overlay settings\r\n\r\n                    // Handle specific step types\r\n                    if (stepType.toLowerCase() === \"announcement\") {\r\n                      TooltipGuideDetails();\r\n                      setSelectedTemplateTour(\"Announcement\", true); // BUGFIX: Preserve overlay settings\r\n                      setSelectedTemplate(\"Tour\", true);\r\n\r\n                      // Synchronize AI announcement data with announcementGuideMetaData\r\n                      setTimeout(() => {\r\n                        syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup\r\n                      }, 100);\r\n\r\n                      if (setStepData) setStepData({ type: \"Announcement\" });\r\n                    }\r\n                    else if (stepType.toLowerCase() === \"banner\") {\r\n                      TooltipGuideDetails();\r\n                      setSelectedTemplate(\"Tour\", true);\r\n                      setSelectedTemplateTour(\"Banner\", true); // BUGFIX: Preserve overlay settings\r\n                      if (setStepData) setStepData({ type: \"Banner\" });\r\n                      setBannerPopup(true);\r\n                    }\r\n                    else if (stepType.toLowerCase() === \"tooltip\") {\r\n                      setElementSelected(false);\r\n\r\n                      TooltipGuideDetails();\r\n                      setSelectedTemplateTour(\"Tooltip\", true); // BUGFIX: Preserve overlay settings\r\n                      setSelectedTemplate(\"Tour\", true);\r\n\r\n                      // Synchronize AI tooltip data with tooltipguidemetadata\r\n                      setTimeout(() => {\r\n                        syncAITooltipDataForPreview();\r\n                      }, 100);\r\n\r\n                      if (setStepData) setStepData({ type: \"Tooltip\" });\r\n                      setTooltipCount(tooltipCount + 1);\r\n                    }\r\n                    else if (stepType.toLowerCase() === \"hotspot\") {\r\n                      setElementSelected(false);\r\n                      HotspotGuideDetails();\r\n                      setSelectedTemplateTour(\"Hotspot\");\r\n                      setSelectedTemplate(\"Tour\", true);\r\n                      setSelectedStepTypeHotspot(true);\r\n                      if (setStepData) setStepData({ type: \"Hotspot\" });\r\n                      setTooltipCount(tooltipCount + 1);\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"checklist\") {\r\n              // Handle checklist type if needed\r\n              setSelectedTemplate(\"Checklist\", true);\r\n              setCurrentGuideId(data?.GuideId);\r\n              SetGuideName(data?.Name);\r\n              if (setStepData) setStepData({ type: \"Checklist\" });\r\n            }\r\n          }\r\n\r\n        } catch (error) {\r\n          console.error(\"Error creating interaction:\", error);\r\n          const errorMessage: ChatMessage = {\r\n            text: \"Sorry, I encountered an error while processing your request.\",\r\n            isUser: false,\r\n            timestamp: new Date()\r\n          };\r\n          setMessages(prev => [...prev, errorMessage]);\r\n        } finally {\r\n          setIsLoading(false);\r\n          setError(null);\r\n        }\r\n    }\r\n};\r\n  return (\r\n    <>\r\n    <div className=\"qadpt-chat-window\">\r\n      <div className=\"qadpt-chat-header\">\r\n        <div className=\"qadpt-chat-title\">\r\n          <span className=\"qadpt-chat-icon\" dangerouslySetInnerHTML={{ __html: ai }} />\r\n          <span>AI Chatbot</span>\r\n        </div>\r\n        <button className=\"qadpt-close-btn\" onClick={onClose}>\r\n          <CloseIcon fontSize=\"small\" />\r\n        </button>\r\n      </div>\r\n          <div className=\"qadpt-chat-container\">\r\n        <div className=\"qadpt-messages\" ref={chatContainerRef} onScroll={handleScroll}>\r\n          {messages.map((message, index) => (\r\n            <div\r\n              key={index}\r\n              className={`qadpt-message ${message.isUser ? 'user-message' : 'ai-message'}`}\r\n            >\r\n              {!message.isUser && (\r\n                <div className=\"ai-avatar\">\r\n                  <span dangerouslySetInnerHTML={{ __html: airobot }} />\r\n                </div>\r\n              )}\r\n              <div className=\"message-content\">\r\n                <p>{message.text}</p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n          {isLoading && (\r\n            <div className=\"qadpt-chat-message ai-message\">\r\n              <div className=\"ai-avatar\">\r\n                <span dangerouslySetInnerHTML={{ __html: airobot }} />\r\n              </div>\r\n              <div className=\"message-content\">\r\n                <div className=\"typing-indicator\">\r\n                  <span></span>\r\n                  <span></span>\r\n                  <span></span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          <div ref={messagesEndRef} />\r\n\r\n          {showScrollButton && (\r\n            <button className=\"scroll-to-bottom\" onClick={scrollToBottom}>\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M12 16L6 10L7.4 8.6L12 13.2L16.6 8.6L18 10L12 16Z\" fill=\"currentColor\"/>\r\n              </svg>\r\n            </button>\r\n          )}\r\n          </div>\r\n          </div>\r\n\r\n      <div className=\"qadpt-input\">\r\n        {error && <div className=\"error-message\">{error}</div>}\r\n\r\n            <div className=\"input-with-icons\">\r\n            <div className='qadpt-txtcont'>\r\n\r\n            <textarea\r\n              ref={inputRef}\r\n              value={inputText}\r\n              onChange={handleInputChange}\r\n              onKeyDown={handleKeyDown}\r\n              placeholder=\"Enter your prompt....\"\r\n              disabled={isLoading}\r\n              rows={1}\r\n                />\r\n           </div>\r\n            <div className=\"input-icons\">\r\n              <div>\r\n              {/* <button\r\n                className={`icon-btn mic-btn ${isListening ? 'listening' : ''}`}\r\n                onClick={handleSpeechRecognition}\r\n                onMouseEnter={() => setIsMicHovered(true)}\r\n                onMouseLeave={() => setIsMicHovered(false)}\r\n                disabled={isLoading || !speechSupported}\r\n                title={speechSupported ? \"Click to speak\" : \"Speech recognition not supported in your browser\"}\r\n              >\r\n                {isListening ? (\r\n                  <div className=\"listening-indicator\">\r\n                    <span className=\"listening-dot\"></span>\r\n                    <span className=\"listening-dot\"></span>\r\n                    <span className=\"listening-dot\"></span>\r\n                  </div>\r\n                ) : (\r\n                  <span dangerouslySetInnerHTML={{ __html: isMicHovered ? micicon_hover : micicon }} />\r\n                )}\r\n              </button> */}\r\n              <button\r\n                className=\"icon-btn upload-btn\"\r\n                onMouseEnter={() => setIsUploadHovered(true)}\r\n                onMouseLeave={() => setIsUploadHovered(false)}\r\n              >\r\n                <span dangerouslySetInnerHTML={{ __html: isUploadHovered ? upload_hover : upload }} />\r\n                </button>\r\n                </div>\r\n              <button\r\n                className=\"icon-btn send-btn\"\r\n                onClick={() => handleCreateInteraction(inputText)}\r\n                disabled={isLoading || !inputText.trim()}\r\n              >\r\n                <span dangerouslySetInnerHTML={{ __html: send }} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          </div>\r\n\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ModernChatWindow;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AACtE,OAAO,wBAAwB;AAC/B,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,EAAE,EAAEC,OAAO,EAA0BC,MAAM,EAAEC,YAAY,EAAEC,IAAI,QAAQ,0BAA0B;AAC1G,OAAOC,SAAS,MAAM,2BAA2B;AACjD;AACA,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,sBAAsB,EAAEC,qBAAqB,EAAEC,4BAA4B,QAAQ,yCAAyC;AACrI,OAAOC,cAAc,MAAuB,yBAAyB;AAErE,OAAO,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsBrD;;AAEA,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,OAAO;EAACC,gBAAgB;EAAEC,WAAW;EAAEC,cAAc;EAAEC,iBAAiB;EAAEC,aAAa;EAAEC,kBAAkB;EAAEC,iBAAiB;EAAEC,oBAAoB;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EACrO;EACA,MAAM;IACRC,mBAAmB;IACnBC,uBAAuB;IACvBC,QAAQ;IACRC,YAAY;IACZC,cAAc;IACdC,cAAc;IACdC,mBAAmB;IACnBC,mBAAmB;IACnBC,eAAe;IACfC,YAAY;IACZC,kBAAkB;IACdC,0BAA0B;IAC1BC,eAAe;IACfC,qBAAqB;IACrBC,kBAAkB;IAClBC,aAAa;IACbC,uBAAuB;IACvBC,2BAA2B;IAC3BC,gCAAgC;IAChCC,mBAAmB;IACnBC;EACL,CAAC,GAAGrC,cAAc,CAAEsC,KAAkB,IAAKA,KAAK,CAAC;EAC9C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;EACtD,MAAM;IAAEwD;EAAU,CAAC,GAAGrD,UAAU,CAACC,cAAc,CAAC;EAChD,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAgB,CACpD;IACA6D,IAAI,EAAE,mHAAmH;IACzHC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACpB,CAAC,CACJ,CAAC;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACjD;EACA,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6E,eAAe,EAAEC,kBAAkB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM+E,cAAc,GAAG9E,MAAM,CAAiB,IAAI,CAAC;EACnD,MAAM+E,QAAQ,GAAG/E,MAAM,CAAsB,IAAI,CAAC;EAClD,MAAMgF,gBAAgB,GAAGhF,MAAM,CAAiB,IAAI,CAAC;EAErDC,SAAS,CAAC,MAAM;IACd,IAAI+E,gBAAgB,CAACC,OAAO,EAAE;MAC5B;MACAD,gBAAgB,CAACC,OAAO,CAACC,SAAS,GAAGF,gBAAgB,CAACC,OAAO,CAACE,YAAY;IAC5E;IAEAV,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACf,QAAQ,EAAEM,SAAS,CAAC,CAAC;EAGzB,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIJ,gBAAgB,CAACC,OAAO,EAAE;MAC5B,MAAM;QAAEC,SAAS;QAAEC,YAAY;QAAEE;MAAa,CAAC,GAAGL,gBAAgB,CAACC,OAAO;;MAE1E;MACA,MAAMK,UAAU,GAAGH,YAAY,GAAGD,SAAS,GAAGG,YAAY,GAAG,EAAE;MAC/DZ,mBAAmB,CAAC,CAACa,UAAU,CAAC;IAClC;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIP,gBAAgB,CAACC,OAAO,EAAE;MAC5BD,gBAAgB,CAACC,OAAO,CAACC,SAAS,GAAGF,gBAAgB,CAACC,OAAO,CAACE,YAAY;MAC1EV,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACAxE,SAAS,CAAC,MAAM;IAAA,IAAAuF,iBAAA;IACd,CAAAA,iBAAA,GAAAT,QAAQ,CAACE,OAAO,cAAAO,iBAAA,uBAAhBA,iBAAA,CAAkBC,KAAK,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxF,SAAS,CAAC,MAAM;IACd4E,kBAAkB,CAAChE,4BAA4B,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6E,iBAAiB,GAAIC,CAAyC,IAAK;IACvElC,YAAY,CAACkC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC5B1B,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIY,QAAQ,CAACE,OAAO,EAAE;MACpBF,QAAQ,CAACE,OAAO,CAACa,KAAK,CAACC,MAAM,GAAG,MAAM,CAAC,CAAC;MACxChB,QAAQ,CAACE,OAAO,CAACa,KAAK,CAACC,MAAM,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAClB,QAAQ,CAACE,OAAO,CAACE,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IACtF;EACF,CAAC;EACDlF,SAAS,CAAC,MAAM;IACd,IAAI8E,QAAQ,CAACE,OAAO,IAAIzB,SAAS,KAAK,EAAE,EAAE;MACxCuB,QAAQ,CAACE,OAAO,CAACa,KAAK,CAACC,MAAM,GAAG,MAAM;IACxC;EACF,CAAC,EAAE,CAACvC,SAAS,CAAC,CAAC;EAGf,MAAM0C,aAAa,GAAIP,CAAsB,IAAK;IAChD,IAAIA,CAAC,CAACQ,GAAG,KAAK,OAAO,IAAI,CAACR,CAAC,CAACS,QAAQ,EAAE;MACpCT,CAAC,CAACU,cAAc,CAAC,CAAC;MAClBC,uBAAuB,CAAC9C,SAAS,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAM+C,aAAa,GAAGA,CAAA,KAAe;IACnC,OAAOC,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EAClD,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIjC,WAAW,EAAE;MACf9D,qBAAqB,CAAC,CAAC;MACvB+D,cAAc,CAAC,KAAK,CAAC;MACrB;IACF;;IAEA;IACA,MAAMiC,WAAW,GAAGpD,SAAS,CAACqD,IAAI,CAAC,CAAC;;IAEpC;IACA,IAAIN,aAAa,CAAC,CAAC,IAAI,CAAC3B,eAAe,EAAE;MACvCT,QAAQ,CAAC,+FAA+F,CAAC;MACzG2C,UAAU,CAAC,MAAM3C,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IACxC;IAEAQ,cAAc,CAAC,IAAI,CAAC;IACpBhE,sBAAsB,CAAC;MACrBoG,aAAa,EAAE,IAAI;MAAE;MACrBC,OAAO,EAAEA,CAAA,KAAM;QACbrC,cAAc,CAAC,IAAI,CAAC;QACpBR,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB,CAAC;MACD8C,QAAQ,EAAEA,CAACrD,IAAY,EAAEsD,OAAgB,KAAK;QAC5C;QACA;QACA;QACA,IAAIN,WAAW,EAAE;UACfnD,YAAY,CAACmD,WAAW,GAAG,GAAG,GAAGhD,IAAI,CAAC;QACxC,CAAC,MAAM;UACLH,YAAY,CAACG,IAAI,CAAC;QACpB;MACF,CAAC;MACDuD,KAAK,EAAEA,CAAA,KAAM;QACXxC,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;MACDyC,OAAO,EAAGlD,KAAU,IAAK;QACvBmD,OAAO,CAACnD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDS,cAAc,CAAC,KAAK,CAAC;;QAErB;QACA,IAAI4B,aAAa,CAAC,CAAC,EAAE;UACnB,IAAIrC,KAAK,KAAK,aAAa,EAAE;YAC3BC,QAAQ,CAAC,oFAAoF,CAAC;UAChG,CAAC,MAAM,IAAID,KAAK,KAAK,SAAS,EAAE;YAC9BC,QAAQ,CAAC,wGAAwG,CAAC;UACpH,CAAC,MAAM;YACLA,QAAQ,CAAC,sCAAsCD,KAAK,wCAAwC,CAAC;UAC/F;QACF,CAAC,MAAM;UACLC,QAAQ,CAAC,8BAA8BD,KAAK,qBAAqB,CAAC;QACpE;;QAEA;QACA4C,UAAU,CAAC,MAAM3C,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;MACxC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEF;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACE;EACA,MAAMmD,oBAAoB,GAAIC,MAAc,IAAc;IACxD,MAAMC,YAAY,GAAG,CACnB,uCAAuC,EACvC,iCAAiC,EACjC,+CAA+C,EAC/C,oCAAoC,EACpC,oBAAoB,EACpB,sBAAsB,EACtB,8BAA8B,EAC9B,gCAAgC,CACjC;IAED,OAAOA,YAAY,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAACJ,MAAM,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMK,cAAc,GAAIL,MAAc,IAAuI;IAC3K,MAAMM,KAAuI,GAAG,EAAE;;IAElJ;IACA;IACA,MAAMC,gBAAgB,GAAG,uGAAuG;IAChI;IACA,MAAMC,gBAAgB,GAAG,6DAA6D;IAEtF,IAAIC,KAAK;;IAET;IACA,OAAO,CAACA,KAAK,GAAGF,gBAAgB,CAACG,IAAI,CAACV,MAAM,CAAC,MAAM,IAAI,EAAE;MACvD,MAAMW,UAAU,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MACrC,IAAII,QAAQ,GAAGJ,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC;;MAE9B;MACA,IAAIuB,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC/FF,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAClDF,QAAQ,GAAG,QAAQ;MACrB,CAAC,MAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAClDF,QAAQ,GAAG,SAAS;MACtB,CAAC,MAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACjDF,QAAQ,GAAG,SAAS;MACtB,CAAC,MAAM;QACL;QACAA,QAAQ,GAAG,cAAc;MAC3B;;MAEA;MACAA,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC;MAE/DZ,KAAK,CAACa,IAAI,CAAC;QACTN,QAAQ,EAAEA,QAAQ;QAClBO,KAAK,EAAE,QAAQT,UAAU,EAAE;QAC3BU,OAAO,EAAE,WAAWV,UAAU;MAChC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIL,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,CAACb,KAAK,GAAGD,gBAAgB,CAACE,IAAI,CAACV,MAAM,CAAC,MAAM,IAAI,EAAE;QACvD,MAAMW,UAAU,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;QACrC,IAAII,QAAQ,GAAGJ,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC;;QAE9B;QACA,IAAIuB,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAIF,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;UAC/FF,QAAQ,GAAG,cAAc;QAC3B,CAAC,MAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAClDF,QAAQ,GAAG,QAAQ;QACrB,CAAC,MAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAClDF,QAAQ,GAAG,SAAS;QACtB,CAAC,MAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;UACjDF,QAAQ,GAAG,SAAS;QACtB,CAAC,MAAM;UACL;UACAA,QAAQ,GAAG,cAAc;QAC3B;;QAEA;QACAA,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC;QAE/DZ,KAAK,CAACa,IAAI,CAAC;UACTN,QAAQ,EAAEA,QAAQ;UAClBO,KAAK,EAAE,QAAQT,UAAU,EAAE;UAC3BU,OAAO,EAAE,WAAWV,UAAU;QAChC,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIL,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;MACtB,MAAMC,mBAAmB,GAAG,gGAAgG;MAE5H,OAAO,CAACd,KAAK,GAAGc,mBAAmB,CAACb,IAAI,CAACV,MAAM,CAAC,MAAM,IAAI,EAAE;QAC1DM,KAAK,CAACa,IAAI,CAAC;UACTN,QAAQ,EAAEJ,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGR,KAAK,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,CAAC;UAC9DE,KAAK,EAAEX,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC;UACtB+B,OAAO,EAAE,MAAMZ,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,MAAMkC,aAAa,GAAG,yLAAyL;IAC/M,IAAIC,WAAW;IACf,OAAO,CAACA,WAAW,GAAGD,aAAa,CAACd,IAAI,CAACV,MAAM,CAAC,MAAM,IAAI,EAAE;MAC1D,MAAMW,UAAU,GAAGC,QAAQ,CAACa,WAAW,CAAC,CAAC,CAAC,CAAC;MAC3C,MAAMC,UAAU,GAAGD,WAAW,CAAC,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC;MACxC,MAAMqC,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC;MACxD,MAAMc,iBAAiB,GAAGH,WAAW,CAAC,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC;;MAE/C;MACA,MAAMuC,SAAS,GAAGvB,KAAK,CAACwB,SAAS,CAACC,IAAI,IAAI;QACxC,MAAMC,SAAS,GAAGD,IAAI,CAACX,KAAK,IAAI,EAAE;QAClC,OAAOY,SAAS,CAACjB,QAAQ,CAAC,GAAGJ,UAAU,EAAE,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAIkB,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB;QACAvB,KAAK,CAACuB,SAAS,CAAC,CAACH,UAAU,GAAGA,UAAU;QACxCpB,KAAK,CAACuB,SAAS,CAAC,CAACF,YAAY,GAAGA,YAAY;QAC5CrB,KAAK,CAACuB,SAAS,CAAC,CAACD,iBAAiB,GAAGA,iBAAiB;MACxD;IACF;;IAEA;IACA,MAAMK,WAAW,GAAG,kFAAkF;IACtG,IAAIC,SAAS;IACb,OAAO,CAACA,SAAS,GAAGD,WAAW,CAACvB,IAAI,CAACV,MAAM,CAAC,MAAM,IAAI,EAAE;MACtD,MAAMW,UAAU,GAAGC,QAAQ,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC;MACzC,MAAMC,WAAW,GAAGD,SAAS,CAAC,CAAC,CAAC,CAAC5C,IAAI,CAAC,CAAC;;MAEvC;MACA,MAAMuC,SAAS,GAAGvB,KAAK,CAACwB,SAAS,CAACC,IAAI,IAAI;QACxC,MAAMC,SAAS,GAAGD,IAAI,CAACX,KAAK,IAAI,EAAE;QAClC,OAAOY,SAAS,CAACjB,QAAQ,CAAC,GAAGJ,UAAU,EAAE,CAAC;MAC5C,CAAC,CAAC;MAEF,IAAIkB,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB;QACAvB,KAAK,CAACuB,SAAS,CAAC,CAACR,OAAO,GAAG,MAAMc,WAAW,MAAM;MACpD;IACF;;IAEA;IACA,IAAI7B,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;MACtB;MACA,MAAMc,gBAAgB,GAAG,kEAAkE;MAC3F,MAAMC,SAAmB,GAAG,EAAE;MAE9B,OAAO,CAAC5B,KAAK,GAAG2B,gBAAgB,CAAC1B,IAAI,CAACV,MAAM,CAAC,MAAM,IAAI,EAAE;QACvDqC,SAAS,CAAClB,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGR,KAAK,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC;MACtE;MAEA,IAAImB,SAAS,CAACf,MAAM,GAAG,CAAC,EAAE;QACxBe,SAAS,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UACjClC,KAAK,CAACa,IAAI,CAAC;YACTN,QAAQ,EAAE0B,IAAI;YACdnB,KAAK,EAAE,QAAQoB,KAAK,GAAG,CAAC,EAAE;YAC1BnB,OAAO,EAAE,WAAWmB,KAAK,GAAG,CAAC;UAC/B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAlC,KAAK,CAACa,IAAI,CACR;UAAEN,QAAQ,EAAE,cAAc;UAAEO,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAe,CAAC,EACtE;UAAER,QAAQ,EAAE,QAAQ;UAAEO,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAuB,CAAC,EACxE;UAAER,QAAQ,EAAE,cAAc;UAAEO,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAgB,CACxE,CAAC;MACH;IACF;;IAEA;IACAf,KAAK,CAACmC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACnB,MAAMC,IAAI,GAAGF,CAAC,CAACtB,KAAK,GAAGR,QAAQ,CAAC8B,CAAC,CAACtB,KAAK,CAACyB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;MAC/D,MAAMC,IAAI,GAAGH,CAAC,CAACvB,KAAK,GAAGR,QAAQ,CAAC+B,CAAC,CAACvB,KAAK,CAACyB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;MAC/D,OAAOD,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IAEF,OAAOxC,KAAK;EACd,CAAC;EAGD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAGxK,QAAQ,CAAM,CAAC;EAC7C,MAAK,CAACyK,WAAW,EAACC,cAAc,CAAC,GAAC1K,QAAQ,CAAM,CAAC;EACjDE,SAAS,CAAC,MACV;IACIwC,kBAAkB,CAAC,KAAK,CAAC;EAE/B,CAAC,EAAC,EAAE,CAAC;EACH,MAAM6D,uBAAuB,GAAG,MAAOT,KAAa,IAAK;IACvD3C,mBAAmB,CAAC,IAAI,CAAC;IACzBiB,QAAQ,CAAC,IAAI,CAAC;IACdxB,eAAe,CAAC,IAAI,CAAC;IACrBC,qBAAqB,CAAC,KAAK,CAAC;IAC5B,IAAIiD,KAAK,CAACgD,MAAM,GAAG,EAAE,EAAE;MACnB1E,QAAQ,CAAC,0CAA0C,CAAC;IACxD,CAAC,MAAM;MACH;MACA,MAAMuG,WAAwB,GAAG;QAC/B9G,IAAI,EAAEiC,KAAK;QACXhC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDJ,WAAW,CAACgH,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,WAAW,CAAC,CAAC;MAC3CjH,YAAY,CAAC,EAAE,CAAC;MAChBQ,YAAY,CAAC,IAAI,CAAC;MAClB,IAAI;QAAA,IAAA2G,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA;QACF;;QAGA,MAAMC,UAAuB,GAAG;UAC9BnH,IAAI,EAAE,8BAA8B;UACpCC,MAAM,EAAE,KAAK;UACbC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDJ,WAAW,CAACgH,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEI,UAAU,CAAC,CAAC;QAE1C,MAAMC,IAAI,GAAG,MAAMtK,iBAAiB,CAACmF,KAAK,EAAEtC,SAAS,EAAE0H,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;QAE5E3J,iBAAiB,CAACwJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,CAAC;QAChCJ,IAAI,CAACK,SAAS,GAAGJ,MAAM,CAACC,QAAQ,CAACC,IAAI;QACrC,IAAG,CAAAH,IAAI,aAAJA,IAAI,wBAAAJ,gBAAA,GAAJI,IAAI,CAAEM,SAAS,CAAC,CAAC,CAAC,cAAAV,gBAAA,uBAAlBA,gBAAA,CAAoBW,QAAQ,MAAK,QAAQ,IAAI,CAAAP,IAAI,aAAJA,IAAI,wBAAAH,iBAAA,GAAJG,IAAI,CAAEM,SAAS,CAAC,CAAC,CAAC,cAAAT,iBAAA,wBAAAC,qBAAA,GAAlBD,iBAAA,CAAoBW,aAAa,cAAAV,qBAAA,uBAAjCA,qBAAA,CAAmCjC,MAAM,IAAC,CAAC,EAAC;UAC1F9F,uBAAuB,CAAC,IAAI,CAAC;QAC/B;QACAF,kBAAkB,CAACmI,IAAI,CAAC;QACxB7H,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QACvB9B,gBAAgB,GAAG2J,IAAI;QAEvBlI,aAAa,CAAC,CAAAkI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,SAAS,KAAI,EAAE,CAAC;QACpC7H,YAAY,CAAC,EAAE,CAAC;QAEhB,IAAIrC,OAAO,EAAEA,OAAO,CAAC,CAAC;QAEtB,IAAI4J,IAAI,EAAE;UACRT,UAAU,CAACS,IAAI,CAAC;UAChB,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,MAAK,cAAc,EAAE;YACpD9G,cAAc,CAAC,IAAI,CAAC;YACpBC,iBAAiB,CAACwJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,CAAC;YAChCrJ,mBAAmB,CAAC,cAAc,CAAC;YACnCI,cAAc,CAAC,KAAK,CAAC;YACrBD,YAAY,CAAC8I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,CAAC;YACxB,IAAIpK,WAAW,EAAEA,WAAW,CAAC;cAAEwI,IAAI,EAAE;YAAe,CAAC,CAAC;YACtDzH,mBAAmB,CAAC,CAAC;;YAErB;YACAyE,UAAU,CAAC,MAAM;cACf7D,gCAAgC,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3C,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MACI,IAAI,CAAA+H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,MAAK,QAAQ,EAAE;YACnDtG,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC;YACnCI,cAAc,CAAC,IAAI,CAAC;YACpBZ,cAAc,CAAC,KAAK,CAAC;YACrBC,iBAAiB,CAACwJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,CAAC;YAChClJ,YAAY,CAAC8I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,CAAC;YACxB,IAAIpK,WAAW,EAAEA,WAAW,CAAC;cAAEwI,IAAI,EAAE;YAAS,CAAC,CAAC;YAChD;YACAzH,mBAAmB,CAAC,CAAC;UACvB,CAAC,MACI,IAAI,CAAA2I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,MAAK,SAAS,EAAE;YACpD5F,kBAAkB,CAAC,KAAK,CAAC;YACzBV,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC;YACpCR,cAAc,CAAC,KAAK,CAAC;YACrBW,YAAY,CAAC8I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,CAAC;YACxBjK,aAAa,CAAC,IAAI,CAAC;YACnBC,kBAAkB,CAAC,KAAK,CAAC;YACzBC,iBAAiB,CAAC,IAAI,CAAC;YACvBC,oBAAoB,CAAC,KAAK,CAAC;YAC3B,IAAIN,WAAW,EAAEA,WAAW,CAAC;cAAEwI,IAAI,EAAE;YAAU,CAAC,CAAC;YACjD;YACAxH,mBAAmB,CAAC,CAAC;UAEvB,CAAC,MACI,IAAI,CAAA0I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,MAAK,SAAS,EAAE;YACpD5F,kBAAkB,CAAC,KAAK,CAAC;YACzBV,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC;YACpCR,cAAc,CAAC,KAAK,CAAC;YACrBW,YAAY,CAAC8I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,CAAC;YACxBjK,aAAa,CAAC,IAAI,CAAC;YACnBC,kBAAkB,CAAC,KAAK,CAAC;YACzBC,iBAAiB,CAAC,IAAI,CAAC;YACvBC,oBAAoB,CAAC,KAAK,CAAC;YAC3B,IAAIN,WAAW,EAAEA,WAAW,CAAC;cAAEwI,IAAI,EAAE;YAAU,CAAC,CAAC;YACjD;YACAzH,mBAAmB,CAAC,CAAC;YACrBE,eAAe,CAACC,YAAY,GAAG,CAAC,CAAC;UAEnC,CAAC,MACI,IAAI,CAAAwI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,MAAK,MAAM,EAAE;YACjDtG,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;YACjCP,iBAAiB,CAACwJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,CAAC;YAChClJ,YAAY,CAAC8I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,CAAC;YACxBnK,cAAc,CAAC,IAAI,CAAC;YACpBY,cAAc,CAAC,KAAK,CAAC;YACrBV,aAAa,CAAC,IAAI,CAAC;YACnBC,kBAAkB,CAAC,IAAI,CAAC;YACxBC,iBAAiB,CAAC,KAAK,CAAC;YACxBC,oBAAoB,CAAC,KAAK,CAAC;;YAE3B;YACA,IAAIoJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,SAAS,IAAIN,IAAI,CAACM,SAAS,CAACzC,MAAM,GAAG,CAAC,EAAE;cAChD,MAAM8C,SAAS,GAAGX,IAAI,CAACM,SAAS,CAACM,GAAG,CAAC,CAACtC,IAAS,EAAES,KAAa,MAAM;gBAClE8B,EAAE,EAAEvC,IAAI,CAACwC,MAAM;gBACfC,IAAI,EAAEzC,IAAI,CAAC0C,SAAS,IAAI,QAAQjC,KAAK,GAAG,CAAC,EAAE;gBAC3C3B,QAAQ,EAAEkB,IAAI,CAACiC,QAAQ;gBACvBU,SAAS,EAAElC,KAAK,GAAG;cACrB,CAAC,CAAC,CAAC;cACH9H,QAAQ,CAAC0J,SAAS,CAAC;;cAEnB;cACA,IAAIA,SAAS,CAAC9C,MAAM,GAAG,CAAC,EAAE;gBACxBzG,cAAc,CAAC,CAAC,CAAC;;gBAEjB;gBACA,MAAM8J,SAAS,GAAGlB,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC;gBACnC,IAAIY,SAAS,IAAIA,SAAS,CAACX,QAAQ,EAAE;kBACnC,MAAMnD,QAAQ,GAAG8D,SAAS,CAACX,QAAQ;kBACnCd,cAAc,CAACrC,QAAQ,CAAC;;kBAExB;kBACApG,uBAAuB,CAACoG,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;;kBAEzC;kBACA,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,cAAc,EAAE;oBAC7ChG,mBAAmB,CAAC,CAAC;oBACrBL,uBAAuB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC/CD,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;;oBAEjC;oBACA+E,UAAU,CAAC,MAAM;sBACf7D,gCAAgC,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC3C,CAAC,EAAE,GAAG,CAAC;oBAEP,IAAI3B,WAAW,EAAEA,WAAW,CAAC;sBAAEwI,IAAI,EAAE;oBAAe,CAAC,CAAC;kBACxD,CAAC,MACI,IAAI1B,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;oBAC5ChG,mBAAmB,CAAC,CAAC;oBACrBN,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;oBACjCC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;oBACzC,IAAIV,WAAW,EAAEA,WAAW,CAAC;sBAAEwI,IAAI,EAAE;oBAAS,CAAC,CAAC;oBAChD3H,cAAc,CAAC,IAAI,CAAC;kBACtB,CAAC,MACI,IAAIiG,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,EAAE;oBAC7C5F,kBAAkB,CAAC,KAAK,CAAC;oBAEzBJ,mBAAmB,CAAC,CAAC;oBACrBL,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;oBAC1CD,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;;oBAEjC;oBACA+E,UAAU,CAAC,MAAM;sBACf9D,2BAA2B,CAAC,CAAC;oBAC/B,CAAC,EAAE,GAAG,CAAC;oBAEP,IAAI1B,WAAW,EAAEA,WAAW,CAAC;sBAAEwI,IAAI,EAAE;oBAAU,CAAC,CAAC;oBACjDvH,eAAe,CAACC,YAAY,GAAG,CAAC,CAAC;kBACnC,CAAC,MACI,IAAI4F,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,EAAE;oBAC7C5F,kBAAkB,CAAC,KAAK,CAAC;oBACzBH,mBAAmB,CAAC,CAAC;oBACrBN,uBAAuB,CAAC,SAAS,CAAC;oBAClCD,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC;oBACjCW,0BAA0B,CAAC,IAAI,CAAC;oBAChC,IAAIpB,WAAW,EAAEA,WAAW,CAAC;sBAAEwI,IAAI,EAAE;oBAAU,CAAC,CAAC;oBACjDvH,eAAe,CAACC,YAAY,GAAG,CAAC,CAAC;kBACnC;gBACF;cACF;YACF;UACF,CAAC,MACI,IAAI,CAAAwI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,MAAK,WAAW,EAAE;YACtD;YACAtG,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC;YACtCP,iBAAiB,CAACwJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,CAAC;YAChClJ,YAAY,CAAC8I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,CAAC;YACxB,IAAIpK,WAAW,EAAEA,WAAW,CAAC;cAAEwI,IAAI,EAAE;YAAY,CAAC,CAAC;UACrD;QACF;MAEF,CAAC,CAAC,OAAO5F,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,MAAMiI,YAAyB,GAAG;UAChCvI,IAAI,EAAE,8DAA8D;UACpEC,MAAM,EAAE,KAAK;UACbC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDJ,WAAW,CAACgH,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEwB,YAAY,CAAC,CAAC;MAC9C,CAAC,SAAS;QACRlI,YAAY,CAAC,KAAK,CAAC;QACnBE,QAAQ,CAAC,IAAI,CAAC;MAChB;IACJ;EACJ,CAAC;EACC,oBACEnD,OAAA,CAAAE,SAAA;IAAAkL,QAAA,eACApL,OAAA;MAAKqL,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAChCpL,OAAA;QAAKqL,SAAS,EAAC,mBAAmB;QAAAD,QAAA,gBAChCpL,OAAA;UAAKqL,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBAC/BpL,OAAA;YAAMqL,SAAS,EAAC,iBAAiB;YAACC,uBAAuB,EAAE;cAAEC,MAAM,EAAEnM;YAAG;UAAE;YAAAoM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7E3L,OAAA;YAAAoL,QAAA,EAAM;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACN3L,OAAA;UAAQqL,SAAS,EAAC,iBAAiB;UAACO,OAAO,EAAExL,OAAQ;UAAAgL,QAAA,eACnDpL,OAAA,CAACP,SAAS;YAACoM,QAAQ,EAAC;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACF3L,OAAA;QAAKqL,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACvCpL,OAAA;UAAKqL,SAAS,EAAC,gBAAgB;UAACS,GAAG,EAAE9H,gBAAiB;UAAC+H,QAAQ,EAAE3H,YAAa;UAAAgH,QAAA,GAC3E1I,QAAQ,CAACkI,GAAG,CAAC,CAACoB,OAAO,EAAEjD,KAAK,kBAC3B/I,OAAA;YAEEqL,SAAS,EAAE,iBAAiBW,OAAO,CAACnJ,MAAM,GAAG,cAAc,GAAG,YAAY,EAAG;YAAAuI,QAAA,GAE5E,CAACY,OAAO,CAACnJ,MAAM,iBACd7C,OAAA;cAAKqL,SAAS,EAAC,WAAW;cAAAD,QAAA,eACxBpL,OAAA;gBAAMsL,uBAAuB,EAAE;kBAAEC,MAAM,EAAElM;gBAAQ;cAAE;gBAAAmM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,eACD3L,OAAA;cAAKqL,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC9BpL,OAAA;gBAAAoL,QAAA,EAAIY,OAAO,CAACpJ;cAAI;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA,GAVD5C,KAAK;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWP,CACN,CAAC,EACD3I,SAAS,iBACRhD,OAAA;YAAKqL,SAAS,EAAC,+BAA+B;YAAAD,QAAA,gBAC5CpL,OAAA;cAAKqL,SAAS,EAAC,WAAW;cAAAD,QAAA,eACxBpL,OAAA;gBAAMsL,uBAAuB,EAAE;kBAAEC,MAAM,EAAElM;gBAAQ;cAAE;gBAAAmM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN3L,OAAA;cAAKqL,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC9BpL,OAAA;gBAAKqL,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BpL,OAAA;kBAAAwL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3L,OAAA;kBAAAwL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3L,OAAA;kBAAAwL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACD3L,OAAA;YAAK8L,GAAG,EAAEhI;UAAe;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAE3BnI,gBAAgB,iBACfxD,OAAA;YAAQqL,SAAS,EAAC,kBAAkB;YAACO,OAAO,EAAErH,cAAe;YAAA6G,QAAA,eAC3DpL,OAAA;cAAKiM,KAAK,EAAC,IAAI;cAAClH,MAAM,EAAC,IAAI;cAACmH,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,KAAK,EAAC,4BAA4B;cAAAhB,QAAA,eAC5FpL,OAAA;gBAAMqM,CAAC,EAAC,mDAAmD;gBAACF,IAAI,EAAC;cAAc;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEV3L,OAAA;QAAKqL,SAAS,EAAC,aAAa;QAAAD,QAAA,GACzBlI,KAAK,iBAAIlD,OAAA;UAAKqL,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAElI;QAAK;UAAAsI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElD3L,OAAA;UAAKqL,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBACjCpL,OAAA;YAAKqL,SAAS,EAAC,eAAe;YAAAD,QAAA,eAE9BpL,OAAA;cACE8L,GAAG,EAAE/H,QAAS;cACdc,KAAK,EAAErC,SAAU;cACjB8J,QAAQ,EAAE5H,iBAAkB;cAC5B6H,SAAS,EAAErH,aAAc;cACzBsH,WAAW,EAAC,uBAAuB;cACnCC,QAAQ,EAAEzJ,SAAU;cACpB0J,IAAI,EAAE;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL3L,OAAA;YAAKqL,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAC1BpL,OAAA;cAAAoL,QAAA,eAmBApL,OAAA;gBACEqL,SAAS,EAAC,qBAAqB;gBAC/BsB,YAAY,EAAEA,CAAA,KAAMpJ,kBAAkB,CAAC,IAAI,CAAE;gBAC7CqJ,YAAY,EAAEA,CAAA,KAAMrJ,kBAAkB,CAAC,KAAK,CAAE;gBAAA6H,QAAA,eAE9CpL,OAAA;kBAAMsL,uBAAuB,EAAE;oBAAEC,MAAM,EAAEjI,eAAe,GAAG/D,YAAY,GAAGD;kBAAO;gBAAE;kBAAAkM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACR3L,OAAA;cACEqL,SAAS,EAAC,mBAAmB;cAC7BO,OAAO,EAAEA,CAAA,KAAMtG,uBAAuB,CAAC9C,SAAS,CAAE;cAClDiK,QAAQ,EAAEzJ,SAAS,IAAI,CAACR,SAAS,CAACqD,IAAI,CAAC,CAAE;cAAAuF,QAAA,eAEzCpL,OAAA;gBAAMsL,uBAAuB,EAAE;kBAAEC,MAAM,EAAE/L;gBAAK;cAAE;gBAAAgM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAED,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP;EAAC,gBACJ,CAAC;AAEP,CAAC;AAAC7K,EAAA,CAvuBIX,gBAAiD;EAAA,QAwBlDL,cAAc;AAAA;AAAA+M,EAAA,GAxBb1M,gBAAiD;AAyuBvD,eAAeA,gBAAgB;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}