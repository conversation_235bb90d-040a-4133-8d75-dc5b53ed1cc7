{"ast": null, "code": "import React from\"react\";import{<PERSON>,<PERSON>po<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,Toolt<PERSON>}from\"@mui/material\";import{deletestep,editpricol}from\"../../assets/icons/icons\";import{useTranslation}from'react-i18next';// Function to modify the color of an SVG icon\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const modifySVGColor=(base64SVG,color)=>{if(!base64SVG){return\"\";}try{// Check if the string is a valid base64 SVG\nif(!base64SVG.includes(\"data:image/svg+xml;base64,\")){return base64SVG;// Return the original if it's not an SVG\n}const decodedSVG=atob(base64SVG.split(\",\")[1]);// Check if this is primarily a stroke-based or fill-based icon\nconst hasStroke=decodedSVG.includes('stroke=\"');const hasColoredFill=/fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);let modifiedSVG=decodedSVG;if(hasStroke&&!hasColoredFill){// This is a stroke-based icon (like chkicn2-6) - only change stroke color\nmodifiedSVG=modifiedSVG.replace(/stroke=\"[^\"]+\"/g,`stroke=\"${color}\"`);}else if(hasColoredFill){// This is a fill-based icon (like chkicn1) - only change fill color\nmodifiedSVG=modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g,`fill=\"${color}\"`);}else{// No existing fill or stroke, add fill to make it visible\nmodifiedSVG=modifiedSVG.replace(/<path(?![^>]*fill=)/g,`<path fill=\"${color}\"`);modifiedSVG=modifiedSVG.replace(/<svg(?![^>]*fill=)/g,`<svg fill=\"${color}\"`);}const modifiedBase64=`data:image/svg+xml;base64,${btoa(modifiedSVG)}`;return modifiedBase64;}catch(error){console.error(\"Error modifying SVG color:\",error);return base64SVG;// Return the original if there's an error\n}};const DraggableCheckpoint=_ref=>{let{checkpoint,index,handleEditClick,handleDeleteClick,handleDragStart,handleDragOver,handleDrop,isDragging}=_ref;const{t:translate}=useTranslation();return/*#__PURE__*/_jsx(Box,{draggable:true,onDragStart:()=>handleDragStart(index),onDragOver:handleDragOver,onDrop:()=>handleDrop(index),className:\"qadpt-control-box\",sx:{backgroundColor:\"#E5DADA !important\",height:\"auto !important\",cursor:\"grab\",padding:\"0 !important\",\"&:hover\":{backgroundColor:\"#D9CACA\"}},children:/*#__PURE__*/_jsxs(\"div\",{style:{width:\"100%\",textOverflow:\"ellipsis\",overflow:\"hidden\",display:\"block !important\",whiteSpace:\"nowrap\",textAlign:\"left\",fontWeight:\"bold\"},className:\"qadpt-drag\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",gap:\"10px\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{textOverflow:\"ellipsis\",overflow:\"hidden\",display:\"block !important\",whiteSpace:\"nowrap\",fontWeight:\"bold\"},children:checkpoint.title}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate('Edit',{defaultValue:'Edit'}),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleEditClick(checkpoint===null||checkpoint===void 0?void 0:checkpoint.id),sx:{padding:\"5px !important\"},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:editpricol},style:{zoom:1}})})}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate('Delete',{defaultValue:'Delete'}),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleDeleteClick(checkpoint===null||checkpoint===void 0?void 0:checkpoint.interaction),sx:{padding:\"5px !important\"},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deletestep},style:{zoom:1}})})})]}),checkpoint.description&&/*#__PURE__*/_jsx(Typography,{className:\"qadpt-desdrag\",sx:{padding:\"0 8px 8px 8px\",textOverflow:\"ellipsis\",overflow:\"hidden\",display:\"block !important\",whiteSpace:\"nowrap\"},children:checkpoint.description})]})},index);};export default DraggableCheckpoint;", "map": {"version": 3, "names": ["React", "Box", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "deletestep", "editpricol", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "modifySVGColor", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "btoa", "error", "console", "DraggableCheckpoint", "_ref", "checkpoint", "index", "handleEditClick", "handleDeleteClick", "handleDragStart", "handleDragOver", "handleDrop", "isDragging", "t", "translate", "draggable", "onDragStart", "onDragOver", "onDrop", "className", "sx", "backgroundColor", "height", "cursor", "padding", "children", "style", "width", "textOverflow", "overflow", "display", "whiteSpace", "textAlign", "fontWeight", "alignItems", "gap", "title", "arrow", "defaultValue", "onClick", "id", "dangerouslySetInnerHTML", "__html", "zoom", "interaction", "description"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/DraggableCheckpoint.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect, useContext, useRef } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n    InfoFilled,\r\n    QuestionFill,\r\n    Reselect,\r\n      Solid,\r\n      editicon,\r\n      deleteicon,\r\n      deletestep,\r\n      editpricol\r\n} from \"../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n// Function to modify the color of an SVG icon\r\nconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\tif (!base64SVG) {\r\n\t\treturn \"\";\r\n\t}\r\n\r\n\ttry {\r\n\t\t// Check if the string is a valid base64 SVG\r\n\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t}\r\n\r\n\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t} else if (hasColoredFill) {\r\n\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t} else {\r\n\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t}\r\n\r\n\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\treturn modifiedBase64;\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error modifying SVG color:\", error);\r\n\t\treturn base64SVG; // Return the original if there's an error\r\n\t}\r\n};\r\n  const DraggableCheckpoint = ({\r\n    checkpoint,\r\n    index,\r\n    handleEditClick,\r\n    handleDeleteClick,\r\n    handleDragStart,\r\n    handleDragOver,\r\n    handleDrop,\r\n    isDragging,\r\n  }: any) => {\r\n\t  const { t: translate } = useTranslation();\r\n\t  \r\n    return (\r\n\t\t\t<Box\r\n\t\t\t\tkey={index}\r\n\t\t\t\tdraggable\r\n\t\t\t\tonDragStart={() => handleDragStart(index)}\r\n\t\t\t\tonDragOver={handleDragOver}\r\n\t\t\t\tonDrop={() => handleDrop(index)}\r\n\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\theight: \"auto !important\",\r\n\t\t\t\t\tcursor: \"grab\",\r\n\t\t\t\t\tpadding: \"0 !important\",\r\n\t\t\t\t\t\"&:hover\": { backgroundColor: \"#D9CACA\" },\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\tdisplay: \"block !important\",\r\n\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tclassName=\"qadpt-drag\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* Title */}\r\n\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}>\r\n\t\t\t\t\t\t{/* {checkpoint.icon && (\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\tuseDrawerStore.getState().checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || \"#333\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={checkpoint.icon}\r\n\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"14px\", height: \"14px\", filter: \"brightness(0) invert(1)\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t)} */}\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\tdisplay: \"block !important\",\r\n\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{checkpoint.title}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle={translate('Edit', { defaultValue: 'Edit' })}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={() => handleEditClick(checkpoint?.id)}\r\n\t\t\t\t\t\t\t\tsx={{ padding: \"5px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: editpricol }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ zoom: 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t{/* Delete Button */}\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle={translate('Delete', { defaultValue: 'Delete' })}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={() => handleDeleteClick(checkpoint?.interaction)}\r\n\t\t\t\t\t\t\t\tsx={{ padding: \"5px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deletestep }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ zoom: 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t{/* Description */}\r\n\t\t\t\t\t{checkpoint.description && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tclassName=\"qadpt-desdrag\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tpadding: \"0 8px 8px 8px\",\r\n\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\tdisplay: \"block !important\",\r\n\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{checkpoint.description}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Edit Button */}\r\n\t\t\t</Box>\r\n\t\t);\r\n};\r\n\r\nexport default DraggableCheckpoint;\r\n\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAA8D,OAAO,CACjF,OAASC,GAAG,CAAEC,UAAU,CAAmBC,UAAU,CAAkHC,OAAO,KAAQ,eAAe,CAIrM,OAOMC,UAAU,CACVC,UAAU,KACT,0BAA0B,CACjC,OAASC,cAAc,KAAQ,eAAe,CAE9C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,cAAc,CAAGA,CAACC,SAAc,CAAEC,KAAU,GAAK,CACtD,GAAI,CAACD,SAAS,CAAE,CACf,MAAO,EAAE,CACV,CAEA,GAAI,CACH;AACA,GAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,CAAE,CACtD,MAAO,CAAAF,SAAS,CAAE;AACnB,CAEA,KAAM,CAAAG,UAAU,CAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAEhD;AACA,KAAM,CAAAC,SAAS,CAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC,CACjD,KAAM,CAAAK,cAAc,CAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC,CAE/D,GAAI,CAAAM,WAAW,CAAGN,UAAU,CAE5B,GAAIG,SAAS,EAAI,CAACC,cAAc,CAAE,CACjC;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,CAAE,WAAWT,KAAK,GAAG,CAAC,CAC1E,CAAC,IAAM,IAAIM,cAAc,CAAE,CAC1B;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,CAAE,SAAST,KAAK,GAAG,CAAC,CAC9E,CAAC,IAAM,CACN;AACAQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,CAAE,eAAeT,KAAK,GAAG,CAAC,CAClFQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,CAAE,cAAcT,KAAK,GAAG,CAAC,CACjF,CAEA,KAAM,CAAAU,cAAc,CAAG,6BAA6BC,IAAI,CAACH,WAAW,CAAC,EAAE,CACvE,MAAO,CAAAE,cAAc,CACtB,CAAE,MAAOE,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CAAAb,SAAS,CAAE;AACnB,CACD,CAAC,CACC,KAAM,CAAAe,mBAAmB,CAAGC,IAAA,EASjB,IATkB,CAC3BC,UAAU,CACVC,KAAK,CACLC,eAAe,CACfC,iBAAiB,CACjBC,eAAe,CACfC,cAAc,CACdC,UAAU,CACVC,UACG,CAAC,CAAAR,IAAA,CACL,KAAM,CAAES,CAAC,CAAEC,SAAU,CAAC,CAAGhC,cAAc,CAAC,CAAC,CAExC,mBACDE,IAAA,CAACR,GAAG,EAEHuC,SAAS,MACTC,WAAW,CAAEA,CAAA,GAAMP,eAAe,CAACH,KAAK,CAAE,CAC1CW,UAAU,CAAEP,cAAe,CAC3BQ,MAAM,CAAEA,CAAA,GAAMP,UAAU,CAACL,KAAK,CAAE,CAChCa,SAAS,CAAC,mBAAmB,CAC7BC,EAAE,CAAE,CACHC,eAAe,CAAE,oBAAoB,CACrCC,MAAM,CAAE,iBAAiB,CACzBC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,cAAc,CACvB,SAAS,CAAE,CAAEH,eAAe,CAAE,SAAU,CACzC,CAAE,CAAAI,QAAA,cAEFvC,KAAA,QACCwC,KAAK,CAAE,CACNC,KAAK,CAAE,MAAM,CACbC,YAAY,CAAE,UAAU,CACxBC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,kBAAkB,CAC3BC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,MAAM,CACjBC,UAAU,CAAE,MACb,CAAE,CACFd,SAAS,CAAC,YAAY,CAAAM,QAAA,eAGtBvC,KAAA,QAAKwC,KAAK,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEI,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,MAAO,CAAE,CAAAV,QAAA,eAqBlEzC,IAAA,CAACP,UAAU,EACV0C,SAAS,CAAC,qBAAqB,CAC/BC,EAAE,CAAE,CACHQ,YAAY,CAAE,UAAU,CACxBC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,kBAAkB,CAC3BC,UAAU,CAAE,QAAQ,CACpBE,UAAU,CAAE,MACb,CAAE,CAAAR,QAAA,CAEDpB,UAAU,CAAC+B,KAAK,CACN,CAAC,cACbpD,IAAA,CAACL,OAAO,EACP0D,KAAK,MACND,KAAK,CAAEtB,SAAS,CAAC,MAAM,CAAE,CAAEwB,YAAY,CAAE,MAAO,CAAC,CAAE,CAAAb,QAAA,cAElDzC,IAAA,CAACN,UAAU,EACV6D,OAAO,CAAEA,CAAA,GAAMhC,eAAe,CAACF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEmC,EAAE,CAAE,CAC/CpB,EAAE,CAAE,CAAEI,OAAO,CAAE,gBAAiB,CAAE,CAAAC,QAAA,cAElCzC,IAAA,SACCyD,uBAAuB,CAAE,CAAEC,MAAM,CAAE7D,UAAW,CAAE,CAChD6C,KAAK,CAAE,CAAEiB,IAAI,CAAE,CAAE,CAAE,CACnB,CAAC,CACS,CAAC,CACL,CAAC,cAGV3D,IAAA,CAACL,OAAO,EACP0D,KAAK,MACND,KAAK,CAAEtB,SAAS,CAAC,QAAQ,CAAE,CAAEwB,YAAY,CAAE,QAAS,CAAC,CAAE,CAAAb,QAAA,cAEtDzC,IAAA,CAACN,UAAU,EACV6D,OAAO,CAAEA,CAAA,GAAM/B,iBAAiB,CAACH,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEuC,WAAW,CAAE,CAC1DxB,EAAE,CAAE,CAAEI,OAAO,CAAE,gBAAiB,CAAE,CAAAC,QAAA,cAElCzC,IAAA,SACCyD,uBAAuB,CAAE,CAAEC,MAAM,CAAE9D,UAAW,CAAE,CAChD8C,KAAK,CAAE,CAAEiB,IAAI,CAAE,CAAE,CAAE,CACnB,CAAC,CACS,CAAC,CACL,CAAC,EACN,CAAC,CAGLtC,UAAU,CAACwC,WAAW,eACtB7D,IAAA,CAACP,UAAU,EACV0C,SAAS,CAAC,eAAe,CACzBC,EAAE,CAAE,CACHI,OAAO,CAAE,eAAe,CACxBI,YAAY,CAAE,UAAU,CACxBC,QAAQ,CAAE,QAAQ,CAClBC,OAAO,CAAE,kBAAkB,CAC3BC,UAAU,CAAE,QACb,CAAE,CAAAN,QAAA,CAEDpB,UAAU,CAACwC,WAAW,CACZ,CACZ,EACG,CAAC,EA3GDvC,KA8GD,CAAC,CAET,CAAC,CAED,cAAe,CAAAH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}