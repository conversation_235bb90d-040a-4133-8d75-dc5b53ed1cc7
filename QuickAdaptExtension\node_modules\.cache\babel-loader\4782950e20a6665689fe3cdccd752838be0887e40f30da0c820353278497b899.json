{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{<PERSON>,Typography,<PERSON>Field,IconButton,Button}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import useDrawerStore from\"../../store/drawerStore\";import{warning}from\"../../assets/icons/icons\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ChecklistCanvasSettings=_ref=>{let{zindeex,setZindeex,setShowChecklistCanvasSettings,selectedTemplate}=_ref;const{t:translate}=useTranslation();const{setCanvasSetting,borderColor,announcementJson,width,setWidth,backgroundColor,setBorderColor,setBackgroundColor,borderRadius,setBorderRadius,Annpad<PERSON>,setAnnPadding,AnnborderSize,setAnnBorderSize,Bposition,setBposition,checklistGuideMetaData,updateChecklistCanvas,setIsUnSavedChanges,isUnSavedChanges}=useDrawerStore(state=>state);const[isOpen,setIsOpen]=useState(true);// Error states for validation\nconst[heightError,setHeightError]=useState(false);const[widthError,setWidthError]=useState(false);const[cornerRadiusError,setCornerRadiusError]=useState(false);const[borderWidthError,setBorderWidthError]=useState(false);const[checklistCanvasProperties,setChecklistCanvasProperties]=useState(()=>{var _checklistGuideMetaDa;const initialchecklistCanvasProperties=((_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:_checklistGuideMetaDa.canvas)||{width:\"930\",height:\"450\",cornerRadius:\"12\",primaryColor:\"#5F9EA0\",borderColor:\"\",backgroundColor:\"#ffffff\",openByDefault:false,hideAfterCompletion:true,borderWidth:\"0\"};return initialchecklistCanvasProperties;});// State for tracking changes and apply button\nconst[isDisabled,setIsDisabled]=useState(true);const[hasChanges,setHasChanges]=useState(false);const[initialState,setInitialState]=useState(checklistCanvasProperties);// Function to check if the Apply button should be enabled\nconst updateApplyButtonState=function(changed){let hasErrors=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;setIsDisabled(!changed||hasErrors);};// Effect to check for any changes compared to initial state\nuseEffect(()=>{// Compare current properties with initial state\nconst hasAnyChanges=JSON.stringify(checklistCanvasProperties)!==JSON.stringify(initialState);setHasChanges(hasAnyChanges);// Check for validation errors\nconst hasValidationErrors=heightError||widthError||cornerRadiusError||borderWidthError;updateApplyButtonState(hasAnyChanges,hasValidationErrors);},[checklistCanvasProperties,initialState,heightError,widthError,cornerRadiusError,borderWidthError]);const handleBorderColorChange=e=>setBorderColor(e.target.value);const handleBackgroundColorChange=e=>setBackgroundColor(e.target.value);const handleClose=()=>{setIsOpen(false);setShowChecklistCanvasSettings(false);};const onPropertyChange=(key,value)=>{setChecklistCanvasProperties(prevState=>{const newState={...prevState,[key]:value};// Mark that changes have been made\nsetHasChanges(true);return newState;});};const handleApplyChanges=()=>{// Apply the changes - updateChecklistCanvas will handle recording the change for undo/redo\nupdateChecklistCanvas(checklistCanvasProperties);// Update the initial state to the current state after applying changes\nsetInitialState({...checklistCanvasProperties});// Reset the changes flag\nsetHasChanges(false);// Disable the Apply button\nsetIsDisabled(true);handleClose();setIsUnSavedChanges(true);};if(!isOpen)return null;return/*#__PURE__*/(//<Draggable>\n_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate('Canvas')}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Height')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:checklistCanvasProperties.height,size:\"small\",autoFocus:true,className:\"qadpt-control-input\",onChange:e=>{// Only allow numeric input\nconst value=e.target.value;if(value===''){onPropertyChange(\"height\",'0');setHeightError(false);return;}if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate height between 100px and 1000px\nif(inputValue<400||inputValue>600){setHeightError(true);}else{setHeightError(false);}onPropertyChange(\"height\",value);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:heightError})})]}),heightError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate('Value must be between 400px and 600px.')]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Width')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:checklistCanvasProperties.width,size:\"small\",autoFocus:true,className:\"qadpt-control-input\",onChange:e=>{// Only allow numeric input\nconst value=e.target.value;if(value===''){onPropertyChange(\"width\",'0');setWidthError(false);return;}if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate width between 300px and 1200px\nif(inputValue<300||inputValue>1200){setWidthError(true);}else{setWidthError(false);}onPropertyChange(\"width\",value);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:widthError})})]}),widthError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate('Value must be between 300px and 1200px.')]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Corner Radius')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:checklistCanvasProperties.cornerRadius,fullWidth:true,size:\"small\",className:\"qadpt-control-input\",onChange:e=>{// Only allow numeric input\nconst value=e.target.value;if(value===''){onPropertyChange(\"cornerRadius\",'0');setCornerRadiusError(false);return;}if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate corner radius between 0px and 50px\nif(inputValue<0||inputValue>50){setCornerRadiusError(true);}else{setCornerRadiusError(false);}onPropertyChange(\"cornerRadius\",value);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:cornerRadiusError})})]}),cornerRadiusError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate('Value must be between 0px and 50px.')]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Border Width')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:checklistCanvasProperties.borderWidth,fullWidth:true,size:\"small\",className:\"qadpt-control-input\",onChange:e=>{// Only allow numeric input\nconst value=e.target.value;if(value===''){onPropertyChange(\"borderWidth\",'0');setBorderWidthError(false);return;}if(!/^-?\\d*$/.test(value)){return;}const inputValue=parseInt(value)||0;// Validate border width between 0px and 20px\nif(inputValue<0||inputValue>20){setBorderWidthError(true);}else{setBorderWidthError(false);}onPropertyChange(\"borderWidth\",value);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:borderWidthError})})]}),borderWidthError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate('Value must be between 0px and 20px.')]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Primary Color')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistCanvasProperties.primaryColor,onChange:e=>onPropertyChange(\"primaryColor\",e.target.value),className:\"qadpt-color-input\",style:{backgroundColor:'#5F9EA0'}})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Background')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistCanvasProperties.backgroundColor,onChange:e=>onPropertyChange(\"backgroundColor\",e.target.value),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Border')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistCanvasProperties.borderColor,onChange:e=>onPropertyChange(\"borderColor\",e.target.value),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Open by Default')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:checklistCanvasProperties.openByDefault,onChange:e=>onPropertyChange(\"openByDefault\",e.target.checked),name:\"showByDefault\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Hide After Completion')}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:checklistCanvasProperties.hideAfterCompletion,onChange:e=>onPropertyChange(\"hideAfterCompletion\",e.target.checked),name:\"showByDefault\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn ${isDisabled?\"disabled\":\"\"}`,disabled:isDisabled,children:translate('Apply')})})]})})//</Draggable>\n);};export default ChecklistCanvasSettings;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "warning", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "ChecklistCanvasSettings", "_ref", "zindeex", "setZindeex", "setShowChecklistCanvasSettings", "selectedTemplate", "t", "translate", "setCanvasSetting", "borderColor", "announcement<PERSON><PERSON>", "width", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "setBorderColor", "setBackgroundColor", "borderRadius", "setBorderRadius", "Annpadding", "setAnnPadding", "AnnborderSize", "setAnnBorderSize", "Bposition", "setBposition", "checklistGuideMetaData", "updateChecklistCanvas", "setIsUnSavedChanges", "isUnSavedChanges", "state", "isOpen", "setIsOpen", "heightError", "setHeightError", "widthError", "setWidthError", "cornerRadiusError", "setCornerRadiusError", "borderWidthError", "setBorderWidthError", "checklistCanvasProperties", "setChecklistCanvasProperties", "_checklistGuideMetaDa", "initialchecklistCanvasProperties", "canvas", "height", "cornerRadius", "primaryColor", "openByDefault", "hideAfterCompletion", "borderWidth", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "initialState", "setInitialState", "updateApplyButtonState", "changed", "hasErrors", "arguments", "length", "undefined", "hasAnyChanges", "JSON", "stringify", "hasValidationErrors", "handleBorderColorChange", "e", "target", "value", "handleBackgroundColorChange", "handleClose", "onPropertyChange", "key", "prevState", "newState", "handleApplyChanges", "id", "className", "children", "onClick", "size", "variant", "autoFocus", "onChange", "test", "inputValue", "parseInt", "InputProps", "endAdornment", "sx", "border", "error", "style", "fontSize", "color", "textAlign", "top", "left", "marginBottom", "display", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "fullWidth", "type", "checked", "name", "disabled"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistCanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport RadioButtonUncheckedIcon from \"@mui/icons-material/RadioButtonUnchecked\";\r\nimport RadioButtonCheckedIcon from \"@mui/icons-material/RadioButtonChecked\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { defaultDots, topLeft, topCenter, topRight, middleLeft, middleCenter, middleRight, bottomLeft, bottomMiddle, bottomRight, topcenter, warning } from \"../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ChecklistCanvasSettings = ({ zindeex, setZindeex, setShowChecklistCanvasSettings, selectedTemplate }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetCanvasSetting,\r\n\t\tborderColor,\r\n\t\tannouncementJson,\r\n\t\twidth,\r\n\t\tsetWidth,\r\n\t\tbackgroundColor,\r\n\t\tsetBorderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tborderRadius,\r\n\t\tsetBorderRadius,\r\n\t\tAnnpadding,\r\n\t\tsetAnnPadding,\r\n\t\tAnnborderSize,\r\n\t\tsetAnnBorderSize,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tchecklistGuideMetaData,\r\n\t\tupdateChecklistCanvas,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tisUnSavedChanges,\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\r\n\t// Error states for validation\r\n\tconst [heightError, setHeightError] = useState(false);\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [cornerRadiusError, setCornerRadiusError] = useState(false);\r\n\tconst [borderWidthError, setBorderWidthError] = useState(false);\r\n\r\n\r\n\tconst [checklistCanvasProperties, setChecklistCanvasProperties] = useState<any>(() => {\r\n\t\tconst initialchecklistCanvasProperties = checklistGuideMetaData[0]?.canvas || {\r\n\twidth: \"930\",\r\n\theight: \"450\",\r\n\tcornerRadius: \"12\",\r\n\tprimaryColor: \"#5F9EA0\",\r\n\t\t\tborderColor: \"\",\r\n\tbackgroundColor: \"#ffffff\",\r\n\t\t\topenByDefault: false,\r\n\t\t\thideAfterCompletion: true,\r\n\tborderWidth:\"0\"\r\n\t\t};\r\n\t\treturn initialchecklistCanvasProperties;\r\n\t});\r\n\t// State for tracking changes and apply button\r\n\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\tconst [initialState, setInitialState] = useState(checklistCanvasProperties);\r\n\r\n\t// Function to check if the Apply button should be enabled\r\n\tconst updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {\r\n\t\tsetIsDisabled(!changed || hasErrors);\r\n\t};\r\n\r\n\t// Effect to check for any changes compared to initial state\r\n\tuseEffect(() => {\r\n\t\t// Compare current properties with initial state\r\n\t\tconst hasAnyChanges = JSON.stringify(checklistCanvasProperties) !== JSON.stringify(initialState);\r\n\t\tsetHasChanges(hasAnyChanges);\r\n\r\n\t\t// Check for validation errors\r\n\t\tconst hasValidationErrors = heightError || widthError || cornerRadiusError || borderWidthError;\r\n\r\n\t\tupdateApplyButtonState(hasAnyChanges, hasValidationErrors);\r\n\t}, [checklistCanvasProperties, initialState, heightError, widthError, cornerRadiusError, borderWidthError]);\r\n\r\n\tconst handleBorderColorChange = (e: any) => setBorderColor(e.target.value);\r\n\tconst handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowChecklistCanvasSettings(false);\r\n\r\n\t};\r\n\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\tsetChecklistCanvasProperties((prevState: any) => {\r\n\t\t\tconst newState = {\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t};\r\n\t\t\t// Mark that changes have been made\r\n\t\t\tsetHasChanges(true);\r\n\t\t\treturn newState;\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Apply the changes - updateChecklistCanvas will handle recording the change for undo/redo\r\n\t\tupdateChecklistCanvas(checklistCanvasProperties);\r\n\t\t// Update the initial state to the current state after applying changes\r\n\t\tsetInitialState({ ...checklistCanvasProperties });\r\n\t\t// Reset the changes flag\r\n\t\tsetHasChanges(false);\r\n\t\t// Disable the Apply button\r\n\t\tsetIsDisabled(true);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate('Canvas')}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Position Grid */}\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\r\n\r\n\t\t\t\t\t{/* Height Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Height')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.height}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"height\", '0');\r\n\t\t\t\t\t\t\t\t\tsetHeightError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate height between 100px and 1000px\r\n\t\t\t\t\t\t\t\tif (inputValue < 400 || inputValue > 600) {\r\n\t\t\t\t\t\t\t\t\tsetHeightError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetHeightError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"height\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={heightError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\t\t\t\t\t{heightError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 400px and 600px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n                        <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Width')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.width}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"width\", '0');\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate width between 300px and 1200px\r\n\t\t\t\t\t\t\t\tif (inputValue < 300 || inputValue > 1200) {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"width\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 300px and 1200px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* Corner Radius Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Corner Radius')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.cornerRadius}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"cornerRadius\", '0');\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate corner radius between 0px and 50px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 50) {\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"cornerRadius\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={cornerRadiusError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t{cornerRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 0px and 50px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Border Width')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.borderWidth}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"borderWidth\", '0');\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border width between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"borderWidth\", value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderWidthError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderWidthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 0px and 20px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n                    <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Primary Color')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.primaryColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"primaryColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#5F9EA0'}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Background')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.backgroundColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"backgroundColor\", e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Border')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.borderColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"borderColor\", e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Open by Default')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    checked={checklistCanvasProperties.openByDefault}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"openByDefault\", e.target.checked)}\r\n                                    name=\"showByDefault\"\r\n                                />\r\n                                <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n                        <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Hide After Completion')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    checked={checklistCanvasProperties.hideAfterCompletion}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"hideAfterCompletion\", e.target.checked)}\r\n                                    name=\"showByDefault\"\r\n                                />\r\n                                <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate('Apply')}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ChecklistCanvasSettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAEC,MAAM,KAAiB,eAAe,CAC7F,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAGjD;AACA,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,OAA6IC,OAAO,KAAQ,0BAA0B,CACtL,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,uBAAuB,CAAGC,IAAA,EAAoF,IAAnF,CAAEC,OAAO,CAAEC,UAAU,CAAEC,8BAA8B,CAAEC,gBAAsB,CAAC,CAAAJ,IAAA,CAC9G,KAAM,CAAEK,CAAC,CAAEC,SAAU,CAAC,CAAGZ,cAAc,CAAC,CAAC,CACzC,KAAM,CACLa,gBAAgB,CAChBC,WAAW,CACXC,gBAAgB,CAChBC,KAAK,CACLC,QAAQ,CACRC,eAAe,CACfC,cAAc,CACdC,kBAAkB,CAClBC,YAAY,CACZC,eAAe,CACfC,UAAU,CACVC,aAAa,CACbC,aAAa,CACbC,gBAAgB,CAChBC,SAAS,CACTC,YAAY,CACZC,sBAAsB,CACtBC,qBAAqB,CACrBC,mBAAmB,CACnBC,gBACD,CAAC,CAAGlC,cAAc,CAAEmC,KAAU,EAAKA,KAAK,CAAC,CAEzC,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CAE1C;AACA,KAAM,CAAC8C,WAAW,CAAEC,cAAc,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACgD,UAAU,CAAEC,aAAa,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACkD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACoD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CAG/D,KAAM,CAACsD,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGvD,QAAQ,CAAM,IAAM,KAAAwD,qBAAA,CACrF,KAAM,CAAAC,gCAAgC,CAAG,EAAAD,qBAAA,CAAAjB,sBAAsB,CAAC,CAAC,CAAC,UAAAiB,qBAAA,iBAAzBA,qBAAA,CAA2BE,MAAM,GAAI,CAC/EhC,KAAK,CAAE,KAAK,CACZiC,MAAM,CAAE,KAAK,CACbC,YAAY,CAAE,IAAI,CAClBC,YAAY,CAAE,SAAS,CACrBrC,WAAW,CAAE,EAAE,CACjBI,eAAe,CAAE,SAAS,CACxBkC,aAAa,CAAE,KAAK,CACpBC,mBAAmB,CAAE,IAAI,CAC3BC,WAAW,CAAC,GACX,CAAC,CACD,MAAO,CAAAP,gCAAgC,CACxC,CAAC,CAAC,CACF;AACA,KAAM,CAACQ,UAAU,CAAEC,aAAa,CAAC,CAAGlE,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACmE,UAAU,CAAEC,aAAa,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAACsD,yBAAyB,CAAC,CAE3E;AACA,KAAM,CAAAiB,sBAAsB,CAAG,QAAAA,CAACC,OAAgB,CAAiC,IAA/B,CAAAC,SAAkB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC3ER,aAAa,CAAC,CAACM,OAAO,EAAIC,SAAS,CAAC,CACrC,CAAC,CAED;AACA1E,SAAS,CAAC,IAAM,CACf;AACA,KAAM,CAAA8E,aAAa,CAAGC,IAAI,CAACC,SAAS,CAACzB,yBAAyB,CAAC,GAAKwB,IAAI,CAACC,SAAS,CAACV,YAAY,CAAC,CAChGD,aAAa,CAACS,aAAa,CAAC,CAE5B;AACA,KAAM,CAAAG,mBAAmB,CAAGlC,WAAW,EAAIE,UAAU,EAAIE,iBAAiB,EAAIE,gBAAgB,CAE9FmB,sBAAsB,CAACM,aAAa,CAAEG,mBAAmB,CAAC,CAC3D,CAAC,CAAE,CAAC1B,yBAAyB,CAAEe,YAAY,CAAEvB,WAAW,CAAEE,UAAU,CAAEE,iBAAiB,CAAEE,gBAAgB,CAAC,CAAC,CAE3G,KAAM,CAAA6B,uBAAuB,CAAIC,CAAM,EAAKrD,cAAc,CAACqD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC1E,KAAM,CAAAC,2BAA2B,CAAIH,CAAM,EAAKpD,kBAAkB,CAACoD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAElF,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACzBzC,SAAS,CAAC,KAAK,CAAC,CAChB1B,8BAA8B,CAAC,KAAK,CAAC,CAEtC,CAAC,CACD,KAAM,CAAAoE,gBAAgB,CAAGA,CAACC,GAAQ,CAAEJ,KAAU,GAAK,CAClD7B,4BAA4B,CAAEkC,SAAc,EAAK,CAChD,KAAM,CAAAC,QAAQ,CAAG,CAChB,GAAGD,SAAS,CACZ,CAACD,GAAG,EAAGJ,KACR,CAAC,CACD;AACAhB,aAAa,CAAC,IAAI,CAAC,CACnB,MAAO,CAAAsB,QAAQ,CAChB,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAChC;AACAnD,qBAAqB,CAACc,yBAAyB,CAAC,CAChD;AACAgB,eAAe,CAAC,CAAE,GAAGhB,yBAA0B,CAAC,CAAC,CACjD;AACAc,aAAa,CAAC,KAAK,CAAC,CACpB;AACAF,aAAa,CAAC,IAAI,CAAC,CACnBoB,WAAW,CAAC,CAAC,CACb7C,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,GAAI,CAACG,MAAM,CAAE,MAAO,KAAI,CAExB,mBACC;AACAhC,IAAA,QACCgF,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BhF,KAAA,QAAK+E,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BhF,KAAA,QAAK+E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnClF,IAAA,CAACR,UAAU,EACV,aAAW,OAAO,CAClB2F,OAAO,CAAET,WAAY,CAAAQ,QAAA,cAErBlF,IAAA,CAACL,2BAA2B,GAAE,CAAC,CAEpB,CAAC,cACbK,IAAA,QAAKiF,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAExE,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cAExDV,IAAA,CAACR,UAAU,EACV4F,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBD,OAAO,CAAET,WAAY,CAAAQ,QAAA,cAErBlF,IAAA,CAACN,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAGNM,IAAA,QAAKiF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC/BhF,KAAA,QAAK+E,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAI9BhF,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cAChEV,IAAA,QAAAkF,QAAA,cACDlF,IAAA,CAACT,SAAS,EACT8F,OAAO,CAAC,UAAU,CAClBb,KAAK,CAAE9B,yBAAyB,CAACK,MAAO,CACxCqC,IAAI,CAAC,OAAO,CACZE,SAAS,MACTL,SAAS,CAAC,qBAAqB,CAC/BM,QAAQ,CAAGjB,CAAC,EAAK,CAChB;AACA,KAAM,CAAAE,KAAK,CAAGF,CAAC,CAACC,MAAM,CAACC,KAAK,CAC5B,GAAIA,KAAK,GAAK,EAAE,CAAE,CACjBG,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CAC/BxC,cAAc,CAAC,KAAK,CAAC,CACrB,OACD,CAEA,GAAI,CAAC,SAAS,CAACqD,IAAI,CAAChB,KAAK,CAAC,CAAE,CAC3B,OACD,CAEA,KAAM,CAAAiB,UAAU,CAAGC,QAAQ,CAAClB,KAAK,CAAC,EAAI,CAAC,CAEvC;AACA,GAAIiB,UAAU,CAAG,GAAG,EAAIA,UAAU,CAAG,GAAG,CAAE,CACzCtD,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACNA,cAAc,CAAC,KAAK,CAAC,CACtB,CAEAwC,gBAAgB,CAAC,QAAQ,CAAEH,KAAK,CAAC,CAClC,CAAE,CAEFmB,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFC,KAAK,CAAE7D,WAAY,CACjB,CAAC,CACG,CAAC,EACe,CAAC,CACxBA,WAAW,eACXhC,KAAA,CAACZ,UAAU,EACV0G,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MACV,CAAE,CAAArB,QAAA,eAEFlF,IAAA,SAAMgG,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEN,QAAQ,CAAE,MAAM,CAAEO,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAC3FC,uBAAuB,CAAE,CAAEC,MAAM,CAAE9G,OAAQ,CAAE,CAC7C,CAAC,CACAa,SAAS,CAAC,wCAAwC,CAAC,EAC1C,CACZ,cACkBR,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnDlF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,OAAO,CAAC,CAAM,CAAC,cAC/DV,IAAA,QAAAkF,QAAA,cACDlF,IAAA,CAACT,SAAS,EACT8F,OAAO,CAAC,UAAU,CAClBb,KAAK,CAAE9B,yBAAyB,CAAC5B,KAAM,CACvCsE,IAAI,CAAC,OAAO,CACZE,SAAS,MACTL,SAAS,CAAC,qBAAqB,CAC/BM,QAAQ,CAAGjB,CAAC,EAAK,CAChB;AACA,KAAM,CAAAE,KAAK,CAAGF,CAAC,CAACC,MAAM,CAACC,KAAK,CAC5B,GAAIA,KAAK,GAAK,EAAE,CAAE,CACjBG,gBAAgB,CAAC,OAAO,CAAE,GAAG,CAAC,CAC9BtC,aAAa,CAAC,KAAK,CAAC,CACpB,OACD,CAEA,GAAI,CAAC,SAAS,CAACmD,IAAI,CAAChB,KAAK,CAAC,CAAE,CAC3B,OACD,CAEA,KAAM,CAAAiB,UAAU,CAAGC,QAAQ,CAAClB,KAAK,CAAC,EAAI,CAAC,CAEvC;AACA,GAAIiB,UAAU,CAAG,GAAG,EAAIA,UAAU,CAAG,IAAI,CAAE,CAC1CpD,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,IAAM,CACNA,aAAa,CAAC,KAAK,CAAC,CACrB,CAEAsC,gBAAgB,CAAC,OAAO,CAAEH,KAAK,CAAC,CACjC,CAAE,CAEFmB,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFC,KAAK,CAAE3D,UAAW,CAChB,CAAC,CACG,CAAC,EACJ,CAAC,CACLA,UAAU,eACVlC,KAAA,CAACZ,UAAU,EACV0G,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MACV,CAAE,CAAArB,QAAA,eAEFlF,IAAA,SAAMgG,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEN,QAAQ,CAAE,MAAM,CAAEO,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAC3FC,uBAAuB,CAAE,CAAEC,MAAM,CAAE9G,OAAQ,CAAE,CAC7C,CAAC,CACAa,SAAS,CAAC,yCAAyC,CAAC,EAC3C,CACZ,cAEDR,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,eAAe,CAAC,CAAM,CAAC,cACvEV,IAAA,QAAAkF,QAAA,cACDlF,IAAA,CAACT,SAAS,EACT8F,OAAO,CAAC,UAAU,CAClBb,KAAK,CAAE9B,yBAAyB,CAACM,YAAa,CAC9C4D,SAAS,MACTxB,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BM,QAAQ,CAAGjB,CAAC,EAAK,CAChB;AACA,KAAM,CAAAE,KAAK,CAAGF,CAAC,CAACC,MAAM,CAACC,KAAK,CAC5B,GAAIA,KAAK,GAAK,EAAE,CAAE,CACjBG,gBAAgB,CAAC,cAAc,CAAE,GAAG,CAAC,CACrCpC,oBAAoB,CAAC,KAAK,CAAC,CAC3B,OACD,CAEA,GAAI,CAAC,SAAS,CAACiD,IAAI,CAAChB,KAAK,CAAC,CAAE,CAC3B,OACD,CAEA,KAAM,CAAAiB,UAAU,CAAGC,QAAQ,CAAClB,KAAK,CAAC,EAAI,CAAC,CAEvC;AACA,GAAIiB,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACtClD,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAC,IAAM,CACNA,oBAAoB,CAAC,KAAK,CAAC,CAC5B,CAEAoC,gBAAgB,CAAC,cAAc,CAAEH,KAAK,CAAC,CACxC,CAAE,CAEFmB,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFC,KAAK,CAAEzD,iBAAkB,CACvB,CAAC,CACG,CAAC,EACH,CAAC,CACNA,iBAAiB,eACjBpC,KAAA,CAACZ,UAAU,EACV0G,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MACV,CAAE,CAAArB,QAAA,eAEFlF,IAAA,SAAMgG,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEN,QAAQ,CAAE,MAAM,CAAEO,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAC3FC,uBAAuB,CAAE,CAAEC,MAAM,CAAE9G,OAAQ,CAAE,CAC7C,CAAC,CACAa,SAAS,CAAC,qCAAqC,CAAC,EACvC,CACZ,cAEAR,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjClF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,cAAc,CAAC,CAAM,CAAC,cACtEV,IAAA,QAAAkF,QAAA,cACDlF,IAAA,CAACT,SAAS,EACT8F,OAAO,CAAC,UAAU,CAClBb,KAAK,CAAE9B,yBAAyB,CAACU,WAAY,CAC7CwD,SAAS,MACTxB,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/BM,QAAQ,CAAGjB,CAAC,EAAK,CAChB;AACA,KAAM,CAAAE,KAAK,CAAGF,CAAC,CAACC,MAAM,CAACC,KAAK,CAC5B,GAAIA,KAAK,GAAK,EAAE,CAAE,CACjBG,gBAAgB,CAAC,aAAa,CAAE,GAAG,CAAC,CACpClC,mBAAmB,CAAC,KAAK,CAAC,CAC1B,OACD,CAEA,GAAI,CAAC,SAAS,CAAC+C,IAAI,CAAChB,KAAK,CAAC,CAAE,CAC3B,OACD,CAEA,KAAM,CAAAiB,UAAU,CAAGC,QAAQ,CAAClB,KAAK,CAAC,EAAI,CAAC,CAEvC;AACA,GAAIiB,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACtChD,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,CACNA,mBAAmB,CAAC,KAAK,CAAC,CAC3B,CAEAkC,gBAAgB,CAAC,aAAa,CAAEH,KAAK,CAAC,CACvC,CAAE,CACFmB,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFC,KAAK,CAAEvD,gBAAiB,CACtB,CAAC,CACG,CAAC,EACJ,CAAC,CACLA,gBAAgB,eAChBtC,KAAA,CAACZ,UAAU,EACV0G,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MACV,CAAE,CAAArB,QAAA,eAEFlF,IAAA,SAAMgG,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEN,QAAQ,CAAE,MAAM,CAAEO,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAC3FC,uBAAuB,CAAE,CAAEC,MAAM,CAAE9G,OAAQ,CAAE,CAC7C,CAAC,CACAa,SAAS,CAAC,qCAAqC,CAAC,EACvC,CACZ,cAEcR,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/ClF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,eAAe,CAAC,CAAM,CAAC,cACvEV,IAAA,QAAAkF,QAAA,cACDlF,IAAA,UACC6G,IAAI,CAAC,OAAO,CACZrC,KAAK,CAAE9B,yBAAyB,CAACO,YAAa,CAC9CsC,QAAQ,CAAGjB,CAAC,EAAKK,gBAAgB,CAAC,cAAc,CAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAChES,SAAS,CAAC,mBAAmB,CAC7Be,KAAK,CAAE,CAAChF,eAAe,CAAC,SAAS,CAAE,CACnC,CAAC,CACG,CAAC,EACe,CAAC,cAGzBd,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,YAAY,CAAC,CAAM,CAAC,cACpEV,IAAA,QAAAkF,QAAA,cACDlF,IAAA,UACC6G,IAAI,CAAC,OAAO,CACZrC,KAAK,CAAE9B,yBAAyB,CAAC1B,eAAgB,CACjDuE,QAAQ,CAAGjB,CAAC,EAAKK,gBAAgB,CAAC,iBAAiB,CAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACrES,SAAS,CAAC,mBAAmB,CAC3B,CAAC,CACG,CAAC,EACe,CAAC,cAGzB/E,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cAChEV,IAAA,QAAAkF,QAAA,cACDlF,IAAA,UACC6G,IAAI,CAAC,OAAO,CACZrC,KAAK,CAAE9B,yBAAyB,CAAC9B,WAAY,CAC7C2E,QAAQ,CAAGjB,CAAC,EAAKK,gBAAgB,CAAC,aAAa,CAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACjES,SAAS,CAAC,mBAAmB,CAC3B,CAAC,CACG,CAAC,EACJ,CAAC,cAEL/E,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjClF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,iBAAiB,CAAC,CAAM,CAAC,cACzEV,IAAA,QAAAkF,QAAA,cACChF,KAAA,UAAO+E,SAAS,CAAC,eAAe,CAAAC,QAAA,eACRlF,IAAA,UACI6G,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEpE,yBAAyB,CAACQ,aAAc,CAC5EqC,QAAQ,CAAGjB,CAAC,EAAKK,gBAAgB,CAAC,eAAe,CAAEL,CAAC,CAACC,MAAM,CAACuC,OAAO,CAAE,CAC1CC,IAAI,CAAC,eAAe,CACvB,CAAC,cACF/G,IAAA,SAAMiF,SAAS,CAAC,QAAQ,CAAO,CAAC,EACjD,CAAC,CACH,CAAC,EACH,CAAC,cAEY/E,KAAA,CAACb,GAAG,EAAC4F,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnDlF,IAAA,QAAKiF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExE,SAAS,CAAC,uBAAuB,CAAC,CAAM,CAAC,cAC/EV,IAAA,QAAAkF,QAAA,cACChF,KAAA,UAAO+E,SAAS,CAAC,eAAe,CAAAC,QAAA,eACRlF,IAAA,UACI6G,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEpE,yBAAyB,CAACS,mBAAoB,CAClFoC,QAAQ,CAAGjB,CAAC,EAAKK,gBAAgB,CAAC,qBAAqB,CAAEL,CAAC,CAACC,MAAM,CAACuC,OAAO,CAAE,CAChDC,IAAI,CAAC,eAAe,CACvB,CAAC,cACF/G,IAAA,SAAMiF,SAAS,CAAC,QAAQ,CAAO,CAAC,EACjD,CAAC,CACH,CAAC,EACH,CAAC,EAGF,CAAC,CACD,CAAC,cACPjF,IAAA,QAAKiF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjClF,IAAA,CAACP,MAAM,EACN4F,OAAO,CAAC,WAAW,CACnBF,OAAO,CAAEJ,kBAAmB,CAC5BE,SAAS,CAAE,aAAa5B,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CACvD2D,QAAQ,CAAE3D,UAAW,CAAA6B,QAAA,CAErBxE,SAAS,CAAC,OAAO,CAAC,CACX,CAAC,CACL,CAAC,EACH,CAAC,CAEF,CACL;AAAA,EAEF,CAAC,CAED,cAAe,CAAAP,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}