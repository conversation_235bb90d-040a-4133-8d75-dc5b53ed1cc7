{"ast": null, "code": "import React,{useEffect,useState}from'react';import ChecklistCircle from\"./ChecklistCheckIcon\";import useDrawerStore from'../../store/drawerStore';import ImageCarousel from\"./ImageCarousel\";import VideoPlayer from\"./VideoPlayer\";import{chkdefault,closepluginicon,maximize}from'../../assets/icons/icons';import{GetGudeDetailsByGuideId}from'../../services/GuideListServices';import{useTranslation}from'react-i18next';import'../../styles/rtl_styles.scss';// Function to modify the color of an SVG icon\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const modifySVGColor=(base64SVG,color)=>{if(!base64SVG){return\"\";}try{// Check if the string is a valid base64 SVG\nif(!base64SVG.includes(\"data:image/svg+xml;base64,\")){return base64SVG;// Return the original if it's not an SVG\n}const decodedSVG=atob(base64SVG.split(\",\")[1]);// Check if this is primarily a stroke-based or fill-based icon\nconst hasStroke=decodedSVG.includes('stroke=\"');const hasColoredFill=/fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);let modifiedSVG=decodedSVG;if(hasStroke&&!hasColoredFill){// This is a stroke-based icon (like chkicn2-6) - only change stroke color\nmodifiedSVG=modifiedSVG.replace(/stroke=\"[^\"]+\"/g,`stroke=\"${color}\"`);}else if(hasColoredFill){// This is a fill-based icon (like chkicn1) - only change fill color\nmodifiedSVG=modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g,`fill=\"${color}\"`);}else{// No existing fill or stroke, add fill to make it visible\nmodifiedSVG=modifiedSVG.replace(/<path(?![^>]*fill=)/g,`<path fill=\"${color}\"`);modifiedSVG=modifiedSVG.replace(/<svg(?![^>]*fill=)/g,`<svg fill=\"${color}\"`);}const modifiedBase64=`data:image/svg+xml;base64,${btoa(modifiedSVG)}`;return modifiedBase64;}catch(error){console.error(\"Error modifying SVG color:\",error);return base64SVG;// Return the original if there's an error\n}};const ChecklistPreview=_ref=>{var _checklistGuideMetaDa,_checklistGuideMetaDa2,_checklistGuideMetaDa3,_checkpointslistData$,_checklistGuideMetaDa4,_checklistGuideMetaDa5,_checklistGuideMetaDa9,_checklistGuideMetaDa10,_checklistGuideMetaDa11,_checklistGuideMetaDa12,_checklistGuideMetaDa13,_checklistGuideMetaDa14,_checklistGuideMetaDa15,_checklistGuideMetaDa16,_checklistGuideMetaDa17,_checklistGuideMetaDa18,_checklistGuideMetaDa19,_checklistGuideMetaDa20,_checklistGuideMetaDa21,_checklistGuideMetaDa22,_checklistGuideMetaDa23,_checklistGuideMetaDa24,_checklistGuideMetaDa25,_checklistGuideMetaDa26,_checklistGuideMetaDa27,_checklistGuideMetaDa28,_checklistGuideMetaDa29,_checklistGuideMetaDa30,_checklistGuideMetaDa31,_checklistGuideMetaDa32,_checklistGuideMetaDa33,_checklistGuideMetaDa34,_checklistGuideMetaDa35,_checklistGuideMetaDa36,_checklistGuideMetaDa37,_checklistGuideMetaDa38,_checklistGuideMetaDa39,_checklistGuideMetaDa40,_checklistGuideMetaDa41,_checklistGuideMetaDa42,_checklistGuideMetaDa43,_checklistGuideMetaDa44,_checklistGuideMetaDa45,_checklistGuideMetaDa46,_checklistGuideMetaDa47,_checklistGuideMetaDa48,_checklistGuideMetaDa49,_checklistGuideMetaDa50,_checklistGuideMetaDa51,_selectedItem$support,_selectedItem$support2,_checklistGuideMetaDa58,_selectedItem$support3,_checklistGuideMetaDa59,_checklistGuideMetaDa60,_checklistGuideMetaDa61,_checklistGuideMetaDa62,_checklistGuideMetaDa63,_checklistGuideMetaDa64,_checklistGuideMetaDa65,_checklistGuideMetaDa66,_checklistGuideMetaDa67,_checklistGuideMetaDa68,_selectedItem$support4,_selectedItem$support5,_checklistGuideMetaDa69,_checklistGuideMetaDa70,_selectedItem$support6,_checklistGuideMetaDa71,_checklistGuideMetaDa72;let{isOpen,onClose,onRemainingCountUpdate,data,guideDetails,isRightPanelVisible,setIsRightPanelVisible}=_ref;const{t:translate}=useTranslation();const{checklistGuideMetaData,createWithAI,interactionData}=useDrawerStore(state=>state);const[isMaximized,setIsMaximized]=useState(false);const[completedStatus,setCompletedStatus]=useState({});const checkpointslistData=((_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:(_checklistGuideMetaDa2=_checklistGuideMetaDa.checkpoints)===null||_checklistGuideMetaDa2===void 0?void 0:(_checklistGuideMetaDa3=_checklistGuideMetaDa2.checkpointsList)===null||_checklistGuideMetaDa3===void 0?void 0:_checklistGuideMetaDa3.map((checkpoint,index)=>({...checkpoint,completed:index===0?true:false})))||[];const[checklistItems,setChecklistItems]=useState(checkpointslistData);const[activeItem,setActiveItem]=useState(((_checkpointslistData$=checkpointslistData[0])===null||_checkpointslistData$===void 0?void 0:_checkpointslistData$.id)||\"\");useEffect(()=>{if(Object.keys(completedStatus).length===0){const initialCompletedStatus={};checkpointslistData.forEach((item,index)=>{initialCompletedStatus[item.id]=index===0;});setCompletedStatus(initialCompletedStatus);}},[]);const checklistColor=(_checklistGuideMetaDa4=checklistGuideMetaData[0])===null||_checklistGuideMetaDa4===void 0?void 0:(_checklistGuideMetaDa5=_checklistGuideMetaDa4.canvas)===null||_checklistGuideMetaDa5===void 0?void 0:_checklistGuideMetaDa5.primaryColor;const selectedItem=checkpointslistData===null||checkpointslistData===void 0?void 0:checkpointslistData.find(item=>item.id===activeItem);useEffect(()=>{document.documentElement.style.setProperty(\"--chkcolor\",checklistColor);},[checklistColor]);const[isPublished,setIsPublished]=useState(true);useEffect(()=>{const fetchGuideDetails=async()=>{if(!(selectedItem!==null&&selectedItem!==void 0&&selectedItem.id))return;// Ensure there's a valid ID\ntry{var _res$GuideDetails,_res$GuideDetails2;const res=await GetGudeDetailsByGuideId(selectedItem.id,createWithAI,interactionData);if((res===null||res===void 0?void 0:(_res$GuideDetails=res.GuideDetails)===null||_res$GuideDetails===void 0?void 0:_res$GuideDetails.GuideStatus)===\"InActive\"||(res===null||res===void 0?void 0:(_res$GuideDetails2=res.GuideDetails)===null||_res$GuideDetails2===void 0?void 0:_res$GuideDetails2.GuideStatus)===\"Draft\"){setIsPublished(false);}else{setIsPublished(true);}}catch(error){}};fetchGuideDetails();},[selectedItem,activeItem]);useEffect(()=>{var _checkpointslistData$2;if(checkpointslistData)setActiveItem((_checkpointslistData$2=checkpointslistData[0])===null||_checkpointslistData$2===void 0?void 0:_checkpointslistData$2.id);},[(checkpointslistData===null||checkpointslistData===void 0?void 0:checkpointslistData.length)==1]);useEffect(()=>{var _checklistGuideMetaDa6;if(((_checklistGuideMetaDa6=checklistGuideMetaData[0])===null||_checklistGuideMetaDa6===void 0?void 0:_checklistGuideMetaDa6.length)>0){var _checklistGuideMetaDa7,_checklistGuideMetaDa8;const checkpointList=((_checklistGuideMetaDa7=checklistGuideMetaData[0])===null||_checklistGuideMetaDa7===void 0?void 0:(_checklistGuideMetaDa8=_checklistGuideMetaDa7.checkpoints)===null||_checklistGuideMetaDa8===void 0?void 0:_checklistGuideMetaDa8.checkpointlist)||[];const formattedChecklist=checkpointList.map((checkpoint,index)=>({id:checkpoint.id||index+1,title:checkpoint.title||`Step ${index+1}`,description:checkpoint.description||\"No description provided\",redirectURL:checkpoint.redirectURL||\"\",icon:checkpoint.icon||\"\",supportingMedia:checkpoint.supportingMedia||\"\",mediaTitle:checkpoint.mediaTitle||\"\",mediaDescription:checkpoint.mediaDescription||\"\"}));setChecklistItems(formattedChecklist);const initialCompletedStatus={};formattedChecklist.forEach(item=>{initialCompletedStatus[item.id]=false;});setCompletedStatus(initialCompletedStatus);}},[checklistGuideMetaData[0]]);// Update when checklistGuideMetaData changes\nconst totalItems=checkpointslistData.length||1;const progress=Object.values(completedStatus).filter(status=>status).length||1;// We'll let the ChecklistPopup component update the count based on completed status\n// This useEffect is no longer needed as we're getting the count from ChecklistPopup\nconst toggleItemCompletion=id=>{setCompletedStatus(prevStatus=>({...prevStatus,[id]:!prevStatus[id]}));};const handleMarkAsCompleted=id=>{setCompletedStatus(prevStatus=>({...prevStatus,[id]:true}));};const handleSelect=id=>{setActiveItem(id);setIsRightPanelVisible(true);};const handleClose=()=>{if(isRightPanelVisible){setIsRightPanelVisible(false);}else{onClose();}};const handleMinimize=()=>{setIsMaximized(false);};if(!isOpen)return null;if(!isOpen)return null;const handleNavigate=()=>{// window.open(\"http://localhost:3000/\", '_blank');\n};const handleMaximize=()=>{setIsMaximized(true);};// \t  const xOffset = parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\");\n// const xOffsetWithUnit = `${xOffset + 30}px`;\nconst isRTL=document.documentElement.getAttribute('dir')==='rtl'||document.body.getAttribute('dir')==='rtl';return/*#__PURE__*/_jsxs(_Fragment,{children:[isOpen&&/*#__PURE__*/_jsxs(\"div\",{style:{position:\"fixed\",inset:0,display:\"flex\",alignItems:\"center\",// justifyContent: 'center',\nzIndex:'99999'},children:[/*#__PURE__*/_jsx(\"div\",{style:{position:\"absolute\",inset:0,backgroundColor:\"rgba(0, 0, 0, 0.3)\"},onClick:handleClose}),/*#__PURE__*/_jsx(\"div\",{style:{boxShadow:\"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",zIndex:9,marginTop:\"auto\",marginBottom:`${parseInt(((_checklistGuideMetaDa9=checklistGuideMetaData[0])===null||_checklistGuideMetaDa9===void 0?void 0:(_checklistGuideMetaDa10=_checklistGuideMetaDa9.launcher)===null||_checklistGuideMetaDa10===void 0?void 0:(_checklistGuideMetaDa11=_checklistGuideMetaDa10.launcherposition)===null||_checklistGuideMetaDa11===void 0?void 0:_checklistGuideMetaDa11.yaxisOffset)||\"10\")+70}px`,marginLeft:((_checklistGuideMetaDa12=checklistGuideMetaData[0])===null||_checklistGuideMetaDa12===void 0?void 0:_checklistGuideMetaDa12.launcher.launcherposition.left)===true?`${parseInt(((_checklistGuideMetaDa13=checklistGuideMetaData[0])===null||_checklistGuideMetaDa13===void 0?void 0:(_checklistGuideMetaDa14=_checklistGuideMetaDa13.launcher)===null||_checklistGuideMetaDa14===void 0?void 0:(_checklistGuideMetaDa15=_checklistGuideMetaDa14.launcherposition)===null||_checklistGuideMetaDa15===void 0?void 0:_checklistGuideMetaDa15.xaxisOffset)||\"10\")+30}px`:\"auto\",marginRight:((_checklistGuideMetaDa16=checklistGuideMetaData[0])===null||_checklistGuideMetaDa16===void 0?void 0:_checklistGuideMetaDa16.launcher.launcherposition.left)===true?\"auto\":`${parseInt(((_checklistGuideMetaDa17=checklistGuideMetaData[0])===null||_checklistGuideMetaDa17===void 0?void 0:(_checklistGuideMetaDa18=_checklistGuideMetaDa17.launcher)===null||_checklistGuideMetaDa18===void 0?void 0:(_checklistGuideMetaDa19=_checklistGuideMetaDa18.launcherposition)===null||_checklistGuideMetaDa19===void 0?void 0:_checklistGuideMetaDa19.xaxisOffset)||\"10\")+30}px`},className:\"qadpt-chkpopup\",children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:(_checklistGuideMetaDa20=checklistGuideMetaData[0])===null||_checklistGuideMetaDa20===void 0?void 0:(_checklistGuideMetaDa21=_checklistGuideMetaDa20.canvas)===null||_checklistGuideMetaDa21===void 0?void 0:_checklistGuideMetaDa21.backgroundColor,border:`${(_checklistGuideMetaDa22=checklistGuideMetaData[0])===null||_checklistGuideMetaDa22===void 0?void 0:(_checklistGuideMetaDa23=_checklistGuideMetaDa22.canvas)===null||_checklistGuideMetaDa23===void 0?void 0:_checklistGuideMetaDa23.borderWidth}px solid ${(_checklistGuideMetaDa24=checklistGuideMetaData[0])===null||_checklistGuideMetaDa24===void 0?void 0:(_checklistGuideMetaDa25=_checklistGuideMetaDa24.canvas)===null||_checklistGuideMetaDa25===void 0?void 0:_checklistGuideMetaDa25.borderColor}`,borderRadius:`${(_checklistGuideMetaDa26=checklistGuideMetaData[0])===null||_checklistGuideMetaDa26===void 0?void 0:(_checklistGuideMetaDa27=_checklistGuideMetaDa26.canvas)===null||_checklistGuideMetaDa27===void 0?void 0:_checklistGuideMetaDa27.cornerRadius}px`,width:isRightPanelVisible?`${((_checklistGuideMetaDa28=checklistGuideMetaData[0])===null||_checklistGuideMetaDa28===void 0?void 0:(_checklistGuideMetaDa29=_checklistGuideMetaDa28.canvas)===null||_checklistGuideMetaDa29===void 0?void 0:_checklistGuideMetaDa29.width)||930}px`:\"350px\",height:`${((_checklistGuideMetaDa30=checklistGuideMetaData[0])===null||_checklistGuideMetaDa30===void 0?void 0:(_checklistGuideMetaDa31=_checklistGuideMetaDa30.canvas)===null||_checklistGuideMetaDa31===void 0?void 0:_checklistGuideMetaDa31.height)||500}px`},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",height:\"100%\",width:\"100%\",overflow:\"auto hidden\"},className:\"qadpt-chkcontent\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{width:isRightPanelVisible?\"40%\":\"100%\",borderRight:\"1px solid #e5e7eb\",textAlign:isRTL?\"right\":\"left\"},className:\"qadpt-chkrgt\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",flexDirection:\"column\",gap:\"16px\",borderBottom:\"1px solid #E8E8E8\",padding:\"24px 24px 16px 24px\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",flexDirection:\"column\",gap:\"6px\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"20px\",fontWeight:(_checklistGuideMetaDa32=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa32!==void 0&&(_checklistGuideMetaDa33=_checklistGuideMetaDa32.TitleSubTitle)!==null&&_checklistGuideMetaDa33!==void 0&&_checklistGuideMetaDa33.titleBold?\"bold\":\"normal\",fontStyle:(_checklistGuideMetaDa34=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa34!==void 0&&(_checklistGuideMetaDa35=_checklistGuideMetaDa34.TitleSubTitle)!==null&&_checklistGuideMetaDa35!==void 0&&_checklistGuideMetaDa35.titleItalic?\"italic\":\"normal\",color:((_checklistGuideMetaDa36=checklistGuideMetaData[0])===null||_checklistGuideMetaDa36===void 0?void 0:(_checklistGuideMetaDa37=_checklistGuideMetaDa36.TitleSubTitle)===null||_checklistGuideMetaDa37===void 0?void 0:_checklistGuideMetaDa37.titleColor)||\"#333\",display:\"block\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",wordBreak:\"break-word\",overflow:\"hidden\"},children:translate(((_checklistGuideMetaDa38=checklistGuideMetaData[0])===null||_checklistGuideMetaDa38===void 0?void 0:(_checklistGuideMetaDa39=_checklistGuideMetaDa38.TitleSubTitle)===null||_checklistGuideMetaDa39===void 0?void 0:_checklistGuideMetaDa39.title)||\"Checklist Title\")}),/*#__PURE__*/_jsx(\"div\",{children:!isRightPanelVisible&&/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:closepluginicon},onClick:handleClose,style:{background:\"#e8e8e8\",borderRadius:\"50%\",padding:\"8px\",display:\"flex\",cursor:\"pointer\"}})})]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"14px\",fontWeight:(_checklistGuideMetaDa40=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa40!==void 0&&(_checklistGuideMetaDa41=_checklistGuideMetaDa40.TitleSubTitle)!==null&&_checklistGuideMetaDa41!==void 0&&_checklistGuideMetaDa41.subTitleBold?\"bold\":\"normal\",fontStyle:(_checklistGuideMetaDa42=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa42!==void 0&&(_checklistGuideMetaDa43=_checklistGuideMetaDa42.TitleSubTitle)!==null&&_checklistGuideMetaDa43!==void 0&&_checklistGuideMetaDa43.subTitleItalic?\"italic\":\"normal\",color:((_checklistGuideMetaDa44=checklistGuideMetaData[0])===null||_checklistGuideMetaDa44===void 0?void 0:(_checklistGuideMetaDa45=_checklistGuideMetaDa44.TitleSubTitle)===null||_checklistGuideMetaDa45===void 0?void 0:_checklistGuideMetaDa45.subTitleColor)||\"#8D8D8D\"},className:\"qadpt-subtl\",children:translate(((_checklistGuideMetaDa46=checklistGuideMetaData[0])===null||_checklistGuideMetaDa46===void 0?void 0:(_checklistGuideMetaDa47=_checklistGuideMetaDa46.TitleSubTitle)===null||_checklistGuideMetaDa47===void 0?void 0:_checklistGuideMetaDa47.subTitle)||\"Context about the tasks in the checklist below users should prioritize completing.\")})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",marginBottom:\"8px\"},children:/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:\"14px\",color:\"#6b7280\"},children:[progress,\"/\",totalItems]})}),/*#__PURE__*/_jsx(\"div\",{style:{height:\"8px\",backgroundColor:\"#e5e7eb\",borderRadius:\"9999px\",overflow:\"hidden\"},children:/*#__PURE__*/_jsx(\"div\",{style:{height:\"100%\",backgroundColor:(_checklistGuideMetaDa48=checklistGuideMetaData[0])===null||_checklistGuideMetaDa48===void 0?void 0:(_checklistGuideMetaDa49=_checklistGuideMetaDa48.canvas)===null||_checklistGuideMetaDa49===void 0?void 0:_checklistGuideMetaDa49.primaryColor,borderRadius:\"9999px\",width:`${progress/totalItems*100}%`}})})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:`calc(${((_checklistGuideMetaDa50=checklistGuideMetaData[0])===null||_checklistGuideMetaDa50===void 0?void 0:(_checklistGuideMetaDa51=_checklistGuideMetaDa50.canvas)===null||_checklistGuideMetaDa51===void 0?void 0:_checklistGuideMetaDa51.height)||500}px - 190px)`,overflow:\"auto\"},className:\"qadpt-chklist\",children:checkpointslistData===null||checkpointslistData===void 0?void 0:checkpointslistData.map(item=>{var _checklistGuideMetaDa52,_checklistGuideMetaDa53,_checklistGuideMetaDa54,_checklistGuideMetaDa55,_checklistGuideMetaDa56,_checklistGuideMetaDa57;return/*#__PURE__*/_jsx(\"div\",{className:`${activeItem===item.id?\"qadpt-chkstp\":\"\"}`,children:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",flexDirection:\"column\",padding:\"10px 16px 10px 10px\",cursor:\"pointer\",// borderLeft: activeItem === item.id ? `4px solid ${checklistGuideMetaData[0]?.canvas?.primaryColor}` : '4px solid transparent', // Straight left border highlight\nborderBottom:\"1px solid #E8E8E8\"},onClick:()=>handleSelect(item.id),children:/*#__PURE__*/_jsxs(\"div\",{style:{paddingLeft:\"10px\",display:\"flex\",gap:\"6px\",flexDirection:\"column\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",width:\"100%\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",gap:\"10px\",flexDirection:\"row\",width:\"calc(100% - 60px)\"},children:[item.icon&&typeof item.icon===\"string\"?/*#__PURE__*/_jsx(\"img\",{src:modifySVGColor(item.icon,((_checklistGuideMetaDa52=checklistGuideMetaData[0])===null||_checklistGuideMetaDa52===void 0?void 0:(_checklistGuideMetaDa53=_checklistGuideMetaDa52.checkpoints)===null||_checklistGuideMetaDa53===void 0?void 0:_checklistGuideMetaDa53.checkpointsIcons)||\"#333\"),alt:\"icon\",style:{width:\"20px\",height:\"20px\"}}):/*#__PURE__*/_jsx(\"div\",{style:{width:\"20px\",height:\"20px\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(\"span\",{style:{width:\"16px\",height:\"16px\"}})}),/*#__PURE__*/_jsx(\"span\",{style:{color:((_checklistGuideMetaDa54=checklistGuideMetaData[0])===null||_checklistGuideMetaDa54===void 0?void 0:(_checklistGuideMetaDa55=_checklistGuideMetaDa54.checkpoints)===null||_checklistGuideMetaDa55===void 0?void 0:_checklistGuideMetaDa55.checkpointTitles)||\"#333\",overflow:\"hidden\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",wordBreak:\"break-word\"},children:item.title})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(ChecklistCircle,{completed:completedStatus[item.id],onClick:()=>{},size:\"sm\"},item.id)})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"p\",{style:{fontSize:\"14px\",color:(_checklistGuideMetaDa56=checklistGuideMetaData[0])===null||_checklistGuideMetaDa56===void 0?void 0:(_checklistGuideMetaDa57=_checklistGuideMetaDa56.checkpoints)===null||_checklistGuideMetaDa57===void 0?void 0:_checklistGuideMetaDa57.checkpointsDescription},className:\"qadpt-chkpopdesc\",children:item.description})})]})},item.id)});})})]}),isRightPanelVisible&&/*#__PURE__*/_jsxs(\"div\",{style:{width:\"60%\",padding:\"20px 20px 0 20px\"},className:\"qadpt-chklft\",children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",placeContent:\"end\",width:\"100%\",gap:\"6px\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:maximize},style:{background:\"#e8e8e8\",borderRadius:\"50%\",padding:\"6px\",display:\"flex\",cursor:\"pointer\"},onClick:handleMaximize}),/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:closepluginicon},onClick:handleClose,style:{background:\"#e8e8e8\",borderRadius:\"50%\",padding:\"8px\",display:\"flex\",cursor:\"pointer\"}})]}),/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",flexDirection:\"column\",gap:\"10px\",height:\"calc(100% - 90px)\"},children:/*#__PURE__*/_jsxs(\"div\",{style:{overflow:\"hidden auto\",display:\"flex\",alignItems:\"center\",flexDirection:\"column\",width:\"-webkit-fill-available\"},children:[(selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support=selectedItem.supportingMedia)===null||_selectedItem$support===void 0?void 0:_selectedItem$support.length)>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[selectedItem.supportingMedia.some(file=>{var _file$Base;return file===null||file===void 0?void 0:(_file$Base=file.Base64)===null||_file$Base===void 0?void 0:_file$Base.startsWith(\"data:image\");})&&/*#__PURE__*/_jsx(ImageCarousel,{selectedItem:selectedItem,activeItem:activeItem,images:selectedItem.supportingMedia.filter(file=>{var _file$Base2;return file===null||file===void 0?void 0:(_file$Base2=file.Base64)===null||_file$Base2===void 0?void 0:_file$Base2.startsWith(\"data:image\");}).map(file=>file.Base64),isMaximized:isMaximized}),selectedItem.supportingMedia.some(file=>{var _file$Base3;return file===null||file===void 0?void 0:(_file$Base3=file.Base64)===null||_file$Base3===void 0?void 0:_file$Base3.startsWith(\"data:video\");})&&selectedItem.supportingMedia.filter(file=>{var _file$Base4;return file===null||file===void 0?void 0:(_file$Base4=file.Base64)===null||_file$Base4===void 0?void 0:_file$Base4.startsWith(\"data:video\");}).map((file,index)=>/*#__PURE__*/_jsx(VideoPlayer,{videoFile:file.Base64,isMaximized:isMaximized},index))]}),((selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support2=selectedItem.supportingMedia)===null||_selectedItem$support2===void 0?void 0:_selectedItem$support2.length)===0||!(selectedItem!==null&&selectedItem!==void 0&&selectedItem.supportingMedia))&&/*#__PURE__*/_jsxs(\"div\",{style:{width:\"auto\",height:\"244px\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkdefault}}),/*#__PURE__*/_jsx(\"div\",{style:{color:\"#8D8D8D\"},children:translate(\"Check tasks, stay organized, and finish strong!\")})]}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\",marginTop:\"10px\"},className:\"qadpt-chkdesc\",children:selectedItem&&/*#__PURE__*/_jsx(\"div\",{style:{height:\"100%\",display:\"flex\",flexDirection:\"column\"},children:/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:isRTL?\"right\":\"left\",display:\"flex\",flexDirection:\"column\",gap:\"12px\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"16px\",fontWeight:600,color:\"#333\",overflow:\"hidden\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",wordBreak:\"break-word\"},children:selectedItem.mediaTitle}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-desc\",style:{color:\"#8D8D8D\"},children:selectedItem.mediaDescription})]})})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",gap:\"12px\",alignItems:\"center\",placeContent:\"end\",paddingBottom:\"20px\"},className:\"qadpt-btnsec\",children:[/*#__PURE__*/_jsx(\"button\",{style:{backgroundColor:(_checklistGuideMetaDa58=checklistGuideMetaData[0].canvas)===null||_checklistGuideMetaDa58===void 0?void 0:_checklistGuideMetaDa58.primaryColor,borderRadius:\"10px\",padding:\"9px 16px\",color:\"#fff\",border:\"none\",cursor:isPublished?\"pointer\":\"not-allowed\"},disabled:!isPublished,children:isPublished?translate(\"Take Tour\"):translate(\"Interaction Not Available\")}),(selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support3=selectedItem.supportingMedia)===null||_selectedItem$support3===void 0?void 0:_selectedItem$support3.length)>0&&/*#__PURE__*/_jsx(\"button\",{style:{borderRadius:\"10px\",padding:\"9px 16px\",color:(_checklistGuideMetaDa59=checklistGuideMetaData[0])===null||_checklistGuideMetaDa59===void 0?void 0:(_checklistGuideMetaDa60=_checklistGuideMetaDa59.canvas)===null||_checklistGuideMetaDa60===void 0?void 0:_checklistGuideMetaDa60.primaryColor,border:\"none\",background:\"#D3D9DA\",cursor:\"pointer\"},onClick:e=>handleMarkAsCompleted(selectedItem===null||selectedItem===void 0?void 0:selectedItem.id),children:translate(\"Mark as Completed\")})]})]})]})})})]}),isMaximized&&/*#__PURE__*/_jsx(\"div\",{style:{position:\"fixed\",top:0,left:0,right:0,bottom:0,backgroundColor:\"rgba(0, 0, 0, 0.5)\",zIndex:99999},children:/*#__PURE__*/_jsx(\"div\",{style:{position:\"fixed\",inset:0,display:\"flex\",alignItems:\"center\",// justifyContent: 'center',\nzIndex:50},children:/*#__PURE__*/_jsx(\"div\",{style:{boxShadow:\"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",zIndex:9,marginTop:\"8%\",marginBottom:\"5%\",// marginLeft: 'auto',\n//       marginRight: '100px',\ndisplay:\"flex\",alignItems:\"center\",placeContent:\"center\",width:\"100%\"},className:\"qadpt-chkpopup\",children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:(_checklistGuideMetaDa61=checklistGuideMetaData[0])===null||_checklistGuideMetaDa61===void 0?void 0:(_checklistGuideMetaDa62=_checklistGuideMetaDa61.canvas)===null||_checklistGuideMetaDa62===void 0?void 0:_checklistGuideMetaDa62.backgroundColor,border:`${(_checklistGuideMetaDa63=checklistGuideMetaData[0])===null||_checklistGuideMetaDa63===void 0?void 0:(_checklistGuideMetaDa64=_checklistGuideMetaDa63.canvas)===null||_checklistGuideMetaDa64===void 0?void 0:_checklistGuideMetaDa64.borderWidth}px solid ${(_checklistGuideMetaDa65=checklistGuideMetaData[0])===null||_checklistGuideMetaDa65===void 0?void 0:(_checklistGuideMetaDa66=_checklistGuideMetaDa65.canvas)===null||_checklistGuideMetaDa66===void 0?void 0:_checklistGuideMetaDa66.borderColor}`,borderRadius:`${(_checklistGuideMetaDa67=checklistGuideMetaData[0])===null||_checklistGuideMetaDa67===void 0?void 0:(_checklistGuideMetaDa68=_checklistGuideMetaDa67.canvas)===null||_checklistGuideMetaDa68===void 0?void 0:_checklistGuideMetaDa68.cornerRadius}px`,width:\"calc(-250px + 100vw)\",height:\"calc(100vh - 140px)\",overflow:\"auto\"},children:/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",height:\"100%\",width:\"100%\"},className:\"qadpt-chkcontent\",children:/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\",padding:\"20px 20px 0 20px\"},className:\"qadpt-chklft\",children:/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",flexDirection:\"column\",gap:\"10px\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",placeContent:\"end\",width:\"100%\",gap:\"6px\"},onClick:handleMinimize,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:closepluginicon},style:{background:\"#e8e8e8\",borderRadius:\"50%\",padding:\"8px\",display:\"flex\",cursor:\"pointer\"}})}),(selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support4=selectedItem.supportingMedia)===null||_selectedItem$support4===void 0?void 0:_selectedItem$support4.length)===0&&/*#__PURE__*/_jsxs(\"div\",{style:{width:\"auto\",height:\"244px\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkdefault}}),/*#__PURE__*/_jsx(\"div\",{style:{color:\"#8D8D8D\"},children:translate(\"Check tasks, stay organized, and finish strong!\")})]}),(selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support5=selectedItem.supportingMedia)===null||_selectedItem$support5===void 0?void 0:_selectedItem$support5.length)>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[selectedItem.supportingMedia.some(file=>{var _file$Base5;return file===null||file===void 0?void 0:(_file$Base5=file.Base64)===null||_file$Base5===void 0?void 0:_file$Base5.startsWith(\"data:image\");})&&/*#__PURE__*/_jsx(ImageCarousel,{selectedItem:selectedItem,activeItem:activeItem,images:selectedItem.supportingMedia.filter(file=>{var _file$Base6;return file===null||file===void 0?void 0:(_file$Base6=file.Base64)===null||_file$Base6===void 0?void 0:_file$Base6.startsWith(\"data:image\");}).map(file=>file.Base64),isMaximized:isMaximized}),selectedItem.supportingMedia.some(file=>{var _file$Base7;return file===null||file===void 0?void 0:(_file$Base7=file.Base64)===null||_file$Base7===void 0?void 0:_file$Base7.startsWith(\"data:video\");})&&selectedItem.supportingMedia.filter(file=>{var _file$base;return file===null||file===void 0?void 0:(_file$base=file.base64)===null||_file$base===void 0?void 0:_file$base.startsWith(\"data:video\");}).map((file,index)=>/*#__PURE__*/_jsx(VideoPlayer,{videoFile:file.base64,isMaximized:isMaximized},index))]}),/*#__PURE__*/_jsx(\"div\",{style:{width:\"100%\",marginTop:\"10px\"},className:\"qadpt-chkdesc\",children:selectedItem&&/*#__PURE__*/_jsxs(\"div\",{style:{height:\"100%\",display:\"flex\",flexDirection:\"column\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:isRTL?\"right\":\"left\",display:\"flex\",flexDirection:\"column\",gap:\"12px\",width:\"100%\"},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:\"16px\",fontWeight:600,color:\"#333\",overflow:\"hidden\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",wordBreak:\"break-word\"},children:selectedItem.mediaTitle}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-desc\",children:selectedItem.mediaDescription})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",gap:\"12px\",alignItems:\"center\",placeContent:\"end\",paddingBottom:\"20px\"},className:\"qadpt-btnsec\",children:[/*#__PURE__*/_jsx(\"button\",{style:{backgroundColor:(_checklistGuideMetaDa69=checklistGuideMetaData[0])===null||_checklistGuideMetaDa69===void 0?void 0:(_checklistGuideMetaDa70=_checklistGuideMetaDa69.canvas)===null||_checklistGuideMetaDa70===void 0?void 0:_checklistGuideMetaDa70.primaryColor,borderRadius:\"10px\",padding:\"9px 16px\",color:\"#fff\",border:\"none\",cursor:isPublished?\"pointer\":\"not-allowed\"},onClick:handleNavigate,disabled:!isPublished,children:isPublished?translate(\"Take Tour\"):translate(\"Interaction Not Available\")}),(selectedItem===null||selectedItem===void 0?void 0:(_selectedItem$support6=selectedItem.supportingMedia)===null||_selectedItem$support6===void 0?void 0:_selectedItem$support6.length)>0&&/*#__PURE__*/_jsx(\"button\",{style:{borderRadius:\"10px\",padding:\"9px 16px\",color:(_checklistGuideMetaDa71=checklistGuideMetaData[0])===null||_checklistGuideMetaDa71===void 0?void 0:(_checklistGuideMetaDa72=_checklistGuideMetaDa71.canvas)===null||_checklistGuideMetaDa72===void 0?void 0:_checklistGuideMetaDa72.primaryColor,border:\"none\",background:\"#D3D9DA\",cursor:\"pointer\"},onClick:e=>handleMarkAsCompleted(selectedItem===null||selectedItem===void 0?void 0:selectedItem.id),children:translate(\"Mark as Completed\")})]})]})})]})})})})})})})]});};export default ChecklistPreview;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "ChecklistCircle", "useDrawerStore", "ImageCarousel", "VideoPlayer", "chkdefault", "closepluginicon", "maximize", "GetGudeDetailsByGuideId", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "modifySVGColor", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "btoa", "error", "console", "ChecklistPreview", "_ref", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "_checkpointslistData$", "_checklistGuideMetaDa4", "_checklistGuideMetaDa5", "_checklistGuideMetaDa9", "_checklistGuideMetaDa10", "_checklistGuideMetaDa11", "_checklistGuideMetaDa12", "_checklistGuideMetaDa13", "_checklistGuideMetaDa14", "_checklistGuideMetaDa15", "_checklistGuideMetaDa16", "_checklistGuideMetaDa17", "_checklistGuideMetaDa18", "_checklistGuideMetaDa19", "_checklistGuideMetaDa20", "_checklistGuideMetaDa21", "_checklistGuideMetaDa22", "_checklistGuideMetaDa23", "_checklistGuideMetaDa24", "_checklistGuideMetaDa25", "_checklistGuideMetaDa26", "_checklistGuideMetaDa27", "_checklistGuideMetaDa28", "_checklistGuideMetaDa29", "_checklistGuideMetaDa30", "_checklistGuideMetaDa31", "_checklistGuideMetaDa32", "_checklistGuideMetaDa33", "_checklistGuideMetaDa34", "_checklistGuideMetaDa35", "_checklistGuideMetaDa36", "_checklistGuideMetaDa37", "_checklistGuideMetaDa38", "_checklistGuideMetaDa39", "_checklistGuideMetaDa40", "_checklistGuideMetaDa41", "_checklistGuideMetaDa42", "_checklistGuideMetaDa43", "_checklistGuideMetaDa44", "_checklistGuideMetaDa45", "_checklistGuideMetaDa46", "_checklistGuideMetaDa47", "_checklistGuideMetaDa48", "_checklistGuideMetaDa49", "_checklistGuideMetaDa50", "_checklistGuideMetaDa51", "_selectedItem$support", "_selectedItem$support2", "_checklistGuideMetaDa58", "_selectedItem$support3", "_checklistGuideMetaDa59", "_checklistGuideMetaDa60", "_checklistGuideMetaDa61", "_checklistGuideMetaDa62", "_checklistGuideMetaDa63", "_checklistGuideMetaDa64", "_checklistGuideMetaDa65", "_checklistGuideMetaDa66", "_checklistGuideMetaDa67", "_checklistGuideMetaDa68", "_selectedItem$support4", "_selectedItem$support5", "_checklistGuideMetaDa69", "_checklistGuideMetaDa70", "_selectedItem$support6", "_checklistGuideMetaDa71", "_checklistGuideMetaDa72", "isOpen", "onClose", "onRemainingCountUpdate", "data", "guideDetails", "isRightPanelVisible", "setIsRightPanelVisible", "t", "translate", "checklistGuideMetaData", "createWithAI", "interactionData", "state", "isMaximized", "setIsMaximized", "completedStatus", "setCompletedStatus", "checkpointslistData", "checkpoints", "checkpointsList", "map", "checkpoint", "index", "completed", "checklistItems", "setChecklistItems", "activeItem", "setActiveItem", "id", "Object", "keys", "length", "initialCompletedStatus", "for<PERSON>ach", "item", "checklistColor", "canvas", "primaryColor", "selectedItem", "find", "document", "documentElement", "style", "setProperty", "isPublished", "setIsPublished", "fetchGuideDetails", "_res$GuideDetails", "_res$GuideDetails2", "res", "GuideDetails", "GuideStatus", "_checkpointslistData$2", "_checklistGuideMetaDa6", "_checklistGuideMetaDa7", "_checklistGuideMetaDa8", "checkpointList", "checkpointlist", "formattedChecklist", "title", "description", "redirectURL", "icon", "supportingMedia", "mediaTitle", "mediaDescription", "totalItems", "progress", "values", "filter", "status", "toggleItemCompletion", "prevStatus", "handleMarkAsCompleted", "handleSelect", "handleClose", "handleMinimize", "handleNavigate", "handleMaximize", "isRTL", "getAttribute", "body", "children", "position", "inset", "display", "alignItems", "zIndex", "backgroundColor", "onClick", "boxShadow", "marginTop", "marginBottom", "parseInt", "launcher", "launcherposition", "yaxisOffset", "marginLeft", "left", "xaxisOffset", "marginRight", "className", "border", "borderWidth", "borderColor", "borderRadius", "cornerRadius", "width", "height", "overflow", "borderRight", "textAlign", "flexDirection", "gap", "borderBottom", "padding", "justifyContent", "fontSize", "fontWeight", "TitleSubTitle", "titleBold", "fontStyle", "titleItalic", "titleColor", "textOverflow", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "__html", "background", "cursor", "subTitleBold", "subTitleItalic", "subTitleColor", "subTitle", "maxHeight", "_checklistGuideMetaDa52", "_checklistGuideMetaDa53", "_checklistGuideMetaDa54", "_checklistGuideMetaDa55", "_checklistGuideMetaDa56", "_checklistGuideMetaDa57", "paddingLeft", "src", "checkpointsIcons", "alt", "checkpointTitles", "size", "checkpointsDescription", "place<PERSON><PERSON>nt", "some", "file", "_file$Base", "Base64", "startsWith", "images", "_file$Base2", "_file$Base3", "_file$Base4", "videoFile", "paddingBottom", "disabled", "e", "top", "right", "bottom", "_file$Base5", "_file$Base6", "_file$Base7", "_file$base", "base64"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistPreview.tsx"], "sourcesContent": ["\r\n\r\n\r\n\r\nimport React, { useEffect, useMemo, useState } from 'react';\r\nimport ChecklistCircle from \"./ChecklistCheckIcon\";\r\nimport useDrawerStore from '../../store/drawerStore';\r\nimport LauncherSettings from './LauncherSettings';\r\nimport ImageCarousel from \"./ImageCarousel\";\r\nimport VideoPlayer from \"./VideoPlayer\";\r\nimport {  chkdefault, closepluginicon, maximize } from '../../assets/icons/icons';\r\nimport { GetGudeDetailsByGuideId } from '../../services/GuideListServices';\r\nimport { useTranslation } from 'react-i18next';\r\nimport '../../styles/rtl_styles.scss';\r\n\r\n// Function to modify the color of an SVG icon\r\nconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\tif (!base64SVG) {\r\n\t\treturn \"\";\r\n\t}\r\n\r\n\ttry {\r\n\t\t// Check if the string is a valid base64 SVG\r\n\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t}\r\n\r\n\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t} else if (hasColoredFill) {\r\n\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t} else {\r\n\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t}\r\n\r\n\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\treturn modifiedBase64;\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error modifying SVG color:\", error);\r\n\t\treturn base64SVG; // Return the original if there's an error\r\n\t}\r\n};\r\ninterface CheckListPopupProps {\r\n    isOpen: any;\r\n    onClose: () => void;\r\n    onRemainingCountUpdate: (formattedCount: string) => void;\r\n    data: any;\r\n    guideDetails: any;\r\n    isRightPanelVisible: any;\r\n    setIsRightPanelVisible: any;\r\n  }\r\n  const ChecklistPreview: React.FC<CheckListPopupProps> = ({\r\n\t\tisOpen,\r\n\t\tonClose,\r\n\t\tonRemainingCountUpdate,\r\n\t\tdata,\r\n\t\tguideDetails,\r\n\t\tisRightPanelVisible,\r\n\t\tsetIsRightPanelVisible,\r\n\t}) => {\r\n\t  const { t: translate } = useTranslation();\r\n\t\tconst { checklistGuideMetaData, createWithAI, interactionData } = useDrawerStore((state: any) => state);\r\n\r\n\t\tconst [isMaximized, setIsMaximized] = useState(false);\r\n\t\tconst [completedStatus, setCompletedStatus] = useState<{ [key: string]: boolean }>({});\r\n\r\n\t\tconst checkpointslistData =\r\n\t\t\tchecklistGuideMetaData[0]?.checkpoints?.checkpointsList?.map((checkpoint: any, index: number) => ({\r\n\t\t\t\t...checkpoint,\r\n\t\t\t\tcompleted: index === 0 ? true : false,\r\n\t\t\t})) || [];\r\n\r\n\t\tconst [checklistItems, setChecklistItems] = useState(checkpointslistData);\r\n\t\tconst [activeItem, setActiveItem] = useState(checkpointslistData[0]?.id || \"\");\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tif (Object.keys(completedStatus).length === 0) {\r\n\t\t\t\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\r\n\t\t\t\tcheckpointslistData.forEach((item: any, index: number) => {\r\n\t\t\t\t\tinitialCompletedStatus[item.id] = index === 0;\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetCompletedStatus(initialCompletedStatus);\r\n\t\t\t}\r\n\t\t}, []);\r\n\t\tconst checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;\r\n\t\tconst selectedItem = checkpointslistData?.find((item: any) => item.id === activeItem);\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tdocument.documentElement.style.setProperty(\"--chkcolor\", checklistColor);\r\n\t\t}, [checklistColor]);\r\n\t\tconst [isPublished, setIsPublished] = useState(true);\r\n\t\tuseEffect(() => {\r\n\t\t\tconst fetchGuideDetails = async () => {\r\n\t\t\t\tif (!selectedItem?.id) return; // Ensure there's a valid ID\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await GetGudeDetailsByGuideId(selectedItem.id, createWithAI, interactionData);\r\n\t\t\t\t\tif (res?.GuideDetails?.GuideStatus === \"InActive\" || res?.GuideDetails?.GuideStatus === \"Draft\") {\r\n\t\t\t\t\t\tsetIsPublished(false);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tsetIsPublished(true);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {}\r\n\t\t\t};\r\n\r\n\t\t\tfetchGuideDetails();\r\n\t\t}, [selectedItem, activeItem]);\r\n\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checkpointslistData) setActiveItem(checkpointslistData[0]?.id);\r\n\t\t}, [checkpointslistData?.length == 1]);\r\n\t\tuseEffect(() => {\r\n\t\t\tif (checklistGuideMetaData[0]?.length > 0) {\r\n\t\t\t\tconst checkpointList = checklistGuideMetaData[0]?.checkpoints?.checkpointlist || [];\r\n\r\n\t\t\t\tconst formattedChecklist = checkpointList.map((checkpoint: any, index: number) => ({\r\n\t\t\t\t\tid: checkpoint.id || index + 1,\r\n\t\t\t\t\ttitle: checkpoint.title || `Step ${index + 1}`,\r\n\t\t\t\t\tdescription: checkpoint.description || \"No description provided\",\r\n\t\t\t\t\tredirectURL: checkpoint.redirectURL || \"\",\r\n\t\t\t\t\ticon: checkpoint.icon || \"\",\r\n\t\t\t\t\tsupportingMedia: checkpoint.supportingMedia || \"\",\r\n\t\t\t\t\tmediaTitle: checkpoint.mediaTitle || \"\",\r\n\t\t\t\t\tmediaDescription: checkpoint.mediaDescription || \"\",\r\n\t\t\t\t}));\r\n\t\t\t\tsetChecklistItems(formattedChecklist);\r\n\t\t\t\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\t\t\t\tformattedChecklist.forEach((item: any) => {\r\n\t\t\t\t\tinitialCompletedStatus[item.id] = false;\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetCompletedStatus(initialCompletedStatus);\r\n\t\t\t}\r\n\t\t}, [checklistGuideMetaData[0]]); // Update when checklistGuideMetaData changes\r\n\r\n\t\tconst totalItems = checkpointslistData.length || 1;\r\n\t\tconst progress = Object.values(completedStatus).filter((status) => status).length || 1;\r\n\r\n\t\t// We'll let the ChecklistPopup component update the count based on completed status\r\n\t\t// This useEffect is no longer needed as we're getting the count from ChecklistPopup\r\n\r\n\t\tconst toggleItemCompletion = (id: string) => {\r\n\t\t\tsetCompletedStatus((prevStatus) => ({\r\n\t\t\t\t...prevStatus,\r\n\t\t\t\t[id]: !prevStatus[id],\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleMarkAsCompleted = (id: string) => {\r\n\t\t\tsetCompletedStatus((prevStatus) => ({\r\n\t\t\t\t...prevStatus,\r\n\t\t\t\t[id]: true,\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleSelect = (id: any) => {\r\n\t\t\tsetActiveItem(id);\r\n\t\t\tsetIsRightPanelVisible(true);\r\n\t\t};\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tif (isRightPanelVisible) {\r\n\t\t\t\tsetIsRightPanelVisible(false);\r\n\t\t\t} else {\r\n\t\t\t\tonClose();\r\n\t\t\t}\r\n\t\t};\r\n\t\tconst handleMinimize = () => {\r\n\t\t\tsetIsMaximized(false);\r\n\t\t};\r\n\r\n\t\tif (!isOpen) return null;\r\n\r\n\t\tif (!isOpen) return null;\r\n\r\n\t\tconst handleNavigate = () => {\r\n\t\t\t// window.open(\"http://localhost:3000/\", '_blank');\r\n\t\t};\r\n\r\n\t\tconst handleMaximize = () => {\r\n\t\t\tsetIsMaximized(true);\r\n\t  };\r\n// \t  const xOffset = parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\");\r\n// const xOffsetWithUnit = `${xOffset + 30}px`;\r\n\t  \t\tconst isRTL = \r\n  document.documentElement.getAttribute('dir') === 'rtl' ||\r\n  document.body.getAttribute('dir') === 'rtl';\r\n\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t{isOpen && (\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t// justifyContent: 'center',\r\n\t\t\t\t\t\t\tzIndex: '99999',\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.3)\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t></div>\r\n\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",\r\n\t\t\t\t\t\t\t\tzIndex: 9,\r\n\t\t\t\t\t\t\t\tmarginTop: \"auto\",\r\n\t\t\t\t\t\t\t\tmarginBottom: `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.yaxisOffset || \"10\") + 70}px`,\r\n\t\t\t\t\t\t\t\tmarginLeft: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\") + 30}px` : \"auto\",\r\n\t\t\t\t\t\t\t\tmarginRight: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? \"auto\" : `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\") + 30}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-chkpopup\"\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,\r\n\t\t\t\t\t\t\t\t\tborder: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,\r\n\t\t\t\t\t\t\t\t\tborderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,\r\n\t\t\t\t\t\t\t\t\twidth: isRightPanelVisible ? `${checklistGuideMetaData[0]?.canvas?.width || 930}px` : \"350px\",\r\n\t\t\t\t\t\t\t\t\theight: `${checklistGuideMetaData[0]?.canvas?.height || 500}px`,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\toverflow: \"auto hidden\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkcontent\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{/* Left side - Checklist items */}\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: isRightPanelVisible ? \"40%\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRight: \"1px solid #e5e7eb\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\" : \"left\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkrgt\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderBottom: \"1px solid #E8E8E8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"24px 24px 16px 24px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", justifyContent: \"space-between\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.titleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.titleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.titleColor || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(checklistGuideMetaData[0]?.TitleSubTitle?.title || \"Checklist Title\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{!isRightPanelVisible && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleColor || \"#8D8D8D\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-subtl\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(checklistGuideMetaData[0]?.TitleSubTitle?.subTitle || \"Context about the tasks in the checklist below users should prioritize completing.\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ fontSize: \"14px\", color: \"#6b7280\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{progress}/{totalItems}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5e7eb\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: `${(progress / totalItems) * 100}%`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: `calc(${checklistGuideMetaData[0]?.canvas?.height || 500}px - 190px)`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chklist\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{checkpointslistData?.map((item: any) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div className={`${activeItem === item.id ? \"qadpt-chkstp\" : \"\"}`}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 16px 10px 10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// borderLeft: activeItem === item.id ? `4px solid ${checklistGuideMetaData[0]?.canvas?.primaryColor}` : '4px solid transparent', // Straight left border highlight\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderBottom: \"1px solid #E8E8E8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSelect(item.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{/* Title Section */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ paddingLeft: \"10px\", display: \"flex\", gap: \"6px\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"calc(100% - 60px)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.icon && typeof item.icon === \"string\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={modifySVGColor(item.icon, checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || \"#333\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ width: \"16px\", height: \"16px\" }}></span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.title}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ChecklistCircle\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcompleted={completedStatus[item.id]}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkpopdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.description}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t{/* Right side - Selected item details - only show when an item is selected */}\r\n\t\t\t\t\t\t\t\t\t{/* {activeItem && ( */}\r\n\t\t\t\t\t\t\t\t\t{isRightPanelVisible && (\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"60%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"20px 20px 0 20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chklft\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: maximize }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleMaximize}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"10px\",    height: \"calc(100% - 90px)\",\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{\r\n    \t\t\t\t\t\t\t\t\toverflow: \"hidden auto\",display: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",width:\"-webkit-fill-available\"}} >\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfile?.Base64?.startsWith(\"data:image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ImageCarousel\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem={selectedItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tactiveItem={activeItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timages={selectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:image\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any) => file.Base64)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:video\")) &&\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:video\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<VideoPlayer\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvideoFile={file.Base64}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{(selectedItem?.supportingMedia?.length === 0 || !selectedItem?.supportingMedia) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"auto\", height: \"244px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: chkdefault }} />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ color: \"#8D8D8D\" }}>{translate(\"Check tasks, stay organized, and finish strong!\")}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ height: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\" : \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 600,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-desc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"#8D8D8D\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaDescription}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpaddingBottom: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btnsec\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0].canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: isPublished ? \"pointer\" : \"not-allowed\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={!isPublished}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{isPublished ? translate(\"Take Tour\") : translate(\"Interaction Not Available\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e: any) => handleMarkAsCompleted(selectedItem?.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Mark as Completed\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t{/* )} */}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{isMaximized && (\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\tzIndex: 99999,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t// justifyContent: 'center',\r\n\t\t\t\t\t\t\t\tzIndex: 50,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 9,\r\n\t\t\t\t\t\t\t\t\tmarginTop: \"8%\",\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5%\",\r\n\t\t\t\t\t\t\t\t\t// marginLeft: 'auto',\r\n\t\t\t\t\t\t\t\t\t//       marginRight: '100px',\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-chkpopup\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\tborder: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,\r\n\t\t\t\t\t\t\t\t\t\twidth: \"calc(-250px + 100vw)\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"calc(100vh - 140px)\",\r\n\t\t\t\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkcontent\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{/* Left side - Checklist items */}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{/* Right side - Selected item details - only show when an item is selected */}\r\n\t\t\t\t\t\t\t\t\t\t{/* {activeItem && ( */}\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"20px 20px 0 20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chklft\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", flexDirection: \"column\", gap: \"10px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleMinimize}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length === 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"auto\", height: \"244px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: chkdefault }} />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ color: \"#8D8D8D\" }}>{translate(\"Check tasks, stay organized, and finish strong!\")}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfile?.Base64?.startsWith(\"data:image\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ImageCarousel\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem={selectedItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tactiveItem={activeItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\timages={selectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:image\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any) => file.Base64)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:video\")) &&\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.base64?.startsWith(\"data:video\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<VideoPlayer\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvideoFile={file.base64}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={isMaximized}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ height: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\" : \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 600,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"qadpt-desc\">{selectedItem.mediaDescription}</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpaddingBottom: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btnsec\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: isPublished ? \"pointer\" : \"not-allowed\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={handleNavigate}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={!isPublished}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{isPublished ? translate(\"Take Tour\") : translate(\"Interaction Not Available\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e: any) => handleMarkAsCompleted(selectedItem?.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Mark as Completed\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t{/* )} */}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t)}\r\n\t\t\t</>\r\n\t\t);\r\n\t};\r\nexport default ChecklistPreview;"], "mappings": "AAIA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAWC,QAAQ,KAAQ,OAAO,CAC3D,MAAO,CAAAC,eAAe,KAAM,sBAAsB,CAClD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAEpD,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,OAAUC,UAAU,CAAEC,eAAe,CAAEC,QAAQ,KAAQ,0BAA0B,CACjF,OAASC,uBAAuB,KAAQ,kCAAkC,CAC1E,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,8BAA8B,CAErC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,cAAc,CAAGA,CAACC,SAAc,CAAEC,KAAU,GAAK,CACtD,GAAI,CAACD,SAAS,CAAE,CACf,MAAO,EAAE,CACV,CAEA,GAAI,CACH;AACA,GAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,CAAE,CACtD,MAAO,CAAAF,SAAS,CAAE;AACnB,CAEA,KAAM,CAAAG,UAAU,CAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAEhD;AACA,KAAM,CAAAC,SAAS,CAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC,CACjD,KAAM,CAAAK,cAAc,CAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC,CAE/D,GAAI,CAAAM,WAAW,CAAGN,UAAU,CAE5B,GAAIG,SAAS,EAAI,CAACC,cAAc,CAAE,CACjC;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,CAAE,WAAWT,KAAK,GAAG,CAAC,CAC1E,CAAC,IAAM,IAAIM,cAAc,CAAE,CAC1B;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,CAAE,SAAST,KAAK,GAAG,CAAC,CAC9E,CAAC,IAAM,CACN;AACAQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,CAAE,eAAeT,KAAK,GAAG,CAAC,CAClFQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,CAAE,cAAcT,KAAK,GAAG,CAAC,CACjF,CAEA,KAAM,CAAAU,cAAc,CAAG,6BAA6BC,IAAI,CAACH,WAAW,CAAC,EAAE,CACvE,MAAO,CAAAE,cAAc,CACtB,CAAE,MAAOE,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CAAAb,SAAS,CAAE;AACnB,CACD,CAAC,CAUC,KAAM,CAAAe,gBAA+C,CAAGC,IAAA,EAQnD,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IARoD,CACzDC,MAAM,CACNC,OAAO,CACPC,sBAAsB,CACtBC,IAAI,CACJC,YAAY,CACZC,mBAAmB,CACnBC,sBACD,CAAC,CAAA7E,IAAA,CACC,KAAM,CAAE8E,CAAC,CAAEC,SAAU,CAAC,CAAGvG,cAAc,CAAC,CAAC,CAC1C,KAAM,CAAEwG,sBAAsB,CAAEC,YAAY,CAAEC,eAAgB,CAAC,CAAGjH,cAAc,CAAEkH,KAAU,EAAKA,KAAK,CAAC,CAEvG,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGtH,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACuH,eAAe,CAAEC,kBAAkB,CAAC,CAAGxH,QAAQ,CAA6B,CAAC,CAAC,CAAC,CAEtF,KAAM,CAAAyH,mBAAmB,CACxB,EAAAvF,qBAAA,CAAA+E,sBAAsB,CAAC,CAAC,CAAC,UAAA/E,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA2BwF,WAAW,UAAAvF,sBAAA,kBAAAC,sBAAA,CAAtCD,sBAAA,CAAwCwF,eAAe,UAAAvF,sBAAA,iBAAvDA,sBAAA,CAAyDwF,GAAG,CAAC,CAACC,UAAe,CAAEC,KAAa,IAAM,CACjG,GAAGD,UAAU,CACbE,SAAS,CAAED,KAAK,GAAK,CAAC,CAAG,IAAI,CAAG,KACjC,CAAC,CAAC,CAAC,GAAI,EAAE,CAEV,KAAM,CAACE,cAAc,CAAEC,iBAAiB,CAAC,CAAGjI,QAAQ,CAACyH,mBAAmB,CAAC,CACzE,KAAM,CAACS,UAAU,CAAEC,aAAa,CAAC,CAAGnI,QAAQ,CAAC,EAAAqC,qBAAA,CAAAoF,mBAAmB,CAAC,CAAC,CAAC,UAAApF,qBAAA,iBAAtBA,qBAAA,CAAwB+F,EAAE,GAAI,EAAE,CAAC,CAE9ErI,SAAS,CAAC,IAAM,CACf,GAAIsI,MAAM,CAACC,IAAI,CAACf,eAAe,CAAC,CAACgB,MAAM,GAAK,CAAC,CAAE,CAC9C,KAAM,CAAAC,sBAAkD,CAAG,CAAC,CAAC,CAE7Df,mBAAmB,CAACgB,OAAO,CAAC,CAACC,IAAS,CAAEZ,KAAa,GAAK,CACzDU,sBAAsB,CAACE,IAAI,CAACN,EAAE,CAAC,CAAGN,KAAK,GAAK,CAAC,CAC9C,CAAC,CAAC,CAEFN,kBAAkB,CAACgB,sBAAsB,CAAC,CAC3C,CACD,CAAC,CAAE,EAAE,CAAC,CACN,KAAM,CAAAG,cAAc,EAAArG,sBAAA,CAAG2E,sBAAsB,CAAC,CAAC,CAAC,UAAA3E,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA2BsG,MAAM,UAAArG,sBAAA,iBAAjCA,sBAAA,CAAmCsG,YAAY,CACtE,KAAM,CAAAC,YAAY,CAAGrB,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEsB,IAAI,CAAEL,IAAS,EAAKA,IAAI,CAACN,EAAE,GAAKF,UAAU,CAAC,CAErFnI,SAAS,CAAC,IAAM,CACfiJ,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,YAAY,CAAER,cAAc,CAAC,CACzE,CAAC,CAAE,CAACA,cAAc,CAAC,CAAC,CACpB,KAAM,CAACS,WAAW,CAAEC,cAAc,CAAC,CAAGrJ,QAAQ,CAAC,IAAI,CAAC,CACpDD,SAAS,CAAC,IAAM,CACf,KAAM,CAAAuJ,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,EAACR,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEV,EAAE,EAAE,OAAQ;AAE/B,GAAI,KAAAmB,iBAAA,CAAAC,kBAAA,CACH,KAAM,CAAAC,GAAG,CAAG,KAAM,CAAAjJ,uBAAuB,CAACsI,YAAY,CAACV,EAAE,CAAElB,YAAY,CAAEC,eAAe,CAAC,CACzF,GAAI,CAAAsC,GAAG,SAAHA,GAAG,kBAAAF,iBAAA,CAAHE,GAAG,CAAEC,YAAY,UAAAH,iBAAA,iBAAjBA,iBAAA,CAAmBI,WAAW,IAAK,UAAU,EAAI,CAAAF,GAAG,SAAHA,GAAG,kBAAAD,kBAAA,CAAHC,GAAG,CAAEC,YAAY,UAAAF,kBAAA,iBAAjBA,kBAAA,CAAmBG,WAAW,IAAK,OAAO,CAAE,CAChGN,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,IAAM,CACNA,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAE,MAAOvH,KAAK,CAAE,CAAC,CAClB,CAAC,CAEDwH,iBAAiB,CAAC,CAAC,CACpB,CAAC,CAAE,CAACR,YAAY,CAAEZ,UAAU,CAAC,CAAC,CAE9BnI,SAAS,CAAC,IAAM,KAAA6J,sBAAA,CACf,GAAInC,mBAAmB,CAAEU,aAAa,EAAAyB,sBAAA,CAACnC,mBAAmB,CAAC,CAAC,CAAC,UAAAmC,sBAAA,iBAAtBA,sBAAA,CAAwBxB,EAAE,CAAC,CACnE,CAAC,CAAE,CAAC,CAAAX,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEc,MAAM,GAAI,CAAC,CAAC,CAAC,CACtCxI,SAAS,CAAC,IAAM,KAAA8J,sBAAA,CACf,GAAI,EAAAA,sBAAA,CAAA5C,sBAAsB,CAAC,CAAC,CAAC,UAAA4C,sBAAA,iBAAzBA,sBAAA,CAA2BtB,MAAM,EAAG,CAAC,CAAE,KAAAuB,sBAAA,CAAAC,sBAAA,CAC1C,KAAM,CAAAC,cAAc,CAAG,EAAAF,sBAAA,CAAA7C,sBAAsB,CAAC,CAAC,CAAC,UAAA6C,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA2BpC,WAAW,UAAAqC,sBAAA,iBAAtCA,sBAAA,CAAwCE,cAAc,GAAI,EAAE,CAEnF,KAAM,CAAAC,kBAAkB,CAAGF,cAAc,CAACpC,GAAG,CAAC,CAACC,UAAe,CAAEC,KAAa,IAAM,CAClFM,EAAE,CAAEP,UAAU,CAACO,EAAE,EAAIN,KAAK,CAAG,CAAC,CAC9BqC,KAAK,CAAEtC,UAAU,CAACsC,KAAK,EAAI,QAAQrC,KAAK,CAAG,CAAC,EAAE,CAC9CsC,WAAW,CAAEvC,UAAU,CAACuC,WAAW,EAAI,yBAAyB,CAChEC,WAAW,CAAExC,UAAU,CAACwC,WAAW,EAAI,EAAE,CACzCC,IAAI,CAAEzC,UAAU,CAACyC,IAAI,EAAI,EAAE,CAC3BC,eAAe,CAAE1C,UAAU,CAAC0C,eAAe,EAAI,EAAE,CACjDC,UAAU,CAAE3C,UAAU,CAAC2C,UAAU,EAAI,EAAE,CACvCC,gBAAgB,CAAE5C,UAAU,CAAC4C,gBAAgB,EAAI,EAClD,CAAC,CAAC,CAAC,CACHxC,iBAAiB,CAACiC,kBAAkB,CAAC,CACrC,KAAM,CAAA1B,sBAAkD,CAAG,CAAC,CAAC,CAC7D0B,kBAAkB,CAACzB,OAAO,CAAEC,IAAS,EAAK,CACzCF,sBAAsB,CAACE,IAAI,CAACN,EAAE,CAAC,CAAG,KAAK,CACxC,CAAC,CAAC,CAEFZ,kBAAkB,CAACgB,sBAAsB,CAAC,CAC3C,CACD,CAAC,CAAE,CAACvB,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;AAEjC,KAAM,CAAAyD,UAAU,CAAGjD,mBAAmB,CAACc,MAAM,EAAI,CAAC,CAClD,KAAM,CAAAoC,QAAQ,CAAGtC,MAAM,CAACuC,MAAM,CAACrD,eAAe,CAAC,CAACsD,MAAM,CAAEC,MAAM,EAAKA,MAAM,CAAC,CAACvC,MAAM,EAAI,CAAC,CAEtF;AACA;AAEA,KAAM,CAAAwC,oBAAoB,CAAI3C,EAAU,EAAK,CAC5CZ,kBAAkB,CAAEwD,UAAU,GAAM,CACnC,GAAGA,UAAU,CACb,CAAC5C,EAAE,EAAG,CAAC4C,UAAU,CAAC5C,EAAE,CACrB,CAAC,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA6C,qBAAqB,CAAI7C,EAAU,EAAK,CAC7CZ,kBAAkB,CAAEwD,UAAU,GAAM,CACnC,GAAGA,UAAU,CACb,CAAC5C,EAAE,EAAG,IACP,CAAC,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA8C,YAAY,CAAI9C,EAAO,EAAK,CACjCD,aAAa,CAACC,EAAE,CAAC,CACjBtB,sBAAsB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAqE,WAAW,CAAGA,CAAA,GAAM,CACzB,GAAItE,mBAAmB,CAAE,CACxBC,sBAAsB,CAAC,KAAK,CAAC,CAC9B,CAAC,IAAM,CACNL,OAAO,CAAC,CAAC,CACV,CACD,CAAC,CACD,KAAM,CAAA2E,cAAc,CAAGA,CAAA,GAAM,CAC5B9D,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,GAAI,CAACd,MAAM,CAAE,MAAO,KAAI,CAExB,GAAI,CAACA,MAAM,CAAE,MAAO,KAAI,CAExB,KAAM,CAAA6E,cAAc,CAAGA,CAAA,GAAM,CAC5B;AAAA,CACA,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC5BhE,cAAc,CAAC,IAAI,CAAC,CACpB,CAAC,CACJ;AACA;AACK,KAAM,CAAAiE,KAAK,CACdvC,QAAQ,CAACC,eAAe,CAACuC,YAAY,CAAC,KAAK,CAAC,GAAK,KAAK,EACtDxC,QAAQ,CAACyC,IAAI,CAACD,YAAY,CAAC,KAAK,CAAC,GAAK,KAAK,CAE3C,mBACC3K,KAAA,CAAAE,SAAA,EAAA2K,QAAA,EACElF,MAAM,eACN3F,KAAA,QACCqI,KAAK,CAAE,CACNyC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,CAAC,CACRC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB;AACAC,MAAM,CAAE,OAAc,CAAE,CAAAL,QAAA,eAEzB/K,IAAA,QACCuI,KAAK,CAAE,CACNyC,QAAQ,CAAE,UAAU,CACpBC,KAAK,CAAE,CAAC,CACRI,eAAe,CAAE,oBAClB,CAAE,CACFC,OAAO,CAAEd,WAAY,CAChB,CAAC,cAEPxK,IAAA,QACCuI,KAAK,CAAE,CACNgD,SAAS,CAAE,+EAA+E,CAC1FH,MAAM,CAAE,CAAC,CACTI,SAAS,CAAE,MAAM,CACjBC,YAAY,CAAE,GAAGC,QAAQ,CAAC,EAAA7J,sBAAA,CAAAyE,sBAAsB,CAAC,CAAC,CAAC,UAAAzE,sBAAA,kBAAAC,uBAAA,CAAzBD,sBAAA,CAA2B8J,QAAQ,UAAA7J,uBAAA,kBAAAC,uBAAA,CAAnCD,uBAAA,CAAqC8J,gBAAgB,UAAA7J,uBAAA,iBAArDA,uBAAA,CAAuD8J,WAAW,GAAI,IAAI,CAAC,CAAG,EAAE,IAAI,CAC9GC,UAAU,CAAE,EAAA9J,uBAAA,CAAAsE,sBAAsB,CAAC,CAAC,CAAC,UAAAtE,uBAAA,iBAAzBA,uBAAA,CAA2B2J,QAAQ,CAACC,gBAAgB,CAACG,IAAI,IAAK,IAAI,CAAG,GAAGL,QAAQ,CAAC,EAAAzJ,uBAAA,CAAAqE,sBAAsB,CAAC,CAAC,CAAC,UAAArE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B0J,QAAQ,UAAAzJ,uBAAA,kBAAAC,uBAAA,CAAnCD,uBAAA,CAAqC0J,gBAAgB,UAAAzJ,uBAAA,iBAArDA,uBAAA,CAAuD6J,WAAW,GAAI,IAAI,CAAC,CAAG,EAAE,IAAI,CAAG,MAAM,CAC1LC,WAAW,CAAE,EAAA7J,uBAAA,CAAAkE,sBAAsB,CAAC,CAAC,CAAC,UAAAlE,uBAAA,iBAAzBA,uBAAA,CAA2BuJ,QAAQ,CAACC,gBAAgB,CAACG,IAAI,IAAK,IAAI,CAAG,MAAM,CAAG,GAAGL,QAAQ,CAAC,EAAArJ,uBAAA,CAAAiE,sBAAsB,CAAC,CAAC,CAAC,UAAAjE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BsJ,QAAQ,UAAArJ,uBAAA,kBAAAC,uBAAA,CAAnCD,uBAAA,CAAqCsJ,gBAAgB,UAAArJ,uBAAA,iBAArDA,uBAAA,CAAuDyJ,WAAW,GAAI,IAAI,CAAC,CAAG,EAAE,IACxL,CAAE,CACFE,SAAS,CAAC,gBAAgB,CAAAnB,QAAA,cAC1B/K,IAAA,QACCuI,KAAK,CAAE,CACN8C,eAAe,EAAA7I,uBAAA,CAAE8D,sBAAsB,CAAC,CAAC,CAAC,UAAA9D,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2ByF,MAAM,UAAAxF,uBAAA,iBAAjCA,uBAAA,CAAmC4I,eAAe,CACnEc,MAAM,CAAE,IAAAzJ,uBAAA,CAAG4D,sBAAsB,CAAC,CAAC,CAAC,UAAA5D,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BuF,MAAM,UAAAtF,uBAAA,iBAAjCA,uBAAA,CAAmCyJ,WAAW,aAAAxJ,uBAAA,CAAY0D,sBAAsB,CAAC,CAAC,CAAC,UAAA1D,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BqF,MAAM,UAAApF,uBAAA,iBAAjCA,uBAAA,CAAmCwJ,WAAW,EAAE,CACrHC,YAAY,CAAE,IAAAxJ,uBAAA,CAAGwD,sBAAsB,CAAC,CAAC,CAAC,UAAAxD,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BmF,MAAM,UAAAlF,uBAAA,iBAAjCA,uBAAA,CAAmCwJ,YAAY,IAAI,CACpEC,KAAK,CAAEtG,mBAAmB,CAAG,GAAG,EAAAlD,uBAAA,CAAAsD,sBAAsB,CAAC,CAAC,CAAC,UAAAtD,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BiF,MAAM,UAAAhF,uBAAA,iBAAjCA,uBAAA,CAAmCuJ,KAAK,GAAI,GAAG,IAAI,CAAG,OAAO,CAC7FC,MAAM,CAAE,GAAG,EAAAvJ,uBAAA,CAAAoD,sBAAsB,CAAC,CAAC,CAAC,UAAApD,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B+E,MAAM,UAAA9E,uBAAA,iBAAjCA,uBAAA,CAAmCsJ,MAAM,GAAI,GAAG,IAC5D,CAAE,CAAA1B,QAAA,cAEF7K,KAAA,QACCqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfuB,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,MAAM,CACbE,QAAQ,CAAE,aACX,CAAE,CACFR,SAAS,CAAC,kBAAkB,CAAAnB,QAAA,eAG5B7K,KAAA,QACCqI,KAAK,CAAE,CACNiE,KAAK,CAAEtG,mBAAmB,CAAG,KAAK,CAAG,MAAM,CAC3CyG,WAAW,CAAE,mBAAmB,CAEhCC,SAAS,CAAEhC,KAAK,CAAG,OAAO,CAAG,MAC9B,CAAE,CACFsB,SAAS,CAAC,cAAc,CAAAnB,QAAA,eAExB7K,KAAA,QACCqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,MAAM,CACXC,YAAY,CAAE,mBAAmB,CACjCC,OAAO,CAAE,qBACV,CAAE,CAAAjC,QAAA,eAEF7K,KAAA,QACCqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,KACN,CAAE,CAAA/B,QAAA,eAEF7K,KAAA,QAAKqI,KAAK,CAAE,CAAE2C,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE8B,cAAc,CAAE,eAAgB,CAAE,CAAAlC,QAAA,eACtF/K,IAAA,QACCuI,KAAK,CAAE,CACN2E,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,CAAA/J,uBAAA,CAAAkD,sBAAsB,CAAC,CAAC,CAAC,UAAAlD,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2BgK,aAAa,UAAA/J,uBAAA,WAAxCA,uBAAA,CAA0CgK,SAAS,CAAG,MAAM,CAAG,QAAQ,CACnFC,SAAS,CAAE,CAAAhK,uBAAA,CAAAgD,sBAAsB,CAAC,CAAC,CAAC,UAAAhD,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2B8J,aAAa,UAAA7J,uBAAA,WAAxCA,uBAAA,CAA0CgK,WAAW,CAAG,QAAQ,CAAG,QAAQ,CACtFhN,KAAK,CAAE,EAAAiD,uBAAA,CAAA8C,sBAAsB,CAAC,CAAC,CAAC,UAAA9C,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B4J,aAAa,UAAA3J,uBAAA,iBAAxCA,uBAAA,CAA0C+J,UAAU,GAAI,MAAM,CACrEtC,OAAO,CAAE,OAAO,CAChBuC,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YAAY,CACvBjB,QAAQ,CAAE,QACX,CAAE,CAAA3B,QAAA,CAED1E,SAAS,CAAC,EAAA3C,uBAAA,CAAA4C,sBAAsB,CAAC,CAAC,CAAC,UAAA5C,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B0J,aAAa,UAAAzJ,uBAAA,iBAAxCA,uBAAA,CAA0C6F,KAAK,GAAI,iBAAiB,CAAC,CAC5E,CAAC,cACNxJ,IAAA,QAAA+K,QAAA,CACE,CAAC7E,mBAAmB,eACpBlG,IAAA,SACC4N,uBAAuB,CAAE,CAAEC,MAAM,CAAElO,eAAgB,CAAE,CACrD2L,OAAO,CAAEd,WAAY,CACrBjC,KAAK,CAAE,CACNuF,UAAU,CAAE,SAAS,CACrBxB,YAAY,CAAE,KAAK,CACnBU,OAAO,CAAE,KAAK,CACd9B,OAAO,CAAE,MAAM,CACf6C,MAAM,CAAE,SACT,CAAE,CACF,CACD,CACG,CAAC,EACF,CAAC,cACN/N,IAAA,QACCuI,KAAK,CAAE,CACN2E,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,CAAAvJ,uBAAA,CAAA0C,sBAAsB,CAAC,CAAC,CAAC,UAAA1C,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2BwJ,aAAa,UAAAvJ,uBAAA,WAAxCA,uBAAA,CAA0CmK,YAAY,CAAG,MAAM,CAAG,QAAQ,CACtFV,SAAS,CAAE,CAAAxJ,uBAAA,CAAAwC,sBAAsB,CAAC,CAAC,CAAC,UAAAxC,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA2BsJ,aAAa,UAAArJ,uBAAA,WAAxCA,uBAAA,CAA0CkK,cAAc,CAAG,QAAQ,CAAG,QAAQ,CACzF1N,KAAK,CAAE,EAAAyD,uBAAA,CAAAsC,sBAAsB,CAAC,CAAC,CAAC,UAAAtC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BoJ,aAAa,UAAAnJ,uBAAA,iBAAxCA,uBAAA,CAA0CiK,aAAa,GAAI,SACnE,CAAE,CACFhC,SAAS,CAAC,aAAa,CAAAnB,QAAA,CAEtB1E,SAAS,CAAC,EAAAnC,uBAAA,CAAAoC,sBAAsB,CAAC,CAAC,CAAC,UAAApC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BkJ,aAAa,UAAAjJ,uBAAA,iBAAxCA,uBAAA,CAA0CgK,QAAQ,GAAI,oFAAoF,CAAC,CAClJ,CAAC,EACF,CAAC,cAENjO,KAAA,QAAA6K,QAAA,eACC/K,IAAA,QACCuI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,eAAe,CAC/BxB,YAAY,CAAE,KACf,CAAE,CAAAV,QAAA,cAEF7K,KAAA,SAAMqI,KAAK,CAAE,CAAE2E,QAAQ,CAAE,MAAM,CAAE3M,KAAK,CAAE,SAAU,CAAE,CAAAwK,QAAA,EAClDf,QAAQ,CAAC,GAAC,CAACD,UAAU,EACjB,CAAC,CACH,CAAC,cACN/J,IAAA,QACCuI,KAAK,CAAE,CACNkE,MAAM,CAAE,KAAK,CACbpB,eAAe,CAAE,SAAS,CAC1BiB,YAAY,CAAE,QAAQ,CACtBI,QAAQ,CAAE,QACX,CAAE,CAAA3B,QAAA,cAEF/K,IAAA,QACCuI,KAAK,CAAE,CACNkE,MAAM,CAAE,MAAM,CACdpB,eAAe,EAAAjH,uBAAA,CAAEkC,sBAAsB,CAAC,CAAC,CAAC,UAAAlC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B6D,MAAM,UAAA5D,uBAAA,iBAAjCA,uBAAA,CAAmC6D,YAAY,CAChEoE,YAAY,CAAE,QAAQ,CACtBE,KAAK,CAAE,GAAIxC,QAAQ,CAAGD,UAAU,CAAI,GAAG,GACxC,CAAE,CACG,CAAC,CACH,CAAC,EACF,CAAC,EACF,CAAC,cAEN/J,IAAA,QACCuI,KAAK,CAAE,CACL6F,SAAS,CAAE,QAAQ,EAAA9J,uBAAA,CAAAgC,sBAAsB,CAAC,CAAC,CAAC,UAAAhC,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B2D,MAAM,UAAA1D,uBAAA,iBAAjCA,uBAAA,CAAmCkI,MAAM,GAAI,GAAG,aAAa,CAChFC,QAAQ,CAAE,MACV,CAAE,CACJR,SAAS,CAAC,eAAe,CAAAnB,QAAA,CAExBjE,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEG,GAAG,CAAEc,IAAS,OAAAsG,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,oBACnC1O,IAAA,QAAKkM,SAAS,CAAE,GAAG3E,UAAU,GAAKQ,IAAI,CAACN,EAAE,CAAG,cAAc,CAAG,EAAE,EAAG,CAAAsD,QAAA,cACjE/K,IAAA,QAECuI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAQ,CACvBG,OAAO,CAAE,qBAAqB,CAC9Be,MAAM,CAAE,SAAS,CACjB;AACAhB,YAAY,CAAE,mBACf,CAAE,CACFzB,OAAO,CAAEA,CAAA,GAAMf,YAAY,CAACxC,IAAI,CAACN,EAAE,CAAE,CAAAsD,QAAA,cAGrC7K,KAAA,QAAKqI,KAAK,CAAE,CAAEoG,WAAW,CAAE,MAAM,CAAEzD,OAAO,CAAE,MAAM,CAAE4B,GAAG,CAAE,KAAK,CAAED,aAAa,CAAE,QAAS,CAAE,CAAA9B,QAAA,eACzF7K,KAAA,QACCqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,eAAe,CAC/BT,KAAK,CAAE,MACR,CAAE,CAAAzB,QAAA,eAEF7K,KAAA,QACCqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB2B,GAAG,CAAE,MAAM,CACXD,aAAa,CAAE,KAAK,CACpBL,KAAK,CAAE,mBACR,CAAE,CAAAzB,QAAA,EAEDhD,IAAI,CAAC4B,IAAI,EAAI,MAAO,CAAA5B,IAAI,CAAC4B,IAAI,GAAK,QAAQ,cAC1C3J,IAAA,QACC4O,GAAG,CAAEvO,cAAc,CAAC0H,IAAI,CAAC4B,IAAI,CAAE,EAAA0E,uBAAA,CAAA/H,sBAAsB,CAAC,CAAC,CAAC,UAAA+H,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BtH,WAAW,UAAAuH,uBAAA,iBAAtCA,uBAAA,CAAwCO,gBAAgB,GAAI,MAAM,CAAE,CACnGC,GAAG,CAAC,MAAM,CACVvG,KAAK,CAAE,CAAEiE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CACzC,CAAC,cAEFzM,IAAA,QACCuI,KAAK,CAAE,CACNiE,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdvB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB8B,cAAc,CAAE,QACjB,CAAE,CAAAlC,QAAA,cAEF/K,IAAA,SAAMuI,KAAK,CAAE,CAAEiE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAO,CAAC,CACnD,CACL,cAEDzM,IAAA,SACCuI,KAAK,CAAE,CACNhI,KAAK,CAAE,EAAAgO,uBAAA,CAAAjI,sBAAsB,CAAC,CAAC,CAAC,UAAAiI,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BxH,WAAW,UAAAyH,uBAAA,iBAAtCA,uBAAA,CAAwCO,gBAAgB,GAAI,MAAM,CACzErC,QAAQ,CAAE,QAAQ,CAClBe,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YACZ,CAAE,CAAA5C,QAAA,CAEDhD,IAAI,CAACyB,KAAK,CACN,CAAC,EACH,CAAC,cACNxJ,IAAA,QAAA+K,QAAA,cACC/K,IAAA,CAACV,eAAe,EAEf8H,SAAS,CAAER,eAAe,CAACmB,IAAI,CAACN,EAAE,CAAE,CACpC6D,OAAO,CAAEA,CAAA,GAAM,CAAC,CAAE,CAClB0D,IAAI,CAAC,IAAI,EAHJjH,IAAI,CAACN,EAIV,CAAC,CACE,CAAC,EACF,CAAC,cACNzH,IAAA,QAAA+K,QAAA,cACC/K,IAAA,MACCuI,KAAK,CAAE,CACN2E,QAAQ,CAAE,MAAM,CAChB3M,KAAK,EAAAkO,uBAAA,CAAEnI,sBAAsB,CAAC,CAAC,CAAC,UAAAmI,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B1H,WAAW,UAAA2H,uBAAA,iBAAtCA,uBAAA,CAAwCO,sBAChD,CAAE,CACF/C,SAAS,CAAC,kBAAkB,CAAAnB,QAAA,CAE3BhD,IAAI,CAAC0B,WAAW,CACf,CAAC,CACA,CAAC,EACF,CAAC,EAlFD1B,IAAI,CAACN,EAmFN,CAAC,CACF,CAAC,EACN,CAAC,CACE,CAAC,EACF,CAAC,CAILvB,mBAAmB,eACnBhG,KAAA,QACCqI,KAAK,CAAE,CACNiE,KAAK,CAAE,KAAK,CACZQ,OAAO,CAAE,kBACV,CAAE,CACFd,SAAS,CAAC,cAAc,CAAAnB,QAAA,eAExB7K,KAAA,QACEqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CAEpB+D,YAAY,CAAE,KAAK,CACnB1C,KAAK,CAAE,MAAM,CACbM,GAAG,CAAE,KACN,CAAE,CAAA/B,QAAA,eAEF/K,IAAA,SACC4N,uBAAuB,CAAE,CAAEC,MAAM,CAAEjO,QAAS,CAAE,CAC9C2I,KAAK,CAAE,CACNuF,UAAU,CAAE,SAAS,CACrBxB,YAAY,CAAE,KAAK,CACnBU,OAAO,CAAE,KAAK,CACd9B,OAAO,CAAE,MAAM,CACf6C,MAAM,CAAE,SACT,CAAE,CACFzC,OAAO,CAAEX,cAAe,CACxB,CAAC,cACF3K,IAAA,SACC4N,uBAAuB,CAAE,CAAEC,MAAM,CAAElO,eAAgB,CAAE,CACrD2L,OAAO,CAAEd,WAAY,CACrBjC,KAAK,CAAE,CACNuF,UAAU,CAAE,SAAS,CACrBxB,YAAY,CAAE,KAAK,CACnBU,OAAO,CAAE,KAAK,CACd9B,OAAO,CAAE,MAAM,CACf6C,MAAM,CAAE,SACT,CAAE,CACF,CAAC,EACG,CAAC,cACR/N,IAAA,QACFuI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB0B,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,MAAM,CAAKL,MAAM,CAAE,mBAEzB,CAAE,CAAA1B,QAAA,cAGE7K,KAAA,QAAKqI,KAAK,CAAE,CACZmE,QAAQ,CAAE,aAAa,CAACxB,OAAO,CAAE,MAAM,CAC1CC,UAAU,CAAE,QAAQ,CACpB0B,aAAa,CAAE,QAAQ,CAACL,KAAK,CAAC,wBAAwB,CAAE,CAAAzB,QAAA,EACrD,CAAA5C,YAAY,SAAZA,YAAY,kBAAA3D,qBAAA,CAAZ2D,YAAY,CAAEyB,eAAe,UAAApF,qBAAA,iBAA7BA,qBAAA,CAA+BoD,MAAM,EAAG,CAAC,eACzC1H,KAAA,CAAAE,SAAA,EAAA2K,QAAA,EACE5C,YAAY,CAACyB,eAAe,CAACuF,IAAI,CAAEC,IAAS,OAAAC,UAAA,OAC5C,CAAAD,IAAI,SAAJA,IAAI,kBAAAC,UAAA,CAAJD,IAAI,CAAEE,MAAM,UAAAD,UAAA,iBAAZA,UAAA,CAAcE,UAAU,CAAC,YAAY,CAAC,EACvC,CAAC,eACAvP,IAAA,CAACR,aAAa,EACb2I,YAAY,CAAEA,YAAa,CAC3BZ,UAAU,CAAEA,UAAW,CACvBiI,MAAM,CAAErH,YAAY,CAACyB,eAAe,CAClCM,MAAM,CAAEkF,IAAS,OAAAK,WAAA,OAAK,CAAAL,IAAI,SAAJA,IAAI,kBAAAK,WAAA,CAAJL,IAAI,CAAEE,MAAM,UAAAG,WAAA,iBAAZA,WAAA,CAAcF,UAAU,CAAC,YAAY,CAAC,GAAC,CAC7DtI,GAAG,CAAEmI,IAAS,EAAKA,IAAI,CAACE,MAAM,CAAE,CAClC5I,WAAW,CAAEA,WAAY,CACzB,CACD,CAEAyB,YAAY,CAACyB,eAAe,CAACuF,IAAI,CAAEC,IAAS,OAAAM,WAAA,OAAK,CAAAN,IAAI,SAAJA,IAAI,kBAAAM,WAAA,CAAJN,IAAI,CAAEE,MAAM,UAAAI,WAAA,iBAAZA,WAAA,CAAcH,UAAU,CAAC,YAAY,CAAC,GAAC,EACxFpH,YAAY,CAACyB,eAAe,CAC1BM,MAAM,CAAEkF,IAAS,OAAAO,WAAA,OAAK,CAAAP,IAAI,SAAJA,IAAI,kBAAAO,WAAA,CAAJP,IAAI,CAAEE,MAAM,UAAAK,WAAA,iBAAZA,WAAA,CAAcJ,UAAU,CAAC,YAAY,CAAC,GAAC,CAC7DtI,GAAG,CAAC,CAACmI,IAAS,CAAEjI,KAAa,gBAC7BnH,IAAA,CAACP,WAAW,EAEXmQ,SAAS,CAAER,IAAI,CAACE,MAAO,CACvB5I,WAAW,CAAEA,WAAY,EAFpBS,KAGL,CACD,CAAC,EACH,CACF,CACA,CAAC,CAAAgB,YAAY,SAAZA,YAAY,kBAAA1D,sBAAA,CAAZ0D,YAAY,CAAEyB,eAAe,UAAAnF,sBAAA,iBAA7BA,sBAAA,CAA+BmD,MAAM,IAAK,CAAC,EAAI,EAACO,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEyB,eAAe,iBAC9E1J,KAAA,QAAKqI,KAAK,CAAE,CAAEiE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAA1B,QAAA,eAC9C/K,IAAA,SAAM4N,uBAAuB,CAAE,CAAEC,MAAM,CAAEnO,UAAW,CAAE,CAAE,CAAC,cACzDM,IAAA,QAAKuI,KAAK,CAAE,CAAEhI,KAAK,CAAE,SAAU,CAAE,CAAAwK,QAAA,CAAE1E,SAAS,CAAC,iDAAiD,CAAC,CAAM,CAAC,EAClG,CACL,cAEDrG,IAAA,QACCuI,KAAK,CAAE,CAAEiE,KAAK,CAAE,MAAM,CAAEhB,SAAS,CAAE,MAAO,CAAE,CAC5CU,SAAS,CAAC,eAAe,CAAAnB,QAAA,CAExB5C,YAAY,eACZnI,IAAA,QAAKuI,KAAK,CAAE,CAAEkE,MAAM,CAAE,MAAM,CAAEvB,OAAO,CAAE,MAAM,CAAE2B,aAAa,CAAE,QAAS,CAAE,CAAA9B,QAAA,cACxE7K,KAAA,QACCqI,KAAK,CAAE,CACNqE,SAAS,CAAEhC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCM,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,MACN,CAAE,CAAA/B,QAAA,eAEF/K,IAAA,QACCuI,KAAK,CAAE,CACN2E,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,GAAG,CACf5M,KAAK,CAAE,MAAM,CACbmM,QAAQ,CAAE,QAAQ,CAClBe,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YACZ,CAAE,CAAA5C,QAAA,CAED5C,YAAY,CAAC0B,UAAU,CACpB,CAAC,cAEN7J,IAAA,QACCkM,SAAS,CAAC,YAAY,CACtB3D,KAAK,CAAE,CAAEhI,KAAK,CAAE,SAAU,CAAE,CAAAwK,QAAA,CAE3B5C,YAAY,CAAC2B,gBAAgB,CAC1B,CAAC,EACF,CAAC,CACF,CACL,CACG,CAAC,EACD,CAAC,CACD,CAAC,cACP5J,KAAA,QACCqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACf4B,GAAG,CAAE,MAAM,CACX3B,UAAU,CAAE,QAAQ,CACpB+D,YAAY,CAAE,KAAK,CACnBW,aAAa,CAAE,MAChB,CAAE,CACF3D,SAAS,CAAC,cAAc,CAAAnB,QAAA,eAExB/K,IAAA,WACCuI,KAAK,CAAE,CACN8C,eAAe,EAAA3G,uBAAA,CAAE4B,sBAAsB,CAAC,CAAC,CAAC,CAAC2B,MAAM,UAAAvD,uBAAA,iBAAhCA,uBAAA,CAAkCwD,YAAY,CAC/DoE,YAAY,CAAE,MAAM,CACpBU,OAAO,CAAE,UAAU,CACnBzM,KAAK,CAAE,MAAM,CACb4L,MAAM,CAAE,MAAM,CACd4B,MAAM,CAAEtF,WAAW,CAAG,SAAS,CAAG,aACnC,CAAE,CACFqH,QAAQ,CAAE,CAACrH,WAAY,CAAAsC,QAAA,CAEtBtC,WAAW,CAAGpC,SAAS,CAAC,WAAW,CAAC,CAAGA,SAAS,CAAC,2BAA2B,CAAC,CACvE,CAAC,CACR,CAAA8B,YAAY,SAAZA,YAAY,kBAAAxD,sBAAA,CAAZwD,YAAY,CAAEyB,eAAe,UAAAjF,sBAAA,iBAA7BA,sBAAA,CAA+BiD,MAAM,EAAG,CAAC,eACzC5H,IAAA,WACCuI,KAAK,CAAE,CACN+D,YAAY,CAAE,MAAM,CACpBU,OAAO,CAAE,UAAU,CACnBzM,KAAK,EAAAqE,uBAAA,CAAE0B,sBAAsB,CAAC,CAAC,CAAC,UAAA1B,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BqD,MAAM,UAAApD,uBAAA,iBAAjCA,uBAAA,CAAmCqD,YAAY,CACtDiE,MAAM,CAAE,MAAM,CACd2B,UAAU,CAAE,SAAS,CACrBC,MAAM,CAAE,SACT,CAAE,CACFzC,OAAO,CAAGyE,CAAM,EAAKzF,qBAAqB,CAACnC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEV,EAAE,CAAE,CAAAsD,QAAA,CAE5D1E,SAAS,CAAC,mBAAmB,CAAC,CACxB,CACR,EACI,CAAC,EACD,CAEP,EAEG,CAAC,CACF,CAAC,CACF,CAAC,EACF,CACL,CAEAK,WAAW,eACX1G,IAAA,QACCuI,KAAK,CAAE,CACNyC,QAAQ,CAAE,OAAO,CACjBgF,GAAG,CAAE,CAAC,CACNjE,IAAI,CAAE,CAAC,CACPkE,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACT7E,eAAe,CAAE,oBAAoB,CACrCD,MAAM,CAAE,KACT,CAAE,CAAAL,QAAA,cAEF/K,IAAA,QACCuI,KAAK,CAAE,CACNyC,QAAQ,CAAE,OAAO,CACjBC,KAAK,CAAE,CAAC,CACRC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB;AACAC,MAAM,CAAE,EACT,CAAE,CAAAL,QAAA,cAEF/K,IAAA,QACCuI,KAAK,CAAE,CACNgD,SAAS,CAAE,+EAA+E,CAC1FH,MAAM,CAAE,CAAC,CACTI,SAAS,CAAE,IAAI,CACfC,YAAY,CAAE,IAAI,CAClB;AACA;AACAP,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB+D,YAAY,CAAE,QAAQ,CACtB1C,KAAK,CAAE,MACR,CAAE,CACFN,SAAS,CAAC,gBAAgB,CAAAnB,QAAA,cAE1B/K,IAAA,QACCuI,KAAK,CAAE,CACN8C,eAAe,EAAAvG,uBAAA,CAAEwB,sBAAsB,CAAC,CAAC,CAAC,UAAAxB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BmD,MAAM,UAAAlD,uBAAA,iBAAjCA,uBAAA,CAAmCsG,eAAe,CACnEc,MAAM,CAAE,IAAAnH,uBAAA,CAAGsB,sBAAsB,CAAC,CAAC,CAAC,UAAAtB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BiD,MAAM,UAAAhD,uBAAA,iBAAjCA,uBAAA,CAAmCmH,WAAW,aAAAlH,uBAAA,CAAYoB,sBAAsB,CAAC,CAAC,CAAC,UAAApB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B+C,MAAM,UAAA9C,uBAAA,iBAAjCA,uBAAA,CAAmCkH,WAAW,EAAE,CACrHC,YAAY,CAAE,IAAAlH,uBAAA,CAAGkB,sBAAsB,CAAC,CAAC,CAAC,UAAAlB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2B6C,MAAM,UAAA5C,uBAAA,iBAAjCA,uBAAA,CAAmCkH,YAAY,IAAI,CACpEC,KAAK,CAAE,sBAAsB,CAC7BC,MAAM,CAAE,qBAAqB,CAC7BC,QAAQ,CAAE,MACX,CAAE,CAAA3B,QAAA,cAEF/K,IAAA,QACCuI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfuB,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,MACR,CAAE,CACFN,SAAS,CAAC,kBAAkB,CAAAnB,QAAA,cAO5B/K,IAAA,QACCuI,KAAK,CAAE,CACNiE,KAAK,CAAE,MAAM,CACbQ,OAAO,CAAE,kBACV,CAAE,CACFd,SAAS,CAAC,cAAc,CAAAnB,QAAA,cAExB7K,KAAA,QAAKqI,KAAK,CAAE,CAAE2C,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE0B,aAAa,CAAE,QAAQ,CAAEC,GAAG,CAAE,MAAO,CAAE,CAAA/B,QAAA,eAC3F/K,IAAA,QACCuI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB+D,YAAY,CAAE,KAAK,CACnB1C,KAAK,CAAE,MAAM,CACbM,GAAG,CAAE,KACN,CAAE,CACFxB,OAAO,CAAEb,cAAe,CAAAM,QAAA,cAExB/K,IAAA,SACC4N,uBAAuB,CAAE,CAAEC,MAAM,CAAElO,eAAgB,CAAE,CACrD4I,KAAK,CAAE,CACNuF,UAAU,CAAE,SAAS,CACrBxB,YAAY,CAAE,KAAK,CACnBU,OAAO,CAAE,KAAK,CACd9B,OAAO,CAAE,MAAM,CACf6C,MAAM,CAAE,SACT,CAAE,CACF,CAAC,CACE,CAAC,CACL,CAAA5F,YAAY,SAAZA,YAAY,kBAAA7C,sBAAA,CAAZ6C,YAAY,CAAEyB,eAAe,UAAAtE,sBAAA,iBAA7BA,sBAAA,CAA+BsC,MAAM,IAAK,CAAC,eAC3C1H,KAAA,QAAKqI,KAAK,CAAE,CAAEiE,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAAA1B,QAAA,eAC9C/K,IAAA,SAAM4N,uBAAuB,CAAE,CAAEC,MAAM,CAAEnO,UAAW,CAAE,CAAE,CAAC,cACzDM,IAAA,QAAKuI,KAAK,CAAE,CAAEhI,KAAK,CAAE,SAAU,CAAE,CAAAwK,QAAA,CAAE1E,SAAS,CAAC,iDAAiD,CAAC,CAAM,CAAC,EAClG,CACL,CACA,CAAA8B,YAAY,SAAZA,YAAY,kBAAA5C,sBAAA,CAAZ4C,YAAY,CAAEyB,eAAe,UAAArE,sBAAA,iBAA7BA,sBAAA,CAA+BqC,MAAM,EAAG,CAAC,eACzC1H,KAAA,CAAAE,SAAA,EAAA2K,QAAA,EACE5C,YAAY,CAACyB,eAAe,CAACuF,IAAI,CAAEC,IAAS,OAAAe,WAAA,OAC5C,CAAAf,IAAI,SAAJA,IAAI,kBAAAe,WAAA,CAAJf,IAAI,CAAEE,MAAM,UAAAa,WAAA,iBAAZA,WAAA,CAAcZ,UAAU,CAAC,YAAY,CAAC,EACvC,CAAC,eACAvP,IAAA,CAACR,aAAa,EACb2I,YAAY,CAAEA,YAAa,CAC3BZ,UAAU,CAAEA,UAAW,CACvBiI,MAAM,CAAErH,YAAY,CAACyB,eAAe,CAClCM,MAAM,CAAEkF,IAAS,OAAAgB,WAAA,OAAK,CAAAhB,IAAI,SAAJA,IAAI,kBAAAgB,WAAA,CAAJhB,IAAI,CAAEE,MAAM,UAAAc,WAAA,iBAAZA,WAAA,CAAcb,UAAU,CAAC,YAAY,CAAC,GAAC,CAC7DtI,GAAG,CAAEmI,IAAS,EAAKA,IAAI,CAACE,MAAM,CAAE,CAClC5I,WAAW,CAAEA,WAAY,CACzB,CACD,CAEAyB,YAAY,CAACyB,eAAe,CAACuF,IAAI,CAAEC,IAAS,OAAAiB,WAAA,OAAK,CAAAjB,IAAI,SAAJA,IAAI,kBAAAiB,WAAA,CAAJjB,IAAI,CAAEE,MAAM,UAAAe,WAAA,iBAAZA,WAAA,CAAcd,UAAU,CAAC,YAAY,CAAC,GAAC,EACxFpH,YAAY,CAACyB,eAAe,CAC1BM,MAAM,CAAEkF,IAAS,OAAAkB,UAAA,OAAK,CAAAlB,IAAI,SAAJA,IAAI,kBAAAkB,UAAA,CAAJlB,IAAI,CAAEmB,MAAM,UAAAD,UAAA,iBAAZA,UAAA,CAAcf,UAAU,CAAC,YAAY,CAAC,GAAC,CAC7DtI,GAAG,CAAC,CAACmI,IAAS,CAAEjI,KAAa,gBAC7BnH,IAAA,CAACP,WAAW,EAEXmQ,SAAS,CAAER,IAAI,CAACmB,MAAO,CACvB7J,WAAW,CAAEA,WAAY,EAFpBS,KAGL,CACD,CAAC,EACH,CACF,cACDnH,IAAA,QACCuI,KAAK,CAAE,CAAEiE,KAAK,CAAE,MAAM,CAAEhB,SAAS,CAAE,MAAO,CAAE,CAC5CU,SAAS,CAAC,eAAe,CAAAnB,QAAA,CAExB5C,YAAY,eACZjI,KAAA,QAAKqI,KAAK,CAAE,CAAEkE,MAAM,CAAE,MAAM,CAAEvB,OAAO,CAAE,MAAM,CAAE2B,aAAa,CAAE,QAAS,CAAE,CAAA9B,QAAA,eACxE7K,KAAA,QACCqI,KAAK,CAAE,CACNqE,SAAS,CAAEhC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCM,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,MAAM,CACXN,KAAK,CAAE,MACR,CAAE,CAAAzB,QAAA,eAEF/K,IAAA,QACCuI,KAAK,CAAE,CACN2E,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,GAAG,CACf5M,KAAK,CAAE,MAAM,CACbmM,QAAQ,CAAE,QAAQ,CAClBe,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,YACZ,CAAE,CAAA5C,QAAA,CAED5C,YAAY,CAAC0B,UAAU,CACpB,CAAC,cAEN7J,IAAA,QAAKkM,SAAS,CAAC,YAAY,CAAAnB,QAAA,CAAE5C,YAAY,CAAC2B,gBAAgB,CAAM,CAAC,EAC7D,CAAC,cAEN5J,KAAA,QACCqI,KAAK,CAAE,CACN2C,OAAO,CAAE,MAAM,CACf4B,GAAG,CAAE,MAAM,CACX3B,UAAU,CAAE,QAAQ,CACpB+D,YAAY,CAAE,KAAK,CACnBW,aAAa,CAAE,MAChB,CAAE,CACF3D,SAAS,CAAC,cAAc,CAAAnB,QAAA,eAExB/K,IAAA,WACCuI,KAAK,CAAE,CACN8C,eAAe,EAAA7F,uBAAA,CAAEc,sBAAsB,CAAC,CAAC,CAAC,UAAAd,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2ByC,MAAM,UAAAxC,uBAAA,iBAAjCA,uBAAA,CAAmCyC,YAAY,CAChEoE,YAAY,CAAE,MAAM,CACpBU,OAAO,CAAE,UAAU,CACnBzM,KAAK,CAAE,MAAM,CACb4L,MAAM,CAAE,MAAM,CACd4B,MAAM,CAAEtF,WAAW,CAAG,SAAS,CAAG,aACnC,CAAE,CACF6C,OAAO,CAAEZ,cAAe,CACxBoF,QAAQ,CAAE,CAACrH,WAAY,CAAAsC,QAAA,CAEtBtC,WAAW,CAAGpC,SAAS,CAAC,WAAW,CAAC,CAAGA,SAAS,CAAC,2BAA2B,CAAC,CACvE,CAAC,CACR,CAAA8B,YAAY,SAAZA,YAAY,kBAAAzC,sBAAA,CAAZyC,YAAY,CAAEyB,eAAe,UAAAlE,sBAAA,iBAA7BA,sBAAA,CAA+BkC,MAAM,EAAG,CAAC,eAC7C5H,IAAA,WACCuI,KAAK,CAAE,CACN+D,YAAY,CAAE,MAAM,CACpBU,OAAO,CAAE,UAAU,CACnBzM,KAAK,EAAAoF,uBAAA,CAAEW,sBAAsB,CAAC,CAAC,CAAC,UAAAX,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA2BsC,MAAM,UAAArC,uBAAA,iBAAjCA,uBAAA,CAAmCsC,YAAY,CACtDiE,MAAM,CAAE,MAAM,CACd2B,UAAU,CAAE,SAAS,CACrBC,MAAM,CAAE,SACT,CAAE,CACFzC,OAAO,CAAGyE,CAAM,EAAKzF,qBAAqB,CAACnC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEV,EAAE,CAAE,CAAAsD,QAAA,CAExD1E,SAAS,CAAC,mBAAmB,CAAC,CAC5B,CACR,EACO,CAAC,EACF,CACL,CACG,CAAC,EACF,CAAC,CACF,CAAC,CAGF,CAAC,CACF,CAAC,CACF,CAAC,CACF,CAAC,CACF,CACL,EACA,CAAC,CAEL,CAAC,CACF,cAAe,CAAAhF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}