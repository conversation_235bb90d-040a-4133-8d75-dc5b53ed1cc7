{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\tours\\\\tourTemplate.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport useDrawerStore from '../../store/drawerStore';\nimport { ToursAnnouncementsIcon, ToursBannerIcon, ToursHotspotIcon, ToursTooltipIcon } from '../../assets/icons/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeatureSelectionModal = ({\n  isOpen,\n  onClose,\n  guideName,\n  setStepData,\n  stepData,\n  count\n}) => {\n  _s();\n  const [hoveredItem, setHoveredItem] = useState();\n  const [selectedStepType, setSelectedStepType] = useState(null); // Track selected step type\n  const {\n    setSelectedTemplate,\n    setBannerPopup,\n    setSelectedTemplateTour,\n    setSteps,\n    steps,\n    setTooltipCount,\n    tooltipCount,\n    HotspotGuideDetails,\n    setElementSelected,\n    TooltipGuideDetails,\n    HotspotGuideDetailsNew,\n    setSelectedStepTypeHotspot,\n    selectedTemplate,\n    selectedTemplateTour,\n    createWithAI\n  } = useDrawerStore(state => state);\n  const [selectedStepStyle, setSelectedStepStyle] = useState({});\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: ToursAnnouncementsIcon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 15\n    }, this),\n    title: \"Announcement\",\n    description: \"An announcement of any new feature\",\n    action: () => {\n      setSelectedStepType(\"Announcement\");\n      setSelectedStepStyle({\n        borderColor: \"#5F9EA0\",\n        background: \"#F6FFFF\"\n      });\n    }\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: ToursHotspotIcon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 15\n    }, this),\n    title: \"Hotspot\",\n    description: \"Offer users quick tips\",\n    action: () => {\n      setSelectedStepType(\"Hotspot\");\n    }\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: ToursBannerIcon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 15\n    }, this),\n    title: \"Banner\",\n    description: \"Create in-line banners that get noticed\",\n    action: () => {\n      setSelectedStepType(\"Banner\");\n    }\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: ToursTooltipIcon\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 15\n    }, this),\n    title: \"Tooltip\",\n    description: \"Anchored to selected elements\",\n    action: () => {\n      setSelectedStepType(\"Tooltip\");\n    }\n  }];\n  if (!isOpen) return null;\n  const handleNextClick = () => {\n    if (selectedTemplate === \"Tour\" && selectedStepType === \"Banner\") {\n      let styleTag = document.getElementById(\"dynamic-body-style\");\n      const bodyElement = document.body;\n\n      // Add a dynamic class to the body\n      bodyElement.classList.add(\"dynamic-body-style\");\n      if (!styleTag) {\n        styleTag = document.createElement(\"style\");\n        styleTag.id = \"dynamic-body-style\";\n\n        // Add styles for body and nested elements\n        let styles = `\n\t\t\t\t\t.dynamic-body-style {\n\t\t\t\t\t\tpadding-top: 50px !important;\n\t\t\t\t\t\tmax-height:calc(100% - 55px);\n\t\t\t\t\t}\n\n\t\t\t\t`;\n        styleTag.innerHTML = styles;\n        document.head.appendChild(styleTag);\n      }\n    }\n    if (selectedStepType) {\n      // Based on selectedStepType, navigate and update steps\n      if (selectedStepType === \"Announcement\") {\n        TooltipGuideDetails(true); // BUGFIX: Preserve overlay settings\n        setSelectedTemplateTour(\"Announcement\", true); // BUGFIX: Preserve overlay settings\n        setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\n        setStepData({\n          ...stepData,\n          type: \"Announcement\"\n        });\n      } else if (selectedStepType === \"Hotspot\") {\n        HotspotGuideDetails();\n        setSelectedTemplateTour(\"Hotspot\", true); // BUGFIX: Preserve overlay settings\n        setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\n        setSelectedStepTypeHotspot(true);\n        setStepData({\n          ...stepData,\n          type: \"Hotspot\"\n        });\n        setTooltipCount(tooltipCount + 1);\n        setElementSelected(false);\n      } else if (selectedStepType === \"Banner\") {\n        TooltipGuideDetails(true); // BUGFIX: Preserve overlay settings\n        setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\n        setSelectedTemplateTour(\"Banner\", true); // BUGFIX: Preserve overlay settings\n        setStepData({\n          ...stepData,\n          type: \"Banner\"\n        });\n        // Reset all banner canvas settings to defaults for new banner steps\n        useDrawerStore.getState().resetBannerCanvasToDefaults();\n        setBannerPopup(true);\n      } else if (selectedStepType === \"Tooltip\") {\n        TooltipGuideDetails(true); // BUGFIX: Preserve overlay settings\n        setSelectedTemplateTour(\"Tooltip\", true); // BUGFIX: Preserve overlay settings\n        setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\n        setStepData({\n          ...stepData,\n          type: \"Tooltip\"\n        });\n        setTooltipCount(tooltipCount + 1);\n      }\n      const updatedSteps = steps.map(step => ({\n        ...step,\n        stepType: selectedStepType\n      }));\n      setSteps(updatedSteps);\n      onClose(); // Close the modal after proceeding\n    }\n  };\n  if (createWithAI) {\n    onClose();\n    return null;\n  }\n  const isSelected = title => selectedStepType === title;\n  const isHovered = index => hoveredItem === index;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      zIndex: 999,\n      backgroundColor: \"rgba(0, 0, 0, 0.5)\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-tours-container\",\n      style: {\n        width: \"100%\",\n        maxWidth: \"663px\",\n        backgroundColor: \"white\",\n        boxShadow: \"0 2px 10px rgba(0,0,0,0.1)\",\n        border: \"1px solid #e0e0e0\",\n        height: \"365px\",\n        borderRadius: \"12px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-tour-header\",\n        style: {\n          textAlign: \"left\",\n          padding: \"10px 15px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"qadpt-title\",\n            style: {\n              fontSize: \"20px\",\n              fontWeight: 600\n            },\n            children: guideName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              backgroundColor: \"#C8E7E8\",\n              borderRadius: \"4px\",\n              fontSize: \"10px\",\n              width: \"45px\",\n              height: \"20px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              marginLeft: \"8px\"\n            },\n            children: \"Step-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: \"13px\",\n            color: \"#B1B1B1\",\n            lineHeight: \"19.5px\",\n            letterSpacing: \"0.3px\"\n          },\n          children: \"Choose Step-1: Tour Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-tours-content\",\n        style: {\n          display: \"flex\",\n          flexDirection: \"row\",\n          justifyContent: \"center\",\n          gap: \"10px\",\n          padding: \"10px 15px\"\n        },\n        children: features.map((feature, index) => {\n          const isSelected = selectedStepType === feature.title;\n          const isHovered = hoveredItem === index;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => setSelectedStepType(feature.title),\n            onMouseEnter: () => setHoveredItem(index),\n            onMouseLeave: () => setHoveredItem(null),\n            style: {\n              width: \"145px\",\n              height: \"218px\",\n              textAlign: \"center\",\n              cursor: \"pointer\",\n              borderRadius: \"7px\",\n              padding: \"12px 10px\",\n              border: \"0.5px solid\",\n              borderColor: isSelected || isHovered ? \"#5F9EA0\" : \"#E0E0E0\",\n              background: isSelected || isHovered ? \"#F6FFFF\" : \"#fff\",\n              transition: \"all 0.2s ease-in-out\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: \"24px\",\n                fontWeight: \"600px\",\n                marginBlock: \"5px\",\n                height: \"90px\"\n              },\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: \"16px\",\n                fontWeight: \"600\",\n                marginBottom: \"10px\"\n              },\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: \"14px\",\n                lineHeight: \"16px\",\n                letterSpacing: \"0.3px\",\n                textAlign: \"center\"\n              },\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-tours-actions\",\n        style: {\n          padding: \"10px 15px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNextClick,\n          disabled: !selectedStepType,\n          style: {\n            padding: \"8px 32px\",\n            backgroundColor: \"#5F9EA0\",\n            opacity: selectedStepType ? \"1\" : \"0.5\",\n            color: \"#fff\",\n            borderRadius: \"7px\",\n            fontSize: \"14px\",\n            fontWeight: 500,\n            // cursor: selectedStepType ? \"pointer\" : \"not-allowed\",\n            transition: \"background-color 0.3s ease\",\n            border: \"none\",\n            cursor: \"pointer\"\n          },\n          children: \"NEXT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 7\n  }, this);\n};\n_s(FeatureSelectionModal, \"yuntHQrnE8BGhZ2QnKrcS62R0Uk=\", false, function () {\n  return [useDrawerStore];\n});\n_c = FeatureSelectionModal;\nexport default FeatureSelectionModal;\nvar _c;\n$RefreshReg$(_c, \"FeatureSelectionModal\");", "map": {"version": 3, "names": ["React", "useState", "useDrawerStore", "ToursAnnouncementsIcon", "ToursBannerIcon", "ToursHotspotIcon", "ToursTooltipIcon", "jsxDEV", "_jsxDEV", "FeatureSelectionModal", "isOpen", "onClose", "guideName", "setStepData", "stepData", "count", "_s", "hoveredItem", "setHoveredItem", "selectedStepType", "setSelectedStepType", "setSelectedTemplate", "setBannerPopup", "setSelectedTemplateTour", "setSteps", "steps", "setTooltipCount", "tooltipCount", "HotspotGuideDetails", "setElementSelected", "TooltipGuideDetails", "HotspotGuideDetailsNew", "setSelectedStepTypeHotspot", "selectedTemplate", "selectedTemplateTour", "createWithAI", "state", "selectedStepStyle", "setSelectedStepStyle", "features", "icon", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "action", "borderColor", "background", "handleNextClick", "styleTag", "document", "getElementById", "bodyElement", "body", "classList", "add", "createElement", "id", "styles", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "type", "getState", "resetBannerCanvasToDefaults", "updatedSteps", "map", "step", "stepType", "isSelected", "isHovered", "index", "style", "position", "top", "left", "right", "bottom", "display", "alignItems", "justifyContent", "zIndex", "backgroundColor", "children", "className", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "border", "height", "borderRadius", "textAlign", "padding", "fontSize", "fontWeight", "marginLeft", "color", "lineHeight", "letterSpacing", "flexDirection", "gap", "feature", "onClick", "onMouseEnter", "onMouseLeave", "cursor", "transition", "marginBlock", "marginBottom", "disabled", "opacity", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/tours/tourTemplate.tsx"], "sourcesContent": ["\r\n\r\n\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Card, CardContent } from \"@mui/material\";\r\nimport { Button } from \"@mui/material\";\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport { ToursAnnouncementsIcon, ToursBannerIcon, ToursHotspotIcon, ToursTooltipIcon } from '../../assets/icons/icons';\r\n\r\n\r\nconst FeatureSelectionModal: React.FC<{ isOpen: boolean; onClose: () => void; guideName: any; setStepData: any; stepData:any,count:any}> = ({ isOpen, onClose,guideName,setStepData ,stepData,count}) => {\r\n    const [hoveredItem, setHoveredItem] = useState<Number|null>();\r\n    const [selectedStepType, setSelectedStepType] = useState<string | null>(null); // Track selected step type\r\n    const {\r\n        setSelectedTemplate,\r\n        setBannerPopup,\r\n      setSelectedTemplateTour,\r\n      setSteps,\r\n      steps,\r\n      setTooltipCount,\r\n      tooltipCount,\r\n      HotspotGuideDetails,\r\n      setElementSelected,\r\n      TooltipGuideDetails,\r\n      HotspotGuideDetailsNew,\r\n      setSelectedStepTypeHotspot,\r\n      selectedTemplate,\r\n      selectedTemplateTour,\r\n      createWithAI\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const [selectedStepStyle, setSelectedStepStyle] = useState({});\r\n    const features = [\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursAnnouncementsIcon }} />,\r\n        title: \"Announcement\",\r\n        description: \"An announcement of any new feature\",\r\n        action: () => {\r\n          setSelectedStepType(\"Announcement\");\r\n          setSelectedStepStyle({\r\n            borderColor: \"#5F9EA0\",\r\n            background: \"#F6FFFF\",\r\n          });\r\n        },\r\n      },\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursHotspotIcon }} />,\r\n        title: \"Hotspot\",\r\n        description: \"Offer users quick tips\",\r\n        action: () => {\r\n          setSelectedStepType(\"Hotspot\");\r\n        },\r\n      },\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursBannerIcon }} />,\r\n        title: \"Banner\",\r\n        description: \"Create in-line banners that get noticed\",\r\n        action: () => {\r\n          setSelectedStepType(\"Banner\");\r\n        },\r\n      },\r\n      {\r\n        icon: <span dangerouslySetInnerHTML={{ __html: ToursTooltipIcon }} />,\r\n        title: \"Tooltip\",\r\n        description: \"Anchored to selected elements\",\r\n        action: () => {\r\n          setSelectedStepType(\"Tooltip\");\r\n        },\r\n      },\r\n    ];\r\n\r\n    if (!isOpen) return null;\r\n  const handleNextClick = () => {\r\n\r\n    if ( (selectedTemplate===\"Tour\" &&(selectedStepType===\"Banner\"))) {\r\n\t\t\tlet styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n\t\t\tconst bodyElement = document.body;\r\n\r\n\t\t\t// Add a dynamic class to the body\r\n\t\t\tbodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n\t\t\tif (!styleTag) {\r\n\t\t\t\tstyleTag = document.createElement(\"style\");\r\n\t\t\t\tstyleTag.id = \"dynamic-body-style\";\r\n\r\n\t\t\t\t// Add styles for body and nested elements\r\n\t\t\t\tlet styles = `\r\n\t\t\t\t\t.dynamic-body-style {\r\n\t\t\t\t\t\tpadding-top: 50px !important;\r\n\t\t\t\t\t\tmax-height:calc(100% - 55px);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t`;\r\n\r\n\t\t\t\tstyleTag.innerHTML = styles;\r\n\t\t\t\tdocument.head.appendChild(styleTag);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n      if (selectedStepType) {\r\n        // Based on selectedStepType, navigate and update steps\r\n        if (selectedStepType === \"Announcement\") {\r\n          TooltipGuideDetails(true); // BUGFIX: Preserve overlay settings\r\n          setSelectedTemplateTour(\"Announcement\", true); // BUGFIX: Preserve overlay settings\r\n          setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\r\n          setStepData({ ...stepData, type: \"Announcement\" });\r\n        } else if (selectedStepType === \"Hotspot\") {\r\n          HotspotGuideDetails();\r\n          setSelectedTemplateTour(\"Hotspot\", true); // BUGFIX: Preserve overlay settings\r\n          setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\r\n          setSelectedStepTypeHotspot(true);\r\n          setStepData({ ...stepData, type: \"Hotspot\" });\r\n          setTooltipCount(tooltipCount + 1);\r\n          setElementSelected(false);\r\n        } else if (selectedStepType === \"Banner\") {\r\n          TooltipGuideDetails(true); // BUGFIX: Preserve overlay settings\r\n          setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\r\n          setSelectedTemplateTour(\"Banner\", true); // BUGFIX: Preserve overlay settings\r\n          setStepData({ ...stepData, type: \"Banner\" });\r\n          // Reset all banner canvas settings to defaults for new banner steps\r\n          useDrawerStore.getState().resetBannerCanvasToDefaults();\r\n          setBannerPopup(true);\r\n        } else if (selectedStepType === \"Tooltip\") {\r\n          TooltipGuideDetails(true); // BUGFIX: Preserve overlay settings\r\n          setSelectedTemplateTour(\"Tooltip\", true); // BUGFIX: Preserve overlay settings\r\n          setSelectedTemplate(\"Tour\", true); // BUGFIX: Preserve overlay settings\r\n          setStepData({ ...stepData, type: \"Tooltip\" });\r\n          setTooltipCount(tooltipCount + 1);\r\n        }\r\n\r\n        const updatedSteps = steps.map(step => ({\r\n          ...step,\r\n          stepType: selectedStepType,\r\n        }));\r\n\r\n        setSteps(updatedSteps);\r\n        onClose(); // Close the modal after proceeding\r\n      }\r\n    };\r\n    if (createWithAI) {\r\n      onClose();\r\n      return null;\r\n  }\r\n    const isSelected = (title: string) => selectedStepType === title;\r\n    const isHovered = (index: number) => hoveredItem === index;\r\n    return (\r\n\r\n      <div\r\n      style={{\r\n        position: \"fixed\",\r\n        top: 0,\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0,\r\n        display: \"flex\",\r\n        alignItems: \"center\",\r\n        justifyContent: \"center\",\r\n        zIndex: 999,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n      }}\r\n    >\r\n      <div\r\n        className=\"qadpt-tours-container\"\r\n        style={{\r\n          width: \"100%\",\r\n          maxWidth: \"663px\",\r\n          backgroundColor: \"white\",\r\n          boxShadow: \"0 2px 10px rgba(0,0,0,0.1)\",\r\n          border: \"1px solid #e0e0e0\",\r\n          height: \"365px\",\r\n          borderRadius: \"12px\",\r\n        }}\r\n      >\r\n        {/* Header Section */}\r\n        <div\r\n          className=\"qadpt-tour-header\"\r\n          style={{\r\n            textAlign: \"left\",\r\n            padding: \"10px 15px\",\r\n          }}\r\n        >\r\n          <div\r\n            style={{\r\n              display: \"flex\",\r\n              alignItems: \"center\",\r\n            }}\r\n          >\r\n            <span\r\n              className=\"qadpt-title\"\r\n              style={{\r\n                fontSize: \"20px\",\r\n                fontWeight: 600,\r\n              }}\r\n            >\r\n              {guideName}\r\n            </span>\r\n            <span\r\n              style={{\r\n                backgroundColor: \"#C8E7E8\",\r\n                borderRadius: \"4px\",\r\n                fontSize: \"10px\",\r\n                width: \"45px\",\r\n                height: \"20px\",\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"center\",\r\n                marginLeft: \"8px\",\r\n              }}\r\n            >\r\n              Step-1\r\n            </span>\r\n          </div>\r\n          <div\r\n            style={{\r\n              fontSize: \"13px\",\r\n              color: \"#B1B1B1\",\r\n              lineHeight: \"19.5px\",\r\n              letterSpacing: \"0.3px\",\r\n            }}\r\n          >\r\n            Choose Step-1: Tour Type\r\n          </div>\r\n        </div>\r\n\r\n        {/* Step Selection Section */}\r\n        <div\r\n          className=\"qadpt-tours-content\"\r\n          style={{\r\n            display: \"flex\",\r\n            flexDirection: \"row\",\r\n            justifyContent: \"center\",\r\n            gap: \"10px\",\r\n            padding: \"10px 15px\",\r\n          }}\r\n        >\r\n          {features.map((feature, index) => {\r\n            const isSelected = selectedStepType === feature.title;\r\n            const isHovered = hoveredItem === index;\r\n\r\n            return (\r\n              <div\r\n                key={index}\r\n                onClick={() => setSelectedStepType(feature.title)}\r\n                onMouseEnter={() => setHoveredItem(index)}\r\n                onMouseLeave={() => setHoveredItem(null)}\r\n                style={{\r\n                  width: \"145px\",\r\n                  height: \"218px\",\r\n                  textAlign: \"center\",\r\n                  cursor: \"pointer\",\r\n                  borderRadius: \"7px\",\r\n                  padding: \"12px 10px\",\r\n                  border: \"0.5px solid\",\r\n                  borderColor: isSelected || isHovered ? \"#5F9EA0\" : \"#E0E0E0\",\r\n                  background: isSelected || isHovered ? \"#F6FFFF\" : \"#fff\",\r\n                  transition: \"all 0.2s ease-in-out\",\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    fontSize: \"24px\",\r\n                    fontWeight: \"600px\",\r\n                    marginBlock: \"5px\",\r\n                    height: \"90px\",\r\n                  }}\r\n                >\r\n                  {feature.icon}\r\n                </div>\r\n                <div\r\n                  style={{\r\n                    fontSize: \"16px\",\r\n                    fontWeight: \"600\",\r\n                    marginBottom: \"10px\",\r\n                  }}\r\n                >\r\n                  {feature.title}\r\n                </div>\r\n                <div\r\n                  style={{\r\n                    fontSize: \"14px\",\r\n                    lineHeight: \"16px\",\r\n                    letterSpacing: \"0.3px\",\r\n                    textAlign: \"center\",\r\n                  }}\r\n                >\r\n                  {feature.description}\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Footer Action Buttons */}\r\n        <div className=\"qadpt-tours-actions\" style={{ padding: \"10px 15px\" }}>\r\n          <button\r\n            onClick={handleNextClick}\r\n            disabled={!selectedStepType}\r\n            style={{\r\n              padding: \"8px 32px\",\r\n              backgroundColor: \"#5F9EA0\",\r\n              opacity:selectedStepType?\"1\":\"0.5\",\r\n              color: \"#fff\",\r\n              borderRadius: \"7px\",\r\n              fontSize: \"14px\",\r\n              fontWeight: 500,\r\n              // cursor: selectedStepType ? \"pointer\" : \"not-allowed\",\r\n              transition: \"background-color 0.3s ease\",\r\n              border: \"none\",\r\n              cursor:\"pointer\",\r\n            }}\r\n          >\r\n            NEXT\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FeatureSelectionModal;"], "mappings": ";;AAIA,OAAOA,KAAK,IAAeC,QAAQ,QAAQ,OAAO;AAGlD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE,SAASC,sBAAsB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGvH,MAAMC,qBAAkI,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAACC,SAAS;EAACC,WAAW;EAAEC,QAAQ;EAACC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrM,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAc,CAAC;EAC7D,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC,CAAC,CAAC;EAC/E,MAAM;IACFoB,mBAAmB;IACnBC,cAAc;IAChBC,uBAAuB;IACvBC,QAAQ;IACRC,KAAK;IACLC,eAAe;IACfC,YAAY;IACZC,mBAAmB;IACnBC,kBAAkB;IAClBC,mBAAmB;IACnBC,sBAAsB;IACtBC,0BAA0B;IAC1BC,gBAAgB;IAChBC,oBAAoB;IACpBC;EACJ,CAAC,GAAGjC,cAAc,CAAEkC,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMsC,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEhC,OAAA;MAAMiC,uBAAuB,EAAE;QAAEC,MAAM,EAAEvC;MAAuB;IAAE;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3EC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,oCAAoC;IACjDC,MAAM,EAAEA,CAAA,KAAM;MACZ7B,mBAAmB,CAAC,cAAc,CAAC;MACnCkB,oBAAoB,CAAC;QACnBY,WAAW,EAAE,SAAS;QACtBC,UAAU,EAAE;MACd,CAAC,CAAC;IACJ;EACF,CAAC,EACD;IACEX,IAAI,eAAEhC,OAAA;MAAMiC,uBAAuB,EAAE;QAAEC,MAAM,EAAErC;MAAiB;IAAE;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrEC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,wBAAwB;IACrCC,MAAM,EAAEA,CAAA,KAAM;MACZ7B,mBAAmB,CAAC,SAAS,CAAC;IAChC;EACF,CAAC,EACD;IACEoB,IAAI,eAAEhC,OAAA;MAAMiC,uBAAuB,EAAE;QAAEC,MAAM,EAAEtC;MAAgB;IAAE;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpEC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,yCAAyC;IACtDC,MAAM,EAAEA,CAAA,KAAM;MACZ7B,mBAAmB,CAAC,QAAQ,CAAC;IAC/B;EACF,CAAC,EACD;IACEoB,IAAI,eAAEhC,OAAA;MAAMiC,uBAAuB,EAAE;QAAEC,MAAM,EAAEpC;MAAiB;IAAE;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrEC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,+BAA+B;IAC5CC,MAAM,EAAEA,CAAA,KAAM;MACZ7B,mBAAmB,CAAC,SAAS,CAAC;IAChC;EACF,CAAC,CACF;EAED,IAAI,CAACV,MAAM,EAAE,OAAO,IAAI;EAC1B,MAAM0C,eAAe,GAAGA,CAAA,KAAM;IAE5B,IAAMnB,gBAAgB,KAAG,MAAM,IAAId,gBAAgB,KAAG,QAAS,EAAG;MACnE,IAAIkC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB;MAChF,MAAMC,WAAW,GAAGF,QAAQ,CAACG,IAAI;;MAEjC;MACAD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAE/C,IAAI,CAACN,QAAQ,EAAE;QACdA,QAAQ,GAAGC,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;QAC1CP,QAAQ,CAACQ,EAAE,GAAG,oBAAoB;;QAElC;QACA,IAAIC,MAAM,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA,KAAK;QAEDT,QAAQ,CAACU,SAAS,GAAGD,MAAM;QAC3BR,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,QAAQ,CAAC;MACpC;IACD;IAGI,IAAIlC,gBAAgB,EAAE;MACpB;MACA,IAAIA,gBAAgB,KAAK,cAAc,EAAE;QACvCW,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3BP,uBAAuB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;QAC/CF,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QACnCR,WAAW,CAAC;UAAE,GAAGC,QAAQ;UAAEoD,IAAI,EAAE;QAAe,CAAC,CAAC;MACpD,CAAC,MAAM,IAAI/C,gBAAgB,KAAK,SAAS,EAAE;QACzCS,mBAAmB,CAAC,CAAC;QACrBL,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1CF,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QACnCW,0BAA0B,CAAC,IAAI,CAAC;QAChCnB,WAAW,CAAC;UAAE,GAAGC,QAAQ;UAAEoD,IAAI,EAAE;QAAU,CAAC,CAAC;QAC7CxC,eAAe,CAACC,YAAY,GAAG,CAAC,CAAC;QACjCE,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,MAAM,IAAIV,gBAAgB,KAAK,QAAQ,EAAE;QACxCW,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3BT,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QACnCE,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QACzCV,WAAW,CAAC;UAAE,GAAGC,QAAQ;UAAEoD,IAAI,EAAE;QAAS,CAAC,CAAC;QAC5C;QACAhE,cAAc,CAACiE,QAAQ,CAAC,CAAC,CAACC,2BAA2B,CAAC,CAAC;QACvD9C,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM,IAAIH,gBAAgB,KAAK,SAAS,EAAE;QACzCW,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3BP,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1CF,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QACnCR,WAAW,CAAC;UAAE,GAAGC,QAAQ;UAAEoD,IAAI,EAAE;QAAU,CAAC,CAAC;QAC7CxC,eAAe,CAACC,YAAY,GAAG,CAAC,CAAC;MACnC;MAEA,MAAM0C,YAAY,GAAG5C,KAAK,CAAC6C,GAAG,CAACC,IAAI,KAAK;QACtC,GAAGA,IAAI;QACPC,QAAQ,EAAErD;MACZ,CAAC,CAAC,CAAC;MAEHK,QAAQ,CAAC6C,YAAY,CAAC;MACtB1D,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;EACF,CAAC;EACD,IAAIwB,YAAY,EAAE;IAChBxB,OAAO,CAAC,CAAC;IACT,OAAO,IAAI;EACf;EACE,MAAM8D,UAAU,GAAI1B,KAAa,IAAK5B,gBAAgB,KAAK4B,KAAK;EAChE,MAAM2B,SAAS,GAAIC,KAAa,IAAK1D,WAAW,KAAK0D,KAAK;EAC1D,oBAEEnE,OAAA;IACAoE,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE,GAAG;MACXC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,eAEF/E,OAAA;MACEgF,SAAS,EAAC,uBAAuB;MACjCZ,KAAK,EAAE;QACLa,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,OAAO;QACjBJ,eAAe,EAAE,OAAO;QACxBK,SAAS,EAAE,4BAA4B;QACvCC,MAAM,EAAE,mBAAmB;QAC3BC,MAAM,EAAE,OAAO;QACfC,YAAY,EAAE;MAChB,CAAE;MAAAP,QAAA,gBAGF/E,OAAA;QACEgF,SAAS,EAAC,mBAAmB;QAC7BZ,KAAK,EAAE;UACLmB,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE;QACX,CAAE;QAAAT,QAAA,gBAEF/E,OAAA;UACEoE,KAAK,EAAE;YACLM,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE;UACd,CAAE;UAAAI,QAAA,gBAEF/E,OAAA;YACEgF,SAAS,EAAC,aAAa;YACvBZ,KAAK,EAAE;cACLqB,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,EAED3E;UAAS;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACPtC,OAAA;YACEoE,KAAK,EAAE;cACLU,eAAe,EAAE,SAAS;cAC1BQ,YAAY,EAAE,KAAK;cACnBG,QAAQ,EAAE,MAAM;cAChBR,KAAK,EAAE,MAAM;cACbI,MAAM,EAAE,MAAM;cACdX,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBe,UAAU,EAAE;YACd,CAAE;YAAAZ,QAAA,EACH;UAED;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNtC,OAAA;UACEoE,KAAK,EAAE;YACLqB,QAAQ,EAAE,MAAM;YAChBG,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,QAAQ;YACpBC,aAAa,EAAE;UACjB,CAAE;UAAAf,QAAA,EACH;QAED;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtC,OAAA;QACEgF,SAAS,EAAC,qBAAqB;QAC/BZ,KAAK,EAAE;UACLM,OAAO,EAAE,MAAM;UACfqB,aAAa,EAAE,KAAK;UACpBnB,cAAc,EAAE,QAAQ;UACxBoB,GAAG,EAAE,MAAM;UACXR,OAAO,EAAE;QACX,CAAE;QAAAT,QAAA,EAEDhD,QAAQ,CAAC+B,GAAG,CAAC,CAACmC,OAAO,EAAE9B,KAAK,KAAK;UAChC,MAAMF,UAAU,GAAGtD,gBAAgB,KAAKsF,OAAO,CAAC1D,KAAK;UACrD,MAAM2B,SAAS,GAAGzD,WAAW,KAAK0D,KAAK;UAEvC,oBACEnE,OAAA;YAEEkG,OAAO,EAAEA,CAAA,KAAMtF,mBAAmB,CAACqF,OAAO,CAAC1D,KAAK,CAAE;YAClD4D,YAAY,EAAEA,CAAA,KAAMzF,cAAc,CAACyD,KAAK,CAAE;YAC1CiC,YAAY,EAAEA,CAAA,KAAM1F,cAAc,CAAC,IAAI,CAAE;YACzC0D,KAAK,EAAE;cACLa,KAAK,EAAE,OAAO;cACdI,MAAM,EAAE,OAAO;cACfE,SAAS,EAAE,QAAQ;cACnBc,MAAM,EAAE,SAAS;cACjBf,YAAY,EAAE,KAAK;cACnBE,OAAO,EAAE,WAAW;cACpBJ,MAAM,EAAE,aAAa;cACrB1C,WAAW,EAAEuB,UAAU,IAAIC,SAAS,GAAG,SAAS,GAAG,SAAS;cAC5DvB,UAAU,EAAEsB,UAAU,IAAIC,SAAS,GAAG,SAAS,GAAG,MAAM;cACxDoC,UAAU,EAAE;YACd,CAAE;YAAAvB,QAAA,gBAEF/E,OAAA;cACEoE,KAAK,EAAE;gBACLqB,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,OAAO;gBACnBa,WAAW,EAAE,KAAK;gBAClBlB,MAAM,EAAE;cACV,CAAE;cAAAN,QAAA,EAEDkB,OAAO,CAACjE;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtC,OAAA;cACEoE,KAAK,EAAE;gBACLqB,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBc,YAAY,EAAE;cAChB,CAAE;cAAAzB,QAAA,EAEDkB,OAAO,CAAC1D;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNtC,OAAA;cACEoE,KAAK,EAAE;gBACLqB,QAAQ,EAAE,MAAM;gBAChBI,UAAU,EAAE,MAAM;gBAClBC,aAAa,EAAE,OAAO;gBACtBP,SAAS,EAAE;cACb,CAAE;cAAAR,QAAA,EAEDkB,OAAO,CAACzD;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA,GA7CD6B,KAAK;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CP,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtC,OAAA;QAAKgF,SAAS,EAAC,qBAAqB;QAACZ,KAAK,EAAE;UAAEoB,OAAO,EAAE;QAAY,CAAE;QAAAT,QAAA,eACnE/E,OAAA;UACEkG,OAAO,EAAEtD,eAAgB;UACzB6D,QAAQ,EAAE,CAAC9F,gBAAiB;UAC5ByD,KAAK,EAAE;YACLoB,OAAO,EAAE,UAAU;YACnBV,eAAe,EAAE,SAAS;YAC1B4B,OAAO,EAAC/F,gBAAgB,GAAC,GAAG,GAAC,KAAK;YAClCiF,KAAK,EAAE,MAAM;YACbN,YAAY,EAAE,KAAK;YACnBG,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,GAAG;YACf;YACAY,UAAU,EAAE,4BAA4B;YACxClB,MAAM,EAAE,MAAM;YACdiB,MAAM,EAAC;UACT,CAAE;UAAAtB,QAAA,EACH;QAED;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAnTIP,qBAAkI;EAAA,QAmBlIP,cAAc;AAAA;AAAAiH,EAAA,GAnBd1G,qBAAkI;AAqTxI,eAAeA,qBAAqB;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}