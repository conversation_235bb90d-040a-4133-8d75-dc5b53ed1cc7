{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{Box,Typography}from\"@mui/material\";import Modal from'@mui/material/Modal';import{useTranslation}from'react-i18next';import{getAllFiles}from\"../../services/FileService\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SelectImageFromApplication=_ref=>{let{isOpen,handleModelClose,onImageSelect,handleImageUpload,setFormOfUpload,formOfUpload,handleReplaceImage,isReplaceImage}=_ref;const{t:translate}=useTranslation();const[files,setFiles]=useState([]);const getAllFilesData=async()=>{try{const data=await getAllFiles();if(data){const uploads=data.map(file=>({ImageId:file.Id,FileName:file.Name||null,Url:file.Url||''}));setFiles(uploads);}else{}}catch(error){}};useEffect(()=>{getAllFilesData();},[]);return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(Modal,{open:isOpen,onClose:handleModelClose,children:/*#__PURE__*/_jsx(Box,{sx:{position:\"absolute\",top:\"50%\",left:\"50%\",transform:\"translate(-50%, -50%)\",width:450,bgcolor:\"background.paper\",boxShadow:24,p:4,maxHeight:\"400px\",overflow:\"auto\"},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:translate(\"Select a File\")}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",flexWrap:\"wrap\"},children:files&&files.map(file=>{return/*#__PURE__*/_jsx(Box,{sx:{width:\"100px\",height:\"100px\",mx:2},children:/*#__PURE__*/_jsx(\"img\",{src:file===null||file===void 0?void 0:file.Url,style:{width:\"inherit\"},onClick:()=>{onImageSelect(file);},alt:translate(\"Uploaded file image\")})});})})]})})})});};export default SelectImageFromApplication;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Modal", "useTranslation", "getAllFiles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SelectImageFromApplication", "_ref", "isOpen", "handleModelClose", "onImageSelect", "handleImageUpload", "setFormOfUpload", "formOfUpload", "handleReplaceImage", "isReplaceImage", "t", "translate", "files", "setFiles", "getAllFilesData", "data", "uploads", "map", "file", "ImageId", "Id", "FileName", "Name", "Url", "error", "children", "open", "onClose", "sx", "position", "top", "left", "transform", "width", "bgcolor", "boxShadow", "p", "maxHeight", "overflow", "variant", "display", "flexWrap", "height", "mx", "src", "style", "onClick", "alt"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/common/SelectImageFromApplication.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip } from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport Modal from '@mui/material/Modal';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport DriveFolderUploadIcon from '@mui/icons-material/DriveFolderUpload';\r\nimport BackupIcon from '@mui/icons-material/Backup';\r\nimport { getAllFiles } from \"../../services/FileService\";\r\nimport { FileUpload } from \"../../models/FileUpload\";\r\n\r\n\r\n\r\nconst SelectImageFromApplication = ({ isOpen, handleModelClose, onImageSelect, handleImageUpload, setFormOfUpload, formOfUpload,handleReplaceImage, isReplaceImage }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\tconst [files, setFiles] = useState<FileUpload[]>([]);\r\n\t\r\n\r\n\tconst getAllFilesData = async () => {\r\n\t\ttry {\r\n\t\t\tconst data = await getAllFiles();\r\n\t\t\tif (data) {\r\n\t\t\t\tconst uploads: any = data.map((file:any) => ({\r\n\t\t\t\t\tImageId: file.Id, \r\n\t\t\t\t\tFileName: file.Name || null,\r\n\t\t\t\t\tUrl: file.Url || '',\r\n\t\t\t\t}));\r\n\t\t\t\tsetFiles(uploads);\r\n\t\t\t}else{\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t}\r\n\t}\r\n\r\n\tuseEffect(() => {\r\n\t\tgetAllFilesData();\r\n\t}, []);\r\n\r\n\treturn (<>\r\n\t\t<Modal open={isOpen} onClose={handleModelClose}>\r\n\t\t\t<Box sx={{\r\n\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\ttop: \"50%\",\r\n\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\twidth: 450,\r\n\t\t\t\t\tbgcolor: \"background.paper\",\r\n\t\t\t\t\tboxShadow: 24,\r\n\t\t\t\t\tp: 4,\r\n\t\t\t\t\tmaxHeight: \"400px\",\r\n\t\t\t\t\toverflow: \"auto\"\r\n\t\t\t\t}}>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"h6\">{translate(\"Select a File\")}</Typography>\r\n\t\t\t\t\t<Box sx={{ display: \"flex\", flexWrap: \"wrap\" }}>\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tfiles && (\r\n\t\t\t\t\t\t\t\tfiles.map((file) => {\r\n\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ width: \"100px\", height: \"100px\", mx: 2 }} >\r\n\t\t\t\t\t\t\t\t\t\t\t<img src={file?.Url} style={{ width: \"inherit\" }} onClick={() => { onImageSelect(file) }} alt={translate(\"Uploaded file image\")} />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Box>\r\n\t\t\t</Box>\r\n\t\t</Modal>\r\n\t</>);\r\n}\r\n\r\n\r\nexport default SelectImageFromApplication;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,UAAU,KAAmE,eAAe,CAG1G,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,OAASC,cAAc,KAAQ,eAAe,CAI9C,OAASC,WAAW,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAKzD,KAAM,CAAAC,0BAA0B,CAAGC,IAAA,EAA2I,IAA1I,CAAEC,MAAM,CAAEC,gBAAgB,CAAEC,aAAa,CAAEC,iBAAiB,CAAEC,eAAe,CAAEC,YAAY,CAACC,kBAAkB,CAAEC,cAAoB,CAAC,CAAAR,IAAA,CACxK,KAAM,CAAES,CAAC,CAAEC,SAAU,CAAC,CAAGnB,cAAc,CAAC,CAAC,CAEzC,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGzB,QAAQ,CAAe,EAAE,CAAC,CAGpD,KAAM,CAAA0B,eAAe,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACH,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAtB,WAAW,CAAC,CAAC,CAChC,GAAIsB,IAAI,CAAE,CACT,KAAM,CAAAC,OAAY,CAAGD,IAAI,CAACE,GAAG,CAAEC,IAAQ,GAAM,CAC5CC,OAAO,CAAED,IAAI,CAACE,EAAE,CAChBC,QAAQ,CAAEH,IAAI,CAACI,IAAI,EAAI,IAAI,CAC3BC,GAAG,CAAEL,IAAI,CAACK,GAAG,EAAI,EAClB,CAAC,CAAC,CAAC,CACHV,QAAQ,CAACG,OAAO,CAAC,CAClB,CAAC,IAAI,CACL,CACD,CAAE,MAAOQ,KAAK,CAAE,CAChB,CACD,CAAC,CAEDrC,SAAS,CAAC,IAAM,CACf2B,eAAe,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,mBAAQnB,IAAA,CAAAI,SAAA,EAAA0B,QAAA,cACP9B,IAAA,CAACJ,KAAK,EAACmC,IAAI,CAAExB,MAAO,CAACyB,OAAO,CAAExB,gBAAiB,CAAAsB,QAAA,cAC9C9B,IAAA,CAACN,GAAG,EAACuC,EAAE,CAAE,CACPC,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,KAAK,CACVC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,uBAAuB,CAClCC,KAAK,CAAE,GAAG,CACVC,OAAO,CAAE,kBAAkB,CAC3BC,SAAS,CAAE,EAAE,CACbC,CAAC,CAAE,CAAC,CACJC,SAAS,CAAE,OAAO,CAClBC,QAAQ,CAAE,MACX,CAAE,CAAAb,QAAA,cACF5B,KAAA,CAACR,GAAG,EAAAoC,QAAA,eACH9B,IAAA,CAACL,UAAU,EAACiD,OAAO,CAAC,IAAI,CAAAd,QAAA,CAAEd,SAAS,CAAC,eAAe,CAAC,CAAa,CAAC,cAClEhB,IAAA,CAACN,GAAG,EAACuC,EAAE,CAAE,CAAEY,OAAO,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAhB,QAAA,CAE7Cb,KAAK,EACJA,KAAK,CAACK,GAAG,CAAEC,IAAI,EAAK,CACnB,mBACCvB,IAAA,CAACN,GAAG,EAACuC,EAAE,CAAE,CAAEK,KAAK,CAAE,OAAO,CAAES,MAAM,CAAE,OAAO,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAlB,QAAA,cACnD9B,IAAA,QAAKiD,GAAG,CAAE1B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,GAAI,CAACsB,KAAK,CAAE,CAAEZ,KAAK,CAAE,SAAU,CAAE,CAACa,OAAO,CAAEA,CAAA,GAAM,CAAE1C,aAAa,CAACc,IAAI,CAAC,CAAC,CAAE,CAAC6B,GAAG,CAAEpC,SAAS,CAAC,qBAAqB,CAAE,CAAE,CAAC,CAC/H,CAAC,CAER,CAAC,CACD,CAEE,CAAC,EACF,CAAC,CACF,CAAC,CACA,CAAC,CACP,CAAC,CACJ,CAAC,CAGD,cAAe,CAAAX,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}