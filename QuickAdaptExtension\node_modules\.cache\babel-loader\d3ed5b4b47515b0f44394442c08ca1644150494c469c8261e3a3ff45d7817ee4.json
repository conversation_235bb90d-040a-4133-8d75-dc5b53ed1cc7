{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\GuidesPreview\\\\tooltippreview\\\\Tooltips\\\\Tooltips.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { Button, Tooltip, Box, LinearProgress, Typography, tooltipClasses, MobileStepper, IconButton } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { styled } from \"@mui/material/styles\";\nimport useDrawerStore from \"../../../../store/drawerStore\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CustomWidthTooltip = styled(({\n  className,\n  canvasStyle,\n  hasOnlyButtons,\n  hasOnlyText,\n  dynamicWidth,\n  ...props\n}) => /*#__PURE__*/_jsxDEV(Tooltip, {\n  ...props,\n  classes: {\n    popper: className\n  },\n  id: \"Tooltip-unique\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 113,\n  columnNumber: 2\n}, this))(({\n  canvasStyle,\n  hasOnlyButtons,\n  hasOnlyText,\n  dynamicWidth\n}) => ({\n  [`& .${tooltipClasses.tooltip}`]: {\n    backgroundColor: (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BackgroundColor) || \"#ffffff\",\n    // color: \"black\", // Set static text color\n    //fontSize: \"14px\", // Font size\n    padding: hasOnlyButtons ? \"0px\" : canvasStyle.Padding,\n    // padding: \"0px !important\",\n    borderRadius: (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.Radius) || (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderRadius) || \"10px\",\n    boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\n    border: canvasStyle !== null && canvasStyle !== void 0 && canvasStyle.BorderSize && (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderSize) !== \"0px\" ? `${canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderSize} solid ${(canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderColor) || \"transparent\"}` : \"none\",\n    // width: (hasOnlyButtons ) ? \"auto !important\" :\n    // \t   canvasStyle?.Width ? `${canvasStyle.Width} !important` : \"300px\",\n    width: 'auto !important',\n    maxWidth: canvasStyle !== null && canvasStyle !== void 0 && canvasStyle.Width ? `${canvasStyle.Width} !important` : \"300px\",\n    left: `${(canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.XAxisOffset) || \"auto\"} !important`,\n    bottom: `${(canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.YAxisOffset) || \"auto\"} !important`\n  },\n  [`& .${tooltipClasses.arrow}`]: {\n    color: (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BackgroundColor) || \"#ffffff\",\n    fontSize: \"16px\",\n    \"&:before\": {\n      outlineWidth: canvasStyle !== null && canvasStyle !== void 0 && canvasStyle.BorderSize && (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderSize) !== \"0px\" ? `${canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderSize}` : \"0px\",\n      // This controls the width of the border of the arrow\n      outlineColor: (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderColor) || \"transparent\",\n      // This controls the color of the border of the arrow\n      outlineStyle: \"solid\" // Ensure the border is applied properly\n    }\n  },\n  [`&.MuiTooltip-popper`]: {\n    zIndex: (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.Zindex) || 11000 // Tooltip z-index\n  }\n}));\n_c = CustomWidthTooltip;\nconst TooltipGuide = ({\n  steps,\n  currentUrl,\n  onClose,\n  tooltipConfig,\n  startStepIndex,\n  data\n}) => {\n  _s();\n  var _currentStepData$moda, _currentStepData$butt3, _currentStepData$imag6, _currentStepData$imag7, _currentStepData$butt4, _currentStepData$butt12, _currentStepData$butt13;\n  let rect;\n  const [currentStepIndex, setCurrentStepIndex] = useState(startStepIndex || 1);\n  const contentRef = useRef(null);\n  const buttonContainerRef = useRef(null);\n\n  // Auto-scroll related refs and state\n  const scrollOperationQueue = useRef(Promise.resolve());\n\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const scrollbarRef = useRef(null);\n  useEffect(() => {\n    if (startStepIndex !== undefined) {\n      setCurrentStepIndex(startStepIndex);\n    }\n  }, [startStepIndex]);\n  const {\n    setCurrentStep,\n    selectedTemplate,\n    currentStep,\n    ProgressColor,\n    createWithAI,\n    pageinteraction\n  } = useDrawerStore(state => state);\n  const currentStepData = steps[currentStepIndex - 1];\n  const [targetElement, setTargetElement] = useState(null);\n  const [tooltipPosition, setTooltipPosition] = useState({\n    top: 0,\n    left: 0\n  });\n  const [tooltipPlacement, setTooltipPlacement] = useState(\"top\");\n  const [isElementVisible, setIsElementVisible] = useState(false);\n  const observerRef = useRef(null);\n\n  // Add refs to store previous position values to prevent unnecessary updates\n  const prevPositionRef = useRef({\n    top: 0,\n    left: 0\n  });\n  const prevPlacementRef = useRef(\"top\");\n  const calculateBestPosition = element => {\n    const rect = element.getBoundingClientRect();\n    const viewportWidth = window.innerWidth;\n    const viewportHeight = window.innerHeight;\n    const spaceTop = rect.top;\n    const spaceBottom = viewportHeight - rect.bottom;\n    const spaceLeft = rect.left;\n    const spaceRight = viewportWidth - rect.right;\n    const maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);\n    if (maxSpace === spaceTop) return \"top\";\n    if (maxSpace === spaceBottom) return \"bottom\";\n    if (maxSpace === spaceLeft) return \"left\";\n    return \"right\";\n  };\n  // Removed unused isElementInViewport function\n\n  const validateElementPosition = element => {\n    const rect = element.getBoundingClientRect();\n    return rect.width > 0 && rect.height > 0 && rect.top !== 0 && rect.left !== 0 && !Number.isNaN(rect.top) && !Number.isNaN(rect.left);\n  };\n  const getElementByXPath = (xpath, PossibleElementPath) => {\n    // Validate XPath before using it\n    if (!xpath || xpath.trim() === \"\") {\n      console.log(\"XPath is empty or undefined, trying PossibleElementPath:\", PossibleElementPath);\n      if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\n        try {\n          const result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n          const node = result.singleNodeValue;\n          if (node instanceof HTMLElement) {\n            return node;\n          } else if (node !== null && node !== void 0 && node.parentElement) {\n            return node.parentElement;\n          }\n        } catch (error) {\n          console.error(\"Error evaluating PossibleElementPath:\", PossibleElementPath, error);\n        }\n      }\n      return null;\n    }\n    try {\n      const query = `${xpath}[not(ancestor::div[@id='quickAdopt_banner']) and not(@id='quickAdopt_banner')]`;\n      console.log(\"Evaluating XPath query:\", query);\n      const result = document.evaluate(query, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n      const node = result.singleNodeValue;\n      if (node instanceof HTMLElement) {\n        return node;\n      } else if (node !== null && node !== void 0 && node.parentElement) {\n        return node.parentElement;\n      } else if (node === null) {\n        // Try PossibleElementPath as fallback\n        if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\n          console.log(\"Primary XPath failed, trying PossibleElementPath:\", PossibleElementPath);\n          const fallbackResult = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n          const fallbackNode = fallbackResult.singleNodeValue;\n          if (fallbackNode instanceof HTMLElement) {\n            return fallbackNode;\n          } else if (fallbackNode !== null && fallbackNode !== void 0 && fallbackNode.parentElement) {\n            return fallbackNode.parentElement;\n          }\n        }\n        return null;\n      } else {\n        return null;\n      }\n    } catch (error) {\n      console.error(\"Error evaluating XPath:\", xpath, error);\n      // Try PossibleElementPath as fallback\n      if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\n        try {\n          console.log(\"XPath evaluation failed, trying PossibleElementPath:\", PossibleElementPath);\n          const result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n          const node = result.singleNodeValue;\n          if (node instanceof HTMLElement) {\n            return node;\n          } else if (node !== null && node !== void 0 && node.parentElement) {\n            return node.parentElement;\n          }\n        } catch (fallbackError) {\n          console.error(\"Error evaluating PossibleElementPath:\", PossibleElementPath, fallbackError);\n        }\n      }\n      return null;\n    }\n  };\n\n  // Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\n  const smoothScrollTo = (element, targetTop, duration = 300) => {\n    // Ensure targetTop is within valid bounds\n    const maxScroll = element.scrollHeight - element.clientHeight;\n    const clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));\n\n    // Method 1: Try native smooth scrolling first\n    try {\n      if ('scrollTo' in element && typeof element.scrollTo === 'function') {\n        element.scrollTo({\n          top: clampedTargetTop,\n          behavior: 'smooth'\n        });\n        return;\n      }\n    } catch (error) {\n      console.log(\"Native smooth scrollTo failed, trying animation fallback\");\n    }\n\n    // Method 2: Manual animation fallback\n    try {\n      const startTop = element.scrollTop;\n      const distance = clampedTargetTop - startTop;\n      const startTime = performance.now();\n      const animateScroll = currentTime => {\n        const elapsed = currentTime - startTime;\n        const progress = Math.min(elapsed / duration, 1);\n\n        // Easing function for smooth animation\n        const easeInOutCubic = t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n        const easedProgress = easeInOutCubic(progress);\n        element.scrollTop = startTop + distance * easedProgress;\n        if (progress < 1) {\n          requestAnimationFrame(animateScroll);\n        }\n      };\n      requestAnimationFrame(animateScroll);\n    } catch (error) {\n      console.log(\"RequestAnimationFrame failed, using direct assignment\");\n      // Method 3: Direct assignment as final fallback\n      element.scrollTop = clampedTargetTop;\n    }\n  };\n\n  // Enhanced reusable element polling function with exponential backoff and better timing\n  const pollForElement = useCallback((xpath, possibleElementPath, onElementFound, maxAttempts = 30, initialIntervalMs = 16,\n  // Start with one frame\n  logPrefix = \"Element\", onElementNotFound) => {\n    let elementCheckTimeout = null;\n    let attempts = 0;\n    let currentInterval = initialIntervalMs;\n    let isCleanedUp = false;\n    const cleanup = () => {\n      if (elementCheckTimeout) {\n        clearTimeout(elementCheckTimeout);\n        elementCheckTimeout = null;\n      }\n      isCleanedUp = true;\n    };\n    const checkForElement = () => {\n      if (isCleanedUp) return;\n      attempts++;\n      console.log(`${logPrefix}: Polling attempt ${attempts}/${maxAttempts} (interval: ${currentInterval}ms)`);\n      const element = getElementByXPath(xpath, possibleElementPath);\n      if (element && validateElementPosition(element)) {\n        console.log(`${logPrefix}: Found and validated after ${attempts} attempts`);\n        cleanup();\n        onElementFound(element);\n        return;\n      }\n      if (attempts >= maxAttempts) {\n        console.log(`${logPrefix}: Max attempts (${maxAttempts}) reached, element not found`);\n        cleanup();\n        if (onElementNotFound) {\n          onElementNotFound();\n        }\n        return;\n      }\n\n      // Exponential backoff with jitter to prevent thundering herd\n      const jitter = Math.random() * 0.1; // 10% jitter\n      currentInterval = Math.min(currentInterval * (1.2 + jitter), 1000); // Cap at 1 second\n\n      elementCheckTimeout = setTimeout(checkForElement, currentInterval);\n    };\n\n    // Start the polling\n    checkForElement();\n\n    // Return cleanup function\n    return cleanup;\n  }, []);\n\n  // Enhanced cross-environment scrolling function\n  const universalScrollTo = (element, options) => {\n    const isWindow = element === window;\n    const targetElement = isWindow ? document.documentElement : element;\n\n    // Method 1: Try native scrollTo if available and not blocked\n    if (!isWindow && 'scrollTo' in element && typeof element.scrollTo === 'function') {\n      try {\n        element.scrollTo(options);\n        return true;\n      } catch (error) {\n        console.log(\"Native scrollTo blocked or failed:\", error);\n      }\n    }\n\n    // Method 2: Try window.scrollTo for window element\n    if (isWindow && options.behavior === 'smooth') {\n      try {\n        window.scrollTo(options);\n        return true;\n      } catch (error) {\n        console.log(\"Window scrollTo failed:\", error);\n      }\n    }\n\n    // Method 3: Try smooth scrolling with custom animation\n    if (options.behavior === 'smooth' && options.top !== undefined) {\n      try {\n        smoothScrollTo(targetElement, options.top);\n        return true;\n      } catch (error) {\n        console.log(\"Smooth scroll animation failed:\", error);\n      }\n    }\n\n    // Method 4: Direct property assignment (final fallback)\n    try {\n      if (options.top !== undefined) {\n        targetElement.scrollTop = options.top;\n      }\n      if (options.left !== undefined) {\n        targetElement.scrollLeft = options.left;\n      }\n      return true;\n    } catch (error) {\n      console.log(\"Direct property assignment failed:\", error);\n      return false;\n    }\n  };\n  const scrollToTargetElement = useCallback(async (targetElement, placement, stepData) => {\n    if (!targetElement) {\n      console.log(\"ScrollToTargetElement: No target element provided\");\n      return;\n    }\n    console.log(\"🎯 Starting enhanced auto-scroll to target element:\", {\n      element: targetElement,\n      tagName: targetElement.tagName,\n      className: targetElement.className,\n      id: targetElement.id,\n      placement: placement\n    });\n    try {\n      // Add the scroll operation to the queue to prevent conflicts\n      scrollOperationQueue.current = scrollOperationQueue.current.then(async () => {\n        const rect = targetElement.getBoundingClientRect();\n        const viewportHeight = window.innerHeight;\n        const viewportWidth = window.innerWidth;\n\n        // Calculate optimal scroll position based on placement and viewport\n        let targetScrollTop = window.scrollY;\n        let targetScrollLeft = window.scrollX;\n\n        // Enhanced positioning logic based on tooltip placement\n        switch (placement) {\n          case \"top\":\n            // Position element in lower third of viewport to leave room for tooltip above\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.7;\n            break;\n          case \"bottom\":\n            // Position element in upper third of viewport to leave room for tooltip below\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.3;\n            break;\n          case \"left\":\n            // Position element towards right side to leave room for tooltip on left\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.5;\n            targetScrollLeft = window.scrollX + rect.left - viewportWidth * 0.7;\n            break;\n          case \"right\":\n            // Position element towards left side to leave room for tooltip on right\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.5;\n            targetScrollLeft = window.scrollX + rect.left - viewportWidth * 0.3;\n            break;\n          default:\n            // Default: center the element vertically\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.5;\n        }\n\n        // Ensure scroll positions are within valid bounds\n        const maxScrollTop = Math.max(0, document.documentElement.scrollHeight - viewportHeight);\n        const maxScrollLeft = Math.max(0, document.documentElement.scrollWidth - viewportWidth);\n        targetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));\n        targetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));\n        console.log(\"📍 Calculated scroll position:\", {\n          targetScrollTop,\n          targetScrollLeft,\n          currentScrollY: window.scrollY,\n          currentScrollX: window.scrollX,\n          elementRect: rect\n        });\n\n        // Perform the scroll with multiple fallback methods\n        let scrollSuccess = false;\n\n        // Method 1: Try smooth scrolling\n        try {\n          scrollSuccess = universalScrollTo(window, {\n            top: targetScrollTop,\n            left: targetScrollLeft,\n            behavior: 'smooth'\n          });\n          console.log(\"✅ Universal scroll success:\", scrollSuccess);\n        } catch (error) {\n          console.log(\"❌ Universal scroll failed:\", error);\n        }\n\n        // Method 2: Fallback to immediate scroll if smooth scroll failed\n        if (!scrollSuccess) {\n          try {\n            window.scrollTo(targetScrollLeft, targetScrollTop);\n            scrollSuccess = true;\n            console.log(\"✅ Fallback scroll successful\");\n          } catch (error) {\n            console.log(\"❌ Fallback scroll failed:\", error);\n          }\n        }\n\n        // Method 3: Final fallback using direct property assignment\n        if (!scrollSuccess) {\n          try {\n            document.documentElement.scrollTop = targetScrollTop;\n            document.documentElement.scrollLeft = targetScrollLeft;\n            document.body.scrollTop = targetScrollTop;\n            document.body.scrollLeft = targetScrollLeft;\n            console.log(\"✅ Direct property assignment completed\");\n          } catch (error) {\n            console.log(\"❌ Direct property assignment failed:\", error);\n          }\n        }\n\n        // Small delay to allow scroll to complete\n        await new Promise(resolve => setTimeout(resolve, 100));\n        console.log(\"🏁 Auto-scroll operation completed\");\n      });\n      await scrollOperationQueue.current;\n    } catch (error) {\n      console.error(\"❌ ScrollToTargetElement error:\", error);\n    }\n  }, [smoothScrollTo, universalScrollTo]);\n  const progress = currentStepIndex / steps.length * 100;\n  const interactWithPage = tooltipConfig === null || tooltipConfig === void 0 ? void 0 : tooltipConfig.InteractWithPage;\n  useEffect(() => {\n    if (currentStep && currentStepIndex >= 0 && interactWithPage === false) {\n      console.log('[Tooltips.tsx] Setting document.body.style.pointerEvents =', selectedTemplate === \"Tour\" ? \"auto\" : \"none\");\n      document.body.style.pointerEvents = selectedTemplate === \"Tour\" ? \"auto\" : \"none\";\n      const style = document.createElement('style');\n      style.innerHTML = `.qadpt-editor  { pointer-events: auto !important; }`;\n      document.head.appendChild(style);\n      return () => {\n        console.log('[Tooltips.tsx] Resetting document.body.style.pointerEvents = auto');\n        document.body.style.pointerEvents = \"auto\";\n        document.head.removeChild(style);\n      };\n    }\n    return () => {\n      // In case there's no active step or tooltip is closed\n      console.log('[Tooltips.tsx] Cleanup: Resetting document.body.style.pointerEvents = auto');\n      document.body.style.pointerEvents = \"auto\"; // Enable interactions\n    };\n  }, [currentStepIndex, interactWithPage]);\n\n  // Handle overflow hidden based on overlay and page interaction settings\n  useEffect(() => {\n    if (currentStep && currentStepIndex >= 0 && currentStepData !== null && currentStepData !== void 0 && currentStepData.overlay && interactWithPage === false) {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"\";\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = \"\";\n    };\n  }, [currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.overlay, interactWithPage]);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStepData, currentStep]);\n  const updateTargetAndPosition = () => {\n    // if (currentUrl !== currentStep?.targetUrl) {\n    // \tsetTargetElement(null);\n    // \tsetIsElementVisible(false);\n    // \treturn;\n    // }\n\n    // Debug logging for XPath data\n    console.log(\"Tooltip updateTargetAndPosition - currentStepData:\", {\n      xpath: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath,\n      PossibleElementPath: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.PossibleElementPath,\n      stepIndex: currentStepIndex,\n      createWithAI: createWithAI\n    });\n    const element = getElementByXPath(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath, currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.PossibleElementPath);\n    if (!element) {\n      console.log(\"Tooltip element not found for XPath:\", currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath, \"PossibleElementPath:\", currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.PossibleElementPath);\n      setTargetElement(null);\n      setIsElementVisible(false);\n      return;\n    }\n    const isValid = validateElementPosition(element);\n    //const isVisible = isElementInViewport(element);\n\n    if (!isValid) {\n      setTargetElement(null);\n      setIsElementVisible(false);\n      return;\n    }\n    const rect = element.getBoundingClientRect();\n    const xOffset = parseFloat((currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.positionXAxisOffset) || \"0\");\n    const yOffset = parseFloat((currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.positionYAxisOffset) || \"0\");\n    setTargetElement(element);\n    setIsElementVisible(true);\n\n    // Calculate placement\n    let newPlacement;\n    if (currentStepData !== null && currentStepData !== void 0 && currentStepData.autoposition) {\n      newPlacement = calculateBestPosition(element);\n    } else {\n      var _currentStepData$canv;\n      const validPlacements = [\"top\", \"left\", \"right\", \"bottom\"];\n      const placement = (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$canv = currentStepData.canvas) === null || _currentStepData$canv === void 0 ? void 0 : _currentStepData$canv.Position) || \"bottom\";\n      newPlacement = validPlacements.includes(placement) ? placement : \"bottom\";\n    }\n    if (prevPlacementRef.current !== newPlacement) {\n      prevPlacementRef.current = newPlacement;\n      setTooltipPlacement(newPlacement);\n    }\n    const newPosition = {\n      top: Math.round(rect.top + window.scrollY + yOffset),\n      left: Math.round(rect.left + window.scrollX + xOffset)\n    };\n    const positionChanged = Math.abs(prevPositionRef.current.top - newPosition.top) > 1 || Math.abs(prevPositionRef.current.left - newPosition.left) > 1;\n    if (positionChanged) {\n      prevPositionRef.current = newPosition;\n      setTooltipPosition(newPosition);\n    }\n  };\n\n  // Enhanced auto-scroll navigation system for tooltip next functionality\n  const handleNext = useCallback(async () => {\n    if (selectedTemplate !== \"Tour\") {\n      // Calculate the next index first (currentStepIndex is 1-based, so we need to check against steps.length)\n      const nextIndex = currentStepIndex + 1;\n      if (nextIndex <= steps.length) {\n        // Get the next step data for scrolling (convert to 0-based for array access)\n        const nextStepData = steps[nextIndex - 1];\n        console.log(\"🔍 Starting navigation to next step:\", {\n          currentIndex: currentStepIndex,\n          nextIndex: nextIndex,\n          xpath: nextStepData === null || nextStepData === void 0 ? void 0 : nextStepData.xpath,\n          stepTitle: `Step ${nextIndex}`\n        });\n\n        // Smart auto-scroll: Only scroll if element is not reasonably visible\n        if (nextStepData !== null && nextStepData !== void 0 && nextStepData.xpath) {\n          console.log(\"🔍 Checking auto-scroll for next element:\", {\n            xpath: nextStepData.xpath,\n            stepIndex: nextIndex,\n            stepTitle: `Step ${nextIndex}`\n          });\n\n          // Create a promise to handle element finding and scrolling\n          const scrollPromise = new Promise(resolve => {\n            // Use polling to find the element\n            pollForElement(nextStepData.xpath || \"\", nextStepData.PossibleElementPath || \"\", async foundElement => {\n              console.log(\"✅ Next element found:\", {\n                element: foundElement,\n                tagName: foundElement.tagName,\n                className: foundElement.className,\n                id: foundElement.id\n              });\n\n              // Check if element needs scrolling\n              const rect = foundElement.getBoundingClientRect();\n              const viewportHeight = window.innerHeight;\n              const viewportWidth = window.innerWidth;\n\n              // More generous visibility check - element should be reasonably visible\n              const isReasonablyVisible = rect.top >= -50 &&\n              // Allow some element to be above viewport\n              rect.left >= -50 &&\n              // Allow some element to be left of viewport\n              rect.bottom <= viewportHeight + 50 &&\n              // Allow some element below viewport\n              rect.right <= viewportWidth + 50 &&\n              // Allow some element right of viewport\n              rect.width > 0 && rect.height > 0;\n              if (isReasonablyVisible) {\n                console.log(\"✅ Element is reasonably visible, minimal adjustment\");\n                // Element is mostly visible, just ensure it's well positioned\n                try {\n                  foundElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'nearest',\n                    // Don't force center if already visible\n                    inline: 'nearest'\n                  });\n                } catch (error) {\n                  console.log(\"Minimal scroll adjustment failed:\", error);\n                }\n              } else {\n                console.log(\"🎯 Element not visible, performing auto-scroll\");\n                // Element is not visible, scroll to bring it into view\n                try {\n                  foundElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center',\n                    // Center it for better visibility\n                    inline: 'nearest'\n                  });\n                  console.log(\"✅ Auto-scroll completed successfully\");\n                } catch (scrollError) {\n                  console.error(\"❌ Auto-scroll failed:\", scrollError);\n                }\n              }\n              resolve();\n            }, 20,\n            // Reasonable maxAttempts\n            30,\n            // Reasonable initial interval\n            \"Next step element\", () => {\n              console.log(\"❌ Next element not found after polling, continuing without scroll\");\n              resolve();\n            });\n          });\n          try {\n            await scrollPromise;\n            console.log(\"✅ Element finding and scroll check completed\");\n          } catch (error) {\n            console.error(\"❌ Scroll promise failed:\", error);\n          }\n        } else {\n          console.log(\"ℹ️ No xpath provided for next step, skipping auto-scroll\");\n        }\n\n        // Update the step index AFTER scrolling is complete\n        console.log(\"🔄 Updating step index from\", currentStepIndex, \"to\", nextIndex);\n        setCurrentStepIndex(nextIndex);\n        setCurrentStep(currentStep + 1);\n\n        // Small delay to allow DOM to update after step change\n        await new Promise(resolve => setTimeout(resolve, 50));\n      } else {\n        console.log(\"🏁 Reached end of tooltip steps\", {\n          currentStepIndex,\n          nextIndex,\n          totalSteps: steps.length\n        });\n        // onClose(); // Close tooltip if no more steps\n        return;\n      }\n    } else {\n      // Tour template logic (consistent with 1-based indexing)\n      if (currentStep < (steps === null || steps === void 0 ? void 0 : steps.length)) {\n        setCurrentStepIndex(prev => Math.max(prev + 1, 1));\n        setCurrentStep(currentStep + 1);\n      }\n    }\n  }, [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, calculateBestPosition, scrollToTargetElement, setCurrentStep, setCurrentStepIndex]);\n\n  // Enhanced handlePrevious with auto-scroll functionality\n  const handlePrevious = useCallback(async () => {\n    if (selectedTemplate !== \"Tour\") {\n      const prevIndex = Math.max(currentStepIndex - 1, 1);\n      if (prevIndex >= 1) {\n        console.log(\"🔙 Navigating to previous step:\", {\n          currentIndex: currentStepIndex,\n          prevIndex: prevIndex\n        });\n\n        // Smart previous navigation scrolling\n        if (prevIndex === 1) {\n          console.log(\"HandlePrevious: Going back to first step, scroll to top\");\n          try {\n            window.scrollTo({\n              top: 0,\n              behavior: 'smooth'\n            });\n          } catch (error) {\n            console.log(\"HandlePrevious: Scroll to top failed:\", error);\n          }\n        } else {\n          // For other steps, check if previous step element needs scrolling\n          const prevStepData = steps[prevIndex - 1];\n          if (prevStepData !== null && prevStepData !== void 0 && prevStepData.xpath) {\n            console.log(\"HandlePrevious: Checking if previous step element needs scrolling\");\n            setTimeout(() => {\n              const prevElement = getElementByXPath(prevStepData.xpath, prevStepData.PossibleElementPath || \"\");\n              if (prevElement) {\n                const rect = prevElement.getBoundingClientRect();\n                const isOutOfView = rect.bottom < 0 || rect.top > window.innerHeight || rect.right < 0 || rect.left > window.innerWidth;\n                if (isOutOfView) {\n                  console.log(\"HandlePrevious: Previous element out of view, scrolling\");\n                  try {\n                    prevElement.scrollIntoView({\n                      behavior: 'smooth',\n                      block: 'center',\n                      inline: 'nearest'\n                    });\n                  } catch (error) {\n                    console.log(\"HandlePrevious: Element scroll failed:\", error);\n                  }\n                }\n              }\n            }, 100);\n          }\n        }\n\n        // Update step index\n        setCurrentStepIndex(prevIndex);\n        setCurrentStep(currentStep - 1);\n\n        // Small delay to allow DOM to update\n        await new Promise(resolve => setTimeout(resolve, 50));\n      }\n    } else {\n      setCurrentStep(currentStep - 1);\n    }\n  }, [currentStepIndex, selectedTemplate, currentStep, universalScrollTo, setCurrentStep, setCurrentStepIndex]);\n  useEffect(() => {\n    var _currentStepData$elem, _currentStepData$elem2, _currentStepData$elem3, _currentStepData$elem4;\n    // Debug logging for AI tooltip button click functionality\n    console.log(\"🔍 Tooltip useEffect - Element click setup:\", {\n      currentStepIndex,\n      elementclick: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.elementclick,\n      NextStep: currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem = currentStepData.elementclick) === null || _currentStepData$elem === void 0 ? void 0 : _currentStepData$elem.NextStep,\n      Id: currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem2 = currentStepData.elementclick) === null || _currentStepData$elem2 === void 0 ? void 0 : _currentStepData$elem2.Id,\n      xpath: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath\n    });\n    if ((currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem3 = currentStepData.elementclick) === null || _currentStepData$elem3 === void 0 ? void 0 : _currentStepData$elem3.NextStep) === \"element\" || (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem4 = currentStepData.elementclick) === null || _currentStepData$elem4 === void 0 ? void 0 : _currentStepData$elem4.NextStep) === \"button\") {\n      const element = getElementByXPath(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath, currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.PossibleElementPath);\n      if (element) {\n        console.log(\"✅ Element found for click handler:\", element);\n        const handleClick = () => {\n          console.log(\"🖱️ Element clicked - advancing to next step\");\n          handleNext();\n        };\n        element.addEventListener(\"click\", handleClick);\n        return () => {\n          element.removeEventListener(\"click\", handleClick);\n        };\n      } else {\n        console.log(\"❌ Element not found for xpath:\", currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath);\n      }\n    } else {\n      var _currentStepData$elem5;\n      console.log(\"ℹ️ No element click setup - NextStep:\", currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem5 = currentStepData.elementclick) === null || _currentStepData$elem5 === void 0 ? void 0 : _currentStepData$elem5.NextStep);\n    }\n  }, [currentStepData, handleNext]);\n  useEffect(() => {\n    const handleDOMChanges = () => {\n      requestAnimationFrame(updateTargetAndPosition);\n    };\n    observerRef.current = new MutationObserver(handleDOMChanges);\n    const targetNode = document.body;\n    observerRef.current.observe(targetNode, {\n      childList: true,\n      subtree: true,\n      attributes: true,\n      characterData: true\n    });\n    updateTargetAndPosition();\n    return () => {\n      var _observerRef$current;\n      (_observerRef$current = observerRef.current) === null || _observerRef$current === void 0 ? void 0 : _observerRef$current.disconnect();\n    };\n  }, [currentStepData, currentUrl]);\n  useEffect(() => {\n    const handleViewportChanges = () => {\n      requestAnimationFrame(updateTargetAndPosition);\n    };\n    window.addEventListener(\"scroll\", handleViewportChanges);\n    window.addEventListener(\"resize\", handleViewportChanges);\n    return () => {\n      window.removeEventListener(\"scroll\", handleViewportChanges);\n      window.removeEventListener(\"resize\", handleViewportChanges);\n    };\n  }, [currentStepData, currentUrl]);\n  useEffect(() => {\n    updateTargetAndPosition();\n  }, [currentStepData, currentUrl, rect]); // Ensure all dependencies are included\n\n  // Smart auto-scroll for current step changes - only when element is not visible\n  useEffect(() => {\n    if (!(currentStepData !== null && currentStepData !== void 0 && currentStepData.xpath)) {\n      return;\n    }\n\n    // Small delay to allow DOM to settle\n    const timeoutId = setTimeout(() => {\n      const currentElement = getElementByXPath(currentStepData.xpath, currentStepData.PossibleElementPath || \"\");\n      if (currentElement) {\n        const rect = currentElement.getBoundingClientRect();\n        const viewportHeight = window.innerHeight;\n        const viewportWidth = window.innerWidth;\n\n        // Check if element is completely out of view\n        const isCompletelyOutOfView = rect.bottom < 0 ||\n        // Completely above viewport\n        rect.top > viewportHeight ||\n        // Completely below viewport\n        rect.right < 0 ||\n        // Completely left of viewport\n        rect.left > viewportWidth // Completely right of viewport\n        ;\n        if (isCompletelyOutOfView) {\n          console.log(\"🔄 Current step element is out of view, gentle auto-scroll\");\n          try {\n            currentElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          } catch (error) {\n            console.log(\"Current step auto-scroll failed:\", error);\n          }\n        }\n      }\n    }, 100);\n    return () => {\n      clearTimeout(timeoutId);\n    };\n  }, [currentStepData]);\n  const canvasStyle = (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.canvas) || {}; // Assuming canvas is an array, take the first item\n  const enableProgress = tooltipConfig.EnableProgress || false;\n  function getProgressTemplate(tooltipConfig) {\n    if ((tooltipConfig === null || tooltipConfig === void 0 ? void 0 : tooltipConfig.ProgressTemplate) === \"1\") {\n      return \"dots\";\n    } else if ((tooltipConfig === null || tooltipConfig === void 0 ? void 0 : tooltipConfig.ProgressTemplate) === \"2\") {\n      return \"linear\";\n    } else if ((tooltipConfig === null || tooltipConfig === void 0 ? void 0 : tooltipConfig.ProgressTemplate) === \"3\") {\n      return \"BreadCrumbs\";\n    } else {\n      return \"breadcrumbs\";\n    }\n  }\n  const progressTemplate = getProgressTemplate(tooltipConfig);\n  const enabelCross = currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$moda = currentStepData.modal) === null || _currentStepData$moda === void 0 ? void 0 : _currentStepData$moda.DismissOption;\n  const renderContent = () => {\n    var _currentStepData$imag, _currentStepData$imag2, _currentStepData$imag3, _currentStepData$imag4, _currentStepData$imag5;\n    const hasImage = (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.imageUrl.startsWith(\"data:image/\")) || (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.imageUrl.startsWith(\"http\"));\n    const hasText = Array.isArray(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) ? currentStepData.content.some(item => (item === null || item === void 0 ? void 0 : item.Text) && typeof item.Text === \"string\" && item.Text.trim() !== \"\") : typeof (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) === \"string\" || /*#__PURE__*/React.isValidElement(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content);\n    const textStyle = {\n      fontSize: \"14px\",\n      lineHeight: \"1.5\",\n      whiteSpace: \"pre-wrap\",\n      wordBreak: \"break-word\",\n      color: \"black\"\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [hasImage && /*#__PURE__*/_jsxDEV(Box, {\n        component: \"img\",\n        src: currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$imag = currentStepData.imageproperties) === null || _currentStepData$imag === void 0 ? void 0 : _currentStepData$imag.Url,\n        alt: (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$imag2 = currentStepData.imageproperties) === null || _currentStepData$imag2 === void 0 ? void 0 : _currentStepData$imag2.AltText) || \"Step Image\",\n        sx: {\n          backgroundColor: (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$imag3 = currentStepData.imageproperties) === null || _currentStepData$imag3 === void 0 ? void 0 : _currentStepData$imag3.BackgroundColor) || \"transparent\",\n          objectFit: (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$imag4 = currentStepData.imageproperties) === null || _currentStepData$imag4 === void 0 ? void 0 : _currentStepData$imag4.Fit) || \"cover\",\n          maxHeight: (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$imag5 = currentStepData.imageproperties) === null || _currentStepData$imag5 === void 0 ? void 0 : _currentStepData$imag5.SectionHeight) || \"auto\",\n          width: \"100%\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1086,\n        columnNumber: 11\n      }, this), hasText && /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-preview\",\n        sx: {\n          margin: \"0 !important\",\n          ...textStyle,\n          \"& p\": {\n            margin: \"4px 0\"\n          }\n        },\n        dangerouslySetInnerHTML: {\n          __html: Array.isArray(currentStepData.content) ? currentStepData.content.map(item => item.Text.replace(/<a /g, '<a target=\"_blank\" rel=\"noopener noreferrer\" ')).join(\"<br/>\") : typeof currentStepData.content === \"string\" ? currentStepData.content.replace(/<a /g, '<a target=\"_blank\" rel=\"noopener noreferrer\" ') : \"\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1099,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1084,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Helper function to check if tooltip has only buttons (no text or images)\n  const hasOnlyButtons = () => {\n    var _currentStepData$butt;\n    const hasImage = (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.imageUrl.startsWith(\"data:image/\")) || (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.imageUrl.startsWith(\"http\"));\n    const hasText = Array.isArray(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) ? currentStepData.content.some(item => (item === null || item === void 0 ? void 0 : item.Text) && typeof item.Text === \"string\" && item.Text.trim() !== \"\") : typeof (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) === \"string\" || /*#__PURE__*/React.isValidElement(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content);\n    const hasButtons = (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt = currentStepData.buttonData) === null || _currentStepData$butt === void 0 ? void 0 : _currentStepData$butt.length) > 0;\n    return hasButtons && !hasImage && !hasText;\n  };\n\n  // Helper function to check if tooltip has only text (no buttons or images)\n  const hasOnlyText = () => {\n    var _currentStepData$butt2;\n    const hasImage = (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.imageUrl.startsWith(\"data:image/\")) || (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.imageUrl.startsWith(\"http\"));\n    const hasText = Array.isArray(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) ? currentStepData.content.some(item => (item === null || item === void 0 ? void 0 : item.Text) && typeof item.Text === \"string\" && item.Text.trim() !== \"\") : typeof (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) === \"string\" || /*#__PURE__*/React.isValidElement(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content);\n    const hasButtons = (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt2 = currentStepData.buttonData) === null || _currentStepData$butt2 === void 0 ? void 0 : _currentStepData$butt2.length) > 0;\n    return hasText && !hasImage && !hasButtons;\n  };\n  const hasHtmlMeaningfulContent = htmlContent => {\n    if (!htmlContent || htmlContent.trim() === '') {\n      return false;\n    }\n\n    // Clean up common empty HTML patterns before checking\n    let cleanedContent = htmlContent;\n\n    // Remove empty paragraph tags\n    cleanedContent = cleanedContent.replace(/<p>\\s*(&nbsp;)*\\s*<\\/p>/gi, '');\n\n    // Remove empty div tags\n    cleanedContent = cleanedContent.replace(/<div>\\s*(&nbsp;)*\\s*<\\/div>/gi, '');\n\n    // Remove empty span tags\n    cleanedContent = cleanedContent.replace(/<span>\\s*(&nbsp;)*\\s*<\\/span>/gi, '');\n\n    // Remove <br> tags\n    cleanedContent = cleanedContent.replace(/<br\\s*\\/?>/gi, '');\n\n    // Remove &nbsp; entities\n    cleanedContent = cleanedContent.replace(/&nbsp;/gi, ' ');\n\n    // If after cleaning there's no content left, return false\n    if (cleanedContent.trim() === '') {\n      return false;\n    }\n\n    // Create a temporary div to parse the cleaned HTML content\n    const tempDiv = document.createElement('div');\n    tempDiv.innerHTML = cleanedContent;\n\n    // Get the text content (strips all HTML tags)\n    const textContent = tempDiv.textContent || tempDiv.innerText;\n\n    // Check if there's any non-whitespace text content\n    if (textContent === null || textContent.trim() === '') {\n      return false;\n    }\n\n    // Additional check for common empty HTML patterns\n    // This handles cases like \"<div><br></div>\" or \"<p>&nbsp;</p>\" that might appear non-empty\n    const lowerContent = cleanedContent.toLowerCase();\n    const emptyPatterns = ['<div><br></div>', '<p><br></p>', '<div></div>', '<p></p>', '<span></span>', '<p>&nbsp;</p>', '<div>&nbsp;</div>', '<p> </p>', '<div> </div>'];\n    if (emptyPatterns.some(pattern => lowerContent.includes(pattern)) && textContent.trim().length <= 1) {\n      return false;\n    }\n    return true;\n  };\n  const hasValidTextContent = typeof (currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) === \"string\" && hasHtmlMeaningfulContent(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) || /*#__PURE__*/React.isValidElement(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) || Array.isArray(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.content) && currentStepData.content.some(item => (item === null || item === void 0 ? void 0 : item.Text) && typeof item.Text === \"string\" && hasHtmlMeaningfulContent(item.Text));\n  // Check if there are buttons\n  const hasButtons = (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt3 = currentStepData.buttonData) === null || _currentStepData$butt3 === void 0 ? void 0 : _currentStepData$butt3.length) > 0;\n\n  //Check if there's a valid image\n  const hasValidImage = (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$imag6 = currentStepData.imageUrl) === null || _currentStepData$imag6 === void 0 ? void 0 : _currentStepData$imag6.startsWith(\"data:image/\")) || (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$imag7 = currentStepData.imageUrl) === null || _currentStepData$imag7 === void 0 ? void 0 : _currentStepData$imag7.startsWith(\"http\"));\n  // Check if there's only text content (no images or buttons)\n  const hasOnlyTextContent = hasValidTextContent && !hasValidImage && !hasButtons;\n\n  // Check if there's only a button (no text or images)\n  const hasOnlyButton = hasButtons && !hasValidTextContent && !hasValidImage && (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt4 = currentStepData.buttonData) === null || _currentStepData$butt4 === void 0 ? void 0 : _currentStepData$butt4.length) === 1;\n\n  // Check if there's any meaningful content to display\n  const hasValidContent = hasValidTextContent || hasValidImage;\n  //Function to determine padding based on content and buttons\n  const getPadding = () => {\n    var _currentStepData$butt5, _currentStepData$butt6, _currentStepData$butt7, _currentStepData$butt8, _currentStepData$butt9;\n    // Check if we have exactly one button and it's a previous button\n    const hasPreviousButton = (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt5 = currentStepData.buttonData) === null || _currentStepData$butt5 === void 0 ? void 0 : _currentStepData$butt5.length) === 1 && (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt6 = currentStepData.buttonData) === null || _currentStepData$butt6 === void 0 ? void 0 : (_currentStepData$butt7 = _currentStepData$butt6[0]) === null || _currentStepData$butt7 === void 0 ? void 0 : (_currentStepData$butt8 = _currentStepData$butt7.ButtonAction) === null || _currentStepData$butt8 === void 0 ? void 0 : (_currentStepData$butt9 = _currentStepData$butt8.Action) === null || _currentStepData$butt9 === void 0 ? void 0 : _currentStepData$butt9.toLocaleLowerCase()) === \"previous\";\n\n    // Special case for previous button\n    if (hasPreviousButton) {\n      return \"0px\";\n    }\n\n    // Original logic\n    if (!hasValidContent) {\n      var _currentStepData$butt10;\n      return (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt10 = currentStepData.buttonData) === null || _currentStepData$butt10 === void 0 ? void 0 : _currentStepData$butt10.length) === 1 ? \"0px\" : \"4px\";\n    } else {\n      return \"0px\";\n    }\n  };\n\n  // Function to calculate the optimal width based on content and buttons\n  // const calculateOptimalWidth = () => {\n  // \t// If we have a fixed width from canvas settings and not a compact tooltip, use that\n  // \tif (canvasStyle?.Width && !hasOnlyButtons() && !hasOnlyText()) {\n  // \t\treturn `${canvasStyle.Width}`;\n  // \t}\n\n  // \t// For tooltips with only buttons or only text, use auto width\n  // \tif (hasOnlyButtons() || hasOnlyText()) {\n  // \t\treturn \"auto\";\n  // \t}\n\n  // \t// Get the width of content and button container\n  // \tconst contentWidth = contentRef.current?.scrollWidth || 0;\n  // \tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\n\n  // \t// Use the larger of the two, with some minimum and maximum constraints\n  // \tconst optimalWidth = Math.max(contentWidth, buttonWidth);\n\n  // \t// Add some padding to ensure text has room to wrap naturally\n  // \tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\n\n  // \t// Ensure width is between reasonable bounds\n  // \tconst minWidth = 250; // Minimum width\n  // \tconst maxWidth = 800; // Maximum width\n\n  // \tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\n\n  // \treturn `${finalWidth}px`;\n  // };\n\n  // Update dynamic width when content or buttons change\n  // useEffect(() => {\n  // \t// Use requestAnimationFrame to ensure DOM has been updated\n  // \trequestAnimationFrame(() => {\n  // \t\tconst newWidth = calculateOptimalWidth();\n  // \t\tsetDynamicWidth(newWidth);\n  // \t});\n  // }, [currentStepData, currentStepIndex]);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: steps.length,\n        position: \"static\",\n        activeStep: currentStepIndex - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          },\n          placeContent: \"center\",\n          padding: \"2px  !important\",\n          \"& .MuiMobileStepper-dot\": {\n            width: \"6px !important\",\n            height: \"6px !important\"\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            display: \"none\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1307,\n          columnNumber: 23\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            display: \"none\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1308,\n          columnNumber: 23\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1291,\n        columnNumber: 9\n      }, this);\n    }\n\n    // if (progressTemplate === \"breadcrumbs\") {\n    // \treturn (\n    // \t\t<Breadcrumbs\n    // \t\t\taria-label=\"breadcrumb\"\n    // \t\t\tsx={{ marginTop: \"10px\" }}\n    // \t\t>\n    // \t\t\t{steps.map((_, index) => (\n    // \t\t\t\t<Typography\n    // \t\t\t\t\tkey={index}\n    // \t\t\t\t\tcolor={index === currentStepIndex ? \"primary\" : \"text.secondary\"}\n    // \t\t\t\t>\n    // \t\t\t\t\tStep {index + 1} of {steps.length}\n    // \t\t\t\t</Typography>\n    // \t\t\t))}\n    // \t\t</Breadcrumbs>\n    // \t);\n    // }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\",\n          padding: \"8px\"\n        },\n        children: Array.from({\n          length: steps.length\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '14px',\n            height: '4px',\n            backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0',\n            // Active color and inactive color\n            borderRadius: '100px'\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1339,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1332,\n        columnNumber: 17\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          paddingTop: \"8px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            padding: \"8px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStepIndex, \" of \", steps.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1357,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1356,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: hasOnlyButtons() ? \"8px\" : \"0\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: hasOnlyButtons() ? \"0\" : \"6px 10px\",\n              '& .MuiLinearProgress-bar': {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1371,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1370,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1369,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  const renderButtons = () => {\n    var _currentStepData$butt11;\n    return (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt11 = currentStepData.buttonData) === null || _currentStepData$butt11 === void 0 ? void 0 : _currentStepData$butt11.length) > 0 ? currentStepData.buttonData.map((button, index) => {\n      const buttonStyle = {\n        backgroundColor: button.ButtonProperties.ButtonBackgroundColor,\n        color: button.ButtonProperties.ButtonTextColor,\n        border: button.ButtonProperties.ButtonBorderColor,\n        padding: \"4px 8px !important\",\n        lineHeight: \"normal\",\n        width: \"auto\",\n        fontSize: \"12px\",\n        fontFamily: \"Poppins\",\n        borderRadius: \"8px\",\n        textTransform: \"none\",\n        minWidth: \"fit-content\",\n        boxShadow: \"none !important\",\n        // Remove box shadow in normal state\n        \"&:hover\": {\n          boxShadow: \"none !important\",\n          // Remove box shadow in hover state\n          backgroundColor: button.ButtonProperties.ButtonBackgroundColor,\n          // Keep the same background color on hover\n          opacity: 0.9 // Slightly reduce opacity on hover for visual feedback\n        }\n      };\n      const handleClick = () => {\n        var _currentStepData$elem6, _currentStepData$elem7, _currentStepData$elem8, _currentStepData$elem9, _currentStepData$elem10, _currentStepData$elem11;\n        console.log(\"🔍 Button clicked:\", {\n          buttonId: button.Id,\n          buttonAction: button.ButtonAction.Action,\n          elementclick: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.elementclick,\n          NextStep: currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem6 = currentStepData.elementclick) === null || _currentStepData$elem6 === void 0 ? void 0 : _currentStepData$elem6.NextStep,\n          expectedButtonId: currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem7 = currentStepData.elementclick) === null || _currentStepData$elem7 === void 0 ? void 0 : _currentStepData$elem7.Id\n        });\n        if (button.ButtonAction.Action.toLocaleLowerCase() === \"close\") {\n          //onClose();\n        } else if (button.ButtonAction.Action.toLocaleLowerCase() === \"next\" && (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem8 = currentStepData.elementclick) === null || _currentStepData$elem8 === void 0 ? void 0 : _currentStepData$elem8.NextStep) !== \"button\") {\n          console.log(\"🚀 Regular next button - advancing step\");\n          handleNext();\n        } else if (button.ButtonAction.Action.toLocaleLowerCase() === \"next\" && (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem9 = currentStepData.elementclick) === null || _currentStepData$elem9 === void 0 ? void 0 : _currentStepData$elem9.NextStep) === \"button\" && (button.Id === (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem10 = currentStepData.elementclick) === null || _currentStepData$elem10 === void 0 ? void 0 : _currentStepData$elem10.ButtonId) || button.Id === (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem11 = currentStepData.elementclick) === null || _currentStepData$elem11 === void 0 ? void 0 : _currentStepData$elem11.Id))) {\n          var _currentStepData$elem12, _currentStepData$elem13;\n          console.log(\"🎯 Button click with element interaction - clicking element and advancing\", {\n            buttonId: button.Id,\n            expectedButtonId: currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem12 = currentStepData.elementclick) === null || _currentStepData$elem12 === void 0 ? void 0 : _currentStepData$elem12.ButtonId,\n            expectedId: currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$elem13 = currentStepData.elementclick) === null || _currentStepData$elem13 === void 0 ? void 0 : _currentStepData$elem13.Id,\n            elementclick: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.elementclick,\n            stepIndex: currentStepIndex,\n            currentStepData: currentStepData,\n            xpath: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath,\n            possibleElementPath: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.PossibleElementPath\n          });\n          const element = getElementByXPath(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath, currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.PossibleElementPath);\n          if (element) {\n            console.log(\"✅ Element found, clicking it:\", element);\n            element.click();\n            handleNext();\n          } else {\n            console.log(\"❌ Element not found for button click interaction\", {\n              xpath: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.xpath,\n              possibleElementPath: currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.PossibleElementPath,\n              stepIndex: currentStepIndex\n            });\n          }\n        } else if (button.ButtonAction.Action === \"previous\") {\n          handlePrevious();\n        } else if (button.ButtonAction.Action === \"restart\" || button.ButtonAction.Action === \"Restart\") {\n          var _steps$;\n          // Enhanced restart functionality with auto-scroll\n          console.log(\"🔄 Restarting tooltip guide\");\n\n          // Reset to the first step (1-based indexing)\n          setCurrentStepIndex(1);\n          setCurrentStep(1);\n\n          // Check if we need to navigate to a different page (multi-page) or stay on current page (single-page)\n          if ((_steps$ = steps[0]) !== null && _steps$ !== void 0 && _steps$.targetUrl && steps[0].targetUrl.trim() !== window.location.href.trim()) {\n            // Multi-page: Navigate to the first step's URL\n            window.location.href = steps[0].targetUrl;\n          } else {\n            var _steps$2;\n            // Single-page: Gentle scroll to first step element or top\n            if ((_steps$2 = steps[0]) !== null && _steps$2 !== void 0 && _steps$2.xpath) {\n              var _steps$3;\n              console.log(\"🎯 Gentle scroll to first step element on restart\");\n\n              // Check if first step element exists and use gentle scroll\n              const firstElement = getElementByXPath(steps[0].xpath, ((_steps$3 = steps[0]) === null || _steps$3 === void 0 ? void 0 : _steps$3.PossibleElementPath) || \"\");\n              if (firstElement) {\n                try {\n                  firstElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center',\n                    inline: 'nearest'\n                  });\n                  console.log(\"✅ Gentle restart scroll completed\");\n                } catch (error) {\n                  console.log(\"❌ Gentle restart scroll failed, scrolling to top\");\n                  window.scrollTo({\n                    top: 0,\n                    behavior: 'smooth'\n                  });\n                }\n              } else {\n                console.log(\"❌ First step element not found, scrolling to top\");\n                window.scrollTo({\n                  top: 0,\n                  behavior: 'smooth'\n                });\n              }\n            } else {\n              // No xpath available, just scroll to top\n              console.log(\"ℹ️ No xpath for first step, scrolling to top\");\n              window.scrollTo({\n                top: 0,\n                behavior: 'smooth'\n              });\n            }\n          }\n        } else if (button.ButtonAction.Action === \"open-url\" && button.ButtonAction.ActionValue === \"new-tab\") {\n          window.open(button.ButtonAction.TargetUrl, \"_blank\");\n        } else if (button.ButtonAction.Action === \"open-url\" && button.ButtonAction.ActionValue === \"same-tab\") {\n          window.location.href = button.ButtonAction.TargetUrl;\n        } else {\n          //onClose();\n        }\n      };\n      return /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        sx: buttonStyle,\n        onClick: handleClick,\n        children: button.ButtonName\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1509,\n        columnNumber: 7\n      }, this);\n    }) : null;\n  };\n  const TooltipContent = /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        placeContent: \"end\",\n        display: \"flex\"\n      },\n      children: enabelCross && /*#__PURE__*/_jsxDEV(IconButton, {\n        sx: {\n          position: \"absolute\",\n          boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\n          background: \"#fff !important\",\n          border: \"1px solid #ccc\",\n          zIndex: \"999\",\n          borderRadius: \"50px\",\n          padding: \"1px !important\",\n          float: \"right\",\n          top: \"-12px\",\n          right: \"-12px\",\n          margin: canvasStyle !== null && canvasStyle !== void 0 && canvasStyle.BorderSize && (canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderSize) !== \"0px\" ? `-${parseInt(canvasStyle === null || canvasStyle === void 0 ? void 0 : canvasStyle.BorderSize) - 3}px` : \"0px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n          sx: {\n            zoom: \"1\",\n            color: \"#000\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1542,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1526,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1524,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n      ref: scrollbarRef,\n      style: {\n        maxHeight: \"270px\"\n      },\n      options: {\n        suppressScrollY: !needsScrolling,\n        suppressScrollX: true,\n        wheelPropagation: false,\n        swipeEasing: true,\n        minScrollbarLength: 20,\n        scrollingThreshold: 1000,\n        scrollYMarginOffset: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: contentRef,\n        style: {\n          // maxHeight: \"270px\",\n          overflow: \"hidden\",\n          borderRadius: \"4px\",\n          padding: getPadding(),\n          position: \"relative\",\n          zIndex: \"999\"\n          // border: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || \"transparent\"}` : \"none\",\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [!hasOnlyButtons() && /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: hasOnlyText() ? \"flex-start\" : \"center\",\n            sx: {\n              width: hasOnlyText() ? \"auto\" : \"100%\",\n              padding: hasOnlyText() ? \"0\" : undefined\n            },\n            children: renderContent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1573,\n            columnNumber: 15\n          }, this), (currentStepData === null || currentStepData === void 0 ? void 0 : (_currentStepData$butt12 = currentStepData.buttonData) === null || _currentStepData$butt12 === void 0 ? void 0 : _currentStepData$butt12.length) > 0 && /*#__PURE__*/_jsxDEV(Box, {\n            ref: buttonContainerRef,\n            display: \"flex\",\n            sx: {\n              placeContent: \"center\",\n              // padding: hasOnlyButtons() ? \"4px\" : \"10px\",\n              gap: \"4px\",\n              backgroundColor: (_currentStepData$butt13 = currentStepData.buttonData[0]) === null || _currentStepData$butt13 === void 0 ? void 0 : _currentStepData$butt13.BackgroundColor,\n              width: hasOnlyButtons() ? \"auto\" : \"100%\",\n              alignItems: \"center\"\n            },\n            children: renderButtons()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1586,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1571,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1560,\n        columnNumber: 4\n      }, this)\n    }, `scrollbar-${needsScrolling}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1546,\n      columnNumber: 4\n    }, this), enableProgress && steps.length > 1 && selectedTemplate !== \"Hotspot\" && /*#__PURE__*/_jsxDEV(Box, {\n      children: renderProgress()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1604,\n      columnNumber: 78\n    }, this), \" \"]\n  }, void 0, true);\n\n  //const overlay = currentStep?.overlay || \"\";\n  const overlayStyle = {\n    position: \"fixed\",\n    top: 0,\n    left: 0,\n    width: \"100vw\",\n    height: \"100vh\",\n    backgroundColor: \"transparent\",\n    pointerEvents: \"none\",\n    zIndex: 9999\n  };\n  const getOverlaySections = () => {\n    if (!targetElement) return null;\n    const rect = targetElement.getBoundingClientRect();\n    const viewportHeight = window.innerHeight;\n    const viewportWidth = window.innerWidth;\n    const sections = {\n      top: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        width: \"100%\",\n        height: `${rect.top}px`,\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        pointerEvents: \"auto\"\n      },\n      bottom: {\n        position: \"fixed\",\n        top: `${rect.bottom}px`,\n        left: 0,\n        width: \"100%\",\n        height: `${viewportHeight - rect.bottom}px`,\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        pointerEvents: \"auto\"\n      },\n      left: {\n        position: \"fixed\",\n        top: `${rect.top}px`,\n        left: 0,\n        width: `${rect.left}px`,\n        height: `${rect.height}px`,\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        pointerEvents: \"auto\"\n      },\n      right: {\n        position: \"fixed\",\n        top: `${rect.top}px`,\n        left: `${rect.right}px`,\n        width: `${viewportWidth - rect.right}px`,\n        height: `${rect.height}px`,\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        pointerEvents: \"auto\"\n      }\n    };\n    return sections;\n  };\n  const highlightBoxStyle = targetElement ? {\n    position: \"fixed\",\n    top: `${targetElement.getBoundingClientRect().top}px`,\n    left: `${targetElement.getBoundingClientRect().left}px`,\n    width: `${targetElement.getBoundingClientRect().width}px`,\n    height: `${targetElement.getBoundingClientRect().height}px`,\n    // border: '2px solid #fff',\n    borderRadius: \"4px\",\n    pointerEvents: interactWithPage ? \"auto\" : \"none\",\n    zIndex: 0\n  } : {};\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [(currentStepData === null || currentStepData === void 0 ? void 0 : currentStepData.overlay) && targetElement && isElementVisible && selectedTemplate !== \"Tour\" && /*#__PURE__*/_jsxDEV(Box, {\n      sx: overlayStyle,\n      children: [Object.entries(getOverlaySections() || {}).map(([key, style]) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: style\n      }, key, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1687,\n        columnNumber: 7\n      }, this)), /*#__PURE__*/_jsxDEV(Box, {\n        sx: highlightBoxStyle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1693,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1685,\n      columnNumber: 9\n    }, this), targetElement && isElementVisible && /*#__PURE__*/_jsxDEV(CustomWidthTooltip, {\n      open: true,\n      title: TooltipContent,\n      placement: tooltipPlacement,\n      arrow: true,\n      PopperProps: {\n        anchorEl: targetElement,\n        modifiers: [{\n          name: \"preventOverflow\",\n          options: {\n            boundary: window,\n            altAxis: true,\n            padding: 10\n          }\n        }, {\n          name: \"computeStyles\",\n          options: {\n            // Disable adaptive positioning to prevent micro-adjustments\n            adaptive: false,\n            // Round positions to prevent sub-pixel rendering\n            roundOffsets: ({\n              x,\n              y\n            }) => ({\n              x: Math.round(x),\n              y: Math.round(y)\n            })\n          }\n        }]\n      },\n      canvasStyle: canvasStyle,\n      hasOnlyButtons: hasOnlyButtons(),\n      hasOnlyText: hasOnlyText()\n      //dynamicWidth={dynamicWidth}\n      ,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"absolute\",\n          top: targetElement.offsetTop,\n          left: targetElement.offsetLeft,\n          width: targetElement.offsetWidth,\n          height: targetElement.offsetHeight\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1733,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1698,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(TooltipGuide, \"79NxyOs3DArj5gYUZeP+CwpgTdc=\", false, function () {\n  return [useDrawerStore];\n});\n_c2 = TooltipGuide;\nexport default TooltipGuide;\nvar _c, _c2;\n$RefreshReg$(_c, \"CustomWidthTooltip\");\n$RefreshReg$(_c2, \"TooltipGuide\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useCallback", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Box", "LinearProgress", "Typography", "tooltipClasses", "MobileStepper", "IconButton", "CloseIcon", "styled", "useDrawerStore", "PerfectScrollbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomWidthTooltip", "className", "canvasStyle", "hasOnlyButtons", "hasOnlyText", "dynamicWidth", "props", "classes", "popper", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tooltip", "backgroundColor", "BackgroundColor", "padding", "Padding", "borderRadius", "<PERSON><PERSON>", "BorderRadius", "boxShadow", "border", "BorderSize", "BorderColor", "width", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "left", "XAxisOffset", "bottom", "YAxisOffset", "arrow", "color", "fontSize", "outlineWidth", "outlineColor", "outlineStyle", "zIndex", "Zindex", "_c", "TooltipGuide", "steps", "currentUrl", "onClose", "tooltipConfig", "startStepIndex", "data", "_s", "_currentStepData$moda", "_currentStepData$butt3", "_currentStepData$imag6", "_currentStepData$imag7", "_currentStepData$butt4", "_currentStepData$butt12", "_currentStepData$butt13", "rect", "currentStepIndex", "setCurrentStepIndex", "contentRef", "buttonContainerRef", "scrollOperationQueue", "Promise", "resolve", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "undefined", "setCurrentStep", "selectedTemplate", "currentStep", "ProgressColor", "createWithAI", "pageinteraction", "state", "currentStepData", "targetElement", "setTargetElement", "tooltipPosition", "setTooltipPosition", "top", "tooltipPlacement", "setTooltipPlacement", "isElementVisible", "setIsElementVisible", "observerRef", "prevPositionRef", "prevPlacementRef", "calculateBestPosition", "element", "getBoundingClientRect", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "spaceTop", "spaceBottom", "spaceLeft", "spaceRight", "right", "maxSpace", "Math", "max", "validateElementPosition", "height", "Number", "isNaN", "getElementByXPath", "xpath", "PossibleElementPath", "trim", "console", "log", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "error", "query", "fallback<PERSON><PERSON><PERSON>", "fallbackNode", "fallback<PERSON><PERSON>r", "smoothScrollTo", "targetTop", "duration", "maxScroll", "scrollHeight", "clientHeight", "clampedTargetTop", "min", "scrollTo", "behavior", "startTop", "scrollTop", "distance", "startTime", "performance", "now", "animateScroll", "currentTime", "elapsed", "progress", "easeInOutCubic", "t", "easedProgress", "requestAnimationFrame", "pollForElement", "possibleElementPath", "onElementFound", "maxAttempts", "initialIntervalMs", "logPrefix", "onElementNotFound", "elementCheckTimeout", "attempts", "currentInterval", "isCleanedUp", "cleanup", "clearTimeout", "checkForElement", "jitter", "random", "setTimeout", "universalScrollTo", "options", "isWindow", "documentElement", "scrollLeft", "scrollToTargetElement", "placement", "stepData", "tagName", "current", "then", "targetScrollTop", "scrollY", "targetScrollLeft", "scrollX", "maxScrollTop", "maxScrollLeft", "scrollWidth", "currentScrollY", "currentScrollX", "elementRect", "scrollSuccess", "body", "length", "interactWithPage", "InteractWithPage", "style", "pointerEvents", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "overlay", "overflow", "checkScrollNeeded", "contentHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "disconnect", "updateTargetAndPosition", "stepIndex", "<PERSON><PERSON><PERSON><PERSON>", "xOffset", "parseFloat", "positionXAxisOffset", "yOffset", "positionYAxisOffset", "newPlacement", "autoposition", "_currentStepData$canv", "validPlacements", "canvas", "Position", "includes", "newPosition", "round", "positionChanged", "abs", "handleNext", "nextIndex", "nextStepData", "currentIndex", "step<PERSON>itle", "scrollPromise", "foundElement", "isReasonablyVisible", "scrollIntoView", "block", "inline", "scrollError", "totalSteps", "prev", "handlePrevious", "prevIndex", "prevStepData", "prevElement", "isOutOfView", "_currentStepData$elem", "_currentStepData$elem2", "_currentStepData$elem3", "_currentStepData$elem4", "elementclick", "NextStep", "Id", "handleClick", "addEventListener", "removeEventListener", "_currentStepData$elem5", "handleDOMChanges", "targetNode", "characterData", "_observerRef$current", "handleViewportChanges", "timeoutId", "currentElement", "isCompletelyOutOfView", "enableProgress", "EnableProgress", "getProgressTemplate", "ProgressTemplate", "progressTemplate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modal", "DismissOption", "renderContent", "_currentStepData$imag", "_currentStepData$imag2", "_currentStepData$imag3", "_currentStepData$imag4", "_currentStepData$imag5", "hasImage", "imageUrl", "startsWith", "hasText", "Array", "isArray", "content", "some", "item", "Text", "isValidElement", "textStyle", "lineHeight", "whiteSpace", "wordBreak", "children", "component", "src", "imageproperties", "Url", "alt", "AltText", "sx", "objectFit", "Fit", "maxHeight", "SectionHeight", "margin", "dangerouslySetInnerHTML", "__html", "map", "replace", "join", "_currentStepData$butt", "hasButtons", "buttonData", "_currentStepData$butt2", "hasHtmlMeaningfulContent", "htmlContent", "cleanedContent", "tempDiv", "textContent", "innerText", "lowerContent", "toLowerCase", "emptyPatterns", "pattern", "hasValidTextContent", "hasValidImage", "hasOnlyTextContent", "hasOnlyButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getPadding", "_currentStepData$butt5", "_currentStepData$butt6", "_currentStepData$butt7", "_currentStepData$butt8", "_currentStepData$butt9", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ButtonAction", "Action", "toLocaleLowerCase", "_currentStepData$butt10", "renderProgress", "variant", "position", "activeStep", "place<PERSON><PERSON>nt", "backButton", "display", "nextButton", "alignItems", "gap", "from", "_", "index", "paddingTop", "value", "renderButtons", "_currentStepData$butt11", "button", "buttonStyle", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "fontFamily", "textTransform", "min<PERSON><PERSON><PERSON>", "opacity", "_currentStepData$elem6", "_currentStepData$elem7", "_currentStepData$elem8", "_currentStepData$elem9", "_currentStepData$elem10", "_currentStepData$elem11", "buttonId", "buttonAction", "expectedButtonId", "ButtonId", "_currentStepData$elem12", "_currentStepData$elem13", "expectedId", "click", "_steps$", "targetUrl", "location", "href", "_steps$2", "_steps$3", "firstElement", "ActionValue", "open", "TargetUrl", "onClick", "ButtonName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "background", "float", "parseInt", "zoom", "ref", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "flexDirection", "overlayStyle", "getOverlaySections", "sections", "highlightBoxStyle", "Object", "entries", "key", "title", "PopperProps", "anchorEl", "modifiers", "name", "boundary", "altAxis", "adaptive", "roundOffsets", "x", "y", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "_c2", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/tooltippreview/Tooltips/Tooltips.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport {\r\n  Button,\r\n  Tooltip,\r\n  Box,\r\n  LinearProgress,\r\n  Typography,\r\n  tooltipClasses,\r\n\tTooltipProps,\r\n  MobileStepper,\r\n\tBreadcrumbs,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport { CustomIconButton } from \"../../Button\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport useDrawerStore, { DrawerState } from \"../../../../store/drawerStore\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\ninterface ButtonAction {\r\n\tAction: string;\r\n\tActionValue: string;\r\n\tTargetUrl: string;\r\n}\r\ninterface ButtonProperties {\r\n\tPadding: number;\r\n\tWidth: number;\r\n\tFont: number;\r\n\tFontSize: number;\r\n\tButtonTextColor: string;\r\n\tButtonBackgroundColor: string;\r\n\tButtonBorderColor: string;\r\n}\r\ninterface Canvas {\r\n\tBackgroundColor: string;\r\n\tBorderColor: string;\r\n\tBorderSize: string;\r\n\tPadding: string;\r\n\tPosition: string;\r\n\tRadius: string;\r\n\tWidth: string;\r\n\tZindex: string;\r\n}\r\ninterface Modal {\r\n\tDismissOption: boolean;\r\n\tIncludeRequisiteButtons: boolean;\r\n\tInteractionWithPopup: boolean;\r\n\tModalPlacedOn: string;\r\n}\r\n\r\ninterface ButtonData {\r\n\tButtonStyle: string;\r\n\tButtonName: string;\r\n\tAlignment: string;\r\n\tBackgroundColor: string;\r\n\tButtonAction: ButtonAction;\r\n  Padding: {\r\n\t\tTop: number;\r\n\t\tRight: number;\r\n\t\tBottom: number;\r\n\t\tLeft: number;\r\n\t};\r\n\tButtonProperties: ButtonProperties;\r\n\tId: string;\r\n  }\r\ninterface CustomImage {\r\n\tAltText: string;\r\n\tBackgroundColor: string;\r\n\tFill: string;\r\n\tFit: string;\r\n\tSectionHeight: string;\r\n\tUrl: string;\r\n}\r\ninterface Design {\r\n\tNextStep: string;\r\n\tButtonName: string;\r\n\tElementPath: string;\r\n\tId: string;\r\n\tButtonId: string;\r\n}\r\n\r\ninterface Step {\r\n\txpath: string;\r\n\tcontent: string | JSX.Element;\r\n\ttargetUrl: string;\r\n\timageUrl: string;\r\n\tbuttonData: ButtonData[];\r\n\toverlay: boolean;\r\n\tpositionXAxisOffset: string;\r\n\tpositionYAxisOffset: string;\r\n\tcanvas: Canvas;\r\n\tmodal: Modal;\r\n\timageproperties: any;\r\n\tautoposition: any;\r\n\telementclick: Design;\r\n  PossibleElementPath: string\r\n}\r\ninterface TooltipGuideProps {\r\n\tsteps: Step[];\r\n\tcurrentUrl: string;\r\n\tonClose: () => void;\r\n\ttooltipConfig: any;\r\n\tstartStepIndex: any;\r\n\tdata: any;\r\n}\r\nconst CustomWidthTooltip = styled(({ className, canvasStyle, hasOnlyButtons, hasOnlyText, dynamicWidth, ...props }: TooltipProps & {\r\n\tcanvasStyle?: any,\r\n\thasOnlyButtons?: boolean,\r\n\thasOnlyText?: boolean,\r\n    dynamicWidth?: string | null\r\n}) => (\r\n\t<Tooltip\r\n\t\t{...props}\r\n\t\tclasses={{ popper: className }}\r\n\t\tid=\"Tooltip-unique\"\r\n\t/>\r\n))(({ canvasStyle, hasOnlyButtons, hasOnlyText, dynamicWidth }: {\r\n\tcanvasStyle: any,\r\n\thasOnlyButtons?: boolean,\r\n\thasOnlyText?: boolean,\r\n    dynamicWidth?: string | null\r\n  }) => ({\r\n    [`& .${tooltipClasses.tooltip}`]: {\r\n      backgroundColor: canvasStyle?.BackgroundColor || \"#ffffff\",\r\n\t\t// color: \"black\", // Set static text color\r\n\t\t//fontSize: \"14px\", // Font size\r\n      padding: hasOnlyButtons ? \"0px\" : canvasStyle.Padding,\r\n\t\t// padding: \"0px !important\",\r\n      borderRadius: canvasStyle?.Radius || canvasStyle?.BorderRadius||\"10px\",\r\n      boxShadow: \"0px 4px 12px rgba(0, 0, 0, 0.2)\",\r\n\t\tborder: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || \"transparent\"}` : \"none\",\r\n\t\t// width: (hasOnlyButtons ) ? \"auto !important\" :\r\n\t\t// \t   canvasStyle?.Width ? `${canvasStyle.Width} !important` : \"300px\",\r\n\t\twidth : 'auto !important',\r\n      maxWidth: canvasStyle?.Width ? `${canvasStyle.Width} !important` : \"300px\",\r\n      left: `${canvasStyle?.XAxisOffset || \"auto\"} !important`,\r\n      bottom: `${canvasStyle?.YAxisOffset || \"auto\"} !important`,\r\n    },\r\n    [`& .${tooltipClasses.arrow}`]: {\r\n      color: canvasStyle?.BackgroundColor || \"#ffffff\",\r\n      fontSize: \"16px\",\r\n      \"&:before\": {\r\n\t\t\toutlineWidth: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize}` : \"0px\", // This controls the width of the border of the arrow\r\n\t\t\toutlineColor: canvasStyle?.BorderColor || \"transparent\", // This controls the color of the border of the arrow\r\n\t\t\toutlineStyle: \"solid\", // Ensure the border is applied properly\r\n      },\r\n    },\r\n    [`&.MuiTooltip-popper`]: {\r\n\t\tzIndex: canvasStyle?.Zindex || 11000, // Tooltip z-index\r\n    },\r\n  }),\r\n)\r\n\r\nconst TooltipGuide: React.FC<TooltipGuideProps> = ({\r\n  steps,\r\n  currentUrl,\r\n  onClose,\r\n  tooltipConfig,\r\n  startStepIndex,\r\n  data,\r\n}) => {\r\n  let rect: any\r\n  const [currentStepIndex, setCurrentStepIndex] = useState(startStepIndex || 1)\r\n  const contentRef = useRef<HTMLDivElement>(null)\r\n  const buttonContainerRef = useRef<HTMLDivElement>(null)\r\n\r\n  // Auto-scroll related refs and state\r\n  const scrollOperationQueue = useRef<Promise<void>>(Promise.resolve())\r\n\r\n  // State to track if scrolling is needed\r\n  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n  const scrollbarRef = useRef<any>(null);\r\n\r\n  useEffect(() => {\r\n    if (startStepIndex !== undefined) {\r\n\t\t\tsetCurrentStepIndex(startStepIndex);\r\n    }\r\n\t}, [startStepIndex]);\r\n\tconst { setCurrentStep, selectedTemplate, currentStep, ProgressColor, createWithAI, pageinteraction } = useDrawerStore((state: DrawerState) => state);\r\n\r\n\tconst currentStepData = steps[currentStepIndex - 1];\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\tconst [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });\r\n\tconst [tooltipPlacement, setTooltipPlacement] = useState<\"top\" | \"left\" | \"right\" | \"bottom\">(\"top\");\r\n\tconst [isElementVisible, setIsElementVisible] = useState(false);\r\n  const observerRef = useRef<MutationObserver | null>(null)\r\n\r\n  // Add refs to store previous position values to prevent unnecessary updates\r\n  const prevPositionRef = useRef({ top: 0, left: 0 })\r\n\tconst prevPlacementRef = useRef<\"top\" | \"left\" | \"right\" | \"bottom\">(\"top\")\r\n\r\n\r\n  const calculateBestPosition = (element: HTMLElement): \"top\" | \"left\" | \"right\" | \"bottom\" => {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\tconst spaceTop = rect.top;\r\n\t\tconst spaceBottom = viewportHeight - rect.bottom;\r\n\t\tconst spaceLeft = rect.left;\r\n\t\tconst spaceRight = viewportWidth - rect.right;\r\n\r\n\t\tconst maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);\r\n\r\n\t\tif (maxSpace === spaceTop) return \"top\";\r\n\t\tif (maxSpace === spaceBottom) return \"bottom\";\r\n\t\tif (maxSpace === spaceLeft) return \"left\";\r\n\t\treturn \"right\";\r\n\t};\r\n\t// Removed unused isElementInViewport function\r\n\r\n  const validateElementPosition = (element: HTMLElement) => {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n    return (\r\n      rect.width > 0 &&\r\n      rect.height > 0 &&\r\n      rect.top !== 0 &&\r\n      rect.left !== 0 &&\r\n      !Number.isNaN(rect.top) &&\r\n      !Number.isNaN(rect.left)\r\n\t\t);\r\n\t};\r\n  const getElementByXPath = (xpath: string, PossibleElementPath: string): HTMLElement | null => {\r\n\t\t// Validate XPath before using it\r\n    if (!xpath || xpath.trim() === \"\") {\r\n\t\t\tconsole.log(\"XPath is empty or undefined, trying PossibleElementPath:\", PossibleElementPath);\r\n      if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\r\n        try {\r\n\t\t\t\t\tconst result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\t\t\tconst node = result.singleNodeValue;\r\n          if (node instanceof HTMLElement) {\r\n\t\t\t\t\t\treturn node;\r\n          } else if (node?.parentElement) {\r\n\t\t\t\t\t\treturn node.parentElement;\r\n          }\r\n        } catch (error) {\r\n\t\t\t\t\tconsole.error(\"Error evaluating PossibleElementPath:\", PossibleElementPath, error);\r\n        }\r\n      }\r\n\t\t\treturn null;\r\n    }\r\n\r\n    try {\r\n\t\t\tconst query = `${xpath}[not(ancestor::div[@id='quickAdopt_banner']) and not(@id='quickAdopt_banner')]`;\r\n\t\t\tconsole.log(\"Evaluating XPath query:\", query);\r\n\t\t\tconst result = document.evaluate(query, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\tconst node = result.singleNodeValue;\r\n      if (node instanceof HTMLElement) {\r\n\t\t\t\treturn node;\r\n      } else if (node?.parentElement) {\r\n\t\t\t\treturn node.parentElement;\r\n      } else if (node === null) {\r\n\t\t\t\t// Try PossibleElementPath as fallback\r\n        if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\r\n\t\t\t\t\tconsole.log(\"Primary XPath failed, trying PossibleElementPath:\", PossibleElementPath);\r\n\t\t\t\t\tconst fallbackResult = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\t\t\tconst fallbackNode = fallbackResult.singleNodeValue;\r\n          if (fallbackNode instanceof HTMLElement) {\r\n\t\t\t\t\t\treturn fallbackNode;\r\n          } else if (fallbackNode?.parentElement) {\r\n\t\t\t\t\t\treturn fallbackNode.parentElement;\r\n          }\r\n        }\r\n\t\t\t\treturn null;\r\n      } else {\r\n\t\t\t\treturn null;\r\n      }\r\n    } catch (error) {\r\n\t\t\tconsole.error(\"Error evaluating XPath:\", xpath, error);\r\n\t\t\t// Try PossibleElementPath as fallback\r\n      if (PossibleElementPath && PossibleElementPath.trim() !== \"\") {\r\n        try {\r\n\t\t\t\t\tconsole.log(\"XPath evaluation failed, trying PossibleElementPath:\", PossibleElementPath);\r\n\t\t\t\t\tconst result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\t\t\tconst node = result.singleNodeValue;\r\n          if (node instanceof HTMLElement) {\r\n\t\t\t\t\t\treturn node;\r\n          } else if (node?.parentElement) {\r\n\t\t\t\t\t\treturn node.parentElement;\r\n          }\r\n        } catch (fallbackError) {\r\n\t\t\t\t\tconsole.error(\"Error evaluating PossibleElementPath:\", PossibleElementPath, fallbackError);\r\n        }\r\n      }\r\n\t\t\treturn null;\r\n    }\r\n\t};\r\n\r\n\t// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\r\n\tconst smoothScrollTo = (element: HTMLElement, targetTop: number, duration: number = 300) => {\r\n\t\t// Ensure targetTop is within valid bounds\r\n\t\tconst maxScroll = element.scrollHeight - element.clientHeight;\r\n\t\tconst clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));\r\n\r\n\t\t// Method 1: Try native smooth scrolling first\r\n\t\ttry {\r\n\t\t\tif ('scrollTo' in element && typeof element.scrollTo === 'function') {\r\n\t\t\t\telement.scrollTo({\r\n\t\t\t\t\ttop: clampedTargetTop,\r\n\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Native smooth scrollTo failed, trying animation fallback\");\r\n\t\t}\r\n\r\n\t\t// Method 2: Manual animation fallback\r\n\t\ttry {\r\n\t\t\tconst startTop = element.scrollTop;\r\n\t\t\tconst distance = clampedTargetTop - startTop;\r\n\t\t\tconst startTime = performance.now();\r\n\r\n\t\t\tconst animateScroll = (currentTime: number) => {\r\n\t\t\t\tconst elapsed = currentTime - startTime;\r\n\t\t\t\tconst progress = Math.min(elapsed / duration, 1);\r\n\r\n\t\t\t\t// Easing function for smooth animation\r\n\t\t\t\tconst easeInOutCubic = (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\r\n\t\t\t\tconst easedProgress = easeInOutCubic(progress);\r\n\r\n\t\t\t\telement.scrollTop = startTop + (distance * easedProgress);\r\n\r\n\t\t\t\tif (progress < 1) {\r\n\t\t\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"RequestAnimationFrame failed, using direct assignment\");\r\n\t\t\t// Method 3: Direct assignment as final fallback\r\n\t\t\telement.scrollTop = clampedTargetTop;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced reusable element polling function with exponential backoff and better timing\r\n\tconst pollForElement = useCallback((\r\n\t\txpath: string,\r\n\t\tpossibleElementPath: string,\r\n\t\tonElementFound: (element: HTMLElement) => void,\r\n\t\tmaxAttempts: number = 30,\r\n\t\tinitialIntervalMs: number = 16, // Start with one frame\r\n\t\tlogPrefix: string = \"Element\",\r\n\t\tonElementNotFound?: () => void\r\n\t) => {\r\n\t\tlet elementCheckTimeout: NodeJS.Timeout | null = null;\r\n\t\tlet attempts = 0;\r\n\t\tlet currentInterval = initialIntervalMs;\r\n\t\tlet isCleanedUp = false;\r\n\r\n\t\tconst cleanup = () => {\r\n\t\t\tif (elementCheckTimeout) {\r\n\t\t\t\tclearTimeout(elementCheckTimeout);\r\n\t\t\t\telementCheckTimeout = null;\r\n\t\t\t}\r\n\t\t\tisCleanedUp = true;\r\n\t\t};\r\n\r\n\t\tconst checkForElement = () => {\r\n\t\t\tif (isCleanedUp) return;\r\n\r\n\t\t\tattempts++;\r\n\t\t\tconsole.log(`${logPrefix}: Polling attempt ${attempts}/${maxAttempts} (interval: ${currentInterval}ms)`);\r\n\r\n\t\t\tconst element = getElementByXPath(xpath, possibleElementPath);\r\n\r\n\t\t\tif (element && validateElementPosition(element)) {\r\n\t\t\t\tconsole.log(`${logPrefix}: Found and validated after ${attempts} attempts`);\r\n\t\t\t\tcleanup();\r\n\t\t\t\tonElementFound(element);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (attempts >= maxAttempts) {\r\n\t\t\t\tconsole.log(`${logPrefix}: Max attempts (${maxAttempts}) reached, element not found`);\r\n\t\t\t\tcleanup();\r\n\t\t\t\tif (onElementNotFound) {\r\n\t\t\t\t\tonElementNotFound();\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Exponential backoff with jitter to prevent thundering herd\r\n\t\t\tconst jitter = Math.random() * 0.1; // 10% jitter\r\n\t\t\tcurrentInterval = Math.min(currentInterval * (1.2 + jitter), 1000); // Cap at 1 second\r\n\r\n\t\t\telementCheckTimeout = setTimeout(checkForElement, currentInterval);\r\n\t\t};\r\n\r\n\t\t// Start the polling\r\n\t\tcheckForElement();\r\n\r\n\t\t// Return cleanup function\r\n\t\treturn cleanup;\r\n\t}, []);\r\n\r\n\t// Enhanced cross-environment scrolling function\r\n\tconst universalScrollTo = (element: HTMLElement | Window, options: { top?: number; left?: number; behavior?: 'smooth' | 'auto' }) => {\r\n\t\tconst isWindow = element === window;\r\n\t\tconst targetElement = isWindow ? document.documentElement : element as HTMLElement;\r\n\r\n\t\t// Method 1: Try native scrollTo if available and not blocked\r\n\t\tif (!isWindow && 'scrollTo' in element && typeof (element as any).scrollTo === 'function') {\r\n\t\t\ttry {\r\n\t\t\t\t(element as any).scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Native scrollTo blocked or failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 2: Try window.scrollTo for window element\r\n\t\tif (isWindow && options.behavior === 'smooth') {\r\n\t\t\ttry {\r\n\t\t\t\twindow.scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Window scrollTo failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 3: Try smooth scrolling with custom animation\r\n\t\tif (options.behavior === 'smooth' && options.top !== undefined) {\r\n\t\t\ttry {\r\n\t\t\t\tsmoothScrollTo(targetElement, options.top);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Smooth scroll animation failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 4: Direct property assignment (final fallback)\r\n\t\ttry {\r\n\t\t\tif (options.top !== undefined) {\r\n\t\t\t\ttargetElement.scrollTop = options.top;\r\n\t\t\t}\r\n\t\t\tif (options.left !== undefined) {\r\n\t\t\t\ttargetElement.scrollLeft = options.left;\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Direct property assignment failed:\", error);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t};\r\n\r\n\tconst scrollToTargetElement = useCallback(async (targetElement: HTMLElement, placement: \"top\" | \"left\" | \"right\" | \"bottom\", stepData?: Step) => {\r\n\t\tif (!targetElement) {\r\n\t\t\tconsole.log(\"ScrollToTargetElement: No target element provided\");\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconsole.log(\"🎯 Starting enhanced auto-scroll to target element:\", {\r\n\t\t\telement: targetElement,\r\n\t\t\ttagName: targetElement.tagName,\r\n\t\t\tclassName: targetElement.className,\r\n\t\t\tid: targetElement.id,\r\n\t\t\tplacement: placement\r\n\t\t});\r\n\r\n\t\ttry {\r\n\t\t\t// Add the scroll operation to the queue to prevent conflicts\r\n\t\t\tscrollOperationQueue.current = scrollOperationQueue.current.then(async () => {\r\n\t\t\t\tconst rect = targetElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Calculate optimal scroll position based on placement and viewport\r\n\t\t\t\tlet targetScrollTop = window.scrollY;\r\n\t\t\t\tlet targetScrollLeft = window.scrollX;\r\n\r\n\t\t\t\t// Enhanced positioning logic based on tooltip placement\r\n\t\t\t\tswitch (placement) {\r\n\t\t\t\t\tcase \"top\":\r\n\t\t\t\t\t\t// Position element in lower third of viewport to leave room for tooltip above\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\t\t// Position element in upper third of viewport to leave room for tooltip below\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"left\":\r\n\t\t\t\t\t\t// Position element towards right side to leave room for tooltip on left\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"right\":\r\n\t\t\t\t\t\t// Position element towards left side to leave room for tooltip on right\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t// Default: center the element vertically\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Ensure scroll positions are within valid bounds\r\n\t\t\t\tconst maxScrollTop = Math.max(0, document.documentElement.scrollHeight - viewportHeight);\r\n\t\t\t\tconst maxScrollLeft = Math.max(0, document.documentElement.scrollWidth - viewportWidth);\r\n\r\n\t\t\t\ttargetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));\r\n\t\t\t\ttargetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));\r\n\r\n\t\t\t\tconsole.log(\"📍 Calculated scroll position:\", {\r\n\t\t\t\t\ttargetScrollTop,\r\n\t\t\t\t\ttargetScrollLeft,\r\n\t\t\t\t\tcurrentScrollY: window.scrollY,\r\n\t\t\t\t\tcurrentScrollX: window.scrollX,\r\n\t\t\t\t\telementRect: rect\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Perform the scroll with multiple fallback methods\r\n\t\t\t\tlet scrollSuccess = false;\r\n\r\n\t\t\t\t// Method 1: Try smooth scrolling\r\n\t\t\t\ttry {\r\n\t\t\t\t\tscrollSuccess = universalScrollTo(window, {\r\n\t\t\t\t\t\ttop: targetScrollTop,\r\n\t\t\t\t\t\tleft: targetScrollLeft,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log(\"✅ Universal scroll success:\", scrollSuccess);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log(\"❌ Universal scroll failed:\", error);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 2: Fallback to immediate scroll if smooth scroll failed\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\twindow.scrollTo(targetScrollLeft, targetScrollTop);\r\n\t\t\t\t\t\tscrollSuccess = true;\r\n\t\t\t\t\t\tconsole.log(\"✅ Fallback scroll successful\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Fallback scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 3: Final fallback using direct property assignment\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tdocument.documentElement.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.documentElement.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tdocument.body.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.body.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tconsole.log(\"✅ Direct property assignment completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Direct property assignment failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Small delay to allow scroll to complete\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n\t\t\t\tconsole.log(\"🏁 Auto-scroll operation completed\");\r\n\t\t\t});\r\n\r\n\t\t\tawait scrollOperationQueue.current;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"❌ ScrollToTargetElement error:\", error);\r\n\t\t}\r\n\t}, [smoothScrollTo, universalScrollTo]);\r\n\r\n\tconst progress = ((currentStepIndex ) / steps.length) * 100;\r\n\tconst interactWithPage = tooltipConfig?.InteractWithPage;\r\n\r\n  useEffect(() => {\r\n    if (currentStep && currentStepIndex >= 0 && interactWithPage === false) {\r\n      console.log('[Tooltips.tsx] Setting document.body.style.pointerEvents =', selectedTemplate === \"Tour\" ? \"auto\" : \"none\");\r\n      document.body.style.pointerEvents = selectedTemplate === \"Tour\" ? \"auto\" : \"none\";\r\n      const style = document.createElement('style');\r\n      style.innerHTML = `.qadpt-editor  { pointer-events: auto !important; }`;\r\n      document.head.appendChild(style);\r\n      return () => {\r\n        console.log('[Tooltips.tsx] Resetting document.body.style.pointerEvents = auto');\r\n        document.body.style.pointerEvents = \"auto\";\r\n        document.head.removeChild(style);\r\n      };\r\n    }\r\n    return () => {\r\n      // In case there's no active step or tooltip is closed\r\n      console.log('[Tooltips.tsx] Cleanup: Resetting document.body.style.pointerEvents = auto');\r\n      document.body.style.pointerEvents = \"auto\"; // Enable interactions\r\n    };\r\n  }, [currentStepIndex, interactWithPage]);\r\n\r\n\t// Handle overflow hidden based on overlay and page interaction settings\r\n  useEffect(() => {\r\n    if (currentStep && currentStepIndex >= 0 && currentStepData?.overlay && interactWithPage === false) {\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n    } else {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n    }\r\n\r\n\t\t// Cleanup on unmount\r\n    return () => {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t};\r\n  }, [currentStepData?.overlay, interactWithPage]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStepData, currentStep]);\r\n\r\n  const updateTargetAndPosition = () => {\r\n\t\t// if (currentUrl !== currentStep?.targetUrl) {\r\n\t\t// \tsetTargetElement(null);\r\n\t\t// \tsetIsElementVisible(false);\r\n\t\t// \treturn;\r\n\t\t// }\r\n\r\n\t\t// Debug logging for XPath data\r\n    console.log(\"Tooltip updateTargetAndPosition - currentStepData:\", {\r\n      xpath: currentStepData?.xpath,\r\n      PossibleElementPath: currentStepData?.PossibleElementPath,\r\n      stepIndex: currentStepIndex,\r\n\t\t\tcreateWithAI: createWithAI\r\n\t\t});\r\n\r\n\t\tconst element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);\r\n    if (!element) {\r\n\t\t\tconsole.log(\"Tooltip element not found for XPath:\", currentStepData?.xpath, \"PossibleElementPath:\", currentStepData?.PossibleElementPath);\r\n\t\t\tsetTargetElement(null);\r\n\t\t\tsetIsElementVisible(false);\r\n\t\t\treturn;\r\n    }\r\n\r\n\t\tconst isValid = validateElementPosition(element);\r\n\t\t//const isVisible = isElementInViewport(element);\r\n\r\n    if (!isValid) {\r\n\t\t\tsetTargetElement(null);\r\n\t\t\tsetIsElementVisible(false);\r\n\t\t\treturn;\r\n    }\r\n\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\tconst xOffset = parseFloat(currentStepData?.positionXAxisOffset || \"0\");\r\n\t\tconst yOffset = parseFloat(currentStepData?.positionYAxisOffset || \"0\");\r\n\r\n\t\tsetTargetElement(element);\r\n\t\tsetIsElementVisible(true);\r\n\r\n    // Calculate placement\r\n    let newPlacement: \"top\" | \"left\" | \"right\" | \"bottom\"\r\n    if (currentStepData?.autoposition) {\r\n      newPlacement = calculateBestPosition(element)\r\n    } else {\r\n      const validPlacements = [\"top\", \"left\", \"right\", \"bottom\"] as const\r\n      const placement = currentStepData?.canvas?.Position || \"bottom\"\r\n      newPlacement = validPlacements.includes(placement as any)\r\n        ? (placement as \"top\" | \"left\" | \"right\" | \"bottom\")\r\n        : \"bottom\"\r\n    }\r\n\r\n\r\n    if (prevPlacementRef.current !== newPlacement) {\r\n      prevPlacementRef.current = newPlacement\r\n      setTooltipPlacement(newPlacement)\r\n    }\r\n\r\n\r\n    const newPosition = {\r\n      top: Math.round(rect.top + window.scrollY + yOffset),\r\n      left: Math.round(rect.left + window.scrollX + xOffset),\r\n    }\r\n\r\n\r\n    const positionChanged =\r\n      Math.abs(prevPositionRef.current.top - newPosition.top) > 1 ||\r\n      Math.abs(prevPositionRef.current.left - newPosition.left) > 1\r\n\r\n    if (positionChanged) {\r\n      prevPositionRef.current = newPosition\r\n      setTooltipPosition(newPosition)\r\n    }\r\n  }\r\n\r\n  // Enhanced auto-scroll navigation system for tooltip next functionality\r\n\tconst handleNext = useCallback(async () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\t// Calculate the next index first (currentStepIndex is 1-based, so we need to check against steps.length)\r\n\t\t\tconst nextIndex = currentStepIndex + 1;\r\n\r\n\t\t\tif (nextIndex <= steps.length) {\r\n\t\t\t\t// Get the next step data for scrolling (convert to 0-based for array access)\r\n\t\t\t\tconst nextStepData = steps[nextIndex - 1];\r\n\r\n\t\t\t\tconsole.log(\"🔍 Starting navigation to next step:\", {\r\n\t\t\t\t\tcurrentIndex: currentStepIndex,\r\n\t\t\t\t\tnextIndex: nextIndex,\r\n\t\t\t\t\txpath: nextStepData?.xpath,\r\n\t\t\t\t\tstepTitle: `Step ${nextIndex}`\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Smart auto-scroll: Only scroll if element is not reasonably visible\r\n\t\t\t\tif (nextStepData?.xpath) {\r\n\t\t\t\t\tconsole.log(\"🔍 Checking auto-scroll for next element:\", {\r\n\t\t\t\t\t\txpath: nextStepData.xpath,\r\n\t\t\t\t\t\tstepIndex: nextIndex,\r\n\t\t\t\t\t\tstepTitle: `Step ${nextIndex}`\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Create a promise to handle element finding and scrolling\r\n\t\t\t\t\tconst scrollPromise = new Promise<void>((resolve) => {\r\n\t\t\t\t\t\t// Use polling to find the element\r\n\t\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\t\tnextStepData.xpath || \"\",\r\n\t\t\t\t\t\t\tnextStepData.PossibleElementPath || \"\",\r\n\t\t\t\t\t\t\tasync (foundElement) => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Next element found:\", {\r\n\t\t\t\t\t\t\t\t\telement: foundElement,\r\n\t\t\t\t\t\t\t\t\ttagName: foundElement.tagName,\r\n\t\t\t\t\t\t\t\t\tclassName: foundElement.className,\r\n\t\t\t\t\t\t\t\t\tid: foundElement.id\r\n\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t// Check if element needs scrolling\r\n\t\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\t\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t\t\t\t\t// More generous visibility check - element should be reasonably visible\r\n\t\t\t\t\t\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\t\t\t\t\t\trect.top >= -50 && // Allow some element to be above viewport\r\n\t\t\t\t\t\t\t\t\trect.left >= -50 && // Allow some element to be left of viewport\r\n\t\t\t\t\t\t\t\t\trect.bottom <= viewportHeight + 50 && // Allow some element below viewport\r\n\t\t\t\t\t\t\t\t\trect.right <= viewportWidth + 50 && // Allow some element right of viewport\r\n\t\t\t\t\t\t\t\t\trect.width > 0 &&\r\n\t\t\t\t\t\t\t\t\trect.height > 0\r\n\t\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\t\tif (isReasonablyVisible) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"Minimal scroll adjustment failed:\", error);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"🎯 Element not visible, performing auto-scroll\");\r\n\t\t\t\t\t\t\t\t\t// Element is not visible, scroll to bring it into view\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'center', // Center it for better visibility\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Auto-scroll completed successfully\");\r\n\t\t\t\t\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error(\"❌ Auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t20, // Reasonable maxAttempts\r\n\t\t\t\t\t\t\t30, // Reasonable initial interval\r\n\t\t\t\t\t\t\t\"Next step element\",\r\n\t\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"❌ Next element not found after polling, continuing without scroll\");\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tawait scrollPromise;\r\n\t\t\t\t\t\tconsole.log(\"✅ Element finding and scroll check completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error(\"❌ Scroll promise failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log(\"ℹ️ No xpath provided for next step, skipping auto-scroll\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Update the step index AFTER scrolling is complete\r\n\t\t\t\tconsole.log(\"🔄 Updating step index from\", currentStepIndex, \"to\", nextIndex);\r\n\t\t\t\tsetCurrentStepIndex(nextIndex);\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\r\n\t\t\t\t// Small delay to allow DOM to update after step change\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 50));\r\n\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log(\"🏁 Reached end of tooltip steps\", {\r\n\t\t\t\t\tcurrentStepIndex,\r\n\t\t\t\t\tnextIndex,\r\n\t\t\t\t\ttotalSteps: steps.length\r\n\t\t\t\t});\r\n\t\t\t\t// onClose(); // Close tooltip if no more steps\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// Tour template logic (consistent with 1-based indexing)\r\n\t\t\tif (currentStep < steps?.length) {\r\n\t\t\t\tsetCurrentStepIndex((prev: any) => Math.max(prev + 1, 1));\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, calculateBestPosition, scrollToTargetElement, setCurrentStep, setCurrentStepIndex]);\r\n\r\n  // Enhanced handlePrevious with auto-scroll functionality\r\n\tconst handlePrevious = useCallback(async () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tconst prevIndex = Math.max(currentStepIndex - 1, 1);\r\n\r\n\t\t\tif (prevIndex >= 1) {\r\n\t\t\t\tconsole.log(\"🔙 Navigating to previous step:\", {\r\n\t\t\t\t\tcurrentIndex: currentStepIndex,\r\n\t\t\t\t\tprevIndex: prevIndex\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Smart previous navigation scrolling\r\n\t\t\t\tif (prevIndex === 1) {\r\n\t\t\t\t\tconsole.log(\"HandlePrevious: Going back to first step, scroll to top\");\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\twindow.scrollTo({\r\n\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"HandlePrevious: Scroll to top failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// For other steps, check if previous step element needs scrolling\r\n\t\t\t\t\tconst prevStepData = steps[prevIndex - 1];\r\n\t\t\t\t\tif (prevStepData?.xpath) {\r\n\t\t\t\t\t\tconsole.log(\"HandlePrevious: Checking if previous step element needs scrolling\");\r\n\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tconst prevElement = getElementByXPath(prevStepData.xpath, prevStepData.PossibleElementPath || \"\");\r\n\t\t\t\t\t\t\tif (prevElement) {\r\n\t\t\t\t\t\t\t\tconst rect = prevElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\t\tconst isOutOfView = (\r\n\t\t\t\t\t\t\t\t\trect.bottom < 0 ||\r\n\t\t\t\t\t\t\t\t\trect.top > window.innerHeight ||\r\n\t\t\t\t\t\t\t\t\trect.right < 0 ||\r\n\t\t\t\t\t\t\t\t\trect.left > window.innerWidth\r\n\t\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\t\tif (isOutOfView) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Previous element out of view, scrolling\");\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tprevElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Element scroll failed:\", error);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}, 100);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Update step index\r\n\t\t\t\tsetCurrentStepIndex(prevIndex);\r\n\t\t\t\tsetCurrentStep(currentStep - 1);\r\n\r\n\t\t\t\t// Small delay to allow DOM to update\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 50));\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t}\r\n\t}, [currentStepIndex, selectedTemplate, currentStep, universalScrollTo, setCurrentStep, setCurrentStepIndex]);\r\n  useEffect(() => {\r\n    // Debug logging for AI tooltip button click functionality\r\n    console.log(\"🔍 Tooltip useEffect - Element click setup:\", {\r\n      currentStepIndex,\r\n      elementclick: currentStepData?.elementclick,\r\n      NextStep: currentStepData?.elementclick?.NextStep,\r\n      Id: currentStepData?.elementclick?.Id,\r\n      xpath: currentStepData?.xpath\r\n    });\r\n\r\n    if (currentStepData?.elementclick?.NextStep === \"element\" || currentStepData?.elementclick?.NextStep === \"button\") {\r\n\t\t\tconst element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);\r\n      if (element) {\r\n        console.log(\"✅ Element found for click handler:\", element);\r\n        const handleClick = () => {\r\n\t\t\t\t\tconsole.log(\"🖱️ Element clicked - advancing to next step\");\r\n\t\t\t\t\thandleNext();\r\n\t\t\t\t};\r\n\r\n\t\t\t\telement.addEventListener(\"click\", handleClick);\r\n        return () => {\r\n\t\t\t\t\telement.removeEventListener(\"click\", handleClick);\r\n\t\t\t\t};\r\n        } else {\r\n        console.log(\"❌ Element not found for xpath:\", currentStepData?.xpath);\r\n      }\r\n\t\t} else {\r\n\t\t  console.log(\"ℹ️ No element click setup - NextStep:\", currentStepData?.elementclick?.NextStep);\r\n\t\t}\r\n\t}, [currentStepData, handleNext]);\r\n\r\n  useEffect(() => {\r\n    const handleDOMChanges = () => {\r\n\t\t\trequestAnimationFrame(updateTargetAndPosition);\r\n\t\t};\r\n\t\tobserverRef.current = new MutationObserver(handleDOMChanges);\r\n\t\tconst targetNode = document.body;\r\n    observerRef.current.observe(targetNode, {\r\n      childList: true,\r\n      subtree: true,\r\n      attributes: true,\r\n      characterData: true,\r\n\t\t});\r\n\t\tupdateTargetAndPosition();\r\n    return () => {\r\n\t\t\tobserverRef.current?.disconnect();\r\n\t\t};\r\n\t}, [currentStepData, currentUrl]);\r\n\r\n  useEffect(() => {\r\n\t\tconst handleViewportChanges = () => {\r\n\t\t\trequestAnimationFrame(updateTargetAndPosition);\r\n\t\t};\r\n\r\n\t\twindow.addEventListener(\"scroll\", handleViewportChanges);\r\n\t\twindow.addEventListener(\"resize\", handleViewportChanges);\r\n\r\n    return () => {\r\n\t\t\twindow.removeEventListener(\"scroll\", handleViewportChanges);\r\n\t\t\twindow.removeEventListener(\"resize\", handleViewportChanges);\r\n\t\t};\r\n\t}, [currentStepData, currentUrl]);\r\n  useEffect(() => {\r\n\t\tupdateTargetAndPosition();\r\n\t}, [currentStepData, currentUrl, rect]); // Ensure all dependencies are included\r\n\r\n\t// Smart auto-scroll for current step changes - only when element is not visible\r\n\tuseEffect(() => {\r\n\t\tif (!currentStepData?.xpath) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Small delay to allow DOM to settle\r\n\t\tconst timeoutId = setTimeout(() => {\r\n\t\t\tconst currentElement = getElementByXPath(currentStepData.xpath, currentStepData.PossibleElementPath || \"\");\r\n\r\n\t\t\tif (currentElement) {\r\n\t\t\t\tconst rect = currentElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Check if element is completely out of view\r\n\t\t\t\tconst isCompletelyOutOfView = (\r\n\t\t\t\t\trect.bottom < 0 || // Completely above viewport\r\n\t\t\t\t\trect.top > viewportHeight || // Completely below viewport\r\n\t\t\t\t\trect.right < 0 || // Completely left of viewport\r\n\t\t\t\t\trect.left > viewportWidth // Completely right of viewport\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (isCompletelyOutOfView) {\r\n\t\t\t\t\tconsole.log(\"🔄 Current step element is out of view, gentle auto-scroll\");\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tcurrentElement.scrollIntoView({\r\n\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"Current step auto-scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 100);\r\n\r\n\t\treturn () => {\r\n\t\t\tclearTimeout(timeoutId);\r\n\t\t};\r\n\t}, [currentStepData]);\r\n\tconst canvasStyle = currentStepData?.canvas || {}; // Assuming canvas is an array, take the first item\r\n\tconst enableProgress = tooltipConfig.EnableProgress || false;\r\n  function getProgressTemplate(tooltipConfig: any) {\r\n    if (tooltipConfig?.ProgressTemplate === \"1\") {\r\n\t\t\treturn \"dots\";\r\n    } else if (tooltipConfig?.ProgressTemplate === \"2\") {\r\n\t\t\treturn \"linear\";\r\n    } else if (tooltipConfig?.ProgressTemplate === \"3\") {\r\n\t\t\treturn \"BreadCrumbs\";\r\n    } else {\r\n\t\t\treturn \"breadcrumbs\";\r\n    }\r\n  }\r\n\tconst progressTemplate = getProgressTemplate(tooltipConfig);\r\n\r\n\tconst enabelCross = currentStepData?.modal?.DismissOption;\r\n  const renderContent = () => {\r\n\t\tconst hasImage =\r\n\t\t\tcurrentStepData?.imageUrl.startsWith(\"data:image/\") || currentStepData?.imageUrl.startsWith(\"http\");\r\n    const hasText = Array.isArray(currentStepData?.content)\r\n      ? currentStepData.content.some((item) => item?.Text && typeof item.Text === \"string\" && item.Text.trim() !== \"\")\r\n\t\t\t: typeof currentStepData?.content === \"string\" || React.isValidElement(currentStepData?.content);\r\n    const textStyle = {\r\n      fontSize: \"14px\",\r\n      lineHeight: \"1.5\",\r\n      whiteSpace: \"pre-wrap\",\r\n      wordBreak: \"break-word\",\r\n      color: \"black\",\r\n\t\t};\r\n    return (\r\n      <Box>\r\n        {hasImage && (\r\n          <Box\r\n            component=\"img\"\r\n            src={currentStepData?.imageproperties?.Url}\r\n            alt={currentStepData?.imageproperties?.AltText || \"Step Image\"}\r\n            sx={{\r\n              backgroundColor: currentStepData?.imageproperties?.BackgroundColor || \"transparent\",\r\n              objectFit: currentStepData?.imageproperties?.Fit || \"cover\",\r\n              maxHeight: currentStepData?.imageproperties?.SectionHeight || \"auto\",\r\n              width: \"100%\",\r\n            }}\r\n          />\r\n        )}\r\n        {hasText && (\r\n          <Box\r\n            className=\"qadpt-preview\"\r\n\t\t\t\t\tsx={{ margin: \"0 !important\", ...textStyle ,\"& p\": {\r\n\t\t\t\t\t\tmargin: \"4px 0\",\r\n\t\t\t\t\t  },}}\t\t\t\t\tdangerouslySetInnerHTML={{\r\n              __html: Array.isArray(currentStepData.content)\r\n                ? currentStepData.content\r\n\t\t\t\t\t\t\t\t\t.map((item: any) =>\r\n\t\t\t\t\t\t\t\t\t\titem.Text.replace(/<a /g, '<a target=\"_blank\" rel=\"noopener noreferrer\" ')\r\n\t\t\t\t\t\t\t\t\t)\r\n                    .join(\"<br/>\")\r\n                : typeof currentStepData.content === \"string\"\r\n                  ? currentStepData.content.replace(/<a /g, '<a target=\"_blank\" rel=\"noopener noreferrer\" ')\r\n                  : \"\",\r\n            }}\r\n          />\r\n\r\n\t\t\t\t)}\r\n\t\t\t</Box>\r\n\t\t);\r\n\t};\r\n\r\n\t// Helper function to check if tooltip has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage =\r\n\t\t\tcurrentStepData?.imageUrl.startsWith(\"data:image/\") || currentStepData?.imageUrl.startsWith(\"http\");\r\n\t\tconst hasText = Array.isArray(currentStepData?.content)\r\n\t\t\t? currentStepData.content.some((item) => item?.Text && typeof item.Text === \"string\" && item.Text.trim() !== \"\")\r\n\t\t\t: typeof currentStepData?.content === \"string\" || React.isValidElement(currentStepData?.content);\r\n\t\tconst hasButtons = currentStepData?.buttonData?.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if tooltip has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage =\r\n\t\t\tcurrentStepData?.imageUrl.startsWith(\"data:image/\") || currentStepData?.imageUrl.startsWith(\"http\");\r\n\t\tconst hasText = Array.isArray(currentStepData?.content)\r\n\t\t\t? currentStepData.content.some((item) => item?.Text && typeof item.Text === \"string\" && item.Text.trim() !== \"\")\r\n\t\t\t: typeof currentStepData?.content === \"string\" || React.isValidElement(currentStepData?.content);\r\n\t\tconst hasButtons = currentStepData?.buttonData?.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\tconst hasHtmlMeaningfulContent = (htmlContent: string): boolean => {\r\n\t\tif (!htmlContent || htmlContent.trim() === '') {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t// Clean up common empty HTML patterns before checking\r\n\t\tlet cleanedContent = htmlContent;\r\n\r\n\t\t// Remove empty paragraph tags\r\n\t\tcleanedContent = cleanedContent.replace(/<p>\\s*(&nbsp;)*\\s*<\\/p>/gi, '');\r\n\r\n\t\t// Remove empty div tags\r\n\t\tcleanedContent = cleanedContent.replace(/<div>\\s*(&nbsp;)*\\s*<\\/div>/gi, '');\r\n\r\n\t\t// Remove empty span tags\r\n\t\tcleanedContent = cleanedContent.replace(/<span>\\s*(&nbsp;)*\\s*<\\/span>/gi, '');\r\n\r\n\t\t// Remove <br> tags\r\n\t\tcleanedContent = cleanedContent.replace(/<br\\s*\\/?>/gi, '');\r\n\r\n\t\t// Remove &nbsp; entities\r\n\t\tcleanedContent = cleanedContent.replace(/&nbsp;/gi, ' ');\r\n\r\n\t\t// If after cleaning there's no content left, return false\r\n\t\tif (cleanedContent.trim() === '') {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t// Create a temporary div to parse the cleaned HTML content\r\n\t\tconst tempDiv = document.createElement('div');\r\n\t\ttempDiv.innerHTML = cleanedContent;\r\n\r\n\t\t// Get the text content (strips all HTML tags)\r\n\t\tconst textContent = tempDiv.textContent || tempDiv.innerText;\r\n\r\n\t\t// Check if there's any non-whitespace text content\r\n\t\tif (textContent === null || textContent.trim() === '') {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\t// Additional check for common empty HTML patterns\r\n\t\t// This handles cases like \"<div><br></div>\" or \"<p>&nbsp;</p>\" that might appear non-empty\r\n\t\tconst lowerContent = cleanedContent.toLowerCase();\r\n\t\tconst emptyPatterns = [\r\n\t\t\t'<div><br></div>',\r\n\t\t\t'<p><br></p>',\r\n\t\t\t'<div></div>',\r\n\t\t\t'<p></p>',\r\n\t\t\t'<span></span>',\r\n\t\t\t'<p>&nbsp;</p>',\r\n\t\t\t'<div>&nbsp;</div>',\r\n\t\t\t'<p> </p>',\r\n\t\t\t'<div> </div>'\r\n\t\t];\r\n\r\n\t\tif (emptyPatterns.some(pattern => lowerContent.includes(pattern)) && textContent.trim().length <= 1) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t};\r\n\r\n\tconst hasValidTextContent =\r\n\t\t(typeof currentStepData?.content === \"string\" && hasHtmlMeaningfulContent(currentStepData?.content)) ||\r\n\t\tReact.isValidElement(currentStepData?.content) ||\r\n\t\t(Array.isArray(currentStepData?.content) && currentStepData.content.some(\r\n\t\t\t(item: any) => item?.Text && typeof item.Text === \"string\" && hasHtmlMeaningfulContent(item.Text)\r\n\t\t));\r\n\t // Check if there are buttons\r\n\tconst hasButtons = currentStepData?.buttonData?.length > 0;\r\n\r\n\t//Check if there's a valid image\r\n\tconst hasValidImage =\r\n\t\tcurrentStepData?.imageUrl?.startsWith(\"data:image/\") ||\r\n\t\tcurrentStepData?.imageUrl?.startsWith(\"http\");\r\n\t// Check if there's only text content (no images or buttons)\r\n\tconst hasOnlyTextContent = hasValidTextContent && !hasValidImage && !hasButtons;\r\n\r\n\t// Check if there's only a button (no text or images)\r\n\tconst hasOnlyButton = hasButtons && !hasValidTextContent && !hasValidImage && currentStepData?.buttonData?.length === 1;\r\n\r\n\t// Check if there's any meaningful content to display\r\n\tconst hasValidContent = hasValidTextContent || hasValidImage;\r\n\t\t//Function to determine padding based on content and buttons\r\n\t\tconst getPadding = () => {\r\n\t\t\t// Check if we have exactly one button and it's a previous button\r\n\t\t\tconst hasPreviousButton = currentStepData?.buttonData?.length === 1 &&\r\n\t\t\t\tcurrentStepData?.buttonData?.[0]?.ButtonAction?.Action?.toLocaleLowerCase() === \"previous\";\r\n\r\n\t\t\t// Special case for previous button\r\n\t\t\tif (hasPreviousButton) {\r\n\t\t\t\treturn \"0px\";\r\n\t\t\t}\r\n\r\n\t\t\t// Original logic\r\n\t\t\tif (!hasValidContent) {\r\n\t\t\t\treturn currentStepData?.buttonData?.length === 1 ? \"0px\" : \"4px\";\r\n\t\t\t} else {\r\n\t\t\t\treturn \"0px\";\r\n    }\r\n\t\t};\r\n\r\n\r\n\t// Function to calculate the optimal width based on content and buttons\r\n\t// const calculateOptimalWidth = () => {\r\n\t// \t// If we have a fixed width from canvas settings and not a compact tooltip, use that\r\n\t// \tif (canvasStyle?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t// \t\treturn `${canvasStyle.Width}`;\r\n\t// \t}\r\n\r\n\t// \t// For tooltips with only buttons or only text, use auto width\r\n\t// \tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t// \t\treturn \"auto\";\r\n\t// \t}\r\n\r\n\t// \t// Get the width of content and button container\r\n\t// \tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t// \tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t// \t// Use the larger of the two, with some minimum and maximum constraints\r\n\t// \tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t// \t// Add some padding to ensure text has room to wrap naturally\r\n\t// \tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\r\n\r\n\t// \t// Ensure width is between reasonable bounds\r\n\t// \tconst minWidth = 250; // Minimum width\r\n\t// \tconst maxWidth = 800; // Maximum width\r\n\r\n\t// \tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t// \treturn `${finalWidth}px`;\r\n\t// };\r\n\r\n\t// Update dynamic width when content or buttons change\r\n\t// useEffect(() => {\r\n\t// \t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t// \trequestAnimationFrame(() => {\r\n\t// \t\tconst newWidth = calculateOptimalWidth();\r\n\t// \t\tsetDynamicWidth(newWidth);\r\n\t// \t});\r\n\t// }, [currentStepData, currentStepIndex]);\r\n  const renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n    if (progressTemplate === \"dots\") {\r\n      return (\r\n        <MobileStepper\r\n          variant=\"dots\"\r\n          steps={steps.length}\r\n          position=\"static\"\r\n          activeStep={currentStepIndex - 1}\r\n          sx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\", \"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n            },\r\n            placeContent: \"center\",\r\n            padding: \"2px  !important\",\r\n            \"& .MuiMobileStepper-dot\": {\r\n              width: \"6px !important\",\r\n              height: \"6px !important\",\r\n\t\t\t\t\t\t}\r\n          }}\r\n          backButton={<Button style={{ display: \"none\" }} />}\r\n          nextButton={<Button style={{ display: \"none\" }} />}\r\n        />\r\n\t\t\t);\r\n    }\r\n\r\n\t\t// if (progressTemplate === \"breadcrumbs\") {\r\n\t\t// \treturn (\r\n\t\t// \t\t<Breadcrumbs\r\n\t\t// \t\t\taria-label=\"breadcrumb\"\r\n\t\t// \t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t// \t\t>\r\n\t\t// \t\t\t{steps.map((_, index) => (\r\n\t\t// \t\t\t\t<Typography\r\n\t\t// \t\t\t\t\tkey={index}\r\n\t\t// \t\t\t\t\tcolor={index === currentStepIndex ? \"primary\" : \"text.secondary\"}\r\n\t\t// \t\t\t\t>\r\n\t\t// \t\t\t\t\tStep {index + 1} of {steps.length}\r\n\t\t// \t\t\t\t</Typography>\r\n\t\t// \t\t\t))}\r\n\t\t// \t\t</Breadcrumbs>\r\n\t\t// \t);\r\n\t\t// }\r\n    if (progressTemplate === \"BreadCrumbs\") {\r\n      return (\r\n                <Box sx={{display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"5px\",padding:\"8px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n          {Array.from({ length: steps.length }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n                          borderRadius: '100px',\r\n              }}\r\n            />\r\n          ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{paddingTop:\"8px\"}}>\r\n\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\tsx={{ padding: \"8px\", color: ProgressColor}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t Step {currentStepIndex} of {steps.length}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{padding: hasOnlyButtons() ? \"8px\" : \"0\",}}>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : \"6px 10px\",\r\n\t\t\t\t\t\t\t\t'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\r\n\tconst renderButtons = () => {\r\n\t\treturn currentStepData?.buttonData?.length > 0\r\n\t\t\t? currentStepData.buttonData.map((button: any, index: any) => {\r\n\t\t\t\t\tconst buttonStyle = {\r\n\t\t\t\t\t\tbackgroundColor: button.ButtonProperties.ButtonBackgroundColor,\r\n\t\t\t\t\t\tcolor: button.ButtonProperties.ButtonTextColor,\r\n\t\t\t\t\t\tborder: button.ButtonProperties.ButtonBorderColor,\r\n\t\t\t\t\t\tpadding: \"4px 8px !important\",\r\n\t\t\t\t\t\tlineHeight: \"normal\",\r\n\t\t\t\t\t\twidth: \"auto\",\r\n\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\tfontFamily: \"Poppins\",\r\n\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\tminWidth: \"fit-content\",\r\n\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties.ButtonBackgroundColor, // Keep the same background color on hover\r\n\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t};\r\n\t\t\t\t\tconst handleClick = () => {\r\n\t\t\t\t\t\tconsole.log(\"🔍 Button clicked:\", {\r\n\t\t\t\t\t\t\tbuttonId: button.Id,\r\n\t\t\t\t\t\t\tbuttonAction: button.ButtonAction.Action,\r\n\t\t\t\t\t\t\telementclick: currentStepData?.elementclick,\r\n\t\t\t\t\t\t\tNextStep: currentStepData?.elementclick?.NextStep,\r\n\t\t\t\t\t\t\texpectedButtonId: currentStepData?.elementclick?.Id\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tif (button.ButtonAction.Action.toLocaleLowerCase() === \"close\") {\r\n\t\t\t\t\t\t\t//onClose();\r\n\t\t\t\t\t\t} else if (\r\n\t\t\t\t\t\t\tbutton.ButtonAction.Action.toLocaleLowerCase() === \"next\" &&\r\n\t\t\t\t\t\t\tcurrentStepData?.elementclick?.NextStep !== \"button\"\r\n\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\tconsole.log(\"🚀 Regular next button - advancing step\");\r\n\t\t\t\t\t\t\thandleNext();\r\n\t\t\t\t\t\t} else if (\r\n\t\t\t\t\t\t\tbutton.ButtonAction.Action.toLocaleLowerCase() === \"next\" &&\r\n\t\t\t\t\t\t\tcurrentStepData?.elementclick?.NextStep === \"button\" &&\r\n\t\t\t\t\t\t\t(button.Id === currentStepData?.elementclick?.ButtonId ||\r\n\t\t\t\t\t\t\t button.Id === currentStepData?.elementclick?.Id)\r\n\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\tconsole.log(\"🎯 Button click with element interaction - clicking element and advancing\", {\r\n\t\t\t\t\t\t\t\tbuttonId: button.Id,\r\n\t\t\t\t\t\t\t\texpectedButtonId: currentStepData?.elementclick?.ButtonId,\r\n\t\t\t\t\t\t\t\texpectedId: currentStepData?.elementclick?.Id,\r\n\t\t\t\t\t\t\t\telementclick: currentStepData?.elementclick,\r\n\t\t\t\t\t\t\t\tstepIndex: currentStepIndex,\r\n\t\t\t\t\t\t\t\tcurrentStepData: currentStepData,\r\n\t\t\t\t\t\t\t\txpath: currentStepData?.xpath,\r\n\t\t\t\t\t\t\t\tpossibleElementPath: currentStepData?.PossibleElementPath\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tconst element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);\r\n              if (element) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Element found, clicking it:\", element);\r\n\t\t\t\t\t\t\t\telement.click();\r\n\t\t\t\t\t\t\t\thandleNext();\r\n              } else {\r\n              \tconsole.log(\"❌ Element not found for button click interaction\", {\r\n\t\t\t\t\t\t\t\t\txpath: currentStepData?.xpath,\r\n\t\t\t\t\t\t\t\t\tpossibleElementPath: currentStepData?.PossibleElementPath,\r\n\t\t\t\t\t\t\t\t\tstepIndex: currentStepIndex\r\n\t\t\t\t\t\t\t\t});\r\n              }\r\n            } else if (button.ButtonAction.Action === \"previous\") {\r\n\t\t\t\t\t\t\thandlePrevious();\r\n            } else if (button.ButtonAction.Action === \"restart\" || button.ButtonAction.Action === \"Restart\") {\r\n\t\t\t\t\t\t\t// Enhanced restart functionality with auto-scroll\r\n\t\t\t\t\t\t\tconsole.log(\"🔄 Restarting tooltip guide\");\r\n\r\n\t\t\t\t\t\t\t// Reset to the first step (1-based indexing)\r\n\t\t\t\t\t\t\tsetCurrentStepIndex(1);\r\n\t\t\t\t\t\t\tsetCurrentStep(1);\r\n\r\n\t\t\t\t\t\t\t// Check if we need to navigate to a different page (multi-page) or stay on current page (single-page)\r\n\t\t\t\t\t\t\tif (steps[0]?.targetUrl && steps[0].targetUrl.trim() !== window.location.href.trim()) {\r\n\t\t\t\t\t\t\t\t// Multi-page: Navigate to the first step's URL\r\n\t\t\t\t\t\t\t\twindow.location.href = steps[0].targetUrl;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// Single-page: Gentle scroll to first step element or top\r\n\t\t\t\t\t\t\t\tif (steps[0]?.xpath) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"🎯 Gentle scroll to first step element on restart\");\r\n\r\n\t\t\t\t\t\t\t\t\t// Check if first step element exists and use gentle scroll\r\n\t\t\t\t\t\t\t\t\tconst firstElement = getElementByXPath(steps[0].xpath, steps[0]?.PossibleElementPath || \"\");\r\n\t\t\t\t\t\t\t\t\tif (firstElement) {\r\n\t\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\t\tfirstElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Gentle restart scroll completed\");\r\n\t\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(\"❌ Gentle restart scroll failed, scrolling to top\");\r\n\t\t\t\t\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"❌ First step element not found, scrolling to top\");\r\n\t\t\t\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t// No xpath available, just scroll to top\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"ℹ️ No xpath for first step, scrolling to top\");\r\n\t\t\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n            } else if (button.ButtonAction.Action === \"open-url\" && button.ButtonAction.ActionValue === \"new-tab\") {\r\n\t\t\t\t\t\t\twindow.open(button.ButtonAction.TargetUrl, \"_blank\");\r\n            } else if (button.ButtonAction.Action === \"open-url\" && button.ButtonAction.ActionValue === \"same-tab\") {\r\n\t\t\t\t\t\t\twindow.location.href = button.ButtonAction.TargetUrl;\r\n            } else {\r\n\t\t\t\t\t\t\t//onClose();\r\n            }\r\n\t\t\t\t\t};\r\n          return (\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tsx={buttonStyle}\r\n\t\t\t\t\t\t\tonClick={handleClick}\r\n\t\t\t\t\t\t>\r\n              {button.ButtonName}\r\n            </Button>\r\n\t\t\t\t\t);\r\n        })\r\n\t\t\t: null;\r\n\t};\r\n\r\n  const TooltipContent = (\r\n    <>\r\n      <div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n        {enabelCross && (\r\n          <IconButton \r\n            sx={{\r\n              position: \"absolute\",\r\n              boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n              background: \"#fff !important\",\r\n              border: \"1px solid #ccc\",\r\n              zIndex: \"999\",\r\n              borderRadius: \"50px\",\r\n              padding: \"1px !important\",\r\n              float: \"right\",\r\n              top: \"-12px\",\r\n              right: \"-12px\",\r\n\t\t\t\t\t\t\tmargin: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `-${parseInt(canvasStyle?.BorderSize) - 3}px` : \"0px\",\r\n\t\t\t\t\t  }}\r\n\t\t\t\t\t  \r\n          >\r\n            <CloseIcon sx={{ zoom: \"1\", color: \"#000\" }} />\r\n          </IconButton>\r\n        )}\r\n      </div>\r\n\t  <PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"270px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t<div ref={contentRef}\r\n\t\t\tstyle={{\r\n\t\t\t\t// maxHeight: \"270px\",\r\n            overflow: \"hidden\",\r\n            borderRadius: \"4px\",\r\n            padding: getPadding(),\r\n            position: \"relative\",\r\n            zIndex: \"999\",\r\n\t\t\t\t// border: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== \"0px\" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || \"transparent\"}` : \"none\",\r\n\r\n\t\t\t}}>\r\n          <Box>\r\n\t\t\t\t\t{(!hasOnlyButtons()) && (\r\n              <Box\r\n                display=\"flex\"\r\n                flexDirection=\"column\"\r\n                alignItems={hasOnlyText() ? \"flex-start\" : \"center\"}\r\n                sx={{\r\n                  width: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n                }}\r\n              >\r\n                {renderContent()}\r\n              </Box>\r\n            )}\r\n            {currentStepData?.buttonData?.length > 0 && (\r\n              <Box\r\n                ref={buttonContainerRef}\r\n                display=\"flex\"\r\n                sx={{\r\n                  placeContent: \"center\",\r\n\t\t\t\t\t\t\t\t// padding: hasOnlyButtons() ? \"4px\" : \"10px\",\r\n                  gap: \"4px\",\r\n                  backgroundColor: currentStepData.buttonData[0]?.BackgroundColor,\r\n                  width: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n                  alignItems: \"center\",\r\n                }}\r\n              >\r\n                {renderButtons()}\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        </div>\r\n      </PerfectScrollbar>\r\n      {enableProgress && steps.length>1 && selectedTemplate !== \"Hotspot\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n    </>\r\n\t);\r\n\r\n\t//const overlay = currentStep?.overlay || \"\";\r\n  const overlayStyle = {\r\n    position: \"fixed\" as const,\r\n    top: 0,\r\n    left: 0,\r\n    width: \"100vw\",\r\n    height: \"100vh\",\r\n    backgroundColor: \"transparent\",\r\n    pointerEvents: \"none\",\r\n    zIndex: 9999,\r\n\t};\r\n\r\n  const getOverlaySections = () => {\r\n\t\tif (!targetElement) return null;\r\n\r\n\t\tconst rect = targetElement.getBoundingClientRect();\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\r\n    const sections = {\r\n      top: {\r\n        position: \"fixed\" as const,\r\n        top: 0,\r\n        left: 0,\r\n        width: \"100%\",\r\n        height: `${rect.top}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n      bottom: {\r\n        position: \"fixed\" as const,\r\n        top: `${rect.bottom}px`,\r\n        left: 0,\r\n        width: \"100%\",\r\n        height: `${viewportHeight - rect.bottom}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n      left: {\r\n        position: \"fixed\" as const,\r\n        top: `${rect.top}px`,\r\n        left: 0,\r\n        width: `${rect.left}px`,\r\n        height: `${rect.height}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n      right: {\r\n        position: \"fixed\" as const,\r\n        top: `${rect.top}px`,\r\n        left: `${rect.right}px`,\r\n        width: `${viewportWidth - rect.right}px`,\r\n        height: `${rect.height}px`,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n        pointerEvents: \"auto\",\r\n      },\r\n\t\t};\r\n\r\n\t\treturn sections;\r\n\t};\r\n  const highlightBoxStyle = targetElement\r\n    ? {\r\n        position: \"fixed\" as const,\r\n        top: `${targetElement.getBoundingClientRect().top}px`,\r\n        left: `${targetElement.getBoundingClientRect().left}px`,\r\n        width: `${targetElement.getBoundingClientRect().width}px`,\r\n        height: `${targetElement.getBoundingClientRect().height}px`,\r\n\t\t\t\t// border: '2px solid #fff',\r\n        borderRadius: \"4px\",\r\n        pointerEvents: interactWithPage ? \"auto\" : \"none\",\r\n        zIndex: 0,\r\n      }\r\n    : {}\r\n\r\n  return (\r\n    <>\r\n      {currentStepData?.overlay && targetElement && isElementVisible && selectedTemplate !== \"Tour\" && (\r\n        <Box sx={overlayStyle}>\r\n          {Object.entries(getOverlaySections() || {}).map(([key, style]) => (\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tkey={key}\r\n\t\t\t\t\t\t\tsx={style}\r\n\t\t\t\t\t\t/>\r\n          ))}\r\n\r\n          <Box sx={highlightBoxStyle} />\r\n        </Box>\r\n      )}\r\n      {targetElement && isElementVisible && (\r\n\r\n        <CustomWidthTooltip\r\n          open\r\n          title={TooltipContent}\r\n          placement={tooltipPlacement}\r\n          arrow\r\n          PopperProps={{\r\n            anchorEl: targetElement,\r\n            modifiers: [\r\n              {\r\n                name: \"preventOverflow\",\r\n                options: {\r\n                  boundary: window,\r\n                  altAxis: true,\r\n                  padding: 10,\r\n                },\r\n              },\r\n              {\r\n                name: \"computeStyles\",\r\n                options: {\r\n                  // Disable adaptive positioning to prevent micro-adjustments\r\n                  adaptive: false,\r\n                  // Round positions to prevent sub-pixel rendering\r\n                  roundOffsets: ({ x, y }: { x: number; y: number }) => ({\r\n                    x: Math.round(x),\r\n                    y: Math.round(y),\r\n                  }),\r\n                },\r\n              },\r\n            ],\r\n          }}\r\n          canvasStyle={canvasStyle}\r\n          hasOnlyButtons={hasOnlyButtons()}\r\n          hasOnlyText={hasOnlyText()}\r\n\t\t\t\t\t//dynamicWidth={dynamicWidth}\r\n        >\r\n          <Box\r\n            sx={{\r\n              position: \"absolute\",\r\n              top: targetElement.offsetTop,\r\n              left: targetElement.offsetLeft,\r\n              width: targetElement.offsetWidth,\r\n              height: targetElement.offsetHeight,\r\n            }}\r\n          />\r\n        </CustomWidthTooltip>\r\n      )}\r\n    </>\r\n\t);\r\n};\r\n\r\nexport default TooltipGuide;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,cAAc,EACdC,UAAU,EACVC,cAAc,EAEdC,aAAa,EAEbC,UAAU,QACL,eAAe;AAEtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,cAAc,MAAuB,+BAA+B;AAC3E,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAwFrD,MAAMC,kBAAkB,GAAGP,MAAM,CAAC,CAAC;EAAEQ,SAAS;EAAEC,WAAW;EAAEC,cAAc;EAAEC,WAAW;EAAEC,YAAY;EAAE,GAAGC;AAK3G,CAAC,kBACAT,OAAA,CAACZ,OAAO;EAAA,GACHqB,KAAK;EACTC,OAAO,EAAE;IAAEC,MAAM,EAAEP;EAAU,CAAE;EAC/BQ,EAAE,EAAC;AAAgB;EAAAC,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACnB,CACD,CAAC,CAAC,CAAC;EAAEX,WAAW;EAAEC,cAAc;EAAEC,WAAW;EAAEC;AAK9C,CAAC,MAAM;EACL,CAAC,MAAMhB,cAAc,CAACyB,OAAO,EAAE,GAAG;IAChCC,eAAe,EAAE,CAAAb,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,eAAe,KAAI,SAAS;IAC9D;IACA;IACIC,OAAO,EAAEd,cAAc,GAAG,KAAK,GAAGD,WAAW,CAACgB,OAAO;IACzD;IACIC,YAAY,EAAE,CAAAjB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkB,MAAM,MAAIlB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,YAAY,KAAE,MAAM;IACtEC,SAAS,EAAE,iCAAiC;IAChDC,MAAM,EAAErB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEsB,UAAU,IAAI,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,UAAU,MAAK,KAAK,GAAG,GAAGtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,UAAU,UAAU,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuB,WAAW,KAAI,aAAa,EAAE,GAAG,MAAM;IAC/J;IACA;IACAC,KAAK,EAAG,iBAAiB;IACrBC,QAAQ,EAAEzB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE0B,KAAK,GAAG,GAAG1B,WAAW,CAAC0B,KAAK,aAAa,GAAG,OAAO;IAC1EC,IAAI,EAAE,GAAG,CAAA3B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4B,WAAW,KAAI,MAAM,aAAa;IACxDC,MAAM,EAAE,GAAG,CAAA7B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8B,WAAW,KAAI,MAAM;EAC/C,CAAC;EACD,CAAC,MAAM3C,cAAc,CAAC4C,KAAK,EAAE,GAAG;IAC9BC,KAAK,EAAE,CAAAhC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,eAAe,KAAI,SAAS;IAChDmB,QAAQ,EAAE,MAAM;IAChB,UAAU,EAAE;MACfC,YAAY,EAAElC,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEsB,UAAU,IAAI,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,UAAU,MAAK,KAAK,GAAG,GAAGtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,UAAU,EAAE,GAAG,KAAK;MAAE;MACnHa,YAAY,EAAE,CAAAnC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuB,WAAW,KAAI,aAAa;MAAE;MACzDa,YAAY,EAAE,OAAO,CAAE;IACpB;EACF,CAAC;EACD,CAAC,qBAAqB,GAAG;IAC3BC,MAAM,EAAE,CAAArC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsC,MAAM,KAAI,KAAK,CAAE;EACpC;AACF,CAAC,CACH,CAAC;AAAAC,EAAA,GA9CKzC,kBAAkB;AAgDxB,MAAM0C,YAAyC,GAAGA,CAAC;EACjDC,KAAK;EACLC,UAAU;EACVC,OAAO;EACPC,aAAa;EACbC,cAAc;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACJ,IAAIC,IAAS;EACb,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9E,QAAQ,CAACkE,cAAc,IAAI,CAAC,CAAC;EAC7E,MAAMa,UAAU,GAAG9E,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM+E,kBAAkB,GAAG/E,MAAM,CAAiB,IAAI,CAAC;;EAEvD;EACA,MAAMgF,oBAAoB,GAAGhF,MAAM,CAAgBiF,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;;EAErE;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMsF,YAAY,GAAGrF,MAAM,CAAM,IAAI,CAAC;EAEtCF,SAAS,CAAC,MAAM;IACd,IAAImE,cAAc,KAAKqB,SAAS,EAAE;MACnCT,mBAAmB,CAACZ,cAAc,CAAC;IAClC;EACH,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpB,MAAM;IAAEsB,cAAc;IAAEC,gBAAgB;IAAEC,WAAW;IAAEC,aAAa;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGhF,cAAc,CAAEiF,KAAkB,IAAKA,KAAK,CAAC;EAErJ,MAAMC,eAAe,GAAGjC,KAAK,CAACe,gBAAgB,GAAG,CAAC,CAAC;EACnD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjG,QAAQ,CAAqB,IAAI,CAAC;EAC5E,MAAM,CAACkG,eAAe,EAAEC,kBAAkB,CAAC,GAAGnG,QAAQ,CAAC;IAAEoG,GAAG,EAAE,CAAC;IAAEpD,IAAI,EAAE;EAAE,CAAC,CAAC;EAC3E,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtG,QAAQ,CAAsC,KAAK,CAAC;EACpG,MAAM,CAACuG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EAC9D,MAAMyG,WAAW,GAAGxG,MAAM,CAA0B,IAAI,CAAC;;EAEzD;EACA,MAAMyG,eAAe,GAAGzG,MAAM,CAAC;IAAEmG,GAAG,EAAE,CAAC;IAAEpD,IAAI,EAAE;EAAE,CAAC,CAAC;EACpD,MAAM2D,gBAAgB,GAAG1G,MAAM,CAAsC,KAAK,CAAC;EAG1E,MAAM2G,qBAAqB,GAAIC,OAAoB,IAA0C;IAC7F,MAAMjC,IAAI,GAAGiC,OAAO,CAACC,qBAAqB,CAAC,CAAC;IAC5C,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;IACvC,MAAMC,cAAc,GAAGF,MAAM,CAACG,WAAW;IAEzC,MAAMC,QAAQ,GAAGxC,IAAI,CAACwB,GAAG;IACzB,MAAMiB,WAAW,GAAGH,cAAc,GAAGtC,IAAI,CAAC1B,MAAM;IAChD,MAAMoE,SAAS,GAAG1C,IAAI,CAAC5B,IAAI;IAC3B,MAAMuE,UAAU,GAAGR,aAAa,GAAGnC,IAAI,CAAC4C,KAAK;IAE7C,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACP,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,CAAC;IAEvE,IAAIE,QAAQ,KAAKL,QAAQ,EAAE,OAAO,KAAK;IACvC,IAAIK,QAAQ,KAAKJ,WAAW,EAAE,OAAO,QAAQ;IAC7C,IAAII,QAAQ,KAAKH,SAAS,EAAE,OAAO,MAAM;IACzC,OAAO,OAAO;EACf,CAAC;EACD;;EAEC,MAAMM,uBAAuB,GAAIf,OAAoB,IAAK;IAC1D,MAAMjC,IAAI,GAAGiC,OAAO,CAACC,qBAAqB,CAAC,CAAC;IAC1C,OACElC,IAAI,CAAC/B,KAAK,GAAG,CAAC,IACd+B,IAAI,CAACiD,MAAM,GAAG,CAAC,IACfjD,IAAI,CAACwB,GAAG,KAAK,CAAC,IACdxB,IAAI,CAAC5B,IAAI,KAAK,CAAC,IACf,CAAC8E,MAAM,CAACC,KAAK,CAACnD,IAAI,CAACwB,GAAG,CAAC,IACvB,CAAC0B,MAAM,CAACC,KAAK,CAACnD,IAAI,CAAC5B,IAAI,CAAC;EAE7B,CAAC;EACA,MAAMgF,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,mBAA2B,KAAyB;IAC9F;IACE,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACpCC,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAEH,mBAAmB,CAAC;MACzF,IAAIA,mBAAmB,IAAIA,mBAAmB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5D,IAAI;UACP,MAAMG,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACN,mBAAmB,EAAEK,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;UAChH,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;UAC9B,IAAID,IAAI,YAAYE,WAAW,EAAE;YACrC,OAAOF,IAAI;UACP,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;YACpC,OAAOH,IAAI,CAACG,aAAa;UACrB;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACnBX,OAAO,CAACW,KAAK,CAAC,uCAAuC,EAAEb,mBAAmB,EAAEa,KAAK,CAAC;QAC/E;MACF;MACH,OAAO,IAAI;IACV;IAEA,IAAI;MACL,MAAMC,KAAK,GAAG,GAAGf,KAAK,gFAAgF;MACtGG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEW,KAAK,CAAC;MAC7C,MAAMV,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACQ,KAAK,EAAET,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;MAClG,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;MAChC,IAAID,IAAI,YAAYE,WAAW,EAAE;QACnC,OAAOF,IAAI;MACT,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;QAClC,OAAOH,IAAI,CAACG,aAAa;MACvB,CAAC,MAAM,IAAIH,IAAI,KAAK,IAAI,EAAE;QAC5B;QACI,IAAIT,mBAAmB,IAAIA,mBAAmB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UACjEC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEH,mBAAmB,CAAC;UACrF,MAAMe,cAAc,GAAGV,QAAQ,CAACC,QAAQ,CAACN,mBAAmB,EAAEK,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;UACxH,MAAMQ,YAAY,GAAGD,cAAc,CAACL,eAAe;UAC9C,IAAIM,YAAY,YAAYL,WAAW,EAAE;YAC7C,OAAOK,YAAY;UACf,CAAC,MAAM,IAAIA,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEJ,aAAa,EAAE;YAC5C,OAAOI,YAAY,CAACJ,aAAa;UAC7B;QACF;QACJ,OAAO,IAAI;MACT,CAAC,MAAM;QACT,OAAO,IAAI;MACT;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACjBX,OAAO,CAACW,KAAK,CAAC,yBAAyB,EAAEd,KAAK,EAAEc,KAAK,CAAC;MACtD;MACG,IAAIb,mBAAmB,IAAIA,mBAAmB,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5D,IAAI;UACPC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEH,mBAAmB,CAAC;UACxF,MAAMI,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACN,mBAAmB,EAAEK,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;UAChH,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;UAC9B,IAAID,IAAI,YAAYE,WAAW,EAAE;YACrC,OAAOF,IAAI;UACP,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;YACpC,OAAOH,IAAI,CAACG,aAAa;UACrB;QACF,CAAC,CAAC,OAAOK,aAAa,EAAE;UAC3Bf,OAAO,CAACW,KAAK,CAAC,uCAAuC,EAAEb,mBAAmB,EAAEiB,aAAa,CAAC;QACvF;MACF;MACH,OAAO,IAAI;IACV;EACH,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAACvC,OAAoB,EAAEwC,SAAiB,EAAEC,QAAgB,GAAG,GAAG,KAAK;IAC3F;IACA,MAAMC,SAAS,GAAG1C,OAAO,CAAC2C,YAAY,GAAG3C,OAAO,CAAC4C,YAAY;IAC7D,MAAMC,gBAAgB,GAAGhC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACiC,GAAG,CAACN,SAAS,EAAEE,SAAS,CAAC,CAAC;;IAEpE;IACA,IAAI;MACH,IAAI,UAAU,IAAI1C,OAAO,IAAI,OAAOA,OAAO,CAAC+C,QAAQ,KAAK,UAAU,EAAE;QACpE/C,OAAO,CAAC+C,QAAQ,CAAC;UAChBxD,GAAG,EAAEsD,gBAAgB;UACrBG,QAAQ,EAAE;QACX,CAAC,CAAC;QACF;MACD;IACD,CAAC,CAAC,OAAOd,KAAK,EAAE;MACfX,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACxE;;IAEA;IACA,IAAI;MACH,MAAMyB,QAAQ,GAAGjD,OAAO,CAACkD,SAAS;MAClC,MAAMC,QAAQ,GAAGN,gBAAgB,GAAGI,QAAQ;MAC5C,MAAMG,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAEnC,MAAMC,aAAa,GAAIC,WAAmB,IAAK;QAC9C,MAAMC,OAAO,GAAGD,WAAW,GAAGJ,SAAS;QACvC,MAAMM,QAAQ,GAAG7C,IAAI,CAACiC,GAAG,CAACW,OAAO,GAAGhB,QAAQ,EAAE,CAAC,CAAC;;QAEhD;QACA,MAAMkB,cAAc,GAAIC,CAAS,IAAKA,CAAC,GAAG,GAAG,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QACvG,MAAMC,aAAa,GAAGF,cAAc,CAACD,QAAQ,CAAC;QAE9C1D,OAAO,CAACkD,SAAS,GAAGD,QAAQ,GAAIE,QAAQ,GAAGU,aAAc;QAEzD,IAAIH,QAAQ,GAAG,CAAC,EAAE;UACjBI,qBAAqB,CAACP,aAAa,CAAC;QACrC;MACD,CAAC;MAEDO,qBAAqB,CAACP,aAAa,CAAC;IACrC,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACfX,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE;MACAxB,OAAO,CAACkD,SAAS,GAAGL,gBAAgB;IACrC;EACD,CAAC;;EAED;EACA,MAAMkB,cAAc,GAAG1K,WAAW,CAAC,CAClC+H,KAAa,EACb4C,mBAA2B,EAC3BC,cAA8C,EAC9CC,WAAmB,GAAG,EAAE,EACxBC,iBAAyB,GAAG,EAAE;EAAE;EAChCC,SAAiB,GAAG,SAAS,EAC7BC,iBAA8B,KAC1B;IACJ,IAAIC,mBAA0C,GAAG,IAAI;IACrD,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,eAAe,GAAGL,iBAAiB;IACvC,IAAIM,WAAW,GAAG,KAAK;IAEvB,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACrB,IAAIJ,mBAAmB,EAAE;QACxBK,YAAY,CAACL,mBAAmB,CAAC;QACjCA,mBAAmB,GAAG,IAAI;MAC3B;MACAG,WAAW,GAAG,IAAI;IACnB,CAAC;IAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;MAC7B,IAAIH,WAAW,EAAE;MAEjBF,QAAQ,EAAE;MACVhD,OAAO,CAACC,GAAG,CAAC,GAAG4C,SAAS,qBAAqBG,QAAQ,IAAIL,WAAW,eAAeM,eAAe,KAAK,CAAC;MAExG,MAAMxE,OAAO,GAAGmB,iBAAiB,CAACC,KAAK,EAAE4C,mBAAmB,CAAC;MAE7D,IAAIhE,OAAO,IAAIe,uBAAuB,CAACf,OAAO,CAAC,EAAE;QAChDuB,OAAO,CAACC,GAAG,CAAC,GAAG4C,SAAS,+BAA+BG,QAAQ,WAAW,CAAC;QAC3EG,OAAO,CAAC,CAAC;QACTT,cAAc,CAACjE,OAAO,CAAC;QACvB;MACD;MAEA,IAAIuE,QAAQ,IAAIL,WAAW,EAAE;QAC5B3C,OAAO,CAACC,GAAG,CAAC,GAAG4C,SAAS,mBAAmBF,WAAW,8BAA8B,CAAC;QACrFQ,OAAO,CAAC,CAAC;QACT,IAAIL,iBAAiB,EAAE;UACtBA,iBAAiB,CAAC,CAAC;QACpB;QACA;MACD;;MAEA;MACA,MAAMQ,MAAM,GAAGhE,IAAI,CAACiE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;MACpCN,eAAe,GAAG3D,IAAI,CAACiC,GAAG,CAAC0B,eAAe,IAAI,GAAG,GAAGK,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEpEP,mBAAmB,GAAGS,UAAU,CAACH,eAAe,EAAEJ,eAAe,CAAC;IACnE,CAAC;;IAED;IACAI,eAAe,CAAC,CAAC;;IAEjB;IACA,OAAOF,OAAO;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,iBAAiB,GAAGA,CAAChF,OAA6B,EAAEiF,OAAsE,KAAK;IACpI,MAAMC,QAAQ,GAAGlF,OAAO,KAAKG,MAAM;IACnC,MAAMhB,aAAa,GAAG+F,QAAQ,GAAGxD,QAAQ,CAACyD,eAAe,GAAGnF,OAAsB;;IAElF;IACA,IAAI,CAACkF,QAAQ,IAAI,UAAU,IAAIlF,OAAO,IAAI,OAAQA,OAAO,CAAS+C,QAAQ,KAAK,UAAU,EAAE;MAC1F,IAAI;QACF/C,OAAO,CAAS+C,QAAQ,CAACkC,OAAO,CAAC;QAClC,OAAO,IAAI;MACZ,CAAC,CAAC,OAAO/C,KAAK,EAAE;QACfX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEU,KAAK,CAAC;MACzD;IACD;;IAEA;IACA,IAAIgD,QAAQ,IAAID,OAAO,CAACjC,QAAQ,KAAK,QAAQ,EAAE;MAC9C,IAAI;QACH7C,MAAM,CAAC4C,QAAQ,CAACkC,OAAO,CAAC;QACxB,OAAO,IAAI;MACZ,CAAC,CAAC,OAAO/C,KAAK,EAAE;QACfX,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEU,KAAK,CAAC;MAC9C;IACD;;IAEA;IACA,IAAI+C,OAAO,CAACjC,QAAQ,KAAK,QAAQ,IAAIiC,OAAO,CAAC1F,GAAG,KAAKb,SAAS,EAAE;MAC/D,IAAI;QACH6D,cAAc,CAACpD,aAAa,EAAE8F,OAAO,CAAC1F,GAAG,CAAC;QAC1C,OAAO,IAAI;MACZ,CAAC,CAAC,OAAO2C,KAAK,EAAE;QACfX,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEU,KAAK,CAAC;MACtD;IACD;;IAEA;IACA,IAAI;MACH,IAAI+C,OAAO,CAAC1F,GAAG,KAAKb,SAAS,EAAE;QAC9BS,aAAa,CAAC+D,SAAS,GAAG+B,OAAO,CAAC1F,GAAG;MACtC;MACA,IAAI0F,OAAO,CAAC9I,IAAI,KAAKuC,SAAS,EAAE;QAC/BS,aAAa,CAACiG,UAAU,GAAGH,OAAO,CAAC9I,IAAI;MACxC;MACA,OAAO,IAAI;IACZ,CAAC,CAAC,OAAO+F,KAAK,EAAE;MACfX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEU,KAAK,CAAC;MACxD,OAAO,KAAK;IACb;EACD,CAAC;EAED,MAAMmD,qBAAqB,GAAGhM,WAAW,CAAC,OAAO8F,aAA0B,EAAEmG,SAA8C,EAAEC,QAAe,KAAK;IAChJ,IAAI,CAACpG,aAAa,EAAE;MACnBoC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE;IACD;IAEAD,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;MAClExB,OAAO,EAAEb,aAAa;MACtBqG,OAAO,EAAErG,aAAa,CAACqG,OAAO;MAC9BjL,SAAS,EAAE4E,aAAa,CAAC5E,SAAS;MAClCQ,EAAE,EAAEoE,aAAa,CAACpE,EAAE;MACpBuK,SAAS,EAAEA;IACZ,CAAC,CAAC;IAEF,IAAI;MACH;MACAlH,oBAAoB,CAACqH,OAAO,GAAGrH,oBAAoB,CAACqH,OAAO,CAACC,IAAI,CAAC,YAAY;QAC5E,MAAM3H,IAAI,GAAGoB,aAAa,CAACc,qBAAqB,CAAC,CAAC;QAClD,MAAMI,cAAc,GAAGF,MAAM,CAACG,WAAW;QACzC,MAAMJ,aAAa,GAAGC,MAAM,CAACC,UAAU;;QAEvC;QACA,IAAIuF,eAAe,GAAGxF,MAAM,CAACyF,OAAO;QACpC,IAAIC,gBAAgB,GAAG1F,MAAM,CAAC2F,OAAO;;QAErC;QACA,QAAQR,SAAS;UAChB,KAAK,KAAK;YACT;YACAK,eAAe,GAAGxF,MAAM,CAACyF,OAAO,GAAG7H,IAAI,CAACwB,GAAG,GAAIc,cAAc,GAAG,GAAI;YACpE;UACD,KAAK,QAAQ;YACZ;YACAsF,eAAe,GAAGxF,MAAM,CAACyF,OAAO,GAAG7H,IAAI,CAACwB,GAAG,GAAIc,cAAc,GAAG,GAAI;YACpE;UACD,KAAK,MAAM;YACV;YACAsF,eAAe,GAAGxF,MAAM,CAACyF,OAAO,GAAG7H,IAAI,CAACwB,GAAG,GAAIc,cAAc,GAAG,GAAI;YACpEwF,gBAAgB,GAAG1F,MAAM,CAAC2F,OAAO,GAAG/H,IAAI,CAAC5B,IAAI,GAAI+D,aAAa,GAAG,GAAI;YACrE;UACD,KAAK,OAAO;YACX;YACAyF,eAAe,GAAGxF,MAAM,CAACyF,OAAO,GAAG7H,IAAI,CAACwB,GAAG,GAAIc,cAAc,GAAG,GAAI;YACpEwF,gBAAgB,GAAG1F,MAAM,CAAC2F,OAAO,GAAG/H,IAAI,CAAC5B,IAAI,GAAI+D,aAAa,GAAG,GAAI;YACrE;UACD;YACC;YACAyF,eAAe,GAAGxF,MAAM,CAACyF,OAAO,GAAG7H,IAAI,CAACwB,GAAG,GAAIc,cAAc,GAAG,GAAI;QACtE;;QAEA;QACA,MAAM0F,YAAY,GAAGlF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEY,QAAQ,CAACyD,eAAe,CAACxC,YAAY,GAAGtC,cAAc,CAAC;QACxF,MAAM2F,aAAa,GAAGnF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEY,QAAQ,CAACyD,eAAe,CAACc,WAAW,GAAG/F,aAAa,CAAC;QAEvFyF,eAAe,GAAG9E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACiC,GAAG,CAAC6C,eAAe,EAAEI,YAAY,CAAC,CAAC;QACtEF,gBAAgB,GAAGhF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACiC,GAAG,CAAC+C,gBAAgB,EAAEG,aAAa,CAAC,CAAC;QAEzEzE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC7CmE,eAAe;UACfE,gBAAgB;UAChBK,cAAc,EAAE/F,MAAM,CAACyF,OAAO;UAC9BO,cAAc,EAAEhG,MAAM,CAAC2F,OAAO;UAC9BM,WAAW,EAAErI;QACd,CAAC,CAAC;;QAEF;QACA,IAAIsI,aAAa,GAAG,KAAK;;QAEzB;QACA,IAAI;UACHA,aAAa,GAAGrB,iBAAiB,CAAC7E,MAAM,EAAE;YACzCZ,GAAG,EAAEoG,eAAe;YACpBxJ,IAAI,EAAE0J,gBAAgB;YACtB7C,QAAQ,EAAE;UACX,CAAC,CAAC;UACFzB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE6E,aAAa,CAAC;QAC1D,CAAC,CAAC,OAAOnE,KAAK,EAAE;UACfX,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,KAAK,CAAC;QACjD;;QAEA;QACA,IAAI,CAACmE,aAAa,EAAE;UACnB,IAAI;YACHlG,MAAM,CAAC4C,QAAQ,CAAC8C,gBAAgB,EAAEF,eAAe,CAAC;YAClDU,aAAa,GAAG,IAAI;YACpB9E,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC5C,CAAC,CAAC,OAAOU,KAAK,EAAE;YACfX,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEU,KAAK,CAAC;UAChD;QACD;;QAEA;QACA,IAAI,CAACmE,aAAa,EAAE;UACnB,IAAI;YACH3E,QAAQ,CAACyD,eAAe,CAACjC,SAAS,GAAGyC,eAAe;YACpDjE,QAAQ,CAACyD,eAAe,CAACC,UAAU,GAAGS,gBAAgB;YACtDnE,QAAQ,CAAC4E,IAAI,CAACpD,SAAS,GAAGyC,eAAe;YACzCjE,QAAQ,CAAC4E,IAAI,CAAClB,UAAU,GAAGS,gBAAgB;YAC3CtE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACtD,CAAC,CAAC,OAAOU,KAAK,EAAE;YACfX,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEU,KAAK,CAAC;UAC3D;QACD;;QAEA;QACA,MAAM,IAAI7D,OAAO,CAACC,OAAO,IAAIyG,UAAU,CAACzG,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtDiD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MAClD,CAAC,CAAC;MAEF,MAAMpD,oBAAoB,CAACqH,OAAO;IACnC,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACfX,OAAO,CAACW,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACvD;EACD,CAAC,EAAE,CAACK,cAAc,EAAEyC,iBAAiB,CAAC,CAAC;EAEvC,MAAMtB,QAAQ,GAAK1F,gBAAgB,GAAKf,KAAK,CAACsJ,MAAM,GAAI,GAAG;EAC3D,MAAMC,gBAAgB,GAAGpJ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqJ,gBAAgB;EAEvDvN,SAAS,CAAC,MAAM;IACd,IAAI2F,WAAW,IAAIb,gBAAgB,IAAI,CAAC,IAAIwI,gBAAgB,KAAK,KAAK,EAAE;MACtEjF,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE5C,gBAAgB,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;MACxH8C,QAAQ,CAAC4E,IAAI,CAACI,KAAK,CAACC,aAAa,GAAG/H,gBAAgB,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;MACjF,MAAM8H,KAAK,GAAGhF,QAAQ,CAACkF,aAAa,CAAC,OAAO,CAAC;MAC7CF,KAAK,CAACG,SAAS,GAAG,qDAAqD;MACvEnF,QAAQ,CAACoF,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;MAChC,OAAO,MAAM;QACXnF,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;QAChFE,QAAQ,CAAC4E,IAAI,CAACI,KAAK,CAACC,aAAa,GAAG,MAAM;QAC1CjF,QAAQ,CAACoF,IAAI,CAACE,WAAW,CAACN,KAAK,CAAC;MAClC,CAAC;IACH;IACA,OAAO,MAAM;MACX;MACAnF,OAAO,CAACC,GAAG,CAAC,4EAA4E,CAAC;MACzFE,QAAQ,CAAC4E,IAAI,CAACI,KAAK,CAACC,aAAa,GAAG,MAAM,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,CAAC3I,gBAAgB,EAAEwI,gBAAgB,CAAC,CAAC;;EAEzC;EACCtN,SAAS,CAAC,MAAM;IACd,IAAI2F,WAAW,IAAIb,gBAAgB,IAAI,CAAC,IAAIkB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE+H,OAAO,IAAIT,gBAAgB,KAAK,KAAK,EAAE;MACrG9E,QAAQ,CAAC4E,IAAI,CAACI,KAAK,CAACQ,QAAQ,GAAG,QAAQ;IACtC,CAAC,MAAM;MACRxF,QAAQ,CAAC4E,IAAI,CAACI,KAAK,CAACQ,QAAQ,GAAG,EAAE;IAChC;;IAEF;IACE,OAAO,MAAM;MACdxF,QAAQ,CAAC4E,IAAI,CAACI,KAAK,CAACQ,QAAQ,GAAG,EAAE;IAClC,CAAC;EACD,CAAC,EAAE,CAAChI,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+H,OAAO,EAAET,gBAAgB,CAAC,CAAC;EACjD;EACAtN,SAAS,CAAC,MAAM;IACf,MAAMiO,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAIjJ,UAAU,CAACuH,OAAO,EAAE;QACvB;QACAvH,UAAU,CAACuH,OAAO,CAACiB,KAAK,CAAC1F,MAAM,GAAG,MAAM;QACxC,MAAMoG,aAAa,GAAGlJ,UAAU,CAACuH,OAAO,CAAC9C,YAAY;QACrD,MAAM0E,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGF,aAAa,GAAGC,eAAe;QAGpD7I,iBAAiB,CAAC8I,YAAY,CAAC;;QAE/B;QACA,IAAI7I,YAAY,CAACgH,OAAO,EAAE;UACzB;UACA,IAAIhH,YAAY,CAACgH,OAAO,CAAC8B,YAAY,EAAE;YACtC9I,YAAY,CAACgH,OAAO,CAAC8B,YAAY,CAAC,CAAC;UACpC;UACA;UACAxC,UAAU,CAAC,MAAM;YAChB,IAAItG,YAAY,CAACgH,OAAO,IAAIhH,YAAY,CAACgH,OAAO,CAAC8B,YAAY,EAAE;cAC9D9I,YAAY,CAACgH,OAAO,CAAC8B,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDJ,iBAAiB,CAAC,CAAC;IAGnB,MAAMK,QAAQ,GAAG,CAChBzC,UAAU,CAACoC,iBAAiB,EAAE,EAAE,CAAC,EACjCpC,UAAU,CAACoC,iBAAiB,EAAE,GAAG,CAAC,EAClCpC,UAAU,CAACoC,iBAAiB,EAAE,GAAG,CAAC,EAClCpC,UAAU,CAACoC,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIM,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAIxJ,UAAU,CAACuH,OAAO,IAAItF,MAAM,CAACwH,cAAc,EAAE;MAChDF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;QACzC5C,UAAU,CAACoC,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFM,cAAc,CAACG,OAAO,CAAC1J,UAAU,CAACuH,OAAO,CAAC;IAC3C;IAGA,IAAIvH,UAAU,CAACuH,OAAO,IAAItF,MAAM,CAAC0H,gBAAgB,EAAE;MAClDH,gBAAgB,GAAG,IAAIG,gBAAgB,CAAC,MAAM;QAC7C9C,UAAU,CAACoC,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFO,gBAAgB,CAACE,OAAO,CAAC1J,UAAU,CAACuH,OAAO,EAAE;QAC5CqC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZT,QAAQ,CAACU,OAAO,CAACvD,YAAY,CAAC;MAC9B,IAAI8C,cAAc,EAAE;QACnBA,cAAc,CAACU,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIT,gBAAgB,EAAE;QACrBA,gBAAgB,CAACS,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAACjJ,eAAe,EAAEL,WAAW,CAAC,CAAC;EAEjC,MAAMuJ,uBAAuB,GAAGA,CAAA,KAAM;IACtC;IACA;IACA;IACA;IACA;;IAEA;IACE7G,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;MAChEJ,KAAK,EAAElC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK;MAC7BC,mBAAmB,EAAEnC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,mBAAmB;MACzDgH,SAAS,EAAErK,gBAAgB;MAC9Be,YAAY,EAAEA;IACf,CAAC,CAAC;IAEF,MAAMiB,OAAO,GAAGmB,iBAAiB,CAACjC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK,EAAClC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,mBAAmB,CAAC;IAC5F,IAAI,CAACrB,OAAO,EAAE;MACfuB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEtC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK,EAAE,sBAAsB,EAAElC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,mBAAmB,CAAC;MACzIjC,gBAAgB,CAAC,IAAI,CAAC;MACtBO,mBAAmB,CAAC,KAAK,CAAC;MAC1B;IACC;IAEF,MAAM2I,OAAO,GAAGvH,uBAAuB,CAACf,OAAO,CAAC;IAChD;;IAEE,IAAI,CAACsI,OAAO,EAAE;MACflJ,gBAAgB,CAAC,IAAI,CAAC;MACtBO,mBAAmB,CAAC,KAAK,CAAC;MAC1B;IACC;IAEF,MAAM5B,IAAI,GAAGiC,OAAO,CAACC,qBAAqB,CAAC,CAAC;IAC5C,MAAMsI,OAAO,GAAGC,UAAU,CAAC,CAAAtJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuJ,mBAAmB,KAAI,GAAG,CAAC;IACvE,MAAMC,OAAO,GAAGF,UAAU,CAAC,CAAAtJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyJ,mBAAmB,KAAI,GAAG,CAAC;IAEvEvJ,gBAAgB,CAACY,OAAO,CAAC;IACzBL,mBAAmB,CAAC,IAAI,CAAC;;IAEvB;IACA,IAAIiJ,YAAiD;IACrD,IAAI1J,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE2J,YAAY,EAAE;MACjCD,YAAY,GAAG7I,qBAAqB,CAACC,OAAO,CAAC;IAC/C,CAAC,MAAM;MAAA,IAAA8I,qBAAA;MACL,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAU;MACnE,MAAMzD,SAAS,GAAG,CAAApG,eAAe,aAAfA,eAAe,wBAAA4J,qBAAA,GAAf5J,eAAe,CAAE8J,MAAM,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBG,QAAQ,KAAI,QAAQ;MAC/DL,YAAY,GAAGG,eAAe,CAACG,QAAQ,CAAC5D,SAAgB,CAAC,GACpDA,SAAS,GACV,QAAQ;IACd;IAGA,IAAIxF,gBAAgB,CAAC2F,OAAO,KAAKmD,YAAY,EAAE;MAC7C9I,gBAAgB,CAAC2F,OAAO,GAAGmD,YAAY;MACvCnJ,mBAAmB,CAACmJ,YAAY,CAAC;IACnC;IAGA,MAAMO,WAAW,GAAG;MAClB5J,GAAG,EAAEsB,IAAI,CAACuI,KAAK,CAACrL,IAAI,CAACwB,GAAG,GAAGY,MAAM,CAACyF,OAAO,GAAG8C,OAAO,CAAC;MACpDvM,IAAI,EAAE0E,IAAI,CAACuI,KAAK,CAACrL,IAAI,CAAC5B,IAAI,GAAGgE,MAAM,CAAC2F,OAAO,GAAGyC,OAAO;IACvD,CAAC;IAGD,MAAMc,eAAe,GACnBxI,IAAI,CAACyI,GAAG,CAACzJ,eAAe,CAAC4F,OAAO,CAAClG,GAAG,GAAG4J,WAAW,CAAC5J,GAAG,CAAC,GAAG,CAAC,IAC3DsB,IAAI,CAACyI,GAAG,CAACzJ,eAAe,CAAC4F,OAAO,CAACtJ,IAAI,GAAGgN,WAAW,CAAChN,IAAI,CAAC,GAAG,CAAC;IAE/D,IAAIkN,eAAe,EAAE;MACnBxJ,eAAe,CAAC4F,OAAO,GAAG0D,WAAW;MACrC7J,kBAAkB,CAAC6J,WAAW,CAAC;IACjC;EACF,CAAC;;EAED;EACD,MAAMI,UAAU,GAAGlQ,WAAW,CAAC,YAAY;IAC1C,IAAIuF,gBAAgB,KAAK,MAAM,EAAE;MAChC;MACA,MAAM4K,SAAS,GAAGxL,gBAAgB,GAAG,CAAC;MAEtC,IAAIwL,SAAS,IAAIvM,KAAK,CAACsJ,MAAM,EAAE;QAC9B;QACA,MAAMkD,YAAY,GAAGxM,KAAK,CAACuM,SAAS,GAAG,CAAC,CAAC;QAEzCjI,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;UACnDkI,YAAY,EAAE1L,gBAAgB;UAC9BwL,SAAS,EAAEA,SAAS;UACpBpI,KAAK,EAAEqI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAErI,KAAK;UAC1BuI,SAAS,EAAE,QAAQH,SAAS;QAC7B,CAAC,CAAC;;QAEF;QACA,IAAIC,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAErI,KAAK,EAAE;UACxBG,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;YACxDJ,KAAK,EAAEqI,YAAY,CAACrI,KAAK;YACzBiH,SAAS,EAAEmB,SAAS;YACpBG,SAAS,EAAE,QAAQH,SAAS;UAC7B,CAAC,CAAC;;UAEF;UACA,MAAMI,aAAa,GAAG,IAAIvL,OAAO,CAAQC,OAAO,IAAK;YACpD;YACAyF,cAAc,CACb0F,YAAY,CAACrI,KAAK,IAAI,EAAE,EACxBqI,YAAY,CAACpI,mBAAmB,IAAI,EAAE,EACtC,MAAOwI,YAAY,IAAK;cACvBtI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;gBACpCxB,OAAO,EAAE6J,YAAY;gBACrBrE,OAAO,EAAEqE,YAAY,CAACrE,OAAO;gBAC7BjL,SAAS,EAAEsP,YAAY,CAACtP,SAAS;gBACjCQ,EAAE,EAAE8O,YAAY,CAAC9O;cAClB,CAAC,CAAC;;cAEF;cACA,MAAMgD,IAAI,GAAG8L,YAAY,CAAC5J,qBAAqB,CAAC,CAAC;cACjD,MAAMI,cAAc,GAAGF,MAAM,CAACG,WAAW;cACzC,MAAMJ,aAAa,GAAGC,MAAM,CAACC,UAAU;;cAEvC;cACA,MAAM0J,mBAAmB,GACxB/L,IAAI,CAACwB,GAAG,IAAI,CAAC,EAAE;cAAI;cACnBxB,IAAI,CAAC5B,IAAI,IAAI,CAAC,EAAE;cAAI;cACpB4B,IAAI,CAAC1B,MAAM,IAAIgE,cAAc,GAAG,EAAE;cAAI;cACtCtC,IAAI,CAAC4C,KAAK,IAAIT,aAAa,GAAG,EAAE;cAAI;cACpCnC,IAAI,CAAC/B,KAAK,GAAG,CAAC,IACd+B,IAAI,CAACiD,MAAM,GAAG,CACd;cAED,IAAI8I,mBAAmB,EAAE;gBACxBvI,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;gBAClE;gBACA,IAAI;kBACHqI,YAAY,CAACE,cAAc,CAAC;oBAC3B/G,QAAQ,EAAE,QAAQ;oBAClBgH,KAAK,EAAE,SAAS;oBAAE;oBAClBC,MAAM,EAAE;kBACT,CAAC,CAAC;gBACH,CAAC,CAAC,OAAO/H,KAAK,EAAE;kBACfX,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEU,KAAK,CAAC;gBACxD;cACD,CAAC,MAAM;gBACNX,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;gBAC7D;gBACA,IAAI;kBACHqI,YAAY,CAACE,cAAc,CAAC;oBAC3B/G,QAAQ,EAAE,QAAQ;oBAClBgH,KAAK,EAAE,QAAQ;oBAAE;oBACjBC,MAAM,EAAE;kBACT,CAAC,CAAC;kBACF1I,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;gBACpD,CAAC,CAAC,OAAO0I,WAAW,EAAE;kBACrB3I,OAAO,CAACW,KAAK,CAAC,uBAAuB,EAAEgI,WAAW,CAAC;gBACpD;cACD;cAEA5L,OAAO,CAAC,CAAC;YACV,CAAC,EACD,EAAE;YAAE;YACJ,EAAE;YAAE;YACJ,mBAAmB,EACnB,MAAM;cACLiD,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;cAChFlD,OAAO,CAAC,CAAC;YACV,CACD,CAAC;UACF,CAAC,CAAC;UAEF,IAAI;YACH,MAAMsL,aAAa;YACnBrI,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC5D,CAAC,CAAC,OAAOU,KAAK,EAAE;YACfX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UACjD;QACD,CAAC,MAAM;UACNX,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACxE;;QAEA;QACAD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAExD,gBAAgB,EAAE,IAAI,EAAEwL,SAAS,CAAC;QAC7EvL,mBAAmB,CAACuL,SAAS,CAAC;QAC9B7K,cAAc,CAACE,WAAW,GAAG,CAAC,CAAC;;QAE/B;QACA,MAAM,IAAIR,OAAO,CAACC,OAAO,IAAIyG,UAAU,CAACzG,OAAO,EAAE,EAAE,CAAC,CAAC;MAEtD,CAAC,MAAM;QACNiD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC9CxD,gBAAgB;UAChBwL,SAAS;UACTW,UAAU,EAAElN,KAAK,CAACsJ;QACnB,CAAC,CAAC;QACF;QACA;MACD;IACD,CAAC,MAAM;MACN;MACA,IAAI1H,WAAW,IAAG5B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEsJ,MAAM,GAAE;QAChCtI,mBAAmB,CAAEmM,IAAS,IAAKvJ,IAAI,CAACC,GAAG,CAACsJ,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzDzL,cAAc,CAACE,WAAW,GAAG,CAAC,CAAC;MAChC;IACD;EACD,CAAC,EAAE,CAACb,gBAAgB,EAAEf,KAAK,EAAE2B,gBAAgB,EAAEC,WAAW,EAAEkF,cAAc,EAAEhE,qBAAqB,EAAEsF,qBAAqB,EAAE1G,cAAc,EAAEV,mBAAmB,CAAC,CAAC;;EAE9J;EACD,MAAMoM,cAAc,GAAGhR,WAAW,CAAC,YAAY;IAC9C,IAAIuF,gBAAgB,KAAK,MAAM,EAAE;MAChC,MAAM0L,SAAS,GAAGzJ,IAAI,CAACC,GAAG,CAAC9C,gBAAgB,GAAG,CAAC,EAAE,CAAC,CAAC;MAEnD,IAAIsM,SAAS,IAAI,CAAC,EAAE;QACnB/I,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC9CkI,YAAY,EAAE1L,gBAAgB;UAC9BsM,SAAS,EAAEA;QACZ,CAAC,CAAC;;QAEF;QACA,IAAIA,SAAS,KAAK,CAAC,EAAE;UACpB/I,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,IAAI;YACHrB,MAAM,CAAC4C,QAAQ,CAAC;cACfxD,GAAG,EAAE,CAAC;cACNyD,QAAQ,EAAE;YACX,CAAC,CAAC;UACH,CAAC,CAAC,OAAOd,KAAK,EAAE;YACfX,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEU,KAAK,CAAC;UAC5D;QACD,CAAC,MAAM;UACN;UACA,MAAMqI,YAAY,GAAGtN,KAAK,CAACqN,SAAS,GAAG,CAAC,CAAC;UACzC,IAAIC,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEnJ,KAAK,EAAE;YACxBG,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;YAEhFuD,UAAU,CAAC,MAAM;cAChB,MAAMyF,WAAW,GAAGrJ,iBAAiB,CAACoJ,YAAY,CAACnJ,KAAK,EAAEmJ,YAAY,CAAClJ,mBAAmB,IAAI,EAAE,CAAC;cACjG,IAAImJ,WAAW,EAAE;gBAChB,MAAMzM,IAAI,GAAGyM,WAAW,CAACvK,qBAAqB,CAAC,CAAC;gBAChD,MAAMwK,WAAW,GAChB1M,IAAI,CAAC1B,MAAM,GAAG,CAAC,IACf0B,IAAI,CAACwB,GAAG,GAAGY,MAAM,CAACG,WAAW,IAC7BvC,IAAI,CAAC4C,KAAK,GAAG,CAAC,IACd5C,IAAI,CAAC5B,IAAI,GAAGgE,MAAM,CAACC,UACnB;gBAED,IAAIqK,WAAW,EAAE;kBAChBlJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;kBACtE,IAAI;oBACHgJ,WAAW,CAACT,cAAc,CAAC;sBAC1B/G,QAAQ,EAAE,QAAQ;sBAClBgH,KAAK,EAAE,QAAQ;sBACfC,MAAM,EAAE;oBACT,CAAC,CAAC;kBACH,CAAC,CAAC,OAAO/H,KAAK,EAAE;oBACfX,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEU,KAAK,CAAC;kBAC7D;gBACD;cACD;YACD,CAAC,EAAE,GAAG,CAAC;UACR;QACD;;QAEA;QACAjE,mBAAmB,CAACqM,SAAS,CAAC;QAC9B3L,cAAc,CAACE,WAAW,GAAG,CAAC,CAAC;;QAE/B;QACA,MAAM,IAAIR,OAAO,CAACC,OAAO,IAAIyG,UAAU,CAACzG,OAAO,EAAE,EAAE,CAAC,CAAC;MACtD;IACD,CAAC,MAAM;MACNK,cAAc,CAACE,WAAW,GAAG,CAAC,CAAC;IAChC;EACD,CAAC,EAAE,CAACb,gBAAgB,EAAEY,gBAAgB,EAAEC,WAAW,EAAEmG,iBAAiB,EAAErG,cAAc,EAAEV,mBAAmB,CAAC,CAAC;EAC5G/E,SAAS,CAAC,MAAM;IAAA,IAAAwR,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACd;IACAtJ,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;MACzDxD,gBAAgB;MAChB8M,YAAY,EAAE5L,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4L,YAAY;MAC3CC,QAAQ,EAAE7L,eAAe,aAAfA,eAAe,wBAAAwL,qBAAA,GAAfxL,eAAe,CAAE4L,YAAY,cAAAJ,qBAAA,uBAA7BA,qBAAA,CAA+BK,QAAQ;MACjDC,EAAE,EAAE9L,eAAe,aAAfA,eAAe,wBAAAyL,sBAAA,GAAfzL,eAAe,CAAE4L,YAAY,cAAAH,sBAAA,uBAA7BA,sBAAA,CAA+BK,EAAE;MACrC5J,KAAK,EAAElC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC;IAC1B,CAAC,CAAC;IAEF,IAAI,CAAAlC,eAAe,aAAfA,eAAe,wBAAA0L,sBAAA,GAAf1L,eAAe,CAAE4L,YAAY,cAAAF,sBAAA,uBAA7BA,sBAAA,CAA+BG,QAAQ,MAAK,SAAS,IAAI,CAAA7L,eAAe,aAAfA,eAAe,wBAAA2L,sBAAA,GAAf3L,eAAe,CAAE4L,YAAY,cAAAD,sBAAA,uBAA7BA,sBAAA,CAA+BE,QAAQ,MAAK,QAAQ,EAAE;MACpH,MAAM/K,OAAO,GAAGmB,iBAAiB,CAACjC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK,EAAClC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,mBAAmB,CAAC;MAC3F,IAAIrB,OAAO,EAAE;QACXuB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAExB,OAAO,CAAC;QAC1D,MAAMiL,WAAW,GAAGA,CAAA,KAAM;UAC7B1J,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D+H,UAAU,CAAC,CAAC;QACb,CAAC;QAEDvJ,OAAO,CAACkL,gBAAgB,CAAC,OAAO,EAAED,WAAW,CAAC;QAC1C,OAAO,MAAM;UAChBjL,OAAO,CAACmL,mBAAmB,CAAC,OAAO,EAAEF,WAAW,CAAC;QAClD,CAAC;MACG,CAAC,MAAM;QACP1J,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEtC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK,CAAC;MACvE;IACJ,CAAC,MAAM;MAAA,IAAAgK,sBAAA;MACL7J,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEtC,eAAe,aAAfA,eAAe,wBAAAkM,sBAAA,GAAflM,eAAe,CAAE4L,YAAY,cAAAM,sBAAA,uBAA7BA,sBAAA,CAA+BL,QAAQ,CAAC;IAC/F;EACD,CAAC,EAAE,CAAC7L,eAAe,EAAEqK,UAAU,CAAC,CAAC;EAEhCrQ,SAAS,CAAC,MAAM;IACd,MAAMmS,gBAAgB,GAAGA,CAAA,KAAM;MAChCvH,qBAAqB,CAACsE,uBAAuB,CAAC;IAC/C,CAAC;IACDxI,WAAW,CAAC6F,OAAO,GAAG,IAAIoC,gBAAgB,CAACwD,gBAAgB,CAAC;IAC5D,MAAMC,UAAU,GAAG5J,QAAQ,CAAC4E,IAAI;IAC9B1G,WAAW,CAAC6F,OAAO,CAACmC,OAAO,CAAC0D,UAAU,EAAE;MACtCxD,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,IAAI;MAChBuD,aAAa,EAAE;IACnB,CAAC,CAAC;IACFnD,uBAAuB,CAAC,CAAC;IACvB,OAAO,MAAM;MAAA,IAAAoD,oBAAA;MACd,CAAAA,oBAAA,GAAA5L,WAAW,CAAC6F,OAAO,cAAA+F,oBAAA,uBAAnBA,oBAAA,CAAqBrD,UAAU,CAAC,CAAC;IAClC,CAAC;EACF,CAAC,EAAE,CAACjJ,eAAe,EAAEhC,UAAU,CAAC,CAAC;EAEhChE,SAAS,CAAC,MAAM;IAChB,MAAMuS,qBAAqB,GAAGA,CAAA,KAAM;MACnC3H,qBAAqB,CAACsE,uBAAuB,CAAC;IAC/C,CAAC;IAEDjI,MAAM,CAAC+K,gBAAgB,CAAC,QAAQ,EAAEO,qBAAqB,CAAC;IACxDtL,MAAM,CAAC+K,gBAAgB,CAAC,QAAQ,EAAEO,qBAAqB,CAAC;IAEtD,OAAO,MAAM;MACdtL,MAAM,CAACgL,mBAAmB,CAAC,QAAQ,EAAEM,qBAAqB,CAAC;MAC3DtL,MAAM,CAACgL,mBAAmB,CAAC,QAAQ,EAAEM,qBAAqB,CAAC;IAC5D,CAAC;EACF,CAAC,EAAE,CAACvM,eAAe,EAAEhC,UAAU,CAAC,CAAC;EAChChE,SAAS,CAAC,MAAM;IAChBkP,uBAAuB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAAClJ,eAAe,EAAEhC,UAAU,EAAEa,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEzC;EACA7E,SAAS,CAAC,MAAM;IACf,IAAI,EAACgG,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEkC,KAAK,GAAE;MAC5B;IACD;;IAEA;IACA,MAAMsK,SAAS,GAAG3G,UAAU,CAAC,MAAM;MAClC,MAAM4G,cAAc,GAAGxK,iBAAiB,CAACjC,eAAe,CAACkC,KAAK,EAAElC,eAAe,CAACmC,mBAAmB,IAAI,EAAE,CAAC;MAE1G,IAAIsK,cAAc,EAAE;QACnB,MAAM5N,IAAI,GAAG4N,cAAc,CAAC1L,qBAAqB,CAAC,CAAC;QACnD,MAAMI,cAAc,GAAGF,MAAM,CAACG,WAAW;QACzC,MAAMJ,aAAa,GAAGC,MAAM,CAACC,UAAU;;QAEvC;QACA,MAAMwL,qBAAqB,GAC1B7N,IAAI,CAAC1B,MAAM,GAAG,CAAC;QAAI;QACnB0B,IAAI,CAACwB,GAAG,GAAGc,cAAc;QAAI;QAC7BtC,IAAI,CAAC4C,KAAK,GAAG,CAAC;QAAI;QAClB5C,IAAI,CAAC5B,IAAI,GAAG+D,aAAa,CAAC;QAC1B;QAED,IAAI0L,qBAAqB,EAAE;UAC1BrK,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;UACzE,IAAI;YACHmK,cAAc,CAAC5B,cAAc,CAAC;cAC7B/G,QAAQ,EAAE,QAAQ;cAClBgH,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACT,CAAC,CAAC;UACH,CAAC,CAAC,OAAO/H,KAAK,EAAE;YACfX,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEU,KAAK,CAAC;UACvD;QACD;MACD;IACD,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM;MACZyC,YAAY,CAAC+G,SAAS,CAAC;IACxB,CAAC;EACF,CAAC,EAAE,CAACxM,eAAe,CAAC,CAAC;EACrB,MAAM1E,WAAW,GAAG,CAAA0E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8J,MAAM,KAAI,CAAC,CAAC,CAAC,CAAC;EACnD,MAAM6C,cAAc,GAAGzO,aAAa,CAAC0O,cAAc,IAAI,KAAK;EAC3D,SAASC,mBAAmBA,CAAC3O,aAAkB,EAAE;IAC/C,IAAI,CAAAA,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4O,gBAAgB,MAAK,GAAG,EAAE;MAC9C,OAAO,MAAM;IACZ,CAAC,MAAM,IAAI,CAAA5O,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4O,gBAAgB,MAAK,GAAG,EAAE;MACrD,OAAO,QAAQ;IACd,CAAC,MAAM,IAAI,CAAA5O,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4O,gBAAgB,MAAK,GAAG,EAAE;MACrD,OAAO,aAAa;IACnB,CAAC,MAAM;MACR,OAAO,aAAa;IACnB;EACF;EACD,MAAMC,gBAAgB,GAAGF,mBAAmB,CAAC3O,aAAa,CAAC;EAE3D,MAAM8O,WAAW,GAAGhN,eAAe,aAAfA,eAAe,wBAAA1B,qBAAA,GAAf0B,eAAe,CAAEiN,KAAK,cAAA3O,qBAAA,uBAAtBA,qBAAA,CAAwB4O,aAAa;EACxD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC5B,MAAMC,QAAQ,GACb,CAAAzN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0N,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC,MAAI3N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0N,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC;IAClG,MAAMC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAAC9N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC,GACnD/N,eAAe,CAAC+N,OAAO,CAACC,IAAI,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI,OAAOD,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAID,IAAI,CAACC,IAAI,CAAC9L,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GACjH,QAAOpC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,MAAK,QAAQ,iBAAIhU,KAAK,CAACoU,cAAc,CAACnO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC;IAC/F,MAAMK,SAAS,GAAG;MAChB7Q,QAAQ,EAAE,MAAM;MAChB8Q,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,UAAU;MACtBC,SAAS,EAAE,YAAY;MACvBjR,KAAK,EAAE;IACX,CAAC;IACC,oBACErC,OAAA,CAACX,GAAG;MAAAkU,QAAA,GACDf,QAAQ,iBACPxS,OAAA,CAACX,GAAG;QACFmU,SAAS,EAAC,KAAK;QACfC,GAAG,EAAE1O,eAAe,aAAfA,eAAe,wBAAAoN,qBAAA,GAAfpN,eAAe,CAAE2O,eAAe,cAAAvB,qBAAA,uBAAhCA,qBAAA,CAAkCwB,GAAI;QAC3CC,GAAG,EAAE,CAAA7O,eAAe,aAAfA,eAAe,wBAAAqN,sBAAA,GAAfrN,eAAe,CAAE2O,eAAe,cAAAtB,sBAAA,uBAAhCA,sBAAA,CAAkCyB,OAAO,KAAI,YAAa;QAC/DC,EAAE,EAAE;UACF5S,eAAe,EAAE,CAAA6D,eAAe,aAAfA,eAAe,wBAAAsN,sBAAA,GAAftN,eAAe,CAAE2O,eAAe,cAAArB,sBAAA,uBAAhCA,sBAAA,CAAkClR,eAAe,KAAI,aAAa;UACnF4S,SAAS,EAAE,CAAAhP,eAAe,aAAfA,eAAe,wBAAAuN,sBAAA,GAAfvN,eAAe,CAAE2O,eAAe,cAAApB,sBAAA,uBAAhCA,sBAAA,CAAkC0B,GAAG,KAAI,OAAO;UAC3DC,SAAS,EAAE,CAAAlP,eAAe,aAAfA,eAAe,wBAAAwN,sBAAA,GAAfxN,eAAe,CAAE2O,eAAe,cAAAnB,sBAAA,uBAAhCA,sBAAA,CAAkC2B,aAAa,KAAI,MAAM;UACpErS,KAAK,EAAE;QACT;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,EACA2R,OAAO,iBACN3S,OAAA,CAACX,GAAG;QACFe,SAAS,EAAC,eAAe;QAChC0T,EAAE,EAAE;UAAEK,MAAM,EAAE,cAAc;UAAE,GAAGhB,SAAS;UAAE,KAAK,EAAE;YAClDgB,MAAM,EAAE;UACP;QAAE,CAAE;QAAKC,uBAAuB,EAAE;UAC3BC,MAAM,EAAEzB,KAAK,CAACC,OAAO,CAAC9N,eAAe,CAAC+N,OAAO,CAAC,GAC1C/N,eAAe,CAAC+N,OAAO,CAC/BwB,GAAG,CAAEtB,IAAS,IACdA,IAAI,CAACC,IAAI,CAACsB,OAAO,CAAC,MAAM,EAAE,+CAA+C,CAC1E,CAAC,CACWC,IAAI,CAAC,OAAO,CAAC,GAChB,OAAOzP,eAAe,CAAC+N,OAAO,KAAK,QAAQ,GACzC/N,eAAe,CAAC+N,OAAO,CAACyB,OAAO,CAAC,MAAM,EAAE,+CAA+C,CAAC,GACxF;QACR;MAAE;QAAA1T,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAEN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAER,CAAC;;EAED;EACA,MAAMV,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAmU,qBAAA;IAC5B,MAAMjC,QAAQ,GACb,CAAAzN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0N,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC,MAAI3N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0N,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC;IACpG,MAAMC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAAC9N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC,GACpD/N,eAAe,CAAC+N,OAAO,CAACC,IAAI,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI,OAAOD,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAID,IAAI,CAACC,IAAI,CAAC9L,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GAC9G,QAAOpC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,MAAK,QAAQ,iBAAIhU,KAAK,CAACoU,cAAc,CAACnO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC;IACjG,MAAM4B,UAAU,GAAG,CAAA3P,eAAe,aAAfA,eAAe,wBAAA0P,qBAAA,GAAf1P,eAAe,CAAE4P,UAAU,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BrI,MAAM,IAAG,CAAC;IAE1D,OAAOsI,UAAU,IAAI,CAAClC,QAAQ,IAAI,CAACG,OAAO;EAC3C,CAAC;;EAED;EACA,MAAMpS,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAqU,sBAAA;IACzB,MAAMpC,QAAQ,GACb,CAAAzN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0N,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC,MAAI3N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0N,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC;IACpG,MAAMC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAAC9N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC,GACpD/N,eAAe,CAAC+N,OAAO,CAACC,IAAI,CAAEC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI,OAAOD,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAID,IAAI,CAACC,IAAI,CAAC9L,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GAC9G,QAAOpC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,MAAK,QAAQ,iBAAIhU,KAAK,CAACoU,cAAc,CAACnO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC;IACjG,MAAM4B,UAAU,GAAG,CAAA3P,eAAe,aAAfA,eAAe,wBAAA6P,sBAAA,GAAf7P,eAAe,CAAE4P,UAAU,cAAAC,sBAAA,uBAA3BA,sBAAA,CAA6BxI,MAAM,IAAG,CAAC;IAE1D,OAAOuG,OAAO,IAAI,CAACH,QAAQ,IAAI,CAACkC,UAAU;EAC3C,CAAC;EACD,MAAMG,wBAAwB,GAAIC,WAAmB,IAAc;IAClE,IAAI,CAACA,WAAW,IAAIA,WAAW,CAAC3N,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9C,OAAO,KAAK;IACb;;IAEA;IACA,IAAI4N,cAAc,GAAGD,WAAW;;IAEhC;IACAC,cAAc,GAAGA,cAAc,CAACR,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;;IAExE;IACAQ,cAAc,GAAGA,cAAc,CAACR,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC;;IAE5E;IACAQ,cAAc,GAAGA,cAAc,CAACR,OAAO,CAAC,iCAAiC,EAAE,EAAE,CAAC;;IAE9E;IACAQ,cAAc,GAAGA,cAAc,CAACR,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;;IAE3D;IACAQ,cAAc,GAAGA,cAAc,CAACR,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;;IAExD;IACA,IAAIQ,cAAc,CAAC5N,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjC,OAAO,KAAK;IACb;;IAEA;IACA,MAAM6N,OAAO,GAAGzN,QAAQ,CAACkF,aAAa,CAAC,KAAK,CAAC;IAC7CuI,OAAO,CAACtI,SAAS,GAAGqI,cAAc;;IAElC;IACA,MAAME,WAAW,GAAGD,OAAO,CAACC,WAAW,IAAID,OAAO,CAACE,SAAS;;IAE5D;IACA,IAAID,WAAW,KAAK,IAAI,IAAIA,WAAW,CAAC9N,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACtD,OAAO,KAAK;IACb;;IAEA;IACA;IACA,MAAMgO,YAAY,GAAGJ,cAAc,CAACK,WAAW,CAAC,CAAC;IACjD,MAAMC,aAAa,GAAG,CACrB,iBAAiB,EACjB,aAAa,EACb,aAAa,EACb,SAAS,EACT,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,UAAU,EACV,cAAc,CACd;IAED,IAAIA,aAAa,CAACtC,IAAI,CAACuC,OAAO,IAAIH,YAAY,CAACpG,QAAQ,CAACuG,OAAO,CAAC,CAAC,IAAIL,WAAW,CAAC9N,IAAI,CAAC,CAAC,CAACiF,MAAM,IAAI,CAAC,EAAE;MACpG,OAAO,KAAK;IACb;IAEA,OAAO,IAAI;EACZ,CAAC;EAED,MAAMmJ,mBAAmB,GACvB,QAAOxQ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,MAAK,QAAQ,IAAI+B,wBAAwB,CAAC9P,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC,iBACnGhU,KAAK,CAACoU,cAAc,CAACnO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC,IAC7CF,KAAK,CAACC,OAAO,CAAC9N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+N,OAAO,CAAC,IAAI/N,eAAe,CAAC+N,OAAO,CAACC,IAAI,CACtEC,IAAS,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI,OAAOD,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAI4B,wBAAwB,CAAC7B,IAAI,CAACC,IAAI,CACjG,CAAE;EACF;EACD,MAAMyB,UAAU,GAAG,CAAA3P,eAAe,aAAfA,eAAe,wBAAAzB,sBAAA,GAAfyB,eAAe,CAAE4P,UAAU,cAAArR,sBAAA,uBAA3BA,sBAAA,CAA6B8I,MAAM,IAAG,CAAC;;EAE1D;EACA,MAAMoJ,aAAa,GAClB,CAAAzQ,eAAe,aAAfA,eAAe,wBAAAxB,sBAAA,GAAfwB,eAAe,CAAE0N,QAAQ,cAAAlP,sBAAA,uBAAzBA,sBAAA,CAA2BmP,UAAU,CAAC,aAAa,CAAC,MACpD3N,eAAe,aAAfA,eAAe,wBAAAvB,sBAAA,GAAfuB,eAAe,CAAE0N,QAAQ,cAAAjP,sBAAA,uBAAzBA,sBAAA,CAA2BkP,UAAU,CAAC,MAAM,CAAC;EAC9C;EACA,MAAM+C,kBAAkB,GAAGF,mBAAmB,IAAI,CAACC,aAAa,IAAI,CAACd,UAAU;;EAE/E;EACA,MAAMgB,aAAa,GAAGhB,UAAU,IAAI,CAACa,mBAAmB,IAAI,CAACC,aAAa,IAAI,CAAAzQ,eAAe,aAAfA,eAAe,wBAAAtB,sBAAA,GAAfsB,eAAe,CAAE4P,UAAU,cAAAlR,sBAAA,uBAA3BA,sBAAA,CAA6B2I,MAAM,MAAK,CAAC;;EAEvH;EACA,MAAMuJ,eAAe,GAAGJ,mBAAmB,IAAIC,aAAa;EAC3D;EACA,MAAMI,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACxB;IACA,MAAMC,iBAAiB,GAAG,CAAAnR,eAAe,aAAfA,eAAe,wBAAA8Q,sBAAA,GAAf9Q,eAAe,CAAE4P,UAAU,cAAAkB,sBAAA,uBAA3BA,sBAAA,CAA6BzJ,MAAM,MAAK,CAAC,IAClE,CAAArH,eAAe,aAAfA,eAAe,wBAAA+Q,sBAAA,GAAf/Q,eAAe,CAAE4P,UAAU,cAAAmB,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkCI,YAAY,cAAAH,sBAAA,wBAAAC,sBAAA,GAA9CD,sBAAA,CAAgDI,MAAM,cAAAH,sBAAA,uBAAtDA,sBAAA,CAAwDI,iBAAiB,CAAC,CAAC,MAAK,UAAU;;IAE3F;IACA,IAAIH,iBAAiB,EAAE;MACtB,OAAO,KAAK;IACb;;IAEA;IACA,IAAI,CAACP,eAAe,EAAE;MAAA,IAAAW,uBAAA;MACrB,OAAO,CAAAvR,eAAe,aAAfA,eAAe,wBAAAuR,uBAAA,GAAfvR,eAAe,CAAE4P,UAAU,cAAA2B,uBAAA,uBAA3BA,uBAAA,CAA6BlK,MAAM,MAAK,CAAC,GAAG,KAAK,GAAG,KAAK;IACjE,CAAC,MAAM;MACN,OAAO,KAAK;IACZ;EACF,CAAC;;EAGF;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;;EAEA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACC,MAAMmK,cAAc,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC7E,cAAc,EAAE,OAAO,IAAI;IAE9B,IAAII,gBAAgB,KAAK,MAAM,EAAE;MAC/B,oBACE9R,OAAA,CAACP,aAAa;QACZ+W,OAAO,EAAC,MAAM;QACd1T,KAAK,EAAEA,KAAK,CAACsJ,MAAO;QACpBqK,QAAQ,EAAC,QAAQ;QACjBC,UAAU,EAAE7S,gBAAgB,GAAG,CAAE;QACjCiQ,EAAE,EAAE;UACR5S,eAAe,EAAE,aAAa;UAAE,+BAA+B,EAAE;YAChEA,eAAe,EAAEyD,aAAa,CAAE;UAC3B,CAAC;UACDgS,YAAY,EAAE,QAAQ;UACtBvV,OAAO,EAAE,iBAAiB;UAC1B,yBAAyB,EAAE;YACzBS,KAAK,EAAE,gBAAgB;YACvBgF,MAAM,EAAE;UAChB;QACI,CAAE;QACF+P,UAAU,eAAE5W,OAAA,CAACb,MAAM;UAACoN,KAAK,EAAE;YAAEsK,OAAO,EAAE;UAAO;QAAE;UAAAhW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnD8V,UAAU,eAAE9W,OAAA,CAACb,MAAM;UAACoN,KAAK,EAAE;YAAEsK,OAAO,EAAE;UAAO;QAAE;UAAAhW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAEN;;IAEF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACE,IAAI8Q,gBAAgB,KAAK,aAAa,EAAE;MACtC,oBACU9R,OAAA,CAACX,GAAG;QAACyU,EAAE,EAAE;UAAC+C,OAAO,EAAE,MAAM;UACpCE,UAAU,EAAE,QAAQ;UACpBJ,YAAY,EAAE,QAAQ;UACtBK,GAAG,EAAE,KAAK;UAAC5V,OAAO,EAAC;QAAK,CAAE;QAAAmS,QAAA,EAGpBX,KAAK,CAACqE,IAAI,CAAC;UAAE7K,MAAM,EAAEtJ,KAAK,CAACsJ;QAAO,CAAC,CAAC,CAACkI,GAAG,CAAC,CAAC4C,CAAC,EAAEC,KAAK,kBACjDnX,OAAA;UAEEuM,KAAK,EAAE;YACK1K,KAAK,EAAE,MAAM;YACbgF,MAAM,EAAE,KAAK;YACb3F,eAAe,EAAEiW,KAAK,KAAKzS,WAAW,GAAG,CAAC,GAAGC,aAAa,GAAG,SAAS;YAAE;YACxErD,YAAY,EAAE;UAC1B;QAAE,GANG6V,KAAK;UAAAtW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAES,CAAC;IAEpB;IAEA,IAAI8Q,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACC9R,OAAA,CAACX,GAAG;QAACyU,EAAE,EAAE;UAACsD,UAAU,EAAC;QAAK,CAAE;QAAA7D,QAAA,eAC3BvT,OAAA,CAACT,UAAU;UACViX,OAAO,EAAC,OAAO;UACf1C,EAAE,EAAE;YAAE1S,OAAO,EAAE,KAAK;YAAEiB,KAAK,EAAEsC;UAAa,CAAE;UAAA4O,QAAA,GAC5C,OACM,EAAC1P,gBAAgB,EAAC,MAAI,EAACf,KAAK,CAACsJ,MAAM;QAAA;UAAAvL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAI8Q,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACC9R,OAAA,CAACX,GAAG;QAACyU,EAAE,EAAE;UAAC1S,OAAO,EAAEd,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG;QAAI,CAAE;QAAAiT,QAAA,eACnDvT,OAAA,CAACT,UAAU;UAACiX,OAAO,EAAC,OAAO;UAAAjD,QAAA,eAC1BvT,OAAA,CAACV,cAAc;YACdkX,OAAO,EAAC,aAAa;YACrBa,KAAK,EAAE9N,QAAS;YAChBuK,EAAE,EAAE;cACHjN,MAAM,EAAE,KAAK;cACXvF,YAAY,EAAE,MAAM;cACpB6S,MAAM,EAAE7T,cAAc,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU;cAC7C,0BAA0B,EAAE;gBACJY,eAAe,EAAEyD,aAAa,CAAE;cAClC;YAAE;UAAE;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EAED,MAAMsW,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,uBAAA;IAC3B,OAAO,CAAAxS,eAAe,aAAfA,eAAe,wBAAAwS,uBAAA,GAAfxS,eAAe,CAAE4P,UAAU,cAAA4C,uBAAA,uBAA3BA,uBAAA,CAA6BnL,MAAM,IAAG,CAAC,GAC3CrH,eAAe,CAAC4P,UAAU,CAACL,GAAG,CAAC,CAACkD,MAAW,EAAEL,KAAU,KAAK;MAC5D,MAAMM,WAAW,GAAG;QACnBvW,eAAe,EAAEsW,MAAM,CAACE,gBAAgB,CAACC,qBAAqB;QAC9DtV,KAAK,EAAEmV,MAAM,CAACE,gBAAgB,CAACE,eAAe;QAC9ClW,MAAM,EAAE8V,MAAM,CAACE,gBAAgB,CAACG,iBAAiB;QACjDzW,OAAO,EAAE,oBAAoB;QAC7BgS,UAAU,EAAE,QAAQ;QACpBvR,KAAK,EAAE,MAAM;QACbS,QAAQ,EAAE,MAAM;QAChBwV,UAAU,EAAE,SAAS;QACrBxW,YAAY,EAAE,KAAK;QACnByW,aAAa,EAAE,MAAM;QACrBC,QAAQ,EAAE,aAAa;QACvBvW,SAAS,EAAE,iBAAiB;QAAE;QAC9B,SAAS,EAAE;UACVA,SAAS,EAAE,iBAAiB;UAAE;UAC9BP,eAAe,EAAEsW,MAAM,CAACE,gBAAgB,CAACC,qBAAqB;UAAE;UAChEM,OAAO,EAAE,GAAG,CAAE;QACf;MACD,CAAC;MACD,MAAMnH,WAAW,GAAGA,CAAA,KAAM;QAAA,IAAAoH,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA;QACzBnR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;UACjCmR,QAAQ,EAAEhB,MAAM,CAAC3G,EAAE;UACnB4H,YAAY,EAAEjB,MAAM,CAACrB,YAAY,CAACC,MAAM;UACxCzF,YAAY,EAAE5L,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4L,YAAY;UAC3CC,QAAQ,EAAE7L,eAAe,aAAfA,eAAe,wBAAAmT,sBAAA,GAAfnT,eAAe,CAAE4L,YAAY,cAAAuH,sBAAA,uBAA7BA,sBAAA,CAA+BtH,QAAQ;UACjD8H,gBAAgB,EAAE3T,eAAe,aAAfA,eAAe,wBAAAoT,sBAAA,GAAfpT,eAAe,CAAE4L,YAAY,cAAAwH,sBAAA,uBAA7BA,sBAAA,CAA+BtH;QAClD,CAAC,CAAC;QAEF,IAAI2G,MAAM,CAACrB,YAAY,CAACC,MAAM,CAACC,iBAAiB,CAAC,CAAC,KAAK,OAAO,EAAE;UAC/D;QAAA,CACA,MAAM,IACNmB,MAAM,CAACrB,YAAY,CAACC,MAAM,CAACC,iBAAiB,CAAC,CAAC,KAAK,MAAM,IACzD,CAAAtR,eAAe,aAAfA,eAAe,wBAAAqT,sBAAA,GAAfrT,eAAe,CAAE4L,YAAY,cAAAyH,sBAAA,uBAA7BA,sBAAA,CAA+BxH,QAAQ,MAAK,QAAQ,EACnD;UACDxJ,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtD+H,UAAU,CAAC,CAAC;QACb,CAAC,MAAM,IACNoI,MAAM,CAACrB,YAAY,CAACC,MAAM,CAACC,iBAAiB,CAAC,CAAC,KAAK,MAAM,IACzD,CAAAtR,eAAe,aAAfA,eAAe,wBAAAsT,sBAAA,GAAftT,eAAe,CAAE4L,YAAY,cAAA0H,sBAAA,uBAA7BA,sBAAA,CAA+BzH,QAAQ,MAAK,QAAQ,KACnD4G,MAAM,CAAC3G,EAAE,MAAK9L,eAAe,aAAfA,eAAe,wBAAAuT,uBAAA,GAAfvT,eAAe,CAAE4L,YAAY,cAAA2H,uBAAA,uBAA7BA,uBAAA,CAA+BK,QAAQ,KACrDnB,MAAM,CAAC3G,EAAE,MAAK9L,eAAe,aAAfA,eAAe,wBAAAwT,uBAAA,GAAfxT,eAAe,CAAE4L,YAAY,cAAA4H,uBAAA,uBAA7BA,uBAAA,CAA+B1H,EAAE,EAAC,EAChD;UAAA,IAAA+H,uBAAA,EAAAC,uBAAA;UACDzR,OAAO,CAACC,GAAG,CAAC,2EAA2E,EAAE;YACxFmR,QAAQ,EAAEhB,MAAM,CAAC3G,EAAE;YACnB6H,gBAAgB,EAAE3T,eAAe,aAAfA,eAAe,wBAAA6T,uBAAA,GAAf7T,eAAe,CAAE4L,YAAY,cAAAiI,uBAAA,uBAA7BA,uBAAA,CAA+BD,QAAQ;YACzDG,UAAU,EAAE/T,eAAe,aAAfA,eAAe,wBAAA8T,uBAAA,GAAf9T,eAAe,CAAE4L,YAAY,cAAAkI,uBAAA,uBAA7BA,uBAAA,CAA+BhI,EAAE;YAC7CF,YAAY,EAAE5L,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4L,YAAY;YAC3CzC,SAAS,EAAErK,gBAAgB;YAC3BkB,eAAe,EAAEA,eAAe;YAChCkC,KAAK,EAAElC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK;YAC7B4C,mBAAmB,EAAE9E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC;UACvC,CAAC,CAAC;UACF,MAAMrB,OAAO,GAAGmB,iBAAiB,CAACjC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK,EAAClC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,mBAAmB,CAAC;UACvF,IAAIrB,OAAO,EAAE;YACnBuB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAExB,OAAO,CAAC;YACrDA,OAAO,CAACkT,KAAK,CAAC,CAAC;YACf3J,UAAU,CAAC,CAAC;UACN,CAAC,MAAM;YACNhI,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;cACtEJ,KAAK,EAAElC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,KAAK;cAC7B4C,mBAAmB,EAAE9E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,mBAAmB;cACzDgH,SAAS,EAAErK;YACZ,CAAC,CAAC;UACI;QACF,CAAC,MAAM,IAAI2T,MAAM,CAACrB,YAAY,CAACC,MAAM,KAAK,UAAU,EAAE;UAC3DlG,cAAc,CAAC,CAAC;QACX,CAAC,MAAM,IAAIsH,MAAM,CAACrB,YAAY,CAACC,MAAM,KAAK,SAAS,IAAIoB,MAAM,CAACrB,YAAY,CAACC,MAAM,KAAK,SAAS,EAAE;UAAA,IAAA4C,OAAA;UACtG;UACA5R,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;UAE1C;UACAvD,mBAAmB,CAAC,CAAC,CAAC;UACtBU,cAAc,CAAC,CAAC,CAAC;;UAEjB;UACA,IAAI,CAAAwU,OAAA,GAAAlW,KAAK,CAAC,CAAC,CAAC,cAAAkW,OAAA,eAARA,OAAA,CAAUC,SAAS,IAAInW,KAAK,CAAC,CAAC,CAAC,CAACmW,SAAS,CAAC9R,IAAI,CAAC,CAAC,KAAKnB,MAAM,CAACkT,QAAQ,CAACC,IAAI,CAAChS,IAAI,CAAC,CAAC,EAAE;YACrF;YACAnB,MAAM,CAACkT,QAAQ,CAACC,IAAI,GAAGrW,KAAK,CAAC,CAAC,CAAC,CAACmW,SAAS;UAC1C,CAAC,MAAM;YAAA,IAAAG,QAAA;YACN;YACA,KAAAA,QAAA,GAAItW,KAAK,CAAC,CAAC,CAAC,cAAAsW,QAAA,eAARA,QAAA,CAAUnS,KAAK,EAAE;cAAA,IAAAoS,QAAA;cACpBjS,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;cAEhE;cACA,MAAMiS,YAAY,GAAGtS,iBAAiB,CAAClE,KAAK,CAAC,CAAC,CAAC,CAACmE,KAAK,EAAE,EAAAoS,QAAA,GAAAvW,KAAK,CAAC,CAAC,CAAC,cAAAuW,QAAA,uBAARA,QAAA,CAAUnS,mBAAmB,KAAI,EAAE,CAAC;cAC3F,IAAIoS,YAAY,EAAE;gBACjB,IAAI;kBACHA,YAAY,CAAC1J,cAAc,CAAC;oBAC3B/G,QAAQ,EAAE,QAAQ;oBAClBgH,KAAK,EAAE,QAAQ;oBACfC,MAAM,EAAE;kBACT,CAAC,CAAC;kBACF1I,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;gBACjD,CAAC,CAAC,OAAOU,KAAK,EAAE;kBACfX,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;kBAC/DrB,MAAM,CAAC4C,QAAQ,CAAC;oBAAExD,GAAG,EAAE,CAAC;oBAAEyD,QAAQ,EAAE;kBAAS,CAAC,CAAC;gBAChD;cACD,CAAC,MAAM;gBACNzB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;gBAC/DrB,MAAM,CAAC4C,QAAQ,CAAC;kBAAExD,GAAG,EAAE,CAAC;kBAAEyD,QAAQ,EAAE;gBAAS,CAAC,CAAC;cAChD;YACD,CAAC,MAAM;cACN;cACAzB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;cAC3DrB,MAAM,CAAC4C,QAAQ,CAAC;gBAAExD,GAAG,EAAE,CAAC;gBAAEyD,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChD;UACD;QACK,CAAC,MAAM,IAAI2O,MAAM,CAACrB,YAAY,CAACC,MAAM,KAAK,UAAU,IAAIoB,MAAM,CAACrB,YAAY,CAACoD,WAAW,KAAK,SAAS,EAAE;UAC5GvT,MAAM,CAACwT,IAAI,CAAChC,MAAM,CAACrB,YAAY,CAACsD,SAAS,EAAE,QAAQ,CAAC;QAC/C,CAAC,MAAM,IAAIjC,MAAM,CAACrB,YAAY,CAACC,MAAM,KAAK,UAAU,IAAIoB,MAAM,CAACrB,YAAY,CAACoD,WAAW,KAAK,UAAU,EAAE;UAC7GvT,MAAM,CAACkT,QAAQ,CAACC,IAAI,GAAG3B,MAAM,CAACrB,YAAY,CAACsD,SAAS;QAC/C,CAAC,MAAM;UACZ;QAAA;MAEF,CAAC;MACI,oBACJzZ,OAAA,CAACb,MAAM;QAENqX,OAAO,EAAC,WAAW;QACnB1C,EAAE,EAAE2D,WAAY;QAChBiC,OAAO,EAAE5I,WAAY;QAAAyC,QAAA,EAEbiE,MAAM,CAACmC;MAAU,GALpBxC,KAAK;QAAAtW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMG,CAAC;IAEb,CAAC,CAAC,GACL,IAAI;EACR,CAAC;EAEA,MAAM4Y,cAAc,gBAClB5Z,OAAA,CAAAE,SAAA;IAAAqT,QAAA,gBACEvT,OAAA;MAAKuM,KAAK,EAAE;QAAEoK,YAAY,EAAE,KAAK;QAAEE,OAAO,EAAE;MAAO,CAAE;MAAAtD,QAAA,EAClDxB,WAAW,iBACV/R,OAAA,CAACN,UAAU;QACToU,EAAE,EAAE;UACF2C,QAAQ,EAAE,UAAU;UACpBhV,SAAS,EAAE,iCAAiC;UAC5CoY,UAAU,EAAE,iBAAiB;UAC7BnY,MAAM,EAAE,gBAAgB;UACxBgB,MAAM,EAAE,KAAK;UACbpB,YAAY,EAAE,MAAM;UACpBF,OAAO,EAAE,gBAAgB;UACzB0Y,KAAK,EAAE,OAAO;UACd1U,GAAG,EAAE,OAAO;UACZoB,KAAK,EAAE,OAAO;UACrB2N,MAAM,EAAE9T,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEsB,UAAU,IAAI,CAAAtB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,UAAU,MAAK,KAAK,GAAG,IAAIoY,QAAQ,CAAC1Z,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG;QACvH,CAAE;QAAA4R,QAAA,eAGGvT,OAAA,CAACL,SAAS;UAACmU,EAAE,EAAE;YAAEkG,IAAI,EAAE,GAAG;YAAE3X,KAAK,EAAE;UAAO;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACThB,OAAA,CAACF,gBAAgB;MAEhBma,GAAG,EAAE3V,YAAa;MAClBiI,KAAK,EAAE;QAAE0H,SAAS,EAAE;MAAQ,CAAE;MAC9BnJ,OAAO,EAAE;QACRoP,eAAe,EAAE,CAAC9V,cAAc;QAChC+V,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,KAAK;QACvBC,WAAW,EAAE,IAAI;QACjBC,kBAAkB,EAAE,EAAE;QACtBC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE;MACtB,CAAE;MAAAjH,QAAA,eAEHvT,OAAA;QAAKia,GAAG,EAAElW,UAAW;QACrBwI,KAAK,EAAE;UACN;UACQQ,QAAQ,EAAE,QAAQ;UAClBzL,YAAY,EAAE,KAAK;UACnBF,OAAO,EAAEwU,UAAU,CAAC,CAAC;UACrBa,QAAQ,EAAE,UAAU;UACpB/T,MAAM,EAAE;UAChB;QAED,CAAE;QAAA6Q,QAAA,eACKvT,OAAA,CAACX,GAAG;UAAAkU,QAAA,GACP,CAACjT,cAAc,CAAC,CAAC,iBACVN,OAAA,CAACX,GAAG;YACFwX,OAAO,EAAC,MAAM;YACd4D,aAAa,EAAC,QAAQ;YACtB1D,UAAU,EAAExW,WAAW,CAAC,CAAC,GAAG,YAAY,GAAG,QAAS;YACpDuT,EAAE,EAAE;cACFjS,KAAK,EAAEtB,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;cAChDa,OAAO,EAAEb,WAAW,CAAC,CAAC,GAAG,GAAG,GAAGgE;YACvB,CAAE;YAAAgP,QAAA,EAEDrB,aAAa,CAAC;UAAC;YAAArR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EACA,CAAA+D,eAAe,aAAfA,eAAe,wBAAArB,uBAAA,GAAfqB,eAAe,CAAE4P,UAAU,cAAAjR,uBAAA,uBAA3BA,uBAAA,CAA6B0I,MAAM,IAAG,CAAC,iBACtCpM,OAAA,CAACX,GAAG;YACF4a,GAAG,EAAEjW,kBAAmB;YACxB6S,OAAO,EAAC,MAAM;YACd/C,EAAE,EAAE;cACF6C,YAAY,EAAE,QAAQ;cAChC;cACUK,GAAG,EAAE,KAAK;cACV9V,eAAe,GAAAyC,uBAAA,GAAEoB,eAAe,CAAC4P,UAAU,CAAC,CAAC,CAAC,cAAAhR,uBAAA,uBAA7BA,uBAAA,CAA+BxC,eAAe;cAC/DU,KAAK,EAAEvB,cAAc,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;cACzCyW,UAAU,EAAE;YACd,CAAE;YAAAxD,QAAA,EAED+D,aAAa,CAAC;UAAC;YAAAzW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAvDL,aAAaoD,cAAc,EAAE;MAAAvD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwDd,CAAC,EAClB0Q,cAAc,IAAI5O,KAAK,CAACsJ,MAAM,GAAC,CAAC,IAAI3H,gBAAgB,KAAK,SAAS,iBAAIzE,OAAA,CAACX,GAAG;MAAAkU,QAAA,EAAEgD,cAAc,CAAC;IAAC;MAAA1V,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAAE,GAAG;EAAA,eACzG,CACJ;;EAED;EACC,MAAM0Z,YAAY,GAAG;IACnBjE,QAAQ,EAAE,OAAgB;IAC1BrR,GAAG,EAAE,CAAC;IACNpD,IAAI,EAAE,CAAC;IACPH,KAAK,EAAE,OAAO;IACdgF,MAAM,EAAE,OAAO;IACf3F,eAAe,EAAE,aAAa;IAC9BsL,aAAa,EAAE,MAAM;IACrB9J,MAAM,EAAE;EACX,CAAC;EAEA,MAAMiY,kBAAkB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC3V,aAAa,EAAE,OAAO,IAAI;IAE/B,MAAMpB,IAAI,GAAGoB,aAAa,CAACc,qBAAqB,CAAC,CAAC;IAClD,MAAMI,cAAc,GAAGF,MAAM,CAACG,WAAW;IACzC,MAAMJ,aAAa,GAAGC,MAAM,CAACC,UAAU;IAErC,MAAM2U,QAAQ,GAAG;MACfxV,GAAG,EAAE;QACHqR,QAAQ,EAAE,OAAgB;QAC1BrR,GAAG,EAAE,CAAC;QACNpD,IAAI,EAAE,CAAC;QACPH,KAAK,EAAE,MAAM;QACbgF,MAAM,EAAE,GAAGjD,IAAI,CAACwB,GAAG,IAAI;QACvBlE,eAAe,EAAE,oBAAoB;QACrCsL,aAAa,EAAE;MACjB,CAAC;MACDtK,MAAM,EAAE;QACNuU,QAAQ,EAAE,OAAgB;QAC1BrR,GAAG,EAAE,GAAGxB,IAAI,CAAC1B,MAAM,IAAI;QACvBF,IAAI,EAAE,CAAC;QACPH,KAAK,EAAE,MAAM;QACbgF,MAAM,EAAE,GAAGX,cAAc,GAAGtC,IAAI,CAAC1B,MAAM,IAAI;QAC3ChB,eAAe,EAAE,oBAAoB;QACrCsL,aAAa,EAAE;MACjB,CAAC;MACDxK,IAAI,EAAE;QACJyU,QAAQ,EAAE,OAAgB;QAC1BrR,GAAG,EAAE,GAAGxB,IAAI,CAACwB,GAAG,IAAI;QACpBpD,IAAI,EAAE,CAAC;QACPH,KAAK,EAAE,GAAG+B,IAAI,CAAC5B,IAAI,IAAI;QACvB6E,MAAM,EAAE,GAAGjD,IAAI,CAACiD,MAAM,IAAI;QAC1B3F,eAAe,EAAE,oBAAoB;QACrCsL,aAAa,EAAE;MACjB,CAAC;MACDhG,KAAK,EAAE;QACLiQ,QAAQ,EAAE,OAAgB;QAC1BrR,GAAG,EAAE,GAAGxB,IAAI,CAACwB,GAAG,IAAI;QACpBpD,IAAI,EAAE,GAAG4B,IAAI,CAAC4C,KAAK,IAAI;QACvB3E,KAAK,EAAE,GAAGkE,aAAa,GAAGnC,IAAI,CAAC4C,KAAK,IAAI;QACxCK,MAAM,EAAE,GAAGjD,IAAI,CAACiD,MAAM,IAAI;QAC1B3F,eAAe,EAAE,oBAAoB;QACrCsL,aAAa,EAAE;MACjB;IACJ,CAAC;IAED,OAAOoO,QAAQ;EAChB,CAAC;EACA,MAAMC,iBAAiB,GAAG7V,aAAa,GACnC;IACEyR,QAAQ,EAAE,OAAgB;IAC1BrR,GAAG,EAAE,GAAGJ,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAACV,GAAG,IAAI;IACrDpD,IAAI,EAAE,GAAGgD,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAAC9D,IAAI,IAAI;IACvDH,KAAK,EAAE,GAAGmD,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAACjE,KAAK,IAAI;IACzDgF,MAAM,EAAE,GAAG7B,aAAa,CAACc,qBAAqB,CAAC,CAAC,CAACe,MAAM,IAAI;IAC/D;IACIvF,YAAY,EAAE,KAAK;IACnBkL,aAAa,EAAEH,gBAAgB,GAAG,MAAM,GAAG,MAAM;IACjD3J,MAAM,EAAE;EACV,CAAC,GACD,CAAC,CAAC;EAEN,oBACE1C,OAAA,CAAAE,SAAA;IAAAqT,QAAA,GACG,CAAAxO,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+H,OAAO,KAAI9H,aAAa,IAAIO,gBAAgB,IAAId,gBAAgB,KAAK,MAAM,iBAC3FzE,OAAA,CAACX,GAAG;MAACyU,EAAE,EAAE4G,YAAa;MAAAnH,QAAA,GACnBuH,MAAM,CAACC,OAAO,CAACJ,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAACrG,GAAG,CAAC,CAAC,CAAC0G,GAAG,EAAEzO,KAAK,CAAC,kBACjEvM,OAAA,CAACX,GAAG;QAEHyU,EAAE,EAAEvH;MAAM,GADLyO,GAAG;QAAAna,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CACI,CAAC,eAEFhB,OAAA,CAACX,GAAG;QAACyU,EAAE,EAAE+G;MAAkB;QAAAha,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACN,EACAgE,aAAa,IAAIO,gBAAgB,iBAEhCvF,OAAA,CAACG,kBAAkB;MACjBqZ,IAAI;MACJyB,KAAK,EAAErB,cAAe;MACtBzO,SAAS,EAAE9F,gBAAiB;MAC5BjD,KAAK;MACL8Y,WAAW,EAAE;QACXC,QAAQ,EAAEnW,aAAa;QACvBoW,SAAS,EAAE,CACT;UACEC,IAAI,EAAE,iBAAiB;UACvBvQ,OAAO,EAAE;YACPwQ,QAAQ,EAAEtV,MAAM;YAChBuV,OAAO,EAAE,IAAI;YACbna,OAAO,EAAE;UACX;QACF,CAAC,EACD;UACEia,IAAI,EAAE,eAAe;UACrBvQ,OAAO,EAAE;YACP;YACA0Q,QAAQ,EAAE,KAAK;YACf;YACAC,YAAY,EAAEA,CAAC;cAAEC,CAAC;cAAEC;YAA4B,CAAC,MAAM;cACrDD,CAAC,EAAEhV,IAAI,CAACuI,KAAK,CAACyM,CAAC,CAAC;cAChBC,CAAC,EAAEjV,IAAI,CAACuI,KAAK,CAAC0M,CAAC;YACjB,CAAC;UACH;QACF,CAAC;MAEL,CAAE;MACFtb,WAAW,EAAEA,WAAY;MACzBC,cAAc,EAAEA,cAAc,CAAC,CAAE;MACjCC,WAAW,EAAEA,WAAW,CAAC;MAC9B;MAAA;MAAAgT,QAAA,eAEKvT,OAAA,CAACX,GAAG;QACFyU,EAAE,EAAE;UACF2C,QAAQ,EAAE,UAAU;UACpBrR,GAAG,EAAEJ,aAAa,CAAC4W,SAAS;UAC5B5Z,IAAI,EAAEgD,aAAa,CAAC6W,UAAU;UAC9Bha,KAAK,EAAEmD,aAAa,CAAC8W,WAAW;UAChCjV,MAAM,EAAE7B,aAAa,CAAC+W;QACxB;MAAE;QAAAlb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACgB,CACrB;EAAA,eACD,CAAC;AAEP,CAAC;AAACoC,EAAA,CAvjDIP,YAAyC;EAAA,QAyB0DhD,cAAc;AAAA;AAAAmc,GAAA,GAzBjHnZ,YAAyC;AAyjD/C,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAoZ,GAAA;AAAAC,YAAA,CAAArZ,EAAA;AAAAqZ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}