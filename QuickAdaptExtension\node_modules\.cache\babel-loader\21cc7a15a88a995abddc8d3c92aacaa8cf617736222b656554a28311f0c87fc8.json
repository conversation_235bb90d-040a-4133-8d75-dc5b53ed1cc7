{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\ButtonSetting.tsx\",\n  _s = $RefreshSig$();\nimport { Box, Button, FormControl, IconButton, MenuItem, Popover, Select, TextField, ToggleButton, ToggleButtonGroup, Typography } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { useEffect, useState } from \"react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport React from \"react\";\nimport { ChromePicker } from \"react-color\";\nimport { GetGudeDetailsByGuideId } from \"../../../services/GuideListServices\";\nimport userSession from \"../../../store/userSession\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ButtonSetting = ({\n  settingAnchorEl,\n  handleCloseSettingPopup,\n  guideListByOrg,\n  loading,\n  handleApplyChanges,\n  buttonInfo,\n  updatedGuideData\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [currentButtonName, setCurrentButtonName] = useState(\"\");\n  const [currentSelectedColor, setCurrentSelectedColor] = useState({\n    color: \"\",\n    target: \"\"\n  });\n  const {\n    ButtonsDropdown,\n    setButtonsDropdown,\n    toolTipGuideMetaData,\n    updateTooltipButtonAction,\n    btnidss,\n    setBtnIdss,\n    currentStep,\n    setCurrentStep,\n    highlightedButton,\n    getCurrentButtonInfo,\n    interactionData,\n    createWithAI\n  } = useDrawerStore(state => state);\n  const [selectedTab, setSelectedTab] = useState(\"new-tab\");\n  const [selectedInteraction, setSelectedInteraction] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [selectedActions, setSelectActions] = useState(\"close\");\n  const [targetURL, setTargetURL] = useState(\"\");\n  const [targetURLError, setTargetURLError] = useState(\"\");\n  const {\n    setCurrentGuideId,\n    currentGuideId,\n    getCurrentGuideId\n  } = userSession(state => state);\n  const [tempColors, setTempColors] = useState({\n    backgroundColor: \"#5F9EA0\",\n    borderColor: \"#70afaf\",\n    color: \"#ffffff\"\n  });\n  useEffect(() => {\n    // Only fetch button info when we have valid container and button IDs\n    if (settingAnchorEl.containerId && settingAnchorEl.buttonId) {\n      const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\n      // Set all button properties from the result\n      if (result) {\n        // Set button name\n        if (result.title) {\n          setCurrentButtonName(result.title);\n        }\n        // Set button action\n        if (result.selectedActions) {\n          setSelectActions(result.selectedActions);\n        }\n        // Set target URL\n        if (result.targetURL) {\n          setTargetURL(result.targetURL);\n        }\n        // Set colors\n        setTempColors({\n          backgroundColor: result.bgColor || \"#5F9EA0\",\n          borderColor: result.borderColor || \"#70afaf\",\n          color: result.textColor || \"#ffffff\"\n        });\n      }\n    }\n  }, [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo]);\n  useEffect(() => {\n    const fetchGuideDetails = async () => {\n      if (currentGuideId && currentGuideId !== \"\") {\n        var _data$GuideDetails, _data$GuideDetails$Gu, _guideStep$Design, _guideStep$Design$Got;\n        const data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);\n        const guideStep = data === null || data === void 0 ? void 0 : (_data$GuideDetails = data.GuideDetails) === null || _data$GuideDetails === void 0 ? void 0 : (_data$GuideDetails$Gu = _data$GuideDetails.GuideStep) === null || _data$GuideDetails$Gu === void 0 ? void 0 : _data$GuideDetails$Gu[currentStep - 1];\n        const gotoNextButtonId = guideStep === null || guideStep === void 0 ? void 0 : (_guideStep$Design = guideStep.Design) === null || _guideStep$Design === void 0 ? void 0 : (_guideStep$Design$Got = _guideStep$Design.GotoNext) === null || _guideStep$Design$Got === void 0 ? void 0 : _guideStep$Design$Got.ButtonId;\n        if (btnidss === buttonInfo.id) {\n          var _guideStep$ButtonSect, _guideStep$ButtonSect2, _guideStep$ButtonSect3;\n          const matchingButton = guideStep === null || guideStep === void 0 ? void 0 : (_guideStep$ButtonSect = guideStep.ButtonSection) === null || _guideStep$ButtonSect === void 0 ? void 0 : (_guideStep$ButtonSect2 = _guideStep$ButtonSect[0]) === null || _guideStep$ButtonSect2 === void 0 ? void 0 : (_guideStep$ButtonSect3 = _guideStep$ButtonSect2.CustomButtons) === null || _guideStep$ButtonSect3 === void 0 ? void 0 : _guideStep$ButtonSect3.find(btn => btn.ButtonId === buttonInfo.id);\n          setSelectActions(btnidss === buttonInfo.id ? \"Next\" : \"close\");\n        } else if (buttonInfo) {\n          var _buttonInfo$actions;\n          setSelectActions(((_buttonInfo$actions = buttonInfo.actions) === null || _buttonInfo$actions === void 0 ? void 0 : _buttonInfo$actions.value) || \"close\");\n        }\n      }\n    };\n    fetchGuideDetails();\n    if (settingAnchorEl.value && buttonInfo) {\n      var _buttonInfo$actions2, _buttonInfo$actions3, _buttonInfo$actions4, _buttonInfo$actions5, _buttonInfo$actions6;\n      setCurrentButtonName(buttonInfo.name);\n      setSelectedInteraction(((_buttonInfo$actions2 = buttonInfo.actions) === null || _buttonInfo$actions2 === void 0 ? void 0 : _buttonInfo$actions2.interaction) || \"\");\n      setSelectActions(((_buttonInfo$actions3 = buttonInfo.actions) === null || _buttonInfo$actions3 === void 0 ? void 0 : _buttonInfo$actions3.value) || \"\");\n      setTargetURL(((_buttonInfo$actions4 = buttonInfo.actions) === null || _buttonInfo$actions4 === void 0 ? void 0 : _buttonInfo$actions4.targetURL) || ((_buttonInfo$actions5 = buttonInfo.actions) === null || _buttonInfo$actions5 === void 0 ? void 0 : _buttonInfo$actions5.targetUrl) || \"\");\n      setSelectedTab(((_buttonInfo$actions6 = buttonInfo.actions) === null || _buttonInfo$actions6 === void 0 ? void 0 : _buttonInfo$actions6.tab) || \"\");\n      setTempColors({\n        backgroundColor: buttonInfo.style.backgroundColor,\n        borderColor: buttonInfo.style.borderColor,\n        color: buttonInfo.style.color\n      });\n    }\n  }, [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep]);\n  const handleChangeActions = e => {\n    const v = e.target.value;\n    setSelectActions(v);\n    if (ButtonsDropdown === currentButtonName) {\n      const v = e.target.value;\n      setSelectActions(v);\n    }\n  };\n  useEffect(() => {}, [ButtonsDropdown]);\n  const handleChangeTabs = event => {\n    setSelectedTab(event.target.value);\n  };\n  const handleColorChange = (e, targetName) => {\n    const value = e.hex;\n    setTempColors(prev => ({\n      ...prev,\n      [currentSelectedColor.target]: currentSelectedColor.target === \"borderColor\" ? `2px solid ${value}` : value\n    }));\n    setCurrentSelectedColor(prevState => {\n      return {\n        ...prevState,\n        color: value\n      };\n    });\n  };\n  const validateTargetURL = url => {\n    if (selectedActions === \"open-url\") {\n      if (!url) {\n        return \"URL is required\";\n      }\n      try {\n        new URL(url);\n        return \"\";\n      } catch (error) {\n        return \"Invalid URL\";\n      }\n    }\n    return \"\";\n  };\n  const handleChanges = () => {\n    const targetURLError = validateTargetURL(targetURL);\n    setTargetURLError(targetURLError);\n    if (targetURLError) {\n      return;\n    }\n\n    // Retain the previously saved button name if the field is empty\n    let buttonNameToUpdate = currentButtonName;\n    // If the current button name is empty, try to get it from buttonInfo or getCurrentButtonInfo\n    if (!buttonNameToUpdate || !buttonNameToUpdate.trim()) {\n      if (buttonInfo && buttonInfo.name) {\n        buttonNameToUpdate = buttonInfo.name;\n      } else if (settingAnchorEl.containerId && settingAnchorEl.buttonId) {\n        const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\n        if (result !== null && result !== void 0 && result.title) {\n          buttonNameToUpdate = result.title;\n        }\n      }\n    }\n\n    // Apply the changes with the updated button name\n\n    handleApplyChanges(tempColors, selectedActions, targetURL, selectedInteraction, buttonNameToUpdate, selectedTab);\n  };\n  const handleURLChange = e => {\n    const newURL = e.target.value;\n    setTargetURL(newURL);\n  };\n  return /*#__PURE__*/_jsxDEV(Popover, {\n    id: \"btn-setting-toolbar\",\n    open: Boolean(settingAnchorEl.value),\n    anchorEl: settingAnchorEl.value,\n    onClose: () => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)\n    // slotProps={{\n    // \troot: {\n    // \t\t// instead of writing sx on popover write here it also target to root and more clear\n    // \t\tsx: {\n    // \t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\n    // \t\t},\n    // \t},\n    // }}\n    ,\n    slotProps: {\n      paper: {\n        sx: {\n          boxShadow: \"none !important\",\n          backgroundColor: \"transparent\"\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup qadpt-tltbtnprop\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate(\"Properties\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": translate(\"Close\"),\n            onClick: () => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId),\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            sx: {\n              marginBottom: \"16px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontSize: \"14px\",\n                fontWeight: \"bold\",\n                my: \"5px\"\n              },\n              children: translate(\"Button Name\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              value: currentButtonName,\n              size: \"small\",\n              sx: {\n                mb: \"5px\",\n                border: \"1px solid #ccc\",\n                borderRadius: \"4px\",\n                \"& .MuiOutlinedInput-root\": {\n                  height: \"35px\",\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none !important\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none !important\"\n                  }\n                },\n                \"& .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none !important\"\n                }\n              },\n              placeholder: translate(\"Button Name\"),\n              onChange: e => setCurrentButtonName(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontSize: \"14px\",\n                fontWeight: \"bold\",\n                mb: \"5px\"\n              },\n              children: translate(\"Button Action\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedActions,\n              onChange: handleChangeActions,\n              sx: {\n                mb: \"5px\",\n                border: \"1px solid #ccc\",\n                borderRadius: \"4px\",\n                textAlign: \"left\",\n                \"& .MuiSelect-select\": {\n                  padding: \"8px\"\n                },\n                \"& .MuiOutlinedInput-root\": {\n                  height: \"35px\",\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none !important\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none !important\"\n                  }\n                },\n                \"& .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none !important\"\n                }\n              },\n              MenuProps: {\n                slotProps: {\n                  // disablePortal: true,\n                  root: {\n                    disablePortal: true,\n                    disableEnforceFocus: true,\n                    // Prevents focus trapping\n                    disableAutoFocus: true,\n                    // Allows the input field to gain focus\n                    disableRestoreFocus: true,\n                    sx: {\n                      zIndex: theme => theme.zIndex.tooltip + 1200\n                    }\n                  }\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"close\",\n                children: translate(\"Close\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"open-url\",\n                children: translate(\"Open URL\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"previous\",\n                children: translate(\"Previous\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"Next\",\n                children: translate(\"Next\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"Restart\",\n                children: translate(\"Restart\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 8\n            }, this), selectedActions === \"open-url\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"bold\",\n                  my: \"5px\"\n                },\n                children: translate(\"Enter URL\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                value: targetURL,\n                size: \"small\",\n                placeholder: \"https://quixy.com\",\n                onChange: e => {\n                  const newURL = e.target.value;\n                  setTargetURL(newURL); // Update the `targetURL` state with the new value\n                  handleURLChange(e); // Update the selectedButton.targetURL with the new value\n                },\n                error: !!targetURLError,\n                helperText: targetURLError ? translate(targetURLError) : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n                value: selectedTab,\n                onChange: handleChangeTabs,\n                exclusive: true,\n                \"aria-label\": translate(\"open in tab\"),\n                sx: {\n                  gap: \"5px\",\n                  marginY: \"5px\",\n                  height: \"35px\"\n                },\n                children: [\"new-tab\", \"same-tab\"].map(tab => {\n                  return /*#__PURE__*/_jsxDEV(ToggleButton, {\n                    value: tab,\n                    \"aria-label\": \"new tab\",\n                    sx: {\n                      border: \"1px solid #7EA8A5\",\n                      textTransform: \"capitalize\",\n                      color: \"#000\",\n                      borderRadius: \"4px\",\n                      flex: 1,\n                      padding: \"0 !important\",\n                      \"&.Mui-selected\": {\n                        backgroundColor: \"var(--border-color)\",\n                        color: \"#000\",\n                        border: \"2px solid #7EA8A5\"\n                      },\n                      \"&:hover\": {\n                        backgroundColor: \"#f5f5f5\"\n                      },\n                      \"&:last-child\": {\n                        borderLeft: \"1px solid var(--primarycolor) !important\" // Remove left border for the last button\n                      }\n                    },\n                    children: tab\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 13\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            sx: {\n              borderRadius: \"5px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Background\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                backgroundColor: tempColors.backgroundColor,\n                width: \"20px\",\n                height: \"20px\",\n                borderRadius: \"50%\"\n              },\n              component: \"div\",\n              role: \"button\",\n              onClick: e => {\n                setColorPickerAnchorEl(e.currentTarget);\n                setCurrentSelectedColor({\n                  color: tempColors.backgroundColor,\n                  target: \"backgroundColor\"\n                });\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            sx: {\n              borderRadius: \"5px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Border\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                backgroundColor: tempColors.borderColor.split(\" \")[2],\n                width: \"20px\",\n                height: \"20px\",\n                borderRadius: \"50%\"\n              },\n              component: \"div\",\n              role: \"button\",\n              onClick: e => {\n                setColorPickerAnchorEl(e.currentTarget);\n                setCurrentSelectedColor({\n                  color: `2px solid ${tempColors.borderColor.split(\" \")[2]}`,\n                  target: \"borderColor\"\n                });\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            sx: {\n              borderRadius: \"5px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Text\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                backgroundColor: tempColors.color,\n                width: \"20px\",\n                height: \"20px\",\n                borderRadius: \"50%\"\n              },\n              component: \"div\",\n              role: \"button\",\n              onClick: e => {\n                setColorPickerAnchorEl(e.currentTarget);\n                setCurrentSelectedColor({\n                  color: tempColors.color,\n                  target: \"color\"\n                });\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleChanges,\n            className: \"qadpt-btn\",\n            children: translate(\"Apply\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: Boolean(colorPickerAnchorEl),\n      anchorEl: colorPickerAnchorEl,\n      onClose: () => {\n        setColorPickerAnchorEl(null);\n        setCurrentSelectedColor({\n          color: \"\",\n          target: \"\"\n        });\n      },\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      id: \"color-picker\",\n      slotProps: {\n        root: {\n          // instead of writing sx on popover write here it also target to root and more clear\n          sx: {\n            zIndex: theme => theme.zIndex.tooltip + 1000\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentSelectedColor.color,\n          onChange: e => handleColorChange(e, currentSelectedColor.target)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 3\n  }, this);\n};\n_s(ButtonSetting, \"rtp3YvdTIjP6jIf9zAEaBQ78et8=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ButtonSetting;\nexport default ButtonSetting;\nvar _c;\n$RefreshReg$(_c, \"ButtonSetting\");", "map": {"version": 3, "names": ["Box", "<PERSON><PERSON>", "FormControl", "IconButton", "MenuItem", "Popover", "Select", "TextField", "ToggleButton", "ToggleButtonGroup", "Typography", "CloseIcon", "useEffect", "useState", "useDrawerStore", "React", "ChromePicker", "GetGudeDetailsByGuideId", "userSession", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ButtonSetting", "settingAnchorEl", "handleCloseSettingPopup", "guideListByOrg", "loading", "handleApplyChanges", "buttonInfo", "updatedGuideData", "_s", "t", "translate", "currentButtonName", "setCurrentButtonName", "currentSelectedColor", "setCurrentSelectedColor", "color", "target", "ButtonsDropdown", "setButtonsDropdown", "toolTipGuideMetaData", "updateTooltipButtonAction", "btnidss", "setBtnIdss", "currentStep", "setCurrentStep", "highlighted<PERSON><PERSON><PERSON>", "getCurrentButtonInfo", "interactionData", "createWithAI", "state", "selectedTab", "setSelectedTab", "selectedInteraction", "setSelectedInteraction", "colorPickerAnchorEl", "setColorPickerAnchorEl", "selectedActions", "setSelectActions", "targetURL", "setTargetURL", "targetURLError", "setTargetURLError", "setCurrentGuideId", "currentGuideId", "getCurrentGuideId", "tempColors", "setTempColors", "backgroundColor", "borderColor", "containerId", "buttonId", "result", "title", "bgColor", "textColor", "fetchGuideDetails", "_data$GuideDetails", "_data$GuideDetails$Gu", "_guideStep$Design", "_guideStep$Design$Got", "data", "guideStep", "GuideDetails", "GuideStep", "gotoNextButtonId", "Design", "GotoNext", "ButtonId", "id", "_guideStep$ButtonSect", "_guideStep$ButtonSect2", "_guideStep$ButtonSect3", "matchingButton", "ButtonSection", "CustomButtons", "find", "btn", "_buttonInfo$actions", "actions", "value", "_buttonInfo$actions2", "_buttonInfo$actions3", "_buttonInfo$actions4", "_buttonInfo$actions5", "_buttonInfo$actions6", "name", "interaction", "targetUrl", "tab", "style", "handleChangeActions", "e", "v", "handleChangeTabs", "event", "handleColorChange", "targetName", "hex", "prev", "prevState", "validateTargetURL", "url", "URL", "error", "handleChanges", "buttonNameToUpdate", "trim", "handleURLChange", "newURL", "open", "Boolean", "anchorEl", "onClose", "slotProps", "paper", "sx", "boxShadow", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onClick", "fullWidth", "marginBottom", "fontSize", "fontWeight", "my", "mb", "border", "borderRadius", "height", "placeholder", "onChange", "textAlign", "padding", "MenuProps", "root", "disable<PERSON><PERSON><PERSON>", "disableEnforceFocus", "disableAutoFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zIndex", "theme", "tooltip", "helperText", "exclusive", "gap", "marginY", "map", "textTransform", "flex", "borderLeft", "width", "component", "role", "currentTarget", "split", "variant", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/ButtonSetting.tsx"], "sourcesContent": ["import {\r\n\tAutocomplete,\r\n\tBox,\r\n\tButton,\r\n\tFormControl,\r\n\tIconButton,\r\n\tMenuItem,\r\n\tPopover,\r\n\tSelect,\r\n\tSelectChangeEvent,\r\n\tTextField,\r\n\tToggleButton,\r\n\tToggleButtonGroup,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useEffect, useState } from \"react\";\r\nimport useDrawerStore, { TInteractionValue } from \"../../../store/drawerStore\";\r\nimport React from \"react\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { GetGudeDetailsByGuideId } from \"../../../services/GuideListServices\";\r\nimport userSession from \"../../../store/userSession\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ButtonSetting = ({\r\n\tsettingAnchorEl,\r\n\thandleCloseSettingPopup,\r\n\tguideListByOrg,\r\n\tloading,\r\n\thandleApplyChanges,\r\n\tbuttonInfo,\r\n\tupdatedGuideData,\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [currentButtonName, setCurrentButtonName] = useState(\"\");\r\n\tconst [currentSelectedColor, setCurrentSelectedColor] = useState({\r\n\t\tcolor: \"\",\r\n\t\ttarget: \"\",\r\n\t});\r\n\tconst {\r\n\t\tButtonsDropdown,\r\n\t\tsetButtonsDropdown,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tbtnidss,\r\n\t\tsetBtnIdss,\r\n\t\tcurrentStep,\r\n\t\tsetCurrentStep,\r\n\t\thighlightedButton,\r\n\t\tgetCurrentButtonInfo,\r\n\t\tinteractionData,\r\n\t\tcreateWithAI,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst [selectedTab, setSelectedTab] = useState(\"new-tab\");\r\n\tconst [selectedInteraction, setSelectedInteraction] = useState<string | null>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLDivElement | null>(null);\r\n\tconst [selectedActions, setSelectActions] = useState<TInteractionValue>(\"close\");\r\n\tconst [targetURL, setTargetURL] = useState(\"\");\r\n\tconst [targetURLError, setTargetURLError] = useState(\"\");\r\n\tconst { setCurrentGuideId, currentGuideId, getCurrentGuideId } = userSession((state: any) => state);\r\n\tconst [tempColors, setTempColors] = useState({\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tborderColor: \"#70afaf\",\r\n\t\tcolor: \"#ffffff\",\r\n\t});\r\n\r\n\tuseEffect(() => {\r\n\t\t// Only fetch button info when we have valid container and button IDs\r\n\t\tif (settingAnchorEl.containerId && settingAnchorEl.buttonId) {\r\n\t\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\t\t// Set all button properties from the result\r\n\t\t\tif (result) {\r\n\t\t\t\t// Set button name\r\n\t\t\t\tif (result.title) {\r\n\t\t\t\t\tsetCurrentButtonName(result.title);\r\n\t\t\t\t}\r\n\t\t\t\t// Set button action\r\n\t\t\t\tif (result.selectedActions) {\r\n\t\t\t\t\tsetSelectActions(result.selectedActions as TInteractionValue);\r\n\t\t\t\t}\r\n\t\t\t\t// Set target URL\r\n\t\t\t\tif (result.targetURL) {\r\n\t\t\t\t\tsetTargetURL(result.targetURL);\r\n\t\t\t\t}\r\n\t\t\t\t// Set colors\r\n\t\t\t\tsetTempColors({\r\n\t\t\t\t\tbackgroundColor: result.bgColor || \"#5F9EA0\",\r\n\t\t\t\t\tborderColor: result.borderColor || \"#70afaf\",\r\n\t\t\t\t\tcolor: result.textColor || \"#ffffff\",\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\tif (currentGuideId && currentGuideId !== \"\") {\r\n\t\t\t\tconst data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);\r\n\t\t\t\tconst guideStep = data?.GuideDetails?.GuideStep?.[currentStep - 1];\r\n\t\t\t\tconst gotoNextButtonId = guideStep?.Design?.GotoNext?.ButtonId;\r\n\r\n\t\t\t\tif (btnidss === buttonInfo.id) {\r\n\t\t\t\t\tconst matchingButton = guideStep?.ButtonSection?.[0]?.CustomButtons?.find(\r\n\t\t\t\t\t\t(btn: any) => btn.ButtonId === buttonInfo.id\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tsetSelectActions(btnidss === buttonInfo.id ? \"Next\" : \"close\");\r\n\t\t\t\t} else if (buttonInfo) {\r\n\t\t\t\t\tsetSelectActions(buttonInfo.actions?.value || \"close\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\tif (settingAnchorEl.value && buttonInfo) {\r\n\t\t\tsetCurrentButtonName(buttonInfo.name);\r\n\t\t\tsetSelectedInteraction(buttonInfo.actions?.interaction || \"\");\r\n\t\t\tsetSelectActions(buttonInfo.actions?.value || \"\");\r\n\t\t\tsetTargetURL(buttonInfo.actions?.targetURL || buttonInfo.actions?.targetUrl || \"\");\r\n\t\t\tsetSelectedTab(buttonInfo.actions?.tab || \"\");\r\n\t\t\tsetTempColors({\r\n\t\t\t\tbackgroundColor: buttonInfo.style.backgroundColor,\r\n\t\t\t\tborderColor: buttonInfo.style.borderColor,\r\n\t\t\t\tcolor: buttonInfo.style.color,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep]);\r\n\r\n\tconst handleChangeActions = (e: SelectChangeEvent) => {\r\n\t\tconst v = e.target.value;\r\n\t\tsetSelectActions(v as TInteractionValue);\r\n\t\tif (ButtonsDropdown === currentButtonName) {\r\n\t\t\tconst v = e.target.value;\r\n\t\t\tsetSelectActions(v as TInteractionValue);\r\n\t\t}\r\n\t};\r\n\r\n\tuseEffect(() => {}, [ButtonsDropdown]);\r\n\r\n\tconst handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSelectedTab((event.target as HTMLInputElement).value);\r\n\t};\r\n\tconst handleColorChange = (e: any, targetName: any) => {\r\n\t\tconst value = e.hex;\r\n\t\tsetTempColors((prev) => ({\r\n\t\t\t...prev,\r\n\t\t\t[currentSelectedColor.target]: currentSelectedColor.target === \"borderColor\" ? `2px solid ${value}` : value,\r\n\t\t}));\r\n\t\tsetCurrentSelectedColor((prevState) => {\r\n\t\t\treturn {\r\n\t\t\t\t...prevState,\r\n\t\t\t\tcolor: value,\r\n\t\t\t};\r\n\t\t});\r\n\t};\r\n\tconst validateTargetURL = (url: string) => {\r\n\t\tif (selectedActions === \"open-url\") {\r\n\t\t\tif (!url) {\r\n\t\t\t\treturn \"URL is required\";\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tnew URL(url);\r\n\t\t\t\treturn \"\";\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn \"Invalid URL\";\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn \"\";\r\n\t};\r\n\r\n\tconst handleChanges = () => {\r\n\t\tconst targetURLError = validateTargetURL(targetURL);\r\n\t\tsetTargetURLError(targetURLError);\r\n\r\n\t\tif (targetURLError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Retain the previously saved button name if the field is empty\r\n\t\tlet buttonNameToUpdate = currentButtonName;\r\n\t\t// If the current button name is empty, try to get it from buttonInfo or getCurrentButtonInfo\r\n\t\tif (!buttonNameToUpdate || !buttonNameToUpdate.trim()) {\r\n\t\t\tif (buttonInfo && buttonInfo.name) {\r\n\t\t\t\tbuttonNameToUpdate = buttonInfo.name;\r\n\t\t\t} else if (settingAnchorEl.containerId && settingAnchorEl.buttonId) {\r\n\t\t\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\t\t\tif (result?.title) {\r\n\t\t\t\t\tbuttonNameToUpdate = result.title;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Apply the changes with the updated button name\r\n\r\n\t\thandleApplyChanges(tempColors, selectedActions, targetURL, selectedInteraction, buttonNameToUpdate, selectedTab);\r\n\t};\r\n\tconst handleURLChange = (e: any) => {\r\n\t\tconst newURL = e.target.value;\r\n\t\tsetTargetURL(newURL);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<Popover\r\n\t\t\tid={\"btn-setting-toolbar\"}\r\n\t\t\topen={Boolean(settingAnchorEl.value)}\r\n\t\t\tanchorEl={settingAnchorEl.value}\r\n\t\t\tonClose={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t// slotProps={{\r\n\t\t\t// \troot: {\r\n\t\t\t// \t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t// \t\tsx: {\r\n\t\t\t// \t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t// \t\t},\r\n\t\t\t// \t},\r\n\t\t\t// }}\r\n\t\t\tslotProps={{\r\n\t\t\t\tpaper: {\r\n\t\t\t\t  sx: {\r\n\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\"\r\n\t\t\t\t  },\r\n\t\t\t\t},\r\n\t\t\t  }}\t  \r\n\t\t>\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup qadpt-tltbtnprop\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Properties\")}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label={translate(\"Close\")}\r\n\t\t\t\t\t\t\tonClick={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsx={{ marginBottom: \"16px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Button Name\")}</Typography>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={currentButtonName}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tplaceholder={translate(\"Button Name\")}\r\n\t\t\t\t\t\t\t\tonChange={(e) => setCurrentButtonName(e.target.value)}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>{translate(\"Button Action\")}</Typography>\r\n\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\tvalue={selectedActions}\r\n\t\t\t\t\t\t\t\tonChange={handleChangeActions}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": {\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\t\t\tslotProps: {\r\n\t\t\t\t\t\t\t\t\t\t// disablePortal: true,\r\n\t\t\t\t\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t\t\t\t\tdisablePortal: true,\r\n\t\t\t\t\t\t\t\t\t\t\tdisableEnforceFocus: true, // Prevents focus trapping\r\n\t\t\t\t\t\t\t\t\t\t\tdisableAutoFocus: true, // Allows the input field to gain focus\r\n\t\t\t\t\t\t\t\t\t\t\tdisableRestoreFocus: true,\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1200,\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"close\">{translate(\"Close\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"open-url\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"previous\">{translate(\"Previous\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Next\">{translate(\"Next\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Restart\">{translate(\"Restart\")}</MenuItem>\r\n\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t{selectedActions === \"open-url\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Enter URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\tvalue={targetURL}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"https://quixy.com\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconst newURL = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\t\tsetTargetURL(newURL); // Update the `targetURL` state with the new value\r\n\t\t\t\t\t\t\t\t\t\t\thandleURLChange(e); // Update the selectedButton.targetURL with the new value\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\terror={!!targetURLError}\r\n\t\t\t\t\t\t\t\t\t\thelperText={targetURLError ? translate(targetURLError) : \"\"}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t<ToggleButtonGroup\r\n\t\t\t\t\t\t\t\t\t\tvalue={selectedTab}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChangeTabs}\r\n\t\t\t\t\t\t\t\t\t\texclusive\r\n\t\t\t\t\t\t\t\t\t\taria-label={translate(\"open in tab\")}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmarginY: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"35px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{[\"new-tab\", \"same-tab\"].map((tab) => {\r\n\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ToggleButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"new tab\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-selected\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"2px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:last-child\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderLeft: \"1px solid var(--primarycolor) !important\", // Remove left border for the last button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ToggleButton>\r\n\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t</ToggleButtonGroup>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</FormControl>\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>Button Color</Typography> */}\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\")}</div>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: tempColors.backgroundColor,\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\tsetColorPickerAnchorEl(e.currentTarget);\r\n\t\t\t\t\t\t\t\t\tsetCurrentSelectedColor({\r\n\t\t\t\t\t\t\t\t\t\tcolor: tempColors.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\ttarget: \"backgroundColor\",\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\")}</div>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: tempColors.borderColor.split(\" \")[2],\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\tsetColorPickerAnchorEl(e.currentTarget);\r\n\t\t\t\t\t\t\t\t\tsetCurrentSelectedColor({\r\n\t\t\t\t\t\t\t\t\t\tcolor: `2px solid ${tempColors.borderColor.split(\" \")[2]}`,\r\n\t\t\t\t\t\t\t\t\t\ttarget: \"borderColor\",\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Text\")}</div>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: tempColors.color,\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\tsetColorPickerAnchorEl(e.currentTarget);\r\n\t\t\t\t\t\t\t\t\tsetCurrentSelectedColor({\r\n\t\t\t\t\t\t\t\t\t\tcolor: tempColors.color,\r\n\t\t\t\t\t\t\t\t\t\ttarget: \"color\",\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<Popover\r\n\t\t\t\topen={Boolean(colorPickerAnchorEl)}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={() => {\r\n\t\t\t\t\tsetColorPickerAnchorEl(null);\r\n\t\t\t\t\tsetCurrentSelectedColor({ color: \"\", target: \"\" });\r\n\t\t\t\t}}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"color-picker\"\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentSelectedColor.color}\r\n\t\t\t\t\t\tonChange={(e) => handleColorChange(e, currentSelectedColor.target)}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</Popover>\r\n\t);\r\n};\r\n\r\nexport default ButtonSetting;\r\n"], "mappings": ";;AAAA,SAECA,GAAG,EACHC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,MAAM,EAENC,SAAS,EACTC,YAAY,EACZC,iBAAiB,EACjBC,UAAU,QACJ,eAAe;AACtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,cAAc,MAA6B,4BAA4B;AAC9E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAqB,aAAa;AACvD,SAASC,uBAAuB,QAAQ,qCAAqC;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,aAAa,GAAGA,CAAC;EACtBC,eAAe;EACfC,uBAAuB;EACvBC,cAAc;EACdC,OAAO;EACPC,kBAAkB;EAClBC,UAAU;EACVC;AACI,CAAC,KAAK;EAAAC,EAAA;EACV,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGf,cAAc,CAAC,CAAC;EACzC,MAAM,CAACgB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACwB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzB,QAAQ,CAAC;IAChE0B,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM;IACLC,eAAe;IACfC,kBAAkB;IAClBC,oBAAoB;IACpBC,yBAAyB;IACzBC,OAAO;IACPC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,iBAAiB;IACjBC,oBAAoB;IACpBC,eAAe;IACfC;EACD,CAAC,GAAGtC,cAAc,CAAEuC,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,SAAS,CAAC;EACzD,MAAM,CAAC2C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5C,QAAQ,CAAgB,EAAE,CAAC;EACjF,MAAM,CAAC6C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9C,QAAQ,CAAwB,IAAI,CAAC;EAC3F,MAAM,CAAC+C,eAAe,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAoB,OAAO,CAAC;EAChF,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM;IAAEqD,iBAAiB;IAAEC,cAAc;IAAEC;EAAkB,CAAC,GAAGlD,WAAW,CAAEmC,KAAU,IAAKA,KAAK,CAAC;EACnG,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC;IAC5C0D,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE,SAAS;IACtBjC,KAAK,EAAE;EACR,CAAC,CAAC;EAEF3B,SAAS,CAAC,MAAM;IACf;IACA,IAAIa,eAAe,CAACgD,WAAW,IAAIhD,eAAe,CAACiD,QAAQ,EAAE;MAC5D,MAAMC,MAAM,GAAGzB,oBAAoB,CAACzB,eAAe,CAACgD,WAAW,EAAEhD,eAAe,CAACiD,QAAQ,CAAC;MAC1F;MACA,IAAIC,MAAM,EAAE;QACX;QACA,IAAIA,MAAM,CAACC,KAAK,EAAE;UACjBxC,oBAAoB,CAACuC,MAAM,CAACC,KAAK,CAAC;QACnC;QACA;QACA,IAAID,MAAM,CAACf,eAAe,EAAE;UAC3BC,gBAAgB,CAACc,MAAM,CAACf,eAAoC,CAAC;QAC9D;QACA;QACA,IAAIe,MAAM,CAACb,SAAS,EAAE;UACrBC,YAAY,CAACY,MAAM,CAACb,SAAS,CAAC;QAC/B;QACA;QACAQ,aAAa,CAAC;UACbC,eAAe,EAAEI,MAAM,CAACE,OAAO,IAAI,SAAS;UAC5CL,WAAW,EAAEG,MAAM,CAACH,WAAW,IAAI,SAAS;UAC5CjC,KAAK,EAAEoC,MAAM,CAACG,SAAS,IAAI;QAC5B,CAAC,CAAC;MACH;IACD;EACD,CAAC,EAAE,CAACrD,eAAe,CAACgD,WAAW,EAAEhD,eAAe,CAACiD,QAAQ,EAAExB,oBAAoB,CAAC,CAAC;EAEjFtC,SAAS,CAAC,MAAM;IACf,MAAMmE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAIZ,cAAc,IAAIA,cAAc,KAAK,EAAE,EAAE;QAAA,IAAAa,kBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;QAC5C,MAAMC,IAAI,GAAG,MAAMnE,uBAAuB,CAACkD,cAAc,EAAEf,YAAY,EAAED,eAAe,CAAC;QACzF,MAAMkC,SAAS,GAAGD,IAAI,aAAJA,IAAI,wBAAAJ,kBAAA,GAAJI,IAAI,CAAEE,YAAY,cAAAN,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBO,SAAS,cAAAN,qBAAA,uBAA7BA,qBAAA,CAAgClC,WAAW,GAAG,CAAC,CAAC;QAClE,MAAMyC,gBAAgB,GAAGH,SAAS,aAATA,SAAS,wBAAAH,iBAAA,GAATG,SAAS,CAAEI,MAAM,cAAAP,iBAAA,wBAAAC,qBAAA,GAAjBD,iBAAA,CAAmBQ,QAAQ,cAAAP,qBAAA,uBAA3BA,qBAAA,CAA6BQ,QAAQ;QAE9D,IAAI9C,OAAO,KAAKf,UAAU,CAAC8D,EAAE,EAAE;UAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAC9B,MAAMC,cAAc,GAAGX,SAAS,aAATA,SAAS,wBAAAQ,qBAAA,GAATR,SAAS,CAAEY,aAAa,cAAAJ,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BI,aAAa,cAAAH,sBAAA,uBAA5CA,sBAAA,CAA8CI,IAAI,CACvEC,GAAQ,IAAKA,GAAG,CAACT,QAAQ,KAAK7D,UAAU,CAAC8D,EAC3C,CAAC;UAED/B,gBAAgB,CAAChB,OAAO,KAAKf,UAAU,CAAC8D,EAAE,GAAG,MAAM,GAAG,OAAO,CAAC;QAC/D,CAAC,MAAM,IAAI9D,UAAU,EAAE;UAAA,IAAAuE,mBAAA;UACtBxC,gBAAgB,CAAC,EAAAwC,mBAAA,GAAAvE,UAAU,CAACwE,OAAO,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoBE,KAAK,KAAI,OAAO,CAAC;QACvD;MACD;IACD,CAAC;IAEDxB,iBAAiB,CAAC,CAAC;IAEnB,IAAItD,eAAe,CAAC8E,KAAK,IAAIzE,UAAU,EAAE;MAAA,IAAA0E,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;MACxCxE,oBAAoB,CAACN,UAAU,CAAC+E,IAAI,CAAC;MACrCpD,sBAAsB,CAAC,EAAA+C,oBAAA,GAAA1E,UAAU,CAACwE,OAAO,cAAAE,oBAAA,uBAAlBA,oBAAA,CAAoBM,WAAW,KAAI,EAAE,CAAC;MAC7DjD,gBAAgB,CAAC,EAAA4C,oBAAA,GAAA3E,UAAU,CAACwE,OAAO,cAAAG,oBAAA,uBAAlBA,oBAAA,CAAoBF,KAAK,KAAI,EAAE,CAAC;MACjDxC,YAAY,CAAC,EAAA2C,oBAAA,GAAA5E,UAAU,CAACwE,OAAO,cAAAI,oBAAA,uBAAlBA,oBAAA,CAAoB5C,SAAS,OAAA6C,oBAAA,GAAI7E,UAAU,CAACwE,OAAO,cAAAK,oBAAA,uBAAlBA,oBAAA,CAAoBI,SAAS,KAAI,EAAE,CAAC;MAClFxD,cAAc,CAAC,EAAAqD,oBAAA,GAAA9E,UAAU,CAACwE,OAAO,cAAAM,oBAAA,uBAAlBA,oBAAA,CAAoBI,GAAG,KAAI,EAAE,CAAC;MAC7C1C,aAAa,CAAC;QACbC,eAAe,EAAEzC,UAAU,CAACmF,KAAK,CAAC1C,eAAe;QACjDC,WAAW,EAAE1C,UAAU,CAACmF,KAAK,CAACzC,WAAW;QACzCjC,KAAK,EAAET,UAAU,CAACmF,KAAK,CAAC1E;MACzB,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACd,eAAe,CAAC8E,KAAK,EAAEzE,UAAU,EAAEL,eAAe,CAACgD,WAAW,EAAEhD,eAAe,CAACiD,QAAQ,EAAExB,oBAAoB,EAAEL,OAAO,EAAEsB,cAAc,EAAEpB,WAAW,CAAC,CAAC;EAE1J,MAAMmE,mBAAmB,GAAIC,CAAoB,IAAK;IACrD,MAAMC,CAAC,GAAGD,CAAC,CAAC3E,MAAM,CAAC+D,KAAK;IACxB1C,gBAAgB,CAACuD,CAAsB,CAAC;IACxC,IAAI3E,eAAe,KAAKN,iBAAiB,EAAE;MAC1C,MAAMiF,CAAC,GAAGD,CAAC,CAAC3E,MAAM,CAAC+D,KAAK;MACxB1C,gBAAgB,CAACuD,CAAsB,CAAC;IACzC;EACD,CAAC;EAEDxG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC6B,eAAe,CAAC,CAAC;EAEtC,MAAM4E,gBAAgB,GAAIC,KAAoC,IAAK;IAClE/D,cAAc,CAAE+D,KAAK,CAAC9E,MAAM,CAAsB+D,KAAK,CAAC;EACzD,CAAC;EACD,MAAMgB,iBAAiB,GAAGA,CAACJ,CAAM,EAAEK,UAAe,KAAK;IACtD,MAAMjB,KAAK,GAAGY,CAAC,CAACM,GAAG;IACnBnD,aAAa,CAAEoD,IAAI,KAAM;MACxB,GAAGA,IAAI;MACP,CAACrF,oBAAoB,CAACG,MAAM,GAAGH,oBAAoB,CAACG,MAAM,KAAK,aAAa,GAAG,aAAa+D,KAAK,EAAE,GAAGA;IACvG,CAAC,CAAC,CAAC;IACHjE,uBAAuB,CAAEqF,SAAS,IAAK;MACtC,OAAO;QACN,GAAGA,SAAS;QACZpF,KAAK,EAAEgE;MACR,CAAC;IACF,CAAC,CAAC;EACH,CAAC;EACD,MAAMqB,iBAAiB,GAAIC,GAAW,IAAK;IAC1C,IAAIjE,eAAe,KAAK,UAAU,EAAE;MACnC,IAAI,CAACiE,GAAG,EAAE;QACT,OAAO,iBAAiB;MACzB;MACA,IAAI;QACH,IAAIC,GAAG,CAACD,GAAG,CAAC;QACZ,OAAO,EAAE;MACV,CAAC,CAAC,OAAOE,KAAK,EAAE;QACf,OAAO,aAAa;MACrB;IACD;IACA,OAAO,EAAE;EACV,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC3B,MAAMhE,cAAc,GAAG4D,iBAAiB,CAAC9D,SAAS,CAAC;IACnDG,iBAAiB,CAACD,cAAc,CAAC;IAEjC,IAAIA,cAAc,EAAE;MACnB;IACD;;IAEA;IACA,IAAIiE,kBAAkB,GAAG9F,iBAAiB;IAC1C;IACA,IAAI,CAAC8F,kBAAkB,IAAI,CAACA,kBAAkB,CAACC,IAAI,CAAC,CAAC,EAAE;MACtD,IAAIpG,UAAU,IAAIA,UAAU,CAAC+E,IAAI,EAAE;QAClCoB,kBAAkB,GAAGnG,UAAU,CAAC+E,IAAI;MACrC,CAAC,MAAM,IAAIpF,eAAe,CAACgD,WAAW,IAAIhD,eAAe,CAACiD,QAAQ,EAAE;QACnE,MAAMC,MAAM,GAAGzB,oBAAoB,CAACzB,eAAe,CAACgD,WAAW,EAAEhD,eAAe,CAACiD,QAAQ,CAAC;QAC1F,IAAIC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEC,KAAK,EAAE;UAClBqD,kBAAkB,GAAGtD,MAAM,CAACC,KAAK;QAClC;MACD;IACD;;IAEA;;IAEA/C,kBAAkB,CAACwC,UAAU,EAAET,eAAe,EAAEE,SAAS,EAAEN,mBAAmB,EAAEyE,kBAAkB,EAAE3E,WAAW,CAAC;EACjH,CAAC;EACD,MAAM6E,eAAe,GAAIhB,CAAM,IAAK;IACnC,MAAMiB,MAAM,GAAGjB,CAAC,CAAC3E,MAAM,CAAC+D,KAAK;IAC7BxC,YAAY,CAACqE,MAAM,CAAC;EACrB,CAAC;EAED,oBACC/G,OAAA,CAAChB,OAAO;IACPuF,EAAE,EAAE,qBAAsB;IAC1ByC,IAAI,EAAEC,OAAO,CAAC7G,eAAe,CAAC8E,KAAK,CAAE;IACrCgC,QAAQ,EAAE9G,eAAe,CAAC8E,KAAM;IAChCiC,OAAO,EAAEA,CAAA,KAAM9G,uBAAuB,CAACD,eAAe,CAACgD,WAAW,EAAEhD,eAAe,CAACiD,QAAQ;IAC5F;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAA;IACA+D,SAAS,EAAE;MACVC,KAAK,EAAE;QACLC,EAAE,EAAE;UACJC,SAAS,EAAE,iBAAiB;UAC5BrE,eAAe,EAAE;QACjB;MACF;IACC,CAAE;IAAAsE,QAAA,gBAEJxH,OAAA;MACCuE,EAAE,EAAC,mBAAmB;MACtBkD,SAAS,EAAC,oCAAoC;MAAAD,QAAA,eAE9CxH,OAAA;QAAKyH,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC7BxH,OAAA;UAAKyH,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBACnCxH,OAAA;YAAKyH,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAE3G,SAAS,CAAC,YAAY;UAAC;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5D7H,OAAA,CAAClB,UAAU;YACVgJ,IAAI,EAAC,OAAO;YACZ,cAAYjH,SAAS,CAAC,OAAO,CAAE;YAC/BkH,OAAO,EAAEA,CAAA,KAAM1H,uBAAuB,CAACD,eAAe,CAACgD,WAAW,EAAEhD,eAAe,CAACiD,QAAQ,CAAE;YAAAmE,QAAA,eAE9FxH,OAAA,CAACV,SAAS;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACN7H,OAAA;UAAKyH,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC9BxH,OAAA,CAACnB,WAAW;YACXmJ,SAAS;YACTV,EAAE,EAAE;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAE7BxH,OAAA,CAACX,UAAU;cAACiI,EAAE,EAAE;gBAAEY,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAM,CAAE;cAAAZ,QAAA,EAAE3G,SAAS,CAAC,aAAa;YAAC;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC5G7H,OAAA,CAACd,SAAS;cACTgG,KAAK,EAAEpE,iBAAkB;cACzBgH,IAAI,EAAC,OAAO;cACZR,EAAE,EAAE;gBACHe,EAAE,EAAE,KAAK;gBACTC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBAE1B,0BAA0B,EAAE;kBAC1BC,MAAM,EAAE,MAAM;kBACd,0CAA0C,EAAE;oBAC1CF,MAAM,EAAE;kBACV,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,MAAM,EAAE;kBACV;gBACF,CAAC;gBACD,oCAAoC,EAAE;kBACpCA,MAAM,EAAE;gBACV;cAEI,CAAE;cACAG,WAAW,EAAE5H,SAAS,CAAC,aAAa,CAAE;cACtC6H,QAAQ,EAAG5C,CAAC,IAAK/E,oBAAoB,CAAC+E,CAAC,CAAC3E,MAAM,CAAC+D,KAAK;YAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACF7H,OAAA,CAACX,UAAU;cAACiI,EAAE,EAAE;gBAAEY,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEE,EAAE,EAAE;cAAM,CAAE;cAAAb,QAAA,EAAE3G,SAAS,CAAC,eAAe;YAAC;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC9G7H,OAAA,CAACf,MAAM;cACNiG,KAAK,EAAE3C,eAAgB;cACvBmG,QAAQ,EAAE7C,mBAAoB;cAC5ByB,EAAE,EAAE;gBACLe,EAAE,EAAE,KAAK;gBACTC,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBACnBI,SAAS,EAAE,MAAM;gBACjB,qBAAqB,EAAE;kBACxBC,OAAO,EAAE;gBACV,CAAC;gBAEN,0BAA0B,EAAE;kBAC1BJ,MAAM,EAAE,MAAM;kBACd,0CAA0C,EAAE;oBAC1CF,MAAM,EAAE;kBACV,CAAC;kBACD,gDAAgD,EAAE;oBAChDA,MAAM,EAAE;kBACV;gBACF,CAAC;gBACD,oCAAoC,EAAE;kBACpCA,MAAM,EAAE;gBACV;cAEI,CAAE;cACAO,SAAS,EAAE;gBACVzB,SAAS,EAAE;kBACV;kBACA0B,IAAI,EAAE;oBACLC,aAAa,EAAE,IAAI;oBACnBC,mBAAmB,EAAE,IAAI;oBAAE;oBAC3BC,gBAAgB,EAAE,IAAI;oBAAE;oBACxBC,mBAAmB,EAAE,IAAI;oBAEzB5B,EAAE,EAAE;sBACH6B,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,OAAO,GAAG;oBAC3C;kBACD;gBACD;cACD,CAAE;cAAA7B,QAAA,gBAEFxH,OAAA,CAACjB,QAAQ;gBAACmG,KAAK,EAAC,OAAO;gBAAAsC,QAAA,EAAE3G,SAAS,CAAC,OAAO;cAAC;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvD7H,OAAA,CAACjB,QAAQ;gBAACmG,KAAK,EAAC,UAAU;gBAAAsC,QAAA,EAAE3G,SAAS,CAAC,UAAU;cAAC;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7D7H,OAAA,CAACjB,QAAQ;gBAACmG,KAAK,EAAC,UAAU;gBAAAsC,QAAA,EAAE3G,SAAS,CAAC,UAAU;cAAC;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7D7H,OAAA,CAACjB,QAAQ;gBAACmG,KAAK,EAAC,MAAM;gBAAAsC,QAAA,EAAE3G,SAAS,CAAC,MAAM;cAAC;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrD7H,OAAA,CAACjB,QAAQ;gBAACmG,KAAK,EAAC,SAAS;gBAAAsC,QAAA,EAAE3G,SAAS,CAAC,SAAS;cAAC;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,EACRtF,eAAe,KAAK,UAAU,gBAC9BvC,OAAA,CAAAE,SAAA;cAAAsH,QAAA,gBACCxH,OAAA,CAACX,UAAU;gBAACiI,EAAE,EAAE;kBAAEY,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EAAE3G,SAAS,CAAC,WAAW;cAAC;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1G7H,OAAA,CAACd,SAAS;gBACTgG,KAAK,EAAEzC,SAAU;gBACjBqF,IAAI,EAAC,OAAO;gBACZW,WAAW,EAAC,mBAAmB;gBAC/BC,QAAQ,EAAG5C,CAAC,IAAK;kBAChB,MAAMiB,MAAM,GAAGjB,CAAC,CAAC3E,MAAM,CAAC+D,KAAK;kBAC7BxC,YAAY,CAACqE,MAAM,CAAC,CAAC,CAAC;kBACtBD,eAAe,CAAChB,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAE;gBACFY,KAAK,EAAE,CAAC,CAAC/D,cAAe;gBACxB2G,UAAU,EAAE3G,cAAc,GAAG9B,SAAS,CAAC8B,cAAc,CAAC,GAAG;cAAG;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eAEF7H,OAAA,CAACZ,iBAAiB;gBACjB8F,KAAK,EAAEjD,WAAY;gBACnByG,QAAQ,EAAE1C,gBAAiB;gBAC3BuD,SAAS;gBACT,cAAY1I,SAAS,CAAC,aAAa,CAAE;gBACrCyG,EAAE,EAAE;kBACHkC,GAAG,EAAE,KAAK;kBACVC,OAAO,EAAE,KAAK;kBACdjB,MAAM,EAAE;gBACT,CAAE;gBAAAhB,QAAA,EAED,CAAC,SAAS,EAAE,UAAU,CAAC,CAACkC,GAAG,CAAE/D,GAAG,IAAK;kBACrC,oBACC3F,OAAA,CAACb,YAAY;oBACZ+F,KAAK,EAAES,GAAI;oBACX,cAAW,SAAS;oBACpB2B,EAAE,EAAE;sBACHgB,MAAM,EAAE,mBAAmB;sBAC3BqB,aAAa,EAAE,YAAY;sBAC3BzI,KAAK,EAAE,MAAM;sBACbqH,YAAY,EAAE,KAAK;sBACnBqB,IAAI,EAAE,CAAC;sBACPhB,OAAO,EAAE,cAAc;sBAEvB,gBAAgB,EAAE;wBACjB1F,eAAe,EAAE,qBAAqB;wBACtChC,KAAK,EAAE,MAAM;wBACboH,MAAM,EAAE;sBACT,CAAC;sBACD,SAAS,EAAE;wBACVpF,eAAe,EAAE;sBAClB,CAAC;sBACD,cAAc,EAAE;wBACf2G,UAAU,EAAE,0CAA0C,CAAE;sBACzD;oBACD,CAAE;oBAAArC,QAAA,EAED7B;kBAAG;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAEjB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACgB,CAAC;YAAA,eACnB,CAAC,GACA,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAId7H,OAAA,CAACrB,GAAG;YACH8I,SAAS,EAAC,mBAAmB;YAC7BH,EAAE,EAAE;cAAEiB,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,gBAE5BxH,OAAA;cAAKyH,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAAE3G,SAAS,CAAC,YAAY;YAAC;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpE7H,OAAA,CAACrB,GAAG;cACH2I,EAAE,EAAE;gBACHpE,eAAe,EAAEF,UAAU,CAACE,eAAe;gBAC3C4G,KAAK,EAAE,MAAM;gBACbtB,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE;cACf,CAAE;cACFwB,SAAS,EAAE,KAAM;cACjBC,IAAI,EAAC,QAAQ;cACbjC,OAAO,EAAGjC,CAAC,IAAK;gBACfxD,sBAAsB,CAACwD,CAAC,CAACmE,aAAa,CAAC;gBACvChJ,uBAAuB,CAAC;kBACvBC,KAAK,EAAE8B,UAAU,CAACE,eAAe;kBACjC/B,MAAM,EAAE;gBACT,CAAC,CAAC;cACH;YAAE;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN7H,OAAA,CAACrB,GAAG;YACH8I,SAAS,EAAC,mBAAmB;YAC7BH,EAAE,EAAE;cAAEiB,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,gBAE5BxH,OAAA;cAAKyH,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAAE3G,SAAS,CAAC,QAAQ;YAAC;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChE7H,OAAA,CAACrB,GAAG;cACH2I,EAAE,EAAE;gBACHpE,eAAe,EAAEF,UAAU,CAACG,WAAW,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrDJ,KAAK,EAAE,MAAM;gBACbtB,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE;cACf,CAAE;cACFwB,SAAS,EAAE,KAAM;cACjBC,IAAI,EAAC,QAAQ;cACbjC,OAAO,EAAGjC,CAAC,IAAK;gBACfxD,sBAAsB,CAACwD,CAAC,CAACmE,aAAa,CAAC;gBACvChJ,uBAAuB,CAAC;kBACvBC,KAAK,EAAE,aAAa8B,UAAU,CAACG,WAAW,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;kBAC1D/I,MAAM,EAAE;gBACT,CAAC,CAAC;cACH;YAAE;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN7H,OAAA,CAACrB,GAAG;YACH8I,SAAS,EAAC,mBAAmB;YAC7BH,EAAE,EAAE;cAAEiB,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,gBAE5BxH,OAAA;cAAKyH,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAAE3G,SAAS,CAAC,MAAM;YAAC;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9D7H,OAAA,CAACrB,GAAG;cACH2I,EAAE,EAAE;gBACHpE,eAAe,EAAEF,UAAU,CAAC9B,KAAK;gBACjC4I,KAAK,EAAE,MAAM;gBACbtB,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE;cACf,CAAE;cACFwB,SAAS,EAAE,KAAM;cACjBC,IAAI,EAAC,QAAQ;cACbjC,OAAO,EAAGjC,CAAC,IAAK;gBACfxD,sBAAsB,CAACwD,CAAC,CAACmE,aAAa,CAAC;gBACvChJ,uBAAuB,CAAC;kBACvBC,KAAK,EAAE8B,UAAU,CAAC9B,KAAK;kBACvBC,MAAM,EAAE;gBACT,CAAC,CAAC;cACH;YAAE;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN7H,OAAA;UAAKyH,SAAS,EAAC,oBAAoB;UAAAD,QAAA,eAClCxH,OAAA,CAACpB,MAAM;YACNuL,OAAO,EAAC,WAAW;YACnBpC,OAAO,EAAEpB,aAAc;YACvBc,SAAS,EAAC,WAAW;YAAAD,QAAA,EAEpB3G,SAAS,CAAC,OAAO;UAAC;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACN7H,OAAA,CAAChB,OAAO;MACPgI,IAAI,EAAEC,OAAO,CAAC5E,mBAAmB,CAAE;MACnC6E,QAAQ,EAAE7E,mBAAoB;MAC9B8E,OAAO,EAAEA,CAAA,KAAM;QACd7E,sBAAsB,CAAC,IAAI,CAAC;QAC5BrB,uBAAuB,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAG,CAAC,CAAC;MACnD,CAAE;MACFiJ,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACF/F,EAAE,EAAC,cAAc;MACjB6C,SAAS,EAAE;QACV0B,IAAI,EAAE;UACL;UACAxB,EAAE,EAAE;YACH6B,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,OAAO,GAAG;UAC3C;QACD;MACD,CAAE;MAAA7B,QAAA,eAEFxH,OAAA,CAACrB,GAAG;QAAA6I,QAAA,gBACHxH,OAAA,CAACL,YAAY;UACZuB,KAAK,EAAEF,oBAAoB,CAACE,KAAM;UAClCwH,QAAQ,EAAG5C,CAAC,IAAKI,iBAAiB,CAACJ,CAAC,EAAE9E,oBAAoB,CAACG,MAAM;QAAE;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACF7H,OAAA;UAAAwH,QAAA,EACE;AACP;AACA;AACA;AACA;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;AAAClH,EAAA,CAreIR,aAAa;EAAA,QASOL,cAAc,EAmBnCL,cAAc;AAAA;AAAA+K,EAAA,GA5BbrK,aAAa;AAuenB,eAAeA,aAAa;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}