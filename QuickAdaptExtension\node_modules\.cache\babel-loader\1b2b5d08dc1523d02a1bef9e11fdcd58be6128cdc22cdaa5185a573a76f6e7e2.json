{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\ElementsSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Typography, IconButton, Grid, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Elementssettings = ({\n  setShowElementsSettings,\n  setDesignPopup,\n  setMenuPopup,\n  resetHeightofBanner\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setDismissData,\n    dismissData,\n    selectedOption,\n    setSelectedOption,\n    selectedTemplate,\n    setTooltipElementOptions,\n    toolTipGuideMetaData,\n    currentStep,\n    updateprogressclick,\n    progress,\n    setProgress,\n    dismiss,\n    setDismiss,\n    selectedTemplateTour,\n    setIsUnSavedChanges,\n    ProgressColor,\n    setProgressColor,\n    updateDesignelementInTooltip,\n    Bposition\n  } = useDrawerStore(state => state);\n  const [tempDismiss, setTempDismiss] = useState(dismiss); // Temporary state\n  const [colorChange, setColorChange] = useState(ProgressColor);\n  const [isOpen, setIsOpen] = useState(true);\n  const [displayType, setDisplayType] = useState(\"Cross Icon\");\n  const [dontShowAgain, setDontShowAgain] = useState(true);\n  const [colors, setColors] = useState(\"#ff0000\");\n  // Initialize local state with values from global state\n  const [elementSettings, setElementsSettings] = useState({\n    isProgress: progress,\n    progressSelectedOption: Number(selectedOption) || 1 // Default to 1 if not set\n  });\n\n  // Add useEffect to synchronize local state with global state\n  useEffect(() => {\n    // Update local state when global state changes\n    setElementsSettings({\n      isProgress: progress,\n      progressSelectedOption: Number(selectedOption) || 1\n    });\n    setTempDismiss(dismiss);\n    setColorChange(ProgressColor);\n  }, [progress, selectedOption, dismiss, ProgressColor]);\n\n  // Update handlers for each dismiss option\n  const handleOptionSelect = option => {\n    // Update only the local state\n    const numOption = Number(option);\n    setElementsSettings(eleSettings => {\n      return {\n        ...eleSettings,\n        progressSelectedOption: numOption\n      };\n    });\n\n    // Don't update global state until Apply is clicked\n  };\n  const handleDisplayTypeChange = value => {\n    setDisplayType(value);\n  };\n  const handleBorderColorChange = e => {\n    const color = e.target.value;\n    setColors(color);\n  };\n  const handleDontShowAgainChange = (event, checked) => {\n    setDontShowAgain(checked);\n  };\n  const handleApplyChanges = () => {\n    // Store the previous progress state for comparison\n    const previousProgressState = progress;\n    const hasProgressChanged = previousProgressState !== elementSettings.isProgress;\n    // Create a complete element settings object\n    const updatedElement = {\n      progress: elementSettings.isProgress,\n      progressSelectedOption: elementSettings.progressSelectedOption,\n      dismiss: tempDismiss,\n      progressColor: colorChange\n    };\n\n    // Create a batch update function to record a single history entry\n    const batchUpdate = useDrawerStore.getState().batchUpdate;\n    const drawerStore = useDrawerStore.getState();\n\n    // First update the progress state in the store\n    // This ensures resetHeightofBanner will use the correct value\n    if (hasProgressChanged) {\n      // console.log(\"Setting progress state in store before batch update:\", elementSettings.isProgress);\n      drawerStore.setProgress(elementSettings.isProgress);\n    }\n\n    // Always update dismissData when Apply is clicked, regardless of whether it changed\n    // This ensures the dismiss icon state is updated only when Apply is clicked\n    setDismissData({\n      ...(dismissData || {\n        Actions: \"\",\n        DisplayType: \"Cross Icon\",\n        Color: \"#000000\",\n        DontShowAgain: true\n      }),\n      dismisssel: tempDismiss\n    });\n\n    // Use the batch update function to record a single history entry\n    batchUpdate(() => {\n      // Apply all changes at once\n      updateDesignelementInTooltip(updatedElement);\n\n      // Also update the individual state values to ensure everything is in sync\n      drawerStore.setProgress(elementSettings.isProgress);\n      drawerStore.setSelectedOption(elementSettings.progressSelectedOption);\n      drawerStore.setDismiss(tempDismiss);\n      drawerStore.setProgressColor(colorChange);\n\n      // For AI-created tours, immediately apply global progress bar synchronization\n      // This ensures all steps are updated when Apply is clicked\n      if (drawerStore.createWithAI && drawerStore.selectedTemplate === \"Tour\") {\n        var _drawerStore$interact, _drawerStore$interact2;\n        console.log(\"🔄 handleApplyChanges: Applying global progress bar synchronization for AI tour\", {\n          isProgress: elementSettings.isProgress,\n          progressSelectedOption: elementSettings.progressSelectedOption,\n          dismiss: tempDismiss,\n          progressColor: colorChange,\n          currentStep: drawerStore.currentStep,\n          totalSteps: ((_drawerStore$interact = drawerStore.interactionData) === null || _drawerStore$interact === void 0 ? void 0 : (_drawerStore$interact2 = _drawerStore$interact.GuideStep) === null || _drawerStore$interact2 === void 0 ? void 0 : _drawerStore$interact2.length) || 0\n        });\n\n        // Apply the changes to all steps immediately\n        drawerStore.syncGlobalProgressBarStateForAITour();\n      }\n    }, 'ELEMENT_BATCH_UPDATE', `Updated element settings`);\n    // When progress setting changes are applied, update the banner height if needed\n    if (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" && Bposition === \"Push Down\" && resetHeightofBanner) {\n      // Always update the height when Apply is clicked, regardless of whether progress changed\n      // This ensures consistent behavior and fixes any potential state inconsistencies\n      // console.log(\"Applying banner height changes. Progress state:\", elementSettings.isProgress);\n\n      // Get the current padding and border values from the store\n      const currentPadding = drawerStore.bpadding || \"12\";\n      const currentBorder = drawerStore.BborderSize || \"2\";\n\n      // Force a complete recalculation of the banner height\n      // The isFromApply=true parameter ensures all dimensions are included in the calculation\n      resetHeightofBanner(\"Push Down\", parseInt(currentPadding), parseInt(currentBorder), true,\n      // isFromApply\n      0,\n      // oldPadding\n      55 // top\n      );\n    }\n\n    // Update the UI state\n    setIsOpen(false);\n    setShowElementsSettings(false);\n    setDesignPopup(true);\n    setMenuPopup(true);\n    setIsUnSavedChanges(true);\n  };\n  useEffect(() => {\n    if (dismissData !== null && dismissData !== void 0 && dismissData.dismisssel) {\n      setDismiss(true);\n      setColors(dismissData.Color);\n    }\n  }, [dismissData === null || dismissData === void 0 ? void 0 : dismissData.dismisssel]);\n\n  // Early return if popup is closed\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"close\",\n          onClick: () => {\n            setIsOpen(false);\n            setShowElementsSettings(false);\n            setMenuPopup(true);\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate(\"Elements\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: () => {\n            setIsOpen(false);\n            setShowElementsSettings(false);\n            setDesignPopup(true);\n            setMenuPopup(true);\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-controls\"\n        //style={{ opacity: \"0.5\", height: \"60px\" }}\n        ,\n        children: [selectedTemplate !== \"Hotspot\" && selectedTemplate !== \"Banner\" && /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\"\n            //sx={{ opacity: \"0.5\" }}\n            ,\n            children: translate(\"Progress\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"toggle-switch\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: elementSettings.isProgress,\n                onChange: e => {\n                  const newValue = e.target.checked;\n                  // Update only local state\n                  setElementsSettings(eleSettings => {\n                    return {\n                      ...eleSettings,\n                      isProgress: newValue\n                    };\n                  });\n\n                  // Don't update global state until Apply is clicked\n                },\n                className: \"qadpt-progress-switch\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 5\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"slider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 5\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 7\n        }, this), elementSettings.isProgress && selectedTemplate !== \"Hotspot\" && selectedTemplate !== \"Banner\" && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            backgroundColor: \"#EAE2E2\",\n            borderRadius: \"8px\",\n            padding: \"10px\",\n            marginBottom: \"5px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"5px 10px\",\n                  borderRadius: \"6px\",\n                  cursor: \"pointer\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"qadpt-control-label\",\n                  children: translate(\"Progress Color\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: colorChange,\n                  onChange: e => {\n                    const newColor = e.target.value;\n                    // Update only local state\n                    setColorChange(newColor);\n                    // Don't update global state until Apply is clicked\n                  },\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 7\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                onClick: () => handleOptionSelect(1),\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"5px 10px\",\n                  backgroundColor: Number(elementSettings.progressSelectedOption) === 1 ? \"#5f9ea01a\" : \"#e5dada\",\n                  borderRadius: \"6px\",\n                  cursor: \"pointer\",\n                  border: Number(elementSettings.progressSelectedOption) === 1 ? \"1px solid var(--primarycolor)\" : \"transparent\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"var(--primarycolor)\"\n                  },\n                  children: translate(\"Dots\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    gap: \"4px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"8px\",\n                      height: \"8px\",\n                      backgroundColor: \"var(--primarycolor)\",\n                      borderRadius: \"50%\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"8px\",\n                      height: \"8px\",\n                      backgroundColor: \"var(--primarycolor)\",\n                      borderRadius: \"50%\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"8px\",\n                      height: \"8px\",\n                      backgroundColor: \"var(--primarycolor)\",\n                      borderRadius: \"50%\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sx: {\n                paddingTop: \"6px !important\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                onClick: () => handleOptionSelect(2),\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"5px 10px\",\n                  backgroundColor: Number(elementSettings.progressSelectedOption) === 2 ? \"#5f9ea01a\" : \"#e5dada\",\n                  borderRadius: \"6px\",\n                  cursor: \"pointer\",\n                  border: Number(elementSettings.progressSelectedOption) === 2 ? \"1px solid var(--primarycolor)\" : \"transparent\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"var(--primarycolor)\"\n                  },\n                  children: translate(\"Linear Progress\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: \"40px\",\n                    height: \"4px\",\n                    backgroundColor: \"#5f96a0\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sx: {\n                paddingTop: \"6px !important\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                onClick: () => handleOptionSelect(3),\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"5px 10px\",\n                  backgroundColor: Number(elementSettings.progressSelectedOption) === 3 ? \"#5f9ea01a\" : \"#e5dada\",\n                  borderRadius: \"6px\",\n                  cursor: \"pointer\",\n                  border: Number(elementSettings.progressSelectedOption) === 3 ? \"1px solid var(--primarycolor)\" : \"transparent\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"var(--primarycolor)\"\n                  },\n                  children: translate(\"Bread Crumbs\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    gap: \"4px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"14px\",\n                      height: \"4px\",\n                      backgroundColor: \"var(--primarycolor)\",\n                      borderRadius: \"100px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 10\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"14px\",\n                      height: \"4px\",\n                      backgroundColor: \"var(--primarycolor)\",\n                      borderRadius: \"100px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 10\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"14px\",\n                      height: \"4px\",\n                      backgroundColor: \"var(--primarycolor)\",\n                      borderRadius: \"100px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 10\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sx: {\n                paddingTop: \"6px !important\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                onClick: () => handleOptionSelect(4),\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"5px 10px\",\n                  backgroundColor: Number(elementSettings.progressSelectedOption) === 4 ? \"#5f9ea01a\" : \"#e5dada\",\n                  borderRadius: \"6px\",\n                  cursor: \"pointer\",\n                  border: Number(elementSettings.progressSelectedOption) === 4 ? \"1px solid var(--primarycolor)\" : \"transparent\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"var(--primarycolor)\"\n                  },\n                  children: translate(\"Numbers\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"var(--primarycolor)\"\n                  },\n                  children: translate(\"1 of 4\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sx: {\n                paddingTop: \"6px !important\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Box\n              //onClick={() => handleOptionSelect(4)}\n              , {\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  padding: \"5px 10px\",\n                  backgroundColor: Number(elementSettings.progressSelectedOption) === 5 ? \"#5f9ea01a\" : \"#e5dada\",\n                  borderRadius: \"6px\",\n                  cursor: \"pointer\",\n                  border: Number(elementSettings.progressSelectedOption) === 5 ? \"1px solid var(--primarycolor)\" : \"transparent\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"var(--primarycolor)\"\n                  },\n                  children: translate(\"CheckList\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-control-label\",\n            children: translate(\"Dismiss\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"toggle-switch\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: tempDismiss,\n                onChange: e => setTempDismiss(e.target.checked),\n                name: \"tempDismiss\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 5\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"slider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 5\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 6\n        }, this), dismiss && /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn`,\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 3\n  }, this);\n};\n_s(Elementssettings, \"nYyhdQSGar/6NcSRJVSd3D1OuHI=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = Elementssettings;\nexport default Elementssettings;\nvar _c;\n$RefreshReg$(_c, \"Elementssettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "IconButton", "Grid", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "useTranslation", "jsxDEV", "_jsxDEV", "Elementssettings", "setShowElementsSettings", "setDesignPopup", "setMenuPopup", "resetHeightofBanner", "_s", "t", "translate", "setDismissData", "dismissData", "selectedOption", "setSelectedOption", "selectedTemplate", "setTooltipElementOptions", "toolTipGuideMetaData", "currentStep", "updateprogressclick", "progress", "setProgress", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "selectedTemplateTour", "setIsUnSavedChanges", "ProgressColor", "setProgressColor", "updateDesignelementInTooltip", "Bposition", "state", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "colorChange", "setColorChange", "isOpen", "setIsOpen", "displayType", "setDisplayType", "dontShowAgain", "setDontShowAgain", "colors", "setColors", "elementSettings", "setElementsSettings", "isProgress", "progressSelectedOption", "Number", "handleOptionSelect", "option", "numOption", "eleSettings", "handleDisplayTypeChange", "value", "handleBorderColorChange", "e", "color", "target", "handleDontShowAgainChange", "event", "checked", "handleApplyChanges", "previousProgressState", "hasProgressChanged", "updatedElement", "progressColor", "batchUpdate", "getState", "drawerStore", "Actions", "DisplayType", "Color", "DontShowAgain", "dismisssel", "createWithAI", "_drawerStore$interact", "_drawerStore$interact2", "console", "log", "totalSteps", "interactionData", "GuideStep", "length", "syncGlobalProgressBarStateForAITour", "currentPadding", "bpadding", "currentBorder", "BborderSize", "parseInt", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "type", "onChange", "newValue", "sx", "backgroundColor", "borderRadius", "padding", "marginBottom", "container", "spacing", "item", "xs", "display", "justifyContent", "alignItems", "cursor", "newColor", "border", "style", "gap", "width", "height", "paddingTop", "name", "variant", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/ElementsSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n\tBox,\r\n\tTypography,\r\n\tIconButton,\r\n\tSwitch,\r\n\tGrid,\r\n\tButton,\r\n\tSelect,\r\n\tMenuItem,\r\n\tFormControl,\r\n\tTooltip,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface ElementsSettingsProps {\r\n\tonDismissDataChange: (data: {\r\n\t\tActions: string | number | null;\r\n\t\tDisplayType: string;\r\n\t\tColor: string;\r\n\t\tDontShowAgain: boolean;\r\n\t\tdismisssel: boolean;\r\n\t}) => void;\r\n}\r\nconst Elementssettings = ({ setShowElementsSettings, setDesignPopup, setMenuPopup, resetHeightofBanner }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\tconst {\r\n\t\tsetDismissData,\r\n\t\tdismissData,\r\n\t\tselectedOption,\r\n\t\tsetSelectedOption,\r\n\t\tselectedTemplate,\r\n\t\tsetTooltipElementOptions,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tupdateprogressclick,\r\n\t\tprogress,\r\n\t\tsetProgress,\r\n\t\tdismiss,\r\n\t\tsetDismiss,\r\n\t\tselectedTemplateTour,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\tBposition\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [tempDismiss, setTempDismiss] = useState(dismiss); // Temporary state\r\n\tconst [colorChange, setColorChange] = useState(ProgressColor);\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [displayType, setDisplayType] = useState(\"Cross Icon\");\r\n\tconst [dontShowAgain, setDontShowAgain] = useState(true);\r\n\tconst [colors, setColors] = useState(\"#ff0000\");\r\n\t// Initialize local state with values from global state\r\n\tconst [elementSettings, setElementsSettings] = useState({\r\n\t\tisProgress: progress,\r\n\t\tprogressSelectedOption: Number(selectedOption) || 1, // Default to 1 if not set\r\n\t});\r\n\r\n\t// Add useEffect to synchronize local state with global state\r\n\tuseEffect(() => {\r\n\t\t// Update local state when global state changes\r\n\t\tsetElementsSettings({\r\n\t\t\tisProgress: progress,\r\n\t\t\tprogressSelectedOption: Number(selectedOption) || 1,\r\n\t\t});\r\n\t\tsetTempDismiss(dismiss);\r\n\t\tsetColorChange(ProgressColor);\r\n\t}, [progress, selectedOption, dismiss, ProgressColor]);\r\n\r\n\t// Update handlers for each dismiss option\r\n\tconst handleOptionSelect = (option: string | number) => {\r\n\t\t// Update only the local state\r\n\t\tconst numOption = Number(option);\r\n\t\tsetElementsSettings(eleSettings => {\r\n\t\t\treturn { ...eleSettings, progressSelectedOption: numOption }\r\n\t\t});\r\n\r\n\t\t// Don't update global state until Apply is clicked\r\n\t};\r\n\r\n\tconst handleDisplayTypeChange = (value: string) => {\r\n\t\tsetDisplayType(value);\r\n\t};\r\n\r\n\tconst handleBorderColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst color = e.target.value;\r\n\t\tsetColors(color);\r\n\t};\r\n\r\n\tconst handleDontShowAgainChange = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {\r\n\t\tsetDontShowAgain(checked);\r\n\t};\r\n\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Store the previous progress state for comparison\r\n\t\tconst previousProgressState = progress;\r\n\t\tconst hasProgressChanged = previousProgressState !== elementSettings.isProgress;\r\n\t\t// Create a complete element settings object\r\n\t\tconst updatedElement = {\r\n\t\t\tprogress: elementSettings.isProgress,\r\n        progressSelectedOption: elementSettings.progressSelectedOption,\r\n        dismiss: tempDismiss,\r\n        progressColor: colorChange\r\n\t\t};\r\n\r\n\t\t// Create a batch update function to record a single history entry\r\n\t\tconst batchUpdate = useDrawerStore.getState().batchUpdate;\r\n\t\tconst drawerStore = useDrawerStore.getState();\r\n\r\n\t\t// First update the progress state in the store\r\n\t\t// This ensures resetHeightofBanner will use the correct value\r\n\t\tif (hasProgressChanged) {\r\n\t\t\t// console.log(\"Setting progress state in store before batch update:\", elementSettings.isProgress);\r\n\t\t\tdrawerStore.setProgress(elementSettings.isProgress);\r\n\t\t}\r\n\r\n\t\t// Always update dismissData when Apply is clicked, regardless of whether it changed\r\n\t\t// This ensures the dismiss icon state is updated only when Apply is clicked\r\n\t\tsetDismissData({\r\n\t\t\t...(dismissData || { Actions: \"\", DisplayType: \"Cross Icon\", Color: \"#000000\", DontShowAgain: true }),\r\n\t\t\tdismisssel: tempDismiss\r\n\t\t});\r\n\r\n\t\t// Use the batch update function to record a single history entry\r\n\t\tbatchUpdate(\r\n\t\t\t() => {\r\n\t\t\t\t// Apply all changes at once\r\n\t\t\t\tupdateDesignelementInTooltip(updatedElement);\r\n\r\n\t\t\t\t// Also update the individual state values to ensure everything is in sync\r\n\t\t\t\tdrawerStore.setProgress(elementSettings.isProgress);\r\n\t\t\t\tdrawerStore.setSelectedOption(elementSettings.progressSelectedOption);\r\n\t\t\t\tdrawerStore.setDismiss(tempDismiss);\r\n\t\t\t\tdrawerStore.setProgressColor(colorChange);\r\n\r\n\t\t\t\t// For AI-created tours, immediately apply global progress bar synchronization\r\n\t\t\t\t// This ensures all steps are updated when Apply is clicked\r\n\t\t\t\tif (drawerStore.createWithAI && drawerStore.selectedTemplate === \"Tour\") {\r\n\t\t\t\t\tconsole.log(\"🔄 handleApplyChanges: Applying global progress bar synchronization for AI tour\", {\r\n\t\t\t\t\t\tisProgress: elementSettings.isProgress,\r\n\t\t\t\t\t\tprogressSelectedOption: elementSettings.progressSelectedOption,\r\n\t\t\t\t\t\tdismiss: tempDismiss,\r\n\t\t\t\t\t\tprogressColor: colorChange,\r\n\t\t\t\t\t\tcurrentStep: drawerStore.currentStep,\r\n\t\t\t\t\t\ttotalSteps: drawerStore.interactionData?.GuideStep?.length || 0\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Apply the changes to all steps immediately\r\n\t\t\t\t\tdrawerStore.syncGlobalProgressBarStateForAITour();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'ELEMENT_BATCH_UPDATE',\r\n\t\t\t`Updated element settings`\r\n\t\t);\r\n\t\t// When progress setting changes are applied, update the banner height if needed\r\n\t\tif (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" && Bposition === \"Push Down\" && resetHeightofBanner) {\r\n\t\t\t// Always update the height when Apply is clicked, regardless of whether progress changed\r\n\t\t\t// This ensures consistent behavior and fixes any potential state inconsistencies\r\n\t\t\t// console.log(\"Applying banner height changes. Progress state:\", elementSettings.isProgress);\r\n\r\n\t\t\t// Get the current padding and border values from the store\r\n\t\t\tconst currentPadding = drawerStore.bpadding || \"12\";\r\n\t\t\tconst currentBorder = drawerStore.BborderSize || \"2\";\r\n\r\n\t\t\t// Force a complete recalculation of the banner height\r\n\t\t\t// The isFromApply=true parameter ensures all dimensions are included in the calculation\r\n\t\t\tresetHeightofBanner(\r\n\t\t\t\t\"Push Down\",\r\n\t\t\t\tparseInt(currentPadding),\r\n\t\t\t\tparseInt(currentBorder),\r\n\t\t\t\ttrue, // isFromApply\r\n\t\t\t\t0,    // oldPadding\r\n\t\t\t\t55    // top\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t// Update the UI state\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowElementsSettings(false);\r\n\t\tsetDesignPopup(true);\r\n\t\tsetMenuPopup(true);\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (dismissData?.dismisssel) {\r\n\t\t\tsetDismiss(true);\r\n\t\t\tsetColors(dismissData.Color);\r\n\t\t}\r\n\t}, [dismissData?.dismisssel]);\r\n\r\n\r\n\t// Early return if popup is closed\r\n\tif (!isOpen) return null;\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\tsetIsOpen(false);\r\n\t\t\t\t\t\t\tsetShowElementsSettings(false);\r\n\t\t\t\t\t\t\tsetMenuPopup(true);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Elements\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\tsetIsOpen(false);\r\n\t\t\t\t\t\t\tsetShowElementsSettings(false);\r\n\t\t\t\t\t\t\tsetDesignPopup(true);\r\n\t\t\t\t\t\t\tsetMenuPopup(true);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\tclassName=\"qadpt-controls\"\r\n\t\t\t\t\t//style={{ opacity: \"0.5\", height: \"60px\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t{(selectedTemplate!== \"Hotspot\" && selectedTemplate!== \"Banner\" ) && (\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t//sx={{ opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Progress\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={elementSettings.isProgress}\r\n        onChange={(e) => {\r\n\t\t\tconst newValue = e.target.checked;\r\n\t\t\t// Update only local state\r\n\t\t\tsetElementsSettings(eleSettings => {\r\n\t\t\t\treturn { ...eleSettings, isProgress: newValue }\r\n\t\t\t});\r\n\r\n\t\t\t// Don't update global state until Apply is clicked\r\n        }}\r\n        className=\"qadpt-progress-switch\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t{elementSettings.isProgress && (selectedTemplate!== \"Hotspot\" && selectedTemplate!== \"Banner\" ) && (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tpadding: \"10px\",\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\tcontainer\r\n\t\t\t\t\t\t\tspacing={2}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box sx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\"\t\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Progress Color\")}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={colorChange}\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst newColor = e.target.value;\r\n\t\t\t\t\t\t\t\t// Update only local state\r\n\t\t\t\t\t\t\t\tsetColorChange(newColor);\r\n\t\t\t\t\t\t\t\t// Don't update global state until Apply is clicked\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(1)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 1 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 1 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Dots\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", gap: \"4px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"8px\", height: \"8px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"50%\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"8px\", height: \"8px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"50%\" }}></div>\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"8px\", height: \"8px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"50%\" }}></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(2)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 2 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 2 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Linear Progress\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<div style={{ width: \"40px\", height: \"4px\", backgroundColor: \"#5f96a0\" }}></div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(3)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 3 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 3 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Bread Crumbs\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", gap: \"4px\" }}>\r\n\t\t\t\t\t\t\t\t\t<div style={{width: \"14px\", height: \"4px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"100px\" }}></div>\r\n\t\t\t\t\t\t\t\t\t<div style={{width: \"14px\", height: \"4px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"100px\" }}></div>\r\n\t\t\t\t\t\t\t\t\t<div style={{width: \"14px\", height: \"4px\", backgroundColor: \"var(--primarycolor)\", borderRadius: \"100px\" }}></div>\r\n\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(4)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 4 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 4 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"Numbers\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"1 of 4\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t\t<Grid\r\n\t\t\t\t\t\t\t\titem\r\n\t\t\t\t\t\t\t\t\txs={12}\r\n\t\t\t\t\t\t\t\t\tsx={{paddingTop:\"6px !important\"}}\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t//onClick={() => handleOptionSelect(4)}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"5px 10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: Number(elementSettings.progressSelectedOption) === 5 ? \"#5f9ea01a\" : \"#e5dada\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\tborder:Number(elementSettings.progressSelectedOption) === 5 ? \"1px solid var(--primarycolor)\" :\"transparent\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"var(--primarycolor)\" }}>{translate(\"CheckList\")}</Typography>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t)}\r\n\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Dismiss\")}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={tempDismiss}\r\n        onChange={(e) => setTempDismiss(e.target.checked)}\r\n        name=\"tempDismiss\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t{dismiss && (\r\n\t\t\t\t\t<div\r\n\r\n\t\t\t\t>\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ marginBottom: \"8px\" }}>Actions</Typography> */}\r\n\t\t\t\t\t\t{/* <Box sx={{ display: \"flex\", justifyContent: \"space-between\", marginBottom: \"16px\" }}>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedOption === \"dismiss\" ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(\"dismiss\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tDismiss\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedOption === \"snooze\" ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={() => handleOptionSelect(\"snooze\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tSnooze\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ marginBottom: \"8px\", color: \"rgba(0, 0, 0, 0.38)\" }}>Display Type</Typography> */}\r\n\t\t\t\t\t\t{/* <Tooltip\r\n\t\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t{\" \"}\r\n\r\n\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"16px\" }}\r\n\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tvalue={displayType}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleDisplayTypeChange(e.target.value as string)}\r\n\t\t\t\t\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\t\t\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\t\t\t\t\t\t\tdisablePortal: true,\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#F6EEEE\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": { padding: \"8px\" },\r\n\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"Cross Icon\"\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tCross Icon\r\n\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\t\tvalue=\"Close\"\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tClose\r\n\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</Tooltip> */}\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ marginBottom: \"8px\" }}>Color</Typography>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#F6EEEE\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\tmarginBottom: \"16px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography>Fill</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={colors}\r\n\t\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\twidth: \"30px\",\r\n\t\t\t\t\t\t\t\t\theight: \"30px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t{/* <Box sx={{ marginTop: \"16px\" }}>\r\n\t\t\t\t\t\t\t<Typography sx={{ marginBottom: \"8px\", color: \"rgba(0, 0, 0, 0.38)\" }}>Don't show again</Typography>\r\n\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", justifyContent: \"space-between\" }}>\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tvariant={dontShowAgain ? \"contained\" : \"outlined\"}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: dontShowAgain ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDontShowAgainChange(true)}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tYes\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tvariant={!dontShowAgain ? \"contained\" : \"outlined\"}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: !dontShowAgain ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"48%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDontShowAgainChange(false)}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tNo\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tDon't show again\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\tchecked={dontShowAgain}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={() => { setDontShowAgain(!dontShowAgain) }}\r\n\t\t\t\t\t\t\t\t\t\tdisabled={translaterue}//For temporary we are disableing for turn off\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div> */}\r\n\t\t\t\t\t\t{/* <Switch\r\n      checked={dontShowAgain}\r\n      onChange={handleDontShowAgainChange}\r\n      value={dontShowAgain}\r\n      sx={{\r\n        color: dontShowAgain ? \"rgba(211, 217, 218, 0.5)\" : \"rgba(250, 246, 246, 1)\",\r\n      }}\r\n    /> */}\r\n\t\t\t\t\t{/* </Box> */}\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn`}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t</Button>\r\n\r\n\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default Elementssettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACCC,GAAG,EACHC,UAAU,EACVC,UAAU,EAEVC,IAAI,EACJC,MAAM,QAKA,eAAe;AACtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW/C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,uBAAuB;EAAEC,cAAc;EAAEC,YAAY;EAAEC;AAAyB,CAAC,KAAK;EAAAC,EAAA;EACjH,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGV,cAAc,CAAC,CAAC;EAEzC,MAAM;IACLW,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,iBAAiB;IACjBC,gBAAgB;IAChBC,wBAAwB;IACxBC,oBAAoB;IACpBC,WAAW;IACXC,mBAAmB;IACnBC,QAAQ;IACRC,WAAW;IACXC,OAAO;IACPC,UAAU;IACVC,oBAAoB;IACpBC,mBAAmB;IACnBC,aAAa;IACbC,gBAAgB;IAChBC,4BAA4B;IAC5BC;EACD,CAAC,GAAG9B,cAAc,CAAE+B,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAACgC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAACoC,aAAa,CAAC;EAC7D,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,YAAY,CAAC;EAC5D,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC,SAAS,CAAC;EAC/C;EACA,MAAM,CAACqD,eAAe,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAC;IACvDuD,UAAU,EAAEzB,QAAQ;IACpB0B,sBAAsB,EAAEC,MAAM,CAAClC,cAAc,CAAC,IAAI,CAAC,CAAE;EACtD,CAAC,CAAC;;EAEF;EACAtB,SAAS,CAAC,MAAM;IACf;IACAqD,mBAAmB,CAAC;MACnBC,UAAU,EAAEzB,QAAQ;MACpB0B,sBAAsB,EAAEC,MAAM,CAAClC,cAAc,CAAC,IAAI;IACnD,CAAC,CAAC;IACFmB,cAAc,CAACV,OAAO,CAAC;IACvBY,cAAc,CAACR,aAAa,CAAC;EAC9B,CAAC,EAAE,CAACN,QAAQ,EAAEP,cAAc,EAAES,OAAO,EAAEI,aAAa,CAAC,CAAC;;EAEtD;EACA,MAAMsB,kBAAkB,GAAIC,MAAuB,IAAK;IACvD;IACA,MAAMC,SAAS,GAAGH,MAAM,CAACE,MAAM,CAAC;IAChCL,mBAAmB,CAACO,WAAW,IAAI;MAClC,OAAO;QAAE,GAAGA,WAAW;QAAEL,sBAAsB,EAAEI;MAAU,CAAC;IAC7D,CAAC,CAAC;;IAEF;EACD,CAAC;EAED,MAAME,uBAAuB,GAAIC,KAAa,IAAK;IAClDf,cAAc,CAACe,KAAK,CAAC;EACtB,CAAC;EAED,MAAMC,uBAAuB,GAAIC,CAAsC,IAAK;IAC3E,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACJ,KAAK;IAC5BX,SAAS,CAACc,KAAK,CAAC;EACjB,CAAC;EAED,MAAME,yBAAyB,GAAGA,CAACC,KAA0C,EAAEC,OAAgB,KAAK;IACnGpB,gBAAgB,CAACoB,OAAO,CAAC;EAC1B,CAAC;EAGD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACA,MAAMC,qBAAqB,GAAG1C,QAAQ;IACtC,MAAM2C,kBAAkB,GAAGD,qBAAqB,KAAKnB,eAAe,CAACE,UAAU;IAC/E;IACA,MAAMmB,cAAc,GAAG;MACtB5C,QAAQ,EAAEuB,eAAe,CAACE,UAAU;MAC/BC,sBAAsB,EAAEH,eAAe,CAACG,sBAAsB;MAC9DxB,OAAO,EAAES,WAAW;MACpBkC,aAAa,EAAEhC;IACrB,CAAC;;IAED;IACA,MAAMiC,WAAW,GAAGnE,cAAc,CAACoE,QAAQ,CAAC,CAAC,CAACD,WAAW;IACzD,MAAME,WAAW,GAAGrE,cAAc,CAACoE,QAAQ,CAAC,CAAC;;IAE7C;IACA;IACA,IAAIJ,kBAAkB,EAAE;MACvB;MACAK,WAAW,CAAC/C,WAAW,CAACsB,eAAe,CAACE,UAAU,CAAC;IACpD;;IAEA;IACA;IACAlC,cAAc,CAAC;MACd,IAAIC,WAAW,IAAI;QAAEyD,OAAO,EAAE,EAAE;QAAEC,WAAW,EAAE,YAAY;QAAEC,KAAK,EAAE,SAAS;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;MACrGC,UAAU,EAAE1C;IACb,CAAC,CAAC;;IAEF;IACAmC,WAAW,CACV,MAAM;MACL;MACAtC,4BAA4B,CAACoC,cAAc,CAAC;;MAE5C;MACAI,WAAW,CAAC/C,WAAW,CAACsB,eAAe,CAACE,UAAU,CAAC;MACnDuB,WAAW,CAACtD,iBAAiB,CAAC6B,eAAe,CAACG,sBAAsB,CAAC;MACrEsB,WAAW,CAAC7C,UAAU,CAACQ,WAAW,CAAC;MACnCqC,WAAW,CAACzC,gBAAgB,CAACM,WAAW,CAAC;;MAEzC;MACA;MACA,IAAImC,WAAW,CAACM,YAAY,IAAIN,WAAW,CAACrD,gBAAgB,KAAK,MAAM,EAAE;QAAA,IAAA4D,qBAAA,EAAAC,sBAAA;QACxEC,OAAO,CAACC,GAAG,CAAC,iFAAiF,EAAE;UAC9FjC,UAAU,EAAEF,eAAe,CAACE,UAAU;UACtCC,sBAAsB,EAAEH,eAAe,CAACG,sBAAsB;UAC9DxB,OAAO,EAAES,WAAW;UACpBkC,aAAa,EAAEhC,WAAW;UAC1Bf,WAAW,EAAEkD,WAAW,CAAClD,WAAW;UACpC6D,UAAU,EAAE,EAAAJ,qBAAA,GAAAP,WAAW,CAACY,eAAe,cAAAL,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BM,SAAS,cAAAL,sBAAA,uBAAtCA,sBAAA,CAAwCM,MAAM,KAAI;QAC/D,CAAC,CAAC;;QAEF;QACAd,WAAW,CAACe,mCAAmC,CAAC,CAAC;MAClD;IACD,CAAC,EACD,sBAAsB,EACtB,0BACD,CAAC;IACD;IACA,IAAIpE,gBAAgB,KAAK,MAAM,IAAIS,oBAAoB,KAAK,QAAQ,IAAIK,SAAS,KAAK,WAAW,IAAItB,mBAAmB,EAAE;MACzH;MACA;MACA;;MAEA;MACA,MAAM6E,cAAc,GAAGhB,WAAW,CAACiB,QAAQ,IAAI,IAAI;MACnD,MAAMC,aAAa,GAAGlB,WAAW,CAACmB,WAAW,IAAI,GAAG;;MAEpD;MACA;MACAhF,mBAAmB,CAClB,WAAW,EACXiF,QAAQ,CAACJ,cAAc,CAAC,EACxBI,QAAQ,CAACF,aAAa,CAAC,EACvB,IAAI;MAAE;MACN,CAAC;MAAK;MACN,EAAE,CAAI;MACP,CAAC;IACF;;IAEA;IACAlD,SAAS,CAAC,KAAK,CAAC;IAChBhC,uBAAuB,CAAC,KAAK,CAAC;IAC9BC,cAAc,CAAC,IAAI,CAAC;IACpBC,YAAY,CAAC,IAAI,CAAC;IAClBmB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACDlC,SAAS,CAAC,MAAM;IACf,IAAIqB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE6D,UAAU,EAAE;MAC5BlD,UAAU,CAAC,IAAI,CAAC;MAChBmB,SAAS,CAAC9B,WAAW,CAAC2D,KAAK,CAAC;IAC7B;EACD,CAAC,EAAE,CAAC3D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6D,UAAU,CAAC,CAAC;;EAG7B;EACA,IAAI,CAACtC,MAAM,EAAE,OAAO,IAAI;EACxB,oBACCjC,OAAA;IACCuF,EAAE,EAAC,mBAAmB;IACtBC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7BzF,OAAA;MAAKwF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BzF,OAAA;QAAKwF,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnCzF,OAAA,CAACR,UAAU;UACV,cAAW,OAAO;UAClBkG,OAAO,EAAEA,CAAA,KAAM;YACdxD,SAAS,CAAC,KAAK,CAAC;YAChBhC,uBAAuB,CAAC,KAAK,CAAC;YAC9BE,YAAY,CAAC,IAAI,CAAC;UACnB,CAAE;UAAAqF,QAAA,eAEFzF,OAAA,CAACJ,2BAA2B;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACb9F,OAAA;UAAKwF,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEjF,SAAS,CAAC,UAAU;QAAC;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1D9F,OAAA,CAACR,UAAU;UACVuG,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBL,OAAO,EAAEA,CAAA,KAAM;YACdxD,SAAS,CAAC,KAAK,CAAC;YAChBhC,uBAAuB,CAAC,KAAK,CAAC;YAC9BC,cAAc,CAAC,IAAI,CAAC;YACpBC,YAAY,CAAC,IAAI,CAAC;UACnB,CAAE;UAAAqF,QAAA,eAEFzF,OAAA,CAACL,SAAS;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEN9F,OAAA;QACCwF,SAAS,EAAC;QACV;QAAA;QAAAC,QAAA,GAEE5E,gBAAgB,KAAI,SAAS,IAAIA,gBAAgB,KAAI,QAAQ,iBAC9Db,OAAA,CAACV,GAAG;UAACkG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjCzF,OAAA,CAACT,UAAU;YACViG,SAAS,EAAC;YACX;YAAA;YAAAC,QAAA,EAEEjF,SAAS,CAAC,UAAU;UAAC;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEb9F,OAAA;YAAAyF,QAAA,eACAzF,OAAA;cAAOwF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBACnCzF,OAAA;gBACIgG,IAAI,EAAC,UAAU;gBACftC,OAAO,EAAEjB,eAAe,CAACE,UAAW;gBACpCsD,QAAQ,EAAG5C,CAAC,IAAK;kBACtB,MAAM6C,QAAQ,GAAG7C,CAAC,CAACE,MAAM,CAACG,OAAO;kBACjC;kBACAhB,mBAAmB,CAACO,WAAW,IAAI;oBAClC,OAAO;sBAAE,GAAGA,WAAW;sBAAEN,UAAU,EAAEuD;oBAAS,CAAC;kBAChD,CAAC,CAAC;;kBAEF;gBACK,CAAE;gBACFV,SAAS,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACF9F,OAAA;gBAAMwF,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAEL,EAEDrD,eAAe,CAACE,UAAU,IAAK9B,gBAAgB,KAAI,SAAS,IAAIA,gBAAgB,KAAI,QAAU,iBAC9Fb,OAAA,CAACV,GAAG;UACH6G,EAAE,EAAE;YACHC,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UACf,CAAE;UAAAd,QAAA,eAEFzF,OAAA,CAACP,IAAI;YACJ+G,SAAS;YACTC,OAAO,EAAE,CAAE;YAAAhB,QAAA,gBAEXzF,OAAA,CAACP,IAAI;cACJiH,IAAI;cACHC,EAAE,EAAE,EAAG;cAAAlB,QAAA,eAERzF,OAAA,CAACV,GAAG;gBAAC6G,EAAE,EAAE;kBACPS,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBR,OAAO,EAAE,UAAU;kBACnBD,YAAY,EAAE,KAAK;kBACnBU,MAAM,EAAE;gBAAkB,CAAE;gBAAAtB,QAAA,gBAC5BzF,OAAA,CAACT,UAAU;kBAACiG,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEjF,SAAS,CAAC,gBAAgB;gBAAC;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC1F9F,OAAA;kBACCgG,IAAI,EAAC,OAAO;kBACZ7C,KAAK,EAAEpB,WAAY;kBACnBkE,QAAQ,EAAG5C,CAAC,IAAK;oBAChB,MAAM2D,QAAQ,GAAG3D,CAAC,CAACE,MAAM,CAACJ,KAAK;oBAC/B;oBACAnB,cAAc,CAACgF,QAAQ,CAAC;oBACxB;kBACD,CAAE;kBACFxB,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP9F,OAAA,CAACP,IAAI;cACJiH,IAAI;cACHC,EAAE,EAAE,EAAG;cAAAlB,QAAA,eAERzF,OAAA,CAACV,GAAG;gBACHoG,OAAO,EAAEA,CAAA,KAAM5C,kBAAkB,CAAC,CAAC,CAAE;gBACrCqD,EAAE,EAAE;kBACHS,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBR,OAAO,EAAE,UAAU;kBACnBF,eAAe,EAAEvD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,SAAS;kBAC/FyD,YAAY,EAAE,KAAK;kBACnBU,MAAM,EAAE,SAAS;kBACjBE,MAAM,EAACpE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,+BAA+B,GAAE;gBAChG,CAAE;gBAAA6C,QAAA,gBAEDzF,OAAA,CAACT,UAAU;kBAAC4G,EAAE,EAAE;oBAAE7C,KAAK,EAAE;kBAAsB,CAAE;kBAAAmC,QAAA,EAAEjF,SAAS,CAAC,MAAM;gBAAC;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnF9F,OAAA;kBAAKkH,KAAK,EAAE;oBAAEN,OAAO,EAAE,MAAM;oBAAEO,GAAG,EAAE;kBAAM,CAAE;kBAAA1B,QAAA,gBAC3CzF,OAAA;oBAAKkH,KAAK,EAAE;sBAAEE,KAAK,EAAE,KAAK;sBAAEC,MAAM,EAAE,KAAK;sBAAEjB,eAAe,EAAE,qBAAqB;sBAAEC,YAAY,EAAE;oBAAM;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChH9F,OAAA;oBAAKkH,KAAK,EAAE;sBAAEE,KAAK,EAAE,KAAK;sBAAEC,MAAM,EAAE,KAAK;sBAAEjB,eAAe,EAAE,qBAAqB;sBAAEC,YAAY,EAAE;oBAAM;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChH9F,OAAA;oBAAKkH,KAAK,EAAE;sBAAEE,KAAK,EAAE,KAAK;sBAAEC,MAAM,EAAE,KAAK;sBAAEjB,eAAe,EAAE,qBAAqB;sBAAEC,YAAY,EAAE;oBAAM;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEP9F,OAAA,CAACP,IAAI;cACJiH,IAAI;cACHC,EAAE,EAAE,EAAG;cACPR,EAAE,EAAE;gBAACmB,UAAU,EAAC;cAAgB,CAAE;cAAA7B,QAAA,eAEnCzF,OAAA,CAACV,GAAG;gBACHoG,OAAO,EAAEA,CAAA,KAAM5C,kBAAkB,CAAC,CAAC,CAAE;gBACrCqD,EAAE,EAAE;kBACHS,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBR,OAAO,EAAE,UAAU;kBACnBF,eAAe,EAAEvD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,SAAS;kBAC/FyD,YAAY,EAAE,KAAK;kBACnBU,MAAM,EAAE,SAAS;kBACjBE,MAAM,EAACpE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,+BAA+B,GAAE;gBAChG,CAAE;gBAAA6C,QAAA,gBAEDzF,OAAA,CAACT,UAAU;kBAAC4G,EAAE,EAAE;oBAAE7C,KAAK,EAAE;kBAAsB,CAAE;kBAAAmC,QAAA,EAAEjF,SAAS,CAAC,iBAAiB;gBAAC;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC9F9F,OAAA;kBAAKkH,KAAK,EAAE;oBAAEE,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,KAAK;oBAAEjB,eAAe,EAAE;kBAAU;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACP9F,OAAA,CAACP,IAAI;cACJiH,IAAI;cACHC,EAAE,EAAE,EAAG;cACPR,EAAE,EAAE;gBAACmB,UAAU,EAAC;cAAgB,CAAE;cAAA7B,QAAA,eAEnCzF,OAAA,CAACV,GAAG;gBACHoG,OAAO,EAAEA,CAAA,KAAM5C,kBAAkB,CAAC,CAAC,CAAE;gBACrCqD,EAAE,EAAE;kBACHS,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBR,OAAO,EAAE,UAAU;kBACnBF,eAAe,EAAEvD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,SAAS;kBAC/FyD,YAAY,EAAE,KAAK;kBACnBU,MAAM,EAAE,SAAS;kBACjBE,MAAM,EAACpE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,+BAA+B,GAAE;gBAChG,CAAE;gBAAA6C,QAAA,gBAEDzF,OAAA,CAACT,UAAU;kBAAC4G,EAAE,EAAE;oBAAE7C,KAAK,EAAE;kBAAsB,CAAE;kBAAAmC,QAAA,EAAEjF,SAAS,CAAC,cAAc;gBAAC;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC3F9F,OAAA;kBAAKkH,KAAK,EAAE;oBAAEN,OAAO,EAAE,MAAM;oBAAEO,GAAG,EAAE;kBAAM,CAAE;kBAAA1B,QAAA,gBAC5CzF,OAAA;oBAAKkH,KAAK,EAAE;sBAACE,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,KAAK;sBAAEjB,eAAe,EAAE,qBAAqB;sBAAEC,YAAY,EAAE;oBAAQ;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClH9F,OAAA;oBAAKkH,KAAK,EAAE;sBAACE,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,KAAK;sBAAEjB,eAAe,EAAE,qBAAqB;sBAAEC,YAAY,EAAE;oBAAQ;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClH9F,OAAA;oBAAKkH,KAAK,EAAE;sBAACE,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,KAAK;sBAAEjB,eAAe,EAAE,qBAAqB;sBAAEC,YAAY,EAAE;oBAAQ;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE7G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEP9F,OAAA,CAACP,IAAI;cACJiH,IAAI;cACHC,EAAE,EAAE,EAAG;cACPR,EAAE,EAAE;gBAACmB,UAAU,EAAC;cAAgB,CAAE;cAAA7B,QAAA,eAGnCzF,OAAA,CAACV,GAAG;gBACHoG,OAAO,EAAEA,CAAA,KAAM5C,kBAAkB,CAAC,CAAC,CAAE;gBACrCqD,EAAE,EAAE;kBACHS,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBR,OAAO,EAAE,UAAU;kBACnBF,eAAe,EAAEvD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,SAAS;kBAC/FyD,YAAY,EAAE,KAAK;kBACnBU,MAAM,EAAE,SAAS;kBACjBE,MAAM,EAACpE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,+BAA+B,GAAE;gBAChG,CAAE;gBAAA6C,QAAA,gBAEDzF,OAAA,CAACT,UAAU;kBAAC4G,EAAE,EAAE;oBAAE7C,KAAK,EAAE;kBAAsB,CAAE;kBAAAmC,QAAA,EAAEjF,SAAS,CAAC,SAAS;gBAAC;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrF9F,OAAA,CAACT,UAAU;kBAAC4G,EAAE,EAAE;oBAAE7C,KAAK,EAAE;kBAAsB,CAAE;kBAAAmC,QAAA,EAAEjF,SAAS,CAAC,QAAQ;gBAAC;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACP9F,OAAA,CAACP,IAAI;cACJiH,IAAI;cACHC,EAAE,EAAE,EAAG;cACPR,EAAE,EAAE;gBAACmB,UAAU,EAAC;cAAgB,CAAE;cAAA7B,QAAA,eAGnCzF,OAAA,CAACV;cACA;cAAA;gBACA6G,EAAE,EAAE;kBACHS,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBR,OAAO,EAAE,UAAU;kBACnBF,eAAe,EAAEvD,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,SAAS;kBAC/FyD,YAAY,EAAE,KAAK;kBACnBU,MAAM,EAAE,SAAS;kBACjBE,MAAM,EAACpE,MAAM,CAACJ,eAAe,CAACG,sBAAsB,CAAC,KAAK,CAAC,GAAG,+BAA+B,GAAE;gBAChG,CAAE;gBAAA6C,QAAA,eAGDzF,OAAA,CAACT,UAAU;kBAAC4G,EAAE,EAAE;oBAAE7C,KAAK,EAAE;kBAAsB,CAAE;kBAAAmC,QAAA,EAAEjF,SAAS,CAAC,WAAW;gBAAC;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACJ,eAID9F,OAAA,CAACV,GAAG;UAACkG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjCzF,OAAA;YACCwF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAG9BjF,SAAS,CAAC,SAAS;UAAC;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACN9F,OAAA;YAAAyF,QAAA,eACAzF,OAAA;cAAOwF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAClCzF,OAAA;gBACIgG,IAAI,EAAC,UAAU;gBACftC,OAAO,EAAE7B,WAAY;gBACrBoE,QAAQ,EAAG5C,CAAC,IAAKvB,cAAc,CAACuB,CAAC,CAACE,MAAM,CAACG,OAAO,CAAE;gBAClD6D,IAAI,EAAC;cAAa;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACF9F,OAAA;gBAAMwF,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC,EAGN1E,OAAO,iBACPpB,OAAA;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6IM,CACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8BI,CAAC,eACP9F,OAAA;QAAKwF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClCzF,OAAA,CAACN,MAAM;UACN8H,OAAO,EAAC,WAAW;UACnB9B,OAAO,EAAE/B,kBAAmB;UAC5B6B,SAAS,EAAE,WAAY;UAAAC,QAAA,EAEtBjF,SAAS,CAAC,OAAO;QAAC;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAET,CAAC;AAACxF,EAAA,CAxmBIL,gBAAgB;EAAA,QACIH,cAAc,EAsBnCD,cAAc;AAAA;AAAA4H,EAAA,GAvBbxH,gBAAgB;AA0mBtB,eAAeA,gBAAgB;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}