{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\guideList\\\\CloneGuidePopUp.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogActions, DialogContent, DialogTitle, TextField, Button, IconButton } from '@mui/material';\nimport CloseIcon from '@mui/icons-material/Close';\nimport { CheckGuideNameExists, CopyGuide } from '../../../services/GuideListServices';\nimport './GuideMenuOptions.css';\nimport { useSnackbar } from './SnackbarContext';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CloneInteractionDialog = ({\n  open,\n  handleClose,\n  initialName,\n  onCloneSuccess,\n  name\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [announcementName, setAnnouncementName] = useState(`${initialName.Name}-Copy`);\n  const [error, setError] = useState(null);\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const handleNameChange = e => {\n    const newValue = e.target.value;\n    setAnnouncementName(newValue);\n\n    // Validate the length of the new value immediately while typing\n    if (newValue.trim() === \"\") {\n      setError(translate(\"Name is required.\"));\n    } else if (newValue.trim().length < 3) {\n      setError(translate(\"Guide Name must be at least 3 characters.\"));\n    } else if (newValue.trim().length > 50) {\n      setError(translate(\"Guide Name should not exceed 50 characters.\"));\n    } else {\n      setError(null); // Clear the error if the input is valid\n    }\n  };\n  const handleClone = async () => {\n    // Final validation before cloning\n    if (error) {\n      return; // Exit if there's an error\n    }\n    const guideName = announcementName;\n    const accountId = initialName.AccountId;\n    const organizationId = initialName.OrganizationId;\n    const existsResponse = await CheckGuideNameExists(guideName, accountId, name);\n    if (existsResponse === true) {\n      setError(translate(\"A guide with this name already exists.\"));\n    } else {\n      const guideId = initialName.GuideId;\n      const guideStatus = initialName.GuideStatus;\n      const guideType = initialName.GuideType;\n      const copyResponse = await CopyGuide(guideId, organizationId, announcementName, accountId, guideType);\n      if (copyResponse.Success) {\n        const prefix = `${announcementName} ${translate(guideType)} `;\n        const message = `${prefix} ${translate(\"Copied Successfully. Please Check The Drafts\")}`;\n        openSnackbar(message, \"success\");\n        onCloneSuccess();\n        handleClose();\n      } else {\n        setError(copyResponse.ErrorMessage);\n      }\n    }\n  };\n  const isCloneDisabled = !!error || announcementName.trim().length < 3;\n  const paraGraph = `${translate(\"Clone Guide\")} ${initialName.Name}`;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    className: \"qadpt-webclonepopup\",\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      className: \"qadpt-title\",\n      style: {\n        whiteSpace: 'nowrap',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        maxWidth: '100%'\n      },\n      children: paraGraph.length > 15 ? paraGraph.slice(0, 15) + '...' : paraGraph\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n      edge: \"end\",\n      color: \"inherit\",\n      onClick: handleClose,\n      \"aria-label\": \"close\",\n      className: \"qadpt-close\",\n      children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        className: \"name-fld\",\n        variant: \"outlined\",\n        label: translate('Name'),\n        value: announcementName,\n        onChange: handleNameChange,\n        slotProps: {\n          htmlInput: {\n            maxLength: 50\n          }\n        },\n        error: !!error,\n        helperText: error,\n        margin: \"dense\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClone,\n        disabled: isCloneDisabled,\n        variant: \"contained\",\n        children: translate('Clone')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 9\n  }, this);\n};\n_s(CloneInteractionDialog, \"jZ/8Fxcq5JutDn4oxtd5uxgAtsk=\", false, function () {\n  return [useTranslation, useSnackbar];\n});\n_c = CloneInteractionDialog;\nexport default CloneInteractionDialog;\nvar _c;\n$RefreshReg$(_c, \"CloneInteractionDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "TextField", "<PERSON><PERSON>", "IconButton", "CloseIcon", "CheckGuideNameExists", "CopyGuide", "useSnackbar", "useTranslation", "jsxDEV", "_jsxDEV", "CloneInteractionDialog", "open", "handleClose", "initialName", "onCloneSuccess", "name", "_s", "t", "translate", "announcementName", "setAnnouncementName", "Name", "error", "setError", "openSnackbar", "handleNameChange", "e", "newValue", "target", "value", "trim", "length", "handleClone", "guideName", "accountId", "AccountId", "organizationId", "OrganizationId", "existsResponse", "guideId", "GuideId", "guideStatus", "GuideStatus", "guideType", "GuideType", "copyResponse", "Success", "prefix", "message", "ErrorMessage", "isCloneDisabled", "paraGraph", "onClose", "className", "children", "style", "whiteSpace", "overflow", "textOverflow", "max<PERSON><PERSON><PERSON>", "slice", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "edge", "color", "onClick", "fullWidth", "variant", "label", "onChange", "slotProps", "htmlInput", "max<PERSON><PERSON><PERSON>", "helperText", "margin", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/CloneGuidePopUp.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON><PERSON>, <PERSON>alogA<PERSON>, <PERSON>alogContent, DialogTitle, TextField, Button, IconButton, Typography } from '@mui/material';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport { CheckGuideNameExists, CopyGuide } from '../../../services/GuideListServices';\r\nimport './GuideMenuOptions.css';\r\nimport { useSnackbar } from './SnackbarContext';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface Announcement {\r\n    AccountId: string;\r\n    Content: string;\r\n    CreatedBy: string;\r\n    CreatedDate: string;\r\n    Frequency: string;\r\n    GuideId: string;\r\n    GuideStatus: string;\r\n    GuideType: string;\r\n    Name: string;\r\n    OrganizationId: string;\r\n    Segment: string;\r\n    TargetUrl: string;\r\n    TemplateId: string;\r\n    UpdatedBy: string;\r\n    UpdatedDate: string;\r\n}\r\n\r\ninterface CloneInteractionDialogProps {\r\n    open: boolean;\r\n    handleClose: () => void;\r\n    initialName: Announcement; \r\n    onCloneSuccess: () => void;\r\n    name: string;\r\n}\r\n\r\nconst CloneInteractionDialog: React.FC<CloneInteractionDialogProps> = ({ open, handleClose, initialName, onCloneSuccess,name }) => {\r\n    const { t: translate } = useTranslation();\r\n    const [announcementName, setAnnouncementName] = useState(`${initialName.Name}-Copy`);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const { openSnackbar } = useSnackbar();\r\n\r\n    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newValue = e.target.value;\r\n        setAnnouncementName(newValue);\r\n\r\n        // Validate the length of the new value immediately while typing\r\n        if (newValue.trim() === \"\") {\r\n            setError(translate(\"Name is required.\"));\r\n        } else if (newValue.trim().length < 3) {\r\n            setError(translate(\"Guide Name must be at least 3 characters.\"));        \r\n        }\r\n        else if (newValue.trim().length > 50) {\r\n            setError(translate(\"Guide Name should not exceed 50 characters.\"));\r\n        }\r\n        else {\r\n            setError(null); // Clear the error if the input is valid\r\n        }\r\n    };\r\n\r\n    \r\n    const handleClone = async () => {\r\n        // Final validation before cloning\r\n        if (error) {\r\n            return; // Exit if there's an error\r\n        }\r\n        \r\n        const guideName = announcementName; \r\n        const accountId = initialName.AccountId;\r\n        const organizationId = initialName.OrganizationId;\r\n\r\n        const existsResponse = await CheckGuideNameExists(guideName, accountId, name);\r\n        \r\n        if (existsResponse === true) {\r\n            setError(translate(\"A guide with this name already exists.\"));\r\n        } else {\r\n            const guideId = initialName.GuideId; \r\n            const guideStatus = initialName.GuideStatus; \r\n            const guideType = initialName.GuideType;\r\n            const copyResponse = await CopyGuide(guideId, organizationId, announcementName, accountId, guideType);\r\n            if (copyResponse.Success) {\r\n                const prefix = `${announcementName} ${translate(guideType)} `;\r\n                const message = `${prefix} ${translate(\"Copied Successfully. Please Check The Drafts\")}`;\r\n\r\n                openSnackbar(message, \"success\");\r\n                onCloneSuccess();\r\n                handleClose();\r\n            } else {\r\n                setError(copyResponse.ErrorMessage);\r\n            }\r\n        }\r\n    };\r\n    const isCloneDisabled = !!error || announcementName.trim().length < 3;\r\n    const paraGraph = `${translate(\"Clone Guide\")} ${initialName.Name}`;\r\n    return (\r\n        <Dialog open={open} onClose={handleClose} className='qadpt-webclonepopup'>\r\n            <DialogTitle   className='qadpt-title' \r\n        style={{\r\n            whiteSpace: 'nowrap', \r\n            overflow: 'hidden', \r\n            textOverflow: 'ellipsis', \r\n            maxWidth: '100%'\r\n        }}\r\n    >\r\n        {paraGraph.length > 15 ? paraGraph.slice(0, 15) + '...' : paraGraph}</DialogTitle>\r\n            <IconButton \r\n                edge=\"end\" \r\n                color=\"inherit\" \r\n                onClick={handleClose} \r\n                aria-label=\"close\" \r\n                className='qadpt-close'\r\n            >\r\n                <CloseIcon />\r\n            </IconButton>\r\n          \r\n            <DialogContent>\r\n                <TextField\r\n                     fullWidth\r\n                     className=\"name-fld\"\r\n                   \r\n                    variant=\"outlined\"\r\n                    label={translate('Name')}\r\n                    value={announcementName}\r\n                    onChange={handleNameChange}\r\n                    slotProps={{\r\n                        htmlInput: {\r\n                            maxLength: 50, \r\n                        },\r\n                    }}\r\n                    error={!!error}\r\n                    helperText={error}\r\n                    margin=\"dense\"\r\n                />\r\n            </DialogContent>\r\n            <DialogActions>\r\n                <Button onClick={handleClone} disabled={isCloneDisabled} variant=\"contained\" >\r\n                    {translate('Clone')}\r\n                </Button>\r\n            </DialogActions>\r\n        </Dialog>\r\n    );\r\n};\r\n\r\nexport default CloneInteractionDialog;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAoB,eAAe;AAC5H,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,oBAAoB,EAAEC,SAAS,QAAQ,qCAAqC;AACrF,OAAO,wBAAwB;AAC/B,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4B/C,MAAMC,sBAA6D,GAAGA,CAAC;EAAEC,IAAI;EAAEC,WAAW;EAAEC,WAAW;EAAEC,cAAc;EAACC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC/H,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGX,cAAc,CAAC,CAAC;EACzC,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,GAAGkB,WAAW,CAACQ,IAAI,OAAO,CAAC;EACpF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM;IAAE6B;EAAa,CAAC,GAAGlB,WAAW,CAAC,CAAC;EAEtC,MAAMmB,gBAAgB,GAAIC,CAAsC,IAAK;IACjE,MAAMC,QAAQ,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IAC/BT,mBAAmB,CAACO,QAAQ,CAAC;;IAE7B;IACA,IAAIA,QAAQ,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACxBP,QAAQ,CAACL,SAAS,CAAC,mBAAmB,CAAC,CAAC;IAC5C,CAAC,MAAM,IAAIS,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MACnCR,QAAQ,CAACL,SAAS,CAAC,2CAA2C,CAAC,CAAC;IACpE,CAAC,MACI,IAAIS,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,EAAE,EAAE;MAClCR,QAAQ,CAACL,SAAS,CAAC,6CAA6C,CAAC,CAAC;IACtE,CAAC,MACI;MACDK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACpB;EACJ,CAAC;EAGD,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B;IACA,IAAIV,KAAK,EAAE;MACP,OAAO,CAAC;IACZ;IAEA,MAAMW,SAAS,GAAGd,gBAAgB;IAClC,MAAMe,SAAS,GAAGrB,WAAW,CAACsB,SAAS;IACvC,MAAMC,cAAc,GAAGvB,WAAW,CAACwB,cAAc;IAEjD,MAAMC,cAAc,GAAG,MAAMlC,oBAAoB,CAAC6B,SAAS,EAAEC,SAAS,EAAEnB,IAAI,CAAC;IAE7E,IAAIuB,cAAc,KAAK,IAAI,EAAE;MACzBf,QAAQ,CAACL,SAAS,CAAC,wCAAwC,CAAC,CAAC;IACjE,CAAC,MAAM;MACH,MAAMqB,OAAO,GAAG1B,WAAW,CAAC2B,OAAO;MACnC,MAAMC,WAAW,GAAG5B,WAAW,CAAC6B,WAAW;MAC3C,MAAMC,SAAS,GAAG9B,WAAW,CAAC+B,SAAS;MACvC,MAAMC,YAAY,GAAG,MAAMxC,SAAS,CAACkC,OAAO,EAAEH,cAAc,EAAEjB,gBAAgB,EAAEe,SAAS,EAAES,SAAS,CAAC;MACrG,IAAIE,YAAY,CAACC,OAAO,EAAE;QACtB,MAAMC,MAAM,GAAG,GAAG5B,gBAAgB,IAAID,SAAS,CAACyB,SAAS,CAAC,GAAG;QAC7D,MAAMK,OAAO,GAAG,GAAGD,MAAM,IAAI7B,SAAS,CAAC,8CAA8C,CAAC,EAAE;QAExFM,YAAY,CAACwB,OAAO,EAAE,SAAS,CAAC;QAChClC,cAAc,CAAC,CAAC;QAChBF,WAAW,CAAC,CAAC;MACjB,CAAC,MAAM;QACHW,QAAQ,CAACsB,YAAY,CAACI,YAAY,CAAC;MACvC;IACJ;EACJ,CAAC;EACD,MAAMC,eAAe,GAAG,CAAC,CAAC5B,KAAK,IAAIH,gBAAgB,CAACW,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC;EACrE,MAAMoB,SAAS,GAAG,GAAGjC,SAAS,CAAC,aAAa,CAAC,IAAIL,WAAW,CAACQ,IAAI,EAAE;EACnE,oBACIZ,OAAA,CAACb,MAAM;IAACe,IAAI,EAAEA,IAAK;IAACyC,OAAO,EAAExC,WAAY;IAACyC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBACrE7C,OAAA,CAACV,WAAW;MAAGsD,SAAS,EAAC,aAAa;MAC1CE,KAAK,EAAE;QACHC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE;MACd,CAAE;MAAAL,QAAA,EAEDH,SAAS,CAACpB,MAAM,GAAG,EAAE,GAAGoB,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGT;IAAS;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC,eAC9EvD,OAAA,CAACP,UAAU;MACP+D,IAAI,EAAC,KAAK;MACVC,KAAK,EAAC,SAAS;MACfC,OAAO,EAAEvD,WAAY;MACrB,cAAW,OAAO;MAClByC,SAAS,EAAC,aAAa;MAAAC,QAAA,eAEvB7C,OAAA,CAACN,SAAS;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEbvD,OAAA,CAACX,aAAa;MAAAwD,QAAA,eACV7C,OAAA,CAACT,SAAS;QACLoE,SAAS;QACTf,SAAS,EAAC,UAAU;QAErBgB,OAAO,EAAC,UAAU;QAClBC,KAAK,EAAEpD,SAAS,CAAC,MAAM,CAAE;QACzBW,KAAK,EAAEV,gBAAiB;QACxBoD,QAAQ,EAAE9C,gBAAiB;QAC3B+C,SAAS,EAAE;UACPC,SAAS,EAAE;YACPC,SAAS,EAAE;UACf;QACJ,CAAE;QACFpD,KAAK,EAAE,CAAC,CAACA,KAAM;QACfqD,UAAU,EAAErD,KAAM;QAClBsD,MAAM,EAAC;MAAO;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAChBvD,OAAA,CAACZ,aAAa;MAAAyD,QAAA,eACV7C,OAAA,CAACR,MAAM;QAACkE,OAAO,EAAEnC,WAAY;QAAC6C,QAAQ,EAAE3B,eAAgB;QAACmB,OAAO,EAAC,WAAW;QAAAf,QAAA,EACvEpC,SAAS,CAAC,OAAO;MAAC;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEjB,CAAC;AAAChD,EAAA,CAzGIN,sBAA6D;EAAA,QACtCH,cAAc,EAGdD,WAAW;AAAA;AAAAwE,EAAA,GAJlCpE,sBAA6D;AA2GnE,eAAeA,sBAAsB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}