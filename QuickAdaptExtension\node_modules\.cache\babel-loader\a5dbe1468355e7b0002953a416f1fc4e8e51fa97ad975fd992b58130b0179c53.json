{"ast": null, "code": "import{Box,Button,FormControl,IconButton,MenuItem,Popover,Select,TextField,ToggleButton,ToggleButtonGroup,Typography}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import{useEffect,useState}from\"react\";import useDrawerStore from\"../../../store/drawerStore\";import React from\"react\";import{ChromePicker}from\"react-color\";import{GetGudeDetailsByGuideId}from\"../../../services/GuideListServices\";import userSession from\"../../../store/userSession\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ButtonSetting=_ref=>{let{settingAnchorEl,handleCloseSettingPopup,guideListByOrg,loading,handleApplyChanges,buttonInfo,updatedGuideData}=_ref;const{t:translate}=useTranslation();const[currentButtonName,setCurrentButtonName]=useState(\"\");const[currentSelectedColor,setCurrentSelectedColor]=useState({color:\"\",target:\"\"});const{ButtonsDropdown,setButtonsDropdown,toolTipGuideMetaData,updateTooltipButtonAction,btnidss,setBtnIdss,currentStep,setCurrentStep,highlightedButton,getCurrentButtonInfo,interactionData,createWithAI}=useDrawerStore(state=>state);const[selectedTab,setSelectedTab]=useState(\"new-tab\");const[selectedInteraction,setSelectedInteraction]=useState(\"\");const[colorPickerAnchorEl,setColorPickerAnchorEl]=useState(null);const[selectedActions,setSelectActions]=useState(\"close\");const[targetURL,setTargetURL]=useState(\"\");const[targetURLError,setTargetURLError]=useState(\"\");const{setCurrentGuideId,currentGuideId,getCurrentGuideId}=userSession(state=>state);const[tempColors,setTempColors]=useState({backgroundColor:\"#5F9EA0\",borderColor:\"#70afaf\",color:\"#ffffff\"});useEffect(()=>{// Only fetch button info when we have valid container and button IDs\nif(settingAnchorEl.containerId&&settingAnchorEl.buttonId){const result=getCurrentButtonInfo(settingAnchorEl.containerId,settingAnchorEl.buttonId);// Set all button properties from the result\nif(result){// Set button name\nif(result.title){setCurrentButtonName(result.title);}// Set button action\nif(result.selectedActions){setSelectActions(result.selectedActions);}// Set target URL\nif(result.targetURL){setTargetURL(result.targetURL);}// Set colors\nsetTempColors({backgroundColor:result.bgColor||\"#5F9EA0\",borderColor:result.borderColor||\"#70afaf\",color:result.textColor||\"#ffffff\"});}}},[settingAnchorEl.containerId,settingAnchorEl.buttonId,getCurrentButtonInfo]);useEffect(()=>{const fetchGuideDetails=async()=>{if(currentGuideId&&currentGuideId!==\"\"){var _data$GuideDetails,_data$GuideDetails$Gu,_guideStep$Design,_guideStep$Design$Got;const data=await GetGudeDetailsByGuideId(currentGuideId,createWithAI,interactionData);const guideStep=data===null||data===void 0?void 0:(_data$GuideDetails=data.GuideDetails)===null||_data$GuideDetails===void 0?void 0:(_data$GuideDetails$Gu=_data$GuideDetails.GuideStep)===null||_data$GuideDetails$Gu===void 0?void 0:_data$GuideDetails$Gu[currentStep-1];const gotoNextButtonId=guideStep===null||guideStep===void 0?void 0:(_guideStep$Design=guideStep.Design)===null||_guideStep$Design===void 0?void 0:(_guideStep$Design$Got=_guideStep$Design.GotoNext)===null||_guideStep$Design$Got===void 0?void 0:_guideStep$Design$Got.ButtonId;if(btnidss===buttonInfo.id){var _guideStep$ButtonSect,_guideStep$ButtonSect2,_guideStep$ButtonSect3;const matchingButton=guideStep===null||guideStep===void 0?void 0:(_guideStep$ButtonSect=guideStep.ButtonSection)===null||_guideStep$ButtonSect===void 0?void 0:(_guideStep$ButtonSect2=_guideStep$ButtonSect[0])===null||_guideStep$ButtonSect2===void 0?void 0:(_guideStep$ButtonSect3=_guideStep$ButtonSect2.CustomButtons)===null||_guideStep$ButtonSect3===void 0?void 0:_guideStep$ButtonSect3.find(btn=>btn.ButtonId===buttonInfo.id);setSelectActions(btnidss===buttonInfo.id?\"Next\":\"close\");}else if(buttonInfo){var _buttonInfo$actions;setSelectActions(((_buttonInfo$actions=buttonInfo.actions)===null||_buttonInfo$actions===void 0?void 0:_buttonInfo$actions.value)||\"close\");}}};fetchGuideDetails();if(settingAnchorEl.value&&buttonInfo){var _buttonInfo$actions2,_buttonInfo$actions3,_buttonInfo$actions4,_buttonInfo$actions5,_buttonInfo$actions6;setCurrentButtonName(buttonInfo.name);setSelectedInteraction(((_buttonInfo$actions2=buttonInfo.actions)===null||_buttonInfo$actions2===void 0?void 0:_buttonInfo$actions2.interaction)||\"\");setSelectActions(((_buttonInfo$actions3=buttonInfo.actions)===null||_buttonInfo$actions3===void 0?void 0:_buttonInfo$actions3.value)||\"\");setTargetURL(((_buttonInfo$actions4=buttonInfo.actions)===null||_buttonInfo$actions4===void 0?void 0:_buttonInfo$actions4.targetURL)||((_buttonInfo$actions5=buttonInfo.actions)===null||_buttonInfo$actions5===void 0?void 0:_buttonInfo$actions5.targetUrl)||\"\");setSelectedTab(((_buttonInfo$actions6=buttonInfo.actions)===null||_buttonInfo$actions6===void 0?void 0:_buttonInfo$actions6.tab)||\"\");setTempColors({backgroundColor:buttonInfo.style.backgroundColor,borderColor:buttonInfo.style.borderColor,color:buttonInfo.style.color});}},[settingAnchorEl.value,buttonInfo,settingAnchorEl.containerId,settingAnchorEl.buttonId,getCurrentButtonInfo,btnidss,currentGuideId,currentStep]);const handleChangeActions=e=>{const v=e.target.value;setSelectActions(v);if(ButtonsDropdown===currentButtonName){const v=e.target.value;setSelectActions(v);}};useEffect(()=>{},[ButtonsDropdown]);const handleChangeTabs=event=>{setSelectedTab(event.target.value);};const handleColorChange=(e,targetName)=>{const value=e.hex;setTempColors(prev=>({...prev,[currentSelectedColor.target]:currentSelectedColor.target===\"borderColor\"?`2px solid ${value}`:value}));setCurrentSelectedColor(prevState=>{return{...prevState,color:value};});};const validateTargetURL=url=>{if(selectedActions===\"open-url\"){if(!url){return\"URL is required\";}try{new URL(url);return\"\";}catch(error){return\"Invalid URL\";}}return\"\";};const handleChanges=()=>{const targetURLError=validateTargetURL(targetURL);setTargetURLError(targetURLError);if(targetURLError){return;}// Retain the previously saved button name if the field is empty\nlet buttonNameToUpdate=currentButtonName;// If the current button name is empty, try to get it from buttonInfo or getCurrentButtonInfo\nif(!buttonNameToUpdate||!buttonNameToUpdate.trim()){if(buttonInfo&&buttonInfo.name){buttonNameToUpdate=buttonInfo.name;}else if(settingAnchorEl.containerId&&settingAnchorEl.buttonId){const result=getCurrentButtonInfo(settingAnchorEl.containerId,settingAnchorEl.buttonId);if(result!==null&&result!==void 0&&result.title){buttonNameToUpdate=result.title;}}}// Apply the changes with the updated button name\nhandleApplyChanges(tempColors,selectedActions,targetURL,selectedInteraction,buttonNameToUpdate,selectedTab);};const handleURLChange=e=>{const newURL=e.target.value;setTargetURL(newURL);};return/*#__PURE__*/_jsxs(Popover,{id:\"btn-setting-toolbar\",open:Boolean(settingAnchorEl.value),anchorEl:settingAnchorEl.value,onClose:()=>handleCloseSettingPopup(settingAnchorEl.containerId,settingAnchorEl.buttonId)// slotProps={{\n// \troot: {\n// \t\t// instead of writing sx on popover write here it also target to root and more clear\n// \t\tsx: {\n// \t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\n// \t\t},\n// \t},\n// }}\n,slotProps:{paper:{sx:{boxShadow:\"none !important\",backgroundColor:\"transparent\"}}},children:[/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup qadpt-tltbtnprop\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Properties\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":translate(\"Close\"),onClick:()=>handleCloseSettingPopup(settingAnchorEl.containerId,settingAnchorEl.buttonId),children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{marginBottom:\"16px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",my:\"5px\"},children:translate(\"Button Name\")}),/*#__PURE__*/_jsx(TextField,{value:currentButtonName,size:\"small\",sx:{mb:\"5px\",border:\"1px solid #ccc\",borderRadius:\"4px\",\"& .MuiOutlinedInput-root\":{height:\"35px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},placeholder:translate(\"Button Name\"),onChange:e=>setCurrentButtonName(e.target.value)}),/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",mb:\"5px\"},children:translate(\"Button Action\")}),/*#__PURE__*/_jsxs(Select,{value:selectedActions,onChange:handleChangeActions,sx:{mb:\"5px\",border:\"1px solid #ccc\",borderRadius:\"4px\",textAlign:\"left\",\"& .MuiSelect-select\":{padding:\"8px\"},\"& .MuiOutlinedInput-root\":{height:\"35px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},MenuProps:{slotProps:{// disablePortal: true,\nroot:{disablePortal:true,disableEnforceFocus:true,// Prevents focus trapping\ndisableAutoFocus:true,// Allows the input field to gain focus\ndisableRestoreFocus:true,sx:{zIndex:theme=>theme.zIndex.tooltip+1200}}}},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"close\",children:translate(\"Close\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"open-url\",children:translate(\"Open URL\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"previous\",children:translate(\"Previous\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"Next\",children:translate(\"Next\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"Restart\",children:translate(\"Restart\")})]}),selectedActions===\"open-url\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",my:\"5px\"},children:translate(\"Enter URL\")}),/*#__PURE__*/_jsx(TextField,{value:targetURL,size:\"small\",placeholder:\"https://quixy.com\",onChange:e=>{const newURL=e.target.value;setTargetURL(newURL);// Update the `targetURL` state with the new value\nhandleURLChange(e);// Update the selectedButton.targetURL with the new value\n},error:!!targetURLError,helperText:targetURLError?translate(targetURLError):\"\"}),/*#__PURE__*/_jsx(ToggleButtonGroup,{value:selectedTab,onChange:handleChangeTabs,exclusive:true,\"aria-label\":translate(\"open in tab\"),sx:{gap:\"5px\",marginY:\"5px\",height:\"35px\"},children:[\"new-tab\",\"same-tab\"].map(tab=>{return/*#__PURE__*/_jsx(ToggleButton,{value:tab,\"aria-label\":\"new tab\",sx:{border:\"1px solid #7EA8A5\",textTransform:\"capitalize\",color:\"#000\",borderRadius:\"4px\",flex:1,padding:\"0 !important\",\"&.Mui-selected\":{backgroundColor:\"var(--border-color)\",color:\"#000\",border:\"2px solid #7EA8A5\"},\"&:hover\":{backgroundColor:\"#f5f5f5\"},\"&:last-child\":{borderLeft:\"1px solid var(--primarycolor) !important\"// Remove left border for the last button\n}},children:tab});})})]}):null]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Background\")}),/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:tempColors.backgroundColor,width:\"20px\",height:\"20px\",borderRadius:\"50%\"},component:\"div\",role:\"button\",onClick:e=>{setColorPickerAnchorEl(e.currentTarget);setCurrentSelectedColor({color:tempColors.backgroundColor,target:\"backgroundColor\"});}})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Border\")}),/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:tempColors.borderColor.split(\" \")[2],width:\"20px\",height:\"20px\",borderRadius:\"50%\"},component:\"div\",role:\"button\",onClick:e=>{setColorPickerAnchorEl(e.currentTarget);setCurrentSelectedColor({color:`2px solid ${tempColors.borderColor.split(\" \")[2]}`,target:\"borderColor\"});}})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Text\")}),/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:tempColors.color,width:\"20px\",height:\"20px\",borderRadius:\"50%\"},component:\"div\",role:\"button\",onClick:e=>{setColorPickerAnchorEl(e.currentTarget);setCurrentSelectedColor({color:tempColors.color,target:\"color\"});}})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleChanges,className:\"qadpt-btn\",children:translate(\"Apply\")})})]})}),/*#__PURE__*/_jsx(Popover,{open:Boolean(colorPickerAnchorEl),anchorEl:colorPickerAnchorEl,onClose:()=>{setColorPickerAnchorEl(null);setCurrentSelectedColor({color:\"\",target:\"\"});},anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"top\",horizontal:\"center\"},id:\"color-picker\",slotProps:{root:{// instead of writing sx on popover write here it also target to root and more clear\nsx:{zIndex:theme=>theme.zIndex.tooltip+1000}}},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(ChromePicker,{color:currentSelectedColor.color,onChange:e=>handleColorChange(e,currentSelectedColor.target)}),/*#__PURE__*/_jsx(\"style\",{children:`\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `})]})})]});};export default ButtonSetting;", "map": {"version": 3, "names": ["Box", "<PERSON><PERSON>", "FormControl", "IconButton", "MenuItem", "Popover", "Select", "TextField", "ToggleButton", "ToggleButtonGroup", "Typography", "CloseIcon", "useEffect", "useState", "useDrawerStore", "React", "ChromePicker", "GetGudeDetailsByGuideId", "userSession", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ButtonSetting", "_ref", "settingAnchorEl", "handleCloseSettingPopup", "guideListByOrg", "loading", "handleApplyChanges", "buttonInfo", "updatedGuideData", "t", "translate", "currentButtonName", "setCurrentButtonName", "currentSelectedColor", "setCurrentSelectedColor", "color", "target", "ButtonsDropdown", "setButtonsDropdown", "toolTipGuideMetaData", "updateTooltipButtonAction", "btnidss", "setBtnIdss", "currentStep", "setCurrentStep", "highlighted<PERSON><PERSON><PERSON>", "getCurrentButtonInfo", "interactionData", "createWithAI", "state", "selectedTab", "setSelectedTab", "selectedInteraction", "setSelectedInteraction", "colorPickerAnchorEl", "setColorPickerAnchorEl", "selectedActions", "setSelectActions", "targetURL", "setTargetURL", "targetURLError", "setTargetURLError", "setCurrentGuideId", "currentGuideId", "getCurrentGuideId", "tempColors", "setTempColors", "backgroundColor", "borderColor", "containerId", "buttonId", "result", "title", "bgColor", "textColor", "fetchGuideDetails", "_data$GuideDetails", "_data$GuideDetails$Gu", "_guideStep$Design", "_guideStep$Design$Got", "data", "guideStep", "GuideDetails", "GuideStep", "gotoNextButtonId", "Design", "GotoNext", "ButtonId", "id", "_guideStep$ButtonSect", "_guideStep$ButtonSect2", "_guideStep$ButtonSect3", "matchingButton", "ButtonSection", "CustomButtons", "find", "btn", "_buttonInfo$actions", "actions", "value", "_buttonInfo$actions2", "_buttonInfo$actions3", "_buttonInfo$actions4", "_buttonInfo$actions5", "_buttonInfo$actions6", "name", "interaction", "targetUrl", "tab", "style", "handleChangeActions", "e", "v", "handleChangeTabs", "event", "handleColorChange", "targetName", "hex", "prev", "prevState", "validateTargetURL", "url", "URL", "error", "handleChanges", "buttonNameToUpdate", "trim", "handleURLChange", "newURL", "open", "Boolean", "anchorEl", "onClose", "slotProps", "paper", "sx", "boxShadow", "children", "className", "size", "onClick", "fullWidth", "marginBottom", "fontSize", "fontWeight", "my", "mb", "border", "borderRadius", "height", "placeholder", "onChange", "textAlign", "padding", "MenuProps", "root", "disable<PERSON><PERSON><PERSON>", "disableEnforceFocus", "disableAutoFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zIndex", "theme", "tooltip", "helperText", "exclusive", "gap", "marginY", "map", "textTransform", "flex", "borderLeft", "width", "component", "role", "currentTarget", "split", "variant", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/ButtonSetting.tsx"], "sourcesContent": ["import {\r\n\tAutocomplete,\r\n\tBox,\r\n\tButton,\r\n\tFormControl,\r\n\tIconButton,\r\n\tMenuItem,\r\n\tPopover,\r\n\tSelect,\r\n\tSelectChangeEvent,\r\n\tTextField,\r\n\tToggleButton,\r\n\tToggleButtonGroup,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useEffect, useState } from \"react\";\r\nimport useDrawerStore, { TInteractionValue } from \"../../../store/drawerStore\";\r\nimport React from \"react\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { GetGudeDetailsByGuideId } from \"../../../services/GuideListServices\";\r\nimport userSession from \"../../../store/userSession\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ButtonSetting = ({\r\n\tsettingAnchorEl,\r\n\thandleCloseSettingPopup,\r\n\tguideListByOrg,\r\n\tloading,\r\n\thandleApplyChanges,\r\n\tbuttonInfo,\r\n\tupdatedGuideData,\r\n}: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [currentButtonName, setCurrentButtonName] = useState(\"\");\r\n\tconst [currentSelectedColor, setCurrentSelectedColor] = useState({\r\n\t\tcolor: \"\",\r\n\t\ttarget: \"\",\r\n\t});\r\n\tconst {\r\n\t\tButtonsDropdown,\r\n\t\tsetButtonsDropdown,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tbtnidss,\r\n\t\tsetBtnIdss,\r\n\t\tcurrentStep,\r\n\t\tsetCurrentStep,\r\n\t\thighlightedButton,\r\n\t\tgetCurrentButtonInfo,\r\n\t\tinteractionData,\r\n\t\tcreateWithAI,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst [selectedTab, setSelectedTab] = useState(\"new-tab\");\r\n\tconst [selectedInteraction, setSelectedInteraction] = useState<string | null>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLDivElement | null>(null);\r\n\tconst [selectedActions, setSelectActions] = useState<TInteractionValue>(\"close\");\r\n\tconst [targetURL, setTargetURL] = useState(\"\");\r\n\tconst [targetURLError, setTargetURLError] = useState(\"\");\r\n\tconst { setCurrentGuideId, currentGuideId, getCurrentGuideId } = userSession((state: any) => state);\r\n\tconst [tempColors, setTempColors] = useState({\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tborderColor: \"#70afaf\",\r\n\t\tcolor: \"#ffffff\",\r\n\t});\r\n\r\n\tuseEffect(() => {\r\n\t\t// Only fetch button info when we have valid container and button IDs\r\n\t\tif (settingAnchorEl.containerId && settingAnchorEl.buttonId) {\r\n\t\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\t\t// Set all button properties from the result\r\n\t\t\tif (result) {\r\n\t\t\t\t// Set button name\r\n\t\t\t\tif (result.title) {\r\n\t\t\t\t\tsetCurrentButtonName(result.title);\r\n\t\t\t\t}\r\n\t\t\t\t// Set button action\r\n\t\t\t\tif (result.selectedActions) {\r\n\t\t\t\t\tsetSelectActions(result.selectedActions as TInteractionValue);\r\n\t\t\t\t}\r\n\t\t\t\t// Set target URL\r\n\t\t\t\tif (result.targetURL) {\r\n\t\t\t\t\tsetTargetURL(result.targetURL);\r\n\t\t\t\t}\r\n\t\t\t\t// Set colors\r\n\t\t\t\tsetTempColors({\r\n\t\t\t\t\tbackgroundColor: result.bgColor || \"#5F9EA0\",\r\n\t\t\t\t\tborderColor: result.borderColor || \"#70afaf\",\r\n\t\t\t\t\tcolor: result.textColor || \"#ffffff\",\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\tif (currentGuideId && currentGuideId !== \"\") {\r\n\t\t\t\tconst data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);\r\n\t\t\t\tconst guideStep = data?.GuideDetails?.GuideStep?.[currentStep - 1];\r\n\t\t\t\tconst gotoNextButtonId = guideStep?.Design?.GotoNext?.ButtonId;\r\n\r\n\t\t\t\tif (btnidss === buttonInfo.id) {\r\n\t\t\t\t\tconst matchingButton = guideStep?.ButtonSection?.[0]?.CustomButtons?.find(\r\n\t\t\t\t\t\t(btn: any) => btn.ButtonId === buttonInfo.id\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tsetSelectActions(btnidss === buttonInfo.id ? \"Next\" : \"close\");\r\n\t\t\t\t} else if (buttonInfo) {\r\n\t\t\t\t\tsetSelectActions(buttonInfo.actions?.value || \"close\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\tif (settingAnchorEl.value && buttonInfo) {\r\n\t\t\tsetCurrentButtonName(buttonInfo.name);\r\n\t\t\tsetSelectedInteraction(buttonInfo.actions?.interaction || \"\");\r\n\t\t\tsetSelectActions(buttonInfo.actions?.value || \"\");\r\n\t\t\tsetTargetURL(buttonInfo.actions?.targetURL || buttonInfo.actions?.targetUrl || \"\");\r\n\t\t\tsetSelectedTab(buttonInfo.actions?.tab || \"\");\r\n\t\t\tsetTempColors({\r\n\t\t\t\tbackgroundColor: buttonInfo.style.backgroundColor,\r\n\t\t\t\tborderColor: buttonInfo.style.borderColor,\r\n\t\t\t\tcolor: buttonInfo.style.color,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep]);\r\n\r\n\tconst handleChangeActions = (e: SelectChangeEvent) => {\r\n\t\tconst v = e.target.value;\r\n\t\tsetSelectActions(v as TInteractionValue);\r\n\t\tif (ButtonsDropdown === currentButtonName) {\r\n\t\t\tconst v = e.target.value;\r\n\t\t\tsetSelectActions(v as TInteractionValue);\r\n\t\t}\r\n\t};\r\n\r\n\tuseEffect(() => {}, [ButtonsDropdown]);\r\n\r\n\tconst handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSelectedTab((event.target as HTMLInputElement).value);\r\n\t};\r\n\tconst handleColorChange = (e: any, targetName: any) => {\r\n\t\tconst value = e.hex;\r\n\t\tsetTempColors((prev) => ({\r\n\t\t\t...prev,\r\n\t\t\t[currentSelectedColor.target]: currentSelectedColor.target === \"borderColor\" ? `2px solid ${value}` : value,\r\n\t\t}));\r\n\t\tsetCurrentSelectedColor((prevState) => {\r\n\t\t\treturn {\r\n\t\t\t\t...prevState,\r\n\t\t\t\tcolor: value,\r\n\t\t\t};\r\n\t\t});\r\n\t};\r\n\tconst validateTargetURL = (url: string) => {\r\n\t\tif (selectedActions === \"open-url\") {\r\n\t\t\tif (!url) {\r\n\t\t\t\treturn \"URL is required\";\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tnew URL(url);\r\n\t\t\t\treturn \"\";\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn \"Invalid URL\";\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn \"\";\r\n\t};\r\n\r\n\tconst handleChanges = () => {\r\n\t\tconst targetURLError = validateTargetURL(targetURL);\r\n\t\tsetTargetURLError(targetURLError);\r\n\r\n\t\tif (targetURLError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Retain the previously saved button name if the field is empty\r\n\t\tlet buttonNameToUpdate = currentButtonName;\r\n\t\t// If the current button name is empty, try to get it from buttonInfo or getCurrentButtonInfo\r\n\t\tif (!buttonNameToUpdate || !buttonNameToUpdate.trim()) {\r\n\t\t\tif (buttonInfo && buttonInfo.name) {\r\n\t\t\t\tbuttonNameToUpdate = buttonInfo.name;\r\n\t\t\t} else if (settingAnchorEl.containerId && settingAnchorEl.buttonId) {\r\n\t\t\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\t\t\tif (result?.title) {\r\n\t\t\t\t\tbuttonNameToUpdate = result.title;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Apply the changes with the updated button name\r\n\r\n\t\thandleApplyChanges(tempColors, selectedActions, targetURL, selectedInteraction, buttonNameToUpdate, selectedTab);\r\n\t};\r\n\tconst handleURLChange = (e: any) => {\r\n\t\tconst newURL = e.target.value;\r\n\t\tsetTargetURL(newURL);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<Popover\r\n\t\t\tid={\"btn-setting-toolbar\"}\r\n\t\t\topen={Boolean(settingAnchorEl.value)}\r\n\t\t\tanchorEl={settingAnchorEl.value}\r\n\t\t\tonClose={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t// slotProps={{\r\n\t\t\t// \troot: {\r\n\t\t\t// \t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t// \t\tsx: {\r\n\t\t\t// \t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t// \t\t},\r\n\t\t\t// \t},\r\n\t\t\t// }}\r\n\t\t\tslotProps={{\r\n\t\t\t\tpaper: {\r\n\t\t\t\t  sx: {\r\n\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\"\r\n\t\t\t\t  },\r\n\t\t\t\t},\r\n\t\t\t  }}\t  \r\n\t\t>\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup qadpt-tltbtnprop\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Properties\")}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label={translate(\"Close\")}\r\n\t\t\t\t\t\t\tonClick={() => handleCloseSettingPopup(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsx={{ marginBottom: \"16px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Button Name\")}</Typography>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={currentButtonName}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tplaceholder={translate(\"Button Name\")}\r\n\t\t\t\t\t\t\t\tonChange={(e) => setCurrentButtonName(e.target.value)}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>{translate(\"Button Action\")}</Typography>\r\n\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\tvalue={selectedActions}\r\n\t\t\t\t\t\t\t\tonChange={handleChangeActions}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": {\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\t\t\tslotProps: {\r\n\t\t\t\t\t\t\t\t\t\t// disablePortal: true,\r\n\t\t\t\t\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t\t\t\t\tdisablePortal: true,\r\n\t\t\t\t\t\t\t\t\t\t\tdisableEnforceFocus: true, // Prevents focus trapping\r\n\t\t\t\t\t\t\t\t\t\t\tdisableAutoFocus: true, // Allows the input field to gain focus\r\n\t\t\t\t\t\t\t\t\t\t\tdisableRestoreFocus: true,\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1200,\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"close\">{translate(\"Close\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"open-url\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"previous\">{translate(\"Previous\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Next\">{translate(\"Next\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Restart\">{translate(\"Restart\")}</MenuItem>\r\n\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t{selectedActions === \"open-url\" ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>{translate(\"Enter URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\tvalue={targetURL}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"https://quixy.com\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconst newURL = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\t\tsetTargetURL(newURL); // Update the `targetURL` state with the new value\r\n\t\t\t\t\t\t\t\t\t\t\thandleURLChange(e); // Update the selectedButton.targetURL with the new value\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\terror={!!targetURLError}\r\n\t\t\t\t\t\t\t\t\t\thelperText={targetURLError ? translate(targetURLError) : \"\"}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t<ToggleButtonGroup\r\n\t\t\t\t\t\t\t\t\t\tvalue={selectedTab}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleChangeTabs}\r\n\t\t\t\t\t\t\t\t\t\texclusive\r\n\t\t\t\t\t\t\t\t\t\taria-label={translate(\"open in tab\")}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmarginY: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"35px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{[\"new-tab\", \"same-tab\"].map((tab) => {\r\n\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ToggleButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"new tab\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-selected\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"2px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:last-child\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderLeft: \"1px solid var(--primarycolor) !important\", // Remove left border for the last button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{tab}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</ToggleButton>\r\n\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t</ToggleButtonGroup>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</FormControl>\r\n\r\n\t\t\t\t\t\t{/* <Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\" }}>Button Color</Typography> */}\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\")}</div>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: tempColors.backgroundColor,\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\tsetColorPickerAnchorEl(e.currentTarget);\r\n\t\t\t\t\t\t\t\t\tsetCurrentSelectedColor({\r\n\t\t\t\t\t\t\t\t\t\tcolor: tempColors.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\ttarget: \"backgroundColor\",\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\")}</div>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: tempColors.borderColor.split(\" \")[2],\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\tsetColorPickerAnchorEl(e.currentTarget);\r\n\t\t\t\t\t\t\t\t\tsetCurrentSelectedColor({\r\n\t\t\t\t\t\t\t\t\t\tcolor: `2px solid ${tempColors.borderColor.split(\" \")[2]}`,\r\n\t\t\t\t\t\t\t\t\t\ttarget: \"borderColor\",\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Text\")}</div>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: tempColors.color,\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\tsetColorPickerAnchorEl(e.currentTarget);\r\n\t\t\t\t\t\t\t\t\tsetCurrentSelectedColor({\r\n\t\t\t\t\t\t\t\t\t\tcolor: tempColors.color,\r\n\t\t\t\t\t\t\t\t\t\ttarget: \"color\",\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<Popover\r\n\t\t\t\topen={Boolean(colorPickerAnchorEl)}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={() => {\r\n\t\t\t\t\tsetColorPickerAnchorEl(null);\r\n\t\t\t\t\tsetCurrentSelectedColor({ color: \"\", target: \"\" });\r\n\t\t\t\t}}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"color-picker\"\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentSelectedColor.color}\r\n\t\t\t\t\t\tonChange={(e) => handleColorChange(e, currentSelectedColor.target)}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</Popover>\r\n\t);\r\n};\r\n\r\nexport default ButtonSetting;\r\n"], "mappings": "AAAA,OAECA,GAAG,CACHC,MAAM,CACNC,WAAW,CACXC,UAAU,CACVC,QAAQ,CACRC,OAAO,CACPC,MAAM,CAENC,SAAS,CACTC,YAAY,CACZC,iBAAiB,CACjBC,UAAU,KACJ,eAAe,CACtB,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,OAASC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC3C,MAAO,CAAAC,cAAc,KAA6B,4BAA4B,CAC9E,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,YAAY,KAAqB,aAAa,CACvD,OAASC,uBAAuB,KAAQ,qCAAqC,CAC7E,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAQX,IARY,CACtBC,eAAe,CACfC,uBAAuB,CACvBC,cAAc,CACdC,OAAO,CACPC,kBAAkB,CAClBC,UAAU,CACVC,gBACI,CAAC,CAAAP,IAAA,CACL,KAAM,CAAEQ,CAAC,CAAEC,SAAU,CAAC,CAAGjB,cAAc,CAAC,CAAC,CACzC,KAAM,CAACkB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC0B,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3B,QAAQ,CAAC,CAChE4B,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EACT,CAAC,CAAC,CACF,KAAM,CACLC,eAAe,CACfC,kBAAkB,CAClBC,oBAAoB,CACpBC,yBAAyB,CACzBC,OAAO,CACPC,UAAU,CACVC,WAAW,CACXC,cAAc,CACdC,iBAAiB,CACjBC,oBAAoB,CACpBC,eAAe,CACfC,YACD,CAAC,CAAGxC,cAAc,CAAEyC,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,SAAS,CAAC,CACzD,KAAM,CAAC6C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG9C,QAAQ,CAAgB,EAAE,CAAC,CACjF,KAAM,CAAC+C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGhD,QAAQ,CAAwB,IAAI,CAAC,CAC3F,KAAM,CAACiD,eAAe,CAAEC,gBAAgB,CAAC,CAAGlD,QAAQ,CAAoB,OAAO,CAAC,CAChF,KAAM,CAACmD,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqD,cAAc,CAAEC,iBAAiB,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAEuD,iBAAiB,CAAEC,cAAc,CAAEC,iBAAkB,CAAC,CAAGpD,WAAW,CAAEqC,KAAU,EAAKA,KAAK,CAAC,CACnG,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAG3D,QAAQ,CAAC,CAC5C4D,eAAe,CAAE,SAAS,CAC1BC,WAAW,CAAE,SAAS,CACtBjC,KAAK,CAAE,SACR,CAAC,CAAC,CAEF7B,SAAS,CAAC,IAAM,CACf;AACA,GAAIgB,eAAe,CAAC+C,WAAW,EAAI/C,eAAe,CAACgD,QAAQ,CAAE,CAC5D,KAAM,CAAAC,MAAM,CAAGzB,oBAAoB,CAACxB,eAAe,CAAC+C,WAAW,CAAE/C,eAAe,CAACgD,QAAQ,CAAC,CAC1F;AACA,GAAIC,MAAM,CAAE,CACX;AACA,GAAIA,MAAM,CAACC,KAAK,CAAE,CACjBxC,oBAAoB,CAACuC,MAAM,CAACC,KAAK,CAAC,CACnC,CACA;AACA,GAAID,MAAM,CAACf,eAAe,CAAE,CAC3BC,gBAAgB,CAACc,MAAM,CAACf,eAAoC,CAAC,CAC9D,CACA;AACA,GAAIe,MAAM,CAACb,SAAS,CAAE,CACrBC,YAAY,CAACY,MAAM,CAACb,SAAS,CAAC,CAC/B,CACA;AACAQ,aAAa,CAAC,CACbC,eAAe,CAAEI,MAAM,CAACE,OAAO,EAAI,SAAS,CAC5CL,WAAW,CAAEG,MAAM,CAACH,WAAW,EAAI,SAAS,CAC5CjC,KAAK,CAAEoC,MAAM,CAACG,SAAS,EAAI,SAC5B,CAAC,CAAC,CACH,CACD,CACD,CAAC,CAAE,CAACpD,eAAe,CAAC+C,WAAW,CAAE/C,eAAe,CAACgD,QAAQ,CAAExB,oBAAoB,CAAC,CAAC,CAEjFxC,SAAS,CAAC,IAAM,CACf,KAAM,CAAAqE,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAIZ,cAAc,EAAIA,cAAc,GAAK,EAAE,CAAE,KAAAa,kBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAC5C,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAArE,uBAAuB,CAACoD,cAAc,CAAEf,YAAY,CAAED,eAAe,CAAC,CACzF,KAAM,CAAAkC,SAAS,CAAGD,IAAI,SAAJA,IAAI,kBAAAJ,kBAAA,CAAJI,IAAI,CAAEE,YAAY,UAAAN,kBAAA,kBAAAC,qBAAA,CAAlBD,kBAAA,CAAoBO,SAAS,UAAAN,qBAAA,iBAA7BA,qBAAA,CAAgClC,WAAW,CAAG,CAAC,CAAC,CAClE,KAAM,CAAAyC,gBAAgB,CAAGH,SAAS,SAATA,SAAS,kBAAAH,iBAAA,CAATG,SAAS,CAAEI,MAAM,UAAAP,iBAAA,kBAAAC,qBAAA,CAAjBD,iBAAA,CAAmBQ,QAAQ,UAAAP,qBAAA,iBAA3BA,qBAAA,CAA6BQ,QAAQ,CAE9D,GAAI9C,OAAO,GAAKd,UAAU,CAAC6D,EAAE,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC9B,KAAM,CAAAC,cAAc,CAAGX,SAAS,SAATA,SAAS,kBAAAQ,qBAAA,CAATR,SAAS,CAAEY,aAAa,UAAAJ,qBAAA,kBAAAC,sBAAA,CAAxBD,qBAAA,CAA2B,CAAC,CAAC,UAAAC,sBAAA,kBAAAC,sBAAA,CAA7BD,sBAAA,CAA+BI,aAAa,UAAAH,sBAAA,iBAA5CA,sBAAA,CAA8CI,IAAI,CACvEC,GAAQ,EAAKA,GAAG,CAACT,QAAQ,GAAK5D,UAAU,CAAC6D,EAC3C,CAAC,CAED/B,gBAAgB,CAAChB,OAAO,GAAKd,UAAU,CAAC6D,EAAE,CAAG,MAAM,CAAG,OAAO,CAAC,CAC/D,CAAC,IAAM,IAAI7D,UAAU,CAAE,KAAAsE,mBAAA,CACtBxC,gBAAgB,CAAC,EAAAwC,mBAAA,CAAAtE,UAAU,CAACuE,OAAO,UAAAD,mBAAA,iBAAlBA,mBAAA,CAAoBE,KAAK,GAAI,OAAO,CAAC,CACvD,CACD,CACD,CAAC,CAEDxB,iBAAiB,CAAC,CAAC,CAEnB,GAAIrD,eAAe,CAAC6E,KAAK,EAAIxE,UAAU,CAAE,KAAAyE,oBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CACxCxE,oBAAoB,CAACL,UAAU,CAAC8E,IAAI,CAAC,CACrCpD,sBAAsB,CAAC,EAAA+C,oBAAA,CAAAzE,UAAU,CAACuE,OAAO,UAAAE,oBAAA,iBAAlBA,oBAAA,CAAoBM,WAAW,GAAI,EAAE,CAAC,CAC7DjD,gBAAgB,CAAC,EAAA4C,oBAAA,CAAA1E,UAAU,CAACuE,OAAO,UAAAG,oBAAA,iBAAlBA,oBAAA,CAAoBF,KAAK,GAAI,EAAE,CAAC,CACjDxC,YAAY,CAAC,EAAA2C,oBAAA,CAAA3E,UAAU,CAACuE,OAAO,UAAAI,oBAAA,iBAAlBA,oBAAA,CAAoB5C,SAAS,KAAA6C,oBAAA,CAAI5E,UAAU,CAACuE,OAAO,UAAAK,oBAAA,iBAAlBA,oBAAA,CAAoBI,SAAS,GAAI,EAAE,CAAC,CAClFxD,cAAc,CAAC,EAAAqD,oBAAA,CAAA7E,UAAU,CAACuE,OAAO,UAAAM,oBAAA,iBAAlBA,oBAAA,CAAoBI,GAAG,GAAI,EAAE,CAAC,CAC7C1C,aAAa,CAAC,CACbC,eAAe,CAAExC,UAAU,CAACkF,KAAK,CAAC1C,eAAe,CACjDC,WAAW,CAAEzC,UAAU,CAACkF,KAAK,CAACzC,WAAW,CACzCjC,KAAK,CAAER,UAAU,CAACkF,KAAK,CAAC1E,KACzB,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAACb,eAAe,CAAC6E,KAAK,CAAExE,UAAU,CAAEL,eAAe,CAAC+C,WAAW,CAAE/C,eAAe,CAACgD,QAAQ,CAAExB,oBAAoB,CAAEL,OAAO,CAAEsB,cAAc,CAAEpB,WAAW,CAAC,CAAC,CAE1J,KAAM,CAAAmE,mBAAmB,CAAIC,CAAoB,EAAK,CACrD,KAAM,CAAAC,CAAC,CAAGD,CAAC,CAAC3E,MAAM,CAAC+D,KAAK,CACxB1C,gBAAgB,CAACuD,CAAsB,CAAC,CACxC,GAAI3E,eAAe,GAAKN,iBAAiB,CAAE,CAC1C,KAAM,CAAAiF,CAAC,CAAGD,CAAC,CAAC3E,MAAM,CAAC+D,KAAK,CACxB1C,gBAAgB,CAACuD,CAAsB,CAAC,CACzC,CACD,CAAC,CAED1G,SAAS,CAAC,IAAM,CAAC,CAAC,CAAE,CAAC+B,eAAe,CAAC,CAAC,CAEtC,KAAM,CAAA4E,gBAAgB,CAAIC,KAAoC,EAAK,CAClE/D,cAAc,CAAE+D,KAAK,CAAC9E,MAAM,CAAsB+D,KAAK,CAAC,CACzD,CAAC,CACD,KAAM,CAAAgB,iBAAiB,CAAGA,CAACJ,CAAM,CAAEK,UAAe,GAAK,CACtD,KAAM,CAAAjB,KAAK,CAAGY,CAAC,CAACM,GAAG,CACnBnD,aAAa,CAAEoD,IAAI,GAAM,CACxB,GAAGA,IAAI,CACP,CAACrF,oBAAoB,CAACG,MAAM,EAAGH,oBAAoB,CAACG,MAAM,GAAK,aAAa,CAAG,aAAa+D,KAAK,EAAE,CAAGA,KACvG,CAAC,CAAC,CAAC,CACHjE,uBAAuB,CAAEqF,SAAS,EAAK,CACtC,MAAO,CACN,GAAGA,SAAS,CACZpF,KAAK,CAAEgE,KACR,CAAC,CACF,CAAC,CAAC,CACH,CAAC,CACD,KAAM,CAAAqB,iBAAiB,CAAIC,GAAW,EAAK,CAC1C,GAAIjE,eAAe,GAAK,UAAU,CAAE,CACnC,GAAI,CAACiE,GAAG,CAAE,CACT,MAAO,iBAAiB,CACzB,CACA,GAAI,CACH,GAAI,CAAAC,GAAG,CAACD,GAAG,CAAC,CACZ,MAAO,EAAE,CACV,CAAE,MAAOE,KAAK,CAAE,CACf,MAAO,aAAa,CACrB,CACD,CACA,MAAO,EAAE,CACV,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAhE,cAAc,CAAG4D,iBAAiB,CAAC9D,SAAS,CAAC,CACnDG,iBAAiB,CAACD,cAAc,CAAC,CAEjC,GAAIA,cAAc,CAAE,CACnB,OACD,CAEA;AACA,GAAI,CAAAiE,kBAAkB,CAAG9F,iBAAiB,CAC1C;AACA,GAAI,CAAC8F,kBAAkB,EAAI,CAACA,kBAAkB,CAACC,IAAI,CAAC,CAAC,CAAE,CACtD,GAAInG,UAAU,EAAIA,UAAU,CAAC8E,IAAI,CAAE,CAClCoB,kBAAkB,CAAGlG,UAAU,CAAC8E,IAAI,CACrC,CAAC,IAAM,IAAInF,eAAe,CAAC+C,WAAW,EAAI/C,eAAe,CAACgD,QAAQ,CAAE,CACnE,KAAM,CAAAC,MAAM,CAAGzB,oBAAoB,CAACxB,eAAe,CAAC+C,WAAW,CAAE/C,eAAe,CAACgD,QAAQ,CAAC,CAC1F,GAAIC,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEC,KAAK,CAAE,CAClBqD,kBAAkB,CAAGtD,MAAM,CAACC,KAAK,CAClC,CACD,CACD,CAEA;AAEA9C,kBAAkB,CAACuC,UAAU,CAAET,eAAe,CAAEE,SAAS,CAAEN,mBAAmB,CAAEyE,kBAAkB,CAAE3E,WAAW,CAAC,CACjH,CAAC,CACD,KAAM,CAAA6E,eAAe,CAAIhB,CAAM,EAAK,CACnC,KAAM,CAAAiB,MAAM,CAAGjB,CAAC,CAAC3E,MAAM,CAAC+D,KAAK,CAC7BxC,YAAY,CAACqE,MAAM,CAAC,CACrB,CAAC,CAED,mBACC/G,KAAA,CAAClB,OAAO,EACPyF,EAAE,CAAE,qBAAsB,CAC1ByC,IAAI,CAAEC,OAAO,CAAC5G,eAAe,CAAC6E,KAAK,CAAE,CACrCgC,QAAQ,CAAE7G,eAAe,CAAC6E,KAAM,CAChCiC,OAAO,CAAEA,CAAA,GAAM7G,uBAAuB,CAACD,eAAe,CAAC+C,WAAW,CAAE/C,eAAe,CAACgD,QAAQ,CAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACA+D,SAAS,CAAE,CACVC,KAAK,CAAE,CACLC,EAAE,CAAE,CACJC,SAAS,CAAE,iBAAiB,CAC5BrE,eAAe,CAAE,aACjB,CACF,CACC,CAAE,CAAAsE,QAAA,eAEJ1H,IAAA,QACCyE,EAAE,CAAC,mBAAmB,CACtBkD,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cAE9CxH,KAAA,QAAKyH,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC7BxH,KAAA,QAAKyH,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eACnC1H,IAAA,QAAK2H,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAE3G,SAAS,CAAC,YAAY,CAAC,CAAM,CAAC,cAC5Df,IAAA,CAAClB,UAAU,EACV8I,IAAI,CAAC,OAAO,CACZ,aAAY7G,SAAS,CAAC,OAAO,CAAE,CAC/B8G,OAAO,CAAEA,CAAA,GAAMrH,uBAAuB,CAACD,eAAe,CAAC+C,WAAW,CAAE/C,eAAe,CAACgD,QAAQ,CAAE,CAAAmE,QAAA,cAE9F1H,IAAA,CAACV,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNY,KAAA,QAAKyH,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC9BxH,KAAA,CAACrB,WAAW,EACXiJ,SAAS,MACTN,EAAE,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAL,QAAA,eAE7B1H,IAAA,CAACX,UAAU,EAACmI,EAAE,CAAE,CAAEQ,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAAR,QAAA,CAAE3G,SAAS,CAAC,aAAa,CAAC,CAAa,CAAC,cAC5Gf,IAAA,CAACd,SAAS,EACTkG,KAAK,CAAEpE,iBAAkB,CACzB4G,IAAI,CAAC,OAAO,CACZJ,EAAE,CAAE,CACHW,EAAE,CAAE,KAAK,CACTC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CAE1B,0BAA0B,CAAE,CAC1BC,MAAM,CAAE,MAAM,CACd,0CAA0C,CAAE,CAC1CF,MAAM,CAAE,iBACV,CAAC,CACD,gDAAgD,CAAE,CAChDA,MAAM,CAAE,iBACV,CACF,CAAC,CACD,oCAAoC,CAAE,CACpCA,MAAM,CAAE,iBACV,CAEI,CAAE,CACAG,WAAW,CAAExH,SAAS,CAAC,aAAa,CAAE,CACtCyH,QAAQ,CAAGxC,CAAC,EAAK/E,oBAAoB,CAAC+E,CAAC,CAAC3E,MAAM,CAAC+D,KAAK,CAAE,CACtD,CAAC,cACFpF,IAAA,CAACX,UAAU,EAACmI,EAAE,CAAE,CAAEQ,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEE,EAAE,CAAE,KAAM,CAAE,CAAAT,QAAA,CAAE3G,SAAS,CAAC,eAAe,CAAC,CAAa,CAAC,cAC9Gb,KAAA,CAACjB,MAAM,EACNmG,KAAK,CAAE3C,eAAgB,CACvB+F,QAAQ,CAAEzC,mBAAoB,CAC5ByB,EAAE,CAAE,CACLW,EAAE,CAAE,KAAK,CACTC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBI,SAAS,CAAE,MAAM,CACjB,qBAAqB,CAAE,CACxBC,OAAO,CAAE,KACV,CAAC,CAEN,0BAA0B,CAAE,CAC1BJ,MAAM,CAAE,MAAM,CACd,0CAA0C,CAAE,CAC1CF,MAAM,CAAE,iBACV,CAAC,CACD,gDAAgD,CAAE,CAChDA,MAAM,CAAE,iBACV,CACF,CAAC,CACD,oCAAoC,CAAE,CACpCA,MAAM,CAAE,iBACV,CAEI,CAAE,CACAO,SAAS,CAAE,CACVrB,SAAS,CAAE,CACV;AACAsB,IAAI,CAAE,CACLC,aAAa,CAAE,IAAI,CACnBC,mBAAmB,CAAE,IAAI,CAAE;AAC3BC,gBAAgB,CAAE,IAAI,CAAE;AACxBC,mBAAmB,CAAE,IAAI,CAEzBxB,EAAE,CAAE,CACHyB,MAAM,CAAGC,KAAK,EAAKA,KAAK,CAACD,MAAM,CAACE,OAAO,CAAG,IAC3C,CACD,CACD,CACD,CAAE,CAAAzB,QAAA,eAEF1H,IAAA,CAACjB,QAAQ,EAACqG,KAAK,CAAC,OAAO,CAAAsC,QAAA,CAAE3G,SAAS,CAAC,OAAO,CAAC,CAAW,CAAC,cACvDf,IAAA,CAACjB,QAAQ,EAACqG,KAAK,CAAC,UAAU,CAAAsC,QAAA,CAAE3G,SAAS,CAAC,UAAU,CAAC,CAAW,CAAC,cAC7Df,IAAA,CAACjB,QAAQ,EAACqG,KAAK,CAAC,UAAU,CAAAsC,QAAA,CAAE3G,SAAS,CAAC,UAAU,CAAC,CAAW,CAAC,cAC7Df,IAAA,CAACjB,QAAQ,EAACqG,KAAK,CAAC,MAAM,CAAAsC,QAAA,CAAE3G,SAAS,CAAC,MAAM,CAAC,CAAW,CAAC,cACrDf,IAAA,CAACjB,QAAQ,EAACqG,KAAK,CAAC,SAAS,CAAAsC,QAAA,CAAE3G,SAAS,CAAC,SAAS,CAAC,CAAW,CAAC,EACpD,CAAC,CACR0B,eAAe,GAAK,UAAU,cAC9BvC,KAAA,CAAAE,SAAA,EAAAsH,QAAA,eACC1H,IAAA,CAACX,UAAU,EAACmI,EAAE,CAAE,CAAEQ,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAAR,QAAA,CAAE3G,SAAS,CAAC,WAAW,CAAC,CAAa,CAAC,cAC1Gf,IAAA,CAACd,SAAS,EACTkG,KAAK,CAAEzC,SAAU,CACjBiF,IAAI,CAAC,OAAO,CACZW,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,CAAGxC,CAAC,EAAK,CAChB,KAAM,CAAAiB,MAAM,CAAGjB,CAAC,CAAC3E,MAAM,CAAC+D,KAAK,CAC7BxC,YAAY,CAACqE,MAAM,CAAC,CAAE;AACtBD,eAAe,CAAChB,CAAC,CAAC,CAAE;AACrB,CAAE,CACFY,KAAK,CAAE,CAAC,CAAC/D,cAAe,CACxBuG,UAAU,CAAEvG,cAAc,CAAG9B,SAAS,CAAC8B,cAAc,CAAC,CAAG,EAAG,CAC5D,CAAC,cAEF7C,IAAA,CAACZ,iBAAiB,EACjBgG,KAAK,CAAEjD,WAAY,CACnBqG,QAAQ,CAAEtC,gBAAiB,CAC3BmD,SAAS,MACT,aAAYtI,SAAS,CAAC,aAAa,CAAE,CACrCyG,EAAE,CAAE,CACH8B,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,KAAK,CACdjB,MAAM,CAAE,MACT,CAAE,CAAAZ,QAAA,CAED,CAAC,SAAS,CAAE,UAAU,CAAC,CAAC8B,GAAG,CAAE3D,GAAG,EAAK,CACrC,mBACC7F,IAAA,CAACb,YAAY,EACZiG,KAAK,CAAES,GAAI,CACX,aAAW,SAAS,CACpB2B,EAAE,CAAE,CACHY,MAAM,CAAE,mBAAmB,CAC3BqB,aAAa,CAAE,YAAY,CAC3BrI,KAAK,CAAE,MAAM,CACbiH,YAAY,CAAE,KAAK,CACnBqB,IAAI,CAAE,CAAC,CACPhB,OAAO,CAAE,cAAc,CAEvB,gBAAgB,CAAE,CACjBtF,eAAe,CAAE,qBAAqB,CACtChC,KAAK,CAAE,MAAM,CACbgH,MAAM,CAAE,mBACT,CAAC,CACD,SAAS,CAAE,CACVhF,eAAe,CAAE,SAClB,CAAC,CACD,cAAc,CAAE,CACfuG,UAAU,CAAE,0CAA4C;AACzD,CACD,CAAE,CAAAjC,QAAA,CAED7B,GAAG,CACS,CAAC,CAEjB,CAAC,CAAC,CACgB,CAAC,EACnB,CAAC,CACA,IAAI,EACI,CAAC,cAId3F,KAAA,CAACvB,GAAG,EACHgJ,SAAS,CAAC,mBAAmB,CAC7BH,EAAE,CAAE,CAAEa,YAAY,CAAE,KAAM,CAAE,CAAAX,QAAA,eAE5B1H,IAAA,QAAK2H,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAE3G,SAAS,CAAC,YAAY,CAAC,CAAM,CAAC,cACpEf,IAAA,CAACrB,GAAG,EACH6I,EAAE,CAAE,CACHpE,eAAe,CAAEF,UAAU,CAACE,eAAe,CAC3CwG,KAAK,CAAE,MAAM,CACbtB,MAAM,CAAE,MAAM,CACdD,YAAY,CAAE,KACf,CAAE,CACFwB,SAAS,CAAE,KAAM,CACjBC,IAAI,CAAC,QAAQ,CACbjC,OAAO,CAAG7B,CAAC,EAAK,CACfxD,sBAAsB,CAACwD,CAAC,CAAC+D,aAAa,CAAC,CACvC5I,uBAAuB,CAAC,CACvBC,KAAK,CAAE8B,UAAU,CAACE,eAAe,CACjC/B,MAAM,CAAE,iBACT,CAAC,CAAC,CACH,CAAE,CACF,CAAC,EACE,CAAC,cAENnB,KAAA,CAACvB,GAAG,EACHgJ,SAAS,CAAC,mBAAmB,CAC7BH,EAAE,CAAE,CAAEa,YAAY,CAAE,KAAM,CAAE,CAAAX,QAAA,eAE5B1H,IAAA,QAAK2H,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAE3G,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cAChEf,IAAA,CAACrB,GAAG,EACH6I,EAAE,CAAE,CACHpE,eAAe,CAAEF,UAAU,CAACG,WAAW,CAAC2G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrDJ,KAAK,CAAE,MAAM,CACbtB,MAAM,CAAE,MAAM,CACdD,YAAY,CAAE,KACf,CAAE,CACFwB,SAAS,CAAE,KAAM,CACjBC,IAAI,CAAC,QAAQ,CACbjC,OAAO,CAAG7B,CAAC,EAAK,CACfxD,sBAAsB,CAACwD,CAAC,CAAC+D,aAAa,CAAC,CACvC5I,uBAAuB,CAAC,CACvBC,KAAK,CAAE,aAAa8B,UAAU,CAACG,WAAW,CAAC2G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAC1D3I,MAAM,CAAE,aACT,CAAC,CAAC,CACH,CAAE,CACF,CAAC,EACE,CAAC,cAENnB,KAAA,CAACvB,GAAG,EACHgJ,SAAS,CAAC,mBAAmB,CAC7BH,EAAE,CAAE,CAAEa,YAAY,CAAE,KAAM,CAAE,CAAAX,QAAA,eAE5B1H,IAAA,QAAK2H,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAE3G,SAAS,CAAC,MAAM,CAAC,CAAM,CAAC,cAC9Df,IAAA,CAACrB,GAAG,EACH6I,EAAE,CAAE,CACHpE,eAAe,CAAEF,UAAU,CAAC9B,KAAK,CACjCwI,KAAK,CAAE,MAAM,CACbtB,MAAM,CAAE,MAAM,CACdD,YAAY,CAAE,KACf,CAAE,CACFwB,SAAS,CAAE,KAAM,CACjBC,IAAI,CAAC,QAAQ,CACbjC,OAAO,CAAG7B,CAAC,EAAK,CACfxD,sBAAsB,CAACwD,CAAC,CAAC+D,aAAa,CAAC,CACvC5I,uBAAuB,CAAC,CACvBC,KAAK,CAAE8B,UAAU,CAAC9B,KAAK,CACvBC,MAAM,CAAE,OACT,CAAC,CAAC,CACH,CAAE,CACF,CAAC,EACE,CAAC,EACF,CAAC,cAENrB,IAAA,QAAK2H,SAAS,CAAC,oBAAoB,CAAAD,QAAA,cAClC1H,IAAA,CAACpB,MAAM,EACNqL,OAAO,CAAC,WAAW,CACnBpC,OAAO,CAAEhB,aAAc,CACvBc,SAAS,CAAC,WAAW,CAAAD,QAAA,CAEpB3G,SAAS,CAAC,OAAO,CAAC,CACZ,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,cACNf,IAAA,CAAChB,OAAO,EACPkI,IAAI,CAAEC,OAAO,CAAC5E,mBAAmB,CAAE,CACnC6E,QAAQ,CAAE7E,mBAAoB,CAC9B8E,OAAO,CAAEA,CAAA,GAAM,CACd7E,sBAAsB,CAAC,IAAI,CAAC,CAC5BrB,uBAAuB,CAAC,CAAEC,KAAK,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAG,CAAC,CAAC,CACnD,CAAE,CACF6I,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CACF3F,EAAE,CAAC,cAAc,CACjB6C,SAAS,CAAE,CACVsB,IAAI,CAAE,CACL;AACApB,EAAE,CAAE,CACHyB,MAAM,CAAGC,KAAK,EAAKA,KAAK,CAACD,MAAM,CAACE,OAAO,CAAG,IAC3C,CACD,CACD,CAAE,CAAAzB,QAAA,cAEFxH,KAAA,CAACvB,GAAG,EAAA+I,QAAA,eACH1H,IAAA,CAACL,YAAY,EACZyB,KAAK,CAAEF,oBAAoB,CAACE,KAAM,CAClCoH,QAAQ,CAAGxC,CAAC,EAAKI,iBAAiB,CAACJ,CAAC,CAAE9E,oBAAoB,CAACG,MAAM,CAAE,CACnE,CAAC,cACFrB,IAAA,UAAA0H,QAAA,CACE;AACP;AACA;AACA;AACA,KAAK,CACO,CAAC,EACJ,CAAC,CACE,CAAC,EACF,CAAC,CAEZ,CAAC,CAED,cAAe,CAAArH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}