{"ast": null, "code": "import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport const withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR(_ref) {\n    let {\n      initialI18nStore,\n      initialLanguage,\n      ...rest\n    } = _ref;\n    useSSR(initialI18nStore, initialLanguage);\n    return createElement(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};", "map": {"version": 3, "names": ["createElement", "useSSR", "composeInitialProps", "getDisplayName", "withSSR", "Extend", "WrappedComponent", "I18nextWithSSR", "_ref", "initialI18nStore", "initialLanguage", "rest", "getInitialProps", "displayName"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/node_modules/react-i18next/dist/es/withSSR.js"], "sourcesContent": ["import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport const withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    useSSR(initialI18nStore, initialLanguage);\n    return createElement(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,mBAAmB,QAAQ,cAAc;AAClD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM,SAASC,MAAMA,CAACC,gBAAgB,EAAE;EAC7D,SAASC,cAAcA,CAAAC,IAAA,EAIpB;IAAA,IAJqB;MACtBC,gBAAgB;MAChBC,eAAe;MACf,GAAGC;IACL,CAAC,GAAAH,IAAA;IACCP,MAAM,CAACQ,gBAAgB,EAAEC,eAAe,CAAC;IACzC,OAAOV,aAAa,CAACM,gBAAgB,EAAE;MACrC,GAAGK;IACL,CAAC,CAAC;EACJ;EACAJ,cAAc,CAACK,eAAe,GAAGV,mBAAmB,CAACI,gBAAgB,CAAC;EACtEC,cAAc,CAACM,WAAW,GAAG,kBAAkBV,cAAc,CAACG,gBAAgB,CAAC,GAAG;EAClFC,cAAc,CAACD,gBAAgB,GAAGA,gBAAgB;EAClD,OAAOC,cAAc;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}