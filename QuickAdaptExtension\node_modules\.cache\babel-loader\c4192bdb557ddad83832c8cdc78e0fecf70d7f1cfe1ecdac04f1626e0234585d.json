{"ast": null, "code": "import e from \"void-elements\";\nvar t = /\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g;\nfunction n(n) {\n  var r = {\n      type: \"tag\",\n      name: \"\",\n      voidElement: !1,\n      attrs: {},\n      children: []\n    },\n    i = n.match(/<\\/?([^\\s]+?)[/\\s>]/);\n  if (i && (r.name = i[1], (e[i[1]] || \"/\" === n.charAt(n.length - 2)) && (r.voidElement = !0), r.name.startsWith(\"!--\"))) {\n    var s = n.indexOf(\"--\\x3e\");\n    return {\n      type: \"comment\",\n      comment: -1 !== s ? n.slice(4, s) : \"\"\n    };\n  }\n  for (var a = new RegExp(t), c = null; null !== (c = a.exec(n));) if (c[0].trim()) if (c[1]) {\n    var o = c[1].trim(),\n      l = [o, \"\"];\n    o.indexOf(\"=\") > -1 && (l = o.split(\"=\")), r.attrs[l[0]] = l[1], a.lastIndex--;\n  } else c[2] && (r.attrs[c[2]] = c[3].trim().substring(1, c[3].length - 1));\n  return r;\n}\nvar r = /<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g,\n  i = /^\\s*$/,\n  s = Object.create(null);\nfunction a(e, t) {\n  switch (t.type) {\n    case \"text\":\n      return e + t.content;\n    case \"tag\":\n      return e += \"<\" + t.name + (t.attrs ? function (e) {\n        var t = [];\n        for (var n in e) t.push(n + '=\"' + e[n] + '\"');\n        return t.length ? \" \" + t.join(\" \") : \"\";\n      }(t.attrs) : \"\") + (t.voidElement ? \"/>\" : \">\"), t.voidElement ? e : e + t.children.reduce(a, \"\") + \"</\" + t.name + \">\";\n    case \"comment\":\n      return e + \"\\x3c!--\" + t.comment + \"--\\x3e\";\n  }\n}\nvar c = {\n  parse: function (e, t) {\n    t || (t = {}), t.components || (t.components = s);\n    var a,\n      c = [],\n      o = [],\n      l = -1,\n      m = !1;\n    if (0 !== e.indexOf(\"<\")) {\n      var u = e.indexOf(\"<\");\n      c.push({\n        type: \"text\",\n        content: -1 === u ? e : e.substring(0, u)\n      });\n    }\n    return e.replace(r, function (r, s) {\n      if (m) {\n        if (r !== \"</\" + a.name + \">\") return;\n        m = !1;\n      }\n      var u,\n        f = \"/\" !== r.charAt(1),\n        h = r.startsWith(\"\\x3c!--\"),\n        p = s + r.length,\n        d = e.charAt(p);\n      if (h) {\n        var v = n(r);\n        return l < 0 ? (c.push(v), c) : ((u = o[l]).children.push(v), c);\n      }\n      if (f && (l++, \"tag\" === (a = n(r)).type && t.components[a.name] && (a.type = \"component\", m = !0), a.voidElement || m || !d || \"<\" === d || a.children.push({\n        type: \"text\",\n        content: e.slice(p, e.indexOf(\"<\", p))\n      }), 0 === l && c.push(a), (u = o[l - 1]) && u.children.push(a), o[l] = a), (!f || a.voidElement) && (l > -1 && (a.voidElement || a.name === r.slice(2, -1)) && (l--, a = -1 === l ? c : o[l]), !m && \"<\" !== d && d)) {\n        u = -1 === l ? c : o[l].children;\n        var x = e.indexOf(\"<\", p),\n          g = e.slice(p, -1 === x ? void 0 : x);\n        i.test(g) && (g = \" \"), (x > -1 && l + u.length >= 0 || \" \" !== g) && u.push({\n          type: \"text\",\n          content: g\n        });\n      }\n    }), c;\n  },\n  stringify: function (e) {\n    return e.reduce(function (e, t) {\n      return e + a(\"\", t);\n    }, \"\");\n  }\n};\nexport default c;", "map": {"version": 3, "names": ["t", "n", "r", "type", "name", "voidElement", "attrs", "children", "i", "match", "e", "char<PERSON>t", "length", "startsWith", "s", "indexOf", "comment", "slice", "a", "RegExp", "c", "exec", "trim", "o", "l", "split", "lastIndex", "substring", "Object", "create", "content", "push", "join", "reduce", "parse", "components", "m", "u", "replace", "f", "h", "p", "d", "v", "x", "g", "test", "stringify"], "sources": ["E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\node_modules\\html-parse-stringify\\src\\parse-tag.js", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\node_modules\\html-parse-stringify\\src\\parse.js", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\node_modules\\html-parse-stringify\\src\\stringify.js", "E:\\Code\\Quickadopt Bugs Devlatest\\quickadapt\\QuickAdaptExtension\\node_modules\\html-parse-stringify\\src\\index.js"], "sourcesContent": ["import lookup from 'void-elements'\nconst attrRE = /\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g\n\nexport default function stringify(tag) {\n  const res = {\n    type: 'tag',\n    name: '',\n    voidElement: false,\n    attrs: {},\n    children: [],\n  }\n\n  const tagMatch = tag.match(/<\\/?([^\\s]+?)[/\\s>]/)\n  if (tagMatch) {\n    res.name = tagMatch[1]\n    if (\n      lookup[tagMatch[1]] ||\n      tag.charAt(tag.length - 2) === '/'\n    ) {\n      res.voidElement = true\n    }\n\n    // handle comment tag\n    if (res.name.startsWith('!--')) {\n      const endIndex = tag.indexOf('-->')\n      return {\n        type: 'comment',\n        comment: endIndex !== -1 ? tag.slice(4, endIndex) : '',\n      }\n    }\n  }\n\n  const reg = new RegExp(attrRE)\n  let result = null\n  for (;;) {\n    result = reg.exec(tag)\n\n    if (result === null) {\n      break\n    }\n\n    if (!result[0].trim()) {\n      continue\n    }\n\n    if (result[1]) {\n      const attr = result[1].trim()\n      let arr = [attr, '']\n\n      if (attr.indexOf('=') > -1) {\n        arr = attr.split('=')\n      }\n\n      res.attrs[arr[0]] = arr[1]\n      reg.lastIndex--\n    } else if (result[2]) {\n      res.attrs[result[2]] = result[3].trim().substring(1, result[3].length - 1)\n    }\n  }\n\n  return res\n}\n", "import parseTag from './parse-tag'\n\nconst tagRE = /<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g\nconst whitespaceRE = /^\\s*$/\n\n// re-used obj for quick lookups of components\nconst empty = Object.create(null)\n\nexport default function parse(html, options) {\n  options || (options = {})\n  options.components || (options.components = empty)\n  const result = []\n  const arr = []\n  let current\n  let level = -1\n  let inComponent = false\n\n  // handle text at top level\n  if (html.indexOf('<') !== 0) {\n    var end = html.indexOf('<')\n    result.push({\n      type: 'text',\n      content: end === -1 ? html : html.substring(0, end),\n    })\n  }\n\n  html.replace(tagRE, function (tag, index) {\n    if (inComponent) {\n      if (tag !== '</' + current.name + '>') {\n        return\n      } else {\n        inComponent = false\n      }\n    }\n    const isOpen = tag.charAt(1) !== '/'\n    const isComment = tag.startsWith('<!--')\n    const start = index + tag.length\n    const nextChar = html.charAt(start)\n    let parent\n\n    if (isComment) {\n      const comment = parseTag(tag)\n\n      // if we're at root, push new base node\n      if (level < 0) {\n        result.push(comment)\n        return result\n      }\n      parent = arr[level]\n      parent.children.push(comment)\n      return result\n    }\n\n    if (isOpen) {\n      level++\n\n      current = parseTag(tag)\n      if (current.type === 'tag' && options.components[current.name]) {\n        current.type = 'component'\n        inComponent = true\n      }\n\n      if (\n        !current.voidElement &&\n        !inComponent &&\n        nextChar &&\n        nextChar !== '<'\n      ) {\n        current.children.push({\n          type: 'text',\n          content: html.slice(start, html.indexOf('<', start)),\n        })\n      }\n\n      // if we're at root, push new base node\n      if (level === 0) {\n        result.push(current)\n      }\n\n      parent = arr[level - 1]\n\n      if (parent) {\n        parent.children.push(current)\n      }\n\n      arr[level] = current\n    }\n\n    if (!isOpen || current.voidElement) {\n      if (\n        level > -1 &&\n        (current.voidElement || current.name === tag.slice(2, -1))\n      ) {\n        level--\n        // move current up a level to match the end tag\n        current = level === -1 ? result : arr[level]\n      }\n      if (!inComponent && nextChar !== '<' && nextChar) {\n        // trailing text node\n        // if we're at the root, push a base text node. otherwise add as\n        // a child to the current node.\n        parent = level === -1 ? result : arr[level].children\n\n        // calculate correct end of the content slice in case there's\n        // no tag after the text node.\n        const end = html.indexOf('<', start)\n        let content = html.slice(start, end === -1 ? undefined : end)\n        // if a node is nothing but whitespace, collapse it as the spec states:\n        // https://www.w3.org/TR/html4/struct/text.html#h-9.1\n        if (whitespaceRE.test(content)) {\n          content = ' '\n        }\n        // don't add whitespace-only text nodes if they would be trailing text nodes\n        // or if they would be leading whitespace-only text nodes:\n        //  * end > -1 indicates this is not a trailing text node\n        //  * leading node is when level is -1 and parent has length 0\n        if ((end > -1 && level + parent.length >= 0) || content !== ' ') {\n          parent.push({\n            type: 'text',\n            content: content,\n          })\n        }\n      }\n    }\n  })\n\n  return result\n}\n", "function attrString(attrs) {\n  const buff = []\n  for (let key in attrs) {\n    buff.push(key + '=\"' + attrs[key] + '\"')\n  }\n  if (!buff.length) {\n    return ''\n  }\n  return ' ' + buff.join(' ')\n}\n\nfunction stringify(buff, doc) {\n  switch (doc.type) {\n    case 'text':\n      return buff + doc.content\n    case 'tag':\n      buff +=\n        '<' +\n        doc.name +\n        (doc.attrs ? attrString(doc.attrs) : '') +\n        (doc.voidElement ? '/>' : '>')\n      if (doc.voidElement) {\n        return buff\n      }\n      return buff + doc.children.reduce(stringify, '') + '</' + doc.name + '>'\n    case 'comment':\n      buff += '<!--' + doc.comment + '-->'\n      return buff\n  }\n}\n\nexport default function (doc) {\n  return doc.reduce(function (token, rootEl) {\n    return token + stringify('', rootEl)\n  }, '')\n}\n", "import parse from './parse'\nimport stringify from './stringify'\n\nexport default {\n  parse,\n  stringify,\n}\n"], "mappings": ";AACA,IAAMA,CAAA,GAAS;AAAA,SAESC,EAAUA,CAAA;EAChC,IAAMC,CAAA,GAAM;MACVC,IAAA,EAAM;MACNC,IAAA,EAAM;MACNC,WAAA,GAAa;MACbC,KAAA,EAAO;MACPC,QAAA,EAAU;IAAA;IAGNC,CAAA,GAAWP,CAAA,CAAIQ,KAAA,CAAM;EAC3B,IAAID,CAAA,KACFN,CAAA,CAAIE,IAAA,GAAOI,CAAA,CAAS,KAElBE,CAAA,CAAOF,CAAA,CAAS,OACe,QAA/BP,CAAA,CAAIU,MAAA,CAAOV,CAAA,CAAIW,MAAA,GAAS,QAExBV,CAAA,CAAIG,WAAA,IAAc,IAIhBH,CAAA,CAAIE,IAAA,CAAKS,UAAA,CAAW,SAAQ;IAC9B,IAAMC,CAAA,GAAWb,CAAA,CAAIc,OAAA,CAAQ;IAC7B,OAAO;MACLZ,IAAA,EAAM;MACNa,OAAA,GAAuB,MAAdF,CAAA,GAAkBb,CAAA,CAAIgB,KAAA,CAAM,GAAGH,CAAA,IAAY;IAAA;EAAA;EAO1D,KAFA,IAAMI,CAAA,GAAM,IAAIC,MAAA,CAAOnB,CAAA,GACnBoB,CAAA,GAAS,MAII,UAFfA,CAAA,GAASF,CAAA,CAAIG,IAAA,CAAKpB,CAAA,KAMlB,IAAKmB,CAAA,CAAO,GAAGE,IAAA,IAIf,IAAIF,CAAA,CAAO,IAAI;IACb,IAAMG,CAAA,GAAOH,CAAA,CAAO,GAAGE,IAAA;MACnBE,CAAA,GAAM,CAACD,CAAA,EAAM;IAEbA,CAAA,CAAKR,OAAA,CAAQ,QAAQ,MACvBS,CAAA,GAAMD,CAAA,CAAKE,KAAA,CAAM,OAGnBvB,CAAA,CAAII,KAAA,CAAMkB,CAAA,CAAI,MAAMA,CAAA,CAAI,IACxBN,CAAA,CAAIQ,SAAA;EAAA,OACKN,CAAA,CAAO,OAChBlB,CAAA,CAAII,KAAA,CAAMc,CAAA,CAAO,MAAMA,CAAA,CAAO,GAAGE,IAAA,GAAOK,SAAA,CAAU,GAAGP,CAAA,CAAO,GAAGR,MAAA,GAAS;EAI5E,OAAOV,CAAA;AAAA;AC1DT,IAAMA,CAAA,GAAQ;EACRM,CAAA,GAAe;EAGfM,CAAA,GAAQc,MAAA,CAAOC,MAAA,CAAO;ACK5B,SAASX,EAAUR,CAAA,EAAMV,CAAA;EACvB,QAAQA,CAAA,CAAIG,IAAA;IACV,KAAK;MACH,OAAOO,CAAA,GAAOV,CAAA,CAAI8B,OAAA;IACpB,KAAK;MAMH,OALApB,CAAA,IACE,MACAV,CAAA,CAAII,IAAA,IACHJ,CAAA,CAAIM,KAAA,GAnBb,UAAoBI,CAAA;QAClB,IAAMV,CAAA,GAAO;QACb,KAAK,IAAIC,CAAA,IAAOS,CAAA,EACdV,CAAA,CAAK+B,IAAA,CAAK9B,CAAA,GAAM,OAAOS,CAAA,CAAMT,CAAA,IAAO;QAEtC,OAAKD,CAAA,CAAKY,MAAA,GAGH,MAAMZ,CAAA,CAAKgC,IAAA,CAAK,OAFd;MAAA,CAaU,CAAWhC,CAAA,CAAIM,KAAA,IAAS,OACpCN,CAAA,CAAIK,WAAA,GAAc,OAAO,MACxBL,CAAA,CAAIK,WAAA,GACCK,CAAA,GAEFA,CAAA,GAAOV,CAAA,CAAIO,QAAA,CAAS0B,MAAA,CAAOf,CAAA,EAAW,MAAM,OAAOlB,CAAA,CAAII,IAAA,GAAO;IACvE,KAAK;MAEH,OADAM,CAAA,GAAQ,YAASV,CAAA,CAAIgB,OAAA,GAAU;EAAA;AAAA;AAAA,IAAAI,CAAA,GCvBtB;EACbc,KAAA,EFIF,SAAAA,CAA8BxB,CAAA,EAAMV,CAAA;IAClCA,CAAA,KAAYA,CAAA,GAAU,KACtBA,CAAA,CAAQmC,UAAA,KAAenC,CAAA,CAAQmC,UAAA,GAAarB,CAAA;IAC5C,IAEII,CAAA;MAFEE,CAAA,GAAS;MACTG,CAAA,GAAM;MAERC,CAAA,IAAS;MACTY,CAAA,IAAc;IAGlB,IAA0B,MAAtB1B,CAAA,CAAKK,OAAA,CAAQ,MAAY;MAC3B,IAAIsB,CAAA,GAAM3B,CAAA,CAAKK,OAAA,CAAQ;MACvBK,CAAA,CAAOW,IAAA,CAAK;QACV5B,IAAA,EAAM;QACN2B,OAAA,GAAkB,MAATO,CAAA,GAAa3B,CAAA,GAAOA,CAAA,CAAKiB,SAAA,CAAU,GAAGU,CAAA;MAAA;IAAA;IAwGnD,OApGA3B,CAAA,CAAK4B,OAAA,CAAQpC,CAAA,EAAO,UAAUA,CAAA,EAAKY,CAAA;MACjC,IAAIsB,CAAA,EAAa;QACf,IAAIlC,CAAA,KAAQ,OAAOgB,CAAA,CAAQd,IAAA,GAAO,KAChC;QAEAgC,CAAA,IAAc;MAAA;MAGlB,IAIIC,CAAA;QAJEE,CAAA,GAA2B,QAAlBrC,CAAA,CAAIS,MAAA,CAAO;QACpB6B,CAAA,GAAYtC,CAAA,CAAIW,UAAA,CAAW;QAC3B4B,CAAA,GAAQ3B,CAAA,GAAQZ,CAAA,CAAIU,MAAA;QACpB8B,CAAA,GAAWhC,CAAA,CAAKC,MAAA,CAAO8B,CAAA;MAG7B,IAAID,CAAA,EAAW;QACb,IAAMG,CAAA,GAAU1C,CAAA,CAASC,CAAA;QAGzB,OAAIsB,CAAA,GAAQ,KACVJ,CAAA,CAAOW,IAAA,CAAKY,CAAA,GACLvB,CAAA,MAETiB,CAAA,GAASd,CAAA,CAAIC,CAAA,GACNjB,QAAA,CAASwB,IAAA,CAAKY,CAAA,GACdvB,CAAA;MAAA;MAsCT,IAnCImB,CAAA,KACFf,CAAA,IAGqB,WADrBN,CAAA,GAAUjB,CAAA,CAASC,CAAA,GACPC,IAAA,IAAkBH,CAAA,CAAQmC,UAAA,CAAWjB,CAAA,CAAQd,IAAA,MACvDc,CAAA,CAAQf,IAAA,GAAO,aACfiC,CAAA,IAAc,IAIblB,CAAA,CAAQb,WAAA,IACR+B,CAAA,KACDM,CAAA,IACa,QAAbA,CAAA,IAEAxB,CAAA,CAAQX,QAAA,CAASwB,IAAA,CAAK;QACpB5B,IAAA,EAAM;QACN2B,OAAA,EAASpB,CAAA,CAAKO,KAAA,CAAMwB,CAAA,EAAO/B,CAAA,CAAKK,OAAA,CAAQ,KAAK0B,CAAA;MAAA,IAKnC,MAAVjB,CAAA,IACFJ,CAAA,CAAOW,IAAA,CAAKb,CAAA,IAGdmB,CAAA,GAASd,CAAA,CAAIC,CAAA,GAAQ,OAGnBa,CAAA,CAAO9B,QAAA,CAASwB,IAAA,CAAKb,CAAA,GAGvBK,CAAA,CAAIC,CAAA,IAASN,CAAA,KAGVqB,CAAA,IAAUrB,CAAA,CAAQb,WAAA,MAEnBmB,CAAA,IAAS,MACRN,CAAA,CAAQb,WAAA,IAAea,CAAA,CAAQd,IAAA,KAASF,CAAA,CAAIe,KAAA,CAAM,IAAI,QAEvDO,CAAA,IAEAN,CAAA,IAAqB,MAAXM,CAAA,GAAeJ,CAAA,GAASG,CAAA,CAAIC,CAAA,KAEnCY,CAAA,IAA4B,QAAbM,CAAA,IAAoBA,CAAA,GAAU;QAIhDL,CAAA,IAAoB,MAAXb,CAAA,GAAeJ,CAAA,GAASG,CAAA,CAAIC,CAAA,EAAOjB,QAAA;QAI5C,IAAMqC,CAAA,GAAMlC,CAAA,CAAKK,OAAA,CAAQ,KAAK0B,CAAA;UAC1BI,CAAA,GAAUnC,CAAA,CAAKO,KAAA,CAAMwB,CAAA,GAAgB,MAATG,CAAA,QAAa,IAAYA,CAAA;QAGrDpC,CAAA,CAAasC,IAAA,CAAKD,CAAA,MACpBA,CAAA,GAAU,OAMPD,CAAA,IAAO,KAAKpB,CAAA,GAAQa,CAAA,CAAOzB,MAAA,IAAU,KAAkB,QAAZiC,CAAA,KAC9CR,CAAA,CAAON,IAAA,CAAK;UACV5B,IAAA,EAAM;UACN2B,OAAA,EAASe;QAAA;MAAA;IAAA,IAOZzB,CAAA;EAAA;EEzHP2B,SAAA,WAAAA,CD0BuBrC,CAAA;IACvB,OAAOA,CAAA,CAAIuB,MAAA,CAAO,UAAUvB,CAAA,EAAOV,CAAA;MACjC,OAAOU,CAAA,GAAQQ,CAAA,CAAU,IAAIlB,CAAA;IAAA,GAC5B;EAAA;AAAA;AAAA,eAAAoB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}