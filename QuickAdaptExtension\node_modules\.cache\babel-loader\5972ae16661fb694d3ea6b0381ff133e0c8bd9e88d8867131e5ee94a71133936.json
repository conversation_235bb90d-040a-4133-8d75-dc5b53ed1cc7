{"ast": null, "code": "import React,{useContext,useState}from'react';import{ai}from'../../assets/icons/icons';import useDrawerStore from\"../../store/drawerStore\";import{useTranslation}from'react-i18next';import{AccountContext}from'../login/AccountContext';import useInfoStore from'../../store/UserInfoStore';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CreateWithAIButton=_ref=>{let{onClick}=_ref;const{t:translate}=useTranslation();const{isCollapsed,setIsCollapsed}=useDrawerStore(state=>state);const[isSelected,setIsSelected]=useState(false);const{accountId,roles}=useContext(AccountContext);const userType=useInfoStore(state=>state.userType);const handleClick=()=>{setIsSelected(!isSelected);// toggle active state\nonClick();// your original click logic\n};return/*#__PURE__*/_jsx(_Fragment,{children:isCollapsed?/*#__PURE__*/_jsx(\"button\",{className:`qadpt-ai-button ${isCollapsed&&isSelected?'active':''}`,onClick:onClick,children:/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-aiicon\",dangerouslySetInnerHTML:{__html:ai}})}):/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-ai-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"beta\",children:translate(\"BETA\")}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-ai-title\",children:translate(\"Create interactions using the latest AI technology.\")}),/*#__PURE__*/_jsxs(\"button\",{className:\"qadpt-button\",onClick:onClick,disabled:userType.toLocaleLowerCase()!=\"admin\"?roles==null||!roles||![\"Account Admin\",\"Editor\"].some(role=>roles.includes(role)):false,children:[/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-icon\",dangerouslySetInnerHTML:{__html:ai}}),/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-text\",children:translate(\"Create with AI\")})]})]})});};export default CreateWithAIButton;", "map": {"version": 3, "names": ["React", "useContext", "useState", "ai", "useDrawerStore", "useTranslation", "AccountContext", "useInfoStore", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CreateWithAIButton", "_ref", "onClick", "t", "translate", "isCollapsed", "setIsCollapsed", "state", "isSelected", "setIsSelected", "accountId", "roles", "userType", "handleClick", "children", "className", "dangerouslySetInnerHTML", "__html", "disabled", "toLocaleLowerCase", "some", "role", "includes"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AIAgent/CreateWithAIButton.tsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { ai, beta } from '../../assets/icons/icons';\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport { AccountContext } from '../login/AccountContext';\r\nimport useInfoStore from '../../store/UserInfoStore';\r\n\r\ninterface CreateWithAIButtonProps {\r\n  onClick: () => void;\r\n}\r\n\r\nconst CreateWithAIButton: React.FC<CreateWithAIButtonProps> = ({ onClick }) => {\r\n  const { t: translate } = useTranslation();\r\n  const {\r\n    isCollapsed,\r\n    setIsCollapsed,\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const [isSelected, setIsSelected] = useState(false);\r\n  const { accountId, roles } = useContext(AccountContext);\r\n  const userType = useInfoStore((state) => state.userType); \r\n\r\n  const handleClick = () => {\r\n    setIsSelected(!isSelected); // toggle active state\r\n    onClick(); // your original click logic\r\n  };\r\n  return (\r\n    <>\r\n      {isCollapsed ? (\r\n        <button\r\n          className={`qadpt-ai-button ${isCollapsed && isSelected ? 'active' : ''}`}\r\n          onClick={onClick}\r\n        >\r\n          <span className=\"qadpt-aiicon\" dangerouslySetInnerHTML={{ __html: ai }} />\r\n        </button>\r\n     \r\n      ) : (\r\n     \r\n        <div className=\"qadpt-ai-container\">\r\n            <div className=\"beta\">{translate(\"BETA\")}</div>\r\n          <div className=\"qadpt-ai-title\">\r\n              {translate(\"Create interactions using the latest AI technology.\")}\r\n          </div>\r\n          <button className=\"qadpt-button\" onClick={onClick} disabled={userType.toLocaleLowerCase()!=\"admin\" ? roles==null || !roles || ![\"Account Admin\", \"Editor\"].some(role => roles.includes(role)): false}>\r\n            <span className=\"qadpt-icon\" dangerouslySetInnerHTML={{ __html: ai }} />\r\n              <span className=\"qadpt-text\">{translate(\"Create with AI\")}</span>\r\n          </button>\r\n        </div>\r\n       \r\n      )}\r\n       \r\n    </>\r\n  );\r\n  \r\n};\r\n\r\nexport default CreateWithAIButton;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,UAAU,CAAEC,QAAQ,KAAQ,OAAO,CACnD,OAASC,EAAE,KAAc,0BAA0B,CACnD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,cAAc,KAAQ,yBAAyB,CACxD,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAMrD,KAAM,CAAAC,kBAAqD,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CACxE,KAAM,CAAEE,CAAC,CAAEC,SAAU,CAAC,CAAGb,cAAc,CAAC,CAAC,CACzC,KAAM,CACJc,WAAW,CACXC,cACF,CAAC,CAAGhB,cAAc,CAAEiB,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAEsB,SAAS,CAAEC,KAAM,CAAC,CAAGxB,UAAU,CAACK,cAAc,CAAC,CACvD,KAAM,CAAAoB,QAAQ,CAAGnB,YAAY,CAAEc,KAAK,EAAKA,KAAK,CAACK,QAAQ,CAAC,CAExD,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxBJ,aAAa,CAAC,CAACD,UAAU,CAAC,CAAE;AAC5BN,OAAO,CAAC,CAAC,CAAE;AACb,CAAC,CACD,mBACEP,IAAA,CAAAI,SAAA,EAAAe,QAAA,CACGT,WAAW,cACVV,IAAA,WACEoB,SAAS,CAAE,mBAAmBV,WAAW,EAAIG,UAAU,CAAG,QAAQ,CAAG,EAAE,EAAG,CAC1EN,OAAO,CAAEA,OAAQ,CAAAY,QAAA,cAEjBnB,IAAA,SAAMoB,SAAS,CAAC,cAAc,CAACC,uBAAuB,CAAE,CAAEC,MAAM,CAAE5B,EAAG,CAAE,CAAE,CAAC,CACpE,CAAC,cAITQ,KAAA,QAAKkB,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eAC/BnB,IAAA,QAAKoB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEV,SAAS,CAAC,MAAM,CAAC,CAAM,CAAC,cACjDT,IAAA,QAAKoB,SAAS,CAAC,gBAAgB,CAAAD,QAAA,CAC1BV,SAAS,CAAC,qDAAqD,CAAC,CAChE,CAAC,cACNP,KAAA,WAAQkB,SAAS,CAAC,cAAc,CAACb,OAAO,CAAEA,OAAQ,CAACgB,QAAQ,CAAEN,QAAQ,CAACO,iBAAiB,CAAC,CAAC,EAAE,OAAO,CAAGR,KAAK,EAAE,IAAI,EAAI,CAACA,KAAK,EAAI,CAAC,CAAC,eAAe,CAAE,QAAQ,CAAC,CAACS,IAAI,CAACC,IAAI,EAAIV,KAAK,CAACW,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAE,KAAM,CAAAP,QAAA,eACnMnB,IAAA,SAAMoB,SAAS,CAAC,YAAY,CAACC,uBAAuB,CAAE,CAAEC,MAAM,CAAE5B,EAAG,CAAE,CAAE,CAAC,cACtEM,IAAA,SAAMoB,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAEV,SAAS,CAAC,gBAAgB,CAAC,CAAO,CAAC,EAC7D,CAAC,EACN,CAEN,CAED,CAAC,CAGP,CAAC,CAED,cAAe,CAAAJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}