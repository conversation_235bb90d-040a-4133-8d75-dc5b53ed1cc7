{"ast": null, "code": "import React,{useState,useEffect,useContext,useRef}from\"react\";import{<PERSON>,Typo<PERSON>,TextField,IconButton,Button}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import DraggableCheckpoint from\"./DraggableCheckpoint\";import{warning}from\"../../assets/icons/icons\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import CheckPointEditPopup from\"./CheckpointEditPopup\";import AddIcon from\"@mui/icons-material/Add\";import CheckPointAddPopup from\"./CheckpointAddPopup\";import{AccountContext}from\"../login/AccountContext\";import{useTranslation}from'react-i18next';import'../../styles/rtl_styles.scss';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";let editInteractionName;const Checkpoints=()=>{var _checklistGuideMetaDa3;const{t:translate}=useTranslation();const{ShowLauncherSettings,setShowLauncherSettings,setCheckPointsEditPopup,checkpointsEditPopup,titlePopup,setTitlePopup,setDesignPopup,titleColor,setTitleColor,checkpointsPopup,setCheckPointsPopup,checkpointTitleColor,setCheckpointTitleColor,checkpointTitleDescription,setCheckpointTitleDescription,checkpointIconColor,setCheckpointIconColor,setUnlockCheckPointInOrder,unlockCheckPointInOrder,checkPointMessage,setCheckPointMessage,setCheckPointsAddPopup,checkpointsAddPopup,checklistGuideMetaData,updateChecklistCheckPoints,deleteCheckpoint,setIsUnSavedChanges,isUnSavedChanges}=useDrawerStore(state=>state);const{accountId}=useContext(AccountContext);const[messageError,setMessageError]=useState(\"\");const[checklistCheckpointProperties,setChecklistCheckpointProperties]=useState(()=>{var _checklistGuideMetaDa;const initialchecklistCheckpointProperties=((_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:_checklistGuideMetaDa.checkpoints)||{checkpointsList:[],checkpointTitles:\"#333\",checkpointsDescription:\"#8D8D8D\",checkpointsIcons:\"#333\",unlockCHeckpointInOrder:false,message:\"complete items in order\"};return initialchecklistCheckpointProperties;});// State for tracking changes and apply button\nconst[isDisabled,setIsDisabled]=useState(true);const[hasChanges,setHasChanges]=useState(false);const[initialState,setInitialState]=useState(checklistCheckpointProperties);// Keep local state in sync with the store\nuseEffect(()=>{var _checklistGuideMetaDa2;if((_checklistGuideMetaDa2=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa2!==void 0&&_checklistGuideMetaDa2.checkpoints){const newCheckpoints=checklistGuideMetaData[0].checkpoints;setChecklistCheckpointProperties(newCheckpoints);setInitialState(newCheckpoints);setHasChanges(false);setIsDisabled(true);}},[(_checklistGuideMetaDa3=checklistGuideMetaData[0])===null||_checklistGuideMetaDa3===void 0?void 0:_checklistGuideMetaDa3.checkpoints]);// Function to check if the Apply button should be enabled\nconst updateApplyButtonState=function(changed){let hasErrors=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;setIsDisabled(!changed||hasErrors);};// Removed duplicate declaration of messageError\n// Effect to check for any changes compared to initial state\nuseEffect(()=>{// Compare current properties with initial state\nconst hasAnyChanges=JSON.stringify(checklistCheckpointProperties)!==JSON.stringify(initialState);setHasChanges(hasAnyChanges);// Check for validation errors\nconst hasValidationErrors=!!messageError;updateApplyButtonState(hasAnyChanges,hasValidationErrors);},[checklistCheckpointProperties,initialState,messageError]);const[interactions,setInteractions]=useState([]);const[skip,setSkip]=useState(0);const top=5;const[loading,setLoading]=useState(false);const dropdownRef=useRef(null);const handleClose=()=>{setCheckPointsPopup(false);};const handledesignclose=()=>{setDesignPopup(false);};const onReselectElement=()=>{};const onPropertyChange=(key,value)=>{setChecklistCheckpointProperties(prevState=>{const newState={...prevState,[key]:value};// Mark that changes have been made\nsetHasChanges(true);return newState;});};const[kkk,setKKK]=useState(false);const handleApplyChanges=()=>{if(kkk==false){// If no changes were made to the order, use the original list\nchecklistCheckpointProperties.checkpointsList=checklistGuideMetaData[0].checkpoints.checkpointsList;}updateChecklistCheckPoints(checklistCheckpointProperties);// Update the initial state to the current state after applying changes\nsetInitialState({...checklistCheckpointProperties});// Reset the changes flag\nsetHasChanges(false);// Disable the Apply button\nsetIsDisabled(true);handleClose();setIsUnSavedChanges(true);};const handleEditClick=id=>{editInteractionName=id;setCheckPointsEditPopup(true);};const handleDeleteClick=interaction=>{setChecklistCheckpointProperties(prevState=>({...prevState,checkpointsList:prevState.checkpointsList.filter(checkpoint=>checkpoint.interaction!==interaction)}));setHasChanges(true);deleteCheckpoint(interaction);};let checkpoints=checklistCheckpointProperties.checkpointsList;const[draggedItemIndex,setDraggedItemIndex]=useState(null);// Handle drag start\nconst handleDragStart=index=>{setDraggedItemIndex(index);};// Handle drag over (allows dropping)\nconst handleDragOver=event=>{event.preventDefault();};// Handle drop (reorder items)\nconst handleDrop=index=>{if(draggedItemIndex===null||draggedItemIndex===index)return;const updatedCheckpoints=[...checkpoints];const[draggedItem]=updatedCheckpoints.splice(draggedItemIndex,1);updatedCheckpoints.splice(index,0,draggedItem);setKKK(true);checkpoints=updatedCheckpoints;setDraggedItemIndex(null);// Update state correctly and mark changes\nsetChecklistCheckpointProperties(prevState=>({...prevState,// Copy the existing state\ncheckpointsList:updatedCheckpoints// Update only the checkpointsList\n}));setHasChanges(true);};const handleAddCheckpoint=()=>{setCheckPointsAddPopup(true);};const handleMessageChange=e=>{const value=e.target.value;let errorMessage=\"\";if(value.length<2){errorMessage=translate(\"Min: 2 Characters\");}else if(value.length>30){errorMessage=translate(\"Max: 30 Characters\");}setMessageError(errorMessage);onPropertyChange(\"message\",value);};return/*#__PURE__*/_jsxs(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"back\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Steps\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls qadpt-errmsg\",children:[/*#__PURE__*/_jsxs(Box,{sx:{backgroundColor:\"#EAE2E2\",borderRadius:\"var(--button-border-radius)\",height:\"auto\",padding:\"10px\",marginBottom:\"5px\"},children:[/*#__PURE__*/_jsx(Box,{children:checkpoints.map((checkpoint,index)=>/*#__PURE__*/_jsx(DraggableCheckpoint,{checkpoint:checkpoint,index:index,handleEditClick:()=>handleEditClick(checkpoint===null||checkpoint===void 0?void 0:checkpoint.id),handleDeleteClick:()=>handleDeleteClick(checkpoint===null||checkpoint===void 0?void 0:checkpoint.interaction),handleDragStart:handleDragStart,handleDragOver:handleDragOver,handleDrop:handleDrop,isDragging:index===draggedItemIndex},checkpoint.id))}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleAddCheckpoint,className:\"qadpt-memberButton qadpt-check\",style:{width:\"100%\",backgroundColor:\"#D3D9DA\",color:\"var(--primarycolor) !important\",placeContent:\"center\"},children:[/*#__PURE__*/_jsx(AddIcon,{}),/*#__PURE__*/_jsx(\"span\",{children:translate(\"Add Step\")})]})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Step Title\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistCheckpointProperties.checkpointTitles,onChange:e=>onPropertyChange(\"checkpointTitles\",e.target.value),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Step Description\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistCheckpointProperties===null||checklistCheckpointProperties===void 0?void 0:checklistCheckpointProperties.checkpointsDescription,onChange:e=>onPropertyChange(\"checkpointsDescription\",e.target.value),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Icon Color\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistCheckpointProperties===null||checklistCheckpointProperties===void 0?void 0:checklistCheckpointProperties.checkpointsIcons,onChange:e=>{// Update the global Icons color\nonPropertyChange(\"checkpointsIcons\",e.target.value);},className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{height:\"auto !important\",flexDirection:\"column\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Unlock Checkpoints in Order\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:checklistCheckpointProperties.unlockCHeckpointInOrder,onChange:e=>onPropertyChange(\"unlockCHeckpointInOrder\",e.target.checked),name:\"showByDefault\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),checklistCheckpointProperties.unlockCHeckpointInOrder===true&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(Box,{sx:{backgroundColor:\"#EAE2E2\",borderRadius:\"var(--button-border-radius)\",height:\"auto\",padding:\"8px 8px 0 8px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{paddingBottom:\"5px\",textAlign:\"left\"},children:translate(\"Message\")}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-control-box\",sx:{padding:\"0 !important\",height:\"auto !important\"},children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Checklist Title\"),className:\"qadpt-control-input\",value:checklistCheckpointProperties.message,style:{width:\"100%\"},onChange:handleMessageChange,error:Boolean(messageError)// Show error if message exists\n,helperText:messageError?/*#__PURE__*/_jsxs(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),messageError]}):null,InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\",marginBottom:\"5px\"}}}})})]})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn ${isDisabled?\"disabled\":\"\"}`,disabled:isDisabled,children:translate(\"Apply\")})})]}),checkpointsEditPopup&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(CheckPointEditPopup,{checkpointsEditPopup:checkpointsEditPopup,editInteractionName:editInteractionName})}),checkpointsAddPopup&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(CheckPointAddPopup,{checklistCheckpointProperties:checklistCheckpointProperties,setChecklistCheckpointProperties:setChecklistCheckpointProperties})})]});};export default Checkpoints;export{editInteractionName};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useRef", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "CloseIcon", "useDrawerStore", "DraggableCheckpoint", "warning", "ArrowBackIosNewOutlinedIcon", "CheckPointEditPopup", "AddIcon", "CheckPointAddPopup", "AccountContext", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "editInteractionName", "Checkpoints", "_checklistGuideMetaDa3", "t", "translate", "ShowLauncherSettings", "setShowLauncherSettings", "setCheckPointsEditPopup", "checkpointsEditPopup", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checkpointsPopup", "setCheckPointsPopup", "checkpointTitleColor", "setCheckpointTitleColor", "checkpointTitleDescription", "setCheckpointTitleDescription", "checkpointIconColor", "setCheckpointIconColor", "setUnlockCheckPointInOrder", "unlockCheckPointInOrder", "checkPointMessage", "setCheckPointMessage", "setCheckPointsAddPopup", "checkpointsAddPopup", "checklistGuideMetaData", "updateChecklistCheckPoints", "deleteCheckpoint", "setIsUnSavedChanges", "isUnSavedChanges", "state", "accountId", "messageError", "setMessageError", "checklistCheckpointProperties", "setChecklistCheckpointProperties", "_checklistGuideMetaDa", "initialchecklistCheckpointProperties", "checkpoints", "checkpointsList", "checkpointTitles", "checkpointsDescription", "checkpointsIcons", "unlockCHeckpointInOrder", "message", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "initialState", "setInitialState", "_checklistGuideMetaDa2", "newCheckpoints", "updateApplyButtonState", "changed", "hasErrors", "arguments", "length", "undefined", "hasAnyChanges", "JSON", "stringify", "hasValidationErrors", "interactions", "setInteractions", "skip", "setSkip", "top", "loading", "setLoading", "dropdownRef", "handleClose", "handledesignclose", "onReselectElement", "onPropertyChange", "key", "value", "prevState", "newState", "kkk", "setKKK", "handleApplyChanges", "handleEditClick", "id", "handleDeleteClick", "interaction", "filter", "checkpoint", "draggedItemIndex", "setDraggedItemIndex", "handleDragStart", "index", "handleDragOver", "event", "preventDefault", "handleDrop", "updatedCheckpoints", "draggedItem", "splice", "handleAddCheckpoint", "handleMessageChange", "e", "target", "errorMessage", "className", "children", "onClick", "size", "sx", "backgroundColor", "borderRadius", "height", "padding", "marginBottom", "map", "isDragging", "style", "width", "color", "place<PERSON><PERSON>nt", "type", "onChange", "flexDirection", "display", "alignItems", "checked", "name", "paddingBottom", "textAlign", "variant", "placeholder", "error", "Boolean", "helperText", "fontSize", "marginRight", "dangerouslySetInnerHTML", "__html", "InputProps", "endAdornment", "border", "paddingLeft", "disabled"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/Chekpoints.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect, useContext, useRef } from \"react\";\r\nimport { <PERSON>, Typo<PERSON>, <PERSON>Field, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport DraggableCheckpoint from \"./DraggableCheckpoint\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n    Solid,\r\n    editicon,\r\n\tdeleteicon,\r\n\tdeletestep,\r\n\teditpricol,\r\n\twarning\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CheckPointEditPopup from \"./CheckpointEditPopup\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CheckPointAddPopup from \"./CheckpointAddPopup\";\r\nimport { getAllGuides } from \"../../services/GuideListServices\";\r\nimport { AccountContext } from \"../login/AccountContext\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport '../../styles/rtl_styles.scss';\r\n\r\nlet editInteractionName: string;\r\nconst Checkpoints = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tShowLauncherSettings,\r\n\t\tsetShowLauncherSettings,\r\n\t\tsetCheckPointsEditPopup,\r\n\t\tcheckpointsEditPopup,\r\n\t\ttitlePopup,\r\n\t\tsetTitlePopup,\r\n\t\tsetDesignPopup,\r\n\t\ttitleColor,\r\n\t\tsetTitleColor,\r\n\t\tcheckpointsPopup,\r\n\t\tsetCheckPointsPopup,\r\n\t\tcheckpointTitleColor,\r\n\t\tsetCheckpointTitleColor,\r\n\t\tcheckpointTitleDescription,\r\n\t\tsetCheckpointTitleDescription,\r\n\t\tcheckpointIconColor,\r\n\t\tsetCheckpointIconColor,\r\n\t\tsetUnlockCheckPointInOrder,\r\n\t\tunlockCheckPointInOrder,\r\n\t\tcheckPointMessage,\r\n\t\tsetCheckPointMessage,\r\n\t\tsetCheckPointsAddPopup,\r\n\t\tcheckpointsAddPopup,\r\n\t\tchecklistGuideMetaData,\r\n\t\tupdateChecklistCheckPoints,\r\n\t\tdeleteCheckpoint,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tisUnSavedChanges,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { accountId } = useContext(AccountContext);\r\n\tconst [messageError, setMessageError] = useState<string>(\"\");\r\n\tconst [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState<any>(() => {\r\n\t\tconst initialchecklistCheckpointProperties = checklistGuideMetaData[0]?.checkpoints || {\r\n\t\t\tcheckpointsList: [],\r\n\t\t\tcheckpointTitles: \"#333\",\r\n\t\t\tcheckpointsDescription: \"#8D8D8D\",\r\n\t\t\tcheckpointsIcons: \"#333\",\r\n\t\t\tunlockCHeckpointInOrder: false,\r\n\t\t\tmessage: \"complete items in order\",\r\n\t\t};\r\n\t\treturn initialchecklistCheckpointProperties;\r\n\t});\r\n\t// State for tracking changes and apply button\r\n\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\tconst [initialState, setInitialState] = useState(checklistCheckpointProperties);\r\n\r\n\t// Keep local state in sync with the store\r\n\tuseEffect(() => {\r\n\t\tif (checklistGuideMetaData[0]?.checkpoints) {\r\n\t\t\tconst newCheckpoints = checklistGuideMetaData[0].checkpoints;\r\n\t\t\tsetChecklistCheckpointProperties(newCheckpoints);\r\n\t\t\tsetInitialState(newCheckpoints);\r\n\t\t\tsetHasChanges(false);\r\n\t\t\tsetIsDisabled(true);\r\n\t\t}\r\n\t}, [checklistGuideMetaData[0]?.checkpoints]);\r\n\t// Function to check if the Apply button should be enabled\r\n\tconst updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {\r\n\t\tsetIsDisabled(!changed || hasErrors);\r\n\t};\r\n\r\n\t// Removed duplicate declaration of messageError\r\n\r\n\t// Effect to check for any changes compared to initial state\r\n\tuseEffect(() => {\r\n\t\t// Compare current properties with initial state\r\n\t\tconst hasAnyChanges = JSON.stringify(checklistCheckpointProperties) !== JSON.stringify(initialState);\r\n\t\tsetHasChanges(hasAnyChanges);\r\n\r\n\t\t// Check for validation errors\r\n\t\tconst hasValidationErrors = !!messageError;\r\n\r\n\t\tupdateApplyButtonState(hasAnyChanges, hasValidationErrors);\r\n\t}, [checklistCheckpointProperties, initialState, messageError]);\r\n\tconst [interactions, setInteractions] = useState<any[]>([]);\r\n\tconst [skip, setSkip] = useState(0);\r\n\tconst top = 5;\r\n\tconst [loading, setLoading] = useState(false);\r\n\tconst dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetCheckPointsPopup(false);\r\n\t};\r\n\tconst handledesignclose = () => {\r\n\t\tsetDesignPopup(false);\r\n\t};\r\n\r\n\tconst onReselectElement = () => {};\r\n\r\n\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\tsetChecklistCheckpointProperties((prevState: any) => {\r\n\t\t\tconst newState = {\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t};\r\n\t\t\t// Mark that changes have been made\r\n\t\t\tsetHasChanges(true);\r\n\t\t\treturn newState;\r\n\t\t});\r\n\t};\r\n\tconst [kkk, setKKK] = useState(false);\r\n\tconst handleApplyChanges = () => {\r\n\t\tif (kkk == false) {\r\n\t\t\t// If no changes were made to the order, use the original list\r\n\t\t\tchecklistCheckpointProperties.checkpointsList = checklistGuideMetaData[0].checkpoints.checkpointsList;\r\n\t\t}\r\n\r\n\t\tupdateChecklistCheckPoints(checklistCheckpointProperties);\r\n\t\t// Update the initial state to the current state after applying changes\r\n\t\tsetInitialState({ ...checklistCheckpointProperties });\r\n\t\t// Reset the changes flag\r\n\t\tsetHasChanges(false);\r\n\t\t// Disable the Apply button\r\n\t\tsetIsDisabled(true);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst handleEditClick = (id: any) => {\r\n\t\teditInteractionName = id;\r\n\t\tsetCheckPointsEditPopup(true);\r\n\t};\r\n\tconst handleDeleteClick = (interaction: any) => {\r\n\t\tsetChecklistCheckpointProperties((prevState: any) => ({\r\n\t\t\t...prevState,\r\n\t\t\tcheckpointsList: prevState.checkpointsList.filter((checkpoint: any) => checkpoint.interaction !== interaction),\r\n\t\t}));\r\n\t\tsetHasChanges(true);\r\n\t\tdeleteCheckpoint(interaction);\r\n\t};\r\n\tlet checkpoints = checklistCheckpointProperties.checkpointsList;\r\n\tconst [draggedItemIndex, setDraggedItemIndex] = useState<number | null>(null);\r\n\r\n\t// Handle drag start\r\n\tconst handleDragStart = (index: number) => {\r\n\t\tsetDraggedItemIndex(index);\r\n\t};\r\n\r\n\t// Handle drag over (allows dropping)\r\n\tconst handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {\r\n\t\tevent.preventDefault();\r\n\t};\r\n\r\n\t// Handle drop (reorder items)\r\n\tconst handleDrop = (index: number) => {\r\n\t\tif (draggedItemIndex === null || draggedItemIndex === index) return;\r\n\r\n\t\tconst updatedCheckpoints = [...checkpoints];\r\n\t\tconst [draggedItem] = updatedCheckpoints.splice(draggedItemIndex, 1);\r\n\t\tupdatedCheckpoints.splice(index, 0, draggedItem);\r\n\t\tsetKKK(true);\r\n\t\tcheckpoints = updatedCheckpoints;\r\n\t\tsetDraggedItemIndex(null);\r\n\r\n\t\t// Update state correctly and mark changes\r\n\t\tsetChecklistCheckpointProperties((prevState: any) => ({\r\n\t\t\t...prevState, // Copy the existing state\r\n\t\t\tcheckpointsList: updatedCheckpoints, // Update only the checkpointsList\r\n\t\t}));\r\n\t\tsetHasChanges(true);\r\n\t};\r\n\r\n\tconst handleAddCheckpoint = () => {\r\n\t\tsetCheckPointsAddPopup(true);\r\n\t};\r\n\r\n\tconst handleMessageChange = (e: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tlet errorMessage = \"\";\r\n\r\n\t\tif (value.length < 2) {\r\n\t\t\terrorMessage = translate(\"Min: 2 Characters\");\r\n\t\t} else if (value.length > 30) {\r\n\t\t\terrorMessage = translate(\"Max: 30 Characters\");\r\n\t\t}\r\n\r\n\t\tsetMessageError(errorMessage);\r\n\t\tonPropertyChange(\"message\", value);\r\n\t};\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Steps\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\tpadding: \"10px\",\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t{checkpoints.map((checkpoint: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t<DraggableCheckpoint\r\n\t\t\t\t\t\t\t\t\t\tkey={checkpoint.id}\r\n\t\t\t\t\t\t\t\t\t\tcheckpoint={checkpoint}\r\n\t\t\t\t\t\t\t\t\t\tindex={index}\r\n\t\t\t\t\t\t\t\t\t\thandleEditClick={() => handleEditClick(checkpoint?.id)}\r\n\t\t\t\t\t\t\t\t\t\thandleDeleteClick={() => handleDeleteClick(checkpoint?.interaction)}\r\n\t\t\t\t\t\t\t\t\t\thandleDragStart={handleDragStart}\r\n\t\t\t\t\t\t\t\t\t\thandleDragOver={handleDragOver}\r\n\t\t\t\t\t\t\t\t\t\thandleDrop={handleDrop}\r\n\t\t\t\t\t\t\t\t\t\tisDragging={index === draggedItemIndex}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tonClick={handleAddCheckpoint}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton qadpt-check\"\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor) !important\",\r\n\t\t\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<AddIcon  />\r\n\t\t\t\t\t\t\t\t{/* <i className=\"fal fa-add-plus\"></i> */}\r\n\t\t\t\t\t\t\t\t<span>{translate(\"Add Step\")}</span>\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Step Title\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.checkpointTitles}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"checkpointTitles\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Step Description\")}</div>\r\n\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties?.checkpointsDescription}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"checkpointsDescription\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Icon Color\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties?.checkpointsIcons}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t// Update the global Icons color\r\n\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"checkpointsIcons\", e.target.value);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ height: \"auto !important\", flexDirection: \"column\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Unlock Checkpoints in Order\")}</div>\r\n\r\n\t\t\t\t\t\t\t\t{/* Show by Default Toggle */}\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\tchecked={checklistCheckpointProperties.unlockCHeckpointInOrder}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"unlockCHeckpointInOrder\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"showByDefault\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t{checklistCheckpointProperties.unlockCHeckpointInOrder === true && (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px 8px 0 8px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ paddingBottom: \"5px\", textAlign: \"left\" }}>{translate(\"Message\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Checklist Title\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.message}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleMessageChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\terror={Boolean(messageError)} // Show error if message exists\r\n\t\t\t\t\t\t\t\t\t\t\t\thelperText={\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmessageError ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{messageError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t) : null\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\", marginBottom: \"5px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t{checkpointsEditPopup && (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<CheckPointEditPopup\r\n\t\t\t\t\t\tcheckpointsEditPopup={checkpointsEditPopup}\r\n\t\t\t\t\t\teditInteractionName={editInteractionName}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</>\r\n\t\t\t)}\r\n\r\n\t\t\t{checkpointsAddPopup && (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<CheckPointAddPopup\r\n\t\t\t\t\t\tchecklistCheckpointProperties={checklistCheckpointProperties}\r\n\t\t\t\t\t\tsetChecklistCheckpointProperties={setChecklistCheckpointProperties}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default Checkpoints;\r\nexport {editInteractionName};\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAgBC,QAAQ,CAACC,SAAS,CAAEC,UAAU,CAAEC,MAAM,KAAQ,OAAO,CACjF,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAEC,MAAM,KAAyH,eAAe,CACrM,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAA6E,yBAAyB,CAE3H,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CACvD,OASCC,OAAO,KACD,0BAA0B,CACjC,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CACvD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CAErD,OAASC,cAAc,KAAQ,yBAAyB,CACxD,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtC,GAAI,CAAAC,mBAA2B,CAC/B,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CACzB,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGX,cAAc,CAAC,CAAC,CACzC,KAAM,CACLY,oBAAoB,CACpBC,uBAAuB,CACvBC,uBAAuB,CACvBC,oBAAoB,CACpBC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,UAAU,CACVC,aAAa,CACbC,gBAAgB,CAChBC,mBAAmB,CACnBC,oBAAoB,CACpBC,uBAAuB,CACvBC,0BAA0B,CAC1BC,6BAA6B,CAC7BC,mBAAmB,CACnBC,sBAAsB,CACtBC,0BAA0B,CAC1BC,uBAAuB,CACvBC,iBAAiB,CACjBC,oBAAoB,CACpBC,sBAAsB,CACtBC,mBAAmB,CACnBC,sBAAsB,CACtBC,0BAA0B,CAC1BC,gBAAgB,CAChBC,mBAAmB,CACnBC,gBACD,CAAC,CAAG/C,cAAc,CAAEgD,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAAEC,SAAU,CAAC,CAAGzD,UAAU,CAACe,cAAc,CAAC,CAChD,KAAM,CAAC2C,YAAY,CAAEC,eAAe,CAAC,CAAG7D,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAAC8D,6BAA6B,CAAEC,gCAAgC,CAAC,CAAG/D,QAAQ,CAAM,IAAM,KAAAgE,qBAAA,CAC7F,KAAM,CAAAC,oCAAoC,CAAG,EAAAD,qBAAA,CAAAX,sBAAsB,CAAC,CAAC,CAAC,UAAAW,qBAAA,iBAAzBA,qBAAA,CAA2BE,WAAW,GAAI,CACtFC,eAAe,CAAE,EAAE,CACnBC,gBAAgB,CAAE,MAAM,CACxBC,sBAAsB,CAAE,SAAS,CACjCC,gBAAgB,CAAE,MAAM,CACxBC,uBAAuB,CAAE,KAAK,CAC9BC,OAAO,CAAE,yBACV,CAAC,CACD,MAAO,CAAAP,oCAAoC,CAC5C,CAAC,CAAC,CACF;AACA,KAAM,CAACQ,UAAU,CAAEC,aAAa,CAAC,CAAG1E,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAAC2E,UAAU,CAAEC,aAAa,CAAC,CAAG5E,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC6E,YAAY,CAAEC,eAAe,CAAC,CAAG9E,QAAQ,CAAC8D,6BAA6B,CAAC,CAE/E;AACA7D,SAAS,CAAC,IAAM,KAAA8E,sBAAA,CACf,IAAAA,sBAAA,CAAI1B,sBAAsB,CAAC,CAAC,CAAC,UAAA0B,sBAAA,WAAzBA,sBAAA,CAA2Bb,WAAW,CAAE,CAC3C,KAAM,CAAAc,cAAc,CAAG3B,sBAAsB,CAAC,CAAC,CAAC,CAACa,WAAW,CAC5DH,gCAAgC,CAACiB,cAAc,CAAC,CAChDF,eAAe,CAACE,cAAc,CAAC,CAC/BJ,aAAa,CAAC,KAAK,CAAC,CACpBF,aAAa,CAAC,IAAI,CAAC,CACpB,CACD,CAAC,CAAE,EAAA/C,sBAAA,CAAC0B,sBAAsB,CAAC,CAAC,CAAC,UAAA1B,sBAAA,iBAAzBA,sBAAA,CAA2BuC,WAAW,CAAC,CAAC,CAC5C;AACA,KAAM,CAAAe,sBAAsB,CAAG,QAAAA,CAACC,OAAgB,CAAiC,IAA/B,CAAAC,SAAkB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC3EV,aAAa,CAAC,CAACQ,OAAO,EAAIC,SAAS,CAAC,CACrC,CAAC,CAED;AAEA;AACAlF,SAAS,CAAC,IAAM,CACf;AACA,KAAM,CAAAsF,aAAa,CAAGC,IAAI,CAACC,SAAS,CAAC3B,6BAA6B,CAAC,GAAK0B,IAAI,CAACC,SAAS,CAACZ,YAAY,CAAC,CACpGD,aAAa,CAACW,aAAa,CAAC,CAE5B;AACA,KAAM,CAAAG,mBAAmB,CAAG,CAAC,CAAC9B,YAAY,CAE1CqB,sBAAsB,CAACM,aAAa,CAAEG,mBAAmB,CAAC,CAC3D,CAAC,CAAE,CAAC5B,6BAA6B,CAAEe,YAAY,CAAEjB,YAAY,CAAC,CAAC,CAC/D,KAAM,CAAC+B,YAAY,CAAEC,eAAe,CAAC,CAAG5F,QAAQ,CAAQ,EAAE,CAAC,CAC3D,KAAM,CAAC6F,IAAI,CAAEC,OAAO,CAAC,CAAG9F,QAAQ,CAAC,CAAC,CAAC,CACnC,KAAM,CAAA+F,GAAG,CAAG,CAAC,CACb,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGjG,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAAkG,WAAW,CAAG/F,MAAM,CAAiB,IAAI,CAAC,CAEhD,KAAM,CAAAgG,WAAW,CAAGA,CAAA,GAAM,CACzB3D,mBAAmB,CAAC,KAAK,CAAC,CAC3B,CAAC,CACD,KAAM,CAAA4D,iBAAiB,CAAGA,CAAA,GAAM,CAC/BhE,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAiE,iBAAiB,CAAGA,CAAA,GAAM,CAAC,CAAC,CAElC,KAAM,CAAAC,gBAAgB,CAAGA,CAACC,GAAQ,CAAEC,KAAU,GAAK,CAClDzC,gCAAgC,CAAE0C,SAAc,EAAK,CACpD,KAAM,CAAAC,QAAQ,CAAG,CAChB,GAAGD,SAAS,CACZ,CAACF,GAAG,EAAGC,KACR,CAAC,CACD;AACA5B,aAAa,CAAC,IAAI,CAAC,CACnB,MAAO,CAAA8B,QAAQ,CAChB,CAAC,CAAC,CACH,CAAC,CACD,KAAM,CAACC,GAAG,CAAEC,MAAM,CAAC,CAAG5G,QAAQ,CAAC,KAAK,CAAC,CACrC,KAAM,CAAA6G,kBAAkB,CAAGA,CAAA,GAAM,CAChC,GAAIF,GAAG,EAAI,KAAK,CAAE,CACjB;AACA7C,6BAA6B,CAACK,eAAe,CAAGd,sBAAsB,CAAC,CAAC,CAAC,CAACa,WAAW,CAACC,eAAe,CACtG,CAEAb,0BAA0B,CAACQ,6BAA6B,CAAC,CACzD;AACAgB,eAAe,CAAC,CAAE,GAAGhB,6BAA8B,CAAC,CAAC,CACrD;AACAc,aAAa,CAAC,KAAK,CAAC,CACpB;AACAF,aAAa,CAAC,IAAI,CAAC,CACnByB,WAAW,CAAC,CAAC,CACb3C,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAsD,eAAe,CAAIC,EAAO,EAAK,CACpCtF,mBAAmB,CAAGsF,EAAE,CACxB/E,uBAAuB,CAAC,IAAI,CAAC,CAC9B,CAAC,CACD,KAAM,CAAAgF,iBAAiB,CAAIC,WAAgB,EAAK,CAC/ClD,gCAAgC,CAAE0C,SAAc,GAAM,CACrD,GAAGA,SAAS,CACZtC,eAAe,CAAEsC,SAAS,CAACtC,eAAe,CAAC+C,MAAM,CAAEC,UAAe,EAAKA,UAAU,CAACF,WAAW,GAAKA,WAAW,CAC9G,CAAC,CAAC,CAAC,CACHrC,aAAa,CAAC,IAAI,CAAC,CACnBrB,gBAAgB,CAAC0D,WAAW,CAAC,CAC9B,CAAC,CACD,GAAI,CAAA/C,WAAW,CAAGJ,6BAA6B,CAACK,eAAe,CAC/D,KAAM,CAACiD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrH,QAAQ,CAAgB,IAAI,CAAC,CAE7E;AACA,KAAM,CAAAsH,eAAe,CAAIC,KAAa,EAAK,CAC1CF,mBAAmB,CAACE,KAAK,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAIC,KAAsC,EAAK,CAClEA,KAAK,CAACC,cAAc,CAAC,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAC,UAAU,CAAIJ,KAAa,EAAK,CACrC,GAAIH,gBAAgB,GAAK,IAAI,EAAIA,gBAAgB,GAAKG,KAAK,CAAE,OAE7D,KAAM,CAAAK,kBAAkB,CAAG,CAAC,GAAG1D,WAAW,CAAC,CAC3C,KAAM,CAAC2D,WAAW,CAAC,CAAGD,kBAAkB,CAACE,MAAM,CAACV,gBAAgB,CAAE,CAAC,CAAC,CACpEQ,kBAAkB,CAACE,MAAM,CAACP,KAAK,CAAE,CAAC,CAAEM,WAAW,CAAC,CAChDjB,MAAM,CAAC,IAAI,CAAC,CACZ1C,WAAW,CAAG0D,kBAAkB,CAChCP,mBAAmB,CAAC,IAAI,CAAC,CAEzB;AACAtD,gCAAgC,CAAE0C,SAAc,GAAM,CACrD,GAAGA,SAAS,CAAE;AACdtC,eAAe,CAAEyD,kBAAoB;AACtC,CAAC,CAAC,CAAC,CACHhD,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,CAED,KAAM,CAAAmD,mBAAmB,CAAGA,CAAA,GAAM,CACjC5E,sBAAsB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA6E,mBAAmB,CAAIC,CAAM,EAAK,CACvC,KAAM,CAAAzB,KAAK,CAAGyB,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAC5B,GAAI,CAAA2B,YAAY,CAAG,EAAE,CAErB,GAAI3B,KAAK,CAACnB,MAAM,CAAG,CAAC,CAAE,CACrB8C,YAAY,CAAGtG,SAAS,CAAC,mBAAmB,CAAC,CAC9C,CAAC,IAAM,IAAI2E,KAAK,CAACnB,MAAM,CAAG,EAAE,CAAE,CAC7B8C,YAAY,CAAGtG,SAAS,CAAC,oBAAoB,CAAC,CAC/C,CAEAgC,eAAe,CAACsE,YAAY,CAAC,CAC7B7B,gBAAgB,CAAC,SAAS,CAAEE,KAAK,CAAC,CACnC,CAAC,CACD,mBACClF,KAAA,QACCyF,EAAE,CAAC,mBAAmB,CACtBqB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7B/G,KAAA,QAAK8G,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7B/G,KAAA,QAAK8G,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnCjH,IAAA,CAACb,UAAU,EACV,aAAW,MAAM,CACjB+H,OAAO,CAAEnC,WAAY,CAAAkC,QAAA,cAErBjH,IAAA,CAACP,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbO,IAAA,QAAKgH,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAExG,SAAS,CAAC,OAAO,CAAC,CAAM,CAAC,cACvDT,IAAA,CAACb,UAAU,EACVgI,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBD,OAAO,CAAEnC,WAAY,CAAAkC,QAAA,cAErBjH,IAAA,CAACX,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNW,IAAA,QAAKgH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC9B/G,KAAA,QAAK8G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC3C/G,KAAA,CAAClB,GAAG,EACHoI,EAAE,CAAE,CACHC,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,6BAA6B,CAC3CC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,KACf,CAAE,CAAAR,QAAA,eAEFjH,IAAA,CAAChB,GAAG,EAAAiI,QAAA,CACFnE,WAAW,CAAC4E,GAAG,CAAC,CAAC3B,UAAe,CAAEI,KAAa,gBAC/CnG,IAAA,CAACT,mBAAmB,EAEnBwG,UAAU,CAAEA,UAAW,CACvBI,KAAK,CAAEA,KAAM,CACbT,eAAe,CAAEA,CAAA,GAAMA,eAAe,CAACK,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEJ,EAAE,CAAE,CACvDC,iBAAiB,CAAEA,CAAA,GAAMA,iBAAiB,CAACG,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEF,WAAW,CAAE,CACpEK,eAAe,CAAEA,eAAgB,CACjCE,cAAc,CAAEA,cAAe,CAC/BG,UAAU,CAAEA,UAAW,CACvBoB,UAAU,CAAExB,KAAK,GAAKH,gBAAiB,EARlCD,UAAU,CAACJ,EAShB,CACD,CAAC,CACE,CAAC,cACNzF,KAAA,WACCgH,OAAO,CAAEP,mBAAoB,CAC7BK,SAAS,CAAC,gCAAgC,CAC1CY,KAAK,CAAE,CACNC,KAAK,CAAE,MAAM,CACbR,eAAe,CAAE,SAAS,CAC1BS,KAAK,CAAE,gCAAgC,CACvCC,YAAY,CAAE,QACf,CAAE,CAAAd,QAAA,eAEFjH,IAAA,CAACL,OAAO,GAAG,CAAC,cAEZK,IAAA,SAAAiH,QAAA,CAAOxG,SAAS,CAAC,UAAU,CAAC,CAAO,CAAC,EAC7B,CAAC,EACL,CAAC,cAENP,KAAA,CAAClB,GAAG,EAACgI,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCjH,IAAA,QAAKgH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExG,SAAS,CAAC,YAAY,CAAC,CAAM,CAAC,cACpET,IAAA,QAAAiH,QAAA,cACCjH,IAAA,UACCgI,IAAI,CAAC,OAAO,CACZ5C,KAAK,CAAE1C,6BAA6B,CAACM,gBAAiB,CACtDiF,QAAQ,CAAGpB,CAAC,EAAK3B,gBAAgB,CAAC,kBAAkB,CAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CACtE4B,SAAS,CAAC,mBAAmB,CAC7B,CAAC,CACE,CAAC,EACF,CAAC,cAEN9G,KAAA,CAAClB,GAAG,EAACgI,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCjH,IAAA,QAAKgH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExG,SAAS,CAAC,kBAAkB,CAAC,CAAM,CAAC,cAE1ET,IAAA,QAAAiH,QAAA,cACCjH,IAAA,UACCgI,IAAI,CAAC,OAAO,CACZ5C,KAAK,CAAE1C,6BAA6B,SAA7BA,6BAA6B,iBAA7BA,6BAA6B,CAAEO,sBAAuB,CAC7DgF,QAAQ,CAAGpB,CAAC,EAAK3B,gBAAgB,CAAC,wBAAwB,CAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE,CAC5E4B,SAAS,CAAC,mBAAmB,CAC7B,CAAC,CACE,CAAC,EACF,CAAC,cAEN9G,KAAA,CAAClB,GAAG,EAACgI,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCjH,IAAA,QAAKgH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExG,SAAS,CAAC,YAAY,CAAC,CAAM,CAAC,cACpET,IAAA,QAAAiH,QAAA,cACCjH,IAAA,UACCgI,IAAI,CAAC,OAAO,CACZ5C,KAAK,CAAE1C,6BAA6B,SAA7BA,6BAA6B,iBAA7BA,6BAA6B,CAAEQ,gBAAiB,CACvD+E,QAAQ,CAAGpB,CAAC,EAAK,CAChB;AACA3B,gBAAgB,CAAC,kBAAkB,CAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAC,CACrD,CAAE,CACF4B,SAAS,CAAC,mBAAmB,CAC7B,CAAC,CACE,CAAC,EACF,CAAC,cAEN9G,KAAA,CAAClB,GAAG,EACHgI,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEG,MAAM,CAAE,iBAAiB,CAAEW,aAAa,CAAE,QAAS,CAAE,CAAAjB,QAAA,eAE3D/G,KAAA,QAAK0H,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAnB,QAAA,eACrDjH,IAAA,QAAKgH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAExG,SAAS,CAAC,6BAA6B,CAAC,CAAM,CAAC,cAGrFT,IAAA,QAAAiH,QAAA,cACC/G,KAAA,UAAO8G,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC/BjH,IAAA,UACCgI,IAAI,CAAC,UAAU,CACfK,OAAO,CAAE3F,6BAA6B,CAACS,uBAAwB,CAC/D8E,QAAQ,CAAGpB,CAAC,EAAK3B,gBAAgB,CAAC,yBAAyB,CAAE2B,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE,CAC/EC,IAAI,CAAC,eAAe,CACpB,CAAC,cACFtI,IAAA,SAAMgH,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACJ,CAAC,EACF,CAAC,CACLtE,6BAA6B,CAACS,uBAAuB,GAAK,IAAI,eAC9DnD,IAAA,CAAAI,SAAA,EAAA6G,QAAA,cACC/G,KAAA,CAAClB,GAAG,EACHoI,EAAE,CAAE,CACHC,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,6BAA6B,CAC3CC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,eACV,CAAE,CAAAP,QAAA,eAEFjH,IAAA,CAACf,UAAU,EAACmI,EAAE,CAAE,CAAEmB,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAvB,QAAA,CAAExG,SAAS,CAAC,SAAS,CAAC,CAAa,CAAC,cAEhGT,IAAA,CAAChB,GAAG,EACHgI,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEI,OAAO,CAAE,cAAc,CAAED,MAAM,CAAE,iBAAkB,CAAE,CAAAN,QAAA,cAE3DjH,IAAA,CAACd,SAAS,EACTuJ,OAAO,CAAC,UAAU,CAClBtB,IAAI,CAAC,OAAO,CACZuB,WAAW,CAAEjI,SAAS,CAAC,iBAAiB,CAAE,CAC1CuG,SAAS,CAAC,qBAAqB,CAC/B5B,KAAK,CAAE1C,6BAA6B,CAACU,OAAQ,CAC7CwE,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzBI,QAAQ,CAAErB,mBAAoB,CAC9B+B,KAAK,CAAEC,OAAO,CAACpG,YAAY,CAAG;AAAA,CAC9BqG,UAAU,CACTrG,YAAY,cACXtC,KAAA,SAAM0H,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEW,QAAQ,CAAE,MAAM,CAAEV,UAAU,CAAE,QAAS,CAAE,CAAAnB,QAAA,eACxEjH,IAAA,SACC4H,KAAK,CAAE,CAAEmB,WAAW,CAAE,KAAM,CAAE,CAC9BC,uBAAuB,CAAE,CAAEC,MAAM,CAAEzJ,OAAQ,CAAE,CAC7C,CAAC,CACDgD,YAAY,EACR,CAAC,CACJ,IACJ,CACD0G,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChB/B,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEgC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEZ,SAAS,CAAE,iBAAiB,CAAEa,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAE9B,MAAM,CAAE,iBAAiB,CAAEE,YAAY,CAAE,KAAM,CACzE,CACD,CAAE,CACF,CAAC,CACE,CAAC,EACF,CAAC,CACL,CACF,EACG,CAAC,EACF,CAAC,CACF,CAAC,cACNzH,IAAA,QAAKgH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClCjH,IAAA,CAACZ,MAAM,EACNqJ,OAAO,CAAC,WAAW,CACnBvB,OAAO,CAAEzB,kBAAmB,CAC5BuB,SAAS,CAAE,aAAa3D,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CACvDiG,QAAQ,CAAEjG,UAAW,CAAA4D,QAAA,CAEpBxG,SAAS,CAAC,OAAO,CAAC,CACZ,CAAC,CACL,CAAC,EACF,CAAC,CAELI,oBAAoB,eACpBb,IAAA,CAAAI,SAAA,EAAA6G,QAAA,cACCjH,IAAA,CAACN,mBAAmB,EACnBmB,oBAAoB,CAAEA,oBAAqB,CAC3CR,mBAAmB,CAAEA,mBAAoB,CACzC,CAAC,CACD,CACF,CAEA2B,mBAAmB,eACnBhC,IAAA,CAAAI,SAAA,EAAA6G,QAAA,cACCjH,IAAA,CAACJ,kBAAkB,EAClB8C,6BAA6B,CAAEA,6BAA8B,CAC7DC,gCAAgC,CAAEA,gCAAiC,CACnE,CAAC,CACD,CACF,EACG,CAAC,CAER,CAAC,CAED,cAAe,CAAArC,WAAW,CAC1B,OAAQD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}