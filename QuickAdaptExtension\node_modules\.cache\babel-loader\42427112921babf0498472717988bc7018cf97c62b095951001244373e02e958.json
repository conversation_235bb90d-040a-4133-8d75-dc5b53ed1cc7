{"ast": null, "code": "export const warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nexport const warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport const loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nexport const loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nexport const hasLoadedNamespace = function (ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nexport const getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nexport const isString = obj => typeof obj === 'string';\nexport const isObject = obj => typeof obj === 'object' && obj !== null;", "map": {"version": 3, "names": ["warn", "i18n", "code", "msg", "rest", "args", "services", "logger", "forward", "isString", "console", "alreadyWarned", "warnOnce", "Date", "loadedClb", "cb", "isInitialized", "initialized", "setTimeout", "off", "on", "loadNamespaces", "ns", "loadLanguages", "lng", "options", "preload", "indexOf", "for<PERSON>ach", "n", "push", "hasLoadedNamespace", "arguments", "length", "undefined", "languages", "precheck", "i18nInstance", "loadNotPending", "bindI18n", "backendConnector", "backend", "isLanguageChangingTo", "getDisplayName", "Component", "displayName", "name", "obj", "isObject"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/node_modules/react-i18next/dist/es/utils.js"], "sourcesContent": ["export const warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nexport const warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport const loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nexport const loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nexport const hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nexport const getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nexport const isString = obj => typeof obj === 'string';\nexport const isObject = obj => typeof obj === 'object' && obj !== null;"], "mappings": "AAAA,OAAO,MAAMA,IAAI,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC7C,MAAMC,IAAI,GAAG,CAACF,GAAG,EAAE;IACjBD,IAAI;IACJ,IAAIE,IAAI,IAAI,CAAC,CAAC;EAChB,CAAC,CAAC;EACF,IAAIH,IAAI,EAAEK,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACnC,OAAOP,IAAI,CAACK,QAAQ,CAACC,MAAM,CAACC,OAAO,CAACH,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC;EAC5E;EACA,IAAII,QAAQ,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,mBAAmBA,IAAI,CAAC,CAAC,CAAC,EAAE;EAC7D,IAAIJ,IAAI,EAAEK,QAAQ,EAAEC,MAAM,EAAEP,IAAI,EAAE;IAChCC,IAAI,CAACK,QAAQ,CAACC,MAAM,CAACP,IAAI,CAAC,GAAGK,IAAI,CAAC;EACpC,CAAC,MAAM,IAAIK,OAAO,EAAEV,IAAI,EAAE;IACxBU,OAAO,CAACV,IAAI,CAAC,GAAGK,IAAI,CAAC;EACvB;AACF,CAAC;AACD,MAAMM,aAAa,GAAG,CAAC,CAAC;AACxB,OAAO,MAAMC,QAAQ,GAAGA,CAACX,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,KAAK;EACjD,IAAIK,QAAQ,CAACN,GAAG,CAAC,IAAIQ,aAAa,CAACR,GAAG,CAAC,EAAE;EACzC,IAAIM,QAAQ,CAACN,GAAG,CAAC,EAAEQ,aAAa,CAACR,GAAG,CAAC,GAAG,IAAIU,IAAI,CAAC,CAAC;EAClDb,IAAI,CAACC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,CAAC;AAC7B,CAAC;AACD,MAAMU,SAAS,GAAGA,CAACb,IAAI,EAAEc,EAAE,KAAK,MAAM;EACpC,IAAId,IAAI,CAACe,aAAa,EAAE;IACtBD,EAAE,CAAC,CAAC;EACN,CAAC,MAAM;IACL,MAAME,WAAW,GAAGA,CAAA,KAAM;MACxBC,UAAU,CAAC,MAAM;QACfjB,IAAI,CAACkB,GAAG,CAAC,aAAa,EAAEF,WAAW,CAAC;MACtC,CAAC,EAAE,CAAC,CAAC;MACLF,EAAE,CAAC,CAAC;IACN,CAAC;IACDd,IAAI,CAACmB,EAAE,CAAC,aAAa,EAAEH,WAAW,CAAC;EACrC;AACF,CAAC;AACD,OAAO,MAAMI,cAAc,GAAGA,CAACpB,IAAI,EAAEqB,EAAE,EAAEP,EAAE,KAAK;EAC9Cd,IAAI,CAACoB,cAAc,CAACC,EAAE,EAAER,SAAS,CAACb,IAAI,EAAEc,EAAE,CAAC,CAAC;AAC9C,CAAC;AACD,OAAO,MAAMQ,aAAa,GAAGA,CAACtB,IAAI,EAAEuB,GAAG,EAAEF,EAAE,EAAEP,EAAE,KAAK;EAClD,IAAIN,QAAQ,CAACa,EAAE,CAAC,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC;EAC3B,IAAIrB,IAAI,CAACwB,OAAO,CAACC,OAAO,IAAIzB,IAAI,CAACwB,OAAO,CAACC,OAAO,CAACC,OAAO,CAACH,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAOH,cAAc,CAACpB,IAAI,EAAEqB,EAAE,EAAEP,EAAE,CAAC;EACvGO,EAAE,CAACM,OAAO,CAACC,CAAC,IAAI;IACd,IAAI5B,IAAI,CAACwB,OAAO,CAACH,EAAE,CAACK,OAAO,CAACE,CAAC,CAAC,GAAG,CAAC,EAAE5B,IAAI,CAACwB,OAAO,CAACH,EAAE,CAACQ,IAAI,CAACD,CAAC,CAAC;EAC7D,CAAC,CAAC;EACF5B,IAAI,CAACsB,aAAa,CAACC,GAAG,EAAEV,SAAS,CAACb,IAAI,EAAEc,EAAE,CAAC,CAAC;AAC9C,CAAC;AACD,OAAO,MAAMgB,kBAAkB,GAAG,SAAAA,CAACT,EAAE,EAAErB,IAAI,EAAmB;EAAA,IAAjBwB,OAAO,GAAAO,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACvD,IAAI,CAAC/B,IAAI,CAACkC,SAAS,IAAI,CAAClC,IAAI,CAACkC,SAAS,CAACF,MAAM,EAAE;IAC7CrB,QAAQ,CAACX,IAAI,EAAE,cAAc,EAAE,wCAAwC,EAAE;MACvEkC,SAAS,EAAElC,IAAI,CAACkC;IAClB,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACA,OAAOlC,IAAI,CAAC8B,kBAAkB,CAACT,EAAE,EAAE;IACjCE,GAAG,EAAEC,OAAO,CAACD,GAAG;IAChBY,QAAQ,EAAEA,CAACC,YAAY,EAAEC,cAAc,KAAK;MAC1C,IAAIb,OAAO,CAACc,QAAQ,EAAEZ,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAIU,YAAY,CAAC/B,QAAQ,CAACkC,gBAAgB,CAACC,OAAO,IAAIJ,YAAY,CAACK,oBAAoB,IAAI,CAACJ,cAAc,CAACD,YAAY,CAACK,oBAAoB,EAAEpB,EAAE,CAAC,EAAE,OAAO,KAAK;IACvN;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMqB,cAAc,GAAGC,SAAS,IAAIA,SAAS,CAACC,WAAW,IAAID,SAAS,CAACE,IAAI,KAAKrC,QAAQ,CAACmC,SAAS,CAAC,IAAIA,SAAS,CAACX,MAAM,GAAG,CAAC,GAAGW,SAAS,GAAG,SAAS,CAAC;AAC3J,OAAO,MAAMnC,QAAQ,GAAGsC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ;AACtD,OAAO,MAAMC,QAAQ,GAAGD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}