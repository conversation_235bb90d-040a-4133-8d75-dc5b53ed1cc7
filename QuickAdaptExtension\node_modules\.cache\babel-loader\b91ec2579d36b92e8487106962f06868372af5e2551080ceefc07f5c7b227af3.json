{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{Box,Typography,<PERSON>Field,Grid,IconButton,Button}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import\"./Canvas.module.css\";import useDrawerStore from\"../../store/drawerStore\";import{defaultDots,topLeft,topRight,middleLeft,middleCenter,middleRight,bottomLeft,bottomMiddle,bottomRight,topcenter,warning,centercenter}from\"../../assets/icons/icons\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CanvasSettings=_ref=>{let{zindeex,setZindeex,setShowCanvasSettings,selectedTemplate}=_ref;const{setCanvasSetting,borderColor,announcementJson,width,setWidth,backgroundColor,setBorderColor,setBackgroundColor,borderRadius,setBorderRadius,Annpadding,setAnnPadding,AnnborderSize,setAnnBorderSize,Bposition,setBposition,setIsUnSavedChanges,currentStep,syncAIAnnouncementCanvasSettings,createWithAI,//selectedTemplate,\nselectedTemplateTour}=useDrawerStore(state=>state);const{t:translate}=useTranslation();const[isOpen,setIsOpen]=useState(true);const[selectedPosition,setSelectedPosition]=useState(\"middle-center\");const[widthError,setWidthError]=useState(false);const[paddingError,setPaddingError]=useState(false);const[borderRadiusError,setBorderRadiusError]=useState(false);const[borderSizeError,setBorderSizeError]=useState(false);const positions=[{label:translate(\"Top Left\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:topLeft},style:{fontSize:\"small\"}}),value:\"top-left\"},{label:translate(\"Top Center\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:topcenter},style:{fontSize:\"small\"}}),value:\"top-center\"},{label:translate(\"Top Right\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:topRight},style:{fontSize:\"small\"}}),value:\"top-right\"},{label:translate(\"Middle Left\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:middleLeft},style:{fontSize:\"small\"}}),value:\"left-center\"},{label:translate(\"Middle Center\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:middleCenter},style:{fontSize:\"small\"}}),value:\"center-center\"},{label:translate(\"Middle Right\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:middleRight},style:{fontSize:\"small\"}}),value:\"right-center\"},{label:translate(\"Bottom Left\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:bottomLeft},style:{fontSize:\"small\"}}),value:\"bottom-left\"},{label:translate(\"Bottom Center\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:bottomMiddle},style:{fontSize:\"small\"}}),value:\"bottom-center\"},{label:translate(\"Bottom Right\"),icon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:bottomRight},style:{fontSize:\"small\"}}),value:\"bottom-right\"}];const renderPositionIcon=positionValue=>{const isSelected=Bposition===positionValue;if(!isSelected){return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:defaultDots},style:{fontSize:\"small\"}});}switch(positionValue){case\"top-left\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:topLeft},style:{fontSize:\"small\"}});case\"top-center\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:topcenter},style:{fontSize:\"small\"}});case\"top-right\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:topRight},style:{fontSize:\"small\"}});case\"left-center\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:middleLeft},style:{fontSize:\"small\"}});case\"center-center\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:middleCenter},style:{fontSize:\"small\"}});case\"right-center\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:middleRight},style:{fontSize:\"small\"}});case\"bottom-left\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:bottomLeft},style:{fontSize:\"small\"}});case\"bottom-center\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:bottomMiddle},style:{fontSize:\"small\"}});case\"bottom-right\":return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:bottomRight},style:{fontSize:\"small\"}});default:return/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:defaultDots},style:{fontSize:\"small\"}});}};useEffect(()=>{var _announcementJson$Gui,_announcementJson$Gui2,_announcementJson$Gui3,_announcementJson$Gui4,_announcementJson$Gui5;const currentStepIndex=announcementJson===null||announcementJson===void 0?void 0:(_announcementJson$Gui=announcementJson.GuideStep)===null||_announcementJson$Gui===void 0?void 0:_announcementJson$Gui.findIndex(step=>{let stepNum=step.stepName;// \nif(typeof stepNum===\"string\"&&stepNum.toLowerCase().startsWith(\"step\")){stepNum=parseInt(stepNum.replace(/[^0-9]/g,\"\"),10);}return String(stepNum)===String(currentStep);});// Get canvas data for the current step (if found), otherwise use the first step\nconst canvasData=currentStepIndex!==-1?announcementJson===null||announcementJson===void 0?void 0:(_announcementJson$Gui2=announcementJson.GuideStep)===null||_announcementJson$Gui2===void 0?void 0:(_announcementJson$Gui3=_announcementJson$Gui2[currentStepIndex])===null||_announcementJson$Gui3===void 0?void 0:_announcementJson$Gui3.Canvas:announcementJson===null||announcementJson===void 0?void 0:(_announcementJson$Gui4=announcementJson.GuideStep)===null||_announcementJson$Gui4===void 0?void 0:(_announcementJson$Gui5=_announcementJson$Gui4[0])===null||_announcementJson$Gui5===void 0?void 0:_announcementJson$Gui5.Canvas;let initialWidth;let initialPadding;let initialBorderRadius;let initialBorderSize;if(canvasData){var _canvasData$Radius;setSelectedPosition(canvasData.Position||\"center-center\");setBposition(canvasData.Position||\"center-center\");initialWidth=canvasData.Width||500;setBackgroundColor(canvasData.BackgroundColor||\"#ffffff\");initialBorderRadius=(_canvasData$Radius=canvasData.Radius)!==null&&_canvasData$Radius!==void 0?_canvasData$Radius:8;initialPadding=canvasData.Padding||\"12\";setBorderColor(canvasData.BorderColor||\"#000000\");initialBorderSize=canvasData.BorderSize||0;}else{setSelectedPosition(\"center-center\");setBposition(\"center-center\");initialWidth=500;setBackgroundColor(\"#ffffff\");initialBorderRadius=8;initialPadding=\"12\";setBorderColor(\"#000000\");initialBorderSize=0;}// Validate initial width\nif(initialWidth<300||initialWidth>1200){setWidthError(true);// Set width to closest valid value\nsetWidth(initialWidth<300?300:1200);}else{setWidthError(false);setWidth(initialWidth);}// Validate initial padding\nconst paddingValue=parseInt(initialPadding)||12;if(paddingValue<0||paddingValue>20){setPaddingError(true);// Set padding to closest valid value\nsetAnnPadding(paddingValue<0?\"0\":\"20\");}else{setPaddingError(false);setAnnPadding(initialPadding);}// Validate initial border radius\nif(initialBorderRadius<0||initialBorderRadius>20){setBorderRadiusError(true);// Set border radius to closest valid value\nsetBorderRadius(initialBorderRadius<0?0:20);}else{setBorderRadiusError(false);setBorderRadius(initialBorderRadius);}// Validate initial border size\nif(initialBorderSize<0||initialBorderSize>10){setBorderSizeError(true);// Set border size to closest valid value\nsetAnnBorderSize(initialBorderSize<0?0:10);}else{setBorderSizeError(false);setAnnBorderSize(initialBorderSize);}},[announcementJson,currentStep,setBposition,setWidth,setBackgroundColor,setBorderRadius,setAnnPadding,setBorderColor,setAnnBorderSize]);const handlePositionClick=positionValue=>{setSelectedPosition(positionValue);setBposition(positionValue);};const handleBorderColorChange=e=>setBorderColor(e.target.value);const handleBackgroundColorChange=e=>setBackgroundColor(e.target.value);const handleClose=()=>{setIsOpen(false);setShowCanvasSettings(false);};const handleApplyChanges=()=>{// Don't apply changes if there's any validation error\nif(widthError||paddingError||borderRadiusError||borderSizeError){return;}const canvasData={Position:selectedPosition,BackgroundColor:backgroundColor,Width:width||500,Radius:borderRadius!==undefined?borderRadius:0,Padding:Annpadding||\"12\",BorderColor:borderColor,BorderSize:AnnborderSize||0,Zindex:9999};setCanvasSetting(canvasData);// Sync AI announcement canvas settings after applying changes\nif(createWithAI&&(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\")){// Use setTimeout to ensure setCanvasSetting completes first\nsetTimeout(()=>{syncAIAnnouncementCanvasSettings(canvasData);},0);}setBposition(selectedPosition);handleClose();setIsUnSavedChanges(true);};if(!isOpen)return null;return/*#__PURE__*/(//<Draggable>\n_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Canvas\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls qadpt-errmsg\",children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-position-grid\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-ctrl-title\",children:translate(\"Position\")}),/*#__PURE__*/_jsx(Grid,{id:\"pos-container\",container:true,spacing:1//onClick={handlePositionClick}\n,children:positions.map(position=>/*#__PURE__*/_jsx(Grid,{item:true,xs:4,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",disableRipple:true// sx={{\n//     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\n// }}\n,onClick:()=>handlePositionClick(position.value)// Pass value directly\n,children:renderPositionIcon(position.value)})},position.value))})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Width\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:`${width}`,size:\"small\",autoFocus:true,className:\"qadpt-control-input\",onChange:e=>{const inputValue=parseInt(e.target.value)||0;// Validate width between 300px and 1200px\nif(inputValue<300||inputValue>1200){setWidthError(true);}else{setWidthError(false);}setWidth(inputValue);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:widthError})})]}),widthError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\",alignItems:centercenter},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Value must be between 300px and 1200px.\")]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Padding\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:`${Annpadding}`,fullWidth:true,size:\"small\",className:\"qadpt-control-input\",onChange:e=>{const inputValue=parseInt(e.target.value)||0;// Validate padding between 0px and 20px\nif(inputValue<0||inputValue>20){setPaddingError(true);}else{setPaddingError(false);}setAnnPadding(inputValue.toString());},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:paddingError})})]}),paddingError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\",alignItems:centercenter},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Value must be between 0px and 20px.\")]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Border Radius\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:`${borderRadius}`,fullWidth:true,size:\"small\",className:\"qadpt-control-input\",onChange:e=>{const inputValue=parseInt(e.target.value)||0;// Validate border radius between 0px and 20px\nif(inputValue<0||inputValue>20){setBorderRadiusError(true);// Set border radius to closest valid value\n// setBorderRadius(inputValue < 0 ? 0 : 20);\n}else{setBorderRadiusError(false);}setBorderRadius(inputValue);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:borderRadiusError})})]}),borderRadiusError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\",alignItems:centercenter},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Value must be between 0px and 20px.\")]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Border Size\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:`${AnnborderSize}`,fullWidth:true,size:\"small\",className:\"qadpt-control-input\",onChange:e=>{const inputValue=parseInt(e.target.value)||0;// Validate border size between 0px and 10px\nif(inputValue<0||inputValue>10){setBorderSizeError(true);}else{setBorderSizeError(false);}setAnnBorderSize(inputValue);},InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}},error:borderSizeError})})]}),borderSizeError&&/*#__PURE__*/_jsxs(Typography,{style:{fontSize:\"12px\",color:\"#e9a971\",textAlign:\"left\",top:\"100%\",left:0,marginBottom:\"5px\",display:\"flex\",alignItems:centercenter},children:[/*#__PURE__*/_jsx(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\",marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(\"Value must be between 0px and 10px.\")]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Border\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:borderColor,onChange:handleBorderColorChange,className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate(\"Background\")}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:backgroundColor,onChange:handleBackgroundColorChange,className:\"qadpt-color-input\"})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn ${widthError||paddingError||borderRadiusError||borderSizeError?\"disabled\":\"\"}`,disabled:widthError||paddingError||borderRadiusError||borderSizeError,children:translate(\"Apply\")})})]})})//</Draggable>\n);};export default CanvasSettings;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "defaultDots", "topLeft", "topRight", "middleLeft", "middleCenter", "middleRight", "bottomLeft", "bottomMiddle", "bottomRight", "topcenter", "warning", "centercenter", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "CanvasSettings", "_ref", "zindeex", "setZindeex", "setShowCanvasSettings", "selectedTemplate", "setCanvasSetting", "borderColor", "announcement<PERSON><PERSON>", "width", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "setBorderColor", "setBackgroundColor", "borderRadius", "setBorderRadius", "Annpadding", "setAnnPadding", "AnnborderSize", "setAnnBorderSize", "Bposition", "setBposition", "setIsUnSavedChanges", "currentStep", "syncAIAnnouncementCanvasSettings", "createWithAI", "selectedTemplateTour", "state", "t", "translate", "isOpen", "setIsOpen", "selectedPosition", "setSelectedPosition", "widthError", "setWidthError", "paddingError", "setPaddingError", "borderRadiusError", "setBorderRadiusError", "borderSizeError", "setBorderSizeError", "positions", "label", "icon", "dangerouslySetInnerHTML", "__html", "style", "fontSize", "value", "renderPositionIcon", "positionValue", "isSelected", "_announcementJson$Gui", "_announcementJson$Gui2", "_announcementJson$Gui3", "_announcementJson$Gui4", "_announcementJson$Gui5", "currentStepIndex", "GuideStep", "findIndex", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "toLowerCase", "startsWith", "parseInt", "replace", "String", "canvasData", "<PERSON><PERSON>", "initialWidth", "initialPadding", "initialBorderRadius", "initialBorderSize", "_canvasData$Radius", "Position", "<PERSON><PERSON><PERSON>", "BackgroundColor", "<PERSON><PERSON>", "Padding", "BorderColor", "BorderSize", "paddingValue", "handlePositionClick", "handleBorderColorChange", "e", "target", "handleBackgroundColorChange", "handleClose", "handleApplyChanges", "undefined", "Zindex", "setTimeout", "id", "className", "children", "onClick", "size", "container", "spacing", "map", "position", "item", "xs", "disable<PERSON><PERSON><PERSON>", "variant", "autoFocus", "onChange", "inputValue", "InputProps", "endAdornment", "sx", "border", "error", "color", "textAlign", "top", "left", "marginBottom", "display", "alignItems", "marginRight", "fullWidth", "toString", "type", "disabled"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideDesign/CanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport RadioButtonUncheckedIcon from \"@mui/icons-material/RadioButtonUnchecked\";\r\nimport RadioButtonCheckedIcon from \"@mui/icons-material/RadioButtonChecked\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { defaultDots,topLeft,topCenter,topRight,middleLeft,middleCenter,middleRight,bottomLeft,bottomMiddle,bottomRight, topcenter, warning, centercenter } from \"../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\nconst CanvasSettings = ({ zindeex, setZindeex, setShowCanvasSettings, selectedTemplate }: any) => {\r\n\tconst {\r\n\t\tsetCanvasSetting,\r\n\t\tborderColor,\r\n\t\tannouncementJson,\r\n\t\twidth,\r\n\t\tsetWidth,\r\n\t\tbackgroundColor,\r\n\t\tsetBorderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tborderRadius,\r\n\t\tsetBorderRadius,\r\n\t\tAnnpadding,\r\n\t\tsetAnnPadding,\r\n\t\tAnnborderSize,\r\n\t\tsetAnnBorderSize,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tcurrentStep,\r\n\t\tsyncAIAnnouncementCanvasSettings,\r\n\t\tcreateWithAI,\r\n\t\t//selectedTemplate,\r\n\t\tselectedTemplateTour\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [borderRadiusError, setBorderRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\"), icon: <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\"), icon: <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />, value: \"top-center\" },\r\n\t\t{ label: translate(\"Top Right\"), icon: <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\"), icon: <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />, value: \"left-center\" },\r\n\t\t{ label: translate(\"Middle Center\"), icon: <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />, value: \"center-center\" },\r\n\t\t{ label: translate(\"Middle Right\"), icon: <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />, value: \"right-center\" },\r\n\t\t{ label: translate(\"Bottom Left\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />, value: \"bottom-center\" },\r\n\t\t{ label: translate(\"Bottom Right\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />, value: \"bottom-right\" },\r\n\t];\r\n\tconst renderPositionIcon = (positionValue:any) => {\r\n\t\tconst isSelected = Bposition === positionValue;\r\n\r\n\t\tif (!isSelected) {\r\n\t\t  return <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\r\n\t\tswitch (positionValue) {\r\n\t\t  case \"top-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"left-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"center-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"right-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  default:\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\t  };\r\n\r\n\tuseEffect(() => {\r\n\t\tconst currentStepIndex = announcementJson?.GuideStep?.findIndex(\r\n\t\t\t(step: any) => {\r\n\t\t\t\tlet stepNum = step.stepName;// \r\n\t\t\t\tif (typeof stepNum === \"string\" && stepNum.toLowerCase().startsWith(\"step\")) {\r\n\t\t\t\t\tstepNum = parseInt(stepNum.replace(/[^0-9]/g, \"\"), 10);\r\n\t\t\t\t}\r\n\t\t\t\treturn String(stepNum) === String(currentStep); \r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Get canvas data for the current step (if found), otherwise use the first step\r\n\t\tconst canvasData = (currentStepIndex !== -1)\r\n\t\t\t? announcementJson?.GuideStep?.[currentStepIndex]?.Canvas\r\n\t\t\t: announcementJson?.GuideStep?.[0]?.Canvas;\r\n\r\n\t\tlet initialWidth;\r\n\t\tlet initialPadding;\r\n\t\tlet initialBorderRadius;\r\n\t\tlet initialBorderSize;\r\n\r\n\t\tif (canvasData) {\r\n\t\t\tsetSelectedPosition(canvasData.Position || \"center-center\");\r\n\t\t\tsetBposition(canvasData.Position || \"center-center\");\r\n\t\t\tinitialWidth = canvasData.Width || 500;\r\n\t\t\tsetBackgroundColor(canvasData.BackgroundColor || \"#ffffff\");\r\n\t\t\tinitialBorderRadius = canvasData.Radius ?? 8;\r\n\t\t\tinitialPadding = canvasData.Padding || \"12\";\r\n\t\t\tsetBorderColor(canvasData.BorderColor || \"#000000\");\r\n\t\t\tinitialBorderSize = canvasData.BorderSize || 0;\r\n\t\t} else {\r\n\t\t\tsetSelectedPosition(\"center-center\");\r\n\t\t\tsetBposition(\"center-center\");\r\n\t\t\tinitialWidth = 500;\r\n\t\t\tsetBackgroundColor(\"#ffffff\");\r\n\t\t\tinitialBorderRadius = 8;\r\n\t\t\tinitialPadding = \"12\";\r\n\t\t\tsetBorderColor(\"#000000\");\r\n\t\t\tinitialBorderSize = 0;\r\n\t\t}\r\n\r\n\t\t// Validate initial width\r\n\t\tif (initialWidth < 300 || initialWidth > 1200) {\r\n\t\t\tsetWidthError(true);\r\n\t\t\t// Set width to closest valid value\r\n\t\t\tsetWidth(initialWidth < 300 ? 300 : 1200);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t\tsetWidth(initialWidth);\r\n\t\t}\r\n\r\n\t\t// Validate initial padding\r\n\t\tconst paddingValue = parseInt(initialPadding) || 12;\r\n\t\tif (paddingValue < 0 || paddingValue > 20) {\r\n\t\t\tsetPaddingError(true);\r\n\t\t\t// Set padding to closest valid value\r\n\t\t\tsetAnnPadding(paddingValue < 0 ? \"0\" : \"20\");\r\n\t\t} else {\r\n\t\t\tsetPaddingError(false);\r\n\t\t\tsetAnnPadding(initialPadding);\r\n\t\t}\r\n\r\n\t\t// Validate initial border radius\r\n\t\tif (initialBorderRadius < 0 || initialBorderRadius > 20) {\r\n\t\t\tsetBorderRadiusError(true);\r\n\t\t\t// Set border radius to closest valid value\r\n\t\t\tsetBorderRadius(initialBorderRadius < 0 ? 0 : 20);\r\n\t\t} else {\r\n\t\t\tsetBorderRadiusError(false);\r\n\t\t\tsetBorderRadius(initialBorderRadius);\r\n\t\t}\r\n\r\n\t\t// Validate initial border size\r\n\t\tif (initialBorderSize < 0 || initialBorderSize > 10) {\r\n\t\t\tsetBorderSizeError(true);\r\n\t\t\t// Set border size to closest valid value\r\n\t\t\tsetAnnBorderSize(initialBorderSize < 0 ? 0 : 10);\r\n\t\t} else {\r\n\t\t\tsetBorderSizeError(false);\r\n\t\t\tsetAnnBorderSize(initialBorderSize);\r\n\t\t}\r\n\t}, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);\r\n\r\n\tconst handlePositionClick = (positionValue: string) => {\r\n\t\tsetSelectedPosition(positionValue);\r\n\t\tsetBposition(positionValue);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => setBorderColor(e.target.value);\r\n\tconst handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowCanvasSettings(false);\r\n\t};\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Don't apply changes if there's any validation error\r\n\t\tif (widthError || paddingError || borderRadiusError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst canvasData = {\r\n\t\t\tPosition: selectedPosition,\r\n\t\t\tBackgroundColor: backgroundColor,\r\n\t\t\tWidth: width || 500,\r\n\t\t\tRadius: borderRadius !== undefined ? borderRadius : 0,\r\n\t\t\tPadding: Annpadding || \"12\",\r\n\t\t\tBorderColor: borderColor,\r\n\t\t\tBorderSize: AnnborderSize || 0,\r\n\t\t\tZindex: 9999,\r\n\t\t};\r\n\r\n\t\tsetCanvasSetting(canvasData);\r\n\r\n\t\t// Sync AI announcement canvas settings after applying changes\r\n\t\tif (createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")) {\r\n\t\t\t// Use setTimeout to ensure setCanvasSetting completes first\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tsyncAIAnnouncementCanvasSettings(canvasData);\r\n\t\t\t}, 0);\r\n\t\t}\r\n\r\n\t\tsetBposition(selectedPosition);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Canvas\")}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Position Grid */}\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t<Box className=\"qadpt-position-grid\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\">{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t<Grid\r\n\t\t\t\t\t\tid=\"pos-container\"\r\n\t\t\t\t\t\tcontainer\r\n\t\t\t\t\t\tspacing={1}\r\n\t\t\t\t\t\t//onClick={handlePositionClick}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Position Icons */}\r\n\t\t\t\t\t\t{positions.map((position) => (\r\n\t\t\t\t\t\t\t<Grid item xs={4} key={position.value}>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tdisableRipple\r\n\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t//     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handlePositionClick(position.value)} // Pass value directly\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{renderPositionIcon(position.value)}\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t{/* Width Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Width\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${width}`}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate width between 300px and 1200px\r\n\t\t\t\t\t\t\t\tif (inputValue < 300 || inputValue > 1200) {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetWidth(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 300px and 1200px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t{/* Height Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Height</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${height}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetHeight(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Padding Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Padding\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${Annpadding}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnPadding(inputValue.toString());\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Border Radius Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Radius\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${borderRadius}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border radius between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t// Set border radius to closest valid value\r\n\t\t\t\t\t\t\t\t\t// setBorderRadius(inputValue < 0 ? 0 : 20);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetBorderRadius(inputValue);\r\n\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\terror={borderRadiusError}\r\n\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Size\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${AnnborderSize}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border size between 0px and 10px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 10) {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnBorderSize(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderSizeError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderSizeError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 10px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* Zindex value Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Z-Index</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${zindeex}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetZindeex(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Border Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={borderColor}\r\n\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t{/* Background Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={backgroundColor}\r\n\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={widthError || paddingError || borderRadiusError || borderSizeError}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default CanvasSettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAEC,IAAI,CAAEC,UAAU,CAAEC,MAAM,KAAiB,eAAe,CAC7F,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAGjD;AACA,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CACrF,MAAO,qBAAqB,CAC5B,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,OAASC,WAAW,CAACC,OAAO,CAAWC,QAAQ,CAACC,UAAU,CAACC,YAAY,CAACC,WAAW,CAACC,UAAU,CAACC,YAAY,CAACC,WAAW,CAAEC,SAAS,CAAEC,OAAO,CAAEC,YAAY,KAAQ,0BAA0B,CAC3L,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAC/C,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAA2E,IAA1E,CAAEC,OAAO,CAAEC,UAAU,CAAEC,qBAAqB,CAAEC,gBAAsB,CAAC,CAAAJ,IAAA,CAC5F,KAAM,CACLK,gBAAgB,CAChBC,WAAW,CACXC,gBAAgB,CAChBC,KAAK,CACLC,QAAQ,CACRC,eAAe,CACfC,cAAc,CACdC,kBAAkB,CAClBC,YAAY,CACZC,eAAe,CACfC,UAAU,CACVC,aAAa,CACbC,aAAa,CACbC,gBAAgB,CAChBC,SAAS,CACTC,YAAY,CACZC,mBAAmB,CACnBC,WAAW,CACXC,gCAAgC,CAChCC,YAAY,CACZ;AACAC,oBACD,CAAC,CAAG5C,cAAc,CAAE6C,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGlC,cAAc,CAAC,CAAC,CACzC,KAAM,CAACmC,MAAM,CAAEC,SAAS,CAAC,CAAG1D,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAC2D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG5D,QAAQ,CAAC,eAAe,CAAC,CACzE,KAAM,CAAC6D,UAAU,CAAEC,aAAa,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC+D,YAAY,CAAEC,eAAe,CAAC,CAAGhE,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACiE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlE,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACmE,eAAe,CAAEC,kBAAkB,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAAqE,SAAS,CAAG,CACjB,CAAEC,KAAK,CAAEd,SAAS,CAAC,UAAU,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE9D,OAAQ,CAAE,CAAC+D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,UAAW,CAAC,CAC/I,CAAEN,KAAK,CAAEd,SAAS,CAAC,YAAY,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAEtD,SAAU,CAAE,CAACuD,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,YAAa,CAAC,CACrJ,CAAEN,KAAK,CAAEd,SAAS,CAAC,WAAW,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE7D,QAAS,CAAE,CAAC8D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,WAAY,CAAC,CAClJ,CAAEN,KAAK,CAAEd,SAAS,CAAC,aAAa,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE5D,UAAW,CAAE,CAAC6D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,aAAc,CAAC,CACxJ,CAAEN,KAAK,CAAEd,SAAS,CAAC,eAAe,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE3D,YAAa,CAAE,CAAC4D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,eAAgB,CAAC,CAC9J,CAAEN,KAAK,CAAEd,SAAS,CAAC,cAAc,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE1D,WAAY,CAAE,CAAC2D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC3J,CAAEN,KAAK,CAAEd,SAAS,CAAC,aAAa,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAEzD,UAAW,CAAE,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,aAAc,CAAC,CACxJ,CAAEN,KAAK,CAAEd,SAAS,CAAC,eAAe,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAExD,YAAa,CAAE,CAACyD,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,eAAgB,CAAC,CAC9J,CAAEN,KAAK,CAAEd,SAAS,CAAC,cAAc,CAAC,CAAEe,IAAI,cAAE/C,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAEvD,WAAY,CAAE,CAACwD,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAAEC,KAAK,CAAE,cAAe,CAAC,CAC3J,CACD,KAAM,CAAAC,kBAAkB,CAAIC,aAAiB,EAAK,CACjD,KAAM,CAAAC,UAAU,CAAGhC,SAAS,GAAK+B,aAAa,CAE9C,GAAI,CAACC,UAAU,CAAE,CACf,mBAAOvD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE/D,WAAY,CAAE,CAACgE,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CACjG,CAEA,OAAQG,aAAa,EACnB,IAAK,UAAU,CAChB,mBAAOtD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE9D,OAAQ,CAAE,CAAC+D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC1F,IAAK,YAAY,CAClB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAEtD,SAAU,CAAE,CAACuD,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC5F,IAAK,WAAW,CACjB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE7D,QAAS,CAAE,CAAC8D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC3F,IAAK,aAAa,CACnB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE5D,UAAW,CAAE,CAAC6D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC7F,IAAK,eAAe,CACrB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE3D,YAAa,CAAE,CAAC4D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC/F,IAAK,cAAc,CACpB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE1D,WAAY,CAAE,CAAC2D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC9F,IAAK,aAAa,CACnB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAEzD,UAAW,CAAE,CAAC0D,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC7F,IAAK,eAAe,CACrB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAExD,YAAa,CAAE,CAACyD,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC/F,IAAK,cAAc,CACpB,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAEvD,WAAY,CAAE,CAACwD,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC9F,QACD,mBAAOnD,IAAA,SAAMgD,uBAAuB,CAAE,CAAEC,MAAM,CAAE/D,WAAY,CAAE,CAACgE,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAE,CAAC,CAChG,CACC,CAAC,CAEH5E,SAAS,CAAC,IAAM,KAAAiF,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACf,KAAM,CAAAC,gBAAgB,CAAGlD,gBAAgB,SAAhBA,gBAAgB,kBAAA6C,qBAAA,CAAhB7C,gBAAgB,CAAEmD,SAAS,UAAAN,qBAAA,iBAA3BA,qBAAA,CAA6BO,SAAS,CAC7DC,IAAS,EAAK,CACd,GAAI,CAAAC,OAAO,CAAGD,IAAI,CAACE,QAAQ,CAAC;AAC5B,GAAI,MAAO,CAAAD,OAAO,GAAK,QAAQ,EAAIA,OAAO,CAACE,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,MAAM,CAAC,CAAE,CAC5EH,OAAO,CAAGI,QAAQ,CAACJ,OAAO,CAACK,OAAO,CAAC,SAAS,CAAE,EAAE,CAAC,CAAE,EAAE,CAAC,CACvD,CACA,MAAO,CAAAC,MAAM,CAACN,OAAO,CAAC,GAAKM,MAAM,CAAC7C,WAAW,CAAC,CAC/C,CACD,CAAC,CAED;AACA,KAAM,CAAA8C,UAAU,CAAIX,gBAAgB,GAAK,CAAC,CAAC,CACxClD,gBAAgB,SAAhBA,gBAAgB,kBAAA8C,sBAAA,CAAhB9C,gBAAgB,CAAEmD,SAAS,UAAAL,sBAAA,kBAAAC,sBAAA,CAA3BD,sBAAA,CAA8BI,gBAAgB,CAAC,UAAAH,sBAAA,iBAA/CA,sBAAA,CAAiDe,MAAM,CACvD9D,gBAAgB,SAAhBA,gBAAgB,kBAAAgD,sBAAA,CAAhBhD,gBAAgB,CAAEmD,SAAS,UAAAH,sBAAA,kBAAAC,sBAAA,CAA3BD,sBAAA,CAA8B,CAAC,CAAC,UAAAC,sBAAA,iBAAhCA,sBAAA,CAAkCa,MAAM,CAE3C,GAAI,CAAAC,YAAY,CAChB,GAAI,CAAAC,cAAc,CAClB,GAAI,CAAAC,mBAAmB,CACvB,GAAI,CAAAC,iBAAiB,CAErB,GAAIL,UAAU,CAAE,KAAAM,kBAAA,CACf1C,mBAAmB,CAACoC,UAAU,CAACO,QAAQ,EAAI,eAAe,CAAC,CAC3DvD,YAAY,CAACgD,UAAU,CAACO,QAAQ,EAAI,eAAe,CAAC,CACpDL,YAAY,CAAGF,UAAU,CAACQ,KAAK,EAAI,GAAG,CACtChE,kBAAkB,CAACwD,UAAU,CAACS,eAAe,EAAI,SAAS,CAAC,CAC3DL,mBAAmB,EAAAE,kBAAA,CAAGN,UAAU,CAACU,MAAM,UAAAJ,kBAAA,UAAAA,kBAAA,CAAI,CAAC,CAC5CH,cAAc,CAAGH,UAAU,CAACW,OAAO,EAAI,IAAI,CAC3CpE,cAAc,CAACyD,UAAU,CAACY,WAAW,EAAI,SAAS,CAAC,CACnDP,iBAAiB,CAAGL,UAAU,CAACa,UAAU,EAAI,CAAC,CAC/C,CAAC,IAAM,CACNjD,mBAAmB,CAAC,eAAe,CAAC,CACpCZ,YAAY,CAAC,eAAe,CAAC,CAC7BkD,YAAY,CAAG,GAAG,CAClB1D,kBAAkB,CAAC,SAAS,CAAC,CAC7B4D,mBAAmB,CAAG,CAAC,CACvBD,cAAc,CAAG,IAAI,CACrB5D,cAAc,CAAC,SAAS,CAAC,CACzB8D,iBAAiB,CAAG,CAAC,CACtB,CAEA;AACA,GAAIH,YAAY,CAAG,GAAG,EAAIA,YAAY,CAAG,IAAI,CAAE,CAC9CpC,aAAa,CAAC,IAAI,CAAC,CACnB;AACAzB,QAAQ,CAAC6D,YAAY,CAAG,GAAG,CAAG,GAAG,CAAG,IAAI,CAAC,CAC1C,CAAC,IAAM,CACNpC,aAAa,CAAC,KAAK,CAAC,CACpBzB,QAAQ,CAAC6D,YAAY,CAAC,CACvB,CAEA;AACA,KAAM,CAAAY,YAAY,CAAGjB,QAAQ,CAACM,cAAc,CAAC,EAAI,EAAE,CACnD,GAAIW,YAAY,CAAG,CAAC,EAAIA,YAAY,CAAG,EAAE,CAAE,CAC1C9C,eAAe,CAAC,IAAI,CAAC,CACrB;AACApB,aAAa,CAACkE,YAAY,CAAG,CAAC,CAAG,GAAG,CAAG,IAAI,CAAC,CAC7C,CAAC,IAAM,CACN9C,eAAe,CAAC,KAAK,CAAC,CACtBpB,aAAa,CAACuD,cAAc,CAAC,CAC9B,CAEA;AACA,GAAIC,mBAAmB,CAAG,CAAC,EAAIA,mBAAmB,CAAG,EAAE,CAAE,CACxDlC,oBAAoB,CAAC,IAAI,CAAC,CAC1B;AACAxB,eAAe,CAAC0D,mBAAmB,CAAG,CAAC,CAAG,CAAC,CAAG,EAAE,CAAC,CAClD,CAAC,IAAM,CACNlC,oBAAoB,CAAC,KAAK,CAAC,CAC3BxB,eAAe,CAAC0D,mBAAmB,CAAC,CACrC,CAEA;AACA,GAAIC,iBAAiB,CAAG,CAAC,EAAIA,iBAAiB,CAAG,EAAE,CAAE,CACpDjC,kBAAkB,CAAC,IAAI,CAAC,CACxB;AACAtB,gBAAgB,CAACuD,iBAAiB,CAAG,CAAC,CAAG,CAAC,CAAG,EAAE,CAAC,CACjD,CAAC,IAAM,CACNjC,kBAAkB,CAAC,KAAK,CAAC,CACzBtB,gBAAgB,CAACuD,iBAAiB,CAAC,CACpC,CACD,CAAC,CAAE,CAAClE,gBAAgB,CAAEe,WAAW,CAAEF,YAAY,CAAEX,QAAQ,CAAEG,kBAAkB,CAAEE,eAAe,CAAEE,aAAa,CAAEL,cAAc,CAAEO,gBAAgB,CAAC,CAAC,CAEjJ,KAAM,CAAAiE,mBAAmB,CAAIjC,aAAqB,EAAK,CACtDlB,mBAAmB,CAACkB,aAAa,CAAC,CAClC9B,YAAY,CAAC8B,aAAa,CAAC,CAC5B,CAAC,CACD,KAAM,CAAAkC,uBAAuB,CAAIC,CAAM,EAAK1E,cAAc,CAAC0E,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAC,CAC1E,KAAM,CAAAuC,2BAA2B,CAAIF,CAAM,EAAKzE,kBAAkB,CAACyE,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAC,CAElF,KAAM,CAAAwC,WAAW,CAAGA,CAAA,GAAM,CACzB1D,SAAS,CAAC,KAAK,CAAC,CAChB3B,qBAAqB,CAAC,KAAK,CAAC,CAC7B,CAAC,CACD,KAAM,CAAAsF,kBAAkB,CAAGA,CAAA,GAAM,CAChC;AACA,GAAIxD,UAAU,EAAIE,YAAY,EAAIE,iBAAiB,EAAIE,eAAe,CAAE,CACvE,OACD,CAEA,KAAM,CAAA6B,UAAU,CAAG,CAClBO,QAAQ,CAAE5C,gBAAgB,CAC1B8C,eAAe,CAAEnE,eAAe,CAChCkE,KAAK,CAAEpE,KAAK,EAAI,GAAG,CACnBsE,MAAM,CAAEjE,YAAY,GAAK6E,SAAS,CAAG7E,YAAY,CAAG,CAAC,CACrDkE,OAAO,CAAEhE,UAAU,EAAI,IAAI,CAC3BiE,WAAW,CAAE1E,WAAW,CACxB2E,UAAU,CAAEhE,aAAa,EAAI,CAAC,CAC9B0E,MAAM,CAAE,IACT,CAAC,CAEDtF,gBAAgB,CAAC+D,UAAU,CAAC,CAE5B;AACA,GAAI5C,YAAY,GAAKpB,gBAAgB,GAAK,cAAc,EAAIqB,oBAAoB,GAAK,cAAc,CAAC,CAAE,CACrG;AACAmE,UAAU,CAAC,IAAM,CAChBrE,gCAAgC,CAAC6C,UAAU,CAAC,CAC7C,CAAC,CAAE,CAAC,CAAC,CACN,CAEAhD,YAAY,CAACW,gBAAgB,CAAC,CAC9ByD,WAAW,CAAC,CAAC,CACbnE,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,GAAI,CAACQ,MAAM,CAAE,MAAO,KAAI,CAExB,mBACC;AACAjC,IAAA,QACCiG,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BjG,KAAA,QAAKgG,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BjG,KAAA,QAAKgG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnCnG,IAAA,CAACnB,UAAU,EACV,aAAW,OAAO,CAClBuH,OAAO,CAAER,WAAY,CAAAO,QAAA,cAErBnG,IAAA,CAAChB,2BAA2B,GAAE,CAAC,CAEpB,CAAC,cACbgB,IAAA,QAAKkG,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEnE,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cAExDhC,IAAA,CAACnB,UAAU,EACVwH,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBD,OAAO,CAAER,WAAY,CAAAO,QAAA,cAErBnG,IAAA,CAACjB,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAGNiB,IAAA,QAAKkG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC/BjG,KAAA,QAAKgG,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC5CjG,KAAA,CAACzB,GAAG,EAACyH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCnG,IAAA,CAACtB,UAAU,EAACwH,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEnE,SAAS,CAAC,UAAU,CAAC,CAAa,CAAC,cAC/EhC,IAAA,CAACpB,IAAI,EACJqH,EAAE,CAAC,eAAe,CAClBK,SAAS,MACTC,OAAO,CAAE,CACT;AAAA,CAAAJ,QAAA,CAGCtD,SAAS,CAAC2D,GAAG,CAAEC,QAAQ,eACvBzG,IAAA,CAACpB,IAAI,EAAC8H,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAR,QAAA,cAChBnG,IAAA,CAACnB,UAAU,EACVwH,IAAI,CAAC,OAAO,CACZO,aAAa,KACb;AACA;AACA;AAAA,CACAR,OAAO,CAAEA,CAAA,GAAMb,mBAAmB,CAACkB,QAAQ,CAACrD,KAAK,CAAG;AAAA,CAAA+C,QAAA,CAEnD9C,kBAAkB,CAACoD,QAAQ,CAACrD,KAAK,CAAC,CACxB,CAAC,EAVSqD,QAAQ,CAACrD,KAW1B,CACN,CAAC,CACG,CAAC,EACH,CAAC,cAILlD,KAAA,CAACzB,GAAG,EAACyH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnG,IAAA,QAAKkG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEnE,SAAS,CAAC,OAAO,CAAC,CAAM,CAAC,cAC/DhC,IAAA,QAAAmG,QAAA,cACDnG,IAAA,CAACrB,SAAS,EACTkI,OAAO,CAAC,UAAU,CAClBzD,KAAK,CAAE,GAAGxC,KAAK,EAAG,CAClByF,IAAI,CAAC,OAAO,CACZS,SAAS,MACTZ,SAAS,CAAC,qBAAqB,CAC/Ba,QAAQ,CAAGtB,CAAC,EAAK,CAChB,KAAM,CAAAuB,UAAU,CAAG3C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAC,EAAI,CAAC,CAEhD;AACA,GAAI4D,UAAU,CAAG,GAAG,EAAIA,UAAU,CAAG,IAAI,CAAE,CAC1C1E,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,IAAM,CACNA,aAAa,CAAC,KAAK,CAAC,CACrB,CAEAzB,QAAQ,CAACmG,UAAU,CAAC,CACrB,CAAE,CACFC,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFC,KAAK,CAAEhF,UAAW,CACjB,CAAC,CACG,CAAC,EAEF,CAAC,CACLA,UAAU,eACXnC,KAAA,CAACxB,UAAU,EACVwE,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBmE,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAC/H,YACZ,CAAE,CAAAsG,QAAA,eACAnG,IAAA,SAAMkD,KAAK,CAAE,CAAEyE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAE9F7E,uBAAuB,CAAE,CAAEC,MAAM,CAAErD,OAAQ,CAAE,CAC7C,CAAC,CACCoC,SAAS,CAAC,yCAAyC,CAAC,EAC3C,CACZ,cAuBD9B,KAAA,CAACzB,GAAG,EAACyH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnG,IAAA,CAACtB,UAAU,EAACwH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEnE,SAAS,CAAC,SAAS,CAAC,CAAa,CAAC,cAC/EhC,IAAA,QAAAmG,QAAA,cACDnG,IAAA,CAACrB,SAAS,EACTkI,OAAO,CAAC,UAAU,CAClBzD,KAAK,CAAE,GAAGjC,UAAU,EAAG,CACvB2G,SAAS,MAETzB,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/Ba,QAAQ,CAAGtB,CAAC,EAAK,CAChB,KAAM,CAAAuB,UAAU,CAAG3C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAC,EAAI,CAAC,CAEhD;AACA,GAAI4D,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACtCxE,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACNA,eAAe,CAAC,KAAK,CAAC,CACvB,CAEApB,aAAa,CAAC4F,UAAU,CAACe,QAAQ,CAAC,CAAC,CAAC,CACrC,CAAE,CACFd,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFC,KAAK,CAAE9E,YAAa,CACnB,CAAC,CACG,CAAC,EACH,CAAC,CACLA,YAAY,eACZrC,KAAA,CAACxB,UAAU,EACXwE,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBmE,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAC/H,YACZ,CAAE,CAAAsG,QAAA,eACAnG,IAAA,SAAMkD,KAAK,CAAE,CAAEyE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAE9F7E,uBAAuB,CAAE,CAAEC,MAAM,CAAErD,OAAQ,CAAE,CAC7C,CAAC,CACEoC,SAAS,CAAC,qCAAqC,CAAC,EACvC,CACZ,cAGD9B,KAAA,CAACzB,GAAG,EAACyH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnG,IAAA,CAACtB,UAAU,EAACwH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEnE,SAAS,CAAC,eAAe,CAAC,CAAa,CAAC,cACrFhC,IAAA,QAAAmG,QAAA,cACDnG,IAAA,CAACrB,SAAS,EACTkI,OAAO,CAAC,UAAU,CAClBzD,KAAK,CAAE,GAAGnC,YAAY,EAAG,CACzB6G,SAAS,MAETzB,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/Ba,QAAQ,CAAGtB,CAAC,EAAK,CAChB,KAAM,CAAAuB,UAAU,CAAG3C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAC,EAAI,CAAC,CAEhD;AACA,GAAI4D,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACtCtE,oBAAoB,CAAC,IAAI,CAAC,CAC1B;AACA;AACD,CAAC,IAAM,CACNA,oBAAoB,CAAC,KAAK,CAAC,CAC5B,CACCxB,eAAe,CAAC8F,UAAU,CAAC,CAE7B,CAAE,CACFC,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACC,CAAE,CACFC,KAAK,CAAE5E,iBAAkB,CAC1B,CAAC,CAEK,CAAC,EACL,CAAC,CACLA,iBAAiB,eACjBvC,KAAA,CAACxB,UAAU,EACXwE,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBmE,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAC/H,YACZ,CAAE,CAAAsG,QAAA,eACAnG,IAAA,SAAMkD,KAAK,CAAE,CAAEyE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAE9F7E,uBAAuB,CAAE,CAAEC,MAAM,CAAErD,OAAQ,CAAE,CAC7C,CAAC,CACEoC,SAAS,CAAC,qCAAqC,CAAC,EACvC,CACZ,cACD9B,KAAA,CAACzB,GAAG,EAACyH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnG,IAAA,CAACtB,UAAU,EAACwH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEnE,SAAS,CAAC,aAAa,CAAC,CAAa,CAAC,cACnFhC,IAAA,QAAAmG,QAAA,cACDnG,IAAA,CAACrB,SAAS,EACTkI,OAAO,CAAC,UAAU,CAClBzD,KAAK,CAAE,GAAG/B,aAAa,EAAG,CAC1ByG,SAAS,MAETzB,IAAI,CAAC,OAAO,CACZH,SAAS,CAAC,qBAAqB,CAC/Ba,QAAQ,CAAGtB,CAAC,EAAK,CAChB,KAAM,CAAAuB,UAAU,CAAG3C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAC,EAAI,CAAC,CAEhD;AACA,GAAI4D,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,CAAE,CACtCpE,kBAAkB,CAAC,IAAI,CAAC,CACzB,CAAC,IAAM,CACNA,kBAAkB,CAAC,KAAK,CAAC,CAC1B,CAEAtB,gBAAgB,CAAC0F,UAAU,CAAC,CAC7B,CAAE,CACFC,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBC,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAE5B,CACD,CAAE,CACFC,KAAK,CAAE1E,eAAgB,CACtB,CAAC,CACI,CAAC,EACJ,CAAC,CACLA,eAAe,eACfzC,KAAA,CAACxB,UAAU,EACXwE,KAAK,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBmE,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,IAAI,CAAE,CAAC,CACPC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAC/H,YACZ,CAAE,CAAAsG,QAAA,eACAnG,IAAA,SAAMkD,KAAK,CAAE,CAAEyE,OAAO,CAAE,MAAM,CAAExE,QAAQ,CAAE,MAAM,CAAEyE,UAAU,CAAE,QAAQ,CAAEC,WAAW,CAAC,KAAM,CAAE,CAE9F7E,uBAAuB,CAAE,CAAEC,MAAM,CAAErD,OAAQ,CAAE,CAC7C,CAAC,CACEoC,SAAS,CAAC,qCAAqC,CAAC,EACvC,CACZ,cAkBD9B,KAAA,CAACzB,GAAG,EAACyH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnG,IAAA,CAACtB,UAAU,EAACwH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEnE,SAAS,CAAC,QAAQ,CAAC,CAAa,CAAC,cAC9EhC,IAAA,QAAAmG,QAAA,cACDnG,IAAA,UACCgI,IAAI,CAAC,OAAO,CACZ5E,KAAK,CAAE1C,WAAY,CACnBqG,QAAQ,CAAEvB,uBAAwB,CAClCU,SAAS,CAAC,mBAAmB,CAC3B,CAAC,CACG,CAAC,EACJ,CAAC,cAGNhG,KAAA,CAACzB,GAAG,EAACyH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCnG,IAAA,CAACtB,UAAU,EAACwH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEnE,SAAS,CAAC,YAAY,CAAC,CAAa,CAAC,cAClFhC,IAAA,QAAAmG,QAAA,cACDnG,IAAA,UACCgI,IAAI,CAAC,OAAO,CACZ5E,KAAK,CAAEtC,eAAgB,CACvBiG,QAAQ,CAAEpB,2BAA4B,CACtCO,SAAS,CAAC,mBAAmB,CAC3B,CAAC,CACG,CAAC,EACJ,CAAC,EAGD,CAAC,CACD,CAAC,cACPlG,IAAA,QAAKkG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCnG,IAAA,CAAClB,MAAM,EACN+H,OAAO,CAAC,WAAW,CACnBT,OAAO,CAAEP,kBAAmB,CAC5BK,SAAS,CAAE,aAAa7D,UAAU,EAAIE,YAAY,EAAIE,iBAAiB,EAAIE,eAAe,CAAG,UAAU,CAAG,EAAE,EAAG,CAC/GsF,QAAQ,CAAE5F,UAAU,EAAIE,YAAY,EAAIE,iBAAiB,EAAIE,eAAgB,CAAAwD,QAAA,CAE7EnE,SAAS,CAAC,OAAO,CAAC,CACX,CAAC,CACL,CAAC,EACH,CAAC,CAEF,CACL;AAAA,EAEF,CAAC,CAED,cAAe,CAAA7B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}