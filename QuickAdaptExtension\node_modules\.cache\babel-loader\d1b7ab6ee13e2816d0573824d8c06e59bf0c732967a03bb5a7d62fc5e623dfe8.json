{"ast": null, "code": "import { useState, useEffect, useContext, useRef, useCallback } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace, isString, isObject } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => useCallback(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nexport const useTranslation = function (ns) {\n  let props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (isString(optsOrDefaultValue)) return optsOrDefaultValue;\n      if (isObject(optsOrDefaultValue) && isString(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) warnOnce(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  useEffect(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n};", "map": {"version": 3, "names": ["useState", "useEffect", "useContext", "useRef", "useCallback", "getI18n", "getDefaults", "ReportNamespaces", "I18nContext", "warnOnce", "loadNamespaces", "loadLanguages", "hasLoadedNamespace", "isString", "isObject", "usePrevious", "value", "ignore", "ref", "current", "alwaysNewT", "i18n", "language", "namespace", "keyPrefix", "getFixedT", "useMemoizedT", "useTranslation", "ns", "props", "arguments", "length", "undefined", "i18nFromProps", "i18nFromContext", "defaultNS", "defaultNSFromContext", "reportNamespaces", "notReadyT", "k", "optsOrDefaultValue", "defaultValue", "Array", "isArray", "retNotReady", "t", "ready", "options", "react", "wait", "i18nOptions", "useSuspense", "namespaces", "addUsedNamespaces", "isInitialized", "initializedStoreOnce", "every", "n", "memoGetT", "lng", "nsMode", "getT", "getNewT", "setT", "joinedNS", "join", "previousJoinedNS", "isMounted", "bindI18n", "bindI18nStore", "boundReset", "on", "store", "split", "for<PERSON>ach", "e", "off", "ret", "Promise", "resolve"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/node_modules/react-i18next/dist/es/useTranslation.js"], "sourcesContent": ["import { useState, useEffect, useContext, useRef, useCallback } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace, isString, isObject } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => useCallback(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nexport const useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (isString(optsOrDefaultValue)) return optsOrDefaultValue;\n      if (isObject(optsOrDefaultValue) && isString(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) warnOnce(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  useEffect(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n};"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5E,SAASC,OAAO,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,cAAc;AAClF,SAASC,QAAQ,EAAEC,cAAc,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,YAAY;AAC5G,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,MAAMC,GAAG,GAAGf,MAAM,CAAC,CAAC;EACpBF,SAAS,CAAC,MAAM;IACdiB,GAAG,CAACC,OAAO,GAAGF,MAAM,GAAGC,GAAG,CAACC,OAAO,GAAGH,KAAK;EAC5C,CAAC,EAAE,CAACA,KAAK,EAAEC,MAAM,CAAC,CAAC;EACnB,OAAOC,GAAG,CAACC,OAAO;AACpB,CAAC;AACD,MAAMC,UAAU,GAAGA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,KAAKH,IAAI,CAACI,SAAS,CAACH,QAAQ,EAAEC,SAAS,EAAEC,SAAS,CAAC;AAC3G,MAAME,YAAY,GAAGA,CAACL,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,KAAKpB,WAAW,CAACgB,UAAU,CAACC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,CAAC,EAAE,CAACH,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAC;AACpK,OAAO,MAAMG,cAAc,GAAG,SAAAA,CAACC,EAAE,EAAiB;EAAA,IAAfC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC3C,MAAM;IACJT,IAAI,EAAEY;EACR,CAAC,GAAGJ,KAAK;EACT,MAAM;IACJR,IAAI,EAAEa,eAAe;IACrBC,SAAS,EAAEC;EACb,CAAC,GAAGlC,UAAU,CAACM,WAAW,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMa,IAAI,GAAGY,aAAa,IAAIC,eAAe,IAAI7B,OAAO,CAAC,CAAC;EAC1D,IAAIgB,IAAI,IAAI,CAACA,IAAI,CAACgB,gBAAgB,EAAEhB,IAAI,CAACgB,gBAAgB,GAAG,IAAI9B,gBAAgB,CAAC,CAAC;EAClF,IAAI,CAACc,IAAI,EAAE;IACTZ,QAAQ,CAACY,IAAI,EAAE,qBAAqB,EAAE,wFAAwF,CAAC;IAC/H,MAAMiB,SAAS,GAAGA,CAACC,CAAC,EAAEC,kBAAkB,KAAK;MAC3C,IAAI3B,QAAQ,CAAC2B,kBAAkB,CAAC,EAAE,OAAOA,kBAAkB;MAC3D,IAAI1B,QAAQ,CAAC0B,kBAAkB,CAAC,IAAI3B,QAAQ,CAAC2B,kBAAkB,CAACC,YAAY,CAAC,EAAE,OAAOD,kBAAkB,CAACC,YAAY;MACrH,OAAOC,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,GAAGA,CAAC,CAACA,CAAC,CAACR,MAAM,GAAG,CAAC,CAAC,GAAGQ,CAAC;IAC/C,CAAC;IACD,MAAMK,WAAW,GAAG,CAACN,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;IAC1CM,WAAW,CAACC,CAAC,GAAGP,SAAS;IACzBM,WAAW,CAACvB,IAAI,GAAG,CAAC,CAAC;IACrBuB,WAAW,CAACE,KAAK,GAAG,KAAK;IACzB,OAAOF,WAAW;EACpB;EACA,IAAIvB,IAAI,CAAC0B,OAAO,CAACC,KAAK,EAAEC,IAAI,EAAExC,QAAQ,CAACY,IAAI,EAAE,mBAAmB,EAAE,qHAAqH,CAAC;EACxL,MAAM6B,WAAW,GAAG;IAClB,GAAG5C,WAAW,CAAC,CAAC;IAChB,GAAGe,IAAI,CAAC0B,OAAO,CAACC,KAAK;IACrB,GAAGnB;EACL,CAAC;EACD,MAAM;IACJsB,WAAW;IACX3B;EACF,CAAC,GAAG0B,WAAW;EACf,IAAIE,UAAU,GAAGxB,EAAE,IAAIQ,oBAAoB,IAAIf,IAAI,CAAC0B,OAAO,EAAEZ,SAAS;EACtEiB,UAAU,GAAGvC,QAAQ,CAACuC,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAChF/B,IAAI,CAACgB,gBAAgB,CAACgB,iBAAiB,GAAGD,UAAU,CAAC;EACrD,MAAMN,KAAK,GAAG,CAACzB,IAAI,CAACiC,aAAa,IAAIjC,IAAI,CAACkC,oBAAoB,KAAKH,UAAU,CAACI,KAAK,CAACC,CAAC,IAAI7C,kBAAkB,CAAC6C,CAAC,EAAEpC,IAAI,EAAE6B,WAAW,CAAC,CAAC;EAClI,MAAMQ,QAAQ,GAAGhC,YAAY,CAACL,IAAI,EAAEQ,KAAK,CAAC8B,GAAG,IAAI,IAAI,EAAET,WAAW,CAACU,MAAM,KAAK,UAAU,GAAGR,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE5B,SAAS,CAAC;EACjI,MAAMqC,IAAI,GAAGA,CAAA,KAAMH,QAAQ;EAC3B,MAAMI,OAAO,GAAGA,CAAA,KAAM1C,UAAU,CAACC,IAAI,EAAEQ,KAAK,CAAC8B,GAAG,IAAI,IAAI,EAAET,WAAW,CAACU,MAAM,KAAK,UAAU,GAAGR,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAE5B,SAAS,CAAC;EACpI,MAAM,CAACqB,CAAC,EAAEkB,IAAI,CAAC,GAAG/D,QAAQ,CAAC6D,IAAI,CAAC;EAChC,IAAIG,QAAQ,GAAGZ,UAAU,CAACa,IAAI,CAAC,CAAC;EAChC,IAAIpC,KAAK,CAAC8B,GAAG,EAAEK,QAAQ,GAAG,GAAGnC,KAAK,CAAC8B,GAAG,GAAGK,QAAQ,EAAE;EACnD,MAAME,gBAAgB,GAAGnD,WAAW,CAACiD,QAAQ,CAAC;EAC9C,MAAMG,SAAS,GAAGhE,MAAM,CAAC,IAAI,CAAC;EAC9BF,SAAS,CAAC,MAAM;IACd,MAAM;MACJmE,QAAQ;MACRC;IACF,CAAC,GAAGnB,WAAW;IACfiB,SAAS,CAAChD,OAAO,GAAG,IAAI;IACxB,IAAI,CAAC2B,KAAK,IAAI,CAACK,WAAW,EAAE;MAC1B,IAAItB,KAAK,CAAC8B,GAAG,EAAE;QACbhD,aAAa,CAACU,IAAI,EAAEQ,KAAK,CAAC8B,GAAG,EAAEP,UAAU,EAAE,MAAM;UAC/C,IAAIe,SAAS,CAAChD,OAAO,EAAE4C,IAAI,CAACD,OAAO,CAAC;QACtC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLpD,cAAc,CAACW,IAAI,EAAE+B,UAAU,EAAE,MAAM;UACrC,IAAIe,SAAS,CAAChD,OAAO,EAAE4C,IAAI,CAACD,OAAO,CAAC;QACtC,CAAC,CAAC;MACJ;IACF;IACA,IAAIhB,KAAK,IAAIoB,gBAAgB,IAAIA,gBAAgB,KAAKF,QAAQ,IAAIG,SAAS,CAAChD,OAAO,EAAE;MACnF4C,IAAI,CAACD,OAAO,CAAC;IACf;IACA,MAAMQ,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIH,SAAS,CAAChD,OAAO,EAAE4C,IAAI,CAACD,OAAO,CAAC;IACtC,CAAC;IACD,IAAIM,QAAQ,EAAE/C,IAAI,EAAEkD,EAAE,CAACH,QAAQ,EAAEE,UAAU,CAAC;IAC5C,IAAID,aAAa,EAAEhD,IAAI,EAAEmD,KAAK,CAACD,EAAE,CAACF,aAAa,EAAEC,UAAU,CAAC;IAC5D,OAAO,MAAM;MACXH,SAAS,CAAChD,OAAO,GAAG,KAAK;MACzB,IAAIE,IAAI,EAAE+C,QAAQ,EAAEK,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACC,CAAC,IAAItD,IAAI,CAACuD,GAAG,CAACD,CAAC,EAAEL,UAAU,CAAC,CAAC;MACpE,IAAID,aAAa,IAAIhD,IAAI,EAAEgD,aAAa,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACC,CAAC,IAAItD,IAAI,CAACmD,KAAK,CAACI,GAAG,CAACD,CAAC,EAAEL,UAAU,CAAC,CAAC;IACjG,CAAC;EACH,CAAC,EAAE,CAACjD,IAAI,EAAE2C,QAAQ,CAAC,CAAC;EACpB/D,SAAS,CAAC,MAAM;IACd,IAAIkE,SAAS,CAAChD,OAAO,IAAI2B,KAAK,EAAE;MAC9BiB,IAAI,CAACF,IAAI,CAAC;IACZ;EACF,CAAC,EAAE,CAACxC,IAAI,EAAEG,SAAS,EAAEsB,KAAK,CAAC,CAAC;EAC5B,MAAM+B,GAAG,GAAG,CAAChC,CAAC,EAAExB,IAAI,EAAEyB,KAAK,CAAC;EAC5B+B,GAAG,CAAChC,CAAC,GAAGA,CAAC;EACTgC,GAAG,CAACxD,IAAI,GAAGA,IAAI;EACfwD,GAAG,CAAC/B,KAAK,GAAGA,KAAK;EACjB,IAAIA,KAAK,EAAE,OAAO+B,GAAG;EACrB,IAAI,CAAC/B,KAAK,IAAI,CAACK,WAAW,EAAE,OAAO0B,GAAG;EACtC,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC3B,IAAIlD,KAAK,CAAC8B,GAAG,EAAE;MACbhD,aAAa,CAACU,IAAI,EAAEQ,KAAK,CAAC8B,GAAG,EAAEP,UAAU,EAAE,MAAM2B,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLrE,cAAc,CAACW,IAAI,EAAE+B,UAAU,EAAE,MAAM2B,OAAO,CAAC,CAAC,CAAC;IACnD;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}