{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\VideoPlayer.tsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoPlayer = ({\n  videoFile,\n  isMaximized\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  if (!videoFile) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"video\", {\n      width: \"100%\",\n      height: \"100%\",\n      controls: true,\n      style: {\n        borderRadius: \"8px\",\n        objectFit: isMaximized ? \"contain\" : \"cover\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"source\", {\n        src: videoFile,\n        type: \"video/mp4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), translate(\"Your browser does not support the video tag.\", {\n        defaultValue: \"Your browser does not support the video tag.\"\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayer, \"pr/MAE+x5KkRn8BZcdsayxNnBiM=\", false, function () {\n  return [useTranslation];\n});\n_c = VideoPlayer;\nexport default VideoPlayer;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayer\");", "map": {"version": 3, "names": ["React", "useTranslation", "jsxDEV", "_jsxDEV", "VideoPlayer", "videoFile", "isMaximized", "_s", "t", "translate", "children", "width", "height", "controls", "style", "borderRadius", "objectFit", "src", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultValue", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/VideoPlayer.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst VideoPlayer = ({ videoFile ,isMaximized}: { videoFile: string,isMaximized :any }) => {\r\n  const { t: translate } = useTranslation();\r\n  if (!videoFile) return null;\r\n\r\n  return (\r\n    <div>\r\n      <video\r\n        width=\"100%\"\r\n        height=\"100%\"\r\n        controls\r\n        style={{ borderRadius: \"8px\", objectFit: isMaximized?\"contain\":\"cover\" }}\r\n      >\r\n        <source src={videoFile} type=\"video/mp4\" />\r\n        {translate(\"Your browser does not support the video tag.\", { defaultValue: \"Your browser does not support the video tag.\" })}\r\n      </video>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoPlayer;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAmD,CAAC,KAAK;EAAAC,EAAA;EACzF,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGR,cAAc,CAAC,CAAC;EACzC,IAAI,CAACI,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEF,OAAA;IAAAO,QAAA,eACEP,OAAA;MACEQ,KAAK,EAAC,MAAM;MACZC,MAAM,EAAC,MAAM;MACbC,QAAQ;MACRC,KAAK,EAAE;QAAEC,YAAY,EAAE,KAAK;QAAEC,SAAS,EAAEV,WAAW,GAAC,SAAS,GAAC;MAAQ,CAAE;MAAAI,QAAA,gBAEzEP,OAAA;QAAQc,GAAG,EAAEZ,SAAU;QAACa,IAAI,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1Cb,SAAS,CAAC,8CAA8C,EAAE;QAAEc,YAAY,EAAE;MAA+C,CAAC,CAAC;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACf,EAAA,CAjBIH,WAAW;EAAA,QACUH,cAAc;AAAA;AAAAuB,EAAA,GADnCpB,WAAW;AAmBjB,eAAeA,WAAW;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}