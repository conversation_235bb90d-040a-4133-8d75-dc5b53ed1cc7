{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\Tooltip.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport \"./Tooltip.css\";\nimport { Tooltip, tooltipClasses, MobileStepper } from \"@mui/material\";\nimport { styled } from \"@mui/material/styles\";\nimport TooltipBody from \"./TooltipBody\";\nimport { CustomCursor } from \"../../assets/icons/icons\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const TOOLTIP_MX_WIDTH = 500;\nexport const TOOLTIP_MN_WIDTH = 250;\nexport const TOOLTIP_HEIGHT = 500;\nexport const EXTENSION_PART = \"extension-part\";\nexport const CustomWidthTooltip = _s2(_s(styled(_s(({\n  className,\n  selectedTemplate,\n  selectedTemplateTour,\n  ...props\n}) => {\n  _s();\n  const {\n    toolTipGuideMetaData,\n    currentStep,\n    elementSelected\n  } = useDrawerStore();\n  useEffect(() => {\n    if ((selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") && elementSelected) {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"\";\n    }\n\n    // Cleanup\n    return () => {\n      document.body.style.overflow = \"\";\n    };\n  }, [selectedTemplate, selectedTemplateTour, elementSelected]);\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    ...props,\n    classes: {\n      popper: className\n    },\n    id: \"Tooltip-unique\",\n    disableHoverListener: true,\n    disableTouchListener: true,\n    PopperProps: {\n      className: \"qadpt-tlprte\",\n      sx: {\n        top: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? \"-110px !important\" : \"-160px !important\",\n        zIndex: '99999 !important'\n      }\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 4\n  }, this);\n}, \"J9J3j7URIQPYhyQUXaxZMviTeMk=\", false, function () {\n  return [useDrawerStore];\n})), \"J9J3j7URIQPYhyQUXaxZMviTeMk=\", false, function () {\n  return [useDrawerStore];\n})(_s2(({\n  selectedTemplate\n}) => {\n  var _toolTipGuideMetaData, _toolTipGuideMetaData2, _toolTipGuideMetaData3, _toolTipGuideMetaData4, _toolTipGuideMetaData5, _toolTipGuideMetaData6, _toolTipGuideMetaData7, _toolTipGuideMetaData8, _toolTipGuideMetaData9, _toolTipGuideMetaData10, _toolTipGuideMetaData11, _toolTipGuideMetaData12, _toolTipGuideMetaData13, _toolTipGuideMetaData14;\n  _s2();\n  const {\n    toolTipGuideMetaData,\n    currentStep\n  } = useDrawerStore();\n  return {\n    [`& .${tooltipClasses.tooltip}`]: {\n      // maxWidth: TOOLTIP_MX_WIDTH,\n      minWidth: TOOLTIP_MN_WIDTH,\n      width: `${(_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.canvas) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.width} !important` || \"250px !important\",\n      maxWidth: `${(_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.canvas) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.width} !important` || \"800px !important\",\n      backgroundColor: `${(_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.canvas) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.backgroundColor}` || \"#fff\",\n      color: \"transparent\",\n      fontSize: \"14px\",\n      borderRadius: `${(_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData7 === void 0 ? void 0 : (_toolTipGuideMetaData8 = _toolTipGuideMetaData7.canvas) === null || _toolTipGuideMetaData8 === void 0 ? void 0 : _toolTipGuideMetaData8.borderRadius} !important`,\n      position: \"relative !important\",\n      padding: `${(_toolTipGuideMetaData9 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData9 === void 0 ? void 0 : (_toolTipGuideMetaData10 = _toolTipGuideMetaData9.canvas) === null || _toolTipGuideMetaData10 === void 0 ? void 0 : _toolTipGuideMetaData10.padding}` || \"2px\",\n      boxShadow: \"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\" // Adds a smooth shadow\n    },\n    [`& .${tooltipClasses.arrow}`]: {\n      color: \"white\" // Match arrow color with tooltip background\n    }\n  };\n  return {\n    [`& .${tooltipClasses.tooltip}`]: {\n      // maxWidth: TOOLTIP_MX_WIDTH,\n      minWidth: TOOLTIP_MN_WIDTH,\n      maxWidth: `${(_toolTipGuideMetaData11 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData11 === void 0 ? void 0 : (_toolTipGuideMetaData12 = _toolTipGuideMetaData11.canvas) === null || _toolTipGuideMetaData12 === void 0 ? void 0 : _toolTipGuideMetaData12.width} !important` || \"500px !important\",\n      backgroundColor: \"white\",\n      color: \"transparent\",\n      fontSize: \"14px\",\n      borderRadius: `${((_toolTipGuideMetaData13 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData13 === void 0 ? void 0 : (_toolTipGuideMetaData14 = _toolTipGuideMetaData13.canvas) === null || _toolTipGuideMetaData14 === void 0 ? void 0 : _toolTipGuideMetaData14.borderRadius) || \"8px\"} !important`,\n      position: \"relative !important\",\n      padding: \"0px !important\",\n      boxShadow: \"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\" // Adds a smooth shadow\n    },\n    [`& .${tooltipClasses.arrow}`]: {\n      color: \"white\" // Match arrow color with tooltip background\n    }\n  };\n}, \"QD4WQOQFo51nDNqDsyXdVf0H1AA=\", false, function () {\n  return [useDrawerStore];\n})), \"QD4WQOQFo51nDNqDsyXdVf0H1AA=\", false, function () {\n  return [useDrawerStore];\n});\n_c = CustomWidthTooltip;\nconst CreateTooltip = ({\n  isTooltipNameScreenOpen,\n  isUnSavedChanges,\n  openWarning,\n  setopenWarning,\n  handleLeave,\n  updatedGuideData\n}) => {\n  _s3();\n  var _toolTipGuideMetaData16;\n  const {\n    selectedTemplate,\n    setTooltip,\n    tooltip,\n    elementSelected,\n    setElementSelected,\n    guideName,\n    borderColor,\n    backgroundColor,\n    borderSize,\n    borderRadius,\n    padding,\n    width,\n    setXpathToTooltipMetaData,\n    // updatedCanvasSettings,\n    tooltipXaxis,\n    tooltipYaxis,\n    tooltipWidth,\n    setTooltipWidth,\n    setTooltipPadding,\n    setTooltipBorderradius,\n    setTooltipBordersize,\n    setTooltipBordercolor,\n    setTooltipBackgroundcolor,\n    tooltippadding,\n    tooltipborderradius,\n    tooltipbordersize,\n    tooltipBordercolor,\n    tooltipBackgroundcolor,\n    tooltipPosition,\n    setTooltipPosition,\n    selectedOption,\n    steps,\n    currentStepIndex,\n    openTooltip,\n    setOpenTooltip,\n    setCurrentStepIndex,\n    HotspotSettings,\n    toolTipGuideMetaData,\n    hotspotGuideMetaData,\n    currentStep,\n    isPopoverOpen,\n    setIsPopoverOpen,\n    setAxisData,\n    currentGuideId,\n    axisData,\n    isALTKeywordEnabled,\n    setIsALTKeywordEnabled,\n    currentHoveredElement,\n    setCurrentHoveredElement,\n    selectedTemplateTour,\n    isGuideInfoScreen,\n    isCollapsed,\n    createWithAI,\n    syncAITooltipContainerData\n  } = useDrawerStore(state => state);\n  const [hoveredElement, setHoveredElement] = useState(null);\n  const [rectData, setRectData] = useState(null); // State to store rect object\n  const [overlayPosition, setOverlayPosition] = useState(null);\n  const [popupPosition, setPopupPosition] = useState(null);\n  const [smoothPopupPosition, setSmoothPopupPosition] = useState(null);\n  const getXPath = element => {\n    const isUnique = (attr, value, tagName) => {\n      const parent = element === null || element === void 0 ? void 0 : element.parentNode;\n      if (!parent) return false;\n      const matchingElements = [...Array.from(parent.querySelectorAll(`${tagName}[${attr}=\"${value}\"]`))];\n      return matchingElements.length === 1;\n    };\n    const getUniqueSelector = el => {\n      if (!el) return null;\n      const tagName = el.tagName.toLowerCase();\n\n      // find with event attributes\n      const eventAttributes = [...Array.from(el.attributes)].filter(attr => attr.name.startsWith(\"on\"));\n      for (const attr of eventAttributes) {\n        if (isUnique(attr.name, attr.value, tagName)) {\n          return `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\n        }\n      }\n      return null;\n    };\n    const getSegment = el => {\n      if (!el) return null;\n      const tagName = el.tagName.toLowerCase();\n      const uniqueSelector = getUniqueSelector(el);\n      const bannercontainer = document.querySelector(\".quickAdopt_banner\");\n      if (bannercontainer) {\n        if (el.closest(\".quickAdopt_banner\")) {\n          return null;\n        }\n      }\n      if (uniqueSelector) return uniqueSelector;\n      let siblingIndex = 1;\n      let sibling = el.previousElementSibling;\n      while (sibling) {\n        if (sibling.tagName === el.tagName) siblingIndex++;\n        sibling = sibling.previousElementSibling;\n      }\n      return `${tagName}[${siblingIndex}]`;\n    };\n    const segments = [];\n    let currentElement = element;\n    while (currentElement && currentElement !== document.body && currentElement !== document.documentElement) {\n      const segment = getSegment(currentElement);\n      if (!segment) break;\n      segments.unshift(segment);\n      if (segment.startsWith(\"//*[@\")) break;\n      currentElement = currentElement.parentElement;\n    }\n    if (!segments[0].startsWith(\"//\")) {\n      if (segments[0] !== \"html\") {\n        segments.unshift(\"html\");\n      }\n      if (segments[1] !== \"body\") {\n        segments.splice(1, 0, \"body\");\n      }\n    }\n    return `${segments.join(\"/\")}`;\n  };\n  const getRelativeXPath = element => {\n    // Check if element is valid\n    if (!element || !element.tagName) return null;\n    const isUnique = xpath => {\n      try {\n        const matchingElements = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);\n        return matchingElements.snapshotLength === 1;\n      } catch (e) {\n        return false;\n      }\n    };\n    const isHighlightStyle = styleValue => {\n      // Match your specific highlighting styles\n      return styleValue.includes(\"outline: 2px solid #6c97a6\") || styleValue.includes(\"pointer-events: none\") || styleValue.includes(\"pointer-events:\");\n    };\n    const isHighlightClass = className => {\n      // Check for your highlighting class\n      return className === \"quickAdopt-selection\";\n    };\n    const getUniqueSelector = el => {\n      const tagName = el.tagName.toLowerCase();\n\n      // Priority 1: ID\n      if (el.id) {\n        const xpath = `//${tagName}[@id=\"${el.id}\"]`;\n        if (isUnique(xpath)) return xpath;\n      }\n\n      // Priority 2: Class names (excluding highlight class)\n      if (el.className) {\n        const classes = el.className.trim().split(/\\s+/).filter(cls => !isHighlightClass(cls));\n        for (const className of classes) {\n          if (className) {\n            const xpath = `//${tagName}[contains(@class, \"${className}\")]`;\n            if (isUnique(xpath)) return xpath;\n          }\n        }\n      }\n\n      // Priority 3: Event attributes\n      const eventAttributes = [...Array.from(el.attributes)].filter(attr => attr.name.startsWith(\"on\"));\n      for (const attr of eventAttributes) {\n        const xpath = `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\n        if (isUnique(xpath)) return xpath;\n      }\n\n      // Priority 4: Other attributes (excluding highlight style and disabled)\n      const attributes = [...Array.from(el.attributes)].filter(attr => attr.name !== \"id\" && attr.name !== \"class\" && !attr.name.startsWith(\"on\") && attr.name !== \"disabled\" && !(attr.name === \"style\" && isHighlightStyle(attr.value)));\n      for (const attr of attributes) {\n        const xpath = `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\n        if (isUnique(xpath)) return xpath;\n      }\n      return null;\n    };\n    let currentElement = element;\n    let pathSegments = [];\n    while (currentElement && currentElement !== document.documentElement) {\n      var _currentElement$paren;\n      const uniqueSelector = getUniqueSelector(currentElement);\n      if (uniqueSelector) {\n        return uniqueSelector;\n      }\n\n      // Build positional path if no unique selector is found\n      const tagName = currentElement.tagName.toLowerCase();\n      const siblings = Array.from(((_currentElement$paren = currentElement.parentElement) === null || _currentElement$paren === void 0 ? void 0 : _currentElement$paren.children) || []).filter(sib => sib.tagName === currentElement.tagName);\n      const index = siblings.indexOf(currentElement) + 1;\n      pathSegments.unshift(index > 1 ? `${tagName}[${index}]` : tagName);\n      currentElement = currentElement.parentElement;\n    }\n\n    // Check for banner container (from your original code)\n    const bannerContainer = document.querySelector(\".quickAdopt_banner\");\n    if (bannerContainer && element.closest(\".quickAdopt_banner\")) {\n      return null;\n    }\n    return `//${pathSegments.join(\"/\")}`;\n  };\n\n  // Example usage:\n  // const element = document.querySelector('some-element');\n  // const relativeXPath = getRelativeXPath(element);\n  // console.log(relativeXPath);\n\n  const shouldIgnoreHighlight = element => {\n    return element.classList.contains(\"mdc-tooltip__surface\") || element.classList.contains(\"mdc-tooltip__surface-animation\") || element.classList.contains(\"mdc-tooltip\") || element.classList.contains(\"mdc-tooltip--shown\") || element.classList.contains(\"mdc-tooltip--showing\") || element.classList.contains(\"mdc-tooltip--hiding\") || element.getAttribute(\"role\") === \"tooltip\" || element.closest(\"#Tooltip-unique\") || element.closest(\"#my-react-drawer\") || element.closest(\"#tooltip-section-popover\") || element.closest(\"#btn-setting-toolbar\") || element.closest(\"#button-toolbar\") || element.closest(\"#color-picker\") || element.closest(\".qadpt-ext-banner\") || element.closest(\"#leftDrawer\") || element.closest(\"#image-popover\") || element.closest(\"#toggle-fit\") || element.closest(\"#color-popover\") || element.closest(\"#rte-popover\") || element.closest(\"#rte-alignment\") || element.closest(\"#rte-alignment-menu\") || element.closest(\"#rte-font\") || element.closest(\"#rte-bold\") || element.closest(\"#rte-italic\") || element.closest(\"#rte-underline\") || element.closest(\"#rte-strke-through\") || element.closest(\"#rte-alignment-menu-items\") || element.closest(\"#rte-more\") || element.closest(\"#rte-text-color\") || element.closest(\"#rte-text-color-popover\") || element.closest(\"#rte-text-highlight\") || element.closest(\"#rte-text-highlight-pop\") || element.closest(\"#rte-text-heading\") || element.closest(\"#rte-text-heading-menu-items\") || element.closest(\"#rte-text-format\") || element.closest(\"#rte-text-ul\") || element.closest(\"#rte-text-hyperlink\") || element.closest(\"#rte-video\") || element.closest(\"#rte-clear-formatting\") || element.closest(\"#rte-hyperlink-popover\") || element.closest(\"#rte-box\") || element.closest(element.id.startsWith(\"#rt-editor\") ? `${element.id}` : \"nope\") || element.closest(\"#rte-placeholder\") || element.closest(\"#qadpt-designpopup\") || element.closest(\"#image-properties\") || element.closest(\"#rte-toolbar\") || element.closest(\"#tooltipdialog\") || element.closest(\"#rte-toolbar-paper\") || element.closest(\"#rte-alignment-menu\") || element.closest(\"#rte-alignment-menu-items\");\n  };\n  const shouldIgnoreEvents = element => {\n    return element.classList.contains(\"mdc-tooltip__surface\") || element.classList.contains(\"mdc-tooltip__surface-animation\") || element.classList.contains(\"mdc-tooltip\") || element.classList.contains(\"mdc-tooltip--shown\") || element.classList.contains(\"mdc-tooltip--showing\") || element.classList.contains(\"mdc-tooltip--hiding\") || element.getAttribute(\"role\") === \"tooltip\" || element.closest(\"#Tooltip-unique\") || element.closest(\"#tooltip-section-popover\") || element.closest(\"#btn-setting-toolbar\") || element.closest(\"#button-toolbar\") || element.closest(\"#color-picker\") || element.closest(\".qadpt-ext-banner\") || element.closest(\"#leftDrawer\") || element.closest(\"#rte-popover\") || element.closest(element.id.startsWith(\"#rt-editor\") ? `${element.id}` : \"nope\") || element.closest(\"#rte-box\") || element.closest(\"#rte-placeholder\") || element.closest(\"#rte-alignment-menu-items\") || element.closest(\"#qadpt-designpopup\");\n  };\n  let hotspot;\n  const altElementBorderRemove = () => {\n    const previousSelectedDOM = document.querySelector(\".quickAdopt-selection\");\n    if (previousSelectedDOM) {\n      setTimeout(() => {\n        previousSelectedDOM.style.outline = \"none\";\n        //previousSelectedDOM.style.backgroundColor = \"\";\n        previousSelectedDOM.style.boxShadow = \"\";\n        previousSelectedDOM.style.pointerEvents = \"\";\n        previousSelectedDOM.removeAttribute(\"disabled\");\n        previousSelectedDOM.classList.remove(\"quickAdopt-selection\");\n      }, 10);\n    }\n  };\n  const removeAllElementHighlights = () => {\n    // Remove all elements with quickAdopt-selection class\n    const highlightedElements = document.querySelectorAll('.quickAdopt-selection');\n    highlightedElements.forEach(element => {\n      const htmlElement = element;\n      htmlElement.style.outline = '';\n      //htmlElement.style.backgroundColor = '';\n      htmlElement.style.boxShadow = '';\n      htmlElement.style.pointerEvents = '';\n      htmlElement.removeAttribute('disabled');\n      htmlElement.classList.remove('quickAdopt-selection');\n    });\n\n    // Remove any elements with data-target-element attribute\n    const targetElements = document.querySelectorAll('[data-target-element=\"true\"]');\n    targetElements.forEach(element => {\n      const htmlElement = element;\n      htmlElement.style.outline = '';\n      //htmlElement.style.backgroundColor = '';\n      htmlElement.style.boxShadow = '';\n      htmlElement.style.pointerEvents = '';\n      htmlElement.removeAttribute('disabled');\n      htmlElement.removeAttribute('data-target-element');\n    });\n\n    // Remove any elements with quickadapt highlighting attributes\n    const quickAdaptElements = document.querySelectorAll('[data-quickadapt-highlighted=\"true\"]');\n    quickAdaptElements.forEach(element => {\n      const htmlElement = element;\n      htmlElement.style.outline = '';\n      htmlElement.style.outlineOffset = '';\n      //htmlElement.style.backgroundColor = '';\n      htmlElement.style.boxShadow = '';\n      htmlElement.style.pointerEvents = '';\n      htmlElement.removeAttribute('disabled');\n      htmlElement.removeAttribute('data-quickadapt-highlighted');\n    });\n  };\n  const removeAppliedStyleOfEle = element => {\n    element.removeAttribute(\"disabled\");\n    element.style.outline = \"\";\n    element.style.pointerEvents = \"unset\";\n  };\n\n  // Custom cursor utility functions\n  const createCustomCursorDataURL = () => {\n    // Create a data URL from the SVG\n    const svgString = CustomCursor;\n    const encodedSvg = encodeURIComponent(svgString);\n    return `data:image/svg+xml,${encodedSvg}`;\n  };\n  const applyCustomCursor = () => {\n    const cursorDataURL = createCustomCursorDataURL();\n    // Apply cursor to document body and html to ensure it covers the entire page\n    // Using 16 16 as hotspot (center of 32x32 cursor)\n    document.body.style.cursor = `url(\"${cursorDataURL}\") 16 16, crosshair`;\n    document.documentElement.style.cursor = `url(\"${cursorDataURL}\") 16 16, crosshair`;\n\n    // Also apply to all elements that might override the cursor, but exclude extension UI\n    const style = document.createElement('style');\n    style.id = 'quickadapt-custom-cursor-style';\n    style.textContent = `\n\t\t\t* {\n\t\t\t\tcursor: url(\"${cursorDataURL}\") 16 16, crosshair !important;\n\t\t\t}\n\t\t\t/* Exclude extension UI elements from custom cursor */\n\t\t\t#my-react-drawer,\n\t\t\t#my-react-drawer *,\n\t\t\t.MuiDrawer-root,\n\t\t\t.MuiDrawer-root *,\n\t\t\t.MuiDialog-root,\n\t\t\t.MuiDialog-root *,\n\t\t\t.MuiPopover-root,\n\t\t\t.MuiPopover-root *,\n\t\t\t.MuiTooltip-popper,\n\t\t\t.MuiTooltip-popper *,\n\t\t\t.qadpt-ext-banner,\n\t\t\t.qadpt-ext-banner *,\n\t\t\t#leftDrawer,\n\t\t\t#leftDrawer *,\n\t\t\t#rte-popover,\n\t\t\t#rte-popover *,\n\t\t\t#rte-box,\n\t\t\t#rte-box *,\n\t\t\t#qadpt-designpopup,\n\t\t\t#qadpt-designpopup *,\n\t\t\t.quickadapt-no-custom-cursor,\n\t\t\t.quickadapt-no-custom-cursor * {\n\t\t\t\tcursor: default !important;\n\t\t\t}\n\t\t`;\n    document.head.appendChild(style);\n  };\n  const removeCustomCursor = () => {\n    // Remove custom cursor from body and html\n    document.body.style.cursor = '';\n    document.documentElement.style.cursor = '';\n\n    // Remove the custom cursor style\n    const existingStyle = document.getElementById('quickadapt-custom-cursor-style');\n    if (existingStyle) {\n      existingStyle.remove();\n    }\n  };\n  useEffect(() => {\n    let isElementHover = false;\n    let persistentOverlay = null;\n    const createOrUpdateOverlay = rect => {\n      // Get or create the persistent overlay element\n      if (!persistentOverlay) {\n        persistentOverlay = document.createElement(\"div\");\n        persistentOverlay.className = \"quickAdopt-overlay\";\n        persistentOverlay.setAttribute(\"data-overlay\", \"true\");\n        persistentOverlay.setAttribute(\"data-persistent-overlay\", \"true\");\n\n        // Set initial styles\n        Object.assign(persistentOverlay.style, {\n          position: \"fixed\",\n          backgroundColor: \"rgba(108, 151, 166, 0.2)\",\n          outline: \"2px dashed #6c97a6\",\n          boxShadow: \"0 0 0 3px transparent\",\n          pointerEvents: \"none\",\n          zIndex: \"9999\",\n          display: \"block\"\n        });\n        document.body.appendChild(persistentOverlay);\n      }\n\n      // Show the overlay and update position and size with smooth transitions\n      persistentOverlay.style.display = \"block\";\n      Object.assign(persistentOverlay.style, {\n        top: `${rect.top + window.scrollY}px`,\n        left: `${rect.left + window.scrollX}px`,\n        width: `${rect.width}px`,\n        height: `${rect.height}px`\n      });\n    };\n    const handleMouseOver = event => {\n      if (isALTKeywordEnabled && currentHoveredElement && !isTooltipNameScreenOpen && !isCollapsed) {\n        const element = event.target;\n        setCurrentHoveredElement(element);\n        if (!shouldIgnoreHighlight(element)) {\n          altElementBorderRemove();\n          const rect = element.getBoundingClientRect();\n          createOrUpdateOverlay(rect);\n        }\n      }\n    };\n    const hidePersistentOverlay = () => {\n      if (persistentOverlay) {\n        persistentOverlay.style.display = 'none';\n      }\n    };\n    const showPersistentOverlay = () => {\n      if (persistentOverlay) {\n        persistentOverlay.style.display = 'block';\n      }\n    };\n    const handleMouseOut = event => {\n      const element = event.target;\n      setCurrentHoveredElement(element);\n      if (!shouldIgnoreHighlight(element)) {\n        if ([\"input\", \"select\", \"textarea\", \"button\", \"a\", \"select\"].includes(element.tagName.toLowerCase())) {\n          element.style.pointerEvents = \"\";\n        }\n        element.removeAttribute(\"disabled\");\n        element.style.outline = \"\";\n        //element.style.backgroundColor = \"\";\n        element.style.boxShadow = \"\";\n        isElementHover = false;\n\n        // Hide overlay when mouse leaves an element\n        hidePersistentOverlay();\n      }\n    };\n    const existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\n    const handleClick = event => {\n      if (!isGuideInfoScreen && !isTooltipNameScreenOpen && !isCollapsed) {\n        const element = event.target;\n        event.stopPropagation();\n        event.stopImmediatePropagation();\n        event.preventDefault();\n        event.stopImmediatePropagation();\n        setElementSelected(true);\n        if (isALTKeywordEnabled) {\n          if (!shouldIgnoreHighlight(element) && !shouldIgnoreEvents(element)) {\n            if (!element.hasAttribute(\"data-target-element\")) {\n              element.setAttribute(\"data-target-element\", \"true\");\n            }\n            const xpath = getXPath(element);\n            //element.classList.add(\"\");\n            element.style.outline = \"\";\n            const getRelativexpath = getRelativeXPath(element);\n            //element.style.position = \"relative\";\n            //element.style.backgroundColor = \"rgba(108, 151, 166, 0.2)\";\n            element.style.outline = \"2px dashed #6c97a6\";\n            element.style.boxShadow = \"0 0 0 3px transparent\";\n            console.log(getRelativexpath);\n            const rect = element.getBoundingClientRect();\n            setPopupPosition({\n              left: `${rect.left}px`,\n              top: `${element.offsetHeight + window.scrollY}px`\n            });\n            setRectData(rect);\n            setAxisData(rect);\n            if (![\"BODY\", \"HTML\"].includes(element.tagName)) {\n              const tooltipPosition = {\n                x: rect.left + 5,\n                y: rect.top\n              };\n              setTooltip({\n                visible: true,\n                text: xpath,\n                position: {\n                  x: rect.left + 5,\n                  y: rect.top\n                }\n              });\n              if (xpath) {\n                setXpathToTooltipMetaData({\n                  value: xpath,\n                  PossibleElementPath: getRelativexpath,\n                  position: tooltipPosition\n                });\n\n                // For AI-created tooltips, sync the data after element selection\n                if (createWithAI && selectedTemplate === \"Tooltip\") {\n                  // Use setTimeout to ensure the xpath data is set before syncing\n                  setTimeout(() => {\n                    syncAITooltipContainerData();\n                  }, 0);\n                }\n              }\n              setOpenTooltip(true);\n              if (!existingHotspot) {\n                applyHotspotProperties(rect);\n              }\n            }\n          }\n        }\n      }\n    };\n    const handleDocumentClick = event => {\n      const target = event.target;\n      if (isALTKeywordEnabled && !isGuideInfoScreen) {\n        if (!shouldIgnoreHighlight(target) && !shouldIgnoreEvents(target)) {\n          handleClick(event);\n        }\n      }\n    };\n    if (rectData && existingHotspot) {\n      applyHotspotProperties(rectData);\n    }\n    const cleanupPersistentOverlay = () => {\n      // Remove the persistent overlay when cleaning up\n      if (persistentOverlay) {\n        persistentOverlay.remove();\n        persistentOverlay = null;\n      }\n    };\n    if (!isALTKeywordEnabled && currentHoveredElement) {\n      // Clean up all highlighted elements when element selection is disabled\n      removeAllElementHighlights();\n      cleanupPersistentOverlay();\n      // Remove custom cursor when switching to page interaction mode\n      removeCustomCursor();\n      document.removeEventListener(\"mouseover\", handleMouseOver);\n      document.removeEventListener(\"mouseout\", handleMouseOut);\n      document.removeEventListener(\"click\", handleDocumentClick, true);\n      return;\n    } else if (elementSelected === false && (selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\")) {\n      // Apply custom cursor for hotspot element selection\n      if (isALTKeywordEnabled) {\n        applyCustomCursor();\n      }\n      document.addEventListener(\"mouseover\", handleMouseOver);\n      document.addEventListener(\"mouseout\", handleMouseOut);\n      document.addEventListener(\"click\", handleDocumentClick, true);\n    } else if (!elementSelected && (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && isALTKeywordEnabled) {\n      // Apply custom cursor for tooltip element selection\n      applyCustomCursor();\n      document.addEventListener(\"mouseover\", handleMouseOver);\n      document.addEventListener(\"mouseout\", handleMouseOut);\n      document.addEventListener(\"click\", handleDocumentClick, true);\n      setElementSelected(false);\n    }\n    return () => {\n      document.removeEventListener(\"mouseover\", handleMouseOver);\n      document.removeEventListener(\"mouseout\", handleMouseOut);\n      document.removeEventListener(\"click\", handleDocumentClick, true);\n      // Clean up custom cursor on component unmount\n      removeCustomCursor();\n      // Clean up persistent overlay on component unmount\n      cleanupPersistentOverlay();\n    };\n  }, [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement]);\n\n  // Separate useEffect to handle custom cursor based on ALT keyword enabled state\n  useEffect(() => {\n    if (isALTKeywordEnabled && !elementSelected && (selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\")) {\n      // Apply custom cursor when in element selection mode\n      applyCustomCursor();\n    } else {\n      // Remove custom cursor when not in element selection mode\n      removeCustomCursor();\n    }\n\n    // Cleanup on unmount\n    return () => {\n      removeCustomCursor();\n    };\n  }, [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour]);\n  const generateDynamicId = () => {\n    return \"hotspotBlinkCreation\";\n  };\n  const applyHotspotProperties = rect => {\n    var _toolTipGuideMetaData15;\n    const hotspotPropData = toolTipGuideMetaData && ((_toolTipGuideMetaData15 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData15 === void 0 ? void 0 : _toolTipGuideMetaData15.hotspots);\n    //const xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\n    //const yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\n    const size = hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size;\n    // const left = rect.left + window.scrollX + xOffset;\n    // const top = rect.top + window.scrollY + yOffset;\n    const left = \"375\";\n    const top = \"160\";\n    if (selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\") {\n      hotspot = document.getElementById(\"hotspotBlinkCreation\");\n      if (!hotspot) {\n        hotspot = document.createElement(\"div\");\n        hotspot.id = generateDynamicId();\n        document.body.appendChild(hotspot);\n      }\n      hotspot.style.position = \"absolute\";\n      hotspot.style.left = `${left}px`;\n      hotspot.style.top = `${top}px`;\n      hotspot.style.width = `${size}px`;\n      hotspot.style.height = `${size}px`;\n      hotspot.style.backgroundColor = hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.Color ? hotspotPropData.Color : \"yellow\";\n      hotspot.style.borderRadius = \"50%\";\n      hotspot.style.zIndex = \"9999\";\n      hotspot.style.transition = \"none\";\n      hotspot.innerHTML = \"\";\n      if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Info\" || (hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Question\") {\n        const textSpan = document.createElement(\"span\");\n        textSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\n        textSpan.style.color = \"white\";\n        textSpan.style.fontSize = \"14px\";\n        textSpan.style.fontWeight = \"bold\";\n        textSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\n        textSpan.style.display = \"flex\";\n        textSpan.style.alignItems = \"center\";\n        textSpan.style.justifyContent = \"center\";\n        textSpan.style.width = \"100%\";\n        textSpan.style.height = \"100%\";\n        textSpan.style.pointerEvents = \"none\";\n        hotspot.appendChild(textSpan);\n      }\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.PulseAnimation) {\n        hotspot.classList.add(\"pulse-animation\");\n        if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.stopAnimationUponInteraction) {\n          const stopAnimation = () => {\n            if (hotspot) {\n              hotspot.classList.remove(\"pulse-animation\");\n              hotspot.classList.add(\"pulse-animation-removed\");\n              // Ensure the hotspot remains visible by keeping its styles\n              hotspot.style.display = \"flex\";\n              hotspot.style.opacity = \"1\";\n              hotspot.style.transform = \"scale(1)\";\n              // Don't remove event handlers to ensure tooltip still works\n            }\n          };\n\n          // Clear existing event handlers\n          hotspot.onclick = null;\n          hotspot.onmouseover = null;\n\n          // Add appropriate event handlers based on ShowUpon property\n          if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowUpon) === \"Hovering Hotspot\") {\n            hotspot.addEventListener(\"mouseover\", stopAnimation);\n          } else if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowUpon) === \"Clicking Hotspot\") {\n            hotspot.addEventListener(\"click\", stopAnimation);\n          } else {\n            // Default to click if ShowUpon is not specified\n            hotspot.addEventListener(\"click\", stopAnimation);\n          }\n        }\n      } else {\n        hotspot.classList.remove(\"pulse-animation\");\n      }\n    }\n  };\n\n  // Note: \"q\" key handling is now done in the Drawer component for proper navigation logic\n\n  const canvasProperties = (_toolTipGuideMetaData16 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData16 === void 0 ? void 0 : _toolTipGuideMetaData16.canvas;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [(selectedTemplate === \"Hotspot\" || selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Hotspot\" || selectedTemplateTour === \"Tooltip\") && elementSelected === true && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"backdrop\",\n      style: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n        zIndex: 99999\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 894,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(CustomWidthTooltip, {\n      selectedTemplate: selectedTemplate,\n      selectedTemplateTour: selectedTemplateTour,\n      title: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(TooltipBody, {\n          isPopoverOpen: isPopoverOpen,\n          setIsPopoverOpen: setIsPopoverOpen,\n          popupPosition: popupPosition,\n          isUnSavedChanges: isUnSavedChanges,\n          openWarning: openWarning,\n          setopenWarning: setopenWarning,\n          handleLeave: handleLeave,\n          updatedGuideData: updatedGuideData\n          // isRtl={isRtl}\t\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 7\n        }, this)\n      }, void 0, false),\n      open: (selectedTemplate === \"Hotspot\" || selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") && elementSelected === true && openTooltip,\n      placement: \"bottom\",\n      slotProps: {\n        tooltip: {\n          sx: {\n            borderRadius: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.borderRadius) || tooltipborderradius,\n            border: `${(canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.borderSize) || \"0px\"} solid ${(canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.borderColor) || tooltipBordercolor}`,\n            backgroundColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.backgroundColor) || tooltipBackgroundcolor,\n            padding: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.padding) || tooltippadding,\n            Width: `${(canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.width) || \"300px\"} !important`,\n            zIndex: 1000 // Ensure tooltip is above the backdrop\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 973,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 908,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s3(CreateTooltip, \"wF1rZL+IcdR60dmr3AdcTylLEck=\", false, function () {\n  return [useDrawerStore];\n});\n_c2 = CreateTooltip;\nconst normalizePx = (value, fallback = \"8px\") => {\n  if (!value) return fallback;\n  const val = typeof value === \"string\" ? value.trim() : `${value}`;\n  return val.endsWith(\"px\") ? val : `${val}px`;\n};\nexport default CreateTooltip;\nconst DotsStepper = ({\n  steps,\n  activeStep\n}) => {\n  return /*#__PURE__*/_jsxDEV(MobileStepper, {\n    variant: \"dots\",\n    steps: steps,\n    position: \"static\",\n    activeStep: activeStep - 1,\n    sx: {\n      maxWidth: 400,\n      flexGrow: 1,\n      display: \"flex\",\n      justifyContent: \"center\"\n    },\n    nextButton: /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false),\n    backButton: /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 987,\n    columnNumber: 3\n  }, this);\n};\n_c3 = DotsStepper;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"CustomWidthTooltip\");\n$RefreshReg$(_c2, \"CreateTooltip\");\n$RefreshReg$(_c3, \"DotsStepper\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDrawerStore", "<PERSON><PERSON><PERSON>", "tooltipClasses", "MobileStepper", "styled", "TooltipBody", "CustomCursor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TOOLTIP_MX_WIDTH", "TOOLTIP_MN_WIDTH", "TOOLTIP_HEIGHT", "EXTENSION_PART", "CustomWidthTooltip", "_s2", "_s", "className", "selectedTemplate", "selectedTemplateTour", "props", "toolTipGuideMetaData", "currentStep", "elementSelected", "document", "body", "style", "overflow", "classes", "popper", "id", "disableHoverListener", "disableTouch<PERSON><PERSON>ener", "PopperProps", "sx", "top", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "_toolTipGuideMetaData11", "_toolTipGuideMetaData12", "_toolTipGuideMetaData13", "_toolTipGuideMetaData14", "tooltip", "min<PERSON><PERSON><PERSON>", "width", "canvas", "max<PERSON><PERSON><PERSON>", "backgroundColor", "color", "fontSize", "borderRadius", "position", "padding", "boxShadow", "arrow", "_c", "CreateTooltip", "isTooltipNameScreenOpen", "isUnSavedChanges", "openWarning", "setopenWarning", "handleLeave", "updatedGuideData", "_s3", "_toolTipGuideMetaData16", "setTooltip", "setElementSelected", "guideName", "borderColor", "borderSize", "setXpathToTooltipMetaData", "tooltipXaxis", "tooltipYaxis", "tooltipWidth", "setTooltipWidth", "setTooltipPadding", "setTooltipBorderradius", "setTooltipBordersize", "setTooltipBordercolor", "setTooltipBackgroundcolor", "tooltippadding", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipBackgroundcolor", "tooltipPosition", "setTooltipPosition", "selectedOption", "steps", "currentStepIndex", "openTooltip", "setOpenTooltip", "setCurrentStepIndex", "HotspotSettings", "hotspotGuideMetaData", "isPopoverOpen", "setIsPopoverOpen", "setAxisData", "currentGuideId", "axisData", "isALTKeywordEnabled", "setIsALTKeywordEnabled", "currentHoveredElement", "setCurrentHoveredElement", "isGuideInfoScreen", "isCollapsed", "createWithAI", "syncAITooltipContainerData", "state", "hoveredElement", "setHoveredElement", "rectData", "setRectData", "overlayPosition", "setOverlayPosition", "popupPosition", "setPopupPosition", "smoothPopupPosition", "setSmoothPopupPosition", "getXPath", "element", "isUnique", "attr", "value", "tagName", "parent", "parentNode", "matchingElements", "Array", "from", "querySelectorAll", "length", "getUniqueSelector", "el", "toLowerCase", "eventAttributes", "attributes", "filter", "name", "startsWith", "getSegment", "uniqueSelector", "bannercontainer", "querySelector", "closest", "siblingIndex", "sibling", "previousElementSibling", "segments", "currentElement", "documentElement", "segment", "unshift", "parentElement", "splice", "join", "getRelativeXPath", "xpath", "evaluate", "XPathResult", "ORDERED_NODE_SNAPSHOT_TYPE", "snapshotLength", "e", "isHighlightStyle", "styleValue", "includes", "isHighlightClass", "trim", "split", "cls", "pathSegments", "_currentElement$paren", "siblings", "children", "sib", "index", "indexOf", "bannerContainer", "shouldIgnoreHighlight", "classList", "contains", "getAttribute", "shouldIgnoreEvents", "hotspot", "altElementBorderRemove", "previousSelectedDOM", "setTimeout", "outline", "pointerEvents", "removeAttribute", "remove", "removeAllElementHighlights", "highlightedElements", "for<PERSON>ach", "htmlElement", "targetElements", "quickAdaptElements", "outlineOffset", "removeAppliedStyleOfEle", "createCustomCursorDataURL", "svgString", "encodedSvg", "encodeURIComponent", "applyCustomCursor", "cursorDataURL", "cursor", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "removeCustomCursor", "existingStyle", "getElementById", "isElementHover", "persistentOverlay", "createOrUpdateOverlay", "rect", "setAttribute", "Object", "assign", "display", "window", "scrollY", "left", "scrollX", "height", "handleMouseOver", "event", "target", "getBoundingClientRect", "hidePersistentOverlay", "showPersistentOverlay", "handleMouseOut", "existingHotspot", "handleClick", "stopPropagation", "stopImmediatePropagation", "preventDefault", "hasAttribute", "getRelativexpath", "console", "log", "offsetHeight", "x", "y", "visible", "text", "PossibleElementPath", "applyHotspotProperties", "handleDocumentClick", "cleanupPersistentOverlay", "removeEventListener", "addEventListener", "generateDynamicId", "_toolTipGuideMetaData15", "hotspotPropData", "hotspots", "size", "Size", "Color", "transition", "innerHTML", "Type", "textSpan", "innerText", "fontWeight", "fontStyle", "alignItems", "justifyContent", "PulseAnimation", "add", "stopAnimationUponInteraction", "stopAnimation", "opacity", "transform", "onclick", "on<PERSON><PERSON>ver", "ShowUpon", "canvasProperties", "right", "bottom", "title", "open", "placement", "slotProps", "border", "<PERSON><PERSON><PERSON>", "_c2", "normalizePx", "fallback", "val", "endsWith", "DotsStepper", "activeStep", "variant", "flexGrow", "nextButton", "backButton", "_c3", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/Tooltip.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport \"./Tooltip.css\";\r\nimport {\r\n\tBox,\r\n\tClickAwayListener,\r\n\tTooltip,\r\n\tTooltipProps,\r\n\ttooltipClasses,\r\n\tLinearProgress,\r\n\tMobileStepper,\r\n\tBreadcrumbs,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport { styled } from \"@mui/material/styles\";\r\n\r\nimport { updateCacheWithNewRows } from \"@mui/x-data-grid/hooks/features/rows/gridRowsUtils\";\r\nimport TooltipBody from \"./TooltipBody\";\r\nimport { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport { CustomCursor } from \"../../assets/icons/icons\";\r\n\r\nexport const TOOLTIP_MX_WIDTH = 500;\r\nexport const TOOLTIP_MN_WIDTH = 250;\r\nexport const TOOLTIP_HEIGHT = 500;\r\nexport const EXTENSION_PART = \"extension-part\";\r\n\r\nexport const CustomWidthTooltip = styled(\r\n\t({\r\n\t\tclassName,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\t...props\r\n\t}: TooltipProps & { selectedTemplate: string; selectedTemplateTour: string }) => {\r\n\t\tconst { toolTipGuideMetaData, currentStep ,elementSelected} = useDrawerStore();\r\n\t\tuseEffect(() => {\r\n\t\t\tif (\r\n\t\t\t\t(\r\n\t\t\t\t\tselectedTemplate === \"Tooltip\" || \r\n\t\t\t\t\tselectedTemplate === \"Hotspot\" || \r\n\t\t\t\t\tselectedTemplateTour === \"Tooltip\" || \r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t  ) && elementSelected\r\n\t\t\t\t  \r\n\t\t\t) {\r\n\t\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t\t} else {\r\n\t\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t\t}\r\n\t\t\r\n\t\t\t// Cleanup\r\n\t\t\treturn () => {\r\n\t\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t\t};\r\n\t\t}, [selectedTemplate, selectedTemplateTour, elementSelected]);\r\n\t\treturn (\r\n\t\t\t<Tooltip\r\n\t\t\t\t{...props}\r\n\t\t\t\tclasses={{ popper: className }}\r\n\t\t\t\tid=\"Tooltip-unique\"\r\n\t\t\t\tdisableHoverListener\r\n\t\t\t\tdisableTouchListener\r\n\t\t\t\tPopperProps={{\r\n\t\t\t\t\tclassName: \"qadpt-tlprte\",\r\n\t\t\t\t\tsx: {\r\n\t\t\t\t\t\ttop:\r\n\t\t\t\t\t\t\tselectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\"\r\n\t\t\t\t\t\t\t\t? \"-110px !important\"\r\n\t\t\t\t\t\t\t\t: \"-160px !important\",\r\n\t\t\t\t\t\t\t\tzIndex:'99999 !important'\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t/>\r\n\t\t);\r\n\t}\r\n)(({ selectedTemplate }: { selectedTemplate: string }) => {\r\n\tconst { toolTipGuideMetaData, currentStep } = useDrawerStore();\r\n\r\n\treturn {\r\n\t\t[`& .${tooltipClasses.tooltip}`]: {\r\n\t\t\t// maxWidth: TOOLTIP_MX_WIDTH,\r\n\t\t\tminWidth: TOOLTIP_MN_WIDTH,\r\n\t\t\twidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || \"250px !important\",\r\n\t\t\tmaxWidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || \"800px !important\",\r\n\t\t\tbackgroundColor: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.backgroundColor}` || \"#fff\",\r\n\t\t\tcolor: \"transparent\",\r\n\t\t\tfontSize: \"14px\",\r\n\t\t\tborderRadius: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.borderRadius} !important`,\r\n\t\t\tposition: \"relative !important\",\r\n\t\t\tpadding: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.padding}` || \"2px\",\r\n\t\t\tboxShadow: \"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\", // Adds a smooth shadow\r\n\t\t},\r\n\t\t[`& .${tooltipClasses.arrow}`]: {\r\n\t\t\tcolor: \"white\", // Match arrow color with tooltip background\r\n\t\t},\r\n\t};\r\n\treturn {\r\n\t\t[`& .${tooltipClasses.tooltip}`]: {\r\n\t\t\t// maxWidth: TOOLTIP_MX_WIDTH,\r\n\t\t\tminWidth: TOOLTIP_MN_WIDTH,\r\n\t\t\tmaxWidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || \"500px !important\",\r\n\t\t\tbackgroundColor: \"white\",\r\n\t\t\tcolor: \"transparent\",\r\n\t\t\tfontSize: \"14px\",\r\n\t\t\tborderRadius: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.borderRadius || \"8px\"} !important`,\r\n\t\t\tposition: \"relative !important\",\r\n\t\t\tpadding: \"0px !important\",\r\n\t\t\tboxShadow: \"9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f\", // Adds a smooth shadow\r\n\t\t},\r\n\t\t[`& .${tooltipClasses.arrow}`]: {\r\n\t\t\tcolor: \"white\", // Match arrow color with tooltip background\r\n\t\t},\r\n\t};\r\n});\r\n\r\nconst CreateTooltip = ({\r\n\tisTooltipNameScreenOpen,\r\n\tisUnSavedChanges,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n\tupdatedGuideData,\r\n}: {\r\n\tisTooltipNameScreenOpen: boolean;\r\n\tisUnSavedChanges: boolean;\r\n\topenWarning: boolean;\r\n\tsetopenWarning: (params: boolean) => void;\r\n\thandleLeave: () => void;\r\n\t\tupdatedGuideData: any;\r\n}) => {\r\n\tconst {\r\n\t\tselectedTemplate,\r\n\t\tsetTooltip,\r\n\t\ttooltip,\r\n\t\telementSelected,\r\n\t\tsetElementSelected,\r\n\t\tguideName,\r\n\t\tborderColor,\r\n\t\tbackgroundColor,\r\n\t\tborderSize,\r\n\t\tborderRadius,\r\n\t\tpadding,\r\n\t\twidth,\r\n\t\tsetXpathToTooltipMetaData,\r\n\t\t// updatedCanvasSettings,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\ttooltipWidth,\r\n\t\tsetTooltipWidth,\r\n\t\tsetTooltipPadding,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\ttooltippadding,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipBackgroundcolor,\r\n\t\ttooltipPosition,\r\n\t\tsetTooltipPosition,\r\n\t\tselectedOption,\r\n\t\tsteps,\r\n\t\tcurrentStepIndex,\r\n\t\topenTooltip,\r\n\t\tsetOpenTooltip,\r\n\t\tsetCurrentStepIndex,\r\n\t\tHotspotSettings,\r\n\t\ttoolTipGuideMetaData,\r\n\t\thotspotGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tisPopoverOpen,\r\n\t\tsetIsPopoverOpen,\r\n\t\tsetAxisData,\r\n\t\tcurrentGuideId,\r\n\t\taxisData,\r\n\t\tisALTKeywordEnabled,\r\n\t\tsetIsALTKeywordEnabled,\r\n\t\tcurrentHoveredElement,\r\n\t\tsetCurrentHoveredElement,\r\n\t\tselectedTemplateTour,\r\n\t\tisGuideInfoScreen,\r\n\t\tisCollapsed,\r\n\t\tcreateWithAI,\r\n\t\tsyncAITooltipContainerData\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null);\r\n\tconst [rectData, setRectData] = useState<DOMRect | null>(null); // State to store rect object\r\n\tconst [overlayPosition, setOverlayPosition] = useState<{\r\n\t\ttop: number;\r\n\t\tleft: number;\r\n\t\twidth: number;\r\n\t\theight: number;\r\n\t} | null>(null);\r\n\tconst [popupPosition, setPopupPosition] = useState<any>(null);\r\n\tconst [smoothPopupPosition, setSmoothPopupPosition] = useState<any>(null);\r\n\r\n\tconst getXPath = (element: HTMLElement | null) => {\r\n\t\tconst isUnique = (attr: string, value: string, tagName: string) => {\r\n\t\t\tconst parent = element?.parentNode;\r\n\t\t\tif (!parent) return false;\r\n\t\t\tconst matchingElements = [...Array.from(parent.querySelectorAll(`${tagName}[${attr}=\"${value}\"]`))];\r\n\t\t\treturn matchingElements.length === 1;\r\n\t\t};\r\n\r\n\t\tconst getUniqueSelector = (el: HTMLElement | null) => {\r\n\t\t\tif (!el) return null;\r\n\t\t\tconst tagName = el.tagName.toLowerCase();\r\n\r\n\t\t\t// find with event attributes\r\n\t\t\tconst eventAttributes = [...Array.from(el.attributes)].filter((attr) => attr.name.startsWith(\"on\"));\r\n\t\t\tfor (const attr of eventAttributes) {\r\n\t\t\t\tif (isUnique(attr.name, attr.value, tagName)) {\r\n\t\t\t\t\treturn `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn null;\r\n\t\t};\r\n\r\n\t\tconst getSegment = (el: HTMLElement | null) => {\r\n\t\t\tif (!el) return null;\r\n\t\t\tconst tagName = el.tagName.toLowerCase();\r\n\t\t\tconst uniqueSelector = getUniqueSelector(el);\r\n\t\t\tconst bannercontainer = document.querySelector(\".quickAdopt_banner\");\r\n\t\t\tif (bannercontainer) {\r\n\t\t\t\tif (el.closest(\".quickAdopt_banner\")) {\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (uniqueSelector) return uniqueSelector;\r\n\r\n\t\t\tlet siblingIndex = 1;\r\n\t\t\tlet sibling = el.previousElementSibling;\r\n\r\n\t\t\twhile (sibling) {\r\n\t\t\t\tif (sibling.tagName === el.tagName) siblingIndex++;\r\n\t\t\t\tsibling = sibling.previousElementSibling;\r\n\t\t\t}\r\n\r\n\t\t\treturn `${tagName}[${siblingIndex}]`;\r\n\t\t};\r\n\r\n\t\tconst segments: string[] = [];\r\n\r\n\t\tlet currentElement = element;\r\n\r\n\t\twhile (currentElement && currentElement !== document.body && currentElement !== document.documentElement) {\r\n\t\t\tconst segment = getSegment(currentElement);\r\n\t\t\tif (!segment) break;\r\n\t\t\tsegments.unshift(segment);\r\n\t\t\tif (segment.startsWith(\"//*[@\")) break;\r\n\t\t\tcurrentElement = currentElement.parentElement as HTMLElement;\r\n\t\t}\r\n\r\n\t\tif (!segments[0].startsWith(\"//\")) {\r\n\t\t\tif (segments[0] !== \"html\") {\r\n\t\t\t\tsegments.unshift(\"html\");\r\n\t\t\t}\r\n\t\t\tif (segments[1] !== \"body\") {\r\n\t\t\t\tsegments.splice(1, 0, \"body\");\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn `${segments.join(\"/\")}`;\r\n\t};\r\n\tconst getRelativeXPath = (element: HTMLElement | null) => {\r\n\t\t// Check if element is valid\r\n\t\tif (!element || !element.tagName) return null;\r\n\r\n\t\tconst isUnique = (xpath: string) => {\r\n\t\t\ttry {\r\n\t\t\t\tconst matchingElements = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);\r\n\t\t\t\treturn matchingElements.snapshotLength === 1;\r\n\t\t\t} catch (e) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst isHighlightStyle = (styleValue: string) => {\r\n\t\t\t// Match your specific highlighting styles\r\n\t\t\treturn (\r\n\t\t\t\tstyleValue.includes(\"outline: 2px solid #6c97a6\") ||\r\n\t\t\t\tstyleValue.includes(\"pointer-events: none\") ||\r\n\t\t\t\tstyleValue.includes(\"pointer-events:\")\r\n\t\t\t);\r\n\t\t};\r\n\r\n\t\tconst isHighlightClass = (className: string) => {\r\n\t\t\t// Check for your highlighting class\r\n\t\t\treturn className === \"quickAdopt-selection\";\r\n\t\t};\r\n\r\n\t\tconst getUniqueSelector = (el: HTMLElement) => {\r\n\t\t\tconst tagName = el.tagName.toLowerCase();\r\n\r\n\t\t\t// Priority 1: ID\r\n\t\t\tif (el.id) {\r\n\t\t\t\tconst xpath = `//${tagName}[@id=\"${el.id}\"]`;\r\n\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t}\r\n\r\n\t\t\t// Priority 2: Class names (excluding highlight class)\r\n\t\t\tif (el.className) {\r\n\t\t\t\tconst classes = el.className\r\n\t\t\t\t\t.trim()\r\n\t\t\t\t\t.split(/\\s+/)\r\n\t\t\t\t\t.filter((cls) => !isHighlightClass(cls));\r\n\t\t\t\tfor (const className of classes) {\r\n\t\t\t\t\tif (className) {\r\n\t\t\t\t\t\tconst xpath = `//${tagName}[contains(@class, \"${className}\")]`;\r\n\t\t\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Priority 3: Event attributes\r\n\t\t\tconst eventAttributes = [...Array.from(el.attributes)].filter((attr) => attr.name.startsWith(\"on\"));\r\n\t\t\tfor (const attr of eventAttributes) {\r\n\t\t\t\tconst xpath = `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\r\n\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t}\r\n\r\n\t\t\t// Priority 4: Other attributes (excluding highlight style and disabled)\r\n\t\t\tconst attributes = [...Array.from(el.attributes)].filter(\r\n\t\t\t\t(attr) =>\r\n\t\t\t\t\tattr.name !== \"id\" &&\r\n\t\t\t\t\tattr.name !== \"class\" &&\r\n\t\t\t\t\t!attr.name.startsWith(\"on\") &&\r\n\t\t\t\t\tattr.name !== \"disabled\" &&\r\n\t\t\t\t\t!(attr.name === \"style\" && isHighlightStyle(attr.value))\r\n\t\t\t);\r\n\t\t\tfor (const attr of attributes) {\r\n\t\t\t\tconst xpath = `//${tagName}[@${attr.name}=\"${attr.value}\"]`;\r\n\t\t\t\tif (isUnique(xpath)) return xpath;\r\n\t\t\t}\r\n\r\n\t\t\treturn null;\r\n\t\t};\r\n\r\n\t\tlet currentElement = element;\r\n\t\tlet pathSegments: string[] = [];\r\n\r\n\t\twhile (currentElement && currentElement !== document.documentElement) {\r\n\t\t\tconst uniqueSelector = getUniqueSelector(currentElement);\r\n\r\n\t\t\tif (uniqueSelector) {\r\n\t\t\t\treturn uniqueSelector;\r\n\t\t\t}\r\n\r\n\t\t\t// Build positional path if no unique selector is found\r\n\t\t\tconst tagName = currentElement.tagName.toLowerCase();\r\n\t\t\tconst siblings = Array.from(currentElement.parentElement?.children || []).filter(\r\n\t\t\t\t(sib) => sib.tagName === currentElement.tagName\r\n\t\t\t);\r\n\t\t\tconst index = siblings.indexOf(currentElement) + 1;\r\n\r\n\t\t\tpathSegments.unshift(index > 1 ? `${tagName}[${index}]` : tagName);\r\n\t\t\tcurrentElement = currentElement.parentElement as HTMLElement;\r\n\t\t}\r\n\r\n\t\t// Check for banner container (from your original code)\r\n\t\tconst bannerContainer = document.querySelector(\".quickAdopt_banner\");\r\n\t\tif (bannerContainer && element.closest(\".quickAdopt_banner\")) {\r\n\t\t\treturn null;\r\n\t\t}\r\n\r\n\t\treturn `//${pathSegments.join(\"/\")}`;\r\n\t};\r\n\r\n\t// Example usage:\r\n\t// const element = document.querySelector('some-element');\r\n\t// const relativeXPath = getRelativeXPath(element);\r\n\t// console.log(relativeXPath);\r\n\r\n\tconst shouldIgnoreHighlight = (element: HTMLElement) => {\r\n\t\treturn (\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--shown\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--showing\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--hiding\") ||\r\n\t\t\telement.getAttribute(\"role\") === \"tooltip\" ||\r\n\t\t\telement.closest(\"#Tooltip-unique\") ||\r\n\t\t\telement.closest(\"#my-react-drawer\") ||\r\n\t\t\telement.closest(\"#tooltip-section-popover\") ||\r\n\t\t\telement.closest(\"#btn-setting-toolbar\") ||\r\n\t\t\telement.closest(\"#button-toolbar\") ||\r\n\t\t\telement.closest(\"#color-picker\") ||\r\n\t\t\telement.closest(\".qadpt-ext-banner\") ||\r\n\t\t\telement.closest(\"#leftDrawer\") ||\r\n\t\t\telement.closest(\"#image-popover\") ||\r\n\t\t\telement.closest(\"#toggle-fit\") ||\r\n\t\t\telement.closest(\"#color-popover\") ||\r\n\t\t\telement.closest(\"#rte-popover\") ||\r\n\t\t\telement.closest(\"#rte-alignment\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu\") ||\r\n\t\t\telement.closest(\"#rte-font\") ||\r\n\t\t\telement.closest(\"#rte-bold\") ||\r\n\t\t\telement.closest(\"#rte-italic\") ||\r\n\t\t\telement.closest(\"#rte-underline\") ||\r\n\t\t\telement.closest(\"#rte-strke-through\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu-items\") ||\r\n\t\t\telement.closest(\"#rte-more\") ||\r\n\t\t\telement.closest(\"#rte-text-color\") ||\r\n\t\t\telement.closest(\"#rte-text-color-popover\") ||\r\n\t\t\telement.closest(\"#rte-text-highlight\") ||\r\n\t\t\telement.closest(\"#rte-text-highlight-pop\") ||\r\n\t\t\telement.closest(\"#rte-text-heading\") ||\r\n\t\t\telement.closest(\"#rte-text-heading-menu-items\") ||\r\n\t\t\telement.closest(\"#rte-text-format\") ||\r\n\t\t\telement.closest(\"#rte-text-ul\") ||\r\n\t\t\telement.closest(\"#rte-text-hyperlink\") ||\r\n\t\t\telement.closest(\"#rte-video\") ||\r\n\t\t\telement.closest(\"#rte-clear-formatting\") ||\r\n\t\t\telement.closest(\"#rte-hyperlink-popover\") ||\r\n\t\t\telement.closest(\"#rte-box\") ||\r\n\t\t\telement.closest(element.id.startsWith(\"#rt-editor\") ? `${element.id}` : \"nope\") ||\r\n\t\t\telement.closest(\"#rte-placeholder\") ||\r\n\t\t\telement.closest(\"#qadpt-designpopup\") ||\r\n\t\t\telement.closest(\"#image-properties\") ||\r\n\t\t\telement.closest(\"#rte-toolbar\") ||\r\n\t\t\telement.closest(\"#tooltipdialog\") ||\r\n\t\t\telement.closest(\"#rte-toolbar-paper\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu-items\")\r\n\t\t);\r\n\t};\r\n\tconst shouldIgnoreEvents = (element: HTMLElement) => {\r\n\t\treturn (\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--shown\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--showing\") ||\r\n\t\t\telement.classList.contains(\"mdc-tooltip--hiding\") ||\r\n\t\t\telement.getAttribute(\"role\") === \"tooltip\" ||\r\n\t\t\telement.closest(\"#Tooltip-unique\") ||\r\n\t\t\telement.closest(\"#tooltip-section-popover\") ||\r\n\t\t\telement.closest(\"#btn-setting-toolbar\") ||\r\n\t\t\telement.closest(\"#button-toolbar\") ||\r\n\t\t\telement.closest(\"#color-picker\") ||\r\n\t\t\telement.closest(\".qadpt-ext-banner\") ||\r\n\t\t\telement.closest(\"#leftDrawer\") ||\r\n\t\t\telement.closest(\"#rte-popover\") ||\r\n\t\t\telement.closest(element.id.startsWith(\"#rt-editor\") ? `${element.id}` : \"nope\") ||\r\n\t\t\telement.closest(\"#rte-box\") ||\r\n\t\t\telement.closest(\"#rte-placeholder\") ||\r\n\t\t\telement.closest(\"#rte-alignment-menu-items\") ||\r\n\t\t\telement.closest(\"#qadpt-designpopup\")\r\n\t\t);\r\n\t};\r\n\r\n\tlet hotspot: any;\r\n\tconst altElementBorderRemove = () => {\r\n\t\tconst previousSelectedDOM = document.querySelector(\".quickAdopt-selection\") as HTMLElement;\r\n\t\tif (previousSelectedDOM) {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tpreviousSelectedDOM.style.outline = \"none\";\r\n\t\t\t\t//previousSelectedDOM.style.backgroundColor = \"\";\r\n\t\t\t\tpreviousSelectedDOM.style.boxShadow = \"\";\r\n\t\t\t\tpreviousSelectedDOM.style.pointerEvents = \"\";\r\n\t\t\t\tpreviousSelectedDOM.removeAttribute(\"disabled\");\r\n\t\t\t\tpreviousSelectedDOM.classList.remove(\"quickAdopt-selection\");\r\n\t\t\t}, 10);\r\n\t\t}\r\n\t};\r\n\r\n\tconst removeAllElementHighlights = () => {\r\n\t\t// Remove all elements with quickAdopt-selection class\r\n\t\tconst highlightedElements = document.querySelectorAll('.quickAdopt-selection');\r\n\t\thighlightedElements.forEach((element: Element) => {\r\n\t\t\tconst htmlElement = element as HTMLElement;\r\n\t\t\thtmlElement.style.outline = '';\r\n\t\t\t//htmlElement.style.backgroundColor = '';\r\n\t\t\thtmlElement.style.boxShadow = '';\r\n\t\t\thtmlElement.style.pointerEvents = '';\r\n\t\t\thtmlElement.removeAttribute('disabled');\r\n\t\t\thtmlElement.classList.remove('quickAdopt-selection');\r\n\t\t});\r\n\r\n\t\t// Remove any elements with data-target-element attribute\r\n\t\tconst targetElements = document.querySelectorAll('[data-target-element=\"true\"]');\r\n\t\ttargetElements.forEach((element: Element) => {\r\n\t\t\tconst htmlElement = element as HTMLElement;\r\n\t\t\thtmlElement.style.outline = '';\r\n\t\t\t//htmlElement.style.backgroundColor = '';\r\n\t\t\thtmlElement.style.boxShadow = '';\r\n\t\t\thtmlElement.style.pointerEvents = '';\r\n\t\t\thtmlElement.removeAttribute('disabled');\r\n\t\t\thtmlElement.removeAttribute('data-target-element');\r\n\t\t});\r\n\r\n\t\t// Remove any elements with quickadapt highlighting attributes\r\n\t\tconst quickAdaptElements = document.querySelectorAll('[data-quickadapt-highlighted=\"true\"]');\r\n\t\tquickAdaptElements.forEach((element: Element) => {\r\n\t\t\tconst htmlElement = element as HTMLElement;\r\n\t\t\thtmlElement.style.outline = '';\r\n\t\t\thtmlElement.style.outlineOffset = '';\r\n\t\t\t//htmlElement.style.backgroundColor = '';\r\n\t\t\thtmlElement.style.boxShadow = '';\r\n\t\t\thtmlElement.style.pointerEvents = '';\r\n\t\t\thtmlElement.removeAttribute('disabled');\r\n\t\t\thtmlElement.removeAttribute('data-quickadapt-highlighted');\r\n\t\t});\r\n\t};\r\n\r\n\tconst removeAppliedStyleOfEle = (element: HTMLElement) => {\r\n\t\telement.removeAttribute(\"disabled\");\r\n\t\telement.style.outline = \"\";\r\n\t\telement.style.pointerEvents = \"unset\";\r\n\t};\r\n\r\n\t// Custom cursor utility functions\r\n\tconst createCustomCursorDataURL = () => {\r\n\t\t// Create a data URL from the SVG\r\n\t\tconst svgString = CustomCursor;\r\n\t\tconst encodedSvg = encodeURIComponent(svgString);\r\n\t\treturn `data:image/svg+xml,${encodedSvg}`;\r\n\t};\r\n\r\n\tconst applyCustomCursor = () => {\r\n\t\tconst cursorDataURL = createCustomCursorDataURL();\r\n\t\t// Apply cursor to document body and html to ensure it covers the entire page\r\n\t\t// Using 16 16 as hotspot (center of 32x32 cursor)\r\n\t\tdocument.body.style.cursor = `url(\"${cursorDataURL}\") 16 16, crosshair`;\r\n\t\tdocument.documentElement.style.cursor = `url(\"${cursorDataURL}\") 16 16, crosshair`;\r\n\r\n\t\t// Also apply to all elements that might override the cursor, but exclude extension UI\r\n\t\tconst style = document.createElement('style');\r\n\t\tstyle.id = 'quickadapt-custom-cursor-style';\r\n\t\tstyle.textContent = `\r\n\t\t\t* {\r\n\t\t\t\tcursor: url(\"${cursorDataURL}\") 16 16, crosshair !important;\r\n\t\t\t}\r\n\t\t\t/* Exclude extension UI elements from custom cursor */\r\n\t\t\t#my-react-drawer,\r\n\t\t\t#my-react-drawer *,\r\n\t\t\t.MuiDrawer-root,\r\n\t\t\t.MuiDrawer-root *,\r\n\t\t\t.MuiDialog-root,\r\n\t\t\t.MuiDialog-root *,\r\n\t\t\t.MuiPopover-root,\r\n\t\t\t.MuiPopover-root *,\r\n\t\t\t.MuiTooltip-popper,\r\n\t\t\t.MuiTooltip-popper *,\r\n\t\t\t.qadpt-ext-banner,\r\n\t\t\t.qadpt-ext-banner *,\r\n\t\t\t#leftDrawer,\r\n\t\t\t#leftDrawer *,\r\n\t\t\t#rte-popover,\r\n\t\t\t#rte-popover *,\r\n\t\t\t#rte-box,\r\n\t\t\t#rte-box *,\r\n\t\t\t#qadpt-designpopup,\r\n\t\t\t#qadpt-designpopup *,\r\n\t\t\t.quickadapt-no-custom-cursor,\r\n\t\t\t.quickadapt-no-custom-cursor * {\r\n\t\t\t\tcursor: default !important;\r\n\t\t\t}\r\n\t\t`;\r\n\t\tdocument.head.appendChild(style);\r\n\t};\r\n\r\n\tconst removeCustomCursor = () => {\r\n\t\t// Remove custom cursor from body and html\r\n\t\tdocument.body.style.cursor = '';\r\n\t\tdocument.documentElement.style.cursor = '';\r\n\r\n\t\t// Remove the custom cursor style\r\n\t\tconst existingStyle = document.getElementById('quickadapt-custom-cursor-style');\r\n\t\tif (existingStyle) {\r\n\t\t\texistingStyle.remove();\r\n\t\t}\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tlet isElementHover = false;\r\n\t\tlet persistentOverlay: HTMLElement | null = null;\r\n\r\n\t\tconst createOrUpdateOverlay = (rect: DOMRect) => {\r\n\t\t\t// Get or create the persistent overlay element\r\n\t\t\tif (!persistentOverlay) {\r\n\t\t\t\tpersistentOverlay = document.createElement(\"div\");\r\n\t\t\t\tpersistentOverlay.className = \"quickAdopt-overlay\";\r\n\t\t\t\tpersistentOverlay.setAttribute(\"data-overlay\", \"true\");\r\n\t\t\t\tpersistentOverlay.setAttribute(\"data-persistent-overlay\", \"true\");\r\n\r\n\t\t\t\t// Set initial styles\r\n\t\t\t\tObject.assign(persistentOverlay.style, {\r\n\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\tbackgroundColor: \"rgba(108, 151, 166, 0.2)\",\r\n\t\t\t\t\toutline: \"2px dashed #6c97a6\",\r\n\t\t\t\t\tboxShadow: \"0 0 0 3px transparent\",\r\n\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\tzIndex: \"9999\",\r\n\t\t\t\t\tdisplay: \"block\"\r\n\t\t\t\t});\r\n\r\n\t\t\t\tdocument.body.appendChild(persistentOverlay);\r\n\t\t\t}\r\n\r\n\t\t\t// Show the overlay and update position and size with smooth transitions\r\n\t\t\tpersistentOverlay.style.display = \"block\";\r\n\t\t\tObject.assign(persistentOverlay.style, {\r\n\t\t\t\ttop: `${rect.top + window.scrollY}px`,\r\n\t\t\t\tleft: `${rect.left + window.scrollX}px`,\r\n\t\t\t\twidth: `${rect.width}px`,\r\n\t\t\t\theight: `${rect.height}px`\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst handleMouseOver = (event: MouseEvent) => {\r\n\tif (isALTKeywordEnabled && currentHoveredElement && !isTooltipNameScreenOpen && !isCollapsed) {\r\n\t\tconst element = event.target as HTMLElement;\r\n\t\tsetCurrentHoveredElement(element);\r\n\r\n\t\tif (!shouldIgnoreHighlight(element)) {\r\n\t\t\taltElementBorderRemove();\r\n\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tcreateOrUpdateOverlay(rect);\r\n\t\t}\r\n\t}\r\n};\r\n\t\tconst hidePersistentOverlay = () => {\r\n\t\t\tif (persistentOverlay) {\r\n\t\t\t\tpersistentOverlay.style.display = 'none';\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst showPersistentOverlay = () => {\r\n\t\t\tif (persistentOverlay) {\r\n\t\t\t\tpersistentOverlay.style.display = 'block';\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (event: MouseEvent) => {\r\n\t\t\tconst element = event.target as HTMLElement;\r\n\t\t\tsetCurrentHoveredElement(element as HTMLElement);\r\n\t\t\tif (!shouldIgnoreHighlight(element)) {\r\n\t\t\t\tif ([\"input\", \"select\", \"textarea\", \"button\", \"a\", \"select\"].includes(element.tagName.toLowerCase())) {\r\n\t\t\t\t\telement.style.pointerEvents = \"\";\r\n\t\t\t\t}\r\n\t\t\t\telement.removeAttribute(\"disabled\");\r\n\t\t\t\telement.style.outline = \"\";\r\n\t\t\t\t//element.style.backgroundColor = \"\";\r\n\t\t\t\telement.style.boxShadow = \"\";\r\n\t\t\t\tisElementHover = false;\r\n\r\n\t\t\t\t// Hide overlay when mouse leaves an element\r\n\t\t\t\thidePersistentOverlay();\r\n\t\t\t}\r\n\t\t};\r\n\t\tconst existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\r\n\t\tconst handleClick = (event: MouseEvent) => {\r\n\t\t\tif (!isGuideInfoScreen && !isTooltipNameScreenOpen && !isCollapsed) {\r\n\t\t\t\tconst element = event.target as HTMLElement;\r\n\t\t\t\tevent.stopPropagation();\r\n\t\t\t\tevent.stopImmediatePropagation();\r\n\t\t\t\tevent.preventDefault();\r\n\t\t\t\tevent.stopImmediatePropagation();\r\n\t\t\t\tsetElementSelected(true);\r\n\t\t\t\tif (isALTKeywordEnabled) {\r\n\t\t\t\t\tif (!shouldIgnoreHighlight(element) && !shouldIgnoreEvents(element)) {\r\n\t\t\t\t\t\tif (!element.hasAttribute(\"data-target-element\")) {\r\n\t\t\t\t\t\t\telement.setAttribute(\"data-target-element\", \"true\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconst xpath = getXPath(element);\r\n\t\t\t\t\t\t//element.classList.add(\"\");\r\n\t\t\t\t\t\telement.style.outline = \"\";\r\n\t\t\t\t\t\tconst getRelativexpath = getRelativeXPath(element);\r\n\t\t\t\t\t\t//element.style.position = \"relative\";\r\n\t\t\t\t\t\t//element.style.backgroundColor = \"rgba(108, 151, 166, 0.2)\";\r\n\t\t\t\t\t\telement.style.outline = \"2px dashed #6c97a6\";\r\n\t\t\t\t\t\telement.style.boxShadow = \"0 0 0 3px transparent\";\r\n\t\t\t\t\t\tconsole.log(getRelativexpath);\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tsetPopupPosition({\r\n\t\t\t\t\t\t\tleft: `${rect.left}px`,\r\n\t\t\t\t\t\t\ttop: `${element.offsetHeight + window.scrollY}px`,\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tsetRectData(rect);\r\n\t\t\t\t\t\tsetAxisData(rect);\r\n\r\n\t\t\t\t\t\tif (![\"BODY\", \"HTML\"].includes(element.tagName)) {\r\n\t\t\t\t\t\t\tconst tooltipPosition = { x: rect.left + 5, y: rect.top };\r\n\t\t\t\t\t\t\tsetTooltip({\r\n\t\t\t\t\t\t\t\tvisible: true,\r\n\t\t\t\t\t\t\t\ttext: xpath,\r\n\t\t\t\t\t\t\t\tposition: { x: rect.left + 5, y: rect.top },\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tif (xpath) {\r\n\t\t\t\t\t\t\t\tsetXpathToTooltipMetaData({\r\n\t\t\t\t\t\t\t\t\tvalue: xpath,\r\n\t\t\t\t\t\t\t\t\tPossibleElementPath: getRelativexpath,\r\n\t\t\t\t\t\t\t\t\tposition: tooltipPosition,\r\n\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t// For AI-created tooltips, sync the data after element selection\r\n\t\t\t\t\t\t\t\tif (createWithAI && selectedTemplate === \"Tooltip\") {\r\n\t\t\t\t\t\t\t\t\t// Use setTimeout to ensure the xpath data is set before syncing\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tsyncAITooltipContainerData();\r\n\t\t\t\t\t\t\t\t\t}, 0);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t\t\tif (!existingHotspot) {\r\n\t\t\t\t\t\t\t\tapplyHotspotProperties(rect);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleDocumentClick = (event: MouseEvent) => {\r\n\t\t\tconst target = event.target as HTMLElement;\r\n\r\n\t\t\tif (isALTKeywordEnabled && !isGuideInfoScreen) {\r\n\t\t\t\tif (!shouldIgnoreHighlight(target) && !shouldIgnoreEvents(target)) {\r\n\t\t\t\t\thandleClick(event);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tif (rectData && existingHotspot) {\r\n\t\t\tapplyHotspotProperties(rectData);\r\n\t\t}\r\n\r\n\t\tconst cleanupPersistentOverlay = () => {\r\n\t\t\t// Remove the persistent overlay when cleaning up\r\n\t\t\tif (persistentOverlay) {\r\n\t\t\t\tpersistentOverlay.remove();\r\n\t\t\t\tpersistentOverlay = null;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tif (!isALTKeywordEnabled && currentHoveredElement) {\r\n\t\t\t// Clean up all highlighted elements when element selection is disabled\r\n\t\t\tremoveAllElementHighlights();\r\n\t\t\tcleanupPersistentOverlay();\r\n\t\t\t// Remove custom cursor when switching to page interaction mode\r\n\t\t\tremoveCustomCursor();\r\n\t\t\tdocument.removeEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.removeEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.removeEventListener(\"click\", handleDocumentClick, true);\r\n\t\t\treturn;\r\n\t\t} else if (elementSelected === false && (selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\")) {\r\n\t\t\t// Apply custom cursor for hotspot element selection\r\n\t\t\tif (isALTKeywordEnabled) {\r\n\t\t\t\tapplyCustomCursor();\r\n\t\t\t}\r\n\t\t\tdocument.addEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.addEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.addEventListener(\"click\", handleDocumentClick, true);\r\n\t\t} else if (\r\n\t\t\t!elementSelected &&\r\n\t\t\t(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") &&\r\n\t\t\tisALTKeywordEnabled\r\n\t\t) {\r\n\t\t\t// Apply custom cursor for tooltip element selection\r\n\t\t\tapplyCustomCursor();\r\n\t\t\tdocument.addEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.addEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.addEventListener(\"click\", handleDocumentClick, true);\r\n\t\t\tsetElementSelected(false);\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"mouseover\", handleMouseOver);\r\n\t\t\tdocument.removeEventListener(\"mouseout\", handleMouseOut);\r\n\t\t\tdocument.removeEventListener(\"click\", handleDocumentClick, true);\r\n\t\t\t// Clean up custom cursor on component unmount\r\n\t\t\tremoveCustomCursor();\r\n\t\t\t// Clean up persistent overlay on component unmount\r\n\t\t\tcleanupPersistentOverlay();\r\n\t\t};\r\n\t}, [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement]);\r\n\r\n\t// Separate useEffect to handle custom cursor based on ALT keyword enabled state\r\n\tuseEffect(() => {\r\n\t\tif (isALTKeywordEnabled && !elementSelected &&\r\n\t\t\t(selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" ||\r\n\t\t\t selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\")) {\r\n\t\t\t// Apply custom cursor when in element selection mode\r\n\t\t\tapplyCustomCursor();\r\n\t\t} else {\r\n\t\t\t// Remove custom cursor when not in element selection mode\r\n\t\t\tremoveCustomCursor();\r\n\t\t}\r\n\r\n\t\t// Cleanup on unmount\r\n\t\treturn () => {\r\n\t\t\tremoveCustomCursor();\r\n\t\t};\r\n\t}, [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour]);\r\n\r\n\tconst generateDynamicId = () => {\r\n\t\treturn \"hotspotBlinkCreation\";\r\n\t};\r\n\r\n\tconst applyHotspotProperties = (rect: any) => {\r\n\t\tconst hotspotPropData = toolTipGuideMetaData && toolTipGuideMetaData[0]?.hotspots;\r\n\t\t//const xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t//const yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\tconst size = hotspotPropData?.Size;\r\n\t\t// const left = rect.left + window.scrollX + xOffset;\r\n\t\t// const top = rect.top + window.scrollY + yOffset;\r\n\t\tconst left = \"375\";\r\n\t\tconst top = \"160\";\r\n\t\tif (selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\") {\r\n\t\t\thotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\t\t\tif (!hotspot) {\r\n\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\thotspot.id = generateDynamicId();\r\n\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t}\r\n\r\n\t\t\thotspot.style.position = \"absolute\";\r\n\t\t\thotspot.style.left = `${left}px`;\r\n\t\t\thotspot.style.top = `${top}px`;\r\n\r\n\t\t\thotspot.style.width = `${size}px`;\r\n\t\t\thotspot.style.height = `${size}px`;\r\n\t\t\thotspot.style.backgroundColor = hotspotPropData?.Color ? hotspotPropData.Color : \"yellow\";\r\n\t\t\thotspot.style.borderRadius = \"50%\";\r\n\t\t\thotspot.style.zIndex = \"9999\";\r\n\t\t\thotspot.style.transition = \"none\";\r\n\t\t\thotspot.innerHTML = \"\";\r\n\t\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\t\ttextSpan.style.pointerEvents = \"none\";\r\n\t\t\t\thotspot.appendChild(textSpan);\r\n\t\t\t}\r\n\r\n\t\t\tif (hotspotPropData?.PulseAnimation) {\r\n\t\t\t\thotspot.classList.add(\"pulse-animation\");\r\n\r\n\t\t\t\tif (hotspotPropData?.stopAnimationUponInteraction) {\r\n\t\t\t\t\tconst stopAnimation = () => {\r\n\t\t\t\t\t\tif (hotspot) {\r\n\t\t\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\t\t\t// Ensure the hotspot remains visible by keeping its styles\r\n\t\t\t\t\t\t\thotspot.style.display = \"flex\";\r\n\t\t\t\t\t\t\thotspot.style.opacity = \"1\";\r\n\t\t\t\t\t\t\thotspot.style.transform = \"scale(1)\";\r\n\t\t\t\t\t\t\t// Don't remove event handlers to ensure tooltip still works\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t// Clear existing event handlers\r\n\t\t\t\t\thotspot.onclick = null;\r\n\t\t\t\t\thotspot.onmouseover = null;\r\n\r\n\t\t\t\t\t// Add appropriate event handlers based on ShowUpon property\r\n\t\t\t\t\tif (hotspotPropData?.ShowUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t\t\thotspot.addEventListener(\"mouseover\", stopAnimation);\r\n\t\t\t\t\t} else if (hotspotPropData?.ShowUpon === \"Clicking Hotspot\") {\r\n\t\t\t\t\t\thotspot.addEventListener(\"click\", stopAnimation);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Default to click if ShowUpon is not specified\r\n\t\t\t\t\t\thotspot.addEventListener(\"click\", stopAnimation);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t// Note: \"q\" key handling is now done in the Drawer component for proper navigation logic\r\n\r\n\tconst canvasProperties = toolTipGuideMetaData[currentStep - 1]?.canvas;\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{(selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\tselectedTemplate === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\") &&\r\n\t\t\t\telementSelected === true && (\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"backdrop\"\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\tzIndex: 99999,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\r\n\t\t\t<CustomWidthTooltip\r\n\t\t\t\tselectedTemplate={selectedTemplate}\r\n\t\t\t\tselectedTemplateTour={selectedTemplateTour}\r\n\t\t\t\ttitle={\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<TooltipBody\r\n\t\t\t\t\t\t\tisPopoverOpen={isPopoverOpen}\r\n\t\t\t\t\t\t\tsetIsPopoverOpen={setIsPopoverOpen}\r\n\t\t\t\t\t\t\tpopupPosition={popupPosition}\r\n\t\t\t\t\t\t\tisUnSavedChanges={isUnSavedChanges}\r\n\t\t\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t\t\t\tupdatedGuideData={updatedGuideData}\r\n\t\t\t\t\t\t\t// isRtl={isRtl}\t\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t{/* {selectedOption === 1 ? (\r\n\t\t\t\t\t\t\t<DotsStepper\r\n\t\t\t\t\t\t\t\tactiveStep={currentStep}\r\n\t\t\t\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t) : selectedOption === 2 ? (\r\n\t\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\t\tvalue={(currentStep / steps.length) * 100}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t) : selectedOption === 3 ? (\r\n\t\t\t\t\t\t\t<Breadcrumbs\r\n\t\t\t\t\t\t\t\taria-label=\"breadcrumb\"\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{steps.map((_: any, index: any) => (\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\tcolor={index === currentStepIndex ? \"primary\" : \"text.secondary\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\tStep {index + 1} of {steps.length}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</Breadcrumbs>\r\n\t\t\t\t\t\t) : null} */}\r\n\t\t\t\t\t</>\r\n\t\t\t\t}\r\n\t\t\t\topen={\r\n\t\t\t\t\t(selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\t\tselectedTemplate === \"Tooltip\" ||\r\n\t\t\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\t\t\tselectedTemplateTour === \"Hotspot\") &&\r\n\t\t\t\t\telementSelected === true &&\r\n\t\t\t\t\topenTooltip\r\n\t\t\t\t}\r\n\t\t\t\tplacement=\"bottom\"\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\ttooltip: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tborderRadius: canvasProperties?.borderRadius || tooltipborderradius,\r\n\t\t\t\t\t\t\tborder: `${canvasProperties?.borderSize || \"0px\"} solid ${canvasProperties?.borderColor || tooltipBordercolor}`,\r\n\t\t\t\t\t\t\tbackgroundColor: canvasProperties?.backgroundColor || tooltipBackgroundcolor,\r\n\t\t\t\t\t\t\tpadding: canvasProperties?.padding || tooltippadding,\r\n\t\t\t\t\t\t\tWidth: `${canvasProperties?.width || \"300px\"} !important`,\r\n\t\t\t\t\t\t\tzIndex: 1000, // Ensure tooltip is above the backdrop\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<div></div>\r\n\t\t\t</CustomWidthTooltip>\r\n\t\t</>\r\n\t);\r\n};\r\nconst normalizePx = (value:any, fallback = \"8px\") => {\r\n\tif (!value) return fallback;\r\n\tconst val = typeof value === \"string\" ? value.trim() : `${value}`;\r\n\treturn val.endsWith(\"px\") ? val : `${val}px`;\r\n};\r\nexport default CreateTooltip;\r\n\r\nconst DotsStepper = ({ steps, activeStep }: { steps: number; activeStep: number }) => {\r\n\treturn (\r\n\t\t<MobileStepper\r\n\t\t\tvariant=\"dots\"\r\n\t\t\tsteps={steps}\r\n\t\t\tposition=\"static\"\r\n\t\t\tactiveStep={activeStep - 1}\r\n\t\t\tsx={{ maxWidth: 400, flexGrow: 1, display: \"flex\", justifyContent: \"center\" }}\r\n\t\t\tnextButton={<></>}\r\n\t\t\tbackButton={<></>}\r\n\t\t/>\r\n\t);\r\n};\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAUC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAO,eAAe;AACtB,SAGCC,OAAO,EAEPC,cAAc,EAEdC,aAAa,QAGP,eAAe;AACtB,SAASC,MAAM,QAAQ,sBAAsB;AAG7C,OAAOC,WAAW,MAAM,eAAe;AAEvC,SAASC,YAAY,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,OAAO,MAAMC,gBAAgB,GAAG,GAAG;AACnC,OAAO,MAAMC,gBAAgB,GAAG,GAAG;AACnC,OAAO,MAAMC,cAAc,GAAG,GAAG;AACjC,OAAO,MAAMC,cAAc,GAAG,gBAAgB;AAE9C,OAAO,MAAMC,kBAAkB,GAAAC,GAAA,CAAGC,EAAA,CAAAb,MAAM,CAAAa,EAAA,CACvC,CAAC;EACAC,SAAS;EACTC,gBAAgB;EAChBC,oBAAoB;EACpB,GAAGC;AACuE,CAAC,KAAK;EAAAJ,EAAA;EAChF,MAAM;IAAEK,oBAAoB;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAGxB,cAAc,CAAC,CAAC;EAC9EF,SAAS,CAAC,MAAM;IACf,IACC,CACCqB,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BC,oBAAoB,KAAK,SAAS,IAClCA,oBAAoB,KAAK,SAAS,KAC5BI,eAAe,EAErB;MACDC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACxC,CAAC,MAAM;MACNH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IAClC;;IAEA;IACA,OAAO,MAAM;MACZH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IAClC,CAAC;EACF,CAAC,EAAE,CAACT,gBAAgB,EAAEC,oBAAoB,EAAEI,eAAe,CAAC,CAAC;EAC7D,oBACChB,OAAA,CAACP,OAAO;IAAA,GACHoB,KAAK;IACTQ,OAAO,EAAE;MAAEC,MAAM,EAAEZ;IAAU,CAAE;IAC/Ba,EAAE,EAAC,gBAAgB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,WAAW,EAAE;MACZhB,SAAS,EAAE,cAAc;MACzBiB,EAAE,EAAE;QACHC,GAAG,EACFjB,gBAAgB,KAAK,SAAS,IAAIC,oBAAoB,KAAK,SAAS,GACjE,mBAAmB,GACnB,mBAAmB;QACrBiB,MAAM,EAAC;MACV;IACD;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEJ,CAAC;EAAA,QAxC8DzC,cAAc;AAAA,EAyC9E,CAAC;EAAA,QAzC+DA,cAAc;AAAA,GAAAgB,GAAA,CAyC5E,CAAC;EAAEG;AAA+C,CAAC,KAAK;EAAA,IAAAuB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EAAAvC,GAAA;EACzD,MAAM;IAAEM,oBAAoB;IAAEC;EAAY,CAAC,GAAGvB,cAAc,CAAC,CAAC;EAE9D,OAAO;IACN,CAAC,MAAME,cAAc,CAACsD,OAAO,EAAE,GAAG;MACjC;MACAC,QAAQ,EAAE7C,gBAAgB;MAC1B8C,KAAK,EAAE,IAAAhB,qBAAA,GAAGpB,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAmB,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuCiB,MAAM,cAAAhB,sBAAA,uBAA7CA,sBAAA,CAA+Ce,KAAK,aAAa,IAAI,kBAAkB;MACjGE,QAAQ,EAAE,IAAAhB,sBAAA,GAAGtB,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAqB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCe,MAAM,cAAAd,sBAAA,uBAA7CA,sBAAA,CAA+Ca,KAAK,aAAa,IAAI,kBAAkB;MACpGG,eAAe,EAAE,IAAAf,sBAAA,GAAGxB,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAuB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCa,MAAM,cAAAZ,sBAAA,uBAA7CA,sBAAA,CAA+Cc,eAAe,EAAE,IAAI,MAAM;MAC9FC,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE,MAAM;MAChBC,YAAY,EAAE,IAAAhB,sBAAA,GAAG1B,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAyB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCW,MAAM,cAAAV,sBAAA,uBAA7CA,sBAAA,CAA+Ce,YAAY,aAAa;MACzFC,QAAQ,EAAE,qBAAqB;MAC/BC,OAAO,EAAE,IAAAhB,sBAAA,GAAG5B,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAA2B,sBAAA,wBAAAC,uBAAA,GAArCD,sBAAA,CAAuCS,MAAM,cAAAR,uBAAA,uBAA7CA,uBAAA,CAA+Ce,OAAO,EAAE,IAAI,KAAK;MAC7EC,SAAS,EAAE,oFAAoF,CAAE;IAClG,CAAC;IACD,CAAC,MAAMjE,cAAc,CAACkE,KAAK,EAAE,GAAG;MAC/BN,KAAK,EAAE,OAAO,CAAE;IACjB;EACD,CAAC;EACD,OAAO;IACN,CAAC,MAAM5D,cAAc,CAACsD,OAAO,EAAE,GAAG;MACjC;MACAC,QAAQ,EAAE7C,gBAAgB;MAC1BgD,QAAQ,EAAE,IAAAR,uBAAA,GAAG9B,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAA6B,uBAAA,wBAAAC,uBAAA,GAArCD,uBAAA,CAAuCO,MAAM,cAAAN,uBAAA,uBAA7CA,uBAAA,CAA+CK,KAAK,aAAa,IAAI,kBAAkB;MACpGG,eAAe,EAAE,OAAO;MACxBC,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE,MAAM;MAChBC,YAAY,EAAE,GAAG,EAAAV,uBAAA,GAAAhC,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAA+B,uBAAA,wBAAAC,uBAAA,GAArCD,uBAAA,CAAuCK,MAAM,cAAAJ,uBAAA,uBAA7CA,uBAAA,CAA+CS,YAAY,KAAI,KAAK,aAAa;MAClGC,QAAQ,EAAE,qBAAqB;MAC/BC,OAAO,EAAE,gBAAgB;MACzBC,SAAS,EAAE,oFAAoF,CAAE;IAClG,CAAC;IACD,CAAC,MAAMjE,cAAc,CAACkE,KAAK,EAAE,GAAG;MAC/BN,KAAK,EAAE,OAAO,CAAE;IACjB;EACD,CAAC;AACF,CAAC;EAAA,QArC8C9D,cAAc;AAAA,EAqC5D,CAAC;EAAA,QArC6CA,cAAc;AAAA,EAqC3D;AAACqE,EAAA,GAtFUtD,kBAAkB;AAwF/B,MAAMuD,aAAa,GAAGA,CAAC;EACtBC,uBAAuB;EACvBC,gBAAgB;EAChBC,WAAW;EACXC,cAAc;EACdC,WAAW;EACXC;AAQD,CAAC,KAAK;EAAAC,GAAA;EAAA,IAAAC,uBAAA;EACL,MAAM;IACL3D,gBAAgB;IAChB4D,UAAU;IACVvB,OAAO;IACPhC,eAAe;IACfwD,kBAAkB;IAClBC,SAAS;IACTC,WAAW;IACXrB,eAAe;IACfsB,UAAU;IACVnB,YAAY;IACZE,OAAO;IACPR,KAAK;IACL0B,yBAAyB;IACzB;IACAC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC,oBAAoB;IACpBC,qBAAqB;IACrBC,yBAAyB;IACzBC,cAAc;IACdC,mBAAmB;IACnBC,iBAAiB;IACjBC,kBAAkB;IAClBC,sBAAsB;IACtBC,eAAe;IACfC,kBAAkB;IAClBC,cAAc;IACdC,KAAK;IACLC,gBAAgB;IAChBC,WAAW;IACXC,cAAc;IACdC,mBAAmB;IACnBC,eAAe;IACfrF,oBAAoB;IACpBsF,oBAAoB;IACpBrF,WAAW;IACXsF,aAAa;IACbC,gBAAgB;IAChBC,WAAW;IACXC,cAAc;IACdC,QAAQ;IACRC,mBAAmB;IACnBC,sBAAsB;IACtBC,qBAAqB;IACrBC,wBAAwB;IACxBjG,oBAAoB;IACpBkG,iBAAiB;IACjBC,WAAW;IACXC,YAAY;IACZC;EACD,CAAC,GAAGzH,cAAc,CAAE0H,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7H,QAAQ,CAAqB,IAAI,CAAC;EAC9E,MAAM,CAAC8H,QAAQ,EAAEC,WAAW,CAAC,GAAG/H,QAAQ,CAAiB,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACgI,eAAe,EAAEC,kBAAkB,CAAC,GAAGjI,QAAQ,CAK5C,IAAI,CAAC;EACf,MAAM,CAACkI,aAAa,EAAEC,gBAAgB,CAAC,GAAGnI,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAACoI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrI,QAAQ,CAAM,IAAI,CAAC;EAEzE,MAAMsI,QAAQ,GAAIC,OAA2B,IAAK;IACjD,MAAMC,QAAQ,GAAGA,CAACC,IAAY,EAAEC,KAAa,EAAEC,OAAe,KAAK;MAClE,MAAMC,MAAM,GAAGL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,UAAU;MAClC,IAAI,CAACD,MAAM,EAAE,OAAO,KAAK;MACzB,MAAME,gBAAgB,GAAG,CAAC,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,gBAAgB,CAAC,GAAGN,OAAO,IAAIF,IAAI,KAAKC,KAAK,IAAI,CAAC,CAAC,CAAC;MACnG,OAAOI,gBAAgB,CAACI,MAAM,KAAK,CAAC;IACrC,CAAC;IAED,MAAMC,iBAAiB,GAAIC,EAAsB,IAAK;MACrD,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MACpB,MAAMT,OAAO,GAAGS,EAAE,CAACT,OAAO,CAACU,WAAW,CAAC,CAAC;;MAExC;MACA,MAAMC,eAAe,GAAG,CAAC,GAAGP,KAAK,CAACC,IAAI,CAACI,EAAE,CAACG,UAAU,CAAC,CAAC,CAACC,MAAM,CAAEf,IAAI,IAAKA,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnG,KAAK,MAAMjB,IAAI,IAAIa,eAAe,EAAE;QACnC,IAAId,QAAQ,CAACC,IAAI,CAACgB,IAAI,EAAEhB,IAAI,CAACC,KAAK,EAAEC,OAAO,CAAC,EAAE;UAC7C,OAAO,KAAKA,OAAO,KAAKF,IAAI,CAACgB,IAAI,KAAKhB,IAAI,CAACC,KAAK,IAAI;QACrD;MACD;MACA,OAAO,IAAI;IACZ,CAAC;IAED,MAAMiB,UAAU,GAAIP,EAAsB,IAAK;MAC9C,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MACpB,MAAMT,OAAO,GAAGS,EAAE,CAACT,OAAO,CAACU,WAAW,CAAC,CAAC;MACxC,MAAMO,cAAc,GAAGT,iBAAiB,CAACC,EAAE,CAAC;MAC5C,MAAMS,eAAe,GAAGnI,QAAQ,CAACoI,aAAa,CAAC,oBAAoB,CAAC;MACpE,IAAID,eAAe,EAAE;QACpB,IAAIT,EAAE,CAACW,OAAO,CAAC,oBAAoB,CAAC,EAAE;UACrC,OAAO,IAAI;QACZ;MACD;MACA,IAAIH,cAAc,EAAE,OAAOA,cAAc;MAEzC,IAAII,YAAY,GAAG,CAAC;MACpB,IAAIC,OAAO,GAAGb,EAAE,CAACc,sBAAsB;MAEvC,OAAOD,OAAO,EAAE;QACf,IAAIA,OAAO,CAACtB,OAAO,KAAKS,EAAE,CAACT,OAAO,EAAEqB,YAAY,EAAE;QAClDC,OAAO,GAAGA,OAAO,CAACC,sBAAsB;MACzC;MAEA,OAAO,GAAGvB,OAAO,IAAIqB,YAAY,GAAG;IACrC,CAAC;IAED,MAAMG,QAAkB,GAAG,EAAE;IAE7B,IAAIC,cAAc,GAAG7B,OAAO;IAE5B,OAAO6B,cAAc,IAAIA,cAAc,KAAK1I,QAAQ,CAACC,IAAI,IAAIyI,cAAc,KAAK1I,QAAQ,CAAC2I,eAAe,EAAE;MACzG,MAAMC,OAAO,GAAGX,UAAU,CAACS,cAAc,CAAC;MAC1C,IAAI,CAACE,OAAO,EAAE;MACdH,QAAQ,CAACI,OAAO,CAACD,OAAO,CAAC;MACzB,IAAIA,OAAO,CAACZ,UAAU,CAAC,OAAO,CAAC,EAAE;MACjCU,cAAc,GAAGA,cAAc,CAACI,aAA4B;IAC7D;IAEA,IAAI,CAACL,QAAQ,CAAC,CAAC,CAAC,CAACT,UAAU,CAAC,IAAI,CAAC,EAAE;MAClC,IAAIS,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;QAC3BA,QAAQ,CAACI,OAAO,CAAC,MAAM,CAAC;MACzB;MACA,IAAIJ,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;QAC3BA,QAAQ,CAACM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;MAC9B;IACD;IACA,OAAO,GAAGN,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC,EAAE;EAC/B,CAAC;EACD,MAAMC,gBAAgB,GAAIpC,OAA2B,IAAK;IACzD;IACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACI,OAAO,EAAE,OAAO,IAAI;IAE7C,MAAMH,QAAQ,GAAIoC,KAAa,IAAK;MACnC,IAAI;QACH,MAAM9B,gBAAgB,GAAGpH,QAAQ,CAACmJ,QAAQ,CAACD,KAAK,EAAElJ,QAAQ,EAAE,IAAI,EAAEoJ,WAAW,CAACC,0BAA0B,EAAE,IAAI,CAAC;QAC/G,OAAOjC,gBAAgB,CAACkC,cAAc,KAAK,CAAC;MAC7C,CAAC,CAAC,OAAOC,CAAC,EAAE;QACX,OAAO,KAAK;MACb;IACD,CAAC;IAED,MAAMC,gBAAgB,GAAIC,UAAkB,IAAK;MAChD;MACA,OACCA,UAAU,CAACC,QAAQ,CAAC,4BAA4B,CAAC,IACjDD,UAAU,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAC3CD,UAAU,CAACC,QAAQ,CAAC,iBAAiB,CAAC;IAExC,CAAC;IAED,MAAMC,gBAAgB,GAAIlK,SAAiB,IAAK;MAC/C;MACA,OAAOA,SAAS,KAAK,sBAAsB;IAC5C,CAAC;IAED,MAAMgI,iBAAiB,GAAIC,EAAe,IAAK;MAC9C,MAAMT,OAAO,GAAGS,EAAE,CAACT,OAAO,CAACU,WAAW,CAAC,CAAC;;MAExC;MACA,IAAID,EAAE,CAACpH,EAAE,EAAE;QACV,MAAM4I,KAAK,GAAG,KAAKjC,OAAO,SAASS,EAAE,CAACpH,EAAE,IAAI;QAC5C,IAAIwG,QAAQ,CAACoC,KAAK,CAAC,EAAE,OAAOA,KAAK;MAClC;;MAEA;MACA,IAAIxB,EAAE,CAACjI,SAAS,EAAE;QACjB,MAAMW,OAAO,GAAGsH,EAAE,CAACjI,SAAS,CAC1BmK,IAAI,CAAC,CAAC,CACNC,KAAK,CAAC,KAAK,CAAC,CACZ/B,MAAM,CAAEgC,GAAG,IAAK,CAACH,gBAAgB,CAACG,GAAG,CAAC,CAAC;QACzC,KAAK,MAAMrK,SAAS,IAAIW,OAAO,EAAE;UAChC,IAAIX,SAAS,EAAE;YACd,MAAMyJ,KAAK,GAAG,KAAKjC,OAAO,sBAAsBxH,SAAS,KAAK;YAC9D,IAAIqH,QAAQ,CAACoC,KAAK,CAAC,EAAE,OAAOA,KAAK;UAClC;QACD;MACD;;MAEA;MACA,MAAMtB,eAAe,GAAG,CAAC,GAAGP,KAAK,CAACC,IAAI,CAACI,EAAE,CAACG,UAAU,CAAC,CAAC,CAACC,MAAM,CAAEf,IAAI,IAAKA,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC;MACnG,KAAK,MAAMjB,IAAI,IAAIa,eAAe,EAAE;QACnC,MAAMsB,KAAK,GAAG,KAAKjC,OAAO,KAAKF,IAAI,CAACgB,IAAI,KAAKhB,IAAI,CAACC,KAAK,IAAI;QAC3D,IAAIF,QAAQ,CAACoC,KAAK,CAAC,EAAE,OAAOA,KAAK;MAClC;;MAEA;MACA,MAAMrB,UAAU,GAAG,CAAC,GAAGR,KAAK,CAACC,IAAI,CAACI,EAAE,CAACG,UAAU,CAAC,CAAC,CAACC,MAAM,CACtDf,IAAI,IACJA,IAAI,CAACgB,IAAI,KAAK,IAAI,IAClBhB,IAAI,CAACgB,IAAI,KAAK,OAAO,IACrB,CAAChB,IAAI,CAACgB,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC,IAC3BjB,IAAI,CAACgB,IAAI,KAAK,UAAU,IACxB,EAAEhB,IAAI,CAACgB,IAAI,KAAK,OAAO,IAAIyB,gBAAgB,CAACzC,IAAI,CAACC,KAAK,CAAC,CACzD,CAAC;MACD,KAAK,MAAMD,IAAI,IAAIc,UAAU,EAAE;QAC9B,MAAMqB,KAAK,GAAG,KAAKjC,OAAO,KAAKF,IAAI,CAACgB,IAAI,KAAKhB,IAAI,CAACC,KAAK,IAAI;QAC3D,IAAIF,QAAQ,CAACoC,KAAK,CAAC,EAAE,OAAOA,KAAK;MAClC;MAEA,OAAO,IAAI;IACZ,CAAC;IAED,IAAIR,cAAc,GAAG7B,OAAO;IAC5B,IAAIkD,YAAsB,GAAG,EAAE;IAE/B,OAAOrB,cAAc,IAAIA,cAAc,KAAK1I,QAAQ,CAAC2I,eAAe,EAAE;MAAA,IAAAqB,qBAAA;MACrE,MAAM9B,cAAc,GAAGT,iBAAiB,CAACiB,cAAc,CAAC;MAExD,IAAIR,cAAc,EAAE;QACnB,OAAOA,cAAc;MACtB;;MAEA;MACA,MAAMjB,OAAO,GAAGyB,cAAc,CAACzB,OAAO,CAACU,WAAW,CAAC,CAAC;MACpD,MAAMsC,QAAQ,GAAG5C,KAAK,CAACC,IAAI,CAAC,EAAA0C,qBAAA,GAAAtB,cAAc,CAACI,aAAa,cAAAkB,qBAAA,uBAA5BA,qBAAA,CAA8BE,QAAQ,KAAI,EAAE,CAAC,CAACpC,MAAM,CAC9EqC,GAAG,IAAKA,GAAG,CAAClD,OAAO,KAAKyB,cAAc,CAACzB,OACzC,CAAC;MACD,MAAMmD,KAAK,GAAGH,QAAQ,CAACI,OAAO,CAAC3B,cAAc,CAAC,GAAG,CAAC;MAElDqB,YAAY,CAAClB,OAAO,CAACuB,KAAK,GAAG,CAAC,GAAG,GAAGnD,OAAO,IAAImD,KAAK,GAAG,GAAGnD,OAAO,CAAC;MAClEyB,cAAc,GAAGA,cAAc,CAACI,aAA4B;IAC7D;;IAEA;IACA,MAAMwB,eAAe,GAAGtK,QAAQ,CAACoI,aAAa,CAAC,oBAAoB,CAAC;IACpE,IAAIkC,eAAe,IAAIzD,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,EAAE;MAC7D,OAAO,IAAI;IACZ;IAEA,OAAO,KAAK0B,YAAY,CAACf,IAAI,CAAC,GAAG,CAAC,EAAE;EACrC,CAAC;;EAED;EACA;EACA;EACA;;EAEA,MAAMuB,qBAAqB,GAAI1D,OAAoB,IAAK;IACvD,OACCA,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC5D5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IACzC5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAChD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IACjD5D,OAAO,CAAC6D,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,IAC1C7D,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,IAClCxB,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,IACnCxB,OAAO,CAACwB,OAAO,CAAC,0BAA0B,CAAC,IAC3CxB,OAAO,CAACwB,OAAO,CAAC,sBAAsB,CAAC,IACvCxB,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,IAClCxB,OAAO,CAACwB,OAAO,CAAC,eAAe,CAAC,IAChCxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,IACpCxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,IAC9BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,IACjCxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,IAC9BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,IACjCxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,IAC/BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,IACjCxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,IACtCxB,OAAO,CAACwB,OAAO,CAAC,WAAW,CAAC,IAC5BxB,OAAO,CAACwB,OAAO,CAAC,WAAW,CAAC,IAC5BxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,IAC9BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,IACjCxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,IACrCxB,OAAO,CAACwB,OAAO,CAAC,2BAA2B,CAAC,IAC5CxB,OAAO,CAACwB,OAAO,CAAC,WAAW,CAAC,IAC5BxB,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,IAClCxB,OAAO,CAACwB,OAAO,CAAC,yBAAyB,CAAC,IAC1CxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,IACtCxB,OAAO,CAACwB,OAAO,CAAC,yBAAyB,CAAC,IAC1CxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,IACpCxB,OAAO,CAACwB,OAAO,CAAC,8BAA8B,CAAC,IAC/CxB,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,IACnCxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,IAC/BxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,IACtCxB,OAAO,CAACwB,OAAO,CAAC,YAAY,CAAC,IAC7BxB,OAAO,CAACwB,OAAO,CAAC,uBAAuB,CAAC,IACxCxB,OAAO,CAACwB,OAAO,CAAC,wBAAwB,CAAC,IACzCxB,OAAO,CAACwB,OAAO,CAAC,UAAU,CAAC,IAC3BxB,OAAO,CAACwB,OAAO,CAACxB,OAAO,CAACvG,EAAE,CAAC0H,UAAU,CAAC,YAAY,CAAC,GAAG,GAAGnB,OAAO,CAACvG,EAAE,EAAE,GAAG,MAAM,CAAC,IAC/EuG,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,IACnCxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,IACrCxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,IACpCxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,IAC/BxB,OAAO,CAACwB,OAAO,CAAC,gBAAgB,CAAC,IACjCxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC,IACrCxB,OAAO,CAACwB,OAAO,CAAC,qBAAqB,CAAC,IACtCxB,OAAO,CAACwB,OAAO,CAAC,2BAA2B,CAAC;EAE9C,CAAC;EACD,MAAMsC,kBAAkB,GAAI9D,OAAoB,IAAK;IACpD,OACCA,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC5D5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IACzC5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAChD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD5D,OAAO,CAAC2D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IACjD5D,OAAO,CAAC6D,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,IAC1C7D,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,IAClCxB,OAAO,CAACwB,OAAO,CAAC,0BAA0B,CAAC,IAC3CxB,OAAO,CAACwB,OAAO,CAAC,sBAAsB,CAAC,IACvCxB,OAAO,CAACwB,OAAO,CAAC,iBAAiB,CAAC,IAClCxB,OAAO,CAACwB,OAAO,CAAC,eAAe,CAAC,IAChCxB,OAAO,CAACwB,OAAO,CAAC,mBAAmB,CAAC,IACpCxB,OAAO,CAACwB,OAAO,CAAC,aAAa,CAAC,IAC9BxB,OAAO,CAACwB,OAAO,CAAC,cAAc,CAAC,IAC/BxB,OAAO,CAACwB,OAAO,CAACxB,OAAO,CAACvG,EAAE,CAAC0H,UAAU,CAAC,YAAY,CAAC,GAAG,GAAGnB,OAAO,CAACvG,EAAE,EAAE,GAAG,MAAM,CAAC,IAC/EuG,OAAO,CAACwB,OAAO,CAAC,UAAU,CAAC,IAC3BxB,OAAO,CAACwB,OAAO,CAAC,kBAAkB,CAAC,IACnCxB,OAAO,CAACwB,OAAO,CAAC,2BAA2B,CAAC,IAC5CxB,OAAO,CAACwB,OAAO,CAAC,oBAAoB,CAAC;EAEvC,CAAC;EAED,IAAIuC,OAAY;EAChB,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACpC,MAAMC,mBAAmB,GAAG9K,QAAQ,CAACoI,aAAa,CAAC,uBAAuB,CAAgB;IAC1F,IAAI0C,mBAAmB,EAAE;MACxBC,UAAU,CAAC,MAAM;QAChBD,mBAAmB,CAAC5K,KAAK,CAAC8K,OAAO,GAAG,MAAM;QAC1C;QACAF,mBAAmB,CAAC5K,KAAK,CAACwC,SAAS,GAAG,EAAE;QACxCoI,mBAAmB,CAAC5K,KAAK,CAAC+K,aAAa,GAAG,EAAE;QAC5CH,mBAAmB,CAACI,eAAe,CAAC,UAAU,CAAC;QAC/CJ,mBAAmB,CAACN,SAAS,CAACW,MAAM,CAAC,sBAAsB,CAAC;MAC7D,CAAC,EAAE,EAAE,CAAC;IACP;EACD,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxC;IACA,MAAMC,mBAAmB,GAAGrL,QAAQ,CAACuH,gBAAgB,CAAC,uBAAuB,CAAC;IAC9E8D,mBAAmB,CAACC,OAAO,CAAEzE,OAAgB,IAAK;MACjD,MAAM0E,WAAW,GAAG1E,OAAsB;MAC1C0E,WAAW,CAACrL,KAAK,CAAC8K,OAAO,GAAG,EAAE;MAC9B;MACAO,WAAW,CAACrL,KAAK,CAACwC,SAAS,GAAG,EAAE;MAChC6I,WAAW,CAACrL,KAAK,CAAC+K,aAAa,GAAG,EAAE;MACpCM,WAAW,CAACL,eAAe,CAAC,UAAU,CAAC;MACvCK,WAAW,CAACf,SAAS,CAACW,MAAM,CAAC,sBAAsB,CAAC;IACrD,CAAC,CAAC;;IAEF;IACA,MAAMK,cAAc,GAAGxL,QAAQ,CAACuH,gBAAgB,CAAC,8BAA8B,CAAC;IAChFiE,cAAc,CAACF,OAAO,CAAEzE,OAAgB,IAAK;MAC5C,MAAM0E,WAAW,GAAG1E,OAAsB;MAC1C0E,WAAW,CAACrL,KAAK,CAAC8K,OAAO,GAAG,EAAE;MAC9B;MACAO,WAAW,CAACrL,KAAK,CAACwC,SAAS,GAAG,EAAE;MAChC6I,WAAW,CAACrL,KAAK,CAAC+K,aAAa,GAAG,EAAE;MACpCM,WAAW,CAACL,eAAe,CAAC,UAAU,CAAC;MACvCK,WAAW,CAACL,eAAe,CAAC,qBAAqB,CAAC;IACnD,CAAC,CAAC;;IAEF;IACA,MAAMO,kBAAkB,GAAGzL,QAAQ,CAACuH,gBAAgB,CAAC,sCAAsC,CAAC;IAC5FkE,kBAAkB,CAACH,OAAO,CAAEzE,OAAgB,IAAK;MAChD,MAAM0E,WAAW,GAAG1E,OAAsB;MAC1C0E,WAAW,CAACrL,KAAK,CAAC8K,OAAO,GAAG,EAAE;MAC9BO,WAAW,CAACrL,KAAK,CAACwL,aAAa,GAAG,EAAE;MACpC;MACAH,WAAW,CAACrL,KAAK,CAACwC,SAAS,GAAG,EAAE;MAChC6I,WAAW,CAACrL,KAAK,CAAC+K,aAAa,GAAG,EAAE;MACpCM,WAAW,CAACL,eAAe,CAAC,UAAU,CAAC;MACvCK,WAAW,CAACL,eAAe,CAAC,6BAA6B,CAAC;IAC3D,CAAC,CAAC;EACH,CAAC;EAED,MAAMS,uBAAuB,GAAI9E,OAAoB,IAAK;IACzDA,OAAO,CAACqE,eAAe,CAAC,UAAU,CAAC;IACnCrE,OAAO,CAAC3G,KAAK,CAAC8K,OAAO,GAAG,EAAE;IAC1BnE,OAAO,CAAC3G,KAAK,CAAC+K,aAAa,GAAG,OAAO;EACtC,CAAC;;EAED;EACA,MAAMW,yBAAyB,GAAGA,CAAA,KAAM;IACvC;IACA,MAAMC,SAAS,GAAGhN,YAAY;IAC9B,MAAMiN,UAAU,GAAGC,kBAAkB,CAACF,SAAS,CAAC;IAChD,OAAO,sBAAsBC,UAAU,EAAE;EAC1C,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,aAAa,GAAGL,yBAAyB,CAAC,CAAC;IACjD;IACA;IACA5L,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACgM,MAAM,GAAG,QAAQD,aAAa,qBAAqB;IACvEjM,QAAQ,CAAC2I,eAAe,CAACzI,KAAK,CAACgM,MAAM,GAAG,QAAQD,aAAa,qBAAqB;;IAElF;IACA,MAAM/L,KAAK,GAAGF,QAAQ,CAACmM,aAAa,CAAC,OAAO,CAAC;IAC7CjM,KAAK,CAACI,EAAE,GAAG,gCAAgC;IAC3CJ,KAAK,CAACkM,WAAW,GAAG;AACtB;AACA,mBAAmBH,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;IACDjM,QAAQ,CAACqM,IAAI,CAACC,WAAW,CAACpM,KAAK,CAAC;EACjC,CAAC;EAED,MAAMqM,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACAvM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACgM,MAAM,GAAG,EAAE;IAC/BlM,QAAQ,CAAC2I,eAAe,CAACzI,KAAK,CAACgM,MAAM,GAAG,EAAE;;IAE1C;IACA,MAAMM,aAAa,GAAGxM,QAAQ,CAACyM,cAAc,CAAC,gCAAgC,CAAC;IAC/E,IAAID,aAAa,EAAE;MAClBA,aAAa,CAACrB,MAAM,CAAC,CAAC;IACvB;EACD,CAAC;EAED9M,SAAS,CAAC,MAAM;IACf,IAAIqO,cAAc,GAAG,KAAK;IAC1B,IAAIC,iBAAqC,GAAG,IAAI;IAEhD,MAAMC,qBAAqB,GAAIC,IAAa,IAAK;MAChD;MACA,IAAI,CAACF,iBAAiB,EAAE;QACvBA,iBAAiB,GAAG3M,QAAQ,CAACmM,aAAa,CAAC,KAAK,CAAC;QACjDQ,iBAAiB,CAAClN,SAAS,GAAG,oBAAoB;QAClDkN,iBAAiB,CAACG,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;QACtDH,iBAAiB,CAACG,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;;QAEjE;QACAC,MAAM,CAACC,MAAM,CAACL,iBAAiB,CAACzM,KAAK,EAAE;UACtCsC,QAAQ,EAAE,OAAO;UACjBJ,eAAe,EAAE,0BAA0B;UAC3C4I,OAAO,EAAE,oBAAoB;UAC7BtI,SAAS,EAAE,uBAAuB;UAClCuI,aAAa,EAAE,MAAM;UACrBrK,MAAM,EAAE,MAAM;UACdqM,OAAO,EAAE;QACV,CAAC,CAAC;QAEFjN,QAAQ,CAACC,IAAI,CAACqM,WAAW,CAACK,iBAAiB,CAAC;MAC7C;;MAEA;MACAA,iBAAiB,CAACzM,KAAK,CAAC+M,OAAO,GAAG,OAAO;MACzCF,MAAM,CAACC,MAAM,CAACL,iBAAiB,CAACzM,KAAK,EAAE;QACtCS,GAAG,EAAE,GAAGkM,IAAI,CAAClM,GAAG,GAAGuM,MAAM,CAACC,OAAO,IAAI;QACrCC,IAAI,EAAE,GAAGP,IAAI,CAACO,IAAI,GAAGF,MAAM,CAACG,OAAO,IAAI;QACvCpL,KAAK,EAAE,GAAG4K,IAAI,CAAC5K,KAAK,IAAI;QACxBqL,MAAM,EAAE,GAAGT,IAAI,CAACS,MAAM;MACvB,CAAC,CAAC;IACH,CAAC;IAED,MAAMC,eAAe,GAAIC,KAAiB,IAAK;MAChD,IAAI/H,mBAAmB,IAAIE,qBAAqB,IAAI,CAAC7C,uBAAuB,IAAI,CAACgD,WAAW,EAAE;QAC7F,MAAMe,OAAO,GAAG2G,KAAK,CAACC,MAAqB;QAC3C7H,wBAAwB,CAACiB,OAAO,CAAC;QAEjC,IAAI,CAAC0D,qBAAqB,CAAC1D,OAAO,CAAC,EAAE;UACpCgE,sBAAsB,CAAC,CAAC;UAExB,MAAMgC,IAAI,GAAGhG,OAAO,CAAC6G,qBAAqB,CAAC,CAAC;UAC5Cd,qBAAqB,CAACC,IAAI,CAAC;QAC5B;MACD;IACD,CAAC;IACC,MAAMc,qBAAqB,GAAGA,CAAA,KAAM;MACnC,IAAIhB,iBAAiB,EAAE;QACtBA,iBAAiB,CAACzM,KAAK,CAAC+M,OAAO,GAAG,MAAM;MACzC;IACD,CAAC;IAED,MAAMW,qBAAqB,GAAGA,CAAA,KAAM;MACnC,IAAIjB,iBAAiB,EAAE;QACtBA,iBAAiB,CAACzM,KAAK,CAAC+M,OAAO,GAAG,OAAO;MAC1C;IACD,CAAC;IAED,MAAMY,cAAc,GAAIL,KAAiB,IAAK;MAC7C,MAAM3G,OAAO,GAAG2G,KAAK,CAACC,MAAqB;MAC3C7H,wBAAwB,CAACiB,OAAsB,CAAC;MAChD,IAAI,CAAC0D,qBAAqB,CAAC1D,OAAO,CAAC,EAAE;QACpC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC6C,QAAQ,CAAC7C,OAAO,CAACI,OAAO,CAACU,WAAW,CAAC,CAAC,CAAC,EAAE;UACrGd,OAAO,CAAC3G,KAAK,CAAC+K,aAAa,GAAG,EAAE;QACjC;QACApE,OAAO,CAACqE,eAAe,CAAC,UAAU,CAAC;QACnCrE,OAAO,CAAC3G,KAAK,CAAC8K,OAAO,GAAG,EAAE;QAC1B;QACAnE,OAAO,CAAC3G,KAAK,CAACwC,SAAS,GAAG,EAAE;QAC5BgK,cAAc,GAAG,KAAK;;QAEtB;QACAiB,qBAAqB,CAAC,CAAC;MACxB;IACD,CAAC;IACD,MAAMG,eAAe,GAAG9N,QAAQ,CAACyM,cAAc,CAAC,sBAAsB,CAAC;IAEvE,MAAMsB,WAAW,GAAIP,KAAiB,IAAK;MAC1C,IAAI,CAAC3H,iBAAiB,IAAI,CAAC/C,uBAAuB,IAAI,CAACgD,WAAW,EAAE;QACnE,MAAMe,OAAO,GAAG2G,KAAK,CAACC,MAAqB;QAC3CD,KAAK,CAACQ,eAAe,CAAC,CAAC;QACvBR,KAAK,CAACS,wBAAwB,CAAC,CAAC;QAChCT,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBV,KAAK,CAACS,wBAAwB,CAAC,CAAC;QAChC1K,kBAAkB,CAAC,IAAI,CAAC;QACxB,IAAIkC,mBAAmB,EAAE;UACxB,IAAI,CAAC8E,qBAAqB,CAAC1D,OAAO,CAAC,IAAI,CAAC8D,kBAAkB,CAAC9D,OAAO,CAAC,EAAE;YACpE,IAAI,CAACA,OAAO,CAACsH,YAAY,CAAC,qBAAqB,CAAC,EAAE;cACjDtH,OAAO,CAACiG,YAAY,CAAC,qBAAqB,EAAE,MAAM,CAAC;YACpD;YACA,MAAM5D,KAAK,GAAGtC,QAAQ,CAACC,OAAO,CAAC;YAC/B;YACAA,OAAO,CAAC3G,KAAK,CAAC8K,OAAO,GAAG,EAAE;YAC1B,MAAMoD,gBAAgB,GAAGnF,gBAAgB,CAACpC,OAAO,CAAC;YAClD;YACA;YACAA,OAAO,CAAC3G,KAAK,CAAC8K,OAAO,GAAG,oBAAoB;YAC5CnE,OAAO,CAAC3G,KAAK,CAACwC,SAAS,GAAG,uBAAuB;YACjD2L,OAAO,CAACC,GAAG,CAACF,gBAAgB,CAAC;YAC7B,MAAMvB,IAAI,GAAGhG,OAAO,CAAC6G,qBAAqB,CAAC,CAAC;YAC5CjH,gBAAgB,CAAC;cAChB2G,IAAI,EAAE,GAAGP,IAAI,CAACO,IAAI,IAAI;cACtBzM,GAAG,EAAE,GAAGkG,OAAO,CAAC0H,YAAY,GAAGrB,MAAM,CAACC,OAAO;YAC9C,CAAC,CAAC;YAEF9G,WAAW,CAACwG,IAAI,CAAC;YACjBvH,WAAW,CAACuH,IAAI,CAAC;YAEjB,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAACnD,QAAQ,CAAC7C,OAAO,CAACI,OAAO,CAAC,EAAE;cAChD,MAAMvC,eAAe,GAAG;gBAAE8J,CAAC,EAAE3B,IAAI,CAACO,IAAI,GAAG,CAAC;gBAAEqB,CAAC,EAAE5B,IAAI,CAAClM;cAAI,CAAC;cACzD2C,UAAU,CAAC;gBACVoL,OAAO,EAAE,IAAI;gBACbC,IAAI,EAAEzF,KAAK;gBACX1G,QAAQ,EAAE;kBAAEgM,CAAC,EAAE3B,IAAI,CAACO,IAAI,GAAG,CAAC;kBAAEqB,CAAC,EAAE5B,IAAI,CAAClM;gBAAI;cAC3C,CAAC,CAAC;cACF,IAAIuI,KAAK,EAAE;gBACVvF,yBAAyB,CAAC;kBACzBqD,KAAK,EAAEkC,KAAK;kBACZ0F,mBAAmB,EAAER,gBAAgB;kBACrC5L,QAAQ,EAAEkC;gBACX,CAAC,CAAC;;gBAEF;gBACA,IAAIqB,YAAY,IAAIrG,gBAAgB,KAAK,SAAS,EAAE;kBACnD;kBACAqL,UAAU,CAAC,MAAM;oBAChB/E,0BAA0B,CAAC,CAAC;kBAC7B,CAAC,EAAE,CAAC,CAAC;gBACN;cACD;cACAhB,cAAc,CAAC,IAAI,CAAC;cACpB,IAAI,CAAC8I,eAAe,EAAE;gBACrBe,sBAAsB,CAAChC,IAAI,CAAC;cAC7B;YACD;UACD;QACD;MACD;IACD,CAAC;IAED,MAAMiC,mBAAmB,GAAItB,KAAiB,IAAK;MAClD,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;MAE1C,IAAIhI,mBAAmB,IAAI,CAACI,iBAAiB,EAAE;QAC9C,IAAI,CAAC0E,qBAAqB,CAACkD,MAAM,CAAC,IAAI,CAAC9C,kBAAkB,CAAC8C,MAAM,CAAC,EAAE;UAClEM,WAAW,CAACP,KAAK,CAAC;QACnB;MACD;IACD,CAAC;IAED,IAAIpH,QAAQ,IAAI0H,eAAe,EAAE;MAChCe,sBAAsB,CAACzI,QAAQ,CAAC;IACjC;IAEA,MAAM2I,wBAAwB,GAAGA,CAAA,KAAM;MACtC;MACA,IAAIpC,iBAAiB,EAAE;QACtBA,iBAAiB,CAACxB,MAAM,CAAC,CAAC;QAC1BwB,iBAAiB,GAAG,IAAI;MACzB;IACD,CAAC;IAED,IAAI,CAAClH,mBAAmB,IAAIE,qBAAqB,EAAE;MAClD;MACAyF,0BAA0B,CAAC,CAAC;MAC5B2D,wBAAwB,CAAC,CAAC;MAC1B;MACAxC,kBAAkB,CAAC,CAAC;MACpBvM,QAAQ,CAACgP,mBAAmB,CAAC,WAAW,EAAEzB,eAAe,CAAC;MAC1DvN,QAAQ,CAACgP,mBAAmB,CAAC,UAAU,EAAEnB,cAAc,CAAC;MACxD7N,QAAQ,CAACgP,mBAAmB,CAAC,OAAO,EAAEF,mBAAmB,EAAE,IAAI,CAAC;MAChE;IACD,CAAC,MAAM,IAAI/O,eAAe,KAAK,KAAK,KAAKL,gBAAgB,KAAK,SAAS,IAAIC,oBAAoB,KAAK,SAAS,CAAC,EAAE;MAC/G;MACA,IAAI8F,mBAAmB,EAAE;QACxBuG,iBAAiB,CAAC,CAAC;MACpB;MACAhM,QAAQ,CAACiP,gBAAgB,CAAC,WAAW,EAAE1B,eAAe,CAAC;MACvDvN,QAAQ,CAACiP,gBAAgB,CAAC,UAAU,EAAEpB,cAAc,CAAC;MACrD7N,QAAQ,CAACiP,gBAAgB,CAAC,OAAO,EAAEH,mBAAmB,EAAE,IAAI,CAAC;IAC9D,CAAC,MAAM,IACN,CAAC/O,eAAe,KACfL,gBAAgB,KAAK,SAAS,IAAIC,oBAAoB,KAAK,SAAS,CAAC,IACtE8F,mBAAmB,EAClB;MACD;MACAuG,iBAAiB,CAAC,CAAC;MACnBhM,QAAQ,CAACiP,gBAAgB,CAAC,WAAW,EAAE1B,eAAe,CAAC;MACvDvN,QAAQ,CAACiP,gBAAgB,CAAC,UAAU,EAAEpB,cAAc,CAAC;MACrD7N,QAAQ,CAACiP,gBAAgB,CAAC,OAAO,EAAEH,mBAAmB,EAAE,IAAI,CAAC;MAC7DvL,kBAAkB,CAAC,KAAK,CAAC;IAC1B;IAEA,OAAO,MAAM;MACZvD,QAAQ,CAACgP,mBAAmB,CAAC,WAAW,EAAEzB,eAAe,CAAC;MAC1DvN,QAAQ,CAACgP,mBAAmB,CAAC,UAAU,EAAEnB,cAAc,CAAC;MACxD7N,QAAQ,CAACgP,mBAAmB,CAAC,OAAO,EAAEF,mBAAmB,EAAE,IAAI,CAAC;MAChE;MACAvC,kBAAkB,CAAC,CAAC;MACpB;MACAwC,wBAAwB,CAAC,CAAC;IAC3B,CAAC;EACF,CAAC,EAAE,CAAClP,oBAAoB,EAAEE,eAAe,EAAE2D,UAAU,EAAE+B,mBAAmB,EAAEE,qBAAqB,CAAC,CAAC;;EAEnG;EACAtH,SAAS,CAAC,MAAM;IACf,IAAIoH,mBAAmB,IAAI,CAAC1F,eAAe,KACzCL,gBAAgB,KAAK,SAAS,IAAIA,gBAAgB,KAAK,SAAS,IAChEC,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC,EAAE;MAC5E;MACAqM,iBAAiB,CAAC,CAAC;IACpB,CAAC,MAAM;MACN;MACAO,kBAAkB,CAAC,CAAC;IACrB;;IAEA;IACA,OAAO,MAAM;MACZA,kBAAkB,CAAC,CAAC;IACrB,CAAC;EACF,CAAC,EAAE,CAAC9G,mBAAmB,EAAE1F,eAAe,EAAEL,gBAAgB,EAAEC,oBAAoB,CAAC,CAAC;EAElF,MAAMuP,iBAAiB,GAAGA,CAAA,KAAM;IAC/B,OAAO,sBAAsB;EAC9B,CAAC;EAED,MAAML,sBAAsB,GAAIhC,IAAS,IAAK;IAAA,IAAAsC,uBAAA;IAC7C,MAAMC,eAAe,GAAGvP,oBAAoB,MAAAsP,uBAAA,GAAItP,oBAAoB,CAAC,CAAC,CAAC,cAAAsP,uBAAA,uBAAvBA,uBAAA,CAAyBE,QAAQ;IACjF;IACA;IACA,MAAMC,IAAI,GAAGF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,IAAI;IAClC;IACA;IACA,MAAMnC,IAAI,GAAG,KAAK;IAClB,MAAMzM,GAAG,GAAG,KAAK;IACjB,IAAIjB,gBAAgB,KAAK,SAAS,IAAIC,oBAAoB,KAAK,SAAS,EAAE;MACzEiL,OAAO,GAAG5K,QAAQ,CAACyM,cAAc,CAAC,sBAAsB,CAAC;MACzD,IAAI,CAAC7B,OAAO,EAAE;QACbA,OAAO,GAAG5K,QAAQ,CAACmM,aAAa,CAAC,KAAK,CAAC;QACvCvB,OAAO,CAACtK,EAAE,GAAG4O,iBAAiB,CAAC,CAAC;QAChClP,QAAQ,CAACC,IAAI,CAACqM,WAAW,CAAC1B,OAAO,CAAC;MACnC;MAEAA,OAAO,CAAC1K,KAAK,CAACsC,QAAQ,GAAG,UAAU;MACnCoI,OAAO,CAAC1K,KAAK,CAACkN,IAAI,GAAG,GAAGA,IAAI,IAAI;MAChCxC,OAAO,CAAC1K,KAAK,CAACS,GAAG,GAAG,GAAGA,GAAG,IAAI;MAE9BiK,OAAO,CAAC1K,KAAK,CAAC+B,KAAK,GAAG,GAAGqN,IAAI,IAAI;MACjC1E,OAAO,CAAC1K,KAAK,CAACoN,MAAM,GAAG,GAAGgC,IAAI,IAAI;MAClC1E,OAAO,CAAC1K,KAAK,CAACkC,eAAe,GAAGgN,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEI,KAAK,GAAGJ,eAAe,CAACI,KAAK,GAAG,QAAQ;MACzF5E,OAAO,CAAC1K,KAAK,CAACqC,YAAY,GAAG,KAAK;MAClCqI,OAAO,CAAC1K,KAAK,CAACU,MAAM,GAAG,MAAM;MAC7BgK,OAAO,CAAC1K,KAAK,CAACuP,UAAU,GAAG,MAAM;MACjC7E,OAAO,CAAC8E,SAAS,GAAG,EAAE;MACtB,IAAI,CAAAN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,IAAI,MAAK,MAAM,IAAI,CAAAP,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,IAAI,MAAK,UAAU,EAAE;QAC7E,MAAMC,QAAQ,GAAG5P,QAAQ,CAACmM,aAAa,CAAC,MAAM,CAAC;QAC/CyD,QAAQ,CAACC,SAAS,GAAGT,eAAe,CAACO,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;QAChEC,QAAQ,CAAC1P,KAAK,CAACmC,KAAK,GAAG,OAAO;QAC9BuN,QAAQ,CAAC1P,KAAK,CAACoC,QAAQ,GAAG,MAAM;QAChCsN,QAAQ,CAAC1P,KAAK,CAAC4P,UAAU,GAAG,MAAM;QAClCF,QAAQ,CAAC1P,KAAK,CAAC6P,SAAS,GAAGX,eAAe,CAACO,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAQ;QAChFC,QAAQ,CAAC1P,KAAK,CAAC+M,OAAO,GAAG,MAAM;QAC/B2C,QAAQ,CAAC1P,KAAK,CAAC8P,UAAU,GAAG,QAAQ;QACpCJ,QAAQ,CAAC1P,KAAK,CAAC+P,cAAc,GAAG,QAAQ;QACxCL,QAAQ,CAAC1P,KAAK,CAAC+B,KAAK,GAAG,MAAM;QAC7B2N,QAAQ,CAAC1P,KAAK,CAACoN,MAAM,GAAG,MAAM;QAC9BsC,QAAQ,CAAC1P,KAAK,CAAC+K,aAAa,GAAG,MAAM;QACrCL,OAAO,CAAC0B,WAAW,CAACsD,QAAQ,CAAC;MAC9B;MAEA,IAAIR,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEc,cAAc,EAAE;QACpCtF,OAAO,CAACJ,SAAS,CAAC2F,GAAG,CAAC,iBAAiB,CAAC;QAExC,IAAIf,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEgB,4BAA4B,EAAE;UAClD,MAAMC,aAAa,GAAGA,CAAA,KAAM;YAC3B,IAAIzF,OAAO,EAAE;cACZA,OAAO,CAACJ,SAAS,CAACW,MAAM,CAAC,iBAAiB,CAAC;cAC3CP,OAAO,CAACJ,SAAS,CAAC2F,GAAG,CAAC,yBAAyB,CAAC;cAChD;cACAvF,OAAO,CAAC1K,KAAK,CAAC+M,OAAO,GAAG,MAAM;cAC9BrC,OAAO,CAAC1K,KAAK,CAACoQ,OAAO,GAAG,GAAG;cAC3B1F,OAAO,CAAC1K,KAAK,CAACqQ,SAAS,GAAG,UAAU;cACpC;YACD;UACD,CAAC;;UAED;UACA3F,OAAO,CAAC4F,OAAO,GAAG,IAAI;UACtB5F,OAAO,CAAC6F,WAAW,GAAG,IAAI;;UAE1B;UACA,IAAI,CAAArB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsB,QAAQ,MAAK,kBAAkB,EAAE;YACrD9F,OAAO,CAACqE,gBAAgB,CAAC,WAAW,EAAEoB,aAAa,CAAC;UACrD,CAAC,MAAM,IAAI,CAAAjB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsB,QAAQ,MAAK,kBAAkB,EAAE;YAC5D9F,OAAO,CAACqE,gBAAgB,CAAC,OAAO,EAAEoB,aAAa,CAAC;UACjD,CAAC,MAAM;YACN;YACAzF,OAAO,CAACqE,gBAAgB,CAAC,OAAO,EAAEoB,aAAa,CAAC;UACjD;QACD;MACD,CAAC,MAAM;QACNzF,OAAO,CAACJ,SAAS,CAACW,MAAM,CAAC,iBAAiB,CAAC;MAC5C;IACD;EACD,CAAC;;EAED;;EAEA,MAAMwF,gBAAgB,IAAAtN,uBAAA,GAAGxD,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAuD,uBAAA,uBAArCA,uBAAA,CAAuCnB,MAAM;EACtE,oBACCnD,OAAA,CAAAE,SAAA;IAAAiL,QAAA,GACE,CAACxK,gBAAgB,KAAK,SAAS,IAC/BA,gBAAgB,KAAK,SAAS,IAC9BC,oBAAoB,KAAK,SAAS,IAClCA,oBAAoB,KAAK,SAAS,KAClCI,eAAe,KAAK,IAAI,iBACvBhB,OAAA;MACCU,SAAS,EAAC,UAAU;MACpBS,KAAK,EAAE;QACNsC,QAAQ,EAAE,OAAO;QACjB7B,GAAG,EAAE,CAAC;QACNyM,IAAI,EAAE,CAAC;QACPwD,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTzO,eAAe,EAAE,oBAAoB;QACrCxB,MAAM,EAAE;MACT;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACD,eAEFjC,OAAA,CAACO,kBAAkB;MAClBI,gBAAgB,EAAEA,gBAAiB;MACnCC,oBAAoB,EAAEA,oBAAqB;MAC3CmR,KAAK,eACJ/R,OAAA,CAAAE,SAAA;QAAAiL,QAAA,eACCnL,OAAA,CAACH,WAAW;UACXwG,aAAa,EAAEA,aAAc;UAC7BC,gBAAgB,EAAEA,gBAAiB;UACnCmB,aAAa,EAAEA,aAAc;UAC7BzD,gBAAgB,EAAEA,gBAAiB;UACnCC,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BC,WAAW,EAAEA,WAAY;UACzBC,gBAAgB,EAAEA;UAClB;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC,gBA0BD,CACF;MACD+P,IAAI,EACH,CAACrR,gBAAgB,KAAK,SAAS,IAC9BA,gBAAgB,KAAK,SAAS,IAC9BC,oBAAoB,KAAK,SAAS,IAClCA,oBAAoB,KAAK,SAAS,KACnCI,eAAe,KAAK,IAAI,IACxBgF,WACA;MACDiM,SAAS,EAAC,QAAQ;MAClBC,SAAS,EAAE;QACVlP,OAAO,EAAE;UACRrB,EAAE,EAAE;YACH6B,YAAY,EAAE,CAAAoO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEpO,YAAY,KAAI+B,mBAAmB;YACnE4M,MAAM,EAAE,GAAG,CAAAP,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEjN,UAAU,KAAI,KAAK,UAAU,CAAAiN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAElN,WAAW,KAAIe,kBAAkB,EAAE;YAC/GpC,eAAe,EAAE,CAAAuO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEvO,eAAe,KAAIqC,sBAAsB;YAC5EhC,OAAO,EAAE,CAAAkO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAElO,OAAO,KAAI4B,cAAc;YACpD8M,KAAK,EAAE,GAAG,CAAAR,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE1O,KAAK,KAAI,OAAO,aAAa;YACzDrB,MAAM,EAAE,IAAI,CAAE;UACf;QACD;MACD,CAAE;MAAAsJ,QAAA,eAEFnL,OAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA,eACpB,CAAC;AAEL,CAAC;AAACoC,GAAA,CA91BIP,aAAa;EAAA,QAsEdtE,cAAc;AAAA;AAAA6S,GAAA,GAtEbvO,aAAa;AA+1BnB,MAAMwO,WAAW,GAAGA,CAACrK,KAAS,EAAEsK,QAAQ,GAAG,KAAK,KAAK;EACpD,IAAI,CAACtK,KAAK,EAAE,OAAOsK,QAAQ;EAC3B,MAAMC,GAAG,GAAG,OAAOvK,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC4C,IAAI,CAAC,CAAC,GAAG,GAAG5C,KAAK,EAAE;EACjE,OAAOuK,GAAG,CAACC,QAAQ,CAAC,IAAI,CAAC,GAAGD,GAAG,GAAG,GAAGA,GAAG,IAAI;AAC7C,CAAC;AACD,eAAe1O,aAAa;AAE5B,MAAM4O,WAAW,GAAGA,CAAC;EAAE5M,KAAK;EAAE6M;AAAkD,CAAC,KAAK;EACrF,oBACC3S,OAAA,CAACL,aAAa;IACbiT,OAAO,EAAC,MAAM;IACd9M,KAAK,EAAEA,KAAM;IACbrC,QAAQ,EAAC,QAAQ;IACjBkP,UAAU,EAAEA,UAAU,GAAG,CAAE;IAC3BhR,EAAE,EAAE;MAAEyB,QAAQ,EAAE,GAAG;MAAEyP,QAAQ,EAAE,CAAC;MAAE3E,OAAO,EAAE,MAAM;MAAEgD,cAAc,EAAE;IAAS,CAAE;IAC9E4B,UAAU,eAAE9S,OAAA,CAAAE,SAAA,mBAAI,CAAE;IAClB6S,UAAU,eAAE/S,OAAA,CAAAE,SAAA,mBAAI;EAAE;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEJ,CAAC;AAAC+Q,GAAA,GAZIN,WAAW;AAAA,IAAA7O,EAAA,EAAAwO,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAApP,EAAA;AAAAoP,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}