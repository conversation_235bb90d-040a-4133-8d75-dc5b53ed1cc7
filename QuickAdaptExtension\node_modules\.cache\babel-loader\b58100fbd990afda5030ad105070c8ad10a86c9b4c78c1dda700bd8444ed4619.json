{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\ChecklistCanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\n// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { warning } from \"../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChecklistCanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowChecklistCanvasSettings,\n  selectedTemplate\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setCanvasSetting,\n    borderColor,\n    announcementJson,\n    width,\n    setWidth,\n    backgroundColor,\n    setBorderColor,\n    setBackgroundColor,\n    borderRadius,\n    setBorderRadius,\n    Annpadding,\n    setAnnPadding,\n    AnnborderSize,\n    setAnnBorderSize,\n    Bposition,\n    setBposition,\n    checklistGuideMetaData,\n    updateChecklistCanvas,\n    setIsUnSavedChanges,\n    isUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [isOpen, setIsOpen] = useState(true);\n\n  // Error states for validation\n  const [heightError, setHeightError] = useState(false);\n  const [widthError, setWidthError] = useState(false);\n  const [cornerRadiusError, setCornerRadiusError] = useState(false);\n  const [borderWidthError, setBorderWidthError] = useState(false);\n  const [checklistCanvasProperties, setChecklistCanvasProperties] = useState(() => {\n    var _checklistGuideMetaDa;\n    const initialchecklistCanvasProperties = ((_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : _checklistGuideMetaDa.canvas) || {\n      width: \"930\",\n      height: \"450\",\n      cornerRadius: \"12\",\n      primaryColor: \"#5F9EA0\",\n      borderColor: \"\",\n      backgroundColor: \"#ffffff\",\n      openByDefault: false,\n      hideAfterCompletion: true,\n      borderWidth: \"0\"\n    };\n    return initialchecklistCanvasProperties;\n  });\n  // State for tracking changes and apply button\n  const [isDisabled, setIsDisabled] = useState(true);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [initialState, setInitialState] = useState(checklistCanvasProperties);\n\n  // Function to check if the Apply button should be enabled\n  const updateApplyButtonState = (changed, hasErrors = false) => {\n    setIsDisabled(!changed || hasErrors);\n  };\n\n  // Effect to check for any changes compared to initial state\n  useEffect(() => {\n    // Compare current properties with initial state\n    const hasAnyChanges = JSON.stringify(checklistCanvasProperties) !== JSON.stringify(initialState);\n    setHasChanges(hasAnyChanges);\n\n    // Check for validation errors\n    const hasValidationErrors = heightError || widthError || cornerRadiusError || borderWidthError;\n    updateApplyButtonState(hasAnyChanges, hasValidationErrors);\n  }, [checklistCanvasProperties, initialState, heightError, widthError, cornerRadiusError, borderWidthError]);\n  const handleBorderColorChange = e => setBorderColor(e.target.value);\n  const handleBackgroundColorChange = e => setBackgroundColor(e.target.value);\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowChecklistCanvasSettings(false);\n  };\n  const onPropertyChange = (key, value) => {\n    setChecklistCanvasProperties(prevState => {\n      const newState = {\n        ...prevState,\n        [key]: value\n      };\n      // Mark that changes have been made\n      setHasChanges(true);\n      return newState;\n    });\n  };\n  const handleApplyChanges = () => {\n    // Apply the changes - updateChecklistCanvas will handle recording the change for undo/redo\n    updateChecklistCanvas(checklistCanvasProperties);\n    // Update the initial state to the current state after applying changes\n    setInitialState({\n      ...checklistCanvasProperties\n    });\n    // Reset the changes flag\n    setHasChanges(false);\n    // Disable the Apply button\n    setIsDisabled(true);\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate('Canvas')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-canblock\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Height')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.height,\n                  size: \"small\",\n                  autoFocus: true,\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"height\", '0');\n                      setHeightError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate height between 100px and 1000px\n                    if (inputValue < 400 || inputValue > 600) {\n                      setHeightError(true);\n                    } else {\n                      setHeightError(false);\n                    }\n                    onPropertyChange(\"height\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: heightError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 6\n            }, this), heightError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 8\n              }, this), translate('Value must be between 400px and 600px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Width')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.width,\n                  size: \"small\",\n                  autoFocus: true,\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"width\", '0');\n                      setWidthError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate width between 300px and 1200px\n                    if (inputValue < 300 || inputValue > 1200) {\n                      setWidthError(true);\n                    } else {\n                      setWidthError(false);\n                    }\n                    onPropertyChange(\"width\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: widthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 25\n            }, this), widthError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 8\n              }, this), translate('Value must be between 300px and 1200px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Corner Radius')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.cornerRadius,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"cornerRadius\", '0');\n                      setCornerRadiusError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate corner radius between 0px and 50px\n                    if (inputValue < 0 || inputValue > 50) {\n                      setCornerRadiusError(true);\n                    } else {\n                      setCornerRadiusError(false);\n                    }\n                    onPropertyChange(\"cornerRadius\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: cornerRadiusError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 6\n            }, this), cornerRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 8\n              }, this), translate('Value must be between 0px and 50px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Border Width')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.borderWidth,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"borderWidth\", '0');\n                      setBorderWidthError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate border width between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setBorderWidthError(true);\n                    } else {\n                      setBorderWidthError(false);\n                    }\n                    onPropertyChange(\"borderWidth\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderWidthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 7\n            }, this), borderWidthError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 8\n              }, this), translate('Value must be between 0px and 20px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Primary Color')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistCanvasProperties.primaryColor,\n                  onChange: e => onPropertyChange(\"primaryColor\", e.target.value),\n                  className: \"qadpt-color-input\",\n                  style: {\n                    backgroundColor: '#5F9EA0'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Background')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistCanvasProperties.backgroundColor,\n                  onChange: e => onPropertyChange(\"backgroundColor\", e.target.value),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Border')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistCanvasProperties.borderColor,\n                  onChange: e => onPropertyChange(\"borderColor\", e.target.value),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Open by Default')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: checklistCanvasProperties.openByDefault,\n                    onChange: e => onPropertyChange(\"openByDefault\", e.target.checked),\n                    name: \"showByDefault\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 33\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Hide After Completion')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: checklistCanvasProperties.hideAfterCompletion,\n                    onChange: e => onPropertyChange(\"hideAfterCompletion\", e.target.checked),\n                    name: \"showByDefault\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 33\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleApplyChanges,\n            className: `qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`,\n            disabled: isDisabled,\n            children: translate('Apply')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(ChecklistCanvasSettings, \"yaGjregVEmgZKCuVW58zdnUT74s=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ChecklistCanvasSettings;\nexport default ChecklistCanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"ChecklistCanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "ChecklistCanvasSettings", "zindeex", "setZindeex", "setShowChecklistCanvasSettings", "selectedTemplate", "_s", "t", "translate", "setCanvasSetting", "borderColor", "announcement<PERSON><PERSON>", "width", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "setBorderColor", "setBackgroundColor", "borderRadius", "setBorderRadius", "Annpadding", "setAnnPadding", "AnnborderSize", "setAnnBorderSize", "Bposition", "setBposition", "checklistGuideMetaData", "updateChecklistCanvas", "setIsUnSavedChanges", "isUnSavedChanges", "state", "isOpen", "setIsOpen", "heightError", "setHeightError", "widthError", "setWidthError", "cornerRadiusError", "setCornerRadiusError", "borderWidthError", "setBorderWidthError", "checklistCanvasProperties", "setChecklistCanvasProperties", "_checklistGuideMetaDa", "initialchecklistCanvasProperties", "canvas", "height", "cornerRadius", "primaryColor", "openByDefault", "hideAfterCompletion", "borderWidth", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "initialState", "setInitialState", "updateApplyButtonState", "changed", "hasErrors", "hasAnyChanges", "JSON", "stringify", "hasValidationErrors", "handleBorderColorChange", "e", "target", "value", "handleBackgroundColorChange", "handleClose", "onPropertyChange", "key", "prevState", "newState", "handleApplyChanges", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "variant", "autoFocus", "onChange", "test", "inputValue", "parseInt", "InputProps", "endAdornment", "sx", "border", "error", "style", "fontSize", "color", "textAlign", "top", "left", "marginBottom", "display", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "fullWidth", "type", "checked", "name", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistCanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport RadioButtonUncheckedIcon from \"@mui/icons-material/RadioButtonUnchecked\";\r\nimport RadioButtonCheckedIcon from \"@mui/icons-material/RadioButtonChecked\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { defaultDots, topLeft, topCenter, topRight, middleLeft, middleCenter, middleRight, bottomLeft, bottomMiddle, bottomRight, topcenter, warning } from \"../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ChecklistCanvasSettings = ({ zindeex, setZindeex, setShowChecklistCanvasSettings, selectedTemplate }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetCanvasSetting,\r\n\t\tborderColor,\r\n\t\tannouncementJson,\r\n\t\twidth,\r\n\t\tsetWidth,\r\n\t\tbackgroundColor,\r\n\t\tsetBorderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tborderRadius,\r\n\t\tsetBorderRadius,\r\n\t\tAnnpadding,\r\n\t\tsetAnnPadding,\r\n\t\tAnnborderSize,\r\n\t\tsetAnnBorderSize,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tchecklistGuideMetaData,\r\n\t\tupdateChecklistCanvas,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tisUnSavedChanges,\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\r\n\t// Error states for validation\r\n\tconst [heightError, setHeightError] = useState(false);\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [cornerRadiusError, setCornerRadiusError] = useState(false);\r\n\tconst [borderWidthError, setBorderWidthError] = useState(false);\r\n\r\n\r\n\tconst [checklistCanvasProperties, setChecklistCanvasProperties] = useState<any>(() => {\r\n\t\tconst initialchecklistCanvasProperties = checklistGuideMetaData[0]?.canvas || {\r\n\twidth: \"930\",\r\n\theight: \"450\",\r\n\tcornerRadius: \"12\",\r\n\tprimaryColor: \"#5F9EA0\",\r\n\t\t\tborderColor: \"\",\r\n\tbackgroundColor: \"#ffffff\",\r\n\t\t\topenByDefault: false,\r\n\t\t\thideAfterCompletion: true,\r\n\tborderWidth:\"0\"\r\n\t\t};\r\n\t\treturn initialchecklistCanvasProperties;\r\n\t});\r\n\t// State for tracking changes and apply button\r\n\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\tconst [initialState, setInitialState] = useState(checklistCanvasProperties);\r\n\r\n\t// Function to check if the Apply button should be enabled\r\n\tconst updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {\r\n\t\tsetIsDisabled(!changed || hasErrors);\r\n\t};\r\n\r\n\t// Effect to check for any changes compared to initial state\r\n\tuseEffect(() => {\r\n\t\t// Compare current properties with initial state\r\n\t\tconst hasAnyChanges = JSON.stringify(checklistCanvasProperties) !== JSON.stringify(initialState);\r\n\t\tsetHasChanges(hasAnyChanges);\r\n\r\n\t\t// Check for validation errors\r\n\t\tconst hasValidationErrors = heightError || widthError || cornerRadiusError || borderWidthError;\r\n\r\n\t\tupdateApplyButtonState(hasAnyChanges, hasValidationErrors);\r\n\t}, [checklistCanvasProperties, initialState, heightError, widthError, cornerRadiusError, borderWidthError]);\r\n\r\n\tconst handleBorderColorChange = (e: any) => setBorderColor(e.target.value);\r\n\tconst handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowChecklistCanvasSettings(false);\r\n\r\n\t};\r\n\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\tsetChecklistCanvasProperties((prevState: any) => {\r\n\t\t\tconst newState = {\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t};\r\n\t\t\t// Mark that changes have been made\r\n\t\t\tsetHasChanges(true);\r\n\t\t\treturn newState;\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Apply the changes - updateChecklistCanvas will handle recording the change for undo/redo\r\n\t\tupdateChecklistCanvas(checklistCanvasProperties);\r\n\t\t// Update the initial state to the current state after applying changes\r\n\t\tsetInitialState({ ...checklistCanvasProperties });\r\n\t\t// Reset the changes flag\r\n\t\tsetHasChanges(false);\r\n\t\t// Disable the Apply button\r\n\t\tsetIsDisabled(true);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate('Canvas')}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Position Grid */}\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\r\n\r\n\t\t\t\t\t{/* Height Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Height')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.height}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"height\", '0');\r\n\t\t\t\t\t\t\t\t\tsetHeightError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate height between 100px and 1000px\r\n\t\t\t\t\t\t\t\tif (inputValue < 400 || inputValue > 600) {\r\n\t\t\t\t\t\t\t\t\tsetHeightError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetHeightError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"height\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={heightError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\t\t\t\t\t{heightError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 400px and 600px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n                        <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Width')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.width}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"width\", '0');\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate width between 300px and 1200px\r\n\t\t\t\t\t\t\t\tif (inputValue < 300 || inputValue > 1200) {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"width\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 300px and 1200px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* Corner Radius Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Corner Radius')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.cornerRadius}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"cornerRadius\", '0');\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate corner radius between 0px and 50px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 50) {\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"cornerRadius\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={cornerRadiusError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t{cornerRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 0px and 50px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Border Width')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.borderWidth}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"borderWidth\", '0');\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border width between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"borderWidth\", value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderWidthError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderWidthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 0px and 20px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n                    <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Primary Color')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.primaryColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"primaryColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#5F9EA0'}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Background')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.backgroundColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"backgroundColor\", e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Border')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.borderColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"borderColor\", e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Open by Default')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    checked={checklistCanvasProperties.openByDefault}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"openByDefault\", e.target.checked)}\r\n                                    name=\"showByDefault\"\r\n                                />\r\n                                <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n                        <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Hide After Completion')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    checked={checklistCanvasProperties.hideAfterCompletion}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"hideAfterCompletion\", e.target.checked)}\r\n                                    name=\"showByDefault\"\r\n                                />\r\n                                <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate('Apply')}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ChecklistCanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,QAAiB,eAAe;AAC7F,OAAOC,SAAS,MAAM,2BAA2B;AAGjD;AACA,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAA6IC,OAAO,QAAQ,0BAA0B;AACtL,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,uBAAuB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,8BAA8B;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACnH,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGV,cAAc,CAAC,CAAC;EACzC,MAAM;IACLW,gBAAgB;IAChBC,WAAW;IACXC,gBAAgB;IAChBC,KAAK;IACLC,QAAQ;IACRC,eAAe;IACfC,cAAc;IACdC,kBAAkB;IAClBC,YAAY;IACZC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC,SAAS;IACTC,YAAY;IACZC,sBAAsB;IACtBC,qBAAqB;IACrBC,mBAAmB;IACnBC;EACD,CAAC,GAAGhC,cAAc,CAAEiC,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;;EAE1C;EACA,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8C,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAG/D,MAAM,CAACoD,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGrD,QAAQ,CAAM,MAAM;IAAA,IAAAsD,qBAAA;IACrF,MAAMC,gCAAgC,GAAG,EAAAD,qBAAA,GAAAjB,sBAAsB,CAAC,CAAC,CAAC,cAAAiB,qBAAA,uBAAzBA,qBAAA,CAA2BE,MAAM,KAAI;MAC/EhC,KAAK,EAAE,KAAK;MACZiC,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,SAAS;MACrBrC,WAAW,EAAE,EAAE;MACjBI,eAAe,EAAE,SAAS;MACxBkC,aAAa,EAAE,KAAK;MACpBC,mBAAmB,EAAE,IAAI;MAC3BC,WAAW,EAAC;IACX,CAAC;IACD,OAAOP,gCAAgC;EACxC,CAAC,CAAC;EACF;EACA,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAACoD,yBAAyB,CAAC;;EAE3E;EACA,MAAMiB,sBAAsB,GAAGA,CAACC,OAAgB,EAAEC,SAAkB,GAAG,KAAK,KAAK;IAChFP,aAAa,CAAC,CAACM,OAAO,IAAIC,SAAS,CAAC;EACrC,CAAC;;EAED;EACAxE,SAAS,CAAC,MAAM;IACf;IACA,MAAMyE,aAAa,GAAGC,IAAI,CAACC,SAAS,CAACtB,yBAAyB,CAAC,KAAKqB,IAAI,CAACC,SAAS,CAACP,YAAY,CAAC;IAChGD,aAAa,CAACM,aAAa,CAAC;;IAE5B;IACA,MAAMG,mBAAmB,GAAG/B,WAAW,IAAIE,UAAU,IAAIE,iBAAiB,IAAIE,gBAAgB;IAE9FmB,sBAAsB,CAACG,aAAa,EAAEG,mBAAmB,CAAC;EAC3D,CAAC,EAAE,CAACvB,yBAAyB,EAAEe,YAAY,EAAEvB,WAAW,EAAEE,UAAU,EAAEE,iBAAiB,EAAEE,gBAAgB,CAAC,CAAC;EAE3G,MAAM0B,uBAAuB,GAAIC,CAAM,IAAKlD,cAAc,CAACkD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1E,MAAMC,2BAA2B,GAAIH,CAAM,IAAKjD,kBAAkB,CAACiD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAElF,MAAME,WAAW,GAAGA,CAAA,KAAM;IACzBtC,SAAS,CAAC,KAAK,CAAC;IAChB3B,8BAA8B,CAAC,KAAK,CAAC;EAEtC,CAAC;EACD,MAAMkE,gBAAgB,GAAGA,CAACC,GAAQ,EAAEJ,KAAU,KAAK;IAClD1B,4BAA4B,CAAE+B,SAAc,IAAK;MAChD,MAAMC,QAAQ,GAAG;QAChB,GAAGD,SAAS;QACZ,CAACD,GAAG,GAAGJ;MACR,CAAC;MACD;MACAb,aAAa,CAAC,IAAI,CAAC;MACnB,OAAOmB,QAAQ;IAChB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACAhD,qBAAqB,CAACc,yBAAyB,CAAC;IAChD;IACAgB,eAAe,CAAC;MAAE,GAAGhB;IAA0B,CAAC,CAAC;IACjD;IACAc,aAAa,CAAC,KAAK,CAAC;IACpB;IACAF,aAAa,CAAC,IAAI,CAAC;IACnBiB,WAAW,CAAC,CAAC;IACb1C,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAI,CAACG,MAAM,EAAE,OAAO,IAAI;EAExB;IAAA;IACC;IACA9B,OAAA;MACC2E,EAAE,EAAC,mBAAmB;MACtBC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7B7E,OAAA;QAAK4E,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7B7E,OAAA;UAAK4E,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnC7E,OAAA,CAACR,UAAU;YACV,cAAW,OAAO;YAClBsF,OAAO,EAAET,WAAY;YAAAQ,QAAA,eAErB7E,OAAA,CAACL,2BAA2B;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpB,CAAC,eACblF,OAAA;YAAK4E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAErE,SAAS,CAAC,QAAQ;UAAC;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAExDlF,OAAA,CAACR,UAAU;YACV2F,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBL,OAAO,EAAET,WAAY;YAAAQ,QAAA,eAErB7E,OAAA,CAACN,SAAS;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNlF,OAAA;UAAK4E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC/B7E,OAAA;YAAK4E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAI9B7E,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,QAAQ;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChElF,OAAA;gBAAA6E,QAAA,eACD7E,OAAA,CAACT,SAAS;kBACT6F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAACK,MAAO;kBACxCsC,IAAI,EAAC,OAAO;kBACZE,SAAS;kBACTT,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;sBAC/BrC,cAAc,CAAC,KAAK,CAAC;sBACrB;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACsD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;sBACzCvD,cAAc,CAAC,IAAI,CAAC;oBACrB,CAAC,MAAM;sBACNA,cAAc,CAAC,KAAK,CAAC;oBACtB;oBAEAqC,gBAAgB,CAAC,QAAQ,EAAEH,KAAK,CAAC;kBAClC,CAAE;kBAEFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE9D;gBAAY;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,EACxBlD,WAAW,iBACXhC,OAAA,CAACV,UAAU;cACVyG,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF7E,OAAA;gBAAM+F,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7G;gBAAQ;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA1E,SAAS,CAAC,wCAAwC,CAAC;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACZ,eACkBlF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACnD7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,OAAO;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DlF,OAAA;gBAAA6E,QAAA,eACD7E,OAAA,CAACT,SAAS;kBACT6F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAAC5B,KAAM;kBACvCuE,IAAI,EAAC,OAAO;kBACZE,SAAS;kBACTT,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC;sBAC9BnC,aAAa,CAAC,KAAK,CAAC;sBACpB;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACoD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,IAAI,EAAE;sBAC1CrD,aAAa,CAAC,IAAI,CAAC;oBACpB,CAAC,MAAM;sBACNA,aAAa,CAAC,KAAK,CAAC;oBACrB;oBAEAmC,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC;kBACjC,CAAE;kBAEFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE5D;gBAAW;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACLhD,UAAU,iBACVlC,OAAA,CAACV,UAAU;cACVyG,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF7E,OAAA;gBAAM+F,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7G;gBAAQ;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA1E,SAAS,CAAC,yCAAyC,CAAC;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACZ,eAEDlF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,eAAe;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvElF,OAAA;gBAAA6E,QAAA,eACD7E,OAAA,CAACT,SAAS;kBACT6F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAACM,YAAa;kBAC9C6D,SAAS;kBACTxB,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,cAAc,EAAE,GAAG,CAAC;sBACrCjC,oBAAoB,CAAC,KAAK,CAAC;sBAC3B;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACkD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCnD,oBAAoB,CAAC,IAAI,CAAC;oBAC3B,CAAC,MAAM;sBACNA,oBAAoB,CAAC,KAAK,CAAC;oBAC5B;oBAEAiC,gBAAgB,CAAC,cAAc,EAAEH,KAAK,CAAC;kBACxC,CAAE;kBAEFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE1D;gBAAkB;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACN9C,iBAAiB,iBACjBpC,OAAA,CAACV,UAAU;cACVyG,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF7E,OAAA;gBAAM+F,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7G;gBAAQ;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA1E,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAEAlF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,cAAc;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtElF,OAAA;gBAAA6E,QAAA,eACD7E,OAAA,CAACT,SAAS;kBACT6F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAACU,WAAY;kBAC7CyD,SAAS;kBACTxB,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,aAAa,EAAE,GAAG,CAAC;sBACpC/B,mBAAmB,CAAC,KAAK,CAAC;sBAC1B;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACgD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCjD,mBAAmB,CAAC,IAAI,CAAC;oBAC1B,CAAC,MAAM;sBACNA,mBAAmB,CAAC,KAAK,CAAC;oBAC3B;oBAEA+B,gBAAgB,CAAC,aAAa,EAAEH,KAAK,CAAC;kBACvC,CAAE;kBACFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAExD;gBAAiB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACL5C,gBAAgB,iBAChBtC,OAAA,CAACV,UAAU;cACVyG,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF7E,OAAA;gBAAM+F,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7G;gBAAQ;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA1E,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAEclF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/C7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,eAAe;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvElF,OAAA;gBAAA6E,QAAA,eACD7E,OAAA;kBACC4G,IAAI,EAAC,OAAO;kBACZzC,KAAK,EAAE3B,yBAAyB,CAACO,YAAa;kBAC9CuC,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,cAAc,EAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBAChES,SAAS,EAAC,mBAAmB;kBAC7BmB,KAAK,EAAE;oBAACjF,eAAe,EAAC;kBAAS;gBAAE;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eAGzBlF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,YAAY;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpElF,OAAA;gBAAA6E,QAAA,eACD7E,OAAA;kBACC4G,IAAI,EAAC,OAAO;kBACZzC,KAAK,EAAE3B,yBAAyB,CAAC1B,eAAgB;kBACjDwE,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,iBAAiB,EAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBACrES,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eAGzBlF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,QAAQ;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChElF,OAAA;gBAAA6E,QAAA,eACD7E,OAAA;kBACC4G,IAAI,EAAC,OAAO;kBACZzC,KAAK,EAAE3B,yBAAyB,CAAC9B,WAAY;kBAC7C4E,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,aAAa,EAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBACjES,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAELlF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,iBAAiB;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzElF,OAAA;gBAAA6E,QAAA,eACC7E,OAAA;kBAAO4E,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACR7E,OAAA;oBACI4G,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAErE,yBAAyB,CAACQ,aAAc;oBAC5EsC,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,eAAe,EAAEL,CAAC,CAACC,MAAM,CAAC2C,OAAO,CAAE;oBAC1CC,IAAI,EAAC;kBAAe;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFlF,OAAA;oBAAM4E,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEYlF,OAAA,CAACX,GAAG;cAACuF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACnD7E,OAAA;gBAAK4E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAErE,SAAS,CAAC,uBAAuB;cAAC;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/ElF,OAAA;gBAAA6E,QAAA,eACC7E,OAAA;kBAAO4E,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACR7E,OAAA;oBACI4G,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAErE,yBAAyB,CAACS,mBAAoB;oBAClFqC,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,qBAAqB,EAAEL,CAAC,CAACC,MAAM,CAAC2C,OAAO,CAAE;oBAChDC,IAAI,EAAC;kBAAe;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFlF,OAAA;oBAAM4E,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACPlF,OAAA;UAAK4E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC7E,OAAA,CAACP,MAAM;YACN2F,OAAO,EAAC,WAAW;YACnBN,OAAO,EAAEJ,kBAAmB;YAC5BE,SAAS,EAAE,aAAazB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YACvD4D,QAAQ,EAAE5D,UAAW;YAAA0B,QAAA,EAErBrE,SAAS,CAAC,OAAO;UAAC;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEF;IACL;EAAA;AAEF,CAAC;AAAC5E,EAAA,CA1eIL,uBAAuB;EAAA,QACHH,cAAc,EAsBnCF,cAAc;AAAA;AAAAoH,EAAA,GAvBb/G,uBAAuB;AA4e7B,eAAeA,uBAAuB;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}