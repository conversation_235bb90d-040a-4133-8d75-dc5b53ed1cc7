{"ast": null, "code": "import React,{useState,useRef,useEffect,useContext}from'react';import'./ModernChatWindow.css';import{AccountContext}from'../../components/login/AccountContext';import{ai,airobot,upload,upload_hover,send}from\"../../assets/icons/icons\";import CloseIcon from'@mui/icons-material/Close';// Import services\nimport{CreateInteraction}from'../../services/AIService';import{startSpeechRecognition,stopSpeechRecognition,isSpeechRecognitionSupported}from'../../services/SpeechRecognitionService';import useDrawerStore from'../../store/drawerStore';import'react-perfect-scrollbar/dist/css/styles.css';import{IsOpenAIKeyEnabledForAccount}from'../../services/GuideListServices';import{useSnackbar}from'../guideSetting/guideList/SnackbarContext';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";// Chat message type\nconst ModernChatWindow=_ref=>{let{onClose,updatedGuideData,setStepData,setIsPopupOpen,setCurrentGuideId,setIsLoggedIn,setIsTourPopupOpen,setIsDrawerClosed,setShowBannerenduser,setIsAIChatOpen}=_ref;const{t:translate}=useTranslation();// Extract all needed functions from the drawer store\nconst{setSelectedTemplate,setSelectedTemplateTour,setSteps,SetGuideName,setBannerPopup,setCurrentStep,TooltipGuideDetails,HotspotGuideDetails,setTooltipCount,tooltipCount,setElementSelected,setSelectedStepTypeHotspot,setCreateWithAI,setIsAIGuidePersisted,setInteractionData,generateSteps,setBannerButtonSelected,syncAITooltipDataForPreview,syncAIAnnouncementDataForPreview,setIsUnSavedChanges,setOpenWarning}=useDrawerStore(state=>state);const[isChatOpen,setIsChatOpen]=useState(false);// New state for chat visibility\nconst{accountId}=useContext(AccountContext);const[inputText,setInputText]=useState('');const[messages,setMessages]=useState([{text:translate(\"Hello There! Need an banner, tour, or anything else to engage your users? Let me know, and I’ll get it done fast!\"),isUser:false,timestamp:new Date()}]);const[isLoading,setIsLoading]=useState(false);// State for UI and error handling\nconst[error,setError]=useState(null);const[isMicHovered,setIsMicHovered]=useState(false);const[isUploadHovered,setIsUploadHovered]=useState(false);const[showScrollButton,setShowScrollButton]=useState(false);const[isListening,setIsListening]=useState(false);const[speechSupported,setSpeechSupported]=useState(false);const messagesEndRef=useRef(null);const inputRef=useRef(null);const chatContainerRef=useRef(null);const[isOpenAIKeyProvided,setIsOpenAIKeyProvided]=useState(true);const{openSnackbar}=useSnackbar();useEffect(()=>{if(chatContainerRef.current){// Scroll to the bottom of the chat container\nchatContainerRef.current.scrollTop=chatContainerRef.current.scrollHeight;}setShowScrollButton(false);},[messages,isLoading]);const handleScroll=()=>{if(chatContainerRef.current){const{scrollTop,scrollHeight,clientHeight}=chatContainerRef.current;// Show scroll button when not at the bottom\nconst isAtBottom=scrollHeight-scrollTop-clientHeight<20;setShowScrollButton(!isAtBottom);}};const scrollToBottom=()=>{if(chatContainerRef.current){chatContainerRef.current.scrollTop=chatContainerRef.current.scrollHeight;setShowScrollButton(false);}};// Focus input on component mount\nuseEffect(()=>{var _inputRef$current;(_inputRef$current=inputRef.current)===null||_inputRef$current===void 0?void 0:_inputRef$current.focus();IsOpenAIKeyEnabledForAccount(openSnackbar,accountId,setIsOpenAIKeyProvided);},[]);// Check if speech recognition is supported\nuseEffect(()=>{setSpeechSupported(isSpeechRecognitionSupported());},[]);const handleInputChange=e=>{setInputText(e.target.value);setError(null);// Auto-resize the textarea\nif(inputRef.current){inputRef.current.style.height='auto';// Reset height first\ninputRef.current.style.height=`${Math.min(inputRef.current.scrollHeight,80)}px`;// Grow to scrollHeight, max 100px\n}};useEffect(()=>{if(inputRef.current&&inputText===''){inputRef.current.style.height='45px';}},[inputText]);const handleKeyDown=e=>{if(e.key==='Enter'&&!e.shiftKey&&isOpenAIKeyProvided){e.preventDefault();handleCreateInteraction(inputText);}};// Check if browser is Microsoft Edge\nconst isEdgeBrowser=()=>{return navigator.userAgent.indexOf(\"Edg\")!==-1;};// Handle speech recognition\nconst handleSpeechRecognition=()=>{if(isListening){stopSpeechRecognition();setIsListening(false);return;}// Get the current text in the input field\nconst initialText=inputText.trim();// Show Edge-specific message if needed\nif(isEdgeBrowser()&&!speechSupported){setError(translate(\"Speech recognition may not work in this version of Edge. Try using Chrome for better results.\"));setTimeout(()=>setError(null),5000);}setIsListening(true);startSpeechRecognition({pauseDuration:5000,// 5 seconds pause before stopping\nonStart:()=>{setIsListening(true);setError(null);// Clear any previous errors\n},onResult:(text,isFinal)=>{// The text parameter now contains the full accumulated transcript\n// If there was already text in the input field, append the speech recognition result\n// Otherwise, just set the text directly\nif(initialText){setInputText(initialText+' '+text);}else{setInputText(text);}},onEnd:()=>{setIsListening(false);},onError:error=>{console.error(\"Speech recognition error:\",error);setIsListening(false);// Provide more helpful error messages\nif(isEdgeBrowser()){if(error==='not-allowed'){setError(translate(\"Microphone access denied. Please allow microphone access in your browser settings.\"));}else if(error==='network'){setError(translate(\"Network error occurred. Speech recognition might not work well in Edge. Try Chrome for better results.\"));}else{setError(`${translate(\"Speech recognition failed in Edge:\")} ${error}. ${translate(\"Try using Chrome for better results.\")}`);}}else{setError(`${translate(\"Speech recognition failed:\")} ${error}. ${translate(\"Please try again.\")}`);}// Clear error after 5 seconds\nsetTimeout(()=>setError(null),5000);}});};// Removed unused test function\n//   const handleSendMessage = async () => {\n//     if (!inputText.trim()) return;\n//     // Add user message to chat\n//     const userMessage: ChatMessage = {\n//       text: inputText,\n//       isUser: true,\n//       timestamp: new Date()\n//     };\n//     setMessages(prev => [...prev, userMessage]);\n//     setInputText('');\n//     setIsLoading(true);\n//     // Reset textarea height\n//     if (inputRef.current) {\n//       inputRef.current.style.height = 'auto';\n//     }\n//     try {\n//       // For testing purposes, if the message is \"test\", add multiple messages\n//       if (inputText.toLowerCase() === \"test\") {\n//         addTestMessages();\n//         return;\n//       }\n//       // Add user message to conversation history\n//       const updatedConversation = [...conversation, { role: 'user' as const, content: inputText }];\n//       setConversation(updatedConversation);\n//       // Call OpenAI API\n//       const response = await callOpenAI(updatedConversation);\n//       // Add AI response to chat\n//       const aiResponse: ChatMessage = {\n//         text: response,\n//         isUser: false,\n//         timestamp: new Date()\n//       };\n//       // Add AI response to conversation history\n//       setConversation([...updatedConversation, { role: 'assistant' as const, content: response }]);\n//       // Update UI with AI response\n//       setMessages(prev => [...prev, aiResponse]);\n//       setIsLoading(false);\n//     } catch (err) {\n//       console.error(\"Error creating interaction:\", err);\n//       setIsLoading(false);\n//       // Show error message in chat\n//       const errorMessage: ChatMessage = {\n//         text: \"Sorry, I encountered an error while processing your request.\",\n//         isUser: false,\n//         timestamp: new Date()\n//       };\n//       setMessages(prev => [...prev, errorMessage]);\n//     }\n//   };\n// Function to check if the user prompt matches any of the tour creation patterns\nconst isTourCreationPrompt=prompt=>{const tourPatterns=[/create\\s+a\\s+tour\\s+with\\s+steps\\s*:/i,/generate\\s+a\\s+tour\\s+with\\s*:/i,/create\\s+a\\s+user\\s+onboarding\\s+tour\\s+with/i,/make\\s+a\\s+training\\s+tour\\s+with/i,/create\\s+a\\s+tour/i,/generate\\s+a\\s+tour/i,/create\\s+tour\\s+with\\s+step/i,/create\\s+tour\\s+having\\s+step/i];return tourPatterns.some(pattern=>pattern.test(prompt));};// Function to parse the user prompt and determine the steps to include in the tour\nconst parseTourSteps=prompt=>{const steps=[];// Check for specific step patterns with stepType explicitly mentioned\n// First pattern: step1 as stepType having \"Announcement\"\nconst stepTypePattern1=/step\\s*(\\d+)\\s*(?:as|having)\\s*(?:stepType|step type)\\s*(?:as|having|with)?\\s*[\"']?([a-zA-Z]+)[\"']?/gi;// Second pattern: step1 as announcement\nconst stepTypePattern2=/step\\s*(\\d+)\\s*(?:as|having|with)\\s*[\"']?([a-zA-Z]+)[\"']?/gi;let match;// Try the first pattern\nwhile((match=stepTypePattern1.exec(prompt))!==null){const stepNumber=parseInt(match[1]);let stepType=match[2].trim();// Normalize step type (handle misspellings like \"Annoucement\")\nif(stepType.toLowerCase().includes(\"announ\")||stepType.toLowerCase().includes(\"annoucement\")){stepType=\"Announcement\";}else if(stepType.toLowerCase().includes(\"bann\")){stepType=\"Banner\";}else if(stepType.toLowerCase().includes(\"tool\")){stepType=\"Tooltip\";}else if(stepType.toLowerCase().includes(\"hot\")){stepType=\"Hotspot\";}else{// Default to Announcement if type is not recognized\nstepType=\"Announcement\";}// Ensure first letter is capitalized\nstepType=stepType.charAt(0).toUpperCase()+stepType.slice(1);steps.push({stepType:stepType,title:`Step ${stepNumber}`,content:`<p>step ${stepNumber}</p>`});}// If no matches found with the first pattern, try the second pattern\nif(steps.length===0){while((match=stepTypePattern2.exec(prompt))!==null){const stepNumber=parseInt(match[1]);let stepType=match[2].trim();// Normalize step type (handle misspellings like \"Annoucement\")\nif(stepType.toLowerCase().includes(\"announ\")||stepType.toLowerCase().includes(\"annoucement\")){stepType=\"Announcement\";}else if(stepType.toLowerCase().includes(\"bann\")){stepType=\"Banner\";}else if(stepType.toLowerCase().includes(\"tool\")){stepType=\"Tooltip\";}else if(stepType.toLowerCase().includes(\"hot\")){stepType=\"Hotspot\";}else{// Default to Announcement if type is not recognized\nstepType=\"Announcement\";}// Ensure first letter is capitalized\nstepType=stepType.charAt(0).toUpperCase()+stepType.slice(1);steps.push({stepType:stepType,title:`Step ${stepNumber}`,content:`<p>step ${stepNumber}</p>`});}}// Check for specific step patterns like \"1. Announcement titled 'Welcome!'\"\nif(steps.length===0){const numberedStepPattern=/(\\d+)\\s*\\.\\s*(announcement|banner|tooltip|hotspot)\\s*(?:titled|saying)?\\s*['\"]?([^'\"]+)['\"]?/gi;while((match=numberedStepPattern.exec(prompt))!==null){steps.push({stepType:match[2].charAt(0).toUpperCase()+match[2].slice(1),title:match[3].trim(),content:`<p>${match[3].trim()}</p>`});}}// Check for button properties and text content\nconst buttonPattern=/step\\s*(\\d+).*?button\\s*(?:as|having)\\s*buttonName\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']\\s*and\\s*button\\s*actions\\s*as\\s*([a-zA-Z]+)\\s*and\\s*actionvalue\\s*as\\s*[\"']?([^\"']+)[\"']?/gi;let buttonMatch;while((buttonMatch=buttonPattern.exec(prompt))!==null){const stepNumber=parseInt(buttonMatch[1]);const buttonName=buttonMatch[2].trim();const buttonAction=buttonMatch[3].trim().toLowerCase();const buttonActionValue=buttonMatch[4].trim();// Find the step with this number\nconst stepIndex=steps.findIndex(step=>{const stepTitle=step.title||'';return stepTitle.includes(`${stepNumber}`);});if(stepIndex!==-1){// Update existing step with button properties\nsteps[stepIndex].buttonName=buttonName;steps[stepIndex].buttonAction=buttonAction;steps[stepIndex].buttonActionValue=buttonActionValue;}}// Check for text content\nconst textPattern=/step\\s*(\\d+).*?(?:with|having)\\s*text\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']/gi;let textMatch;while((textMatch=textPattern.exec(prompt))!==null){const stepNumber=parseInt(textMatch[1]);const textContent=textMatch[2].trim();// Find the step with this number\nconst stepIndex=steps.findIndex(step=>{const stepTitle=step.title||'';return stepTitle.includes(`${stepNumber}`);});if(stepIndex!==-1){// Update existing step with text content\nsteps[stepIndex].content=`<p>${textContent}</p>`;}}// If no specific steps found, check for general patterns\nif(steps.length===0){// Check for \"Announcement, Banner, Announcement\" pattern\nconst stepTypesPattern=/(announcement|banner|tooltip|hotspot)(?:\\s*,\\s*|\\s+and\\s+|\\s+)/gi;const stepTypes=[];while((match=stepTypesPattern.exec(prompt))!==null){stepTypes.push(match[1].charAt(0).toUpperCase()+match[1].slice(1));}if(stepTypes.length>0){stepTypes.forEach((type,index)=>{steps.push({stepType:type,title:`Step ${index+1}`,content:`<p>step ${index+1}</p>`});});}else{// Default to 3 steps: Announcement, Banner, Announcement\nsteps.push({stepType:\"Announcement\",title:\"Step 1\",content:\"<p>step1</p>\"},{stepType:\"Banner\",title:\"Step 2\",content:\"<p>step 2 banner</p>\"},{stepType:\"Announcement\",title:\"Step 3\",content:\"<p>step 3</p>\"});}}// Sort steps by step number if they have numeric titles\nsteps.sort((a,b)=>{const aNum=a.title?parseInt(a.title.replace(/\\D/g,'')):0;const bNum=b.title?parseInt(b.title.replace(/\\D/g,'')):0;return aNum-bNum;});return steps;};const[dataNew,setDataNew]=useState();const[stepDataNew,setStepDataNew]=useState();useEffect(()=>{setElementSelected(false);},[]);const handleCreateInteraction=async value=>{setIsUnSavedChanges(true);setError(null);setCreateWithAI(true);setIsAIGuidePersisted(false);if(value.length<10){setError(translate(\"Text must be at least 10 characters long\"));}else{// Add user message to chat\nconst userMessage={text:value,isUser:true,timestamp:new Date()};setMessages(prev=>[...prev,userMessage]);setInputText('');setIsLoading(true);try{var _data$GuideStep$,_data$GuideStep$2,_data$GuideStep$2$But;// Check if the prompt is for creating a tour\nconst aiResponse={text:translate(\"Creating your interaction...\"),isUser:false,timestamp:new Date()};setMessages(prev=>[...prev,aiResponse]);const data=await CreateInteraction(value,accountId,window.location.href);setCurrentGuideId(data===null||data===void 0?void 0:data.GuideId);data.TargetUrl=window.location.href;if((data===null||data===void 0?void 0:(_data$GuideStep$=data.GuideStep[0])===null||_data$GuideStep$===void 0?void 0:_data$GuideStep$.StepType)===\"Banner\"&&(data===null||data===void 0?void 0:(_data$GuideStep$2=data.GuideStep[0])===null||_data$GuideStep$2===void 0?void 0:(_data$GuideStep$2$But=_data$GuideStep$2.ButtonSection)===null||_data$GuideStep$2$But===void 0?void 0:_data$GuideStep$2$But.length)>0){setBannerButtonSelected(true);}setInteractionData(data);setOpenWarning(false);// Reset openWarning when starting new AI creation\nupdatedGuideData=data;generateSteps((data===null||data===void 0?void 0:data.GuideStep)||[]);setInputText(\"\");if(onClose)onClose();if(data){setDataNew(data);if((data===null||data===void 0?void 0:data.GuideType.toLowerCase())===\"announcement\"){setIsPopupOpen(true);setCurrentGuideId(data===null||data===void 0?void 0:data.GuideId);setSelectedTemplate(\"Announcement\");setBannerPopup(false);SetGuideName(data===null||data===void 0?void 0:data.Name);if(setStepData)setStepData({type:\"Announcement\"});TooltipGuideDetails();// Synchronize AI announcement data with announcementGuideMetaData\nsetTimeout(()=>{syncAIAnnouncementDataForPreview(false);// Allow setting global state during initial setup\n},100);}else if((data===null||data===void 0?void 0:data.GuideType.toLowerCase())===\"banner\"){setSelectedTemplate(\"Banner\",true);setBannerPopup(true);setIsPopupOpen(false);setCurrentGuideId(data===null||data===void 0?void 0:data.GuideId);SetGuideName(data===null||data===void 0?void 0:data.Name);if(setStepData)setStepData({type:\"Banner\"});// Ensure the builder screen is displayed\nTooltipGuideDetails();}else if((data===null||data===void 0?void 0:data.GuideType.toLowerCase())===\"hotspot\"){setElementSelected(false);setSelectedTemplate(\"Hotspot\",true);setIsPopupOpen(false);SetGuideName(data===null||data===void 0?void 0:data.Name);setIsLoggedIn(true);setIsTourPopupOpen(false);setIsDrawerClosed(true);setShowBannerenduser(false);if(setStepData)setStepData({type:\"Hotspot\"});// Ensure the builder screen is displayed\nHotspotGuideDetails();}else if((data===null||data===void 0?void 0:data.GuideType.toLowerCase())===\"tooltip\"){setElementSelected(false);setSelectedTemplate(\"Tooltip\",true);setIsPopupOpen(false);SetGuideName(data===null||data===void 0?void 0:data.Name);setIsLoggedIn(true);setIsTourPopupOpen(false);setIsDrawerClosed(true);setShowBannerenduser(false);if(setStepData)setStepData({type:\"Tooltip\"});// Ensure the builder screen is displayed\nTooltipGuideDetails();setTooltipCount(tooltipCount+1);}else if((data===null||data===void 0?void 0:data.GuideType.toLowerCase())===\"tour\"){setSelectedTemplate(\"Tour\",true);setCurrentGuideId(data===null||data===void 0?void 0:data.GuideId);SetGuideName(data===null||data===void 0?void 0:data.Name);setIsPopupOpen(true);setBannerPopup(false);setIsLoggedIn(true);setIsTourPopupOpen(true);setIsDrawerClosed(false);setShowBannerenduser(false);// Generate steps for the drawer\nif(data!==null&&data!==void 0&&data.GuideStep&&data.GuideStep.length>0){const stepsData=data.GuideStep.map((step,index)=>({id:step.StepId,name:step.StepTitle||`Step ${index+1}`,stepType:step.StepType,stepCount:index+1}));setSteps(stepsData);// Set the current step to the first step\nif(stepsData.length>0){setCurrentStep(1);// Handle different step types based on the first step\nconst firstStep=data.GuideStep[0];if(firstStep&&firstStep.StepType){const stepType=firstStep.StepType;setStepDataNew(stepType);// Set the selected template tour to the first step's type\nsetSelectedTemplateTour(stepType);// Handle specific step types\nif(stepType.toLowerCase()===\"announcement\"){TooltipGuideDetails();setSelectedTemplateTour(\"Announcement\");setSelectedTemplate(\"Tour\",true);// Synchronize AI announcement data with announcementGuideMetaData\nsetTimeout(()=>{syncAIAnnouncementDataForPreview(false);// Allow setting global state during initial setup\n},100);if(setStepData)setStepData({type:\"Announcement\"});}else if(stepType.toLowerCase()===\"banner\"){TooltipGuideDetails();setSelectedTemplate(\"Tour\",true);setSelectedTemplateTour(\"Banner\");if(setStepData)setStepData({type:\"Banner\"});setBannerPopup(true);}else if(stepType.toLowerCase()===\"tooltip\"){setElementSelected(false);TooltipGuideDetails();setSelectedTemplateTour(\"Tooltip\");setSelectedTemplate(\"Tour\",true);// Synchronize AI tooltip data with tooltipguidemetadata\nsetTimeout(()=>{syncAITooltipDataForPreview();},100);if(setStepData)setStepData({type:\"Tooltip\"});setTooltipCount(tooltipCount+1);}else if(stepType.toLowerCase()===\"hotspot\"){setElementSelected(false);HotspotGuideDetails();setSelectedTemplateTour(\"Hotspot\");setSelectedTemplate(\"Tour\",true);setSelectedStepTypeHotspot(true);if(setStepData)setStepData({type:\"Hotspot\"});setTooltipCount(tooltipCount+1);}}}}}else if((data===null||data===void 0?void 0:data.GuideType.toLowerCase())===\"checklist\"){// Handle checklist type if needed\nsetSelectedTemplate(\"Checklist\",true);setCurrentGuideId(data===null||data===void 0?void 0:data.GuideId);SetGuideName(data===null||data===void 0?void 0:data.Name);if(setStepData)setStepData({type:\"Checklist\"});}}}catch(error){console.error(\"Error creating interaction:\",error);const errorMessage={text:translate(\"Sorry, I encountered an error while processing your request.\"),isUser:false,timestamp:new Date()};setMessages(prev=>[...prev,errorMessage]);}finally{setIsLoading(false);setError(null);}}};return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-chat-window\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-chat-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-chat-title\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-chat-icon\",dangerouslySetInnerHTML:{__html:ai}}),/*#__PURE__*/_jsx(\"span\",{children:translate(\"AI Chatbot\")})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"qadpt-close-btn\",onClick:onClose,\"aria-label\":translate(\"Close\"),children:[\" \",/*#__PURE__*/_jsx(CloseIcon,{fontSize:\"small\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-chat-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-messages\",ref:chatContainerRef,onScroll:handleScroll,children:[messages.map((message,index)=>/*#__PURE__*/_jsxs(\"div\",{className:`qadpt-message ${message.isUser?'user-message':'ai-message'}`,children:[!message.isUser&&/*#__PURE__*/_jsx(\"div\",{className:\"ai-avatar\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:airobot}})}),/*#__PURE__*/_jsx(\"div\",{className:\"message-content\",children:/*#__PURE__*/_jsx(\"p\",{children:message.text})})]},index)),isLoading&&/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-chat-message ai-message\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"ai-avatar\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:airobot}})}),/*#__PURE__*/_jsx(\"div\",{className:\"message-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"typing-indicator\",children:[/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"span\",{})]})})]}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef}),showScrollButton&&/*#__PURE__*/_jsxs(\"button\",{className:\"scroll-to-bottom\",onClick:scrollToBottom,\"aria-label\":translate(\"Scroll to bottom\"),children:[\" \",/*#__PURE__*/_jsx(\"svg\",{width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M12 16L6 10L7.4 8.6L12 13.2L16.6 8.6L18 10L12 16Z\",fill:\"currentColor\"})})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-input\",children:[error&&/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:translate(error)}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-with-icons\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-txtcont\",children:/*#__PURE__*/_jsx(\"textarea\",{ref:inputRef,value:inputText,onChange:handleInputChange,onKeyDown:handleKeyDown,placeholder:isOpenAIKeyProvided?translate(\"Enter your prompt....\"):translate(\"Please Add OpenAI Key At Setting -> Agents\"),disabled:isLoading,rows:1})}),/*#__PURE__*/_jsxs(\"div\",{className:\"input-icons\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn upload-btn\",onMouseEnter:()=>setIsUploadHovered(true),onMouseLeave:()=>setIsUploadHovered(false),\"aria-label\":translate(\"Upload\"),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:isUploadHovered?upload_hover:upload}})})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-btn send-btn\",onClick:()=>handleCreateInteraction(inputText),disabled:!isOpenAIKeyProvided||isLoading||!inputText.trim(),\"aria-label\":translate(\"Send\"),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:send}})})]})]})]})]})});};export default ModernChatWindow;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useContext", "AccountContext", "ai", "airobot", "upload", "upload_hover", "send", "CloseIcon", "CreateInteraction", "startSpeechRecognition", "stopSpeechRecognition", "isSpeechRecognitionSupported", "useDrawerStore", "IsOpenAIKeyEnabledForAccount", "useSnackbar", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ModernChatWindow", "_ref", "onClose", "updatedGuideData", "setStepData", "setIsPopupOpen", "setCurrentGuideId", "setIsLoggedIn", "setIsTourPopupOpen", "setIsDrawerClosed", "setShowBannerenduser", "setIsAIChatOpen", "t", "translate", "setSelectedTemplate", "setSelectedTemplateTour", "setSteps", "SetGuideName", "setBannerPopup", "setCurrentStep", "TooltipGuideDetails", "HotspotGuideDetails", "setTooltipCount", "tooltipCount", "setElementSelected", "setSelectedStepTypeHotspot", "setCreateWithAI", "setIsAIGuidePersisted", "setInteractionData", "generateSteps", "setBannerButtonSelected", "syncAITooltipDataForPreview", "syncAIAnnouncementDataForPreview", "setIsUnSavedChanges", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "isChatOpen", "setIsChatOpen", "accountId", "inputText", "setInputText", "messages", "setMessages", "text", "isUser", "timestamp", "Date", "isLoading", "setIsLoading", "error", "setError", "isMicHovered", "setIsMicHovered", "isUploadHovered", "setIsUploadHovered", "showScrollButton", "setShowScrollButton", "isListening", "setIsListening", "speechSupported", "setSpeechSupported", "messagesEndRef", "inputRef", "chatContainerRef", "isOpenAIKeyProvided", "setIsOpenAIKeyProvided", "openSnackbar", "current", "scrollTop", "scrollHeight", "handleScroll", "clientHeight", "isAtBottom", "scrollToBottom", "_inputRef$current", "focus", "handleInputChange", "e", "target", "value", "style", "height", "Math", "min", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "handleCreateInteraction", "isEdge<PERSON><PERSON>er", "navigator", "userAgent", "indexOf", "handleSpeechRecognition", "initialText", "trim", "setTimeout", "pauseDuration", "onStart", "onResult", "isFinal", "onEnd", "onError", "console", "isTourCreationPrompt", "prompt", "tourPatterns", "some", "pattern", "test", "parseTourSteps", "steps", "stepTypePattern1", "stepTypePattern2", "match", "exec", "<PERSON><PERSON><PERSON><PERSON>", "parseInt", "stepType", "toLowerCase", "includes", "char<PERSON>t", "toUpperCase", "slice", "push", "title", "content", "length", "numberedStepPattern", "buttonPattern", "buttonMatch", "buttonName", "buttonAction", "buttonActionValue", "stepIndex", "findIndex", "step", "step<PERSON>itle", "textPattern", "textMatch", "textContent", "stepTypesPattern", "stepTypes", "for<PERSON>ach", "type", "index", "sort", "a", "b", "aNum", "replace", "bNum", "dataNew", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setStepDataNew", "userMessage", "prev", "_data$GuideStep$", "_data$GuideStep$2", "_data$GuideStep$2$But", "aiResponse", "data", "window", "location", "href", "GuideId", "TargetUrl", "GuideStep", "StepType", "ButtonSection", "GuideType", "Name", "stepsData", "map", "id", "StepId", "name", "<PERSON><PERSON><PERSON><PERSON>", "stepCount", "firstStep", "errorMessage", "children", "className", "dangerouslySetInnerHTML", "__html", "onClick", "fontSize", "ref", "onScroll", "message", "width", "viewBox", "fill", "xmlns", "d", "onChange", "onKeyDown", "placeholder", "disabled", "rows", "onMouseEnter", "onMouseLeave"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/AIAgent/ModernChatWindow.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useContext } from 'react';\r\nimport './ModernChatWindow.css';\r\nimport { AccountContext } from '../../components/login/AccountContext';\r\nimport { ai, airobot, micicon, micicon_hover, upload, upload_hover, send } from \"../../assets/icons/icons\";\r\nimport CloseIcon from '@mui/icons-material/Close';\r\n// Import services\r\nimport { CreateInteraction } from '../../services/AIService';\r\nimport { startSpeechRecognition, stopSpeechRecognition, isSpeechRecognitionSupported } from '../../services/SpeechRecognitionService';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\nimport { IsOpenAIKeyEnabledForAccount } from '../../services/GuideListServices';\r\nimport { useSnackbar } from '../guideSetting/guideList/SnackbarContext';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n\r\n\r\ninterface ModernChatWindowProps {\r\n    onClose: () => void;\r\n    setIsPopupOpen: any;\r\n    setCurrentGuideId: any;\r\n    setIsLoggedIn: any;\r\n    setIsTourPopupOpen: any;\r\n    setIsDrawerClosed: any;\r\n    setShowBannerenduser: any;\r\n  setIsAIChatOpen?: any; // Make this optional\r\n  setStepData?: any; // Optional function to set step data\r\n  updatedGuideData?: any;\r\n}\r\n\r\ninterface ChatMessage {\r\n  text: string;\r\n  isUser: boolean;\r\n  timestamp: Date;\r\n}\r\n\r\n// Chat message type\r\n\r\nconst ModernChatWindow: React.FC<ModernChatWindowProps> = ({ onClose,updatedGuideData, setStepData, setIsPopupOpen, setCurrentGuideId, setIsLoggedIn, setIsTourPopupOpen, setIsDrawerClosed, setShowBannerenduser, setIsAIChatOpen }) => {\r\n  const { t: translate } = useTranslation();\r\n    // Extract all needed functions from the drawer store\r\n    const {\r\n\t\tsetSelectedTemplate,\r\n\t\tsetSelectedTemplateTour,\r\n\t\tsetSteps,\r\n\t\tSetGuideName,\r\n\t\tsetBannerPopup,\r\n\t\tsetCurrentStep,\r\n\t\tTooltipGuideDetails,\r\n\t\tHotspotGuideDetails,\r\n\t\tsetTooltipCount,\r\n\t\ttooltipCount,\r\n\t\tsetElementSelected,\r\n      setSelectedStepTypeHotspot,\r\n      setCreateWithAI,\r\n      setIsAIGuidePersisted,\r\n      setInteractionData,\r\n      generateSteps,\r\n      setBannerButtonSelected,\r\n      syncAITooltipDataForPreview,\r\n      syncAIAnnouncementDataForPreview,\r\n      setIsUnSavedChanges,\r\n      setOpenWarning\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n    const [isChatOpen, setIsChatOpen] = useState(false);  // New state for chat visibility\r\n    const { accountId } = useContext(AccountContext);\r\n    const [inputText, setInputText] = useState('');\r\n    const [messages, setMessages] = useState<ChatMessage[]>([\r\n        {\r\n        text: translate(\"Hello There! Need an banner, tour, or anything else to engage your users? Let me know, and I’ll get it done fast!\"),\r\n        isUser: false,\r\n        timestamp: new Date()\r\n        }\r\n    ]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  // State for UI and error handling\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isMicHovered, setIsMicHovered] = useState(false);\r\n  const [isUploadHovered, setIsUploadHovered] = useState(false);\r\n  const [showScrollButton, setShowScrollButton] = useState(false);\r\n  const [isListening, setIsListening] = useState(false);\r\n  const [speechSupported, setSpeechSupported] = useState(false);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLTextAreaElement>(null);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n  const [isOpenAIKeyProvided, setIsOpenAIKeyProvided] = useState(true);\r\n  const { openSnackbar } = useSnackbar();\r\n\r\n  useEffect(() => {\r\n    if (chatContainerRef.current) {\r\n      // Scroll to the bottom of the chat container\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n    }\r\n\r\n    setShowScrollButton(false);\r\n  }, [messages, isLoading]);\r\n\r\n\r\n  const handleScroll = () => {\r\n    if (chatContainerRef.current) {\r\n      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;\r\n\r\n      // Show scroll button when not at the bottom\r\n      const isAtBottom = scrollHeight - scrollTop - clientHeight < 20;\r\n      setShowScrollButton(!isAtBottom);\r\n    }\r\n  };\r\n\r\n  const scrollToBottom = () => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\r\n      setShowScrollButton(false);\r\n    }\r\n  };\r\n\r\n  // Focus input on component mount\r\n  useEffect(() => {\r\n    inputRef.current?.focus();\r\n    IsOpenAIKeyEnabledForAccount(openSnackbar,accountId,setIsOpenAIKeyProvided);\r\n  }, []);\r\n\r\n  // Check if speech recognition is supported\r\n  useEffect(() => {\r\n    setSpeechSupported(isSpeechRecognitionSupported());\r\n  }, []);\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setInputText(e.target.value);\r\n    setError(null);\r\n\r\n    // Auto-resize the textarea\r\n    if (inputRef.current) {\r\n      inputRef.current.style.height = 'auto'; // Reset height first\r\n      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 80)}px`; // Grow to scrollHeight, max 100px\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    if (inputRef.current && inputText === '') {\r\n      inputRef.current.style.height = '45px';\r\n    }\r\n  }, [inputText]);\r\n\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey && isOpenAIKeyProvided) {\r\n      e.preventDefault();\r\n      handleCreateInteraction(inputText);\r\n    }\r\n  };\r\n\r\n  // Check if browser is Microsoft Edge\r\n  const isEdgeBrowser = (): boolean => {\r\n    return navigator.userAgent.indexOf(\"Edg\") !== -1;\r\n  };\r\n\r\n  // Handle speech recognition\r\n  const handleSpeechRecognition = () => {\r\n    if (isListening) {\r\n      stopSpeechRecognition();\r\n      setIsListening(false);\r\n      return;\r\n    }\r\n\r\n    // Get the current text in the input field\r\n    const initialText = inputText.trim();\r\n\r\n    // Show Edge-specific message if needed\r\n    if (isEdgeBrowser() && !speechSupported) {\r\n      setError(translate(\"Speech recognition may not work in this version of Edge. Try using Chrome for better results.\"));\r\n      setTimeout(() => setError(null), 5000);\r\n    }\r\n\r\n    setIsListening(true);\r\n    startSpeechRecognition({\r\n      pauseDuration: 5000, // 5 seconds pause before stopping\r\n      onStart: () => {\r\n        setIsListening(true);\r\n        setError(null); // Clear any previous errors\r\n      },\r\n      onResult: (text: string, isFinal: boolean) => {\r\n        // The text parameter now contains the full accumulated transcript\r\n        // If there was already text in the input field, append the speech recognition result\r\n        // Otherwise, just set the text directly\r\n        if (initialText) {\r\n          setInputText(initialText + ' ' + text);\r\n        } else {\r\n          setInputText(text);\r\n        }\r\n      },\r\n      onEnd: () => {\r\n        setIsListening(false);\r\n      },\r\n      onError: (error: any) => {\r\n        console.error(\"Speech recognition error:\", error);\r\n        setIsListening(false);\r\n\r\n        // Provide more helpful error messages\r\n        if (isEdgeBrowser()) {\r\n          if (error === 'not-allowed') {\r\n            setError(translate(\"Microphone access denied. Please allow microphone access in your browser settings.\"));\r\n          } else if (error === 'network') {\r\n            setError(translate(\"Network error occurred. Speech recognition might not work well in Edge. Try Chrome for better results.\"));\r\n          } else {\r\n            setError(`${translate(\"Speech recognition failed in Edge:\")} ${error}. ${translate(\"Try using Chrome for better results.\")}`);\r\n          }\r\n        } else {\r\n          setError(`${translate(\"Speech recognition failed:\")} ${error}. ${translate(\"Please try again.\")}`);\r\n        }\r\n\r\n        // Clear error after 5 seconds\r\n        setTimeout(() => setError(null), 5000);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Removed unused test function\r\n\r\n//   const handleSendMessage = async () => {\r\n//     if (!inputText.trim()) return;\r\n\r\n//     // Add user message to chat\r\n//     const userMessage: ChatMessage = {\r\n//       text: inputText,\r\n//       isUser: true,\r\n//       timestamp: new Date()\r\n//     };\r\n\r\n//     setMessages(prev => [...prev, userMessage]);\r\n//     setInputText('');\r\n//     setIsLoading(true);\r\n\r\n//     // Reset textarea height\r\n//     if (inputRef.current) {\r\n//       inputRef.current.style.height = 'auto';\r\n//     }\r\n\r\n//     try {\r\n//       // For testing purposes, if the message is \"test\", add multiple messages\r\n//       if (inputText.toLowerCase() === \"test\") {\r\n//         addTestMessages();\r\n//         return;\r\n//       }\r\n\r\n//       // Add user message to conversation history\r\n//       const updatedConversation = [...conversation, { role: 'user' as const, content: inputText }];\r\n//       setConversation(updatedConversation);\r\n\r\n//       // Call OpenAI API\r\n//       const response = await callOpenAI(updatedConversation);\r\n\r\n//       // Add AI response to chat\r\n//       const aiResponse: ChatMessage = {\r\n//         text: response,\r\n//         isUser: false,\r\n//         timestamp: new Date()\r\n//       };\r\n\r\n//       // Add AI response to conversation history\r\n//       setConversation([...updatedConversation, { role: 'assistant' as const, content: response }]);\r\n\r\n//       // Update UI with AI response\r\n//       setMessages(prev => [...prev, aiResponse]);\r\n//       setIsLoading(false);\r\n\r\n//     } catch (err) {\r\n//       console.error(\"Error creating interaction:\", err);\r\n//       setIsLoading(false);\r\n\r\n//       // Show error message in chat\r\n//       const errorMessage: ChatMessage = {\r\n//         text: \"Sorry, I encountered an error while processing your request.\",\r\n//         isUser: false,\r\n//         timestamp: new Date()\r\n//       };\r\n\r\n//       setMessages(prev => [...prev, errorMessage]);\r\n//     }\r\n//   };\r\n  // Function to check if the user prompt matches any of the tour creation patterns\r\n  const isTourCreationPrompt = (prompt: string): boolean => {\r\n    const tourPatterns = [\r\n      /create\\s+a\\s+tour\\s+with\\s+steps\\s*:/i,\r\n      /generate\\s+a\\s+tour\\s+with\\s*:/i,\r\n      /create\\s+a\\s+user\\s+onboarding\\s+tour\\s+with/i,\r\n      /make\\s+a\\s+training\\s+tour\\s+with/i,\r\n      /create\\s+a\\s+tour/i,\r\n      /generate\\s+a\\s+tour/i,\r\n      /create\\s+tour\\s+with\\s+step/i,\r\n      /create\\s+tour\\s+having\\s+step/i\r\n    ];\r\n\r\n    return tourPatterns.some(pattern => pattern.test(prompt));\r\n  };\r\n\r\n  // Function to parse the user prompt and determine the steps to include in the tour\r\n  const parseTourSteps = (prompt: string): { stepType: string, title?: string, content?: string, buttonName?: string, buttonAction?: string, buttonActionValue?: string }[] => {\r\n    const steps: { stepType: string, title?: string, content?: string, buttonName?: string, buttonAction?: string, buttonActionValue?: string }[] = [];\r\n\r\n    // Check for specific step patterns with stepType explicitly mentioned\r\n    // First pattern: step1 as stepType having \"Announcement\"\r\n    const stepTypePattern1 = /step\\s*(\\d+)\\s*(?:as|having)\\s*(?:stepType|step type)\\s*(?:as|having|with)?\\s*[\"']?([a-zA-Z]+)[\"']?/gi;\r\n    // Second pattern: step1 as announcement\r\n    const stepTypePattern2 = /step\\s*(\\d+)\\s*(?:as|having|with)\\s*[\"']?([a-zA-Z]+)[\"']?/gi;\r\n\r\n    let match;\r\n\r\n    // Try the first pattern\r\n    while ((match = stepTypePattern1.exec(prompt)) !== null) {\r\n      const stepNumber = parseInt(match[1]);\r\n      let stepType = match[2].trim();\r\n\r\n      // Normalize step type (handle misspellings like \"Annoucement\")\r\n      if (stepType.toLowerCase().includes(\"announ\") || stepType.toLowerCase().includes(\"annoucement\")) {\r\n        stepType = \"Announcement\";\r\n      } else if (stepType.toLowerCase().includes(\"bann\")) {\r\n        stepType = \"Banner\";\r\n      } else if (stepType.toLowerCase().includes(\"tool\")) {\r\n        stepType = \"Tooltip\";\r\n      } else if (stepType.toLowerCase().includes(\"hot\")) {\r\n        stepType = \"Hotspot\";\r\n      } else {\r\n        // Default to Announcement if type is not recognized\r\n        stepType = \"Announcement\";\r\n      }\r\n\r\n      // Ensure first letter is capitalized\r\n      stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);\r\n\r\n      steps.push({\r\n        stepType: stepType,\r\n        title: `Step ${stepNumber}`,\r\n        content: `<p>step ${stepNumber}</p>`\r\n      });\r\n    }\r\n\r\n    // If no matches found with the first pattern, try the second pattern\r\n    if (steps.length === 0) {\r\n      while ((match = stepTypePattern2.exec(prompt)) !== null) {\r\n        const stepNumber = parseInt(match[1]);\r\n        let stepType = match[2].trim();\r\n\r\n        // Normalize step type (handle misspellings like \"Annoucement\")\r\n        if (stepType.toLowerCase().includes(\"announ\") || stepType.toLowerCase().includes(\"annoucement\")) {\r\n          stepType = \"Announcement\";\r\n        } else if (stepType.toLowerCase().includes(\"bann\")) {\r\n          stepType = \"Banner\";\r\n        } else if (stepType.toLowerCase().includes(\"tool\")) {\r\n          stepType = \"Tooltip\";\r\n        } else if (stepType.toLowerCase().includes(\"hot\")) {\r\n          stepType = \"Hotspot\";\r\n        } else {\r\n          // Default to Announcement if type is not recognized\r\n          stepType = \"Announcement\";\r\n        }\r\n\r\n        // Ensure first letter is capitalized\r\n        stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);\r\n\r\n        steps.push({\r\n          stepType: stepType,\r\n          title: `Step ${stepNumber}`,\r\n          content: `<p>step ${stepNumber}</p>`\r\n        });\r\n      }\r\n    }\r\n\r\n    // Check for specific step patterns like \"1. Announcement titled 'Welcome!'\"\r\n    if (steps.length === 0) {\r\n      const numberedStepPattern = /(\\d+)\\s*\\.\\s*(announcement|banner|tooltip|hotspot)\\s*(?:titled|saying)?\\s*['\"]?([^'\"]+)['\"]?/gi;\r\n\r\n      while ((match = numberedStepPattern.exec(prompt)) !== null) {\r\n        steps.push({\r\n          stepType: match[2].charAt(0).toUpperCase() + match[2].slice(1),\r\n          title: match[3].trim(),\r\n          content: `<p>${match[3].trim()}</p>`\r\n        });\r\n      }\r\n    }\r\n\r\n    // Check for button properties and text content\r\n    const buttonPattern = /step\\s*(\\d+).*?button\\s*(?:as|having)\\s*buttonName\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']\\s*and\\s*button\\s*actions\\s*as\\s*([a-zA-Z]+)\\s*and\\s*actionvalue\\s*as\\s*[\"']?([^\"']+)[\"']?/gi;\r\n    let buttonMatch;\r\n    while ((buttonMatch = buttonPattern.exec(prompt)) !== null) {\r\n      const stepNumber = parseInt(buttonMatch[1]);\r\n      const buttonName = buttonMatch[2].trim();\r\n      const buttonAction = buttonMatch[3].trim().toLowerCase();\r\n      const buttonActionValue = buttonMatch[4].trim();\r\n\r\n      // Find the step with this number\r\n      const stepIndex = steps.findIndex(step => {\r\n        const stepTitle = step.title || '';\r\n        return stepTitle.includes(`${stepNumber}`);\r\n      });\r\n\r\n      if (stepIndex !== -1) {\r\n        // Update existing step with button properties\r\n        steps[stepIndex].buttonName = buttonName;\r\n        steps[stepIndex].buttonAction = buttonAction;\r\n        steps[stepIndex].buttonActionValue = buttonActionValue;\r\n      }\r\n    }\r\n\r\n    // Check for text content\r\n    const textPattern = /step\\s*(\\d+).*?(?:with|having)\\s*text\\s*(?:as|having|with)?\\s*[\"']([^\"']+)[\"']/gi;\r\n    let textMatch;\r\n    while ((textMatch = textPattern.exec(prompt)) !== null) {\r\n      const stepNumber = parseInt(textMatch[1]);\r\n      const textContent = textMatch[2].trim();\r\n\r\n      // Find the step with this number\r\n      const stepIndex = steps.findIndex(step => {\r\n        const stepTitle = step.title || '';\r\n        return stepTitle.includes(`${stepNumber}`);\r\n      });\r\n\r\n      if (stepIndex !== -1) {\r\n        // Update existing step with text content\r\n        steps[stepIndex].content = `<p>${textContent}</p>`;\r\n      }\r\n    }\r\n\r\n    // If no specific steps found, check for general patterns\r\n    if (steps.length === 0) {\r\n      // Check for \"Announcement, Banner, Announcement\" pattern\r\n      const stepTypesPattern = /(announcement|banner|tooltip|hotspot)(?:\\s*,\\s*|\\s+and\\s+|\\s+)/gi;\r\n      const stepTypes: string[] = [];\r\n\r\n      while ((match = stepTypesPattern.exec(prompt)) !== null) {\r\n        stepTypes.push(match[1].charAt(0).toUpperCase() + match[1].slice(1));\r\n      }\r\n\r\n      if (stepTypes.length > 0) {\r\n        stepTypes.forEach((type, index) => {\r\n          steps.push({\r\n            stepType: type,\r\n            title: `Step ${index + 1}`,\r\n            content: `<p>step ${index + 1}</p>`\r\n          });\r\n        });\r\n      } else {\r\n        // Default to 3 steps: Announcement, Banner, Announcement\r\n        steps.push(\r\n          { stepType: \"Announcement\", title: \"Step 1\", content: \"<p>step1</p>\" },\r\n          { stepType: \"Banner\", title: \"Step 2\", content: \"<p>step 2 banner</p>\" },\r\n          { stepType: \"Announcement\", title: \"Step 3\", content: \"<p>step 3</p>\" }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Sort steps by step number if they have numeric titles\r\n    steps.sort((a, b) => {\r\n      const aNum = a.title ? parseInt(a.title.replace(/\\D/g, '')) : 0;\r\n      const bNum = b.title ? parseInt(b.title.replace(/\\D/g, '')) : 0;\r\n      return aNum - bNum;\r\n    });\r\n\r\n    return steps;\r\n  };\r\n\r\n\r\n  const [dataNew, setDataNew] = useState<any>();\r\n  const[stepDataNew,setStepDataNew]=useState<any>();\r\n  useEffect(() =>\r\n  {\r\n      setElementSelected(false);\r\n\r\n},[])\r\n  const handleCreateInteraction = async (value: string) => {\r\n    setIsUnSavedChanges(true);\r\n    setError(null);\r\n    setCreateWithAI(true);\r\n    setIsAIGuidePersisted(false);\r\n    if (value.length < 10) {\r\n      setError(translate(\"Text must be at least 10 characters long\"));\r\n    } else {\r\n        // Add user message to chat\r\n        const userMessage: ChatMessage = {\r\n          text: value,\r\n          isUser: true,\r\n          timestamp: new Date()\r\n        };\r\n        setMessages(prev => [...prev, userMessage]);\r\n        setInputText('');\r\n        setIsLoading(true);\r\n        try {\r\n          // Check if the prompt is for creating a tour\r\n\r\n\r\n          const aiResponse: ChatMessage = {\r\n            text: translate(\"Creating your interaction...\"),\r\n            isUser: false,\r\n            timestamp: new Date()\r\n          };\r\n          setMessages(prev => [...prev, aiResponse]);\r\n\r\n          const data = await CreateInteraction(value, accountId, window.location.href);\r\n\r\n          setCurrentGuideId(data?.GuideId);\r\n          data.TargetUrl = window.location.href;\r\n          if(data?.GuideStep[0]?.StepType === \"Banner\" && data?.GuideStep[0]?.ButtonSection?.length>0){\r\n            setBannerButtonSelected(true);\r\n          }\r\n          setInteractionData(data);\r\n          setOpenWarning(false); // Reset openWarning when starting new AI creation\r\n          updatedGuideData = data;\r\n\r\n          generateSteps(data?.GuideStep || []);\r\n          setInputText(\"\");\r\n\r\n          if (onClose) onClose();\r\n\r\n          if (data) {\r\n            setDataNew(data);\r\n            if (data?.GuideType.toLowerCase() === \"announcement\") {\r\n              setIsPopupOpen(true);\r\n              setCurrentGuideId(data?.GuideId);\r\n              setSelectedTemplate(\"Announcement\");\r\n              setBannerPopup(false);\r\n              SetGuideName(data?.Name);\r\n              if (setStepData) setStepData({ type: \"Announcement\" });\r\n              TooltipGuideDetails();\r\n\r\n              // Synchronize AI announcement data with announcementGuideMetaData\r\n              setTimeout(() => {\r\n                syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup\r\n              }, 100);\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"banner\") {\r\n              setSelectedTemplate(\"Banner\", true);\r\n              setBannerPopup(true);\r\n              setIsPopupOpen(false);\r\n              setCurrentGuideId(data?.GuideId);\r\n              SetGuideName(data?.Name);\r\n              if (setStepData) setStepData({ type: \"Banner\" });\r\n              // Ensure the builder screen is displayed\r\n              TooltipGuideDetails();\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"hotspot\") {\r\n              setElementSelected(false);\r\n              setSelectedTemplate(\"Hotspot\", true);\r\n              setIsPopupOpen(false);\r\n              SetGuideName(data?.Name);\r\n              setIsLoggedIn(true);\r\n              setIsTourPopupOpen(false);\r\n              setIsDrawerClosed(true);\r\n              setShowBannerenduser(false);\r\n              if (setStepData) setStepData({ type: \"Hotspot\" });\r\n              // Ensure the builder screen is displayed\r\n              HotspotGuideDetails();\r\n\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"tooltip\") {\r\n              setElementSelected(false);\r\n              setSelectedTemplate(\"Tooltip\", true);\r\n              setIsPopupOpen(false);\r\n              SetGuideName(data?.Name);\r\n              setIsLoggedIn(true);\r\n              setIsTourPopupOpen(false);\r\n              setIsDrawerClosed(true);\r\n              setShowBannerenduser(false);\r\n              if (setStepData) setStepData({ type: \"Tooltip\" });\r\n              // Ensure the builder screen is displayed\r\n              TooltipGuideDetails();\r\n              setTooltipCount(tooltipCount + 1);\r\n\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"tour\") {\r\n              setSelectedTemplate(\"Tour\", true);\r\n              setCurrentGuideId(data?.GuideId);\r\n              SetGuideName(data?.Name);\r\n              setIsPopupOpen(true);\r\n              setBannerPopup(false);\r\n              setIsLoggedIn(true);\r\n              setIsTourPopupOpen(true);\r\n              setIsDrawerClosed(false);\r\n              setShowBannerenduser(false);\r\n\r\n              // Generate steps for the drawer\r\n              if (data?.GuideStep && data.GuideStep.length > 0) {\r\n                const stepsData = data.GuideStep.map((step: any, index: number) => ({\r\n                  id: step.StepId,\r\n                  name: step.StepTitle || `Step ${index + 1}`,\r\n                  stepType: step.StepType,\r\n                  stepCount: index + 1\r\n                }));\r\n                setSteps(stepsData);\r\n\r\n                // Set the current step to the first step\r\n                if (stepsData.length > 0) {\r\n                  setCurrentStep(1);\r\n\r\n                  // Handle different step types based on the first step\r\n                  const firstStep = data.GuideStep[0];\r\n                  if (firstStep && firstStep.StepType) {\r\n                    const stepType = firstStep.StepType;\r\n                    setStepDataNew(stepType);\r\n\r\n                    // Set the selected template tour to the first step's type\r\n                    setSelectedTemplateTour(stepType);\r\n\r\n                    // Handle specific step types\r\n                    if (stepType.toLowerCase() === \"announcement\") {\r\n                      TooltipGuideDetails();\r\n                      setSelectedTemplateTour(\"Announcement\");\r\n                      setSelectedTemplate(\"Tour\", true);\r\n\r\n                      // Synchronize AI announcement data with announcementGuideMetaData\r\n                      setTimeout(() => {\r\n                        syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup\r\n                      }, 100);\r\n\r\n                      if (setStepData) setStepData({ type: \"Announcement\" });\r\n                    }\r\n                    else if (stepType.toLowerCase() === \"banner\") {\r\n                      TooltipGuideDetails();\r\n                      setSelectedTemplate(\"Tour\", true);\r\n                      setSelectedTemplateTour(\"Banner\");\r\n                      if (setStepData) setStepData({ type: \"Banner\" });\r\n                      setBannerPopup(true);\r\n                    }\r\n                    else if (stepType.toLowerCase() === \"tooltip\") {\r\n                      setElementSelected(false);\r\n\r\n                      TooltipGuideDetails();\r\n                      setSelectedTemplateTour(\"Tooltip\");\r\n                      setSelectedTemplate(\"Tour\", true);\r\n\r\n                      // Synchronize AI tooltip data with tooltipguidemetadata\r\n                      setTimeout(() => {\r\n                        syncAITooltipDataForPreview();\r\n                      }, 100);\r\n\r\n                      if (setStepData) setStepData({ type: \"Tooltip\" });\r\n                      setTooltipCount(tooltipCount + 1);\r\n                    }\r\n                    else if (stepType.toLowerCase() === \"hotspot\") {\r\n                      setElementSelected(false);\r\n                      HotspotGuideDetails();\r\n                      setSelectedTemplateTour(\"Hotspot\");\r\n                      setSelectedTemplate(\"Tour\", true);\r\n                      setSelectedStepTypeHotspot(true);\r\n                      if (setStepData) setStepData({ type: \"Hotspot\" });\r\n                      setTooltipCount(tooltipCount + 1);\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            else if (data?.GuideType.toLowerCase() === \"checklist\") {\r\n              // Handle checklist type if needed\r\n              setSelectedTemplate(\"Checklist\", true);\r\n              setCurrentGuideId(data?.GuideId);\r\n              SetGuideName(data?.Name);\r\n              if (setStepData) setStepData({ type: \"Checklist\" });\r\n            }\r\n          }\r\n\r\n        } catch (error) {\r\n          console.error(\"Error creating interaction:\", error);\r\n          const errorMessage: ChatMessage = {\r\n            text: translate(\"Sorry, I encountered an error while processing your request.\"),\r\n            isUser: false,\r\n            timestamp: new Date()\r\n          };\r\n          setMessages(prev => [...prev, errorMessage]);\r\n        } finally {\r\n          setIsLoading(false);\r\n          setError(null);\r\n        }\r\n    }\r\n};\r\n  return (\r\n    <>\r\n    <div className=\"qadpt-chat-window\">\r\n      <div className=\"qadpt-chat-header\">\r\n        <div className=\"qadpt-chat-title\">\r\n          <span className=\"qadpt-chat-icon\" dangerouslySetInnerHTML={{ __html: ai }} />\r\n            <span>{translate(\"AI Chatbot\")}</span>\r\n        </div>\r\n          <button className=\"qadpt-close-btn\" onClick={onClose} aria-label={translate(\"Close\")}> {/* accessibility */}\r\n          <CloseIcon fontSize=\"small\" />\r\n        </button>\r\n      </div>\r\n          <div className=\"qadpt-chat-container\">\r\n        <div className=\"qadpt-messages\" ref={chatContainerRef} onScroll={handleScroll}>\r\n          {messages.map((message, index) => (\r\n            <div\r\n              key={index}\r\n              className={`qadpt-message ${message.isUser ? 'user-message' : 'ai-message'}`}\r\n            >\r\n              {!message.isUser && (\r\n                <div className=\"ai-avatar\">\r\n                  <span dangerouslySetInnerHTML={{ __html: airobot }} />\r\n                </div>\r\n              )}\r\n              <div className=\"message-content\">\r\n                <p>{message.text}</p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n          {isLoading && (\r\n            <div className=\"qadpt-chat-message ai-message\">\r\n              <div className=\"ai-avatar\">\r\n                <span dangerouslySetInnerHTML={{ __html: airobot }} />\r\n              </div>\r\n              <div className=\"message-content\">\r\n                <div className=\"typing-indicator\">\r\n                  <span></span>\r\n                  <span></span>\r\n                  <span></span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          <div ref={messagesEndRef} />\r\n\r\n          {showScrollButton && (\r\n              <button className=\"scroll-to-bottom\" onClick={scrollToBottom} aria-label={translate(\"Scroll to bottom\")}> {/* accessibility */}\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M12 16L6 10L7.4 8.6L12 13.2L16.6 8.6L18 10L12 16Z\" fill=\"currentColor\"/>\r\n              </svg>\r\n            </button>\r\n          )}\r\n          </div>\r\n          </div>\r\n\r\n      <div className=\"qadpt-input\">\r\n          {error && <div className=\"error-message\">{translate(error)}</div>}\r\n\r\n            <div className=\"input-with-icons\">\r\n            <div className='qadpt-txtcont'>\r\n\r\n            <textarea\r\n              ref={inputRef}\r\n              value={inputText}\r\n              onChange={handleInputChange}\r\n              onKeyDown={handleKeyDown}\r\n              placeholder={isOpenAIKeyProvided ? translate(\"Enter your prompt....\") : translate(\"Please Add OpenAI Key At Setting -> Agents\")}\r\n              disabled={isLoading}\r\n              rows={1}\r\n                />\r\n           </div>\r\n            <div className=\"input-icons\">\r\n              <div>\r\n              <button\r\n                className=\"icon-btn upload-btn\"\r\n                onMouseEnter={() => setIsUploadHovered(true)}\r\n                onMouseLeave={() => setIsUploadHovered(false)}\r\n                  aria-label={translate(\"Upload\")}\r\n              >\r\n                <span dangerouslySetInnerHTML={{ __html: isUploadHovered ? upload_hover : upload }} />\r\n                </button>\r\n                </div>\r\n              <button\r\n                className=\"icon-btn send-btn\"\r\n                onClick={() => handleCreateInteraction(inputText)}\r\n                disabled={!isOpenAIKeyProvided || isLoading || !inputText.trim()}\r\n                aria-label={translate(\"Send\")}\r\n              >\r\n                <span dangerouslySetInnerHTML={{ __html: send }} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          </div>\r\n\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ModernChatWindow;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,CAAEC,UAAU,KAAQ,OAAO,CACtE,MAAO,wBAAwB,CAC/B,OAASC,cAAc,KAAQ,uCAAuC,CACtE,OAASC,EAAE,CAAEC,OAAO,CAA0BC,MAAM,CAAEC,YAAY,CAAEC,IAAI,KAAQ,0BAA0B,CAC1G,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD;AACA,OAASC,iBAAiB,KAAQ,0BAA0B,CAC5D,OAASC,sBAAsB,CAAEC,qBAAqB,CAAEC,4BAA4B,KAAQ,yCAAyC,CACrI,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CAErE,MAAO,6CAA6C,CACpD,OAASC,4BAA4B,KAAQ,kCAAkC,CAC/E,OAASC,WAAW,KAAQ,2CAA2C,CACvE,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAuB/C;AAEA,KAAM,CAAAC,gBAAiD,CAAGC,IAAA,EAA+K,IAA9K,CAAEC,OAAO,CAACC,gBAAgB,CAAEC,WAAW,CAAEC,cAAc,CAAEC,iBAAiB,CAAEC,aAAa,CAAEC,kBAAkB,CAAEC,iBAAiB,CAAEC,oBAAoB,CAAEC,eAAgB,CAAC,CAAAV,IAAA,CAClO,KAAM,CAAEW,CAAC,CAAEC,SAAU,CAAC,CAAGpB,cAAc,CAAC,CAAC,CACvC;AACA,KAAM,CACRqB,mBAAmB,CACnBC,uBAAuB,CACvBC,QAAQ,CACRC,YAAY,CACZC,cAAc,CACdC,cAAc,CACdC,mBAAmB,CACnBC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,kBAAkB,CACdC,0BAA0B,CAC1BC,eAAe,CACfC,qBAAqB,CACrBC,kBAAkB,CAClBC,aAAa,CACbC,uBAAuB,CACvBC,2BAA2B,CAC3BC,gCAAgC,CAChCC,mBAAmB,CACnBC,cACL,CAAC,CAAG5C,cAAc,CAAE6C,KAAkB,EAAKA,KAAK,CAAC,CAC9C,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CAAG;AACtD,KAAM,CAAE+D,SAAU,CAAC,CAAG5D,UAAU,CAACC,cAAc,CAAC,CAChD,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkE,QAAQ,CAAEC,WAAW,CAAC,CAAGnE,QAAQ,CAAgB,CACpD,CACAoE,IAAI,CAAE9B,SAAS,CAAC,mHAAmH,CAAC,CACpI+B,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACpB,CAAC,CACJ,CAAC,CACJ,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CACjD;AACA,KAAM,CAAC0E,KAAK,CAAEC,QAAQ,CAAC,CAAG3E,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC4E,YAAY,CAAEC,eAAe,CAAC,CAAG7E,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC8E,eAAe,CAAEC,kBAAkB,CAAC,CAAG/E,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACgF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjF,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACkF,WAAW,CAAEC,cAAc,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACoF,eAAe,CAAEC,kBAAkB,CAAC,CAAGrF,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAAsF,cAAc,CAAGrF,MAAM,CAAiB,IAAI,CAAC,CACnD,KAAM,CAAAsF,QAAQ,CAAGtF,MAAM,CAAsB,IAAI,CAAC,CAClD,KAAM,CAAAuF,gBAAgB,CAAGvF,MAAM,CAAiB,IAAI,CAAC,CACrD,KAAM,CAACwF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG1F,QAAQ,CAAC,IAAI,CAAC,CACpE,KAAM,CAAE2F,YAAa,CAAC,CAAG1E,WAAW,CAAC,CAAC,CAEtCf,SAAS,CAAC,IAAM,CACd,GAAIsF,gBAAgB,CAACI,OAAO,CAAE,CAC5B;AACAJ,gBAAgB,CAACI,OAAO,CAACC,SAAS,CAAGL,gBAAgB,CAACI,OAAO,CAACE,YAAY,CAC5E,CAEAb,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAAE,CAACf,QAAQ,CAAEM,SAAS,CAAC,CAAC,CAGzB,KAAM,CAAAuB,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIP,gBAAgB,CAACI,OAAO,CAAE,CAC5B,KAAM,CAAEC,SAAS,CAAEC,YAAY,CAAEE,YAAa,CAAC,CAAGR,gBAAgB,CAACI,OAAO,CAE1E;AACA,KAAM,CAAAK,UAAU,CAAGH,YAAY,CAAGD,SAAS,CAAGG,YAAY,CAAG,EAAE,CAC/Df,mBAAmB,CAAC,CAACgB,UAAU,CAAC,CAClC,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIV,gBAAgB,CAACI,OAAO,CAAE,CAC5BJ,gBAAgB,CAACI,OAAO,CAACC,SAAS,CAAGL,gBAAgB,CAACI,OAAO,CAACE,YAAY,CAC1Eb,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAED;AACA/E,SAAS,CAAC,IAAM,KAAAiG,iBAAA,CACd,CAAAA,iBAAA,CAAAZ,QAAQ,CAACK,OAAO,UAAAO,iBAAA,iBAAhBA,iBAAA,CAAkBC,KAAK,CAAC,CAAC,CACzBpF,4BAA4B,CAAC2E,YAAY,CAAC5B,SAAS,CAAC2B,sBAAsB,CAAC,CAC7E,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxF,SAAS,CAAC,IAAM,CACdmF,kBAAkB,CAACvE,4BAA4B,CAAC,CAAC,CAAC,CACpD,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAuF,iBAAiB,CAAIC,CAAyC,EAAK,CACvErC,YAAY,CAACqC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC5B7B,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,GAAIY,QAAQ,CAACK,OAAO,CAAE,CACpBL,QAAQ,CAACK,OAAO,CAACa,KAAK,CAACC,MAAM,CAAG,MAAM,CAAE;AACxCnB,QAAQ,CAACK,OAAO,CAACa,KAAK,CAACC,MAAM,CAAG,GAAGC,IAAI,CAACC,GAAG,CAACrB,QAAQ,CAACK,OAAO,CAACE,YAAY,CAAE,EAAE,CAAC,IAAI,CAAE;AACtF,CACF,CAAC,CACD5F,SAAS,CAAC,IAAM,CACd,GAAIqF,QAAQ,CAACK,OAAO,EAAI5B,SAAS,GAAK,EAAE,CAAE,CACxCuB,QAAQ,CAACK,OAAO,CAACa,KAAK,CAACC,MAAM,CAAG,MAAM,CACxC,CACF,CAAC,CAAE,CAAC1C,SAAS,CAAC,CAAC,CAGf,KAAM,CAAA6C,aAAa,CAAIP,CAAsB,EAAK,CAChD,GAAIA,CAAC,CAACQ,GAAG,GAAK,OAAO,EAAI,CAACR,CAAC,CAACS,QAAQ,EAAItB,mBAAmB,CAAE,CAC3Da,CAAC,CAACU,cAAc,CAAC,CAAC,CAClBC,uBAAuB,CAACjD,SAAS,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAAkD,aAAa,CAAGA,CAAA,GAAe,CACnC,MAAO,CAAAC,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC,KAAK,CAAC,GAAK,CAAC,CAAC,CAClD,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAGA,CAAA,GAAM,CACpC,GAAIpC,WAAW,CAAE,CACfrE,qBAAqB,CAAC,CAAC,CACvBsE,cAAc,CAAC,KAAK,CAAC,CACrB,OACF,CAEA;AACA,KAAM,CAAAoC,WAAW,CAAGvD,SAAS,CAACwD,IAAI,CAAC,CAAC,CAEpC;AACA,GAAIN,aAAa,CAAC,CAAC,EAAI,CAAC9B,eAAe,CAAE,CACvCT,QAAQ,CAACrC,SAAS,CAAC,+FAA+F,CAAC,CAAC,CACpHmF,UAAU,CAAC,IAAM9C,QAAQ,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,CACxC,CAEAQ,cAAc,CAAC,IAAI,CAAC,CACpBvE,sBAAsB,CAAC,CACrB8G,aAAa,CAAE,IAAI,CAAE;AACrBC,OAAO,CAAEA,CAAA,GAAM,CACbxC,cAAc,CAAC,IAAI,CAAC,CACpBR,QAAQ,CAAC,IAAI,CAAC,CAAE;AAClB,CAAC,CACDiD,QAAQ,CAAEA,CAACxD,IAAY,CAAEyD,OAAgB,GAAK,CAC5C;AACA;AACA;AACA,GAAIN,WAAW,CAAE,CACftD,YAAY,CAACsD,WAAW,CAAG,GAAG,CAAGnD,IAAI,CAAC,CACxC,CAAC,IAAM,CACLH,YAAY,CAACG,IAAI,CAAC,CACpB,CACF,CAAC,CACD0D,KAAK,CAAEA,CAAA,GAAM,CACX3C,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CACD4C,OAAO,CAAGrD,KAAU,EAAK,CACvBsD,OAAO,CAACtD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDS,cAAc,CAAC,KAAK,CAAC,CAErB;AACA,GAAI+B,aAAa,CAAC,CAAC,CAAE,CACnB,GAAIxC,KAAK,GAAK,aAAa,CAAE,CAC3BC,QAAQ,CAACrC,SAAS,CAAC,oFAAoF,CAAC,CAAC,CAC3G,CAAC,IAAM,IAAIoC,KAAK,GAAK,SAAS,CAAE,CAC9BC,QAAQ,CAACrC,SAAS,CAAC,wGAAwG,CAAC,CAAC,CAC/H,CAAC,IAAM,CACLqC,QAAQ,CAAC,GAAGrC,SAAS,CAAC,oCAAoC,CAAC,IAAIoC,KAAK,KAAKpC,SAAS,CAAC,sCAAsC,CAAC,EAAE,CAAC,CAC/H,CACF,CAAC,IAAM,CACLqC,QAAQ,CAAC,GAAGrC,SAAS,CAAC,4BAA4B,CAAC,IAAIoC,KAAK,KAAKpC,SAAS,CAAC,mBAAmB,CAAC,EAAE,CAAC,CACpG,CAEA;AACAmF,UAAU,CAAC,IAAM9C,QAAQ,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,CACxC,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AAEF;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACE;AACA,KAAM,CAAAsD,oBAAoB,CAAIC,MAAc,EAAc,CACxD,KAAM,CAAAC,YAAY,CAAG,CACnB,uCAAuC,CACvC,iCAAiC,CACjC,+CAA+C,CAC/C,oCAAoC,CACpC,oBAAoB,CACpB,sBAAsB,CACtB,8BAA8B,CAC9B,gCAAgC,CACjC,CAED,MAAO,CAAAA,YAAY,CAACC,IAAI,CAACC,OAAO,EAAIA,OAAO,CAACC,IAAI,CAACJ,MAAM,CAAC,CAAC,CAC3D,CAAC,CAED;AACA,KAAM,CAAAK,cAAc,CAAIL,MAAc,EAAuI,CAC3K,KAAM,CAAAM,KAAuI,CAAG,EAAE,CAElJ;AACA;AACA,KAAM,CAAAC,gBAAgB,CAAG,uGAAuG,CAChI;AACA,KAAM,CAAAC,gBAAgB,CAAG,6DAA6D,CAEtF,GAAI,CAAAC,KAAK,CAET;AACA,MAAO,CAACA,KAAK,CAAGF,gBAAgB,CAACG,IAAI,CAACV,MAAM,CAAC,IAAM,IAAI,CAAE,CACvD,KAAM,CAAAW,UAAU,CAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CACrC,GAAI,CAAAI,QAAQ,CAAGJ,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,CAE9B;AACA,GAAIuB,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAIF,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAE,CAC/FF,QAAQ,CAAG,cAAc,CAC3B,CAAC,IAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAClDF,QAAQ,CAAG,QAAQ,CACrB,CAAC,IAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAClDF,QAAQ,CAAG,SAAS,CACtB,CAAC,IAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACjDF,QAAQ,CAAG,SAAS,CACtB,CAAC,IAAM,CACL;AACAA,QAAQ,CAAG,cAAc,CAC3B,CAEA;AACAA,QAAQ,CAAGA,QAAQ,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC,CAE/DZ,KAAK,CAACa,IAAI,CAAC,CACTN,QAAQ,CAAEA,QAAQ,CAClBO,KAAK,CAAE,QAAQT,UAAU,EAAE,CAC3BU,OAAO,CAAE,WAAWV,UAAU,MAChC,CAAC,CAAC,CACJ,CAEA;AACA,GAAIL,KAAK,CAACgB,MAAM,GAAK,CAAC,CAAE,CACtB,MAAO,CAACb,KAAK,CAAGD,gBAAgB,CAACE,IAAI,CAACV,MAAM,CAAC,IAAM,IAAI,CAAE,CACvD,KAAM,CAAAW,UAAU,CAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CACrC,GAAI,CAAAI,QAAQ,CAAGJ,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,CAE9B;AACA,GAAIuB,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAIF,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAE,CAC/FF,QAAQ,CAAG,cAAc,CAC3B,CAAC,IAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAClDF,QAAQ,CAAG,QAAQ,CACrB,CAAC,IAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAClDF,QAAQ,CAAG,SAAS,CACtB,CAAC,IAAM,IAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACjDF,QAAQ,CAAG,SAAS,CACtB,CAAC,IAAM,CACL;AACAA,QAAQ,CAAG,cAAc,CAC3B,CAEA;AACAA,QAAQ,CAAGA,QAAQ,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC,CAE/DZ,KAAK,CAACa,IAAI,CAAC,CACTN,QAAQ,CAAEA,QAAQ,CAClBO,KAAK,CAAE,QAAQT,UAAU,EAAE,CAC3BU,OAAO,CAAE,WAAWV,UAAU,MAChC,CAAC,CAAC,CACJ,CACF,CAEA;AACA,GAAIL,KAAK,CAACgB,MAAM,GAAK,CAAC,CAAE,CACtB,KAAM,CAAAC,mBAAmB,CAAG,gGAAgG,CAE5H,MAAO,CAACd,KAAK,CAAGc,mBAAmB,CAACb,IAAI,CAACV,MAAM,CAAC,IAAM,IAAI,CAAE,CAC1DM,KAAK,CAACa,IAAI,CAAC,CACTN,QAAQ,CAAEJ,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGR,KAAK,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,CAAC,CAC9DE,KAAK,CAAEX,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,CACtB+B,OAAO,CAAE,MAAMZ,KAAK,CAAC,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,MAChC,CAAC,CAAC,CACJ,CACF,CAEA;AACA,KAAM,CAAAkC,aAAa,CAAG,yLAAyL,CAC/M,GAAI,CAAAC,WAAW,CACf,MAAO,CAACA,WAAW,CAAGD,aAAa,CAACd,IAAI,CAACV,MAAM,CAAC,IAAM,IAAI,CAAE,CAC1D,KAAM,CAAAW,UAAU,CAAGC,QAAQ,CAACa,WAAW,CAAC,CAAC,CAAC,CAAC,CAC3C,KAAM,CAAAC,UAAU,CAAGD,WAAW,CAAC,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC,CACxC,KAAM,CAAAqC,YAAY,CAAGF,WAAW,CAAC,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CACxD,KAAM,CAAAc,iBAAiB,CAAGH,WAAW,CAAC,CAAC,CAAC,CAACnC,IAAI,CAAC,CAAC,CAE/C;AACA,KAAM,CAAAuC,SAAS,CAAGvB,KAAK,CAACwB,SAAS,CAACC,IAAI,EAAI,CACxC,KAAM,CAAAC,SAAS,CAAGD,IAAI,CAACX,KAAK,EAAI,EAAE,CAClC,MAAO,CAAAY,SAAS,CAACjB,QAAQ,CAAC,GAAGJ,UAAU,EAAE,CAAC,CAC5C,CAAC,CAAC,CAEF,GAAIkB,SAAS,GAAK,CAAC,CAAC,CAAE,CACpB;AACAvB,KAAK,CAACuB,SAAS,CAAC,CAACH,UAAU,CAAGA,UAAU,CACxCpB,KAAK,CAACuB,SAAS,CAAC,CAACF,YAAY,CAAGA,YAAY,CAC5CrB,KAAK,CAACuB,SAAS,CAAC,CAACD,iBAAiB,CAAGA,iBAAiB,CACxD,CACF,CAEA;AACA,KAAM,CAAAK,WAAW,CAAG,kFAAkF,CACtG,GAAI,CAAAC,SAAS,CACb,MAAO,CAACA,SAAS,CAAGD,WAAW,CAACvB,IAAI,CAACV,MAAM,CAAC,IAAM,IAAI,CAAE,CACtD,KAAM,CAAAW,UAAU,CAAGC,QAAQ,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC,CACzC,KAAM,CAAAC,WAAW,CAAGD,SAAS,CAAC,CAAC,CAAC,CAAC5C,IAAI,CAAC,CAAC,CAEvC;AACA,KAAM,CAAAuC,SAAS,CAAGvB,KAAK,CAACwB,SAAS,CAACC,IAAI,EAAI,CACxC,KAAM,CAAAC,SAAS,CAAGD,IAAI,CAACX,KAAK,EAAI,EAAE,CAClC,MAAO,CAAAY,SAAS,CAACjB,QAAQ,CAAC,GAAGJ,UAAU,EAAE,CAAC,CAC5C,CAAC,CAAC,CAEF,GAAIkB,SAAS,GAAK,CAAC,CAAC,CAAE,CACpB;AACAvB,KAAK,CAACuB,SAAS,CAAC,CAACR,OAAO,CAAG,MAAMc,WAAW,MAAM,CACpD,CACF,CAEA;AACA,GAAI7B,KAAK,CAACgB,MAAM,GAAK,CAAC,CAAE,CACtB;AACA,KAAM,CAAAc,gBAAgB,CAAG,kEAAkE,CAC3F,KAAM,CAAAC,SAAmB,CAAG,EAAE,CAE9B,MAAO,CAAC5B,KAAK,CAAG2B,gBAAgB,CAAC1B,IAAI,CAACV,MAAM,CAAC,IAAM,IAAI,CAAE,CACvDqC,SAAS,CAAClB,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC,CAACO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGR,KAAK,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CACtE,CAEA,GAAImB,SAAS,CAACf,MAAM,CAAG,CAAC,CAAE,CACxBe,SAAS,CAACC,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACjClC,KAAK,CAACa,IAAI,CAAC,CACTN,QAAQ,CAAE0B,IAAI,CACdnB,KAAK,CAAE,QAAQoB,KAAK,CAAG,CAAC,EAAE,CAC1BnB,OAAO,CAAE,WAAWmB,KAAK,CAAG,CAAC,MAC/B,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAlC,KAAK,CAACa,IAAI,CACR,CAAEN,QAAQ,CAAE,cAAc,CAAEO,KAAK,CAAE,QAAQ,CAAEC,OAAO,CAAE,cAAe,CAAC,CACtE,CAAER,QAAQ,CAAE,QAAQ,CAAEO,KAAK,CAAE,QAAQ,CAAEC,OAAO,CAAE,sBAAuB,CAAC,CACxE,CAAER,QAAQ,CAAE,cAAc,CAAEO,KAAK,CAAE,QAAQ,CAAEC,OAAO,CAAE,eAAgB,CACxE,CAAC,CACH,CACF,CAEA;AACAf,KAAK,CAACmC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACnB,KAAM,CAAAC,IAAI,CAAGF,CAAC,CAACtB,KAAK,CAAGR,QAAQ,CAAC8B,CAAC,CAACtB,KAAK,CAACyB,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAAC,CAAG,CAAC,CAC/D,KAAM,CAAAC,IAAI,CAAGH,CAAC,CAACvB,KAAK,CAAGR,QAAQ,CAAC+B,CAAC,CAACvB,KAAK,CAACyB,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAAC,CAAG,CAAC,CAC/D,MAAO,CAAAD,IAAI,CAAGE,IAAI,CACpB,CAAC,CAAC,CAEF,MAAO,CAAAxC,KAAK,CACd,CAAC,CAGD,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAGlL,QAAQ,CAAM,CAAC,CAC7C,KAAK,CAACmL,WAAW,CAACC,cAAc,CAAC,CAACpL,QAAQ,CAAM,CAAC,CACjDE,SAAS,CAAC,IACV,CACI+C,kBAAkB,CAAC,KAAK,CAAC,CAE/B,CAAC,CAAC,EAAE,CAAC,CACH,KAAM,CAAAgE,uBAAuB,CAAG,KAAO,CAAAT,KAAa,EAAK,CACvD9C,mBAAmB,CAAC,IAAI,CAAC,CACzBiB,QAAQ,CAAC,IAAI,CAAC,CACdxB,eAAe,CAAC,IAAI,CAAC,CACrBC,qBAAqB,CAAC,KAAK,CAAC,CAC5B,GAAIoD,KAAK,CAACgD,MAAM,CAAG,EAAE,CAAE,CACrB7E,QAAQ,CAACrC,SAAS,CAAC,0CAA0C,CAAC,CAAC,CACjE,CAAC,IAAM,CACH;AACA,KAAM,CAAA+I,WAAwB,CAAG,CAC/BjH,IAAI,CAAEoC,KAAK,CACXnC,MAAM,CAAE,IAAI,CACZC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACDJ,WAAW,CAACmH,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAED,WAAW,CAAC,CAAC,CAC3CpH,YAAY,CAAC,EAAE,CAAC,CAChBQ,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,KAAA8G,gBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CACF;AAGA,KAAM,CAAAC,UAAuB,CAAG,CAC9BtH,IAAI,CAAE9B,SAAS,CAAC,8BAA8B,CAAC,CAC/C+B,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACDJ,WAAW,CAACmH,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEI,UAAU,CAAC,CAAC,CAE1C,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAhL,iBAAiB,CAAC6F,KAAK,CAAEzC,SAAS,CAAE6H,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAE5E/J,iBAAiB,CAAC4J,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,CAAC,CAChCJ,IAAI,CAACK,SAAS,CAAGJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CACrC,GAAG,CAAAH,IAAI,SAAJA,IAAI,kBAAAJ,gBAAA,CAAJI,IAAI,CAAEM,SAAS,CAAC,CAAC,CAAC,UAAAV,gBAAA,iBAAlBA,gBAAA,CAAoBW,QAAQ,IAAK,QAAQ,EAAI,CAAAP,IAAI,SAAJA,IAAI,kBAAAH,iBAAA,CAAJG,IAAI,CAAEM,SAAS,CAAC,CAAC,CAAC,UAAAT,iBAAA,kBAAAC,qBAAA,CAAlBD,iBAAA,CAAoBW,aAAa,UAAAV,qBAAA,iBAAjCA,qBAAA,CAAmCjC,MAAM,EAAC,CAAC,CAAC,CAC1FjG,uBAAuB,CAAC,IAAI,CAAC,CAC/B,CACAF,kBAAkB,CAACsI,IAAI,CAAC,CACxBhI,cAAc,CAAC,KAAK,CAAC,CAAE;AACvB/B,gBAAgB,CAAG+J,IAAI,CAEvBrI,aAAa,CAAC,CAAAqI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEM,SAAS,GAAI,EAAE,CAAC,CACpChI,YAAY,CAAC,EAAE,CAAC,CAEhB,GAAItC,OAAO,CAAEA,OAAO,CAAC,CAAC,CAEtB,GAAIgK,IAAI,CAAE,CACRT,UAAU,CAACS,IAAI,CAAC,CAChB,GAAI,CAAAA,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,IAAK,cAAc,CAAE,CACpDlH,cAAc,CAAC,IAAI,CAAC,CACpBC,iBAAiB,CAAC4J,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,CAAC,CAChCxJ,mBAAmB,CAAC,cAAc,CAAC,CACnCI,cAAc,CAAC,KAAK,CAAC,CACrBD,YAAY,CAACiJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,CAAC,CACxB,GAAIxK,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,cAAe,CAAC,CAAC,CACtD5H,mBAAmB,CAAC,CAAC,CAErB;AACA4E,UAAU,CAAC,IAAM,CACfhE,gCAAgC,CAAC,KAAK,CAAC,CAAE;AAC3C,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IACI,IAAI,CAAAkI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,IAAK,QAAQ,CAAE,CACnDzG,mBAAmB,CAAC,QAAQ,CAAE,IAAI,CAAC,CACnCI,cAAc,CAAC,IAAI,CAAC,CACpBb,cAAc,CAAC,KAAK,CAAC,CACrBC,iBAAiB,CAAC4J,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,CAAC,CAChCrJ,YAAY,CAACiJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,CAAC,CACxB,GAAIxK,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,QAAS,CAAC,CAAC,CAChD;AACA5H,mBAAmB,CAAC,CAAC,CACvB,CAAC,IACI,IAAI,CAAA8I,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,IAAK,SAAS,CAAE,CACpD/F,kBAAkB,CAAC,KAAK,CAAC,CACzBV,mBAAmB,CAAC,SAAS,CAAE,IAAI,CAAC,CACpCT,cAAc,CAAC,KAAK,CAAC,CACrBY,YAAY,CAACiJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,CAAC,CACxBrK,aAAa,CAAC,IAAI,CAAC,CACnBC,kBAAkB,CAAC,KAAK,CAAC,CACzBC,iBAAiB,CAAC,IAAI,CAAC,CACvBC,oBAAoB,CAAC,KAAK,CAAC,CAC3B,GAAIN,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,SAAU,CAAC,CAAC,CACjD;AACA3H,mBAAmB,CAAC,CAAC,CAEvB,CAAC,IACI,IAAI,CAAA6I,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,IAAK,SAAS,CAAE,CACpD/F,kBAAkB,CAAC,KAAK,CAAC,CACzBV,mBAAmB,CAAC,SAAS,CAAE,IAAI,CAAC,CACpCT,cAAc,CAAC,KAAK,CAAC,CACrBY,YAAY,CAACiJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,CAAC,CACxBrK,aAAa,CAAC,IAAI,CAAC,CACnBC,kBAAkB,CAAC,KAAK,CAAC,CACzBC,iBAAiB,CAAC,IAAI,CAAC,CACvBC,oBAAoB,CAAC,KAAK,CAAC,CAC3B,GAAIN,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,SAAU,CAAC,CAAC,CACjD;AACA5H,mBAAmB,CAAC,CAAC,CACrBE,eAAe,CAACC,YAAY,CAAG,CAAC,CAAC,CAEnC,CAAC,IACI,IAAI,CAAA2I,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,IAAK,MAAM,CAAE,CACjDzG,mBAAmB,CAAC,MAAM,CAAE,IAAI,CAAC,CACjCR,iBAAiB,CAAC4J,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,CAAC,CAChCrJ,YAAY,CAACiJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,CAAC,CACxBvK,cAAc,CAAC,IAAI,CAAC,CACpBa,cAAc,CAAC,KAAK,CAAC,CACrBX,aAAa,CAAC,IAAI,CAAC,CACnBC,kBAAkB,CAAC,IAAI,CAAC,CACxBC,iBAAiB,CAAC,KAAK,CAAC,CACxBC,oBAAoB,CAAC,KAAK,CAAC,CAE3B;AACA,GAAIwJ,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEM,SAAS,EAAIN,IAAI,CAACM,SAAS,CAACzC,MAAM,CAAG,CAAC,CAAE,CAChD,KAAM,CAAA8C,SAAS,CAAGX,IAAI,CAACM,SAAS,CAACM,GAAG,CAAC,CAACtC,IAAS,CAAES,KAAa,IAAM,CAClE8B,EAAE,CAAEvC,IAAI,CAACwC,MAAM,CACfC,IAAI,CAAEzC,IAAI,CAAC0C,SAAS,EAAI,QAAQjC,KAAK,CAAG,CAAC,EAAE,CAC3C3B,QAAQ,CAAEkB,IAAI,CAACiC,QAAQ,CACvBU,SAAS,CAAElC,KAAK,CAAG,CACrB,CAAC,CAAC,CAAC,CACHjI,QAAQ,CAAC6J,SAAS,CAAC,CAEnB;AACA,GAAIA,SAAS,CAAC9C,MAAM,CAAG,CAAC,CAAE,CACxB5G,cAAc,CAAC,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAiK,SAAS,CAAGlB,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,CACnC,GAAIY,SAAS,EAAIA,SAAS,CAACX,QAAQ,CAAE,CACnC,KAAM,CAAAnD,QAAQ,CAAG8D,SAAS,CAACX,QAAQ,CACnCd,cAAc,CAACrC,QAAQ,CAAC,CAExB;AACAvG,uBAAuB,CAACuG,QAAQ,CAAC,CAEjC;AACA,GAAIA,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAK,cAAc,CAAE,CAC7CnG,mBAAmB,CAAC,CAAC,CACrBL,uBAAuB,CAAC,cAAc,CAAC,CACvCD,mBAAmB,CAAC,MAAM,CAAE,IAAI,CAAC,CAEjC;AACAkF,UAAU,CAAC,IAAM,CACfhE,gCAAgC,CAAC,KAAK,CAAC,CAAE;AAC3C,CAAC,CAAE,GAAG,CAAC,CAEP,GAAI5B,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,cAAe,CAAC,CAAC,CACxD,CAAC,IACI,IAAI1B,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAK,QAAQ,CAAE,CAC5CnG,mBAAmB,CAAC,CAAC,CACrBN,mBAAmB,CAAC,MAAM,CAAE,IAAI,CAAC,CACjCC,uBAAuB,CAAC,QAAQ,CAAC,CACjC,GAAIX,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,QAAS,CAAC,CAAC,CAChD9H,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,IACI,IAAIoG,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,CAAE,CAC7C/F,kBAAkB,CAAC,KAAK,CAAC,CAEzBJ,mBAAmB,CAAC,CAAC,CACrBL,uBAAuB,CAAC,SAAS,CAAC,CAClCD,mBAAmB,CAAC,MAAM,CAAE,IAAI,CAAC,CAEjC;AACAkF,UAAU,CAAC,IAAM,CACfjE,2BAA2B,CAAC,CAAC,CAC/B,CAAC,CAAE,GAAG,CAAC,CAEP,GAAI3B,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,SAAU,CAAC,CAAC,CACjD1H,eAAe,CAACC,YAAY,CAAG,CAAC,CAAC,CACnC,CAAC,IACI,IAAI+F,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,CAAE,CAC7C/F,kBAAkB,CAAC,KAAK,CAAC,CACzBH,mBAAmB,CAAC,CAAC,CACrBN,uBAAuB,CAAC,SAAS,CAAC,CAClCD,mBAAmB,CAAC,MAAM,CAAE,IAAI,CAAC,CACjCW,0BAA0B,CAAC,IAAI,CAAC,CAChC,GAAIrB,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,SAAU,CAAC,CAAC,CACjD1H,eAAe,CAACC,YAAY,CAAG,CAAC,CAAC,CACnC,CACF,CACF,CACF,CACF,CAAC,IACI,IAAI,CAAA2I,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAES,SAAS,CAACpD,WAAW,CAAC,CAAC,IAAK,WAAW,CAAE,CACtD;AACAzG,mBAAmB,CAAC,WAAW,CAAE,IAAI,CAAC,CACtCR,iBAAiB,CAAC4J,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,CAAC,CAChCrJ,YAAY,CAACiJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEU,IAAI,CAAC,CACxB,GAAIxK,WAAW,CAAEA,WAAW,CAAC,CAAE4I,IAAI,CAAE,WAAY,CAAC,CAAC,CACrD,CACF,CAEF,CAAE,MAAO/F,KAAK,CAAE,CACdsD,OAAO,CAACtD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,CAAAoI,YAAyB,CAAG,CAChC1I,IAAI,CAAE9B,SAAS,CAAC,8DAA8D,CAAC,CAC/E+B,MAAM,CAAE,KAAK,CACbC,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CACtB,CAAC,CACDJ,WAAW,CAACmH,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEwB,YAAY,CAAC,CAAC,CAC9C,CAAC,OAAS,CACRrI,YAAY,CAAC,KAAK,CAAC,CACnBE,QAAQ,CAAC,IAAI,CAAC,CAChB,CACJ,CACJ,CAAC,CACC,mBACEvD,IAAA,CAAAI,SAAA,EAAAuL,QAAA,cACAzL,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCzL,KAAA,QAAK0L,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCzL,KAAA,QAAK0L,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B3L,IAAA,SAAM4L,SAAS,CAAC,iBAAiB,CAACC,uBAAuB,CAAE,CAAEC,MAAM,CAAE7M,EAAG,CAAE,CAAE,CAAC,cAC3Ee,IAAA,SAAA2L,QAAA,CAAOzK,SAAS,CAAC,YAAY,CAAC,CAAO,CAAC,EACrC,CAAC,cACJhB,KAAA,WAAQ0L,SAAS,CAAC,iBAAiB,CAACG,OAAO,CAAExL,OAAQ,CAAC,aAAYW,SAAS,CAAC,OAAO,CAAE,CAAAyK,QAAA,EAAC,GAAC,cACvF3L,IAAA,CAACV,SAAS,EAAC0M,QAAQ,CAAC,OAAO,CAAE,CAAC,EACxB,CAAC,EACN,CAAC,cACFhM,IAAA,QAAK4L,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACvCzL,KAAA,QAAK0L,SAAS,CAAC,gBAAgB,CAACK,GAAG,CAAE7H,gBAAiB,CAAC8H,QAAQ,CAAEvH,YAAa,CAAAgH,QAAA,EAC3E7I,QAAQ,CAACqI,GAAG,CAAC,CAACgB,OAAO,CAAE7C,KAAK,gBAC3BpJ,KAAA,QAEE0L,SAAS,CAAE,iBAAiBO,OAAO,CAAClJ,MAAM,CAAG,cAAc,CAAG,YAAY,EAAG,CAAA0I,QAAA,EAE5E,CAACQ,OAAO,CAAClJ,MAAM,eACdjD,IAAA,QAAK4L,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxB3L,IAAA,SAAM6L,uBAAuB,CAAE,CAAEC,MAAM,CAAE5M,OAAQ,CAAE,CAAE,CAAC,CACnD,CACN,cACDc,IAAA,QAAK4L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9B3L,IAAA,MAAA2L,QAAA,CAAIQ,OAAO,CAACnJ,IAAI,CAAI,CAAC,CAClB,CAAC,GAVDsG,KAWF,CACN,CAAC,CACDlG,SAAS,eACRlD,KAAA,QAAK0L,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C3L,IAAA,QAAK4L,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxB3L,IAAA,SAAM6L,uBAAuB,CAAE,CAAEC,MAAM,CAAE5M,OAAQ,CAAE,CAAE,CAAC,CACnD,CAAC,cACNc,IAAA,QAAK4L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BzL,KAAA,QAAK0L,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B3L,IAAA,UAAY,CAAC,cACbA,IAAA,UAAY,CAAC,cACbA,IAAA,UAAY,CAAC,EACV,CAAC,CACH,CAAC,EACH,CACN,cACDA,IAAA,QAAKiM,GAAG,CAAE/H,cAAe,CAAE,CAAC,CAE3BN,gBAAgB,eACb1D,KAAA,WAAQ0L,SAAS,CAAC,kBAAkB,CAACG,OAAO,CAAEjH,cAAe,CAAC,aAAY5D,SAAS,CAAC,kBAAkB,CAAE,CAAAyK,QAAA,EAAC,GAAC,cAC1G3L,IAAA,QAAKoM,KAAK,CAAC,IAAI,CAAC9G,MAAM,CAAC,IAAI,CAAC+G,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAAZ,QAAA,cAC5F3L,IAAA,SAAMwM,CAAC,CAAC,mDAAmD,CAACF,IAAI,CAAC,cAAc,CAAC,CAAC,CAC9E,CAAC,EACA,CACT,EACI,CAAC,CACD,CAAC,cAEVpM,KAAA,QAAK0L,SAAS,CAAC,aAAa,CAAAD,QAAA,EACvBrI,KAAK,eAAItD,IAAA,QAAK4L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAEzK,SAAS,CAACoC,KAAK,CAAC,CAAM,CAAC,cAE/DpD,KAAA,QAAK0L,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eACjC3L,IAAA,QAAK4L,SAAS,CAAC,eAAe,CAAAD,QAAA,cAE9B3L,IAAA,aACEiM,GAAG,CAAE9H,QAAS,CACdiB,KAAK,CAAExC,SAAU,CACjB6J,QAAQ,CAAExH,iBAAkB,CAC5ByH,SAAS,CAAEjH,aAAc,CACzBkH,WAAW,CAAEtI,mBAAmB,CAAGnD,SAAS,CAAC,uBAAuB,CAAC,CAAGA,SAAS,CAAC,4CAA4C,CAAE,CAChI0L,QAAQ,CAAExJ,SAAU,CACpByJ,IAAI,CAAE,CAAE,CACL,CAAC,CACF,CAAC,cACL3M,KAAA,QAAK0L,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B3L,IAAA,QAAA2L,QAAA,cACA3L,IAAA,WACE4L,SAAS,CAAC,qBAAqB,CAC/BkB,YAAY,CAAEA,CAAA,GAAMnJ,kBAAkB,CAAC,IAAI,CAAE,CAC7CoJ,YAAY,CAAEA,CAAA,GAAMpJ,kBAAkB,CAAC,KAAK,CAAE,CAC5C,aAAYzC,SAAS,CAAC,QAAQ,CAAE,CAAAyK,QAAA,cAElC3L,IAAA,SAAM6L,uBAAuB,CAAE,CAAEC,MAAM,CAAEpI,eAAe,CAAGtE,YAAY,CAAGD,MAAO,CAAE,CAAE,CAAC,CAC9E,CAAC,CACJ,CAAC,cACRa,IAAA,WACE4L,SAAS,CAAC,mBAAmB,CAC7BG,OAAO,CAAEA,CAAA,GAAMlG,uBAAuB,CAACjD,SAAS,CAAE,CAClDgK,QAAQ,CAAE,CAACvI,mBAAmB,EAAIjB,SAAS,EAAI,CAACR,SAAS,CAACwD,IAAI,CAAC,CAAE,CACjE,aAAYlF,SAAS,CAAC,MAAM,CAAE,CAAAyK,QAAA,cAE9B3L,IAAA,SAAM6L,uBAAuB,CAAE,CAAEC,MAAM,CAAEzM,IAAK,CAAE,CAAE,CAAC,CAC7C,CAAC,EACN,CAAC,EACH,CAAC,EAED,CAAC,EAEP,CAAC,CACJ,CAAC,CAEP,CAAC,CAED,cAAe,CAAAgB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}