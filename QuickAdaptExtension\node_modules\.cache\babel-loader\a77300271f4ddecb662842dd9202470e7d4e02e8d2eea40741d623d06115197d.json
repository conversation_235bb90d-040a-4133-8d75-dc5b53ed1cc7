{"ast": null, "code": "const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      const keyEndIndex = /{.*}/.test(match[1]) ? match[1].lastIndexOf('}') + 1 : match[1].indexOf(this.formatSeparator);\n      if (keyEndIndex !== -1) {\n        formatters = match[1].slice(keyEndIndex).split(this.formatSeparator).map(elem => elem.trim()).filter(Boolean);\n        match[1] = match[1].slice(0, keyEndIndex);\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (formatters.length) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ;else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      const usingLegacyFormatFunction = this.options.interpolation.format && this.options.interpolation.format !== defOpts.interpolation.format;\n      if (usingLegacyFormatFunction) {\n        this.logger.warn(`init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting`);\n      }\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        if (s.formatter.init) s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    try {\n      const l = new Intl.Locale(lng);\n      if (l && l.getTextInfo) {\n        const ti = l.getTextInfo();\n        if (ti && ti.direction) return ti.direction;\n      }\n    } catch (e) {}\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    if (lng.toLowerCase().indexOf('-latn') > 1) return 'ltr';\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };", "map": {"version": 3, "names": ["isString", "obj", "defer", "res", "rej", "promise", "Promise", "resolve", "reject", "makeString", "object", "copy", "a", "s", "t", "for<PERSON>ach", "m", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "key", "indexOf", "replace", "canNotTraverseDeeper", "getLastOfPath", "path", "Empty", "stack", "split", "stackIndex", "length", "Object", "prototype", "hasOwnProperty", "call", "k", "set<PERSON>ath", "newValue", "undefined", "e", "p", "slice", "last", "push<PERSON><PERSON>", "concat", "push", "<PERSON><PERSON><PERSON>", "getPathWithDefaults", "data", "defaultData", "value", "deepExtend", "target", "source", "overwrite", "prop", "String", "regexEscape", "str", "_entityMap", "escape", "RegExpCache", "constructor", "capacity", "regExpMap", "Map", "regExpQueue", "getRegExp", "pattern", "regExpFromCache", "get", "regExpNew", "RegExp", "delete", "shift", "set", "chars", "looksLikeObjectPathRegExpCache", "looksLikeObjectPath", "nsSeparator", "keySeparator", "possibleChars", "filter", "c", "r", "map", "join", "matched", "test", "ki", "substring", "deepFind", "tokens", "current", "i", "next", "nextPath", "j", "getCleanedCode", "code", "consoleLogger", "type", "log", "args", "output", "warn", "error", "console", "apply", "<PERSON><PERSON>", "concreteLogger", "options", "init", "prefix", "logger", "debug", "forward", "deprecate", "lvl", "debugOnly", "create", "moduleName", "clone", "baseLogger", "EventEmitter", "observers", "on", "events", "listener", "event", "numListeners", "off", "emit", "cloned", "Array", "from", "entries", "observer", "numTimesAdded", "ResourceStore", "ns", "defaultNS", "ignoreJSONStructure", "addNamespaces", "removeNamespaces", "index", "splice", "getResource", "lng", "isArray", "result", "addResource", "silent", "addResources", "resources", "addResourceBundle", "deep", "skipCopy", "pack", "JSON", "parse", "stringify", "removeResourceBundle", "hasResourceBundle", "getResourceBundle", "getDataByLanguage", "hasLanguageSomeTranslations", "n", "keys", "find", "v", "toJSON", "postProcessor", "processors", "addPostProcessor", "module", "name", "handle", "translator", "processor", "process", "checkedLoadedFor", "shouldHandleAsObject", "Translator", "services", "changeLanguage", "language", "exists", "o", "interpolation", "opt", "resolved", "extractFromKey", "namespaces", "wouldCheckForNsInKey", "seemsNaturalLanguage", "userDefinedKeySeparator", "userDefinedNsSeparator", "match", "interpolator", "nestingRegexp", "parts", "translate", "last<PERSON>ey", "overloadTranslationOptionHandler", "arguments", "returnDetails", "namespace", "appendNamespaceToCIMode", "toLowerCase", "usedKey", "exactUsed<PERSON>ey", "usedLng", "usedNS", "usedParams", "getUsedParamsDetails", "resUsed<PERSON><PERSON>", "resExactUsedKey", "noObject", "joinArrays", "handleAsObjectInI18nFormat", "i18nFormat", "handleAsObject", "needsPluralHandling", "count", "hasDefaultValue", "defaultValueSuffix", "pluralResolver", "getSuffix", "defaultValueSuffixOrdinalFallback", "ordinal", "needsZeroSuffixLookup", "defaultValue", "pluralSeparator", "resForObjHndl", "resType", "toString", "returnObjects", "returnedObjectHandler", "resTypeIsArray", "newKeyToUse", "<PERSON><PERSON><PERSON>", "extendTranslation", "usedDefault", "isValidLookup", "missingKeyNoValueFallbackToKey", "resForMissing", "updateMissing", "fk", "lngs", "fallbackLngs", "languageUtils", "getFallbackCodes", "fallbackLng", "saveMissingTo", "toResolveHierarchy", "send", "l", "specificDefaultValue", "defaultForMissing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backendConnector", "saveMissing", "saveMissingPlurals", "suffixes", "getSuffixes", "suffix", "appendNamespaceToMissingKey", "parseMissingKeyHandler", "defaultVariables", "skipInterpolation", "skipOnVariables", "nestBef", "nb", "interpolate", "na", "nestAft", "nest", "context", "reset", "postProcess", "postProcessorNames", "applyPostProcessor", "postProcessPassResolved", "i18nResolved", "found", "extracted", "fallbackNS", "needsContextHandling", "codes", "utils", "hasLoadedNamespace", "finalKeys", "addLookupKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "contextSeparator", "<PERSON><PERSON><PERSON>", "pop", "returnNull", "returnEmptyString", "resourceStore", "optionsKeys", "useOptionsReplaceForData", "option", "LanguageUtil", "supportedLngs", "getScriptPartFromCode", "formatLanguageCode", "getLanguagePartFromCode", "formattedCode", "Intl", "getCanonicalLocales", "lowerCaseLng", "cleanCode", "isSupportedCode", "load", "nonExplicitSupportedLngs", "getBestMatchFromCodes", "cleanedLng", "lngScOnly", "lngOnly", "supportedLng", "fallbacks", "default", "fallbackCode", "fallbackCodes", "addCode", "fc", "suffixesOrder", "zero", "one", "two", "few", "many", "other", "dummyRule", "select", "resolvedOptions", "pluralCategories", "PluralResolver", "pluralRulesCache", "addRule", "rules", "clearCache", "getRule", "cleanedCode", "cache<PERSON>ey", "rule", "PluralRules", "err", "lngPart", "needsPlural", "getPluralFormsOfKey", "sort", "pluralCategory1", "pluralCategory2", "pluralCategory", "prepend", "deepFindWithDefaults", "regexSafe", "val", "Interpolator", "format", "escapeValue", "escape$1", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "resetRegExp", "getOrResetRegExp", "existingRegExp", "lastIndex", "regexp", "regexpUnescape", "replaces", "handleFormat", "interpolationkey", "trim", "f", "missingInterpolationHandler", "todos", "regex", "safeValue", "todo", "exec", "matchedVar", "temp", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "keyEndIndex", "lastIndexOf", "elem", "Boolean", "reduce", "parseFormatStr", "formatStr", "formatName", "formatOptions", "optStr", "currency", "range", "opts", "rest", "<PERSON><PERSON><PERSON>", "isNaN", "parseInt", "createCachedFormatter", "fn", "cache", "optForCache", "formatParams", "frm", "createNonCachedFormatter", "<PERSON><PERSON><PERSON>", "cf", "cacheInBuiltFormats", "formats", "number", "formatter", "NumberFormat", "style", "datetime", "DateTimeFormat", "relativetime", "RelativeTimeFormat", "list", "ListFormat", "add", "addCached", "findIndex", "mem", "formatted", "valOptions", "locale", "removePending", "q", "pending", "pendingCount", "Connector", "backend", "store", "waitingReads", "maxP<PERSON>llelReads", "readingCalls", "maxRetries", "retryTimeout", "state", "queue", "queueLoad", "languages", "callback", "toLoad", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "reload", "loaded", "errors", "done", "loadedKeys", "read", "fcName", "tried", "wait", "resolver", "setTimeout", "bind", "then", "catch", "prepareLoading", "loadOne", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "initAsync", "preload", "simplifyPluralSuffix", "partialBundledLanguages", "ret", "tDescription", "transformOptions", "initImmediate", "noop", "bindMemberFunctions", "inst", "mems", "getOwnPropertyNames", "getPrototypeOf", "I18n", "modules", "external", "isInitialized", "isClone", "isInitializing", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "usingLegacyFormatFunction", "languageDetector", "detection", "storeApi", "storeApiChained", "deferred", "finish", "initializedStoreOnce", "loadResources", "usedCallback", "append", "resolvedLanguage", "setResolvedLanguage", "reloadResources", "use", "Error", "li", "lngInLngs", "unshift", "isLanguageChangingTo", "setLngProps", "setLng", "fl", "cacheUserLanguage", "async", "detect", "getFixedT", "keyPrefix", "fixedT", "<PERSON><PERSON><PERSON>", "setDefaultNamespace", "lastLng", "loadNotPending", "loadState", "precheck", "preResult", "loadNamespaces", "loadLanguages", "preloaded", "newLngs", "dir", "Locale", "getTextInfo", "ti", "direction", "rtlLngs", "createInstance", "cloneInstance", "forkResourceStore", "mergedOptions", "membersToCopy", "clonedData", "prev", "acc", "instance"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/node_modules/i18next/dist/esm/i18next.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      const keyEndIndex = /{.*}/.test(match[1]) ? match[1].lastIndexOf('}') + 1 : match[1].indexOf(this.formatSeparator);\n      if (keyEndIndex !== -1) {\n        formatters = match[1].slice(keyEndIndex).split(this.formatSeparator).map(elem => elem.trim()).filter(Boolean);\n        match[1] = match[1].slice(0, keyEndIndex);\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (formatters.length) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      const usingLegacyFormatFunction = this.options.interpolation.format && this.options.interpolation.format !== defOpts.interpolation.format;\n      if (usingLegacyFormatFunction) {\n        this.logger.warn(`init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting`);\n      }\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        if (s.formatter.init) s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    try {\n      const l = new Intl.Locale(lng);\n      if (l && l.getTextInfo) {\n        const ti = l.getTextInfo();\n        if (ti && ti.direction) return ti.direction;\n      }\n    } catch (e) {}\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    if (lng.toLowerCase().indexOf('-latn') > 1) return 'ltr';\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ;AAC/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,IAAIC,GAAG;EACP,IAAIC,GAAG;EACP,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC/CL,GAAG,GAAGI,OAAO;IACbH,GAAG,GAAGI,MAAM;EACd,CAAC,CAAC;EACFH,OAAO,CAACE,OAAO,GAAGJ,GAAG;EACrBE,OAAO,CAACG,MAAM,GAAGJ,GAAG;EACpB,OAAOC,OAAO;AAChB,CAAC;AACD,MAAMI,UAAU,GAAGC,MAAM,IAAI;EAC3B,IAAIA,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE;EAC7B,OAAO,EAAE,GAAGA,MAAM;AACpB,CAAC;AACD,MAAMC,IAAI,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EACxBF,CAAC,CAACG,OAAO,CAACC,CAAC,IAAI;IACb,IAAIH,CAAC,CAACG,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;AACD,MAAMC,yBAAyB,GAAG,MAAM;AACxC,MAAMC,QAAQ,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGD,GAAG,CAACE,OAAO,CAACJ,yBAAyB,EAAE,GAAG,CAAC,GAAGE,GAAG;AAC1G,MAAMG,oBAAoB,GAAGZ,MAAM,IAAI,CAACA,MAAM,IAAIV,QAAQ,CAACU,MAAM,CAAC;AAClE,MAAMa,aAAa,GAAGA,CAACb,MAAM,EAAEc,IAAI,EAAEC,KAAK,KAAK;EAC7C,MAAMC,KAAK,GAAG,CAAC1B,QAAQ,CAACwB,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;EACtD,IAAIC,UAAU,GAAG,CAAC;EAClB,OAAOA,UAAU,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;IACpC,IAAIP,oBAAoB,CAACZ,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3C,MAAMS,GAAG,GAAGD,QAAQ,CAACQ,KAAK,CAACE,UAAU,CAAC,CAAC;IACvC,IAAI,CAAClB,MAAM,CAACS,GAAG,CAAC,IAAIM,KAAK,EAAEf,MAAM,CAACS,GAAG,CAAC,GAAG,IAAIM,KAAK,CAAC,CAAC;IACpD,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACvB,MAAM,EAAES,GAAG,CAAC,EAAE;MACrDT,MAAM,GAAGA,MAAM,CAACS,GAAG,CAAC;IACtB,CAAC,MAAM;MACLT,MAAM,GAAG,CAAC,CAAC;IACb;IACA,EAAEkB,UAAU;EACd;EACA,IAAIN,oBAAoB,CAACZ,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;EAC3C,OAAO;IACLT,GAAG,EAAES,MAAM;IACXwB,CAAC,EAAEhB,QAAQ,CAACQ,KAAK,CAACE,UAAU,CAAC;EAC/B,CAAC;AACH,CAAC;AACD,MAAMO,OAAO,GAAGA,CAACzB,MAAM,EAAEc,IAAI,EAAEY,QAAQ,KAAK;EAC1C,MAAM;IACJnC,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,EAAEM,MAAM,CAAC;EACvC,IAAI7B,GAAG,KAAKoC,SAAS,IAAIb,IAAI,CAACK,MAAM,KAAK,CAAC,EAAE;IAC1C5B,GAAG,CAACiC,CAAC,CAAC,GAAGE,QAAQ;IACjB;EACF;EACA,IAAIE,CAAC,GAAGd,IAAI,CAACA,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EAC7B,IAAIU,CAAC,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAEhB,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EACtC,IAAIY,IAAI,GAAGlB,aAAa,CAACb,MAAM,EAAE6B,CAAC,EAAET,MAAM,CAAC;EAC3C,OAAOW,IAAI,CAACxC,GAAG,KAAKoC,SAAS,IAAIE,CAAC,CAACV,MAAM,EAAE;IACzCS,CAAC,GAAG,GAAGC,CAAC,CAACA,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC,IAAIS,CAAC,EAAE;IAC7BC,CAAC,GAAGA,CAAC,CAACC,KAAK,CAAC,CAAC,EAAED,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;IAC5BY,IAAI,GAAGlB,aAAa,CAACb,MAAM,EAAE6B,CAAC,EAAET,MAAM,CAAC;IACvC,IAAIW,IAAI,EAAExC,GAAG,IAAI,OAAOwC,IAAI,CAACxC,GAAG,CAAC,GAAGwC,IAAI,CAACP,CAAC,IAAII,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;MAClEG,IAAI,CAACxC,GAAG,GAAGoC,SAAS;IACtB;EACF;EACAI,IAAI,CAACxC,GAAG,CAAC,GAAGwC,IAAI,CAACP,CAAC,IAAII,CAAC,EAAE,CAAC,GAAGF,QAAQ;AACvC,CAAC;AACD,MAAMM,QAAQ,GAAGA,CAAChC,MAAM,EAAEc,IAAI,EAAEY,QAAQ,EAAEO,MAAM,KAAK;EACnD,MAAM;IACJ1C,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,EAAEM,MAAM,CAAC;EACvC7B,GAAG,CAACiC,CAAC,CAAC,GAAGjC,GAAG,CAACiC,CAAC,CAAC,IAAI,EAAE;EACrBjC,GAAG,CAACiC,CAAC,CAAC,CAACU,IAAI,CAACR,QAAQ,CAAC;AACvB,CAAC;AACD,MAAMS,OAAO,GAAGA,CAACnC,MAAM,EAAEc,IAAI,KAAK;EAChC,MAAM;IACJvB,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,CAAC;EAC/B,IAAI,CAACvB,GAAG,EAAE,OAAOoC,SAAS;EAC1B,IAAI,CAACP,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChC,GAAG,EAAEiC,CAAC,CAAC,EAAE,OAAOG,SAAS;EACnE,OAAOpC,GAAG,CAACiC,CAAC,CAAC;AACf,CAAC;AACD,MAAMY,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,WAAW,EAAE7B,GAAG,KAAK;EACtD,MAAM8B,KAAK,GAAGJ,OAAO,CAACE,IAAI,EAAE5B,GAAG,CAAC;EAChC,IAAI8B,KAAK,KAAKZ,SAAS,EAAE;IACvB,OAAOY,KAAK;EACd;EACA,OAAOJ,OAAO,CAACG,WAAW,EAAE7B,GAAG,CAAC;AAClC,CAAC;AACD,MAAM+B,UAAU,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,SAAS,KAAK;EAChD,KAAK,MAAMC,IAAI,IAAIF,MAAM,EAAE;IACzB,IAAIE,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,aAAa,EAAE;MAClD,IAAIA,IAAI,IAAIH,MAAM,EAAE;QAClB,IAAInD,QAAQ,CAACmD,MAAM,CAACG,IAAI,CAAC,CAAC,IAAIH,MAAM,CAACG,IAAI,CAAC,YAAYC,MAAM,IAAIvD,QAAQ,CAACoD,MAAM,CAACE,IAAI,CAAC,CAAC,IAAIF,MAAM,CAACE,IAAI,CAAC,YAAYC,MAAM,EAAE;UACxH,IAAIF,SAAS,EAAEF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;QAC5C,CAAC,MAAM;UACLJ,UAAU,CAACC,MAAM,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACE,IAAI,CAAC,EAAED,SAAS,CAAC;QACnD;MACF,CAAC,MAAM;QACLF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;MAC7B;IACF;EACF;EACA,OAAOH,MAAM;AACf,CAAC;AACD,MAAMK,WAAW,GAAGC,GAAG,IAAIA,GAAG,CAACpC,OAAO,CAAC,qCAAqC,EAAE,MAAM,CAAC;AACrF,IAAIqC,UAAU,GAAG;EACf,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,MAAM,GAAGZ,IAAI,IAAI;EACrB,IAAI/C,QAAQ,CAAC+C,IAAI,CAAC,EAAE;IAClB,OAAOA,IAAI,CAAC1B,OAAO,CAAC,YAAY,EAAER,CAAC,IAAI6C,UAAU,CAAC7C,CAAC,CAAC,CAAC;EACvD;EACA,OAAOkC,IAAI;AACb,CAAC;AACD,MAAMa,WAAW,CAAC;EAChBC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,EAAE;EACvB;EACAC,SAASA,CAACC,OAAO,EAAE;IACjB,MAAMC,eAAe,GAAG,IAAI,CAACL,SAAS,CAACM,GAAG,CAACF,OAAO,CAAC;IACnD,IAAIC,eAAe,KAAK/B,SAAS,EAAE;MACjC,OAAO+B,eAAe;IACxB;IACA,MAAME,SAAS,GAAG,IAAIC,MAAM,CAACJ,OAAO,CAAC;IACrC,IAAI,IAAI,CAACF,WAAW,CAACpC,MAAM,KAAK,IAAI,CAACiC,QAAQ,EAAE;MAC7C,IAAI,CAACC,SAAS,CAACS,MAAM,CAAC,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC,CAAC,CAAC;IACjD;IACA,IAAI,CAACV,SAAS,CAACW,GAAG,CAACP,OAAO,EAAEG,SAAS,CAAC;IACtC,IAAI,CAACL,WAAW,CAACrB,IAAI,CAACuB,OAAO,CAAC;IAC9B,OAAOG,SAAS;EAClB;AACF;AACA,MAAMK,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACvC,MAAMC,8BAA8B,GAAG,IAAIhB,WAAW,CAAC,EAAE,CAAC;AAC1D,MAAMiB,mBAAmB,GAAGA,CAAC1D,GAAG,EAAE2D,WAAW,EAAEC,YAAY,KAAK;EAC9DD,WAAW,GAAGA,WAAW,IAAI,EAAE;EAC/BC,YAAY,GAAGA,YAAY,IAAI,EAAE;EACjC,MAAMC,aAAa,GAAGL,KAAK,CAACM,MAAM,CAACC,CAAC,IAAIJ,WAAW,CAAC1D,OAAO,CAAC8D,CAAC,CAAC,GAAG,CAAC,IAAIH,YAAY,CAAC3D,OAAO,CAAC8D,CAAC,CAAC,GAAG,CAAC,CAAC;EAClG,IAAIF,aAAa,CAACnD,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAC3C,MAAMsD,CAAC,GAAGP,8BAA8B,CAACV,SAAS,CAAC,IAAIc,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,KAAK,GAAG,GAAG,KAAK,GAAGA,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EAClH,IAAIC,OAAO,GAAG,CAACH,CAAC,CAACI,IAAI,CAACpE,GAAG,CAAC;EAC1B,IAAI,CAACmE,OAAO,EAAE;IACZ,MAAME,EAAE,GAAGrE,GAAG,CAACC,OAAO,CAAC2D,YAAY,CAAC;IACpC,IAAIS,EAAE,GAAG,CAAC,IAAI,CAACL,CAAC,CAACI,IAAI,CAACpE,GAAG,CAACsE,SAAS,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,EAAE;MAC3CF,OAAO,GAAG,IAAI;IAChB;EACF;EACA,OAAOA,OAAO;AAChB,CAAC;AACD,MAAMI,QAAQ,GAAGA,CAACzF,GAAG,EAAEuB,IAAI,EAAEuD,YAAY,GAAG,GAAG,KAAK;EAClD,IAAI,CAAC9E,GAAG,EAAE,OAAOoC,SAAS;EAC1B,IAAIpC,GAAG,CAACuB,IAAI,CAAC,EAAE;IACb,IAAI,CAACM,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChC,GAAG,EAAEuB,IAAI,CAAC,EAAE,OAAOa,SAAS;IACtE,OAAOpC,GAAG,CAACuB,IAAI,CAAC;EAClB;EACA,MAAMmE,MAAM,GAAGnE,IAAI,CAACG,KAAK,CAACoD,YAAY,CAAC;EACvC,IAAIa,OAAO,GAAG3F,GAAG;EACjB,KAAK,IAAI4F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAC9D,MAAM,GAAG;IAClC,IAAI,CAAC+D,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC3C,OAAOvD,SAAS;IAClB;IACA,IAAIyD,IAAI;IACR,IAAIC,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAGH,CAAC,EAAEG,CAAC,GAAGL,MAAM,CAAC9D,MAAM,EAAE,EAAEmE,CAAC,EAAE;MACtC,IAAIA,CAAC,KAAKH,CAAC,EAAE;QACXE,QAAQ,IAAIhB,YAAY;MAC1B;MACAgB,QAAQ,IAAIJ,MAAM,CAACK,CAAC,CAAC;MACrBF,IAAI,GAAGF,OAAO,CAACG,QAAQ,CAAC;MACxB,IAAID,IAAI,KAAKzD,SAAS,EAAE;QACtB,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACjB,OAAO,CAAC,OAAO0E,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAGL,MAAM,CAAC9D,MAAM,GAAG,CAAC,EAAE;UACtF;QACF;QACAgE,CAAC,IAAIG,CAAC,GAAGH,CAAC,GAAG,CAAC;QACd;MACF;IACF;IACAD,OAAO,GAAGE,IAAI;EAChB;EACA,OAAOF,OAAO;AAChB,CAAC;AACD,MAAMK,cAAc,GAAGC,IAAI,IAAIA,IAAI,EAAE7E,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAEtD,MAAM8E,aAAa,GAAG;EACpBC,IAAI,EAAE,QAAQ;EACdC,GAAGA,CAACC,IAAI,EAAE;IACR,IAAI,CAACC,MAAM,CAAC,KAAK,EAAED,IAAI,CAAC;EAC1B,CAAC;EACDE,IAAIA,CAACF,IAAI,EAAE;IACT,IAAI,CAACC,MAAM,CAAC,MAAM,EAAED,IAAI,CAAC;EAC3B,CAAC;EACDG,KAAKA,CAACH,IAAI,EAAE;IACV,IAAI,CAACC,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;EAC5B,CAAC;EACDC,MAAMA,CAACH,IAAI,EAAEE,IAAI,EAAE;IACjBI,OAAO,GAAGN,IAAI,CAAC,EAAEO,KAAK,GAAGD,OAAO,EAAEJ,IAAI,CAAC;EACzC;AACF,CAAC;AACD,MAAMM,MAAM,CAAC;EACX/C,WAAWA,CAACgD,cAAc,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,IAAI,CAACC,IAAI,CAACF,cAAc,EAAEC,OAAO,CAAC;EACpC;EACAC,IAAIA,CAACF,cAAc,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,IAAI,CAACE,MAAM,GAAGF,OAAO,CAACE,MAAM,IAAI,UAAU;IAC1C,IAAI,CAACC,MAAM,GAAGJ,cAAc,IAAIV,aAAa;IAC7C,IAAI,CAACW,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;EAC5B;EACAb,GAAGA,CAAC,GAAGC,IAAI,EAAE;IACX,OAAO,IAAI,CAACa,OAAO,CAACb,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;EAC5C;EACAE,IAAIA,CAAC,GAAGF,IAAI,EAAE;IACZ,OAAO,IAAI,CAACa,OAAO,CAACb,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;EAC7C;EACAG,KAAKA,CAAC,GAAGH,IAAI,EAAE;IACb,OAAO,IAAI,CAACa,OAAO,CAACb,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC;EACxC;EACAc,SAASA,CAAC,GAAGd,IAAI,EAAE;IACjB,OAAO,IAAI,CAACa,OAAO,CAACb,IAAI,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,CAAC;EACjE;EACAa,OAAOA,CAACb,IAAI,EAAEe,GAAG,EAAEL,MAAM,EAAEM,SAAS,EAAE;IACpC,IAAIA,SAAS,IAAI,CAAC,IAAI,CAACJ,KAAK,EAAE,OAAO,IAAI;IACzC,IAAIlH,QAAQ,CAACsG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAGU,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIV,IAAI,CAAC,CAAC,CAAC,EAAE;IACrE,OAAO,IAAI,CAACW,MAAM,CAACI,GAAG,CAAC,CAACf,IAAI,CAAC;EAC/B;EACAiB,MAAMA,CAACC,UAAU,EAAE;IACjB,OAAO,IAAIZ,MAAM,CAAC,IAAI,CAACK,MAAM,EAAE;MAC7B,GAAG;QACDD,MAAM,EAAE,GAAG,IAAI,CAACA,MAAM,IAAIQ,UAAU;MACtC,CAAC;MACD,GAAG,IAAI,CAACV;IACV,CAAC,CAAC;EACJ;EACAW,KAAKA,CAACX,OAAO,EAAE;IACbA,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACA,OAAO;IACjCA,OAAO,CAACE,MAAM,GAAGF,OAAO,CAACE,MAAM,IAAI,IAAI,CAACA,MAAM;IAC9C,OAAO,IAAIJ,MAAM,CAAC,IAAI,CAACK,MAAM,EAAEH,OAAO,CAAC;EACzC;AACF;AACA,IAAIY,UAAU,GAAG,IAAId,MAAM,CAAC,CAAC;AAE7B,MAAMe,YAAY,CAAC;EACjB9D,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC+D,SAAS,GAAG,CAAC,CAAC;EACrB;EACAC,EAAEA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IACnBD,MAAM,CAACnG,KAAK,CAAC,GAAG,CAAC,CAACZ,OAAO,CAACiH,KAAK,IAAI;MACjC,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,EAAE,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,GAAG,IAAIhE,GAAG,CAAC,CAAC;MAC7D,MAAMiE,YAAY,GAAG,IAAI,CAACL,SAAS,CAACI,KAAK,CAAC,CAAC3D,GAAG,CAAC0D,QAAQ,CAAC,IAAI,CAAC;MAC7D,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC,CAACtD,GAAG,CAACqD,QAAQ,EAAEE,YAAY,GAAG,CAAC,CAAC;IACvD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACAC,GAAGA,CAACF,KAAK,EAAED,QAAQ,EAAE;IACnB,IAAI,CAAC,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC,EAAE;IAC5B,IAAI,CAACD,QAAQ,EAAE;MACb,OAAO,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC;MAC5B;IACF;IACA,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,CAACxD,MAAM,CAACuD,QAAQ,CAAC;EACxC;EACAI,IAAIA,CAACH,KAAK,EAAE,GAAG1B,IAAI,EAAE;IACnB,IAAI,IAAI,CAACsB,SAAS,CAACI,KAAK,CAAC,EAAE;MACzB,MAAMI,MAAM,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACV,SAAS,CAACI,KAAK,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC;MAC1DH,MAAM,CAACrH,OAAO,CAAC,CAAC,CAACyH,QAAQ,EAAEC,aAAa,CAAC,KAAK;QAC5C,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,aAAa,EAAE5C,CAAC,EAAE,EAAE;UACtC2C,QAAQ,CAAC,GAAGlC,IAAI,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAACsB,SAAS,CAAC,GAAG,CAAC,EAAE;MACvB,MAAMQ,MAAM,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACV,SAAS,CAAC,GAAG,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC;MACxDH,MAAM,CAACrH,OAAO,CAAC,CAAC,CAACyH,QAAQ,EAAEC,aAAa,CAAC,KAAK;QAC5C,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,aAAa,EAAE5C,CAAC,EAAE,EAAE;UACtC2C,QAAQ,CAAC7B,KAAK,CAAC6B,QAAQ,EAAE,CAACR,KAAK,EAAE,GAAG1B,IAAI,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;IACJ;EACF;AACF;AAEA,MAAMoC,aAAa,SAASf,YAAY,CAAC;EACvC9D,WAAWA,CAACd,IAAI,EAAE+D,OAAO,GAAG;IAC1B6B,EAAE,EAAE,CAAC,aAAa,CAAC;IACnBC,SAAS,EAAE;EACb,CAAC,EAAE;IACD,KAAK,CAAC,CAAC;IACP,IAAI,CAAC7F,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC+D,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAAC/B,YAAY,KAAK1C,SAAS,EAAE;MAC3C,IAAI,CAACyE,OAAO,CAAC/B,YAAY,GAAG,GAAG;IACjC;IACA,IAAI,IAAI,CAAC+B,OAAO,CAAC+B,mBAAmB,KAAKxG,SAAS,EAAE;MAClD,IAAI,CAACyE,OAAO,CAAC+B,mBAAmB,GAAG,IAAI;IACzC;EACF;EACAC,aAAaA,CAACH,EAAE,EAAE;IAChB,IAAI,IAAI,CAAC7B,OAAO,CAAC6B,EAAE,CAACvH,OAAO,CAACuH,EAAE,CAAC,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC7B,OAAO,CAAC6B,EAAE,CAAC/F,IAAI,CAAC+F,EAAE,CAAC;IAC1B;EACF;EACAI,gBAAgBA,CAACJ,EAAE,EAAE;IACnB,MAAMK,KAAK,GAAG,IAAI,CAAClC,OAAO,CAAC6B,EAAE,CAACvH,OAAO,CAACuH,EAAE,CAAC;IACzC,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAClC,OAAO,CAAC6B,EAAE,CAACM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAClC;EACF;EACAE,WAAWA,CAACC,GAAG,EAAER,EAAE,EAAExH,GAAG,EAAE2F,OAAO,GAAG,CAAC,CAAC,EAAE;IACtC,MAAM/B,YAAY,GAAG+B,OAAO,CAAC/B,YAAY,KAAK1C,SAAS,GAAGyE,OAAO,CAAC/B,YAAY,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,YAAY;IAC1G,MAAM8D,mBAAmB,GAAG/B,OAAO,CAAC+B,mBAAmB,KAAKxG,SAAS,GAAGyE,OAAO,CAAC+B,mBAAmB,GAAG,IAAI,CAAC/B,OAAO,CAAC+B,mBAAmB;IACtI,IAAIrH,IAAI;IACR,IAAI2H,GAAG,CAAC/H,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAG2H,GAAG,CAACxH,KAAK,CAAC,GAAG,CAAC;IACvB,CAAC,MAAM;MACLH,IAAI,GAAG,CAAC2H,GAAG,EAAER,EAAE,CAAC;MAChB,IAAIxH,GAAG,EAAE;QACP,IAAIkH,KAAK,CAACe,OAAO,CAACjI,GAAG,CAAC,EAAE;UACtBK,IAAI,CAACoB,IAAI,CAAC,GAAGzB,GAAG,CAAC;QACnB,CAAC,MAAM,IAAInB,QAAQ,CAACmB,GAAG,CAAC,IAAI4D,YAAY,EAAE;UACxCvD,IAAI,CAACoB,IAAI,CAAC,GAAGzB,GAAG,CAACQ,KAAK,CAACoD,YAAY,CAAC,CAAC;QACvC,CAAC,MAAM;UACLvD,IAAI,CAACoB,IAAI,CAACzB,GAAG,CAAC;QAChB;MACF;IACF;IACA,MAAMkI,MAAM,GAAGxG,OAAO,CAAC,IAAI,CAACE,IAAI,EAAEvB,IAAI,CAAC;IACvC,IAAI,CAAC6H,MAAM,IAAI,CAACV,EAAE,IAAI,CAACxH,GAAG,IAAIgI,GAAG,CAAC/H,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACnD+H,GAAG,GAAG3H,IAAI,CAAC,CAAC,CAAC;MACbmH,EAAE,GAAGnH,IAAI,CAAC,CAAC,CAAC;MACZL,GAAG,GAAGK,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC6C,IAAI,CAAC,GAAG,CAAC;IAC/B;IACA,IAAIgE,MAAM,IAAI,CAACR,mBAAmB,IAAI,CAAC7I,QAAQ,CAACmB,GAAG,CAAC,EAAE,OAAOkI,MAAM;IACnE,OAAO3D,QAAQ,CAAC,IAAI,CAAC3C,IAAI,GAAGoG,GAAG,CAAC,GAAGR,EAAE,CAAC,EAAExH,GAAG,EAAE4D,YAAY,CAAC;EAC5D;EACAuE,WAAWA,CAACH,GAAG,EAAER,EAAE,EAAExH,GAAG,EAAE8B,KAAK,EAAE6D,OAAO,GAAG;IACzCyC,MAAM,EAAE;EACV,CAAC,EAAE;IACD,MAAMxE,YAAY,GAAG+B,OAAO,CAAC/B,YAAY,KAAK1C,SAAS,GAAGyE,OAAO,CAAC/B,YAAY,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,YAAY;IAC1G,IAAIvD,IAAI,GAAG,CAAC2H,GAAG,EAAER,EAAE,CAAC;IACpB,IAAIxH,GAAG,EAAEK,IAAI,GAAGA,IAAI,CAACmB,MAAM,CAACoC,YAAY,GAAG5D,GAAG,CAACQ,KAAK,CAACoD,YAAY,CAAC,GAAG5D,GAAG,CAAC;IACzE,IAAIgI,GAAG,CAAC/H,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAG2H,GAAG,CAACxH,KAAK,CAAC,GAAG,CAAC;MACrBsB,KAAK,GAAG0F,EAAE;MACVA,EAAE,GAAGnH,IAAI,CAAC,CAAC,CAAC;IACd;IACA,IAAI,CAACsH,aAAa,CAACH,EAAE,CAAC;IACtBxG,OAAO,CAAC,IAAI,CAACY,IAAI,EAAEvB,IAAI,EAAEyB,KAAK,CAAC;IAC/B,IAAI,CAAC6D,OAAO,CAACyC,MAAM,EAAE,IAAI,CAACpB,IAAI,CAAC,OAAO,EAAEgB,GAAG,EAAER,EAAE,EAAExH,GAAG,EAAE8B,KAAK,CAAC;EAC9D;EACAuG,YAAYA,CAACL,GAAG,EAAER,EAAE,EAAEc,SAAS,EAAE3C,OAAO,GAAG;IACzCyC,MAAM,EAAE;EACV,CAAC,EAAE;IACD,KAAK,MAAMvI,CAAC,IAAIyI,SAAS,EAAE;MACzB,IAAIzJ,QAAQ,CAACyJ,SAAS,CAACzI,CAAC,CAAC,CAAC,IAAIqH,KAAK,CAACe,OAAO,CAACK,SAAS,CAACzI,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsI,WAAW,CAACH,GAAG,EAAER,EAAE,EAAE3H,CAAC,EAAEyI,SAAS,CAACzI,CAAC,CAAC,EAAE;QACpGuI,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA,IAAI,CAACzC,OAAO,CAACyC,MAAM,EAAE,IAAI,CAACpB,IAAI,CAAC,OAAO,EAAEgB,GAAG,EAAER,EAAE,EAAEc,SAAS,CAAC;EAC7D;EACAC,iBAAiBA,CAACP,GAAG,EAAER,EAAE,EAAEc,SAAS,EAAEE,IAAI,EAAEtG,SAAS,EAAEyD,OAAO,GAAG;IAC/DyC,MAAM,EAAE,KAAK;IACbK,QAAQ,EAAE;EACZ,CAAC,EAAE;IACD,IAAIpI,IAAI,GAAG,CAAC2H,GAAG,EAAER,EAAE,CAAC;IACpB,IAAIQ,GAAG,CAAC/H,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAG2H,GAAG,CAACxH,KAAK,CAAC,GAAG,CAAC;MACrBgI,IAAI,GAAGF,SAAS;MAChBA,SAAS,GAAGd,EAAE;MACdA,EAAE,GAAGnH,IAAI,CAAC,CAAC,CAAC;IACd;IACA,IAAI,CAACsH,aAAa,CAACH,EAAE,CAAC;IACtB,IAAIkB,IAAI,GAAGhH,OAAO,CAAC,IAAI,CAACE,IAAI,EAAEvB,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,CAACsF,OAAO,CAAC8C,QAAQ,EAAEH,SAAS,GAAGK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACP,SAAS,CAAC,CAAC;IACxE,IAAIE,IAAI,EAAE;MACRzG,UAAU,CAAC2G,IAAI,EAAEJ,SAAS,EAAEpG,SAAS,CAAC;IACxC,CAAC,MAAM;MACLwG,IAAI,GAAG;QACL,GAAGA,IAAI;QACP,GAAGJ;MACL,CAAC;IACH;IACAtH,OAAO,CAAC,IAAI,CAACY,IAAI,EAAEvB,IAAI,EAAEqI,IAAI,CAAC;IAC9B,IAAI,CAAC/C,OAAO,CAACyC,MAAM,EAAE,IAAI,CAACpB,IAAI,CAAC,OAAO,EAAEgB,GAAG,EAAER,EAAE,EAAEc,SAAS,CAAC;EAC7D;EACAQ,oBAAoBA,CAACd,GAAG,EAAER,EAAE,EAAE;IAC5B,IAAI,IAAI,CAACuB,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE;MACnC,OAAO,IAAI,CAAC5F,IAAI,CAACoG,GAAG,CAAC,CAACR,EAAE,CAAC;IAC3B;IACA,IAAI,CAACI,gBAAgB,CAACJ,EAAE,CAAC;IACzB,IAAI,CAACR,IAAI,CAAC,SAAS,EAAEgB,GAAG,EAAER,EAAE,CAAC;EAC/B;EACAuB,iBAAiBA,CAACf,GAAG,EAAER,EAAE,EAAE;IACzB,OAAO,IAAI,CAACO,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC,KAAKtG,SAAS;EAChD;EACA8H,iBAAiBA,CAAChB,GAAG,EAAER,EAAE,EAAE;IACzB,IAAI,CAACA,EAAE,EAAEA,EAAE,GAAG,IAAI,CAAC7B,OAAO,CAAC8B,SAAS;IACpC,OAAO,IAAI,CAACM,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC;EAClC;EACAyB,iBAAiBA,CAACjB,GAAG,EAAE;IACrB,OAAO,IAAI,CAACpG,IAAI,CAACoG,GAAG,CAAC;EACvB;EACAkB,2BAA2BA,CAAClB,GAAG,EAAE;IAC/B,MAAMpG,IAAI,GAAG,IAAI,CAACqH,iBAAiB,CAACjB,GAAG,CAAC;IACxC,MAAMmB,CAAC,GAAGvH,IAAI,IAAIjB,MAAM,CAACyI,IAAI,CAACxH,IAAI,CAAC,IAAI,EAAE;IACzC,OAAO,CAAC,CAACuH,CAAC,CAACE,IAAI,CAACC,CAAC,IAAI1H,IAAI,CAAC0H,CAAC,CAAC,IAAI3I,MAAM,CAACyI,IAAI,CAACxH,IAAI,CAAC0H,CAAC,CAAC,CAAC,CAAC5I,MAAM,GAAG,CAAC,CAAC;EAClE;EACA6I,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC3H,IAAI;EAClB;AACF;AAEA,IAAI4H,aAAa,GAAG;EAClBC,UAAU,EAAE,CAAC,CAAC;EACdC,gBAAgBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACF,UAAU,CAACE,MAAM,CAACC,IAAI,CAAC,GAAGD,MAAM;EACvC,CAAC;EACDE,MAAMA,CAACJ,UAAU,EAAE3H,KAAK,EAAE9B,GAAG,EAAE2F,OAAO,EAAEmE,UAAU,EAAE;IAClDL,UAAU,CAAC7J,OAAO,CAACmK,SAAS,IAAI;MAC9BjI,KAAK,GAAG,IAAI,CAAC2H,UAAU,CAACM,SAAS,CAAC,EAAEC,OAAO,CAAClI,KAAK,EAAE9B,GAAG,EAAE2F,OAAO,EAAEmE,UAAU,CAAC,IAAIhI,KAAK;IACvF,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;AACF,CAAC;AAED,MAAMmI,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAMC,oBAAoB,GAAGlL,GAAG,IAAI,CAACH,QAAQ,CAACG,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,QAAQ;AACzG,MAAMmL,UAAU,SAAS3D,YAAY,CAAC;EACpC9D,WAAWA,CAAC0H,QAAQ,EAAEzE,OAAO,GAAG,CAAC,CAAC,EAAE;IAClC,KAAK,CAAC,CAAC;IACPnG,IAAI,CAAC,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE4K,QAAQ,EAAE,IAAI,CAAC;IACrI,IAAI,CAACzE,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAAC/B,YAAY,KAAK1C,SAAS,EAAE;MAC3C,IAAI,CAACyE,OAAO,CAAC/B,YAAY,GAAG,GAAG;IACjC;IACA,IAAI,CAACkC,MAAM,GAAGS,UAAU,CAACH,MAAM,CAAC,YAAY,CAAC;EAC/C;EACAiE,cAAcA,CAACrC,GAAG,EAAE;IAClB,IAAIA,GAAG,EAAE,IAAI,CAACsC,QAAQ,GAAGtC,GAAG;EAC9B;EACAuC,MAAMA,CAACvK,GAAG,EAAEwK,CAAC,GAAG;IACdC,aAAa,EAAE,CAAC;EAClB,CAAC,EAAE;IACD,MAAMC,GAAG,GAAG;MACV,GAAGF;IACL,CAAC;IACD,IAAIxK,GAAG,IAAI,IAAI,EAAE,OAAO,KAAK;IAC7B,MAAM2K,QAAQ,GAAG,IAAI,CAACvL,OAAO,CAACY,GAAG,EAAE0K,GAAG,CAAC;IACvC,OAAOC,QAAQ,EAAE3L,GAAG,KAAKkC,SAAS;EACpC;EACA0J,cAAcA,CAAC5K,GAAG,EAAE0K,GAAG,EAAE;IACvB,IAAI/G,WAAW,GAAG+G,GAAG,CAAC/G,WAAW,KAAKzC,SAAS,GAAGwJ,GAAG,CAAC/G,WAAW,GAAG,IAAI,CAACgC,OAAO,CAAChC,WAAW;IAC5F,IAAIA,WAAW,KAAKzC,SAAS,EAAEyC,WAAW,GAAG,GAAG;IAChD,MAAMC,YAAY,GAAG8G,GAAG,CAAC9G,YAAY,KAAK1C,SAAS,GAAGwJ,GAAG,CAAC9G,YAAY,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,YAAY;IAClG,IAAIiH,UAAU,GAAGH,GAAG,CAAClD,EAAE,IAAI,IAAI,CAAC7B,OAAO,CAAC8B,SAAS,IAAI,EAAE;IACvD,MAAMqD,oBAAoB,GAAGnH,WAAW,IAAI3D,GAAG,CAACC,OAAO,CAAC0D,WAAW,CAAC,GAAG,CAAC,CAAC;IACzE,MAAMoH,oBAAoB,GAAG,CAAC,IAAI,CAACpF,OAAO,CAACqF,uBAAuB,IAAI,CAACN,GAAG,CAAC9G,YAAY,IAAI,CAAC,IAAI,CAAC+B,OAAO,CAACsF,sBAAsB,IAAI,CAACP,GAAG,CAAC/G,WAAW,IAAI,CAACD,mBAAmB,CAAC1D,GAAG,EAAE2D,WAAW,EAAEC,YAAY,CAAC;IAC3M,IAAIkH,oBAAoB,IAAI,CAACC,oBAAoB,EAAE;MACjD,MAAMlL,CAAC,GAAGG,GAAG,CAACkL,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;MACpD,IAAIvL,CAAC,IAAIA,CAAC,CAACa,MAAM,GAAG,CAAC,EAAE;QACrB,OAAO;UACLV,GAAG;UACH6K,UAAU,EAAEhM,QAAQ,CAACgM,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA;QACpD,CAAC;MACH;MACA,MAAMQ,KAAK,GAAGrL,GAAG,CAACQ,KAAK,CAACmD,WAAW,CAAC;MACpC,IAAIA,WAAW,KAAKC,YAAY,IAAID,WAAW,KAAKC,YAAY,IAAI,IAAI,CAAC+B,OAAO,CAAC6B,EAAE,CAACvH,OAAO,CAACoL,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAER,UAAU,GAAGQ,KAAK,CAAC/H,KAAK,CAAC,CAAC;MACtItD,GAAG,GAAGqL,KAAK,CAACnH,IAAI,CAACN,YAAY,CAAC;IAChC;IACA,OAAO;MACL5D,GAAG;MACH6K,UAAU,EAAEhM,QAAQ,CAACgM,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA;IACpD,CAAC;EACH;EACAS,SAASA,CAAClC,IAAI,EAAEoB,CAAC,EAAEe,OAAO,EAAE;IAC1B,IAAIb,GAAG,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAG;MAChC,GAAGA;IACL,CAAC,GAAGA,CAAC;IACL,IAAI,OAAOE,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC/E,OAAO,CAAC6F,gCAAgC,EAAE;MAC5Ed,GAAG,GAAG,IAAI,CAAC/E,OAAO,CAAC6F,gCAAgC,CAACC,SAAS,CAAC;IAChE;IACA,IAAI,OAAO9F,OAAO,KAAK,QAAQ,EAAE+E,GAAG,GAAG;MACrC,GAAGA;IACL,CAAC;IACD,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;IAClB,IAAItB,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE;IAC3B,IAAI,CAAClC,KAAK,CAACe,OAAO,CAACmB,IAAI,CAAC,EAAEA,IAAI,GAAG,CAAChH,MAAM,CAACgH,IAAI,CAAC,CAAC;IAC/C,MAAMsC,aAAa,GAAGhB,GAAG,CAACgB,aAAa,KAAKxK,SAAS,GAAGwJ,GAAG,CAACgB,aAAa,GAAG,IAAI,CAAC/F,OAAO,CAAC+F,aAAa;IACtG,MAAM9H,YAAY,GAAG8G,GAAG,CAAC9G,YAAY,KAAK1C,SAAS,GAAGwJ,GAAG,CAAC9G,YAAY,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,YAAY;IAClG,MAAM;MACJ5D,GAAG;MACH6K;IACF,CAAC,GAAG,IAAI,CAACD,cAAc,CAACxB,IAAI,CAACA,IAAI,CAAC1I,MAAM,GAAG,CAAC,CAAC,EAAEgK,GAAG,CAAC;IACnD,MAAMiB,SAAS,GAAGd,UAAU,CAACA,UAAU,CAACnK,MAAM,GAAG,CAAC,CAAC;IACnD,IAAIiD,WAAW,GAAG+G,GAAG,CAAC/G,WAAW,KAAKzC,SAAS,GAAGwJ,GAAG,CAAC/G,WAAW,GAAG,IAAI,CAACgC,OAAO,CAAChC,WAAW;IAC5F,IAAIA,WAAW,KAAKzC,SAAS,EAAEyC,WAAW,GAAG,GAAG;IAChD,MAAMqE,GAAG,GAAG0C,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ;IACpC,MAAMsB,uBAAuB,GAAGlB,GAAG,CAACkB,uBAAuB,IAAI,IAAI,CAACjG,OAAO,CAACiG,uBAAuB;IACnG,IAAI5D,GAAG,EAAE6D,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MACnC,IAAID,uBAAuB,EAAE;QAC3B,IAAIF,aAAa,EAAE;UACjB,OAAO;YACL1M,GAAG,EAAE,GAAG2M,SAAS,GAAGhI,WAAW,GAAG3D,GAAG,EAAE;YACvC8L,OAAO,EAAE9L,GAAG;YACZ+L,YAAY,EAAE/L,GAAG;YACjBgM,OAAO,EAAEhE,GAAG;YACZiE,MAAM,EAAEN,SAAS;YACjBO,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACzB,GAAG;UAC3C,CAAC;QACH;QACA,OAAO,GAAGiB,SAAS,GAAGhI,WAAW,GAAG3D,GAAG,EAAE;MAC3C;MACA,IAAI0L,aAAa,EAAE;QACjB,OAAO;UACL1M,GAAG,EAAEgB,GAAG;UACR8L,OAAO,EAAE9L,GAAG;UACZ+L,YAAY,EAAE/L,GAAG;UACjBgM,OAAO,EAAEhE,GAAG;UACZiE,MAAM,EAAEN,SAAS;UACjBO,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACzB,GAAG;QAC3C,CAAC;MACH;MACA,OAAO1K,GAAG;IACZ;IACA,MAAM2K,QAAQ,GAAG,IAAI,CAACvL,OAAO,CAACgK,IAAI,EAAEsB,GAAG,CAAC;IACxC,IAAI1L,GAAG,GAAG2L,QAAQ,EAAE3L,GAAG;IACvB,MAAMoN,UAAU,GAAGzB,QAAQ,EAAEmB,OAAO,IAAI9L,GAAG;IAC3C,MAAMqM,eAAe,GAAG1B,QAAQ,EAAEoB,YAAY,IAAI/L,GAAG;IACrD,MAAMsM,QAAQ,GAAG,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;IAC5E,MAAMC,UAAU,GAAG7B,GAAG,CAAC6B,UAAU,KAAKrL,SAAS,GAAGwJ,GAAG,CAAC6B,UAAU,GAAG,IAAI,CAAC5G,OAAO,CAAC4G,UAAU;IAC1F,MAAMC,0BAA0B,GAAG,CAAC,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACC,cAAc;IACrF,MAAMC,mBAAmB,GAAGjC,GAAG,CAACkC,KAAK,KAAK1L,SAAS,IAAI,CAACrC,QAAQ,CAAC6L,GAAG,CAACkC,KAAK,CAAC;IAC3E,MAAMC,eAAe,GAAG1C,UAAU,CAAC0C,eAAe,CAACnC,GAAG,CAAC;IACvD,MAAMoC,kBAAkB,GAAGH,mBAAmB,GAAG,IAAI,CAACI,cAAc,CAACC,SAAS,CAAChF,GAAG,EAAE0C,GAAG,CAACkC,KAAK,EAAElC,GAAG,CAAC,GAAG,EAAE;IACxG,MAAMuC,iCAAiC,GAAGvC,GAAG,CAACwC,OAAO,IAAIP,mBAAmB,GAAG,IAAI,CAACI,cAAc,CAACC,SAAS,CAAChF,GAAG,EAAE0C,GAAG,CAACkC,KAAK,EAAE;MAC3HM,OAAO,EAAE;IACX,CAAC,CAAC,GAAG,EAAE;IACP,MAAMC,qBAAqB,GAAGR,mBAAmB,IAAI,CAACjC,GAAG,CAACwC,OAAO,IAAIxC,GAAG,CAACkC,KAAK,KAAK,CAAC;IACpF,MAAMQ,YAAY,GAAGD,qBAAqB,IAAIzC,GAAG,CAAC,eAAe,IAAI,CAAC/E,OAAO,CAAC0H,eAAe,MAAM,CAAC,IAAI3C,GAAG,CAAC,eAAeoC,kBAAkB,EAAE,CAAC,IAAIpC,GAAG,CAAC,eAAeuC,iCAAiC,EAAE,CAAC,IAAIvC,GAAG,CAAC0C,YAAY;IAC/N,IAAIE,aAAa,GAAGtO,GAAG;IACvB,IAAIwN,0BAA0B,IAAI,CAACxN,GAAG,IAAI6N,eAAe,EAAE;MACzDS,aAAa,GAAGF,YAAY;IAC9B;IACA,MAAMV,cAAc,GAAGxC,oBAAoB,CAACoD,aAAa,CAAC;IAC1D,MAAMC,OAAO,GAAG5M,MAAM,CAACC,SAAS,CAAC4M,QAAQ,CAAChI,KAAK,CAAC8H,aAAa,CAAC;IAC9D,IAAId,0BAA0B,IAAIc,aAAa,IAAIZ,cAAc,IAAIJ,QAAQ,CAACrM,OAAO,CAACsN,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE1O,QAAQ,CAAC0N,UAAU,CAAC,IAAIrF,KAAK,CAACe,OAAO,CAACqF,aAAa,CAAC,CAAC,EAAE;MAC7J,IAAI,CAAC5C,GAAG,CAAC+C,aAAa,IAAI,CAAC,IAAI,CAAC9H,OAAO,CAAC8H,aAAa,EAAE;QACrD,IAAI,CAAC,IAAI,CAAC9H,OAAO,CAAC+H,qBAAqB,EAAE;UACvC,IAAI,CAAC5H,MAAM,CAACT,IAAI,CAAC,iEAAiE,CAAC;QACrF;QACA,MAAMrB,CAAC,GAAG,IAAI,CAAC2B,OAAO,CAAC+H,qBAAqB,GAAG,IAAI,CAAC/H,OAAO,CAAC+H,qBAAqB,CAACtB,UAAU,EAAEkB,aAAa,EAAE;UAC3G,GAAG5C,GAAG;UACNlD,EAAE,EAAEqD;QACN,CAAC,CAAC,GAAG,QAAQ7K,GAAG,KAAK,IAAI,CAACsK,QAAQ,0CAA0C;QAC5E,IAAIoB,aAAa,EAAE;UACjBf,QAAQ,CAAC3L,GAAG,GAAGgF,CAAC;UAChB2G,QAAQ,CAACuB,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAACzB,GAAG,CAAC;UACpD,OAAOC,QAAQ;QACjB;QACA,OAAO3G,CAAC;MACV;MACA,IAAIJ,YAAY,EAAE;QAChB,MAAM+J,cAAc,GAAGzG,KAAK,CAACe,OAAO,CAACqF,aAAa,CAAC;QACnD,MAAM9N,IAAI,GAAGmO,cAAc,GAAG,EAAE,GAAG,CAAC,CAAC;QACrC,MAAMC,WAAW,GAAGD,cAAc,GAAGtB,eAAe,GAAGD,UAAU;QACjE,KAAK,MAAMvM,CAAC,IAAIyN,aAAa,EAAE;UAC7B,IAAI3M,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACwM,aAAa,EAAEzN,CAAC,CAAC,EAAE;YAC1D,MAAMgO,OAAO,GAAG,GAAGD,WAAW,GAAGhK,YAAY,GAAG/D,CAAC,EAAE;YACnD,IAAIgN,eAAe,IAAI,CAAC7N,GAAG,EAAE;cAC3BQ,IAAI,CAACK,CAAC,CAAC,GAAG,IAAI,CAACyL,SAAS,CAACuC,OAAO,EAAE;gBAChC,GAAGnD,GAAG;gBACN0C,YAAY,EAAElD,oBAAoB,CAACkD,YAAY,CAAC,GAAGA,YAAY,CAACvN,CAAC,CAAC,GAAGqB,SAAS;gBAC9E,GAAG;kBACDqL,UAAU,EAAE,KAAK;kBACjB/E,EAAE,EAAEqD;gBACN;cACF,CAAC,CAAC;YACJ,CAAC,MAAM;cACLrL,IAAI,CAACK,CAAC,CAAC,GAAG,IAAI,CAACyL,SAAS,CAACuC,OAAO,EAAE;gBAChC,GAAGnD,GAAG;gBACN,GAAG;kBACD6B,UAAU,EAAE,KAAK;kBACjB/E,EAAE,EAAEqD;gBACN;cACF,CAAC,CAAC;YACJ;YACA,IAAIrL,IAAI,CAACK,CAAC,CAAC,KAAKgO,OAAO,EAAErO,IAAI,CAACK,CAAC,CAAC,GAAGyN,aAAa,CAACzN,CAAC,CAAC;UACrD;QACF;QACAb,GAAG,GAAGQ,IAAI;MACZ;IACF,CAAC,MAAM,IAAIgN,0BAA0B,IAAI3N,QAAQ,CAAC0N,UAAU,CAAC,IAAIrF,KAAK,CAACe,OAAO,CAACjJ,GAAG,CAAC,EAAE;MACnFA,GAAG,GAAGA,GAAG,CAACkF,IAAI,CAACqI,UAAU,CAAC;MAC1B,IAAIvN,GAAG,EAAEA,GAAG,GAAG,IAAI,CAAC8O,iBAAiB,CAAC9O,GAAG,EAAEoK,IAAI,EAAEsB,GAAG,EAAEa,OAAO,CAAC;IAChE,CAAC,MAAM;MACL,IAAIwC,WAAW,GAAG,KAAK;MACvB,IAAIjC,OAAO,GAAG,KAAK;MACnB,IAAI,CAAC,IAAI,CAACkC,aAAa,CAAChP,GAAG,CAAC,IAAI6N,eAAe,EAAE;QAC/CkB,WAAW,GAAG,IAAI;QAClB/O,GAAG,GAAGoO,YAAY;MACpB;MACA,IAAI,CAAC,IAAI,CAACY,aAAa,CAAChP,GAAG,CAAC,EAAE;QAC5B8M,OAAO,GAAG,IAAI;QACd9M,GAAG,GAAGgB,GAAG;MACX;MACA,MAAMiO,8BAA8B,GAAGvD,GAAG,CAACuD,8BAA8B,IAAI,IAAI,CAACtI,OAAO,CAACsI,8BAA8B;MACxH,MAAMC,aAAa,GAAGD,8BAA8B,IAAInC,OAAO,GAAG5K,SAAS,GAAGlC,GAAG;MACjF,MAAMmP,aAAa,GAAGtB,eAAe,IAAIO,YAAY,KAAKpO,GAAG,IAAI,IAAI,CAAC2G,OAAO,CAACwI,aAAa;MAC3F,IAAIrC,OAAO,IAAIiC,WAAW,IAAII,aAAa,EAAE;QAC3C,IAAI,CAACrI,MAAM,CAACZ,GAAG,CAACiJ,aAAa,GAAG,WAAW,GAAG,YAAY,EAAEnG,GAAG,EAAE2D,SAAS,EAAE3L,GAAG,EAAEmO,aAAa,GAAGf,YAAY,GAAGpO,GAAG,CAAC;QACpH,IAAI4E,YAAY,EAAE;UAChB,MAAMwK,EAAE,GAAG,IAAI,CAAChP,OAAO,CAACY,GAAG,EAAE;YAC3B,GAAG0K,GAAG;YACN9G,YAAY,EAAE;UAChB,CAAC,CAAC;UACF,IAAIwK,EAAE,IAAIA,EAAE,CAACpP,GAAG,EAAE,IAAI,CAAC8G,MAAM,CAACT,IAAI,CAAC,iLAAiL,CAAC;QACvN;QACA,IAAIgJ,IAAI,GAAG,EAAE;QACb,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAAC7I,OAAO,CAAC8I,WAAW,EAAE/D,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QAC5G,IAAI,IAAI,CAAC3E,OAAO,CAAC+I,aAAa,KAAK,UAAU,IAAIJ,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,EAAE;UAChF,KAAK,IAAI5J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4J,YAAY,CAAC5N,MAAM,EAAEgE,CAAC,EAAE,EAAE;YAC5C2J,IAAI,CAAC5M,IAAI,CAAC6M,YAAY,CAAC5J,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC,MAAM,IAAI,IAAI,CAACiB,OAAO,CAAC+I,aAAa,KAAK,KAAK,EAAE;UAC/CL,IAAI,GAAG,IAAI,CAACE,aAAa,CAACI,kBAAkB,CAACjE,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QACxE,CAAC,MAAM;UACL+D,IAAI,CAAC5M,IAAI,CAACiJ,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QACrC;QACA,MAAMsE,IAAI,GAAGA,CAACC,CAAC,EAAE9N,CAAC,EAAE+N,oBAAoB,KAAK;UAC3C,MAAMC,iBAAiB,GAAGlC,eAAe,IAAIiC,oBAAoB,KAAK9P,GAAG,GAAG8P,oBAAoB,GAAGZ,aAAa;UAChH,IAAI,IAAI,CAACvI,OAAO,CAACqJ,iBAAiB,EAAE;YAClC,IAAI,CAACrJ,OAAO,CAACqJ,iBAAiB,CAACH,CAAC,EAAElD,SAAS,EAAE5K,CAAC,EAAEgO,iBAAiB,EAAEZ,aAAa,EAAEzD,GAAG,CAAC;UACxF,CAAC,MAAM,IAAI,IAAI,CAACuE,gBAAgB,EAAEC,WAAW,EAAE;YAC7C,IAAI,CAACD,gBAAgB,CAACC,WAAW,CAACL,CAAC,EAAElD,SAAS,EAAE5K,CAAC,EAAEgO,iBAAiB,EAAEZ,aAAa,EAAEzD,GAAG,CAAC;UAC3F;UACA,IAAI,CAAC1D,IAAI,CAAC,YAAY,EAAE6H,CAAC,EAAElD,SAAS,EAAE5K,CAAC,EAAE/B,GAAG,CAAC;QAC/C,CAAC;QACD,IAAI,IAAI,CAAC2G,OAAO,CAACuJ,WAAW,EAAE;UAC5B,IAAI,IAAI,CAACvJ,OAAO,CAACwJ,kBAAkB,IAAIxC,mBAAmB,EAAE;YAC1D0B,IAAI,CAACzO,OAAO,CAAC0K,QAAQ,IAAI;cACvB,MAAM8E,QAAQ,GAAG,IAAI,CAACrC,cAAc,CAACsC,WAAW,CAAC/E,QAAQ,EAAEI,GAAG,CAAC;cAC/D,IAAIyC,qBAAqB,IAAIzC,GAAG,CAAC,eAAe,IAAI,CAAC/E,OAAO,CAAC0H,eAAe,MAAM,CAAC,IAAI+B,QAAQ,CAACnP,OAAO,CAAC,GAAG,IAAI,CAAC0F,OAAO,CAAC0H,eAAe,MAAM,CAAC,GAAG,CAAC,EAAE;gBAClJ+B,QAAQ,CAAC3N,IAAI,CAAC,GAAG,IAAI,CAACkE,OAAO,CAAC0H,eAAe,MAAM,CAAC;cACtD;cACA+B,QAAQ,CAACxP,OAAO,CAAC0P,MAAM,IAAI;gBACzBV,IAAI,CAAC,CAACtE,QAAQ,CAAC,EAAEtK,GAAG,GAAGsP,MAAM,EAAE5E,GAAG,CAAC,eAAe4E,MAAM,EAAE,CAAC,IAAIlC,YAAY,CAAC;cAC9E,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,MAAM;YACLwB,IAAI,CAACP,IAAI,EAAErO,GAAG,EAAEoN,YAAY,CAAC;UAC/B;QACF;MACF;MACApO,GAAG,GAAG,IAAI,CAAC8O,iBAAiB,CAAC9O,GAAG,EAAEoK,IAAI,EAAEsB,GAAG,EAAEC,QAAQ,EAAEY,OAAO,CAAC;MAC/D,IAAIO,OAAO,IAAI9M,GAAG,KAAKgB,GAAG,IAAI,IAAI,CAAC2F,OAAO,CAAC4J,2BAA2B,EAAE;QACtEvQ,GAAG,GAAG,GAAG2M,SAAS,GAAGhI,WAAW,GAAG3D,GAAG,EAAE;MAC1C;MACA,IAAI,CAAC8L,OAAO,IAAIiC,WAAW,KAAK,IAAI,CAACpI,OAAO,CAAC6J,sBAAsB,EAAE;QACnExQ,GAAG,GAAG,IAAI,CAAC2G,OAAO,CAAC6J,sBAAsB,CAAC,IAAI,CAAC7J,OAAO,CAAC4J,2BAA2B,GAAG,GAAG5D,SAAS,GAAGhI,WAAW,GAAG3D,GAAG,EAAE,GAAGA,GAAG,EAAE+N,WAAW,GAAG/O,GAAG,GAAGkC,SAAS,EAAEwJ,GAAG,CAAC;MACpK;IACF;IACA,IAAIgB,aAAa,EAAE;MACjBf,QAAQ,CAAC3L,GAAG,GAAGA,GAAG;MAClB2L,QAAQ,CAACuB,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAACzB,GAAG,CAAC;MACpD,OAAOC,QAAQ;IACjB;IACA,OAAO3L,GAAG;EACZ;EACA8O,iBAAiBA,CAAC9O,GAAG,EAAEgB,GAAG,EAAE0K,GAAG,EAAEC,QAAQ,EAAEY,OAAO,EAAE;IAClD,IAAI,IAAI,CAACkB,UAAU,EAAE7D,KAAK,EAAE;MAC1B5J,GAAG,GAAG,IAAI,CAACyN,UAAU,CAAC7D,KAAK,CAAC5J,GAAG,EAAE;QAC/B,GAAG,IAAI,CAAC2G,OAAO,CAAC8E,aAAa,CAACgF,gBAAgB;QAC9C,GAAG/E;MACL,CAAC,EAAEA,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,IAAIK,QAAQ,CAACqB,OAAO,EAAErB,QAAQ,CAACsB,MAAM,EAAEtB,QAAQ,CAACmB,OAAO,EAAE;QAClFnB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAACD,GAAG,CAACgF,iBAAiB,EAAE;MACjC,IAAIhF,GAAG,CAACD,aAAa,EAAE,IAAI,CAACU,YAAY,CAACvF,IAAI,CAAC;QAC5C,GAAG8E,GAAG;QACN,GAAG;UACDD,aAAa,EAAE;YACb,GAAG,IAAI,CAAC9E,OAAO,CAAC8E,aAAa;YAC7B,GAAGC,GAAG,CAACD;UACT;QACF;MACF,CAAC,CAAC;MACF,MAAMkF,eAAe,GAAG9Q,QAAQ,CAACG,GAAG,CAAC,KAAK0L,GAAG,EAAED,aAAa,EAAEkF,eAAe,KAAKzO,SAAS,GAAGwJ,GAAG,CAACD,aAAa,CAACkF,eAAe,GAAG,IAAI,CAAChK,OAAO,CAAC8E,aAAa,CAACkF,eAAe,CAAC;MAC7K,IAAIC,OAAO;MACX,IAAID,eAAe,EAAE;QACnB,MAAME,EAAE,GAAG7Q,GAAG,CAACkM,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;QACrDwE,OAAO,GAAGC,EAAE,IAAIA,EAAE,CAACnP,MAAM;MAC3B;MACA,IAAIkB,IAAI,GAAG8I,GAAG,CAACxK,OAAO,IAAI,CAACrB,QAAQ,CAAC6L,GAAG,CAACxK,OAAO,CAAC,GAAGwK,GAAG,CAACxK,OAAO,GAAGwK,GAAG;MACpE,IAAI,IAAI,CAAC/E,OAAO,CAAC8E,aAAa,CAACgF,gBAAgB,EAAE7N,IAAI,GAAG;QACtD,GAAG,IAAI,CAAC+D,OAAO,CAAC8E,aAAa,CAACgF,gBAAgB;QAC9C,GAAG7N;MACL,CAAC;MACD5C,GAAG,GAAG,IAAI,CAACmM,YAAY,CAAC2E,WAAW,CAAC9Q,GAAG,EAAE4C,IAAI,EAAE8I,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,IAAIK,QAAQ,CAACqB,OAAO,EAAEtB,GAAG,CAAC;MACjG,IAAIiF,eAAe,EAAE;QACnB,MAAMI,EAAE,GAAG/Q,GAAG,CAACkM,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;QACrD,MAAM4E,OAAO,GAAGD,EAAE,IAAIA,EAAE,CAACrP,MAAM;QAC/B,IAAIkP,OAAO,GAAGI,OAAO,EAAEtF,GAAG,CAACuF,IAAI,GAAG,KAAK;MACzC;MACA,IAAI,CAACvF,GAAG,CAAC1C,GAAG,IAAI2C,QAAQ,IAAIA,QAAQ,CAAC3L,GAAG,EAAE0L,GAAG,CAAC1C,GAAG,GAAG,IAAI,CAACsC,QAAQ,IAAIK,QAAQ,CAACqB,OAAO;MACrF,IAAItB,GAAG,CAACuF,IAAI,KAAK,KAAK,EAAEjR,GAAG,GAAG,IAAI,CAACmM,YAAY,CAAC8E,IAAI,CAACjR,GAAG,EAAE,CAAC,GAAGmG,IAAI,KAAK;QACrE,IAAIoG,OAAO,GAAG,CAAC,CAAC,KAAKpG,IAAI,CAAC,CAAC,CAAC,IAAI,CAACuF,GAAG,CAACwF,OAAO,EAAE;UAC5C,IAAI,CAACpK,MAAM,CAACT,IAAI,CAAC,6CAA6CF,IAAI,CAAC,CAAC,CAAC,YAAYnF,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;UAC1F,OAAO,IAAI;QACb;QACA,OAAO,IAAI,CAACsL,SAAS,CAAC,GAAGnG,IAAI,EAAEnF,GAAG,CAAC;MACrC,CAAC,EAAE0K,GAAG,CAAC;MACP,IAAIA,GAAG,CAACD,aAAa,EAAE,IAAI,CAACU,YAAY,CAACgF,KAAK,CAAC,CAAC;IAClD;IACA,MAAMC,WAAW,GAAG1F,GAAG,CAAC0F,WAAW,IAAI,IAAI,CAACzK,OAAO,CAACyK,WAAW;IAC/D,MAAMC,kBAAkB,GAAGxR,QAAQ,CAACuR,WAAW,CAAC,GAAG,CAACA,WAAW,CAAC,GAAGA,WAAW;IAC9E,IAAIpR,GAAG,IAAI,IAAI,IAAIqR,kBAAkB,EAAE3P,MAAM,IAAIgK,GAAG,CAAC4F,kBAAkB,KAAK,KAAK,EAAE;MACjFtR,GAAG,GAAGwK,aAAa,CAACK,MAAM,CAACwG,kBAAkB,EAAErR,GAAG,EAAEgB,GAAG,EAAE,IAAI,CAAC2F,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC4K,uBAAuB,GAAG;QAC9GC,YAAY,EAAE;UACZ,GAAG7F,QAAQ;UACXuB,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACzB,GAAG;QAC3C,CAAC;QACD,GAAGA;MACL,CAAC,GAAGA,GAAG,EAAE,IAAI,CAAC;IAChB;IACA,OAAO1L,GAAG;EACZ;EACAI,OAAOA,CAACgK,IAAI,EAAEsB,GAAG,GAAG,CAAC,CAAC,EAAE;IACtB,IAAI+F,KAAK;IACT,IAAI3E,OAAO;IACX,IAAIC,YAAY;IAChB,IAAIC,OAAO;IACX,IAAIC,MAAM;IACV,IAAIpN,QAAQ,CAACuK,IAAI,CAAC,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjCA,IAAI,CAACxJ,OAAO,CAACmB,CAAC,IAAI;MAChB,IAAI,IAAI,CAACiN,aAAa,CAACyC,KAAK,CAAC,EAAE;MAC/B,MAAMC,SAAS,GAAG,IAAI,CAAC9F,cAAc,CAAC7J,CAAC,EAAE2J,GAAG,CAAC;MAC7C,MAAM1K,GAAG,GAAG0Q,SAAS,CAAC1Q,GAAG;MACzB8L,OAAO,GAAG9L,GAAG;MACb,IAAI6K,UAAU,GAAG6F,SAAS,CAAC7F,UAAU;MACrC,IAAI,IAAI,CAAClF,OAAO,CAACgL,UAAU,EAAE9F,UAAU,GAAGA,UAAU,CAACrJ,MAAM,CAAC,IAAI,CAACmE,OAAO,CAACgL,UAAU,CAAC;MACpF,MAAMhE,mBAAmB,GAAGjC,GAAG,CAACkC,KAAK,KAAK1L,SAAS,IAAI,CAACrC,QAAQ,CAAC6L,GAAG,CAACkC,KAAK,CAAC;MAC3E,MAAMO,qBAAqB,GAAGR,mBAAmB,IAAI,CAACjC,GAAG,CAACwC,OAAO,IAAIxC,GAAG,CAACkC,KAAK,KAAK,CAAC;MACpF,MAAMgE,oBAAoB,GAAGlG,GAAG,CAACwF,OAAO,KAAKhP,SAAS,KAAKrC,QAAQ,CAAC6L,GAAG,CAACwF,OAAO,CAAC,IAAI,OAAOxF,GAAG,CAACwF,OAAO,KAAK,QAAQ,CAAC,IAAIxF,GAAG,CAACwF,OAAO,KAAK,EAAE;MAC1I,MAAMW,KAAK,GAAGnG,GAAG,CAAC2D,IAAI,GAAG3D,GAAG,CAAC2D,IAAI,GAAG,IAAI,CAACE,aAAa,CAACI,kBAAkB,CAACjE,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,EAAEI,GAAG,CAAC+D,WAAW,CAAC;MACpH5D,UAAU,CAACjL,OAAO,CAAC4H,EAAE,IAAI;QACvB,IAAI,IAAI,CAACwG,aAAa,CAACyC,KAAK,CAAC,EAAE;QAC/BxE,MAAM,GAAGzE,EAAE;QACX,IAAI,CAACyC,gBAAgB,CAAC,GAAG4G,KAAK,CAAC,CAAC,CAAC,IAAIrJ,EAAE,EAAE,CAAC,IAAI,IAAI,CAACsJ,KAAK,EAAEC,kBAAkB,IAAI,CAAC,IAAI,CAACD,KAAK,EAAEC,kBAAkB,CAAC9E,MAAM,CAAC,EAAE;UACvHhC,gBAAgB,CAAC,GAAG4G,KAAK,CAAC,CAAC,CAAC,IAAIrJ,EAAE,EAAE,CAAC,GAAG,IAAI;UAC5C,IAAI,CAAC1B,MAAM,CAACT,IAAI,CAAC,QAAQyG,OAAO,oBAAoB+E,KAAK,CAAC3M,IAAI,CAAC,IAAI,CAAC,sCAAsC+H,MAAM,sBAAsB,EAAE,0NAA0N,CAAC;QACrW;QACA4E,KAAK,CAACjR,OAAO,CAACmF,IAAI,IAAI;UACpB,IAAI,IAAI,CAACiJ,aAAa,CAACyC,KAAK,CAAC,EAAE;UAC/BzE,OAAO,GAAGjH,IAAI;UACd,MAAMiM,SAAS,GAAG,CAAChR,GAAG,CAAC;UACvB,IAAI,IAAI,CAACyM,UAAU,EAAEwE,aAAa,EAAE;YAClC,IAAI,CAACxE,UAAU,CAACwE,aAAa,CAACD,SAAS,EAAEhR,GAAG,EAAE+E,IAAI,EAAEyC,EAAE,EAAEkD,GAAG,CAAC;UAC9D,CAAC,MAAM;YACL,IAAIwG,YAAY;YAChB,IAAIvE,mBAAmB,EAAEuE,YAAY,GAAG,IAAI,CAACnE,cAAc,CAACC,SAAS,CAACjI,IAAI,EAAE2F,GAAG,CAACkC,KAAK,EAAElC,GAAG,CAAC;YAC3F,MAAMyG,UAAU,GAAG,GAAG,IAAI,CAACxL,OAAO,CAAC0H,eAAe,MAAM;YACxD,MAAM+D,aAAa,GAAG,GAAG,IAAI,CAACzL,OAAO,CAAC0H,eAAe,UAAU,IAAI,CAAC1H,OAAO,CAAC0H,eAAe,EAAE;YAC7F,IAAIV,mBAAmB,EAAE;cACvBqE,SAAS,CAACvP,IAAI,CAACzB,GAAG,GAAGkR,YAAY,CAAC;cAClC,IAAIxG,GAAG,CAACwC,OAAO,IAAIgE,YAAY,CAACjR,OAAO,CAACmR,aAAa,CAAC,KAAK,CAAC,EAAE;gBAC5DJ,SAAS,CAACvP,IAAI,CAACzB,GAAG,GAAGkR,YAAY,CAAChR,OAAO,CAACkR,aAAa,EAAE,IAAI,CAACzL,OAAO,CAAC0H,eAAe,CAAC,CAAC;cACzF;cACA,IAAIF,qBAAqB,EAAE;gBACzB6D,SAAS,CAACvP,IAAI,CAACzB,GAAG,GAAGmR,UAAU,CAAC;cAClC;YACF;YACA,IAAIP,oBAAoB,EAAE;cACxB,MAAMS,UAAU,GAAG,GAAGrR,GAAG,GAAG,IAAI,CAAC2F,OAAO,CAAC2L,gBAAgB,GAAG5G,GAAG,CAACwF,OAAO,EAAE;cACzEc,SAAS,CAACvP,IAAI,CAAC4P,UAAU,CAAC;cAC1B,IAAI1E,mBAAmB,EAAE;gBACvBqE,SAAS,CAACvP,IAAI,CAAC4P,UAAU,GAAGH,YAAY,CAAC;gBACzC,IAAIxG,GAAG,CAACwC,OAAO,IAAIgE,YAAY,CAACjR,OAAO,CAACmR,aAAa,CAAC,KAAK,CAAC,EAAE;kBAC5DJ,SAAS,CAACvP,IAAI,CAAC4P,UAAU,GAAGH,YAAY,CAAChR,OAAO,CAACkR,aAAa,EAAE,IAAI,CAACzL,OAAO,CAAC0H,eAAe,CAAC,CAAC;gBAChG;gBACA,IAAIF,qBAAqB,EAAE;kBACzB6D,SAAS,CAACvP,IAAI,CAAC4P,UAAU,GAAGF,UAAU,CAAC;gBACzC;cACF;YACF;UACF;UACA,IAAII,WAAW;UACf,OAAOA,WAAW,GAAGP,SAAS,CAACQ,GAAG,CAAC,CAAC,EAAE;YACpC,IAAI,CAAC,IAAI,CAACxD,aAAa,CAACyC,KAAK,CAAC,EAAE;cAC9B1E,YAAY,GAAGwF,WAAW;cAC1Bd,KAAK,GAAG,IAAI,CAAC1I,WAAW,CAAChD,IAAI,EAAEyC,EAAE,EAAE+J,WAAW,EAAE7G,GAAG,CAAC;YACtD;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO;MACL1L,GAAG,EAAEyR,KAAK;MACV3E,OAAO;MACPC,YAAY;MACZC,OAAO;MACPC;IACF,CAAC;EACH;EACA+B,aAAaA,CAAChP,GAAG,EAAE;IACjB,OAAOA,GAAG,KAAKkC,SAAS,IAAI,EAAE,CAAC,IAAI,CAACyE,OAAO,CAAC8L,UAAU,IAAIzS,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC2G,OAAO,CAAC+L,iBAAiB,IAAI1S,GAAG,KAAK,EAAE,CAAC;EAC7H;EACA+I,WAAWA,CAAChD,IAAI,EAAEyC,EAAE,EAAExH,GAAG,EAAE2F,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,IAAI,IAAI,CAAC8G,UAAU,EAAE1E,WAAW,EAAE,OAAO,IAAI,CAAC0E,UAAU,CAAC1E,WAAW,CAAChD,IAAI,EAAEyC,EAAE,EAAExH,GAAG,EAAE2F,OAAO,CAAC;IAC5F,OAAO,IAAI,CAACgM,aAAa,CAAC5J,WAAW,CAAChD,IAAI,EAAEyC,EAAE,EAAExH,GAAG,EAAE2F,OAAO,CAAC;EAC/D;EACAwG,oBAAoBA,CAACxG,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,MAAMiM,WAAW,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC;IACxN,MAAMC,wBAAwB,GAAGlM,OAAO,CAACzF,OAAO,IAAI,CAACrB,QAAQ,CAAC8G,OAAO,CAACzF,OAAO,CAAC;IAC9E,IAAI0B,IAAI,GAAGiQ,wBAAwB,GAAGlM,OAAO,CAACzF,OAAO,GAAGyF,OAAO;IAC/D,IAAIkM,wBAAwB,IAAI,OAAOlM,OAAO,CAACiH,KAAK,KAAK,WAAW,EAAE;MACpEhL,IAAI,CAACgL,KAAK,GAAGjH,OAAO,CAACiH,KAAK;IAC5B;IACA,IAAI,IAAI,CAACjH,OAAO,CAAC8E,aAAa,CAACgF,gBAAgB,EAAE;MAC/C7N,IAAI,GAAG;QACL,GAAG,IAAI,CAAC+D,OAAO,CAAC8E,aAAa,CAACgF,gBAAgB;QAC9C,GAAG7N;MACL,CAAC;IACH;IACA,IAAI,CAACiQ,wBAAwB,EAAE;MAC7BjQ,IAAI,GAAG;QACL,GAAGA;MACL,CAAC;MACD,KAAK,MAAM5B,GAAG,IAAI4R,WAAW,EAAE;QAC7B,OAAOhQ,IAAI,CAAC5B,GAAG,CAAC;MAClB;IACF;IACA,OAAO4B,IAAI;EACb;EACA,OAAOiL,eAAeA,CAAClH,OAAO,EAAE;IAC9B,MAAME,MAAM,GAAG,cAAc;IAC7B,KAAK,MAAMiM,MAAM,IAAInM,OAAO,EAAE;MAC5B,IAAIhF,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC6E,OAAO,EAAEmM,MAAM,CAAC,IAAIjM,MAAM,KAAKiM,MAAM,CAACxN,SAAS,CAAC,CAAC,EAAEuB,MAAM,CAACnF,MAAM,CAAC,IAAIQ,SAAS,KAAKyE,OAAO,CAACmM,MAAM,CAAC,EAAE;QAC3I,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;AACF;AAEA,MAAMC,YAAY,CAAC;EACjBrP,WAAWA,CAACiD,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqM,aAAa,GAAG,IAAI,CAACrM,OAAO,CAACqM,aAAa,IAAI,KAAK;IACxD,IAAI,CAAClM,MAAM,GAAGS,UAAU,CAACH,MAAM,CAAC,eAAe,CAAC;EAClD;EACA6L,qBAAqBA,CAAClN,IAAI,EAAE;IAC1BA,IAAI,GAAGD,cAAc,CAACC,IAAI,CAAC;IAC3B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC9E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI;IAC/C,MAAMmB,CAAC,GAAG2D,IAAI,CAACvE,KAAK,CAAC,GAAG,CAAC;IACzB,IAAIY,CAAC,CAACV,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC/BU,CAAC,CAACoQ,GAAG,CAAC,CAAC;IACP,IAAIpQ,CAAC,CAACA,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC,CAACmL,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;IACtD,OAAO,IAAI,CAACqG,kBAAkB,CAAC9Q,CAAC,CAAC8C,IAAI,CAAC,GAAG,CAAC,CAAC;EAC7C;EACAiO,uBAAuBA,CAACpN,IAAI,EAAE;IAC5BA,IAAI,GAAGD,cAAc,CAACC,IAAI,CAAC;IAC3B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC9E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO8E,IAAI;IAC/C,MAAM3D,CAAC,GAAG2D,IAAI,CAACvE,KAAK,CAAC,GAAG,CAAC;IACzB,OAAO,IAAI,CAAC0R,kBAAkB,CAAC9Q,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC;EACA8Q,kBAAkBA,CAACnN,IAAI,EAAE;IACvB,IAAIlG,QAAQ,CAACkG,IAAI,CAAC,IAAIA,IAAI,CAAC9E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5C,IAAImS,aAAa;MACjB,IAAI;QACFA,aAAa,GAAGC,IAAI,CAACC,mBAAmB,CAACvN,IAAI,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,OAAO5D,CAAC,EAAE,CAAC;MACb,IAAIiR,aAAa,IAAI,IAAI,CAACzM,OAAO,CAAC4M,YAAY,EAAE;QAC9CH,aAAa,GAAGA,aAAa,CAACvG,WAAW,CAAC,CAAC;MAC7C;MACA,IAAIuG,aAAa,EAAE,OAAOA,aAAa;MACvC,IAAI,IAAI,CAACzM,OAAO,CAAC4M,YAAY,EAAE;QAC7B,OAAOxN,IAAI,CAAC8G,WAAW,CAAC,CAAC;MAC3B;MACA,OAAO9G,IAAI;IACb;IACA,OAAO,IAAI,CAACY,OAAO,CAAC6M,SAAS,IAAI,IAAI,CAAC7M,OAAO,CAAC4M,YAAY,GAAGxN,IAAI,CAAC8G,WAAW,CAAC,CAAC,GAAG9G,IAAI;EACxF;EACA0N,eAAeA,CAAC1N,IAAI,EAAE;IACpB,IAAI,IAAI,CAACY,OAAO,CAAC+M,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC/M,OAAO,CAACgN,wBAAwB,EAAE;MACjF5N,IAAI,GAAG,IAAI,CAACoN,uBAAuB,CAACpN,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC,IAAI,CAACiN,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACtR,MAAM,IAAI,IAAI,CAACsR,aAAa,CAAC/R,OAAO,CAAC8E,IAAI,CAAC,GAAG,CAAC,CAAC;EACnG;EACA6N,qBAAqBA,CAAC/B,KAAK,EAAE;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IACvB,IAAIJ,KAAK;IACTI,KAAK,CAACjR,OAAO,CAACmF,IAAI,IAAI;MACpB,IAAI0L,KAAK,EAAE;MACX,MAAMoC,UAAU,GAAG,IAAI,CAACX,kBAAkB,CAACnN,IAAI,CAAC;MAChD,IAAI,CAAC,IAAI,CAACY,OAAO,CAACqM,aAAa,IAAI,IAAI,CAACS,eAAe,CAACI,UAAU,CAAC,EAAEpC,KAAK,GAAGoC,UAAU;IACzF,CAAC,CAAC;IACF,IAAI,CAACpC,KAAK,IAAI,IAAI,CAAC9K,OAAO,CAACqM,aAAa,EAAE;MACxCnB,KAAK,CAACjR,OAAO,CAACmF,IAAI,IAAI;QACpB,IAAI0L,KAAK,EAAE;QACX,MAAMqC,SAAS,GAAG,IAAI,CAACb,qBAAqB,CAAClN,IAAI,CAAC;QAClD,IAAI,IAAI,CAAC0N,eAAe,CAACK,SAAS,CAAC,EAAE,OAAOrC,KAAK,GAAGqC,SAAS;QAC7D,MAAMC,OAAO,GAAG,IAAI,CAACZ,uBAAuB,CAACpN,IAAI,CAAC;QAClD,IAAI,IAAI,CAAC0N,eAAe,CAACM,OAAO,CAAC,EAAE,OAAOtC,KAAK,GAAGsC,OAAO;QACzDtC,KAAK,GAAG,IAAI,CAAC9K,OAAO,CAACqM,aAAa,CAAC3I,IAAI,CAAC2J,YAAY,IAAI;UACtD,IAAIA,YAAY,KAAKD,OAAO,EAAE,OAAOC,YAAY;UACjD,IAAIA,YAAY,CAAC/S,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI8S,OAAO,CAAC9S,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;UAC/D,IAAI+S,YAAY,CAAC/S,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI8S,OAAO,CAAC9S,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI+S,YAAY,CAAC1O,SAAS,CAAC,CAAC,EAAE0O,YAAY,CAAC/S,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK8S,OAAO,EAAE,OAAOC,YAAY;UACtJ,IAAIA,YAAY,CAAC/S,OAAO,CAAC8S,OAAO,CAAC,KAAK,CAAC,IAAIA,OAAO,CAACrS,MAAM,GAAG,CAAC,EAAE,OAAOsS,YAAY;QACpF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAACvC,KAAK,EAAEA,KAAK,GAAG,IAAI,CAACjC,gBAAgB,CAAC,IAAI,CAAC7I,OAAO,CAAC8I,WAAW,CAAC,CAAC,CAAC,CAAC;IACtE,OAAOgC,KAAK;EACd;EACAjC,gBAAgBA,CAACyE,SAAS,EAAElO,IAAI,EAAE;IAChC,IAAI,CAACkO,SAAS,EAAE,OAAO,EAAE;IACzB,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAEA,SAAS,GAAGA,SAAS,CAAClO,IAAI,CAAC;IAChE,IAAIlG,QAAQ,CAACoU,SAAS,CAAC,EAAEA,SAAS,GAAG,CAACA,SAAS,CAAC;IAChD,IAAI/L,KAAK,CAACe,OAAO,CAACgL,SAAS,CAAC,EAAE,OAAOA,SAAS;IAC9C,IAAI,CAAClO,IAAI,EAAE,OAAOkO,SAAS,CAACC,OAAO,IAAI,EAAE;IACzC,IAAIzC,KAAK,GAAGwC,SAAS,CAAClO,IAAI,CAAC;IAC3B,IAAI,CAAC0L,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAAC,IAAI,CAAChB,qBAAqB,CAAClN,IAAI,CAAC,CAAC;IAC/D,IAAI,CAAC0L,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAAC,IAAI,CAACf,kBAAkB,CAACnN,IAAI,CAAC,CAAC;IAC5D,IAAI,CAAC0L,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAAC,IAAI,CAACd,uBAAuB,CAACpN,IAAI,CAAC,CAAC;IACjE,IAAI,CAAC0L,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAACC,OAAO;IACrC,OAAOzC,KAAK,IAAI,EAAE;EACpB;EACA9B,kBAAkBA,CAAC5J,IAAI,EAAEoO,YAAY,EAAE;IACrC,MAAMC,aAAa,GAAG,IAAI,CAAC5E,gBAAgB,CAAC,CAAC2E,YAAY,KAAK,KAAK,GAAG,EAAE,GAAGA,YAAY,KAAK,IAAI,CAACxN,OAAO,CAAC8I,WAAW,IAAI,EAAE,EAAE1J,IAAI,CAAC;IACjI,MAAM8L,KAAK,GAAG,EAAE;IAChB,MAAMwC,OAAO,GAAGtP,CAAC,IAAI;MACnB,IAAI,CAACA,CAAC,EAAE;MACR,IAAI,IAAI,CAAC0O,eAAe,CAAC1O,CAAC,CAAC,EAAE;QAC3B8M,KAAK,CAACpP,IAAI,CAACsC,CAAC,CAAC;MACf,CAAC,MAAM;QACL,IAAI,CAAC+B,MAAM,CAACT,IAAI,CAAC,uDAAuDtB,CAAC,EAAE,CAAC;MAC9E;IACF,CAAC;IACD,IAAIlF,QAAQ,CAACkG,IAAI,CAAC,KAAKA,IAAI,CAAC9E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI8E,IAAI,CAAC9E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MACxE,IAAI,IAAI,CAAC0F,OAAO,CAAC+M,IAAI,KAAK,cAAc,EAAEW,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACnN,IAAI,CAAC,CAAC;MAChF,IAAI,IAAI,CAACY,OAAO,CAAC+M,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC/M,OAAO,CAAC+M,IAAI,KAAK,aAAa,EAAEW,OAAO,CAAC,IAAI,CAACpB,qBAAqB,CAAClN,IAAI,CAAC,CAAC;MAC1H,IAAI,IAAI,CAACY,OAAO,CAAC+M,IAAI,KAAK,aAAa,EAAEW,OAAO,CAAC,IAAI,CAAClB,uBAAuB,CAACpN,IAAI,CAAC,CAAC;IACtF,CAAC,MAAM,IAAIlG,QAAQ,CAACkG,IAAI,CAAC,EAAE;MACzBsO,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACnN,IAAI,CAAC,CAAC;IACxC;IACAqO,aAAa,CAACxT,OAAO,CAAC0T,EAAE,IAAI;MAC1B,IAAIzC,KAAK,CAAC5Q,OAAO,CAACqT,EAAE,CAAC,GAAG,CAAC,EAAED,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACoB,EAAE,CAAC,CAAC;IACjE,CAAC,CAAC;IACF,OAAOzC,KAAK;EACd;AACF;AAEA,MAAM0C,aAAa,GAAG;EACpBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAEnH,KAAK,IAAIA,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;EAC9CoH,eAAe,EAAEA,CAAA,MAAO;IACtBC,gBAAgB,EAAE,CAAC,KAAK,EAAE,OAAO;EACnC,CAAC;AACH,CAAC;AACD,MAAMC,cAAc,CAAC;EACnBxR,WAAWA,CAAC6L,aAAa,EAAE5I,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,IAAI,CAAC4I,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC5I,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,MAAM,GAAGS,UAAU,CAACH,MAAM,CAAC,gBAAgB,CAAC;IACjD,IAAI,CAAC+N,gBAAgB,GAAG,CAAC,CAAC;EAC5B;EACAC,OAAOA,CAACpM,GAAG,EAAElJ,GAAG,EAAE;IAChB,IAAI,CAACuV,KAAK,CAACrM,GAAG,CAAC,GAAGlJ,GAAG;EACvB;EACAwV,UAAUA,CAAA,EAAG;IACX,IAAI,CAACH,gBAAgB,GAAG,CAAC,CAAC;EAC5B;EACAI,OAAOA,CAACxP,IAAI,EAAEY,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,MAAM6O,WAAW,GAAG1P,cAAc,CAACC,IAAI,KAAK,KAAK,GAAG,IAAI,GAAGA,IAAI,CAAC;IAChE,MAAME,IAAI,GAAGU,OAAO,CAACuH,OAAO,GAAG,SAAS,GAAG,UAAU;IACrD,MAAMuH,QAAQ,GAAG9L,IAAI,CAACE,SAAS,CAAC;MAC9B2L,WAAW;MACXvP;IACF,CAAC,CAAC;IACF,IAAIwP,QAAQ,IAAI,IAAI,CAACN,gBAAgB,EAAE;MACrC,OAAO,IAAI,CAACA,gBAAgB,CAACM,QAAQ,CAAC;IACxC;IACA,IAAIC,IAAI;IACR,IAAI;MACFA,IAAI,GAAG,IAAIrC,IAAI,CAACsC,WAAW,CAACH,WAAW,EAAE;QACvCvP;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO2P,GAAG,EAAE;MACZ,IAAI,CAACvC,IAAI,EAAE;QACT,IAAI,CAACvM,MAAM,CAACR,KAAK,CAAC,+CAA+C,CAAC;QAClE,OAAOwO,SAAS;MAClB;MACA,IAAI,CAAC/O,IAAI,CAACmG,KAAK,CAAC,KAAK,CAAC,EAAE,OAAO4I,SAAS;MACxC,MAAMe,OAAO,GAAG,IAAI,CAACtG,aAAa,CAAC4D,uBAAuB,CAACpN,IAAI,CAAC;MAChE2P,IAAI,GAAG,IAAI,CAACH,OAAO,CAACM,OAAO,EAAElP,OAAO,CAAC;IACvC;IACA,IAAI,CAACwO,gBAAgB,CAACM,QAAQ,CAAC,GAAGC,IAAI;IACtC,OAAOA,IAAI;EACb;EACAI,WAAWA,CAAC/P,IAAI,EAAEY,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI+O,IAAI,GAAG,IAAI,CAACH,OAAO,CAACxP,IAAI,EAAEY,OAAO,CAAC;IACtC,IAAI,CAAC+O,IAAI,EAAEA,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,KAAK,EAAE5O,OAAO,CAAC;IAC9C,OAAO+O,IAAI,EAAEV,eAAe,CAAC,CAAC,CAACC,gBAAgB,CAACvT,MAAM,GAAG,CAAC;EAC5D;EACAqU,mBAAmBA,CAAChQ,IAAI,EAAE/E,GAAG,EAAE2F,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3C,OAAO,IAAI,CAAC0J,WAAW,CAACtK,IAAI,EAAEY,OAAO,CAAC,CAAC1B,GAAG,CAACqL,MAAM,IAAI,GAAGtP,GAAG,GAAGsP,MAAM,EAAE,CAAC;EACzE;EACAD,WAAWA,CAACtK,IAAI,EAAEY,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI+O,IAAI,GAAG,IAAI,CAACH,OAAO,CAACxP,IAAI,EAAEY,OAAO,CAAC;IACtC,IAAI,CAAC+O,IAAI,EAAEA,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,KAAK,EAAE5O,OAAO,CAAC;IAC9C,IAAI,CAAC+O,IAAI,EAAE,OAAO,EAAE;IACpB,OAAOA,IAAI,CAACV,eAAe,CAAC,CAAC,CAACC,gBAAgB,CAACe,IAAI,CAAC,CAACC,eAAe,EAAEC,eAAe,KAAK3B,aAAa,CAAC0B,eAAe,CAAC,GAAG1B,aAAa,CAAC2B,eAAe,CAAC,CAAC,CAACjR,GAAG,CAACkR,cAAc,IAAI,GAAG,IAAI,CAACxP,OAAO,CAACyP,OAAO,GAAGzP,OAAO,CAACuH,OAAO,GAAG,UAAU,IAAI,CAACvH,OAAO,CAACyP,OAAO,EAAE,GAAG,EAAE,GAAGD,cAAc,EAAE,CAAC;EACxR;EACAnI,SAASA,CAACjI,IAAI,EAAE6H,KAAK,EAAEjH,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,MAAM+O,IAAI,GAAG,IAAI,CAACH,OAAO,CAACxP,IAAI,EAAEY,OAAO,CAAC;IACxC,IAAI+O,IAAI,EAAE;MACR,OAAO,GAAG,IAAI,CAAC/O,OAAO,CAACyP,OAAO,GAAGzP,OAAO,CAACuH,OAAO,GAAG,UAAU,IAAI,CAACvH,OAAO,CAACyP,OAAO,EAAE,GAAG,EAAE,GAAGV,IAAI,CAACX,MAAM,CAACnH,KAAK,CAAC,EAAE;IACjH;IACA,IAAI,CAAC9G,MAAM,CAACT,IAAI,CAAC,6BAA6BN,IAAI,EAAE,CAAC;IACrD,OAAO,IAAI,CAACiI,SAAS,CAAC,KAAK,EAAEJ,KAAK,EAAEjH,OAAO,CAAC;EAC9C;AACF;AAEA,MAAM0P,oBAAoB,GAAGA,CAACzT,IAAI,EAAEC,WAAW,EAAE7B,GAAG,EAAE4D,YAAY,GAAG,GAAG,EAAE8D,mBAAmB,GAAG,IAAI,KAAK;EACvG,IAAIrH,IAAI,GAAGsB,mBAAmB,CAACC,IAAI,EAAEC,WAAW,EAAE7B,GAAG,CAAC;EACtD,IAAI,CAACK,IAAI,IAAIqH,mBAAmB,IAAI7I,QAAQ,CAACmB,GAAG,CAAC,EAAE;IACjDK,IAAI,GAAGkE,QAAQ,CAAC3C,IAAI,EAAE5B,GAAG,EAAE4D,YAAY,CAAC;IACxC,IAAIvD,IAAI,KAAKa,SAAS,EAAEb,IAAI,GAAGkE,QAAQ,CAAC1C,WAAW,EAAE7B,GAAG,EAAE4D,YAAY,CAAC;EACzE;EACA,OAAOvD,IAAI;AACb,CAAC;AACD,MAAMiV,SAAS,GAAGC,GAAG,IAAIA,GAAG,CAACrV,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;AACnD,MAAMsV,YAAY,CAAC;EACjB9S,WAAWA,CAACiD,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACG,MAAM,GAAGS,UAAU,CAACH,MAAM,CAAC,cAAc,CAAC;IAC/C,IAAI,CAACT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8P,MAAM,GAAG9P,OAAO,EAAE8E,aAAa,EAAEgL,MAAM,KAAK3T,KAAK,IAAIA,KAAK,CAAC;IAChE,IAAI,CAAC8D,IAAI,CAACD,OAAO,CAAC;EACpB;EACAC,IAAIA,CAACD,OAAO,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,CAACA,OAAO,CAAC8E,aAAa,EAAE9E,OAAO,CAAC8E,aAAa,GAAG;MAClDiL,WAAW,EAAE;IACf,CAAC;IACD,MAAM;MACJlT,MAAM,EAAEmT,QAAQ;MAChBD,WAAW;MACXE,mBAAmB;MACnB/P,MAAM;MACNgQ,aAAa;MACbvG,MAAM;MACNwG,aAAa;MACbC,eAAe;MACfC,cAAc;MACdC,cAAc;MACdC,aAAa;MACbC,oBAAoB;MACpBC,aAAa;MACbC,oBAAoB;MACpBC,uBAAuB;MACvBC,WAAW;MACXC;IACF,CAAC,GAAG7Q,OAAO,CAAC8E,aAAa;IACzB,IAAI,CAACjI,MAAM,GAAGmT,QAAQ,KAAKzU,SAAS,GAAGyU,QAAQ,GAAGnT,MAAM;IACxD,IAAI,CAACkT,WAAW,GAAGA,WAAW,KAAKxU,SAAS,GAAGwU,WAAW,GAAG,IAAI;IACjE,IAAI,CAACE,mBAAmB,GAAGA,mBAAmB,KAAK1U,SAAS,GAAG0U,mBAAmB,GAAG,KAAK;IAC1F,IAAI,CAAC/P,MAAM,GAAGA,MAAM,GAAGxD,WAAW,CAACwD,MAAM,CAAC,GAAGgQ,aAAa,IAAI,IAAI;IAClE,IAAI,CAACvG,MAAM,GAAGA,MAAM,GAAGjN,WAAW,CAACiN,MAAM,CAAC,GAAGwG,aAAa,IAAI,IAAI;IAClE,IAAI,CAACC,eAAe,GAAGA,eAAe,IAAI,GAAG;IAC7C,IAAI,CAACE,cAAc,GAAGD,cAAc,GAAG,EAAE,GAAGC,cAAc,IAAI,GAAG;IACjE,IAAI,CAACD,cAAc,GAAG,IAAI,CAACC,cAAc,GAAG,EAAE,GAAGD,cAAc,IAAI,EAAE;IACrE,IAAI,CAACE,aAAa,GAAGA,aAAa,GAAG7T,WAAW,CAAC6T,aAAa,CAAC,GAAGC,oBAAoB,IAAI9T,WAAW,CAAC,KAAK,CAAC;IAC5G,IAAI,CAAC+T,aAAa,GAAGA,aAAa,GAAG/T,WAAW,CAAC+T,aAAa,CAAC,GAAGC,oBAAoB,IAAIhU,WAAW,CAAC,GAAG,CAAC;IAC1G,IAAI,CAACiU,uBAAuB,GAAGA,uBAAuB,IAAI,GAAG;IAC7D,IAAI,CAACC,WAAW,GAAGA,WAAW,IAAI,IAAI;IACtC,IAAI,CAACC,YAAY,GAAGA,YAAY,KAAKtV,SAAS,GAAGsV,YAAY,GAAG,KAAK;IACrE,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB;EACAtG,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACxK,OAAO,EAAE,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,OAAO,CAAC;EAC3C;EACA8Q,WAAWA,CAAA,EAAG;IACZ,MAAMC,gBAAgB,GAAGA,CAACC,cAAc,EAAE3T,OAAO,KAAK;MACpD,IAAI2T,cAAc,EAAE1U,MAAM,KAAKe,OAAO,EAAE;QACtC2T,cAAc,CAACC,SAAS,GAAG,CAAC;QAC5B,OAAOD,cAAc;MACvB;MACA,OAAO,IAAIvT,MAAM,CAACJ,OAAO,EAAE,GAAG,CAAC;IACjC,CAAC;IACD,IAAI,CAAC6T,MAAM,GAAGH,gBAAgB,CAAC,IAAI,CAACG,MAAM,EAAE,GAAG,IAAI,CAAChR,MAAM,QAAQ,IAAI,CAACyJ,MAAM,EAAE,CAAC;IAChF,IAAI,CAACwH,cAAc,GAAGJ,gBAAgB,CAAC,IAAI,CAACI,cAAc,EAAE,GAAG,IAAI,CAACjR,MAAM,GAAG,IAAI,CAACoQ,cAAc,QAAQ,IAAI,CAACD,cAAc,GAAG,IAAI,CAAC1G,MAAM,EAAE,CAAC;IAC5I,IAAI,CAAClE,aAAa,GAAGsL,gBAAgB,CAAC,IAAI,CAACtL,aAAa,EAAE,GAAG,IAAI,CAAC8K,aAAa,QAAQ,IAAI,CAACE,aAAa,EAAE,CAAC;EAC9G;EACAtG,WAAWA,CAACxN,GAAG,EAAEV,IAAI,EAAEoG,GAAG,EAAErC,OAAO,EAAE;IACnC,IAAIuF,KAAK;IACT,IAAIpJ,KAAK;IACT,IAAIiV,QAAQ;IACZ,MAAMlV,WAAW,GAAG,IAAI,CAAC8D,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC8E,aAAa,IAAI,IAAI,CAAC9E,OAAO,CAAC8E,aAAa,CAACgF,gBAAgB,IAAI,CAAC,CAAC;IACnH,MAAMuH,YAAY,GAAGhX,GAAG,IAAI;MAC1B,IAAIA,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC8V,eAAe,CAAC,GAAG,CAAC,EAAE;QACzC,MAAM1V,IAAI,GAAGgV,oBAAoB,CAACzT,IAAI,EAAEC,WAAW,EAAE7B,GAAG,EAAE,IAAI,CAAC2F,OAAO,CAAC/B,YAAY,EAAE,IAAI,CAAC+B,OAAO,CAAC+B,mBAAmB,CAAC;QACtH,OAAO,IAAI,CAAC8O,YAAY,GAAG,IAAI,CAACf,MAAM,CAACpV,IAAI,EAAEa,SAAS,EAAE8G,GAAG,EAAE;UAC3D,GAAGrC,OAAO;UACV,GAAG/D,IAAI;UACPqV,gBAAgB,EAAEjX;QACpB,CAAC,CAAC,GAAGK,IAAI;MACX;MACA,MAAMe,CAAC,GAAGpB,GAAG,CAACQ,KAAK,CAAC,IAAI,CAACuV,eAAe,CAAC;MACzC,MAAMhV,CAAC,GAAGK,CAAC,CAACkC,KAAK,CAAC,CAAC,CAAC4T,IAAI,CAAC,CAAC;MAC1B,MAAMC,CAAC,GAAG/V,CAAC,CAAC8C,IAAI,CAAC,IAAI,CAAC6R,eAAe,CAAC,CAACmB,IAAI,CAAC,CAAC;MAC7C,OAAO,IAAI,CAACzB,MAAM,CAACJ,oBAAoB,CAACzT,IAAI,EAAEC,WAAW,EAAEd,CAAC,EAAE,IAAI,CAAC4E,OAAO,CAAC/B,YAAY,EAAE,IAAI,CAAC+B,OAAO,CAAC+B,mBAAmB,CAAC,EAAEyP,CAAC,EAAEnP,GAAG,EAAE;QAClI,GAAGrC,OAAO;QACV,GAAG/D,IAAI;QACPqV,gBAAgB,EAAElW;MACpB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC0V,WAAW,CAAC,CAAC;IAClB,MAAMW,2BAA2B,GAAGzR,OAAO,EAAEyR,2BAA2B,IAAI,IAAI,CAACzR,OAAO,CAACyR,2BAA2B;IACpH,MAAMzH,eAAe,GAAGhK,OAAO,EAAE8E,aAAa,EAAEkF,eAAe,KAAKzO,SAAS,GAAGyE,OAAO,CAAC8E,aAAa,CAACkF,eAAe,GAAG,IAAI,CAAChK,OAAO,CAAC8E,aAAa,CAACkF,eAAe;IAClK,MAAM0H,KAAK,GAAG,CAAC;MACbC,KAAK,EAAE,IAAI,CAACR,cAAc;MAC1BS,SAAS,EAAEhC,GAAG,IAAID,SAAS,CAACC,GAAG;IACjC,CAAC,EAAE;MACD+B,KAAK,EAAE,IAAI,CAACT,MAAM;MAClBU,SAAS,EAAEhC,GAAG,IAAI,IAAI,CAACG,WAAW,GAAGJ,SAAS,CAAC,IAAI,CAAC9S,MAAM,CAAC+S,GAAG,CAAC,CAAC,GAAGD,SAAS,CAACC,GAAG;IAClF,CAAC,CAAC;IACF8B,KAAK,CAACzX,OAAO,CAAC4X,IAAI,IAAI;MACpBT,QAAQ,GAAG,CAAC;MACZ,OAAO7L,KAAK,GAAGsM,IAAI,CAACF,KAAK,CAACG,IAAI,CAACnV,GAAG,CAAC,EAAE;QACnC,MAAMoV,UAAU,GAAGxM,KAAK,CAAC,CAAC,CAAC,CAACgM,IAAI,CAAC,CAAC;QAClCpV,KAAK,GAAGkV,YAAY,CAACU,UAAU,CAAC;QAChC,IAAI5V,KAAK,KAAKZ,SAAS,EAAE;UACvB,IAAI,OAAOkW,2BAA2B,KAAK,UAAU,EAAE;YACrD,MAAMO,IAAI,GAAGP,2BAA2B,CAAC9U,GAAG,EAAE4I,KAAK,EAAEvF,OAAO,CAAC;YAC7D7D,KAAK,GAAGjD,QAAQ,CAAC8Y,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;UACpC,CAAC,MAAM,IAAIhS,OAAO,IAAIhF,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC6E,OAAO,EAAE+R,UAAU,CAAC,EAAE;YAC/E5V,KAAK,GAAG,EAAE;UACZ,CAAC,MAAM,IAAI6N,eAAe,EAAE;YAC1B7N,KAAK,GAAGoJ,KAAK,CAAC,CAAC,CAAC;YAChB;UACF,CAAC,MAAM;YACL,IAAI,CAACpF,MAAM,CAACT,IAAI,CAAC,8BAA8BqS,UAAU,sBAAsBpV,GAAG,EAAE,CAAC;YACrFR,KAAK,GAAG,EAAE;UACZ;QACF,CAAC,MAAM,IAAI,CAACjD,QAAQ,CAACiD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC8T,mBAAmB,EAAE;UACxD9T,KAAK,GAAGxC,UAAU,CAACwC,KAAK,CAAC;QAC3B;QACA,MAAMyV,SAAS,GAAGC,IAAI,CAACD,SAAS,CAACzV,KAAK,CAAC;QACvCQ,GAAG,GAAGA,GAAG,CAACpC,OAAO,CAACgL,KAAK,CAAC,CAAC,CAAC,EAAEqM,SAAS,CAAC;QACtC,IAAI5H,eAAe,EAAE;UACnB6H,IAAI,CAACF,KAAK,CAACV,SAAS,IAAI9U,KAAK,CAACpB,MAAM;UACpC8W,IAAI,CAACF,KAAK,CAACV,SAAS,IAAI1L,KAAK,CAAC,CAAC,CAAC,CAACxK,MAAM;QACzC,CAAC,MAAM;UACL8W,IAAI,CAACF,KAAK,CAACV,SAAS,GAAG,CAAC;QAC1B;QACAG,QAAQ,EAAE;QACV,IAAIA,QAAQ,IAAI,IAAI,CAACR,WAAW,EAAE;UAChC;QACF;MACF;IACF,CAAC,CAAC;IACF,OAAOjU,GAAG;EACZ;EACA2N,IAAIA,CAAC3N,GAAG,EAAEgR,EAAE,EAAE3N,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAIuF,KAAK;IACT,IAAIpJ,KAAK;IACT,IAAI8V,aAAa;IACjB,MAAMC,gBAAgB,GAAGA,CAAC7X,GAAG,EAAE8X,gBAAgB,KAAK;MAClD,MAAMC,GAAG,GAAG,IAAI,CAACzB,uBAAuB;MACxC,IAAItW,GAAG,CAACC,OAAO,CAAC8X,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO/X,GAAG;MACpC,MAAM+D,CAAC,GAAG/D,GAAG,CAACQ,KAAK,CAAC,IAAI4C,MAAM,CAAC,GAAG2U,GAAG,OAAO,CAAC,CAAC;MAC9C,IAAIC,aAAa,GAAG,IAAIjU,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B/D,GAAG,GAAG+D,CAAC,CAAC,CAAC,CAAC;MACViU,aAAa,GAAG,IAAI,CAAClI,WAAW,CAACkI,aAAa,EAAEJ,aAAa,CAAC;MAC9D,MAAMK,mBAAmB,GAAGD,aAAa,CAAC9M,KAAK,CAAC,IAAI,CAAC;MACrD,MAAMgN,mBAAmB,GAAGF,aAAa,CAAC9M,KAAK,CAAC,IAAI,CAAC;MACrD,IAAI,CAAC+M,mBAAmB,EAAEvX,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAACwX,mBAAmB,IAAIA,mBAAmB,CAACxX,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QAChHsX,aAAa,GAAGA,aAAa,CAAC9X,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAClD;MACA,IAAI;QACF0X,aAAa,GAAGjP,IAAI,CAACC,KAAK,CAACoP,aAAa,CAAC;QACzC,IAAIF,gBAAgB,EAAEF,aAAa,GAAG;UACpC,GAAGE,gBAAgB;UACnB,GAAGF;QACL,CAAC;MACH,CAAC,CAAC,OAAOzW,CAAC,EAAE;QACV,IAAI,CAAC2E,MAAM,CAACT,IAAI,CAAC,oDAAoDrF,GAAG,EAAE,EAAEmB,CAAC,CAAC;QAC9E,OAAO,GAAGnB,GAAG,GAAG+X,GAAG,GAAGC,aAAa,EAAE;MACvC;MACA,IAAIJ,aAAa,CAACxK,YAAY,IAAIwK,aAAa,CAACxK,YAAY,CAACnN,OAAO,CAAC,IAAI,CAAC4F,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO+R,aAAa,CAACxK,YAAY;MACzH,OAAOpN,GAAG;IACZ,CAAC;IACD,OAAOkL,KAAK,GAAG,IAAI,CAACE,aAAa,CAACqM,IAAI,CAACnV,GAAG,CAAC,EAAE;MAC3C,IAAI6V,UAAU,GAAG,EAAE;MACnBP,aAAa,GAAG;QACd,GAAGjS;MACL,CAAC;MACDiS,aAAa,GAAGA,aAAa,CAAC1X,OAAO,IAAI,CAACrB,QAAQ,CAAC+Y,aAAa,CAAC1X,OAAO,CAAC,GAAG0X,aAAa,CAAC1X,OAAO,GAAG0X,aAAa;MACjHA,aAAa,CAACtH,kBAAkB,GAAG,KAAK;MACxC,OAAOsH,aAAa,CAACxK,YAAY;MACjC,MAAMgL,WAAW,GAAG,MAAM,CAAChU,IAAI,CAAC8G,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACmN,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGnN,KAAK,CAAC,CAAC,CAAC,CAACjL,OAAO,CAAC,IAAI,CAAC8V,eAAe,CAAC;MAClH,IAAIqC,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBD,UAAU,GAAGjN,KAAK,CAAC,CAAC,CAAC,CAAC7J,KAAK,CAAC+W,WAAW,CAAC,CAAC5X,KAAK,CAAC,IAAI,CAACuV,eAAe,CAAC,CAAC9R,GAAG,CAACqU,IAAI,IAAIA,IAAI,CAACpB,IAAI,CAAC,CAAC,CAAC,CAACpT,MAAM,CAACyU,OAAO,CAAC;QAC7GrN,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC7J,KAAK,CAAC,CAAC,EAAE+W,WAAW,CAAC;MAC3C;MACAtW,KAAK,GAAGwR,EAAE,CAACuE,gBAAgB,CAAC/W,IAAI,CAAC,IAAI,EAAEoK,KAAK,CAAC,CAAC,CAAC,CAACgM,IAAI,CAAC,CAAC,EAAEU,aAAa,CAAC,EAAEA,aAAa,CAAC;MACtF,IAAI9V,KAAK,IAAIoJ,KAAK,CAAC,CAAC,CAAC,KAAK5I,GAAG,IAAI,CAACzD,QAAQ,CAACiD,KAAK,CAAC,EAAE,OAAOA,KAAK;MAC/D,IAAI,CAACjD,QAAQ,CAACiD,KAAK,CAAC,EAAEA,KAAK,GAAGxC,UAAU,CAACwC,KAAK,CAAC;MAC/C,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACgE,MAAM,CAACT,IAAI,CAAC,qBAAqB6F,KAAK,CAAC,CAAC,CAAC,gBAAgB5I,GAAG,EAAE,CAAC;QACpER,KAAK,GAAG,EAAE;MACZ;MACA,IAAIqW,UAAU,CAACzX,MAAM,EAAE;QACrBoB,KAAK,GAAGqW,UAAU,CAACK,MAAM,CAAC,CAAClP,CAAC,EAAE6N,CAAC,KAAK,IAAI,CAAC1B,MAAM,CAACnM,CAAC,EAAE6N,CAAC,EAAExR,OAAO,CAACqC,GAAG,EAAE;UACjE,GAAGrC,OAAO;UACVsR,gBAAgB,EAAE/L,KAAK,CAAC,CAAC,CAAC,CAACgM,IAAI,CAAC;QAClC,CAAC,CAAC,EAAEpV,KAAK,CAACoV,IAAI,CAAC,CAAC,CAAC;MACnB;MACA5U,GAAG,GAAGA,GAAG,CAACpC,OAAO,CAACgL,KAAK,CAAC,CAAC,CAAC,EAAEpJ,KAAK,CAAC;MAClC,IAAI,CAAC+U,MAAM,CAACD,SAAS,GAAG,CAAC;IAC3B;IACA,OAAOtU,GAAG;EACZ;AACF;AAEA,MAAMmW,cAAc,GAAGC,SAAS,IAAI;EAClC,IAAIC,UAAU,GAAGD,SAAS,CAAC7M,WAAW,CAAC,CAAC,CAACqL,IAAI,CAAC,CAAC;EAC/C,MAAM0B,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIF,SAAS,CAACzY,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAMmB,CAAC,GAAGsX,SAAS,CAAClY,KAAK,CAAC,GAAG,CAAC;IAC9BmY,UAAU,GAAGvX,CAAC,CAAC,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC,CAACqL,IAAI,CAAC,CAAC;IACtC,MAAM2B,MAAM,GAAGzX,CAAC,CAAC,CAAC,CAAC,CAACkD,SAAS,CAAC,CAAC,EAAElD,CAAC,CAAC,CAAC,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;IACjD,IAAIiY,UAAU,KAAK,UAAU,IAAIE,MAAM,CAAC5Y,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACxD,IAAI,CAAC2Y,aAAa,CAACE,QAAQ,EAAEF,aAAa,CAACE,QAAQ,GAAGD,MAAM,CAAC3B,IAAI,CAAC,CAAC;IACrE,CAAC,MAAM,IAAIyB,UAAU,KAAK,cAAc,IAAIE,MAAM,CAAC5Y,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACnE,IAAI,CAAC2Y,aAAa,CAACG,KAAK,EAAEH,aAAa,CAACG,KAAK,GAAGF,MAAM,CAAC3B,IAAI,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,MAAM8B,IAAI,GAAGH,MAAM,CAACrY,KAAK,CAAC,GAAG,CAAC;MAC9BwY,IAAI,CAACpZ,OAAO,CAAC8K,GAAG,IAAI;QAClB,IAAIA,GAAG,EAAE;UACP,MAAM,CAAC1K,GAAG,EAAE,GAAGiZ,IAAI,CAAC,GAAGvO,GAAG,CAAClK,KAAK,CAAC,GAAG,CAAC;UACrC,MAAM+U,GAAG,GAAG0D,IAAI,CAAC/U,IAAI,CAAC,GAAG,CAAC,CAACgT,IAAI,CAAC,CAAC,CAAChX,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;UACzD,MAAMgZ,UAAU,GAAGlZ,GAAG,CAACkX,IAAI,CAAC,CAAC;UAC7B,IAAI,CAAC0B,aAAa,CAACM,UAAU,CAAC,EAAEN,aAAa,CAACM,UAAU,CAAC,GAAG3D,GAAG;UAC/D,IAAIA,GAAG,KAAK,OAAO,EAAEqD,aAAa,CAACM,UAAU,CAAC,GAAG,KAAK;UACtD,IAAI3D,GAAG,KAAK,MAAM,EAAEqD,aAAa,CAACM,UAAU,CAAC,GAAG,IAAI;UACpD,IAAI,CAACC,KAAK,CAAC5D,GAAG,CAAC,EAAEqD,aAAa,CAACM,UAAU,CAAC,GAAGE,QAAQ,CAAC7D,GAAG,EAAE,EAAE,CAAC;QAChE;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAO;IACLoD,UAAU;IACVC;EACF,CAAC;AACH,CAAC;AACD,MAAMS,qBAAqB,GAAGC,EAAE,IAAI;EAClC,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,OAAO,CAACjQ,CAAC,EAAEuF,CAAC,EAAErE,CAAC,KAAK;IAClB,IAAIgP,WAAW,GAAGhP,CAAC;IACnB,IAAIA,CAAC,IAAIA,CAAC,CAACyM,gBAAgB,IAAIzM,CAAC,CAACiP,YAAY,IAAIjP,CAAC,CAACiP,YAAY,CAACjP,CAAC,CAACyM,gBAAgB,CAAC,IAAIzM,CAAC,CAACA,CAAC,CAACyM,gBAAgB,CAAC,EAAE;MAC5GuC,WAAW,GAAG;QACZ,GAAGA,WAAW;QACd,CAAChP,CAAC,CAACyM,gBAAgB,GAAG/V;MACxB,CAAC;IACH;IACA,MAAMlB,GAAG,GAAG6O,CAAC,GAAGlG,IAAI,CAACE,SAAS,CAAC2Q,WAAW,CAAC;IAC3C,IAAIE,GAAG,GAAGH,KAAK,CAACvZ,GAAG,CAAC;IACpB,IAAI,CAAC0Z,GAAG,EAAE;MACRA,GAAG,GAAGJ,EAAE,CAACxU,cAAc,CAAC+J,CAAC,CAAC,EAAErE,CAAC,CAAC;MAC9B+O,KAAK,CAACvZ,GAAG,CAAC,GAAG0Z,GAAG;IAClB;IACA,OAAOA,GAAG,CAACpQ,CAAC,CAAC;EACf,CAAC;AACH,CAAC;AACD,MAAMqQ,wBAAwB,GAAGL,EAAE,IAAI,CAAChQ,CAAC,EAAEuF,CAAC,EAAErE,CAAC,KAAK8O,EAAE,CAACxU,cAAc,CAAC+J,CAAC,CAAC,EAAErE,CAAC,CAAC,CAAClB,CAAC,CAAC;AAC/E,MAAMsQ,SAAS,CAAC;EACdlX,WAAWA,CAACiD,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACG,MAAM,GAAGS,UAAU,CAACH,MAAM,CAAC,WAAW,CAAC;IAC5C,IAAI,CAACT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,CAACD,OAAO,CAAC;EACpB;EACAC,IAAIA,CAACwE,QAAQ,EAAEzE,OAAO,GAAG;IACvB8E,aAAa,EAAE,CAAC;EAClB,CAAC,EAAE;IACD,IAAI,CAACsL,eAAe,GAAGpQ,OAAO,CAAC8E,aAAa,CAACsL,eAAe,IAAI,GAAG;IACnE,MAAM8D,EAAE,GAAGlU,OAAO,CAACmU,mBAAmB,GAAGT,qBAAqB,GAAGM,wBAAwB;IACzF,IAAI,CAACI,OAAO,GAAG;MACbC,MAAM,EAAEH,EAAE,CAAC,CAAC7R,GAAG,EAAE0C,GAAG,KAAK;QACvB,MAAMuP,SAAS,GAAG,IAAI5H,IAAI,CAAC6H,YAAY,CAAClS,GAAG,EAAE;UAC3C,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO6K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACFuD,QAAQ,EAAEe,EAAE,CAAC,CAAC7R,GAAG,EAAE0C,GAAG,KAAK;QACzB,MAAMuP,SAAS,GAAG,IAAI5H,IAAI,CAAC6H,YAAY,CAAClS,GAAG,EAAE;UAC3C,GAAG0C,GAAG;UACNyP,KAAK,EAAE;QACT,CAAC,CAAC;QACF,OAAO5E,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACF6E,QAAQ,EAAEP,EAAE,CAAC,CAAC7R,GAAG,EAAE0C,GAAG,KAAK;QACzB,MAAMuP,SAAS,GAAG,IAAI5H,IAAI,CAACgI,cAAc,CAACrS,GAAG,EAAE;UAC7C,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO6K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACF+E,YAAY,EAAET,EAAE,CAAC,CAAC7R,GAAG,EAAE0C,GAAG,KAAK;QAC7B,MAAMuP,SAAS,GAAG,IAAI5H,IAAI,CAACkI,kBAAkB,CAACvS,GAAG,EAAE;UACjD,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO6K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,EAAE7K,GAAG,CAACqO,KAAK,IAAI,KAAK,CAAC;MACzD,CAAC,CAAC;MACFyB,IAAI,EAAEX,EAAE,CAAC,CAAC7R,GAAG,EAAE0C,GAAG,KAAK;QACrB,MAAMuP,SAAS,GAAG,IAAI5H,IAAI,CAACoI,UAAU,CAACzS,GAAG,EAAE;UACzC,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO6K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC;IACH,CAAC;EACH;EACAmF,GAAGA,CAAC9Q,IAAI,EAAE0J,EAAE,EAAE;IACZ,IAAI,CAACyG,OAAO,CAACnQ,IAAI,CAACiC,WAAW,CAAC,CAAC,CAACqL,IAAI,CAAC,CAAC,CAAC,GAAG5D,EAAE;EAC9C;EACAqH,SAASA,CAAC/Q,IAAI,EAAE0J,EAAE,EAAE;IAClB,IAAI,CAACyG,OAAO,CAACnQ,IAAI,CAACiC,WAAW,CAAC,CAAC,CAACqL,IAAI,CAAC,CAAC,CAAC,GAAGmC,qBAAqB,CAAC/F,EAAE,CAAC;EACrE;EACAmC,MAAMA,CAAC3T,KAAK,EAAE2T,MAAM,EAAEzN,GAAG,EAAErC,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,MAAMoU,OAAO,GAAGtE,MAAM,CAACjV,KAAK,CAAC,IAAI,CAACuV,eAAe,CAAC;IAClD,IAAIgE,OAAO,CAACrZ,MAAM,GAAG,CAAC,IAAIqZ,OAAO,CAAC,CAAC,CAAC,CAAC9Z,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI8Z,OAAO,CAAC,CAAC,CAAC,CAAC9Z,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI8Z,OAAO,CAAC1Q,IAAI,CAAC8N,CAAC,IAAIA,CAAC,CAAClX,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC9H,MAAM2W,SAAS,GAAGmD,OAAO,CAACa,SAAS,CAACzD,CAAC,IAAIA,CAAC,CAAClX,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7D8Z,OAAO,CAAC,CAAC,CAAC,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,EAAE,GAAGA,OAAO,CAACjS,MAAM,CAAC,CAAC,EAAE8O,SAAS,CAAC,CAAC,CAAC1S,IAAI,CAAC,IAAI,CAAC6R,eAAe,CAAC;IACvF;IACA,MAAM7N,MAAM,GAAG6R,OAAO,CAACvB,MAAM,CAAC,CAACqC,GAAG,EAAE1D,CAAC,KAAK;MACxC,MAAM;QACJwB,UAAU;QACVC;MACF,CAAC,GAAGH,cAAc,CAACtB,CAAC,CAAC;MACrB,IAAI,IAAI,CAAC4C,OAAO,CAACpB,UAAU,CAAC,EAAE;QAC5B,IAAImC,SAAS,GAAGD,GAAG;QACnB,IAAI;UACF,MAAME,UAAU,GAAGpV,OAAO,EAAE8T,YAAY,GAAG9T,OAAO,CAACsR,gBAAgB,CAAC,IAAI,CAAC,CAAC;UAC1E,MAAMpI,CAAC,GAAGkM,UAAU,CAACC,MAAM,IAAID,UAAU,CAAC/S,GAAG,IAAIrC,OAAO,CAACqV,MAAM,IAAIrV,OAAO,CAACqC,GAAG,IAAIA,GAAG;UACrF8S,SAAS,GAAG,IAAI,CAACf,OAAO,CAACpB,UAAU,CAAC,CAACkC,GAAG,EAAEhM,CAAC,EAAE;YAC3C,GAAG+J,aAAa;YAChB,GAAGjT,OAAO;YACV,GAAGoV;UACL,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOzV,KAAK,EAAE;UACd,IAAI,CAACQ,MAAM,CAACT,IAAI,CAACC,KAAK,CAAC;QACzB;QACA,OAAOwV,SAAS;MAClB,CAAC,MAAM;QACL,IAAI,CAAChV,MAAM,CAACT,IAAI,CAAC,oCAAoCsT,UAAU,EAAE,CAAC;MACpE;MACA,OAAOkC,GAAG;IACZ,CAAC,EAAE/Y,KAAK,CAAC;IACT,OAAOoG,MAAM;EACf;AACF;AAEA,MAAM+S,aAAa,GAAGA,CAACC,CAAC,EAAEtR,IAAI,KAAK;EACjC,IAAIsR,CAAC,CAACC,OAAO,CAACvR,IAAI,CAAC,KAAK1I,SAAS,EAAE;IACjC,OAAOga,CAAC,CAACC,OAAO,CAACvR,IAAI,CAAC;IACtBsR,CAAC,CAACE,YAAY,EAAE;EAClB;AACF,CAAC;AACD,MAAMC,SAAS,SAAS7U,YAAY,CAAC;EACnC9D,WAAWA,CAAC4Y,OAAO,EAAEC,KAAK,EAAEnR,QAAQ,EAAEzE,OAAO,GAAG,CAAC,CAAC,EAAE;IAClD,KAAK,CAAC,CAAC;IACP,IAAI,CAAC2V,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACnR,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACmE,aAAa,GAAGnE,QAAQ,CAACmE,aAAa;IAC3C,IAAI,CAAC5I,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,MAAM,GAAGS,UAAU,CAACH,MAAM,CAAC,kBAAkB,CAAC;IACnD,IAAI,CAACoV,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAG9V,OAAO,CAAC8V,gBAAgB,IAAI,EAAE;IACtD,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,UAAU,GAAGhW,OAAO,CAACgW,UAAU,IAAI,CAAC,GAAGhW,OAAO,CAACgW,UAAU,GAAG,CAAC;IAClE,IAAI,CAACC,YAAY,GAAGjW,OAAO,CAACiW,YAAY,IAAI,CAAC,GAAGjW,OAAO,CAACiW,YAAY,GAAG,GAAG;IAC1E,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACR,OAAO,EAAE1V,IAAI,GAAGwE,QAAQ,EAAEzE,OAAO,CAAC2V,OAAO,EAAE3V,OAAO,CAAC;EAC1D;EACAoW,SAASA,CAACC,SAAS,EAAEnR,UAAU,EAAElF,OAAO,EAAEsW,QAAQ,EAAE;IAClD,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMf,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMgB,eAAe,GAAG,CAAC,CAAC;IAC1B,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAC3BJ,SAAS,CAACpc,OAAO,CAACoI,GAAG,IAAI;MACvB,IAAIqU,gBAAgB,GAAG,IAAI;MAC3BxR,UAAU,CAACjL,OAAO,CAAC4H,EAAE,IAAI;QACvB,MAAMoC,IAAI,GAAG,GAAG5B,GAAG,IAAIR,EAAE,EAAE;QAC3B,IAAI,CAAC7B,OAAO,CAAC2W,MAAM,IAAI,IAAI,CAACf,KAAK,CAACxS,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE;UAC5D,IAAI,CAACqU,KAAK,CAACjS,IAAI,CAAC,GAAG,CAAC;QACtB,CAAC,MAAM,IAAI,IAAI,CAACiS,KAAK,CAACjS,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAM,IAAI,IAAI,CAACiS,KAAK,CAACjS,IAAI,CAAC,KAAK,CAAC,EAAE;UAClE,IAAIuR,OAAO,CAACvR,IAAI,CAAC,KAAK1I,SAAS,EAAEia,OAAO,CAACvR,IAAI,CAAC,GAAG,IAAI;QACvD,CAAC,MAAM;UACL,IAAI,CAACiS,KAAK,CAACjS,IAAI,CAAC,GAAG,CAAC;UACpByS,gBAAgB,GAAG,KAAK;UACxB,IAAIlB,OAAO,CAACvR,IAAI,CAAC,KAAK1I,SAAS,EAAEia,OAAO,CAACvR,IAAI,CAAC,GAAG,IAAI;UACrD,IAAIsS,MAAM,CAACtS,IAAI,CAAC,KAAK1I,SAAS,EAAEgb,MAAM,CAACtS,IAAI,CAAC,GAAG,IAAI;UACnD,IAAIwS,gBAAgB,CAAC5U,EAAE,CAAC,KAAKtG,SAAS,EAAEkb,gBAAgB,CAAC5U,EAAE,CAAC,GAAG,IAAI;QACrE;MACF,CAAC,CAAC;MACF,IAAI,CAAC6U,gBAAgB,EAAEF,eAAe,CAACnU,GAAG,CAAC,GAAG,IAAI;IACpD,CAAC,CAAC;IACF,IAAIrH,MAAM,CAACyI,IAAI,CAAC8S,MAAM,CAAC,CAACxb,MAAM,IAAIC,MAAM,CAACyI,IAAI,CAAC+R,OAAO,CAAC,CAACza,MAAM,EAAE;MAC7D,IAAI,CAACob,KAAK,CAACra,IAAI,CAAC;QACd0Z,OAAO;QACPC,YAAY,EAAEza,MAAM,CAACyI,IAAI,CAAC+R,OAAO,CAAC,CAACza,MAAM;QACzC6b,MAAM,EAAE,CAAC,CAAC;QACVC,MAAM,EAAE,EAAE;QACVP;MACF,CAAC,CAAC;IACJ;IACA,OAAO;MACLC,MAAM,EAAEvb,MAAM,CAACyI,IAAI,CAAC8S,MAAM,CAAC;MAC3Bf,OAAO,EAAExa,MAAM,CAACyI,IAAI,CAAC+R,OAAO,CAAC;MAC7BgB,eAAe,EAAExb,MAAM,CAACyI,IAAI,CAAC+S,eAAe,CAAC;MAC7CC,gBAAgB,EAAEzb,MAAM,CAACyI,IAAI,CAACgT,gBAAgB;IAChD,CAAC;EACH;EACAG,MAAMA,CAAC3S,IAAI,EAAEgL,GAAG,EAAEhT,IAAI,EAAE;IACtB,MAAMlC,CAAC,GAAGkK,IAAI,CAACpJ,KAAK,CAAC,GAAG,CAAC;IACzB,MAAMwH,GAAG,GAAGtI,CAAC,CAAC,CAAC,CAAC;IAChB,MAAM8H,EAAE,GAAG9H,CAAC,CAAC,CAAC,CAAC;IACf,IAAIkV,GAAG,EAAE,IAAI,CAAC5N,IAAI,CAAC,eAAe,EAAEgB,GAAG,EAAER,EAAE,EAAEoN,GAAG,CAAC;IACjD,IAAI,CAACA,GAAG,IAAIhT,IAAI,EAAE;MAChB,IAAI,CAAC2Z,KAAK,CAAChT,iBAAiB,CAACP,GAAG,EAAER,EAAE,EAAE5F,IAAI,EAAEV,SAAS,EAAEA,SAAS,EAAE;QAChEuH,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,IAAI,CAACoT,KAAK,CAACjS,IAAI,CAAC,GAAGgL,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/B,IAAIA,GAAG,IAAIhT,IAAI,EAAE,IAAI,CAACia,KAAK,CAACjS,IAAI,CAAC,GAAG,CAAC;IACrC,MAAM2S,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAACT,KAAK,CAAClc,OAAO,CAACsb,CAAC,IAAI;MACtB3Z,QAAQ,CAAC2Z,CAAC,CAACqB,MAAM,EAAE,CAACvU,GAAG,CAAC,EAAER,EAAE,CAAC;MAC7ByT,aAAa,CAACC,CAAC,EAAEtR,IAAI,CAAC;MACtB,IAAIgL,GAAG,EAAEsG,CAAC,CAACsB,MAAM,CAAC/a,IAAI,CAACmT,GAAG,CAAC;MAC3B,IAAIsG,CAAC,CAACE,YAAY,KAAK,CAAC,IAAI,CAACF,CAAC,CAACuB,IAAI,EAAE;QACnC9b,MAAM,CAACyI,IAAI,CAAC8R,CAAC,CAACqB,MAAM,CAAC,CAAC3c,OAAO,CAACiP,CAAC,IAAI;UACjC,IAAI,CAAC0N,MAAM,CAAC1N,CAAC,CAAC,EAAE0N,MAAM,CAAC1N,CAAC,CAAC,GAAG,CAAC,CAAC;UAC9B,MAAM6N,UAAU,GAAGxB,CAAC,CAACqB,MAAM,CAAC1N,CAAC,CAAC;UAC9B,IAAI6N,UAAU,CAAChc,MAAM,EAAE;YACrBgc,UAAU,CAAC9c,OAAO,CAACuJ,CAAC,IAAI;cACtB,IAAIoT,MAAM,CAAC1N,CAAC,CAAC,CAAC1F,CAAC,CAAC,KAAKjI,SAAS,EAAEqb,MAAM,CAAC1N,CAAC,CAAC,CAAC1F,CAAC,CAAC,GAAG,IAAI;YACrD,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QACF+R,CAAC,CAACuB,IAAI,GAAG,IAAI;QACb,IAAIvB,CAAC,CAACsB,MAAM,CAAC9b,MAAM,EAAE;UACnBwa,CAAC,CAACe,QAAQ,CAACf,CAAC,CAACsB,MAAM,CAAC;QACtB,CAAC,MAAM;UACLtB,CAAC,CAACe,QAAQ,CAAC,CAAC;QACd;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACjV,IAAI,CAAC,QAAQ,EAAEuV,MAAM,CAAC;IAC3B,IAAI,CAACT,KAAK,GAAG,IAAI,CAACA,KAAK,CAAChY,MAAM,CAACoX,CAAC,IAAI,CAACA,CAAC,CAACuB,IAAI,CAAC;EAC9C;EACAE,IAAIA,CAAC3U,GAAG,EAAER,EAAE,EAAEoV,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAI,CAAClB,YAAY,EAAEK,QAAQ,EAAE;IACnE,IAAI,CAACjU,GAAG,CAACtH,MAAM,EAAE,OAAOub,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,CAACP,YAAY,IAAI,IAAI,CAACD,gBAAgB,EAAE;MAC9C,IAAI,CAACD,YAAY,CAAC/Z,IAAI,CAAC;QACrBuG,GAAG;QACHR,EAAE;QACFoV,MAAM;QACNC,KAAK;QACLC,IAAI;QACJb;MACF,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACP,YAAY,EAAE;IACnB,MAAMqB,QAAQ,GAAGA,CAACnI,GAAG,EAAEhT,IAAI,KAAK;MAC9B,IAAI,CAAC8Z,YAAY,EAAE;MACnB,IAAI,IAAI,CAACF,YAAY,CAAC9a,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMiE,IAAI,GAAG,IAAI,CAAC6W,YAAY,CAAClY,KAAK,CAAC,CAAC;QACtC,IAAI,CAACqZ,IAAI,CAAChY,IAAI,CAACqD,GAAG,EAAErD,IAAI,CAAC6C,EAAE,EAAE7C,IAAI,CAACiY,MAAM,EAAEjY,IAAI,CAACkY,KAAK,EAAElY,IAAI,CAACmY,IAAI,EAAEnY,IAAI,CAACsX,QAAQ,CAAC;MACjF;MACA,IAAIrH,GAAG,IAAIhT,IAAI,IAAIib,KAAK,GAAG,IAAI,CAAClB,UAAU,EAAE;QAC1CqB,UAAU,CAAC,MAAM;UACf,IAAI,CAACL,IAAI,CAAC7b,IAAI,CAAC,IAAI,EAAEkH,GAAG,EAAER,EAAE,EAAEoV,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEb,QAAQ,CAAC;QACtE,CAAC,EAAEa,IAAI,CAAC;QACR;MACF;MACAb,QAAQ,CAACrH,GAAG,EAAEhT,IAAI,CAAC;IACrB,CAAC;IACD,MAAM0R,EAAE,GAAG,IAAI,CAACgI,OAAO,CAACsB,MAAM,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC;IAClD,IAAIhI,EAAE,CAAC5S,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI;QACF,MAAMsD,CAAC,GAAGsP,EAAE,CAACtL,GAAG,EAAER,EAAE,CAAC;QACrB,IAAIxD,CAAC,IAAI,OAAOA,CAAC,CAACkZ,IAAI,KAAK,UAAU,EAAE;UACrClZ,CAAC,CAACkZ,IAAI,CAACtb,IAAI,IAAImb,QAAQ,CAAC,IAAI,EAAEnb,IAAI,CAAC,CAAC,CAACub,KAAK,CAACJ,QAAQ,CAAC;QACtD,CAAC,MAAM;UACLA,QAAQ,CAAC,IAAI,EAAE/Y,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAO4Q,GAAG,EAAE;QACZmI,QAAQ,CAACnI,GAAG,CAAC;MACf;MACA;IACF;IACA,OAAOtB,EAAE,CAACtL,GAAG,EAAER,EAAE,EAAEuV,QAAQ,CAAC;EAC9B;EACAK,cAAcA,CAACpB,SAAS,EAAEnR,UAAU,EAAElF,OAAO,GAAG,CAAC,CAAC,EAAEsW,QAAQ,EAAE;IAC5D,IAAI,CAAC,IAAI,CAACX,OAAO,EAAE;MACjB,IAAI,CAACxV,MAAM,CAACT,IAAI,CAAC,gEAAgE,CAAC;MAClF,OAAO4W,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAIpd,QAAQ,CAACmd,SAAS,CAAC,EAAEA,SAAS,GAAG,IAAI,CAACzN,aAAa,CAACI,kBAAkB,CAACqN,SAAS,CAAC;IACrF,IAAInd,QAAQ,CAACgM,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;IACnD,MAAMqR,MAAM,GAAG,IAAI,CAACH,SAAS,CAACC,SAAS,EAAEnR,UAAU,EAAElF,OAAO,EAAEsW,QAAQ,CAAC;IACvE,IAAI,CAACC,MAAM,CAACA,MAAM,CAACxb,MAAM,EAAE;MACzB,IAAI,CAACwb,MAAM,CAACf,OAAO,CAACza,MAAM,EAAEub,QAAQ,CAAC,CAAC;MACtC,OAAO,IAAI;IACb;IACAC,MAAM,CAACA,MAAM,CAACtc,OAAO,CAACgK,IAAI,IAAI;MAC5B,IAAI,CAACyT,OAAO,CAACzT,IAAI,CAAC;IACpB,CAAC,CAAC;EACJ;EACA8I,IAAIA,CAACsJ,SAAS,EAAEnR,UAAU,EAAEoR,QAAQ,EAAE;IACpC,IAAI,CAACmB,cAAc,CAACpB,SAAS,EAAEnR,UAAU,EAAE,CAAC,CAAC,EAAEoR,QAAQ,CAAC;EAC1D;EACAK,MAAMA,CAACN,SAAS,EAAEnR,UAAU,EAAEoR,QAAQ,EAAE;IACtC,IAAI,CAACmB,cAAc,CAACpB,SAAS,EAAEnR,UAAU,EAAE;MACzCyR,MAAM,EAAE;IACV,CAAC,EAAEL,QAAQ,CAAC;EACd;EACAoB,OAAOA,CAACzT,IAAI,EAAE/D,MAAM,GAAG,EAAE,EAAE;IACzB,MAAMnG,CAAC,GAAGkK,IAAI,CAACpJ,KAAK,CAAC,GAAG,CAAC;IACzB,MAAMwH,GAAG,GAAGtI,CAAC,CAAC,CAAC,CAAC;IAChB,MAAM8H,EAAE,GAAG9H,CAAC,CAAC,CAAC,CAAC;IACf,IAAI,CAACid,IAAI,CAAC3U,GAAG,EAAER,EAAE,EAAE,MAAM,EAAEtG,SAAS,EAAEA,SAAS,EAAE,CAAC0T,GAAG,EAAEhT,IAAI,KAAK;MAC9D,IAAIgT,GAAG,EAAE,IAAI,CAAC9O,MAAM,CAACT,IAAI,CAAC,GAAGQ,MAAM,qBAAqB2B,EAAE,iBAAiBQ,GAAG,SAAS,EAAE4M,GAAG,CAAC;MAC7F,IAAI,CAACA,GAAG,IAAIhT,IAAI,EAAE,IAAI,CAACkE,MAAM,CAACZ,GAAG,CAAC,GAAGW,MAAM,oBAAoB2B,EAAE,iBAAiBQ,GAAG,EAAE,EAAEpG,IAAI,CAAC;MAC9F,IAAI,CAAC2a,MAAM,CAAC3S,IAAI,EAAEgL,GAAG,EAAEhT,IAAI,CAAC;IAC9B,CAAC,CAAC;EACJ;EACAsN,WAAWA,CAAC8M,SAAS,EAAErQ,SAAS,EAAE3L,GAAG,EAAEsd,aAAa,EAAEC,QAAQ,EAAE5X,OAAO,GAAG,CAAC,CAAC,EAAE6X,GAAG,GAAGA,CAAA,KAAM,CAAC,CAAC,EAAE;IAC5F,IAAI,IAAI,CAACpT,QAAQ,EAAE0G,KAAK,EAAEC,kBAAkB,IAAI,CAAC,IAAI,CAAC3G,QAAQ,EAAE0G,KAAK,EAAEC,kBAAkB,CAACpF,SAAS,CAAC,EAAE;MACpG,IAAI,CAAC7F,MAAM,CAACT,IAAI,CAAC,qBAAqBrF,GAAG,uBAAuB2L,SAAS,sBAAsB,EAAE,0NAA0N,CAAC;MAC5T;IACF;IACA,IAAI3L,GAAG,KAAKkB,SAAS,IAAIlB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,EAAE;IACrD,IAAI,IAAI,CAACsb,OAAO,EAAElV,MAAM,EAAE;MACxB,MAAM4S,IAAI,GAAG;QACX,GAAGrT,OAAO;QACV4X;MACF,CAAC;MACD,MAAMjK,EAAE,GAAG,IAAI,CAACgI,OAAO,CAAClV,MAAM,CAAC6W,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC;MACjD,IAAIhI,EAAE,CAAC5S,MAAM,GAAG,CAAC,EAAE;QACjB,IAAI;UACF,IAAIsD,CAAC;UACL,IAAIsP,EAAE,CAAC5S,MAAM,KAAK,CAAC,EAAE;YACnBsD,CAAC,GAAGsP,EAAE,CAAC0I,SAAS,EAAErQ,SAAS,EAAE3L,GAAG,EAAEsd,aAAa,EAAEtE,IAAI,CAAC;UACxD,CAAC,MAAM;YACLhV,CAAC,GAAGsP,EAAE,CAAC0I,SAAS,EAAErQ,SAAS,EAAE3L,GAAG,EAAEsd,aAAa,CAAC;UAClD;UACA,IAAItZ,CAAC,IAAI,OAAOA,CAAC,CAACkZ,IAAI,KAAK,UAAU,EAAE;YACrClZ,CAAC,CAACkZ,IAAI,CAACtb,IAAI,IAAI4b,GAAG,CAAC,IAAI,EAAE5b,IAAI,CAAC,CAAC,CAACub,KAAK,CAACK,GAAG,CAAC;UAC5C,CAAC,MAAM;YACLA,GAAG,CAAC,IAAI,EAAExZ,CAAC,CAAC;UACd;QACF,CAAC,CAAC,OAAO4Q,GAAG,EAAE;UACZ4I,GAAG,CAAC5I,GAAG,CAAC;QACV;MACF,CAAC,MAAM;QACLtB,EAAE,CAAC0I,SAAS,EAAErQ,SAAS,EAAE3L,GAAG,EAAEsd,aAAa,EAAEE,GAAG,EAAExE,IAAI,CAAC;MACzD;IACF;IACA,IAAI,CAACgD,SAAS,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EAAE;IACjC,IAAI,CAACT,KAAK,CAACpT,WAAW,CAAC6T,SAAS,CAAC,CAAC,CAAC,EAAErQ,SAAS,EAAE3L,GAAG,EAAEsd,aAAa,CAAC;EACrE;AACF;AAEA,MAAMpa,GAAG,GAAGA,CAAA,MAAO;EACjB6C,KAAK,EAAE,KAAK;EACZ0X,SAAS,EAAE,IAAI;EACfjW,EAAE,EAAE,CAAC,aAAa,CAAC;EACnBC,SAAS,EAAE,CAAC,aAAa,CAAC;EAC1BgH,WAAW,EAAE,CAAC,KAAK,CAAC;EACpBkC,UAAU,EAAE,KAAK;EACjBqB,aAAa,EAAE,KAAK;EACpBW,wBAAwB,EAAE,KAAK;EAC/BD,IAAI,EAAE,KAAK;EACXgL,OAAO,EAAE,KAAK;EACdC,oBAAoB,EAAE,IAAI;EAC1B/Z,YAAY,EAAE,GAAG;EACjBD,WAAW,EAAE,GAAG;EAChB0J,eAAe,EAAE,GAAG;EACpBiE,gBAAgB,EAAE,GAAG;EACrBsM,uBAAuB,EAAE,KAAK;EAC9B1O,WAAW,EAAE,KAAK;EAClBf,aAAa,EAAE,KAAK;EACpBO,aAAa,EAAE,UAAU;EACzBS,kBAAkB,EAAE,IAAI;EACxBH,iBAAiB,EAAE,KAAK;EACxBoI,2BAA2B,EAAE,KAAK;EAClChH,WAAW,EAAE,KAAK;EAClBG,uBAAuB,EAAE,KAAK;EAC9BkB,UAAU,EAAE,KAAK;EACjBC,iBAAiB,EAAE,IAAI;EACvBjE,aAAa,EAAE,KAAK;EACpBlB,UAAU,EAAE,KAAK;EACjBmB,qBAAqB,EAAE,KAAK;EAC5B8B,sBAAsB,EAAE,KAAK;EAC7BD,2BAA2B,EAAE,KAAK;EAClC3D,uBAAuB,EAAE,KAAK;EAC9BJ,gCAAgC,EAAErG,IAAI,IAAI;IACxC,IAAI0Y,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,OAAO1Y,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE0Y,GAAG,GAAG1Y,IAAI,CAAC,CAAC,CAAC;IAC9C,IAAItG,QAAQ,CAACsG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE0Y,GAAG,CAACzQ,YAAY,GAAGjI,IAAI,CAAC,CAAC,CAAC;IACjD,IAAItG,QAAQ,CAACsG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE0Y,GAAG,CAACC,YAAY,GAAG3Y,IAAI,CAAC,CAAC,CAAC;IACjD,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC9D,MAAMQ,OAAO,GAAGR,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC;MAClCxE,MAAM,CAACyI,IAAI,CAACzD,OAAO,CAAC,CAAC/F,OAAO,CAACI,GAAG,IAAI;QAClC6d,GAAG,CAAC7d,GAAG,CAAC,GAAG2F,OAAO,CAAC3F,GAAG,CAAC;MACzB,CAAC,CAAC;IACJ;IACA,OAAO6d,GAAG;EACZ,CAAC;EACDpT,aAAa,EAAE;IACbiL,WAAW,EAAE,IAAI;IACjBD,MAAM,EAAE3T,KAAK,IAAIA,KAAK;IACtB+D,MAAM,EAAE,IAAI;IACZyJ,MAAM,EAAE,IAAI;IACZyG,eAAe,EAAE,GAAG;IACpBE,cAAc,EAAE,GAAG;IACnBC,aAAa,EAAE,KAAK;IACpBE,aAAa,EAAE,GAAG;IAClBE,uBAAuB,EAAE,GAAG;IAC5BC,WAAW,EAAE,IAAI;IACjB5G,eAAe,EAAE;EACnB,CAAC;EACDmK,mBAAmB,EAAE;AACvB,CAAC,CAAC;AACF,MAAMiE,gBAAgB,GAAGpY,OAAO,IAAI;EAClC,IAAI9G,QAAQ,CAAC8G,OAAO,CAAC6B,EAAE,CAAC,EAAE7B,OAAO,CAAC6B,EAAE,GAAG,CAAC7B,OAAO,CAAC6B,EAAE,CAAC;EACnD,IAAI3I,QAAQ,CAAC8G,OAAO,CAAC8I,WAAW,CAAC,EAAE9I,OAAO,CAAC8I,WAAW,GAAG,CAAC9I,OAAO,CAAC8I,WAAW,CAAC;EAC9E,IAAI5P,QAAQ,CAAC8G,OAAO,CAACgL,UAAU,CAAC,EAAEhL,OAAO,CAACgL,UAAU,GAAG,CAAChL,OAAO,CAACgL,UAAU,CAAC;EAC3E,IAAIhL,OAAO,CAACqM,aAAa,EAAE/R,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAClD0F,OAAO,CAACqM,aAAa,GAAGrM,OAAO,CAACqM,aAAa,CAACxQ,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClE;EACA,IAAI,OAAOmE,OAAO,CAACqY,aAAa,KAAK,SAAS,EAAErY,OAAO,CAAC8X,SAAS,GAAG9X,OAAO,CAACqY,aAAa;EACzF,OAAOrY,OAAO;AAChB,CAAC;AAED,MAAMsY,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,MAAMC,mBAAmB,GAAGC,IAAI,IAAI;EAClC,MAAMC,IAAI,GAAGzd,MAAM,CAAC0d,mBAAmB,CAAC1d,MAAM,CAAC2d,cAAc,CAACH,IAAI,CAAC,CAAC;EACpEC,IAAI,CAACxe,OAAO,CAACib,GAAG,IAAI;IAClB,IAAI,OAAOsD,IAAI,CAACtD,GAAG,CAAC,KAAK,UAAU,EAAE;MACnCsD,IAAI,CAACtD,GAAG,CAAC,GAAGsD,IAAI,CAACtD,GAAG,CAAC,CAACoC,IAAI,CAACkB,IAAI,CAAC;IAClC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,MAAMI,IAAI,SAAS/X,YAAY,CAAC;EAC9B9D,WAAWA,CAACiD,OAAO,GAAG,CAAC,CAAC,EAAEsW,QAAQ,EAAE;IAClC,KAAK,CAAC,CAAC;IACP,IAAI,CAACtW,OAAO,GAAGoY,gBAAgB,CAACpY,OAAO,CAAC;IACxC,IAAI,CAACyE,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACtE,MAAM,GAAGS,UAAU;IACxB,IAAI,CAACiY,OAAO,GAAG;MACbC,QAAQ,EAAE;IACZ,CAAC;IACDP,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAIjC,QAAQ,IAAI,CAAC,IAAI,CAACyC,aAAa,IAAI,CAAC/Y,OAAO,CAACgZ,OAAO,EAAE;MACvD,IAAI,CAAC,IAAI,CAAChZ,OAAO,CAAC8X,SAAS,EAAE;QAC3B,IAAI,CAAC7X,IAAI,CAACD,OAAO,EAAEsW,QAAQ,CAAC;QAC5B,OAAO,IAAI;MACb;MACAe,UAAU,CAAC,MAAM;QACf,IAAI,CAACpX,IAAI,CAACD,OAAO,EAAEsW,QAAQ,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EACArW,IAAIA,CAACD,OAAO,GAAG,CAAC,CAAC,EAAEsW,QAAQ,EAAE;IAC3B,IAAI,CAAC2C,cAAc,GAAG,IAAI;IAC1B,IAAI,OAAOjZ,OAAO,KAAK,UAAU,EAAE;MACjCsW,QAAQ,GAAGtW,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,IAAIA,OAAO,CAAC8B,SAAS,IAAI,IAAI,IAAI9B,OAAO,CAAC6B,EAAE,EAAE;MAC3C,IAAI3I,QAAQ,CAAC8G,OAAO,CAAC6B,EAAE,CAAC,EAAE;QACxB7B,OAAO,CAAC8B,SAAS,GAAG9B,OAAO,CAAC6B,EAAE;MAChC,CAAC,MAAM,IAAI7B,OAAO,CAAC6B,EAAE,CAACvH,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;QAChD0F,OAAO,CAAC8B,SAAS,GAAG9B,OAAO,CAAC6B,EAAE,CAAC,CAAC,CAAC;MACnC;IACF;IACA,MAAMqX,OAAO,GAAG3b,GAAG,CAAC,CAAC;IACrB,IAAI,CAACyC,OAAO,GAAG;MACb,GAAGkZ,OAAO;MACV,GAAG,IAAI,CAAClZ,OAAO;MACf,GAAGoY,gBAAgB,CAACpY,OAAO;IAC7B,CAAC;IACD,IAAI,CAACA,OAAO,CAAC8E,aAAa,GAAG;MAC3B,GAAGoU,OAAO,CAACpU,aAAa;MACxB,GAAG,IAAI,CAAC9E,OAAO,CAAC8E;IAClB,CAAC;IACD,IAAI9E,OAAO,CAAC/B,YAAY,KAAK1C,SAAS,EAAE;MACtC,IAAI,CAACyE,OAAO,CAACqF,uBAAuB,GAAGrF,OAAO,CAAC/B,YAAY;IAC7D;IACA,IAAI+B,OAAO,CAAChC,WAAW,KAAKzC,SAAS,EAAE;MACrC,IAAI,CAACyE,OAAO,CAACsF,sBAAsB,GAAGtF,OAAO,CAAChC,WAAW;IAC3D;IACA,MAAMmb,mBAAmB,GAAGC,aAAa,IAAI;MAC3C,IAAI,CAACA,aAAa,EAAE,OAAO,IAAI;MAC/B,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE,OAAO,IAAIA,aAAa,CAAC,CAAC;MACnE,OAAOA,aAAa;IACtB,CAAC;IACD,IAAI,CAAC,IAAI,CAACpZ,OAAO,CAACgZ,OAAO,EAAE;MACzB,IAAI,IAAI,CAACH,OAAO,CAAC1Y,MAAM,EAAE;QACvBS,UAAU,CAACX,IAAI,CAACkZ,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAAC1Y,MAAM,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;MACzE,CAAC,MAAM;QACLY,UAAU,CAACX,IAAI,CAAC,IAAI,EAAE,IAAI,CAACD,OAAO,CAAC;MACrC;MACA,IAAIsU,SAAS;MACb,IAAI,IAAI,CAACuE,OAAO,CAACvE,SAAS,EAAE;QAC1BA,SAAS,GAAG,IAAI,CAACuE,OAAO,CAACvE,SAAS;MACpC,CAAC,MAAM;QACLA,SAAS,GAAGL,SAAS;MACvB;MACA,MAAMoF,EAAE,GAAG,IAAIjN,YAAY,CAAC,IAAI,CAACpM,OAAO,CAAC;MACzC,IAAI,CAAC4V,KAAK,GAAG,IAAIhU,aAAa,CAAC,IAAI,CAAC5B,OAAO,CAAC2C,SAAS,EAAE,IAAI,CAAC3C,OAAO,CAAC;MACpE,MAAMjG,CAAC,GAAG,IAAI,CAAC0K,QAAQ;MACvB1K,CAAC,CAACoG,MAAM,GAAGS,UAAU;MACrB7G,CAAC,CAACiS,aAAa,GAAG,IAAI,CAAC4J,KAAK;MAC5B7b,CAAC,CAAC6O,aAAa,GAAGyQ,EAAE;MACpBtf,CAAC,CAACqN,cAAc,GAAG,IAAImH,cAAc,CAAC8K,EAAE,EAAE;QACxC5J,OAAO,EAAE,IAAI,CAACzP,OAAO,CAAC0H,eAAe;QACrCsQ,oBAAoB,EAAE,IAAI,CAAChY,OAAO,CAACgY;MACrC,CAAC,CAAC;MACF,MAAMsB,yBAAyB,GAAG,IAAI,CAACtZ,OAAO,CAAC8E,aAAa,CAACgL,MAAM,IAAI,IAAI,CAAC9P,OAAO,CAAC8E,aAAa,CAACgL,MAAM,KAAKoJ,OAAO,CAACpU,aAAa,CAACgL,MAAM;MACzI,IAAIwJ,yBAAyB,EAAE;QAC7B,IAAI,CAACnZ,MAAM,CAACT,IAAI,CAAC,4IAA4I,CAAC;MAChK;MACA,IAAI4U,SAAS,KAAK,CAAC,IAAI,CAACtU,OAAO,CAAC8E,aAAa,CAACgL,MAAM,IAAI,IAAI,CAAC9P,OAAO,CAAC8E,aAAa,CAACgL,MAAM,KAAKoJ,OAAO,CAACpU,aAAa,CAACgL,MAAM,CAAC,EAAE;QAC3H/V,CAAC,CAACua,SAAS,GAAG6E,mBAAmB,CAAC7E,SAAS,CAAC;QAC5C,IAAIva,CAAC,CAACua,SAAS,CAACrU,IAAI,EAAElG,CAAC,CAACua,SAAS,CAACrU,IAAI,CAAClG,CAAC,EAAE,IAAI,CAACiG,OAAO,CAAC;QACvD,IAAI,CAACA,OAAO,CAAC8E,aAAa,CAACgL,MAAM,GAAG/V,CAAC,CAACua,SAAS,CAACxE,MAAM,CAACwH,IAAI,CAACvd,CAAC,CAACua,SAAS,CAAC;MAC1E;MACAva,CAAC,CAACyL,YAAY,GAAG,IAAIqK,YAAY,CAAC,IAAI,CAAC7P,OAAO,CAAC;MAC/CjG,CAAC,CAACoR,KAAK,GAAG;QACRC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACkM,IAAI,CAAC,IAAI;MACvD,CAAC;MACDvd,CAAC,CAACuP,gBAAgB,GAAG,IAAIoM,SAAS,CAACyD,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAAClD,OAAO,CAAC,EAAE5b,CAAC,CAACiS,aAAa,EAAEjS,CAAC,EAAE,IAAI,CAACiG,OAAO,CAAC;MAC/GjG,CAAC,CAACuP,gBAAgB,CAACvI,EAAE,CAAC,GAAG,EAAE,CAACG,KAAK,EAAE,GAAG1B,IAAI,KAAK;QAC7C,IAAI,CAAC6B,IAAI,CAACH,KAAK,EAAE,GAAG1B,IAAI,CAAC;MAC3B,CAAC,CAAC;MACF,IAAI,IAAI,CAACqZ,OAAO,CAACU,gBAAgB,EAAE;QACjCxf,CAAC,CAACwf,gBAAgB,GAAGJ,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAACU,gBAAgB,CAAC;QACvE,IAAIxf,CAAC,CAACwf,gBAAgB,CAACtZ,IAAI,EAAElG,CAAC,CAACwf,gBAAgB,CAACtZ,IAAI,CAAClG,CAAC,EAAE,IAAI,CAACiG,OAAO,CAACwZ,SAAS,EAAE,IAAI,CAACxZ,OAAO,CAAC;MAC/F;MACA,IAAI,IAAI,CAAC6Y,OAAO,CAAC/R,UAAU,EAAE;QAC3B/M,CAAC,CAAC+M,UAAU,GAAGqS,mBAAmB,CAAC,IAAI,CAACN,OAAO,CAAC/R,UAAU,CAAC;QAC3D,IAAI/M,CAAC,CAAC+M,UAAU,CAAC7G,IAAI,EAAElG,CAAC,CAAC+M,UAAU,CAAC7G,IAAI,CAAC,IAAI,CAAC;MAChD;MACA,IAAI,CAACkE,UAAU,GAAG,IAAIK,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACzE,OAAO,CAAC;MAC7D,IAAI,CAACmE,UAAU,CAACpD,EAAE,CAAC,GAAG,EAAE,CAACG,KAAK,EAAE,GAAG1B,IAAI,KAAK;QAC1C,IAAI,CAAC6B,IAAI,CAACH,KAAK,EAAE,GAAG1B,IAAI,CAAC;MAC3B,CAAC,CAAC;MACF,IAAI,CAACqZ,OAAO,CAACC,QAAQ,CAAC7e,OAAO,CAACC,CAAC,IAAI;QACjC,IAAIA,CAAC,CAAC+F,IAAI,EAAE/F,CAAC,CAAC+F,IAAI,CAAC,IAAI,CAAC;MAC1B,CAAC,CAAC;IACJ;IACA,IAAI,CAAC6P,MAAM,GAAG,IAAI,CAAC9P,OAAO,CAAC8E,aAAa,CAACgL,MAAM;IAC/C,IAAI,CAACwG,QAAQ,EAAEA,QAAQ,GAAGgC,IAAI;IAC9B,IAAI,IAAI,CAACtY,OAAO,CAAC8I,WAAW,IAAI,CAAC,IAAI,CAACrE,QAAQ,CAAC8U,gBAAgB,IAAI,CAAC,IAAI,CAACvZ,OAAO,CAACqC,GAAG,EAAE;MACpF,MAAM6I,KAAK,GAAG,IAAI,CAACzG,QAAQ,CAACmE,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAAC7I,OAAO,CAAC8I,WAAW,CAAC;MACpF,IAAIoC,KAAK,CAACnQ,MAAM,GAAG,CAAC,IAAImQ,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,IAAI,CAAClL,OAAO,CAACqC,GAAG,GAAG6I,KAAK,CAAC,CAAC,CAAC;IACzE;IACA,IAAI,CAAC,IAAI,CAACzG,QAAQ,CAAC8U,gBAAgB,IAAI,CAAC,IAAI,CAACvZ,OAAO,CAACqC,GAAG,EAAE;MACxD,IAAI,CAAClC,MAAM,CAACT,IAAI,CAAC,yDAAyD,CAAC;IAC7E;IACA,MAAM+Z,QAAQ,GAAG,CAAC,aAAa,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;IAC/FA,QAAQ,CAACxf,OAAO,CAACgd,MAAM,IAAI;MACzB,IAAI,CAACA,MAAM,CAAC,GAAG,CAAC,GAAGzX,IAAI,KAAK,IAAI,CAACoW,KAAK,CAACqB,MAAM,CAAC,CAAC,GAAGzX,IAAI,CAAC;IACzD,CAAC,CAAC;IACF,MAAMka,eAAe,GAAG,CAAC,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;IACpGA,eAAe,CAACzf,OAAO,CAACgd,MAAM,IAAI;MAChC,IAAI,CAACA,MAAM,CAAC,GAAG,CAAC,GAAGzX,IAAI,KAAK;QAC1B,IAAI,CAACoW,KAAK,CAACqB,MAAM,CAAC,CAAC,GAAGzX,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb,CAAC;IACH,CAAC,CAAC;IACF,MAAMma,QAAQ,GAAGvgB,KAAK,CAAC,CAAC;IACxB,MAAM2T,IAAI,GAAGA,CAAA,KAAM;MACjB,MAAM6M,MAAM,GAAGA,CAAC3K,GAAG,EAAEjV,CAAC,KAAK;QACzB,IAAI,CAACif,cAAc,GAAG,KAAK;QAC3B,IAAI,IAAI,CAACF,aAAa,IAAI,CAAC,IAAI,CAACc,oBAAoB,EAAE,IAAI,CAAC1Z,MAAM,CAACT,IAAI,CAAC,uEAAuE,CAAC;QAC/I,IAAI,CAACqZ,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,IAAI,CAAC/Y,OAAO,CAACgZ,OAAO,EAAE,IAAI,CAAC7Y,MAAM,CAACZ,GAAG,CAAC,aAAa,EAAE,IAAI,CAACS,OAAO,CAAC;QACvE,IAAI,CAACqB,IAAI,CAAC,aAAa,EAAE,IAAI,CAACrB,OAAO,CAAC;QACtC2Z,QAAQ,CAAClgB,OAAO,CAACO,CAAC,CAAC;QACnBsc,QAAQ,CAACrH,GAAG,EAAEjV,CAAC,CAAC;MAClB,CAAC;MACD,IAAI,IAAI,CAACqc,SAAS,IAAI,CAAC,IAAI,CAAC0C,aAAa,EAAE,OAAOa,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC5f,CAAC,CAACsd,IAAI,CAAC,IAAI,CAAC,CAAC;MACjF,IAAI,CAAC5S,cAAc,CAAC,IAAI,CAAC1E,OAAO,CAACqC,GAAG,EAAEuX,MAAM,CAAC;IAC/C,CAAC;IACD,IAAI,IAAI,CAAC5Z,OAAO,CAAC2C,SAAS,IAAI,CAAC,IAAI,CAAC3C,OAAO,CAAC8X,SAAS,EAAE;MACrD/K,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLsK,UAAU,CAACtK,IAAI,EAAE,CAAC,CAAC;IACrB;IACA,OAAO4M,QAAQ;EACjB;EACAG,aAAaA,CAACnV,QAAQ,EAAE2R,QAAQ,GAAGgC,IAAI,EAAE;IACvC,IAAIyB,YAAY,GAAGzD,QAAQ;IAC3B,MAAMjQ,OAAO,GAAGnN,QAAQ,CAACyL,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC7D,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAEoV,YAAY,GAAGpV,QAAQ;IAC3D,IAAI,CAAC,IAAI,CAAC3E,OAAO,CAAC2C,SAAS,IAAI,IAAI,CAAC3C,OAAO,CAACiY,uBAAuB,EAAE;MACnE,IAAI5R,OAAO,EAAEH,WAAW,CAAC,CAAC,KAAK,QAAQ,KAAK,CAAC,IAAI,CAAClG,OAAO,CAAC+X,OAAO,IAAI,IAAI,CAAC/X,OAAO,CAAC+X,OAAO,CAAChd,MAAM,KAAK,CAAC,CAAC,EAAE,OAAOgf,YAAY,CAAC,CAAC;MAC9H,MAAMxD,MAAM,GAAG,EAAE;MACjB,MAAMyD,MAAM,GAAG3X,GAAG,IAAI;QACpB,IAAI,CAACA,GAAG,EAAE;QACV,IAAIA,GAAG,KAAK,QAAQ,EAAE;QACtB,MAAMqG,IAAI,GAAG,IAAI,CAACjE,QAAQ,CAACmE,aAAa,CAACI,kBAAkB,CAAC3G,GAAG,CAAC;QAChEqG,IAAI,CAACzO,OAAO,CAACiP,CAAC,IAAI;UAChB,IAAIA,CAAC,KAAK,QAAQ,EAAE;UACpB,IAAIqN,MAAM,CAACjc,OAAO,CAAC4O,CAAC,CAAC,GAAG,CAAC,EAAEqN,MAAM,CAACza,IAAI,CAACoN,CAAC,CAAC;QAC3C,CAAC,CAAC;MACJ,CAAC;MACD,IAAI,CAAC7C,OAAO,EAAE;QACZ,MAAMiH,SAAS,GAAG,IAAI,CAAC7I,QAAQ,CAACmE,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAAC7I,OAAO,CAAC8I,WAAW,CAAC;QACxFwE,SAAS,CAACrT,OAAO,CAACiP,CAAC,IAAI8Q,MAAM,CAAC9Q,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACL8Q,MAAM,CAAC3T,OAAO,CAAC;MACjB;MACA,IAAI,CAACrG,OAAO,CAAC+X,OAAO,EAAE9d,OAAO,GAAGiP,CAAC,IAAI8Q,MAAM,CAAC9Q,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACzE,QAAQ,CAAC6E,gBAAgB,CAACyD,IAAI,CAACwJ,MAAM,EAAE,IAAI,CAACvW,OAAO,CAAC6B,EAAE,EAAErG,CAAC,IAAI;QAChE,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,CAACye,gBAAgB,IAAI,IAAI,CAACtV,QAAQ,EAAE,IAAI,CAACuV,mBAAmB,CAAC,IAAI,CAACvV,QAAQ,CAAC;QAC1FoV,YAAY,CAACve,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLue,YAAY,CAAC,IAAI,CAAC;IACpB;EACF;EACAI,eAAeA,CAACzR,IAAI,EAAE7G,EAAE,EAAEyU,QAAQ,EAAE;IAClC,MAAMqD,QAAQ,GAAGvgB,KAAK,CAAC,CAAC;IACxB,IAAI,OAAOsP,IAAI,KAAK,UAAU,EAAE;MAC9B4N,QAAQ,GAAG5N,IAAI;MACfA,IAAI,GAAGnN,SAAS;IAClB;IACA,IAAI,OAAOsG,EAAE,KAAK,UAAU,EAAE;MAC5ByU,QAAQ,GAAGzU,EAAE;MACbA,EAAE,GAAGtG,SAAS;IAChB;IACA,IAAI,CAACmN,IAAI,EAAEA,IAAI,GAAG,IAAI,CAAC2N,SAAS;IAChC,IAAI,CAACxU,EAAE,EAAEA,EAAE,GAAG,IAAI,CAAC7B,OAAO,CAAC6B,EAAE;IAC7B,IAAI,CAACyU,QAAQ,EAAEA,QAAQ,GAAGgC,IAAI;IAC9B,IAAI,CAAC7T,QAAQ,CAAC6E,gBAAgB,CAACqN,MAAM,CAACjO,IAAI,EAAE7G,EAAE,EAAEoN,GAAG,IAAI;MACrD0K,QAAQ,CAAClgB,OAAO,CAAC,CAAC;MAClB6c,QAAQ,CAACrH,GAAG,CAAC;IACf,CAAC,CAAC;IACF,OAAO0K,QAAQ;EACjB;EACAS,GAAGA,CAACpW,MAAM,EAAE;IACV,IAAI,CAACA,MAAM,EAAE,MAAM,IAAIqW,KAAK,CAAC,+FAA+F,CAAC;IAC7H,IAAI,CAACrW,MAAM,CAAC1E,IAAI,EAAE,MAAM,IAAI+a,KAAK,CAAC,0FAA0F,CAAC;IAC7H,IAAIrW,MAAM,CAAC1E,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACuZ,OAAO,CAAClD,OAAO,GAAG3R,MAAM;IAC/B;IACA,IAAIA,MAAM,CAAC1E,IAAI,KAAK,QAAQ,IAAI0E,MAAM,CAACzE,GAAG,IAAIyE,MAAM,CAACtE,IAAI,IAAIsE,MAAM,CAACrE,KAAK,EAAE;MACzE,IAAI,CAACkZ,OAAO,CAAC1Y,MAAM,GAAG6D,MAAM;IAC9B;IACA,IAAIA,MAAM,CAAC1E,IAAI,KAAK,kBAAkB,EAAE;MACtC,IAAI,CAACuZ,OAAO,CAACU,gBAAgB,GAAGvV,MAAM;IACxC;IACA,IAAIA,MAAM,CAAC1E,IAAI,KAAK,YAAY,EAAE;MAChC,IAAI,CAACuZ,OAAO,CAAC/R,UAAU,GAAG9C,MAAM;IAClC;IACA,IAAIA,MAAM,CAAC1E,IAAI,KAAK,eAAe,EAAE;MACnCuE,aAAa,CAACE,gBAAgB,CAACC,MAAM,CAAC;IACxC;IACA,IAAIA,MAAM,CAAC1E,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAI,CAACuZ,OAAO,CAACvE,SAAS,GAAGtQ,MAAM;IACjC;IACA,IAAIA,MAAM,CAAC1E,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI,CAACuZ,OAAO,CAACC,QAAQ,CAAChd,IAAI,CAACkI,MAAM,CAAC;IACpC;IACA,OAAO,IAAI;EACb;EACAkW,mBAAmBA,CAAChR,CAAC,EAAE;IACrB,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,CAACmN,SAAS,EAAE;IAC3B,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC/b,OAAO,CAAC4O,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;IACvC,KAAK,IAAIoR,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACjE,SAAS,CAACtb,MAAM,EAAEuf,EAAE,EAAE,EAAE;MACjD,MAAMC,SAAS,GAAG,IAAI,CAAClE,SAAS,CAACiE,EAAE,CAAC;MACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAChgB,OAAO,CAACigB,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/C,IAAI,IAAI,CAAC3E,KAAK,CAACrS,2BAA2B,CAACgX,SAAS,CAAC,EAAE;QACrD,IAAI,CAACN,gBAAgB,GAAGM,SAAS;QACjC;MACF;IACF;IACA,IAAI,CAAC,IAAI,CAACN,gBAAgB,IAAI,IAAI,CAAC5D,SAAS,CAAC/b,OAAO,CAAC4O,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC0M,KAAK,CAACrS,2BAA2B,CAAC2F,CAAC,CAAC,EAAE;MACxG,IAAI,CAAC+Q,gBAAgB,GAAG/Q,CAAC;MACzB,IAAI,CAACmN,SAAS,CAACmE,OAAO,CAACtR,CAAC,CAAC;IAC3B;EACF;EACAxE,cAAcA,CAACrC,GAAG,EAAEiU,QAAQ,EAAE;IAC5B,IAAI,CAACmE,oBAAoB,GAAGpY,GAAG;IAC/B,MAAMsX,QAAQ,GAAGvgB,KAAK,CAAC,CAAC;IACxB,IAAI,CAACiI,IAAI,CAAC,kBAAkB,EAAEgB,GAAG,CAAC;IAClC,MAAMqY,WAAW,GAAGxR,CAAC,IAAI;MACvB,IAAI,CAACvE,QAAQ,GAAGuE,CAAC;MACjB,IAAI,CAACmN,SAAS,GAAG,IAAI,CAAC5R,QAAQ,CAACmE,aAAa,CAACI,kBAAkB,CAACE,CAAC,CAAC;MAClE,IAAI,CAAC+Q,gBAAgB,GAAG1e,SAAS;MACjC,IAAI,CAAC2e,mBAAmB,CAAChR,CAAC,CAAC;IAC7B,CAAC;IACD,MAAM4N,IAAI,GAAGA,CAAC7H,GAAG,EAAE/F,CAAC,KAAK;MACvB,IAAIA,CAAC,EAAE;QACL,IAAI,IAAI,CAACuR,oBAAoB,KAAKpY,GAAG,EAAE;UACrCqY,WAAW,CAACxR,CAAC,CAAC;UACd,IAAI,CAAC/E,UAAU,CAACO,cAAc,CAACwE,CAAC,CAAC;UACjC,IAAI,CAACuR,oBAAoB,GAAGlf,SAAS;UACrC,IAAI,CAAC8F,IAAI,CAAC,iBAAiB,EAAE6H,CAAC,CAAC;UAC/B,IAAI,CAAC/I,MAAM,CAACZ,GAAG,CAAC,iBAAiB,EAAE2J,CAAC,CAAC;QACvC;MACF,CAAC,MAAM;QACL,IAAI,CAACuR,oBAAoB,GAAGlf,SAAS;MACvC;MACAoe,QAAQ,CAAClgB,OAAO,CAAC,CAAC,GAAG+F,IAAI,KAAK,IAAI,CAACxF,CAAC,CAAC,GAAGwF,IAAI,CAAC,CAAC;MAC9C,IAAI8W,QAAQ,EAAEA,QAAQ,CAACrH,GAAG,EAAE,CAAC,GAAGzP,IAAI,KAAK,IAAI,CAACxF,CAAC,CAAC,GAAGwF,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAMmb,MAAM,GAAGjS,IAAI,IAAI;MACrB,IAAI,CAACrG,GAAG,IAAI,CAACqG,IAAI,IAAI,IAAI,CAACjE,QAAQ,CAAC8U,gBAAgB,EAAE7Q,IAAI,GAAG,EAAE;MAC9D,MAAMkS,EAAE,GAAG1hB,QAAQ,CAACwP,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC;MAClD,MAAMQ,CAAC,GAAG,IAAI,CAAC0M,KAAK,CAACrS,2BAA2B,CAACqX,EAAE,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACnW,QAAQ,CAACmE,aAAa,CAACqE,qBAAqB,CAAC/T,QAAQ,CAACwP,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAGA,IAAI,CAAC;MAC7I,IAAIQ,CAAC,EAAE;QACL,IAAI,CAAC,IAAI,CAACvE,QAAQ,EAAE;UAClB+V,WAAW,CAACxR,CAAC,CAAC;QAChB;QACA,IAAI,CAAC,IAAI,CAAC/E,UAAU,CAACQ,QAAQ,EAAE,IAAI,CAACR,UAAU,CAACO,cAAc,CAACwE,CAAC,CAAC;QAChE,IAAI,CAACzE,QAAQ,CAAC8U,gBAAgB,EAAEsB,iBAAiB,GAAG3R,CAAC,CAAC;MACxD;MACA,IAAI,CAAC4Q,aAAa,CAAC5Q,CAAC,EAAE+F,GAAG,IAAI;QAC3B6H,IAAI,CAAC7H,GAAG,EAAE/F,CAAC,CAAC;MACd,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC7G,GAAG,IAAI,IAAI,CAACoC,QAAQ,CAAC8U,gBAAgB,IAAI,CAAC,IAAI,CAAC9U,QAAQ,CAAC8U,gBAAgB,CAACuB,KAAK,EAAE;MACnFH,MAAM,CAAC,IAAI,CAAClW,QAAQ,CAAC8U,gBAAgB,CAACwB,MAAM,CAAC,CAAC,CAAC;IACjD,CAAC,MAAM,IAAI,CAAC1Y,GAAG,IAAI,IAAI,CAACoC,QAAQ,CAAC8U,gBAAgB,IAAI,IAAI,CAAC9U,QAAQ,CAAC8U,gBAAgB,CAACuB,KAAK,EAAE;MACzF,IAAI,IAAI,CAACrW,QAAQ,CAAC8U,gBAAgB,CAACwB,MAAM,CAAChgB,MAAM,KAAK,CAAC,EAAE;QACtD,IAAI,CAAC0J,QAAQ,CAAC8U,gBAAgB,CAACwB,MAAM,CAAC,CAAC,CAACxD,IAAI,CAACoD,MAAM,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAAClW,QAAQ,CAAC8U,gBAAgB,CAACwB,MAAM,CAACJ,MAAM,CAAC;MAC/C;IACF,CAAC,MAAM;MACLA,MAAM,CAACtY,GAAG,CAAC;IACb;IACA,OAAOsX,QAAQ;EACjB;EACAqB,SAASA,CAAC3Y,GAAG,EAAER,EAAE,EAAEoZ,SAAS,EAAE;IAC5B,MAAMC,MAAM,GAAGA,CAAC7gB,GAAG,EAAEgZ,IAAI,EAAE,GAAGC,IAAI,KAAK;MACrC,IAAIzO,CAAC;MACL,IAAI,OAAOwO,IAAI,KAAK,QAAQ,EAAE;QAC5BxO,CAAC,GAAG,IAAI,CAAC7E,OAAO,CAAC6F,gCAAgC,CAAC,CAACxL,GAAG,EAAEgZ,IAAI,CAAC,CAACxX,MAAM,CAACyX,IAAI,CAAC,CAAC;MAC7E,CAAC,MAAM;QACLzO,CAAC,GAAG;UACF,GAAGwO;QACL,CAAC;MACH;MACAxO,CAAC,CAACxC,GAAG,GAAGwC,CAAC,CAACxC,GAAG,IAAI6Y,MAAM,CAAC7Y,GAAG;MAC3BwC,CAAC,CAAC6D,IAAI,GAAG7D,CAAC,CAAC6D,IAAI,IAAIwS,MAAM,CAACxS,IAAI;MAC9B7D,CAAC,CAAChD,EAAE,GAAGgD,CAAC,CAAChD,EAAE,IAAIqZ,MAAM,CAACrZ,EAAE;MACxB,IAAIgD,CAAC,CAACoW,SAAS,KAAK,EAAE,EAAEpW,CAAC,CAACoW,SAAS,GAAGpW,CAAC,CAACoW,SAAS,IAAIA,SAAS,IAAIC,MAAM,CAACD,SAAS;MAClF,MAAMhd,YAAY,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,YAAY,IAAI,GAAG;MACrD,IAAIkd,SAAS;MACb,IAAItW,CAAC,CAACoW,SAAS,IAAI1Z,KAAK,CAACe,OAAO,CAACjI,GAAG,CAAC,EAAE;QACrC8gB,SAAS,GAAG9gB,GAAG,CAACiE,GAAG,CAAClD,CAAC,IAAI,GAAGyJ,CAAC,CAACoW,SAAS,GAAGhd,YAAY,GAAG7C,CAAC,EAAE,CAAC;MAC/D,CAAC,MAAM;QACL+f,SAAS,GAAGtW,CAAC,CAACoW,SAAS,GAAG,GAAGpW,CAAC,CAACoW,SAAS,GAAGhd,YAAY,GAAG5D,GAAG,EAAE,GAAGA,GAAG;MACvE;MACA,OAAO,IAAI,CAACL,CAAC,CAACmhB,SAAS,EAAEtW,CAAC,CAAC;IAC7B,CAAC;IACD,IAAI3L,QAAQ,CAACmJ,GAAG,CAAC,EAAE;MACjB6Y,MAAM,CAAC7Y,GAAG,GAAGA,GAAG;IAClB,CAAC,MAAM;MACL6Y,MAAM,CAACxS,IAAI,GAAGrG,GAAG;IACnB;IACA6Y,MAAM,CAACrZ,EAAE,GAAGA,EAAE;IACdqZ,MAAM,CAACD,SAAS,GAAGA,SAAS;IAC5B,OAAOC,MAAM;EACf;EACAlhB,CAACA,CAAC,GAAGwF,IAAI,EAAE;IACT,OAAO,IAAI,CAAC2E,UAAU,EAAEwB,SAAS,CAAC,GAAGnG,IAAI,CAAC;EAC5C;EACAoF,MAAMA,CAAC,GAAGpF,IAAI,EAAE;IACd,OAAO,IAAI,CAAC2E,UAAU,EAAES,MAAM,CAAC,GAAGpF,IAAI,CAAC;EACzC;EACA4b,mBAAmBA,CAACvZ,EAAE,EAAE;IACtB,IAAI,CAAC7B,OAAO,CAAC8B,SAAS,GAAGD,EAAE;EAC7B;EACAuJ,kBAAkBA,CAACvJ,EAAE,EAAE7B,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,IAAI,CAAC,IAAI,CAAC+Y,aAAa,EAAE;MACvB,IAAI,CAAC5Y,MAAM,CAACT,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC2W,SAAS,CAAC;MACnF,OAAO,KAAK;IACd;IACA,IAAI,CAAC,IAAI,CAACA,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACtb,MAAM,EAAE;MAC7C,IAAI,CAACoF,MAAM,CAACT,IAAI,CAAC,4DAA4D,EAAE,IAAI,CAAC2W,SAAS,CAAC;MAC9F,OAAO,KAAK;IACd;IACA,MAAMhU,GAAG,GAAGrC,OAAO,CAACqC,GAAG,IAAI,IAAI,CAAC4X,gBAAgB,IAAI,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC;IACrE,MAAMvN,WAAW,GAAG,IAAI,CAAC9I,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC8I,WAAW,GAAG,KAAK;IACnE,MAAMuS,OAAO,GAAG,IAAI,CAAChF,SAAS,CAAC,IAAI,CAACA,SAAS,CAACtb,MAAM,GAAG,CAAC,CAAC;IACzD,IAAIsH,GAAG,CAAC6D,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAO,IAAI;IAC/C,MAAMoV,cAAc,GAAGA,CAACpS,CAAC,EAAE1F,CAAC,KAAK;MAC/B,MAAM+X,SAAS,GAAG,IAAI,CAAC9W,QAAQ,CAAC6E,gBAAgB,CAAC4M,KAAK,CAAC,GAAGhN,CAAC,IAAI1F,CAAC,EAAE,CAAC;MACnE,OAAO+X,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK,CAAC;IAC/D,CAAC;IACD,IAAIvb,OAAO,CAACwb,QAAQ,EAAE;MACpB,MAAMC,SAAS,GAAGzb,OAAO,CAACwb,QAAQ,CAAC,IAAI,EAAEF,cAAc,CAAC;MACxD,IAAIG,SAAS,KAAKlgB,SAAS,EAAE,OAAOkgB,SAAS;IAC/C;IACA,IAAI,IAAI,CAACrY,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE,OAAO,IAAI;IAChD,IAAI,CAAC,IAAI,CAAC4C,QAAQ,CAAC6E,gBAAgB,CAACqM,OAAO,IAAI,IAAI,CAAC3V,OAAO,CAAC2C,SAAS,IAAI,CAAC,IAAI,CAAC3C,OAAO,CAACiY,uBAAuB,EAAE,OAAO,IAAI;IAC3H,IAAIqD,cAAc,CAACjZ,GAAG,EAAER,EAAE,CAAC,KAAK,CAACiH,WAAW,IAAIwS,cAAc,CAACD,OAAO,EAAExZ,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI;IACzF,OAAO,KAAK;EACd;EACA6Z,cAAcA,CAAC7Z,EAAE,EAAEyU,QAAQ,EAAE;IAC3B,MAAMqD,QAAQ,GAAGvgB,KAAK,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC4G,OAAO,CAAC6B,EAAE,EAAE;MACpB,IAAIyU,QAAQ,EAAEA,QAAQ,CAAC,CAAC;MACxB,OAAO9c,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IACA,IAAIP,QAAQ,CAAC2I,EAAE,CAAC,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC;IAC3BA,EAAE,CAAC5H,OAAO,CAACuJ,CAAC,IAAI;MACd,IAAI,IAAI,CAACxD,OAAO,CAAC6B,EAAE,CAACvH,OAAO,CAACkJ,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACxD,OAAO,CAAC6B,EAAE,CAAC/F,IAAI,CAAC0H,CAAC,CAAC;IAC7D,CAAC,CAAC;IACF,IAAI,CAACsW,aAAa,CAAC7K,GAAG,IAAI;MACxB0K,QAAQ,CAAClgB,OAAO,CAAC,CAAC;MAClB,IAAI6c,QAAQ,EAAEA,QAAQ,CAACrH,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO0K,QAAQ;EACjB;EACAgC,aAAaA,CAACjT,IAAI,EAAE4N,QAAQ,EAAE;IAC5B,MAAMqD,QAAQ,GAAGvgB,KAAK,CAAC,CAAC;IACxB,IAAIF,QAAQ,CAACwP,IAAI,CAAC,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjC,MAAMkT,SAAS,GAAG,IAAI,CAAC5b,OAAO,CAAC+X,OAAO,IAAI,EAAE;IAC5C,MAAM8D,OAAO,GAAGnT,IAAI,CAACvK,MAAM,CAACkE,GAAG,IAAIuZ,SAAS,CAACthB,OAAO,CAAC+H,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAACoC,QAAQ,CAACmE,aAAa,CAACkE,eAAe,CAACzK,GAAG,CAAC,CAAC;IAClH,IAAI,CAACwZ,OAAO,CAAC9gB,MAAM,EAAE;MACnB,IAAIub,QAAQ,EAAEA,QAAQ,CAAC,CAAC;MACxB,OAAO9c,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IACA,IAAI,CAACuG,OAAO,CAAC+X,OAAO,GAAG6D,SAAS,CAAC/f,MAAM,CAACggB,OAAO,CAAC;IAChD,IAAI,CAAC/B,aAAa,CAAC7K,GAAG,IAAI;MACxB0K,QAAQ,CAAClgB,OAAO,CAAC,CAAC;MAClB,IAAI6c,QAAQ,EAAEA,QAAQ,CAACrH,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO0K,QAAQ;EACjB;EACAmC,GAAGA,CAACzZ,GAAG,EAAE;IACP,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAG,IAAI,CAAC4X,gBAAgB,KAAK,IAAI,CAAC5D,SAAS,EAAEtb,MAAM,GAAG,CAAC,GAAG,IAAI,CAACsb,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC1R,QAAQ,CAAC;IACzG,IAAI,CAACtC,GAAG,EAAE,OAAO,KAAK;IACtB,IAAI;MACF,MAAM6G,CAAC,GAAG,IAAIwD,IAAI,CAACqP,MAAM,CAAC1Z,GAAG,CAAC;MAC9B,IAAI6G,CAAC,IAAIA,CAAC,CAAC8S,WAAW,EAAE;QACtB,MAAMC,EAAE,GAAG/S,CAAC,CAAC8S,WAAW,CAAC,CAAC;QAC1B,IAAIC,EAAE,IAAIA,EAAE,CAACC,SAAS,EAAE,OAAOD,EAAE,CAACC,SAAS;MAC7C;IACF,CAAC,CAAC,OAAO1gB,CAAC,EAAE,CAAC;IACb,MAAM2gB,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IACxb,MAAMvT,aAAa,GAAG,IAAI,CAACnE,QAAQ,EAAEmE,aAAa,IAAI,IAAIwD,YAAY,CAAC7O,GAAG,CAAC,CAAC,CAAC;IAC7E,IAAI8E,GAAG,CAAC6D,WAAW,CAAC,CAAC,CAAC5L,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK;IACxD,OAAO6hB,OAAO,CAAC7hB,OAAO,CAACsO,aAAa,CAAC4D,uBAAuB,CAACnK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIA,GAAG,CAAC6D,WAAW,CAAC,CAAC,CAAC5L,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK;EACnI;EACA,OAAO8hB,cAAcA,CAACpc,OAAO,GAAG,CAAC,CAAC,EAAEsW,QAAQ,EAAE;IAC5C,OAAO,IAAIsC,IAAI,CAAC5Y,OAAO,EAAEsW,QAAQ,CAAC;EACpC;EACA+F,aAAaA,CAACrc,OAAO,GAAG,CAAC,CAAC,EAAEsW,QAAQ,GAAGgC,IAAI,EAAE;IAC3C,MAAMgE,iBAAiB,GAAGtc,OAAO,CAACsc,iBAAiB;IACnD,IAAIA,iBAAiB,EAAE,OAAOtc,OAAO,CAACsc,iBAAiB;IACvD,MAAMC,aAAa,GAAG;MACpB,GAAG,IAAI,CAACvc,OAAO;MACf,GAAGA,OAAO;MACV,GAAG;QACDgZ,OAAO,EAAE;MACX;IACF,CAAC;IACD,MAAMrY,KAAK,GAAG,IAAIiY,IAAI,CAAC2D,aAAa,CAAC;IACrC,IAAIvc,OAAO,CAACI,KAAK,KAAK7E,SAAS,IAAIyE,OAAO,CAACE,MAAM,KAAK3E,SAAS,EAAE;MAC/DoF,KAAK,CAACR,MAAM,GAAGQ,KAAK,CAACR,MAAM,CAACQ,KAAK,CAACX,OAAO,CAAC;IAC5C;IACA,MAAMwc,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;IACvDA,aAAa,CAACviB,OAAO,CAACC,CAAC,IAAI;MACzByG,KAAK,CAACzG,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;IACpB,CAAC,CAAC;IACFyG,KAAK,CAAC8D,QAAQ,GAAG;MACf,GAAG,IAAI,CAACA;IACV,CAAC;IACD9D,KAAK,CAAC8D,QAAQ,CAAC0G,KAAK,GAAG;MACrBC,kBAAkB,EAAEzK,KAAK,CAACyK,kBAAkB,CAACkM,IAAI,CAAC3W,KAAK;IACzD,CAAC;IACD,IAAI2b,iBAAiB,EAAE;MACrB,MAAMG,UAAU,GAAGzhB,MAAM,CAACyI,IAAI,CAAC,IAAI,CAACmS,KAAK,CAAC3Z,IAAI,CAAC,CAAC4W,MAAM,CAAC,CAAC6J,IAAI,EAAExT,CAAC,KAAK;QAClEwT,IAAI,CAACxT,CAAC,CAAC,GAAG;UACR,GAAG,IAAI,CAAC0M,KAAK,CAAC3Z,IAAI,CAACiN,CAAC;QACtB,CAAC;QACDwT,IAAI,CAACxT,CAAC,CAAC,GAAGlO,MAAM,CAACyI,IAAI,CAACiZ,IAAI,CAACxT,CAAC,CAAC,CAAC,CAAC2J,MAAM,CAAC,CAAC8J,GAAG,EAAEnZ,CAAC,KAAK;UAChDmZ,GAAG,CAACnZ,CAAC,CAAC,GAAG;YACP,GAAGkZ,IAAI,CAACxT,CAAC,CAAC,CAAC1F,CAAC;UACd,CAAC;UACD,OAAOmZ,GAAG;QACZ,CAAC,EAAED,IAAI,CAACxT,CAAC,CAAC,CAAC;QACX,OAAOwT,IAAI;MACb,CAAC,EAAE,CAAC,CAAC,CAAC;MACN/b,KAAK,CAACiV,KAAK,GAAG,IAAIhU,aAAa,CAAC6a,UAAU,EAAEF,aAAa,CAAC;MAC1D5b,KAAK,CAAC8D,QAAQ,CAACuH,aAAa,GAAGrL,KAAK,CAACiV,KAAK;IAC5C;IACAjV,KAAK,CAACwD,UAAU,GAAG,IAAIK,UAAU,CAAC7D,KAAK,CAAC8D,QAAQ,EAAE8X,aAAa,CAAC;IAChE5b,KAAK,CAACwD,UAAU,CAACpD,EAAE,CAAC,GAAG,EAAE,CAACG,KAAK,EAAE,GAAG1B,IAAI,KAAK;MAC3CmB,KAAK,CAACU,IAAI,CAACH,KAAK,EAAE,GAAG1B,IAAI,CAAC;IAC5B,CAAC,CAAC;IACFmB,KAAK,CAACV,IAAI,CAACsc,aAAa,EAAEjG,QAAQ,CAAC;IACnC3V,KAAK,CAACwD,UAAU,CAACnE,OAAO,GAAGuc,aAAa;IACxC5b,KAAK,CAACwD,UAAU,CAACmF,gBAAgB,CAAC7E,QAAQ,CAAC0G,KAAK,GAAG;MACjDC,kBAAkB,EAAEzK,KAAK,CAACyK,kBAAkB,CAACkM,IAAI,CAAC3W,KAAK;IACzD,CAAC;IACD,OAAOA,KAAK;EACd;EACAiD,MAAMA,CAAA,EAAG;IACP,OAAO;MACL5D,OAAO,EAAE,IAAI,CAACA,OAAO;MACrB4V,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBjR,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB0R,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB4D,gBAAgB,EAAE,IAAI,CAACA;IACzB,CAAC;EACH;AACF;AACA,MAAM2C,QAAQ,GAAGhE,IAAI,CAACwD,cAAc,CAAC,CAAC;AACtCQ,QAAQ,CAACR,cAAc,GAAGxD,IAAI,CAACwD,cAAc;AAE7C,MAAMA,cAAc,GAAGQ,QAAQ,CAACR,cAAc;AAC9C,MAAMN,GAAG,GAAGc,QAAQ,CAACd,GAAG;AACxB,MAAM7b,IAAI,GAAG2c,QAAQ,CAAC3c,IAAI;AAC1B,MAAM6Z,aAAa,GAAG8C,QAAQ,CAAC9C,aAAa;AAC5C,MAAMK,eAAe,GAAGyC,QAAQ,CAACzC,eAAe;AAChD,MAAMC,GAAG,GAAGwC,QAAQ,CAACxC,GAAG;AACxB,MAAM1V,cAAc,GAAGkY,QAAQ,CAAClY,cAAc;AAC9C,MAAMsW,SAAS,GAAG4B,QAAQ,CAAC5B,SAAS;AACpC,MAAMhhB,CAAC,GAAG4iB,QAAQ,CAAC5iB,CAAC;AACpB,MAAM4K,MAAM,GAAGgY,QAAQ,CAAChY,MAAM;AAC9B,MAAMwW,mBAAmB,GAAGwB,QAAQ,CAACxB,mBAAmB;AACxD,MAAMhQ,kBAAkB,GAAGwR,QAAQ,CAACxR,kBAAkB;AACtD,MAAMsQ,cAAc,GAAGkB,QAAQ,CAAClB,cAAc;AAC9C,MAAMC,aAAa,GAAGiB,QAAQ,CAACjB,aAAa;AAE5C,SAASjX,cAAc,EAAE0X,cAAc,EAAEQ,QAAQ,IAAIrP,OAAO,EAAEuO,GAAG,EAAElX,MAAM,EAAEoW,SAAS,EAAE5P,kBAAkB,EAAEnL,IAAI,EAAE0b,aAAa,EAAED,cAAc,EAAE5B,aAAa,EAAEK,eAAe,EAAEiB,mBAAmB,EAAEphB,CAAC,EAAEogB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}