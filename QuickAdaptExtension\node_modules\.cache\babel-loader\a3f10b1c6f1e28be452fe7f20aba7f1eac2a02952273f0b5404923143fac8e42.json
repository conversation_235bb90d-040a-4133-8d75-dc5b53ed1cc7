{"ast": null, "code": "import React,{useState,useEffect,useRef,forwardRef,useMemo}from\"react\";import{Box,Tooltip,Icon<PERSON>utton}from\"@mui/material\";import JoditEditor from\"jodit-react\";import useDrawerStore from\"../../../store/drawerStore\";import{copyicon,deleteicon}from\"../../../assets/icons/icons\";import{useTranslation}from'react-i18next';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const RTEsection=/*#__PURE__*/forwardRef((_ref,ref)=>{let{textBoxRef,isBanner,handleDeleteRTESection,index,guidePopUpRef,onClone,isCloneDisabled}=_ref;const{t:translate}=useTranslation();const{rtesContainer,updateRTEContainer,setIsUnSavedChanges,cloneRTEContainer,clearRteDetails,selectedTemplate,selectedTemplateTour,announcementGuideMetaData,toolTipGuideMetaData,handleAnnouncementRTEValue,handleTooltipRTEValue,createWithAI,currentStep,ensureAnnouncementRTEContainer}=useDrawerStore();// Individual state management for each RTE\nconst[editingRTEId,setEditingRTEId]=useState(null);const contentRef=useRef(\"\");// Map to store individual refs for each RTE\nconst editorRefs=useRef(new Map());const containerRefs=useRef(new Map());// Helper function to get or create editor ref for specific RTE\nconst getEditorRef=rteId=>{if(!editorRefs.current.has(rteId)){editorRefs.current.set(rteId,/*#__PURE__*/React.createRef());}return editorRefs.current.get(rteId);};// Helper function to get or create container ref for specific RTE\nconst getContainerRef=rteId=>{if(!containerRefs.current.has(rteId)){containerRefs.current.set(rteId,/*#__PURE__*/React.createRef());}return containerRefs.current.get(rteId);};// Handle clicks outside the editor - now works with individual RTEs\nuseEffect(()=>{const handleClickOutside=event=>{var _document$querySelect,_document$querySelect2,_document$querySelect3,_document$querySelect4;if(!editingRTEId)return;// No RTE is currently being edited\nconst isInsideJoditPopupContent=event.target.closest(\".jodit-popup__content\")!==null;const isInsideAltTextPopup=event.target.closest(\".jodit-ui-input\")!==null;const isInsidePopup=(_document$querySelect=document.querySelector(\".jodit-popup\"))===null||_document$querySelect===void 0?void 0:_document$querySelect.contains(event.target);const isInsideJoditPopup=(_document$querySelect2=document.querySelector(\".jodit-wysiwyg\"))===null||_document$querySelect2===void 0?void 0:_document$querySelect2.contains(event.target);const isInsideWorkplacePopup=isInsideJoditPopup||((_document$querySelect3=document.querySelector(\".jodit-dialog__panel\"))===null||_document$querySelect3===void 0?void 0:_document$querySelect3.contains(event.target));const isSelectionMarker=event.target.id.startsWith(\"jodit-selection_marker_\");const isLinkPopup=(_document$querySelect4=document.querySelector(\".jodit-ui-input__input\"))===null||_document$querySelect4===void 0?void 0:_document$querySelect4.contains(event.target);const isInsideToolbarButton=event.target.closest(\".jodit-toolbar-button__button\")!==null;const isInsertButton=event.target.closest(\"button[aria-pressed='false']\")!==null;// Get the container ref for the currently editing RTE\nconst currentContainerRef=getContainerRef(editingRTEId);// Check if the target is inside the currently editing RTE or related elements\nif(currentContainerRef!==null&&currentContainerRef!==void 0&&currentContainerRef.current&&!currentContainerRef.current.contains(event.target)&&// Click outside the current editor container\n!isInsidePopup&&// Click outside the popup\n!isInsideJoditPopup&&// Click outside the WYSIWYG editor\n!isInsideWorkplacePopup&&// Click outside the workplace popup\n!isSelectionMarker&&// Click outside selection markers\n!isLinkPopup&&// Click outside link input popup\n!isInsideToolbarButton&&// Click outside the toolbar button\n!isInsertButton&&!isInsideJoditPopupContent&&!isInsideAltTextPopup){setEditingRTEId(null);// Close the currently editing RTE\n}};document.addEventListener(\"mousedown\",handleClickOutside);return()=>document.removeEventListener(\"mousedown\",handleClickOutside);},[editingRTEId]);useEffect(()=>{if(editingRTEId){const editorRef=getEditorRef(editingRTEId);if(editorRef!==null&&editorRef!==void 0&&editorRef.current){setTimeout(()=>{//(editorRef.current as any).editor.focus();\n},50);}}},[editingRTEId]);const handleUpdate=(newContent,rteId,containerId)=>{contentRef.current=newContent;// Check if this is an AI-created guide\nconst isAIAnnouncement=createWithAI&&(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\");const isAITour=createWithAI&&selectedTemplate===\"Tour\";const isTourAnnouncement=isAITour&&selectedTemplateTour===\"Announcement\";const isTourBanner=isAITour&&selectedTemplateTour===\"Banner\";const isTourTooltip=isAITour&&(selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\");console.log(\"RTEsection handleUpdate:\",{createWithAI,selectedTemplate,selectedTemplateTour,isAIAnnouncement,isAITour,isTourBanner,containerId,newContent:newContent.substring(0,50)+\"...\"});if(isAIAnnouncement){const currentStepIndex=currentStep-1;if(isTourAnnouncement){var _toolTipGuideMetaData,_toolTipGuideMetaData2;// For Tour+Announcement, use toolTipGuideMetaData\nconst tooltipContainer=(_toolTipGuideMetaData=toolTipGuideMetaData[currentStepIndex])===null||_toolTipGuideMetaData===void 0?void 0:(_toolTipGuideMetaData2=_toolTipGuideMetaData.containers)===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.find(container=>container.id===containerId&&container.type===\"rte\");if(tooltipContainer){// Use the tooltip-specific handler for tour announcements\nhandleTooltipRTEValue(containerId,newContent);}}else{var _announcementGuideMet,_announcementGuideMet2;// For pure Announcements, use announcementGuideMetaData\nconst announcementContainer=(_announcementGuideMet=announcementGuideMetaData[currentStepIndex])===null||_announcementGuideMet===void 0?void 0:(_announcementGuideMet2=_announcementGuideMet.containers)===null||_announcementGuideMet2===void 0?void 0:_announcementGuideMet2.find(container=>container.id===containerId&&container.type===\"rte\");if(announcementContainer){// Use the announcement-specific handler\nhandleAnnouncementRTEValue(containerId,newContent);}}}else if(isAITour&&(isTourBanner||isTourTooltip)){var _toolTipGuideMetaData3,_toolTipGuideMetaData4;// For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\nconst currentStepIndex=currentStep-1;const tooltipContainer=(_toolTipGuideMetaData3=toolTipGuideMetaData[currentStepIndex])===null||_toolTipGuideMetaData3===void 0?void 0:(_toolTipGuideMetaData4=_toolTipGuideMetaData3.containers)===null||_toolTipGuideMetaData4===void 0?void 0:_toolTipGuideMetaData4.find(container=>container.id===containerId&&container.type===\"rte\");if(tooltipContainer){// Use the tooltip-specific handler for all tour step types\nhandleTooltipRTEValue(containerId,newContent);console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);}else{var _toolTipGuideMetaData5,_toolTipGuideMetaData6;console.warn(`No tooltip container found for ${selectedTemplateTour} step`,{currentStepIndex,containerId,availableContainers:(_toolTipGuideMetaData5=toolTipGuideMetaData[currentStepIndex])===null||_toolTipGuideMetaData5===void 0?void 0:(_toolTipGuideMetaData6=_toolTipGuideMetaData5.containers)===null||_toolTipGuideMetaData6===void 0?void 0:_toolTipGuideMetaData6.map(c=>({id:c.id,type:c.type}))});}}else{// For non-AI content or other cases, use the regular RTE container system\nupdateRTEContainer(containerId,rteId,newContent);console.log(\"Used updateRTEContainer for non-AI content\");}setIsUnSavedChanges(true);};const handleCloneContainer=containerId=>{// Check if cloning is disabled due to section limits\nif(isCloneDisabled){return;// Don't clone if limit is reached\n}// Call the clone function from the store\ncloneRTEContainer(containerId);// Call the onClone callback if provided\nif(onClone){onClone();}};const handleDeleteSection=(containerId,rteId)=>{// Check if this is an AI-created announcement\nconst isAIAnnouncement=createWithAI&&(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\");if(isAIAnnouncement){// For AI announcements, we need to remove from announcementGuideMetaData\n// This would require a new function in the store, for now just call the existing one\nclearRteDetails(containerId,rteId);}else{// For banners and non-AI content, use the regular clear function\nclearRteDetails(containerId,rteId);}// Call the handleDeleteRTESection callback to update section counts\nhandleDeleteRTESection(index);};const handlePaste=event=>{event.preventDefault();const clipboardData=event.clipboardData;const pastedText=clipboardData.getData(\"text/plain\");const pastedHtml=clipboardData.getData(\"text/html\");if(pastedHtml){const isRTEContent=pastedHtml.includes(\"<!--RTE-->\");if(isRTEContent){insertContent(pastedHtml);}else{insertContent(pastedHtml);}}else{insertContent(pastedText);}};const insertContent=content=>{if(editingRTEId){const editorRef=getEditorRef(editingRTEId);if(editorRef!==null&&editorRef!==void 0&&editorRef.current){const editor=editorRef.current.editor;editor.selection.insertHTML(content);}}};const[isRtlDirection,setIsRtlDirection]=useState(false);useEffect(()=>{const dir=document.body.getAttribute(\"dir\")||\"ltr\";setIsRtlDirection(dir.toLowerCase()===\"rtl\");},[]);const config=useMemo(()=>({readonly:false,// all options from https://xdsoft.net/jodit/docs/,\ndirection:isRtlDirection?'rtl':'ltr',// Jodit uses 'direction' not just 'rtl'\nlanguage:'en',// Optional: change language as well\ntoolbarSticky:false,toolbarAdaptive:false,buttons:['bold','italic','underline','strikethrough','ul','ol','brush','font','fontsize','link',{name:'more',iconURL:'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',list:['source','image','video','table','align','undo','redo','|','hr','eraser','copyformat','symbol','fullsize','print','superscript','subscript','|','outdent','indent','paragraph']}],autofocus:true,cursorAfterAutofocus:'end',events:{onPaste:handlePaste// Attach custom onPaste handler\n},controls:{font:{list:{\"Poppins, sans-serif\":\"Poppins\",\"Roboto, sans-serif\":\"Roboto\",\"Comic Sans MS, sans-serif\":\"Comic Sans MS\",\"Open Sans, sans-serif\":\"Open Sans\",\"Calibri, sans-serif\":\"Calibri\",\"Century Gothic, sans-serif\":\"Century Gothic\"}}}}),[isRtlDirection]);// Determine which containers to use based on guide type\nconst isAIAnnouncement=createWithAI&&(selectedTemplate===\"Announcement\"||selectedTemplateTour===\"Announcement\");const isAITour=createWithAI&&selectedTemplate===\"Tour\";const isTourAnnouncement=isAITour&&selectedTemplateTour===\"Announcement\";const isTourBanner=isAITour&&selectedTemplateTour===\"Banner\";const isTourTooltip=isAITour&&(selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\");const currentStepIndex=currentStep-1;let containersToRender=[];if(isAIAnnouncement&&!isTourAnnouncement){// For pure AI announcements (not in tours), use announcementGuideMetaData\ncontainersToRender=ensureAnnouncementRTEContainer(currentStepIndex,false);}else if(isAITour&&(isTourBanner||isTourTooltip||isTourAnnouncement)){var _toolTipGuideMetaData7;// For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\nif((_toolTipGuideMetaData7=toolTipGuideMetaData[currentStepIndex])!==null&&_toolTipGuideMetaData7!==void 0&&_toolTipGuideMetaData7.containers){containersToRender=toolTipGuideMetaData[currentStepIndex].containers.filter(c=>c.type===\"rte\");console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`,{totalContainers:toolTipGuideMetaData[currentStepIndex].containers.length,rteContainers:containersToRender.length,rteData:containersToRender.map(c=>({id:c.id,rteBoxValue:c.rteBoxValue}))});}else{console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);containersToRender=[];}}else{// For non-AI content, use rtesContainer\ncontainersToRender=rtesContainer;}return/*#__PURE__*/_jsx(_Fragment,{children:containersToRender.map(item=>{let rteText=\"\";let rteId=\"\";let id=\"\";if(isAIAnnouncement&&!isTourAnnouncement||isAITour&&(isTourBanner||isTourTooltip||isTourAnnouncement)){// For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n// Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\nrteText=item.rteBoxValue||\"\";rteId=item.id;id=item.id;}else{var _item$rtes,_item$rtes$,_item$rtes2,_item$rtes2$;// For non-AI content, get data from rtesContainer\nrteText=((_item$rtes=item.rtes)===null||_item$rtes===void 0?void 0:(_item$rtes$=_item$rtes[0])===null||_item$rtes$===void 0?void 0:_item$rtes$.text)||\"\";rteId=(_item$rtes2=item.rtes)===null||_item$rtes2===void 0?void 0:(_item$rtes2$=_item$rtes2[0])===null||_item$rtes2$===void 0?void 0:_item$rtes2$.id;id=item.id;}if(!id)return null;const isCurrentlyEditing=editingRTEId===id;const currentContainerRef=getContainerRef(id);const currentEditorRef=getEditorRef(id);return/*#__PURE__*/_jsx(Box,{ref:currentContainerRef,sx:{display:\"flex\",alignItems:\"center\",position:\"relative\",\"& .jodit-status-bar-link\":{display:\"none !important\"},\"& .jodit-editor\":{fontFamily:\"'Roboto', sans-serif !important\"},\".jodit-editor span\":{fontFamily:\"'Roboto', sans-serif !important\"},\".jodit-toolbar-button button\":{minWidth:\"29px !important\"},\".jodit-react-container\":{width:selectedTemplate===\"Banner\"?\"100%\":\"100%\",whiteSpace:\"pre-wrap\",wordBreak:\"break-word\"},\".jodit-workplace\":{minHeight:selectedTemplate===\"Banner\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"?\"50px !important\":null,maxHeight:selectedTemplate===\"Banner\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"?\"50px !important\":selectedTemplate===\"Announcement\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Announcement\"?\"calc(100vh - 400px) !important\":null,overflow:selectedTemplate===\"Banner\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"?\"hidden\":\"auto !important\"},\".jodit-container\":{minWidth:selectedTemplate===\"Banner\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"?\"50px !important\":null,minHeight:selectedTemplate===\"Banner\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"?\"50px !important\":null},\".jodit-toolbar__box\":{display:\"flex !important\",justifyContent:\"center !important\",height:selectedTemplate===\"Banner\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"?\"32px !important\":null,maxHeight:selectedTemplate===\"Banner\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Banner\"?\"32px !important\":null}},className:\"qadpt-rte\",children:!isCurrentlyEditing?selectedTemplate===\"Announcement\"||selectedTemplate===\"Tooltip\"||selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Announcement\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\"?/*#__PURE__*/_jsx(Tooltip,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleCloneContainer(item.id),disabled:isCloneDisabled,title:isCloneDisabled?translate(\"Maximum limit of 3 Rich Text sections reached\"):translate(\"Clone Section\"),sx:{\"&:hover\":{backgroundColor:\"transparent !important\"},svg:{height:\"24px\",path:{fill:\"var(--primarycolor)\"}}},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon},style:{opacity:isCloneDisabled?0.5:1,height:'24px'}})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleDeleteSection(item.id,rteId),sx:{\"&:hover\":{backgroundColor:\"transparent !important\"},svg:{path:{fill:\"var(--primarycolor)\"}}},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon},style:{height:'24px'}})})]}),placement:\"top\",slotProps:{tooltip:{sx:{backgroundColor:'white',color:'black',borderRadius:'4px',padding:'0px 4px',border:\"1px dashed var(--primarycolor)\",marginBottom:'30px !important'}}},PopperProps:{modifiers:[{name:'preventOverflow',options:{boundary:'viewport'}},{name:'flip',options:{enabled:true}}]},children:/*#__PURE__*/_jsx(\"div\",{style:{width:\"98%\"},children:/*#__PURE__*/_jsx(Box,{dangerouslySetInnerHTML:{__html:rteText||`<span style='color: #6c757d;'>${translate(\"Start Writing ....\")}</span>`},onClick:()=>setEditingRTEId(id),sx:{width:\"100%\",padding:\"5px 2px\",cursor:\"text\",whiteSpace:\"pre-wrap\",wordBreak:\"break-word\"}})})}):/*#__PURE__*/_jsx(\"div\",{style:{width:\"98%\"},children:/*#__PURE__*/_jsx(Box,{dangerouslySetInnerHTML:{__html:rteText||`<span style='color: #6c757d;'>${translate(\"Start Writing ....\")}</span>`},onClick:()=>setEditingRTEId(id),sx:{width:\"100%\",padding:\"5px 2px\",cursor:\"text\",whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",\"& p\":{margin:\"0\"}}})}):/*#__PURE__*/_jsxs(\"div\",{style:{width:\"100%\",maxWidth:\"100%\",margin:\"0 auto\"},children:[\" \",/*#__PURE__*/_jsx(JoditEditor,{ref:currentEditorRef,value:rteText,config:config,onChange:newContent=>handleUpdate(newContent,rteId,id)})]})},id);})});});export default RTEsection;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useMemo", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "useTranslation", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "RTEsection", "_ref", "ref", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "contentRef", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "console", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "content", "editor", "selection", "insertHTML", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "config", "readonly", "direction", "language", "toolbarSticky", "toolbarAdaptive", "buttons", "name", "iconURL", "list", "autofocus", "cursorAfterAutofocus", "events", "onPaste", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "length", "rteContainers", "rteData", "rteBoxValue", "children", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "isCurrentlyEditing", "currentEditorRef", "sx", "display", "alignItems", "position", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "minHeight", "maxHeight", "overflow", "justifyContent", "height", "className", "title", "size", "onClick", "disabled", "backgroundColor", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "style", "opacity", "placement", "slotProps", "tooltip", "color", "borderRadius", "padding", "border", "marginBottom", "PopperProps", "modifiers", "options", "boundary", "enabled", "cursor", "margin", "max<PERSON><PERSON><PERSON>", "value", "onChange"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef,useMemo } from \"react\";\r\nimport { Box, TextField, Tooltip, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon } from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n        };\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n    const config = useMemo(\r\n        () => ({\r\n            readonly: false, // all options from https://xdsoft.net/jodit/docs/,\r\n            direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n            \r\n// Jodit uses 'direction' not just 'rtl'\r\n        language:  'en', // Optional: change language as well\r\n            toolbarSticky: false,\r\n            toolbarAdaptive: false,\r\n            buttons: [\r\n\r\n        'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n        'font', 'fontsize', 'link',\r\n        {\r\n            name: 'more',\r\n            iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n            list: [\r\n                        'source',\r\n                        'image', 'video', 'table',\r\n                'align', 'undo', 'redo', '|',\r\n                'hr', 'eraser', 'copyformat',\r\n                'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                'outdent', 'indent', 'paragraph',\r\n            ]\r\n        }\r\n    ],\r\n    autofocus: true,\r\n    cursorAfterAutofocus: 'end' as const,\r\n    events: {\r\n                onPaste: handlePaste, // Attach custom onPaste handler\r\n    },\r\n    controls: {\r\n        font: {\r\n            list: {\r\n                \"Poppins, sans-serif\": \"Poppins\",\r\n                \"Roboto, sans-serif\": \"Roboto\",\r\n                \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                \"Open Sans, sans-serif\": \"Open Sans\",\r\n                \"Calibri, sans-serif\": \"Calibri\",\r\n                \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n            }\r\n        }\r\n            }\r\n    }),[isRtlDirection]\r\n\r\n    );\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n                    const isCurrentlyEditing = editingRTEId === id;\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    maxHeight: (\r\n  selectedTemplate === \"Banner\" ||\r\n  (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n)\r\n  ? \"50px !important\"\r\n  : (\r\n      selectedTemplate === \"Announcement\" ||\r\n      (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\")\r\n    )\r\n    ? \"calc(100vh - 400px) !important\"\r\n    : null,\r\n                                    overflow: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ?\"hidden\" : \"auto !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minWidth:selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    minHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: \"flex !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null,\r\n                                    maxHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null\r\n                                  }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {!isCurrentlyEditing ? (\r\n\r\n                                   (selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\") || (selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") ? (\r\n                                    <Tooltip\r\n                                        title={\r\n                                            <>\r\n                                                <IconButton\r\n                                                    size=\"small\"\r\n                                                    onClick={() => handleCloneContainer(item.id)}\r\n                                                    disabled={isCloneDisabled}\r\n                                                    title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                                    sx={{\r\n                                                        \"&:hover\": {\r\n                                                            backgroundColor: \"transparent !important\",\r\n                                                        },\r\n                                                        svg: {\r\n                                                            height: \"24px\",\r\n                                                            path: {\r\n                                                                fill:\"var(--primarycolor)\"\r\n                                                            }\r\n                                                        },\r\n                                                        }}\r\n                                                    >\r\n                                                    <span\r\n                                                        dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                                        style={{\r\n                                                            opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                            height: '24px'\r\n                                                        }}\r\n                                                    />\r\n                                                </IconButton>\r\n                                                <IconButton size=\"small\" onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                                sx={{\r\n                                                    \"&:hover\": {\r\n                                                        backgroundColor: \"transparent !important\",\r\n                                                        },\r\n                                                        svg: {\r\n                                                            path: {\r\n                                                                fill:\"var(--primarycolor)\"\r\n                                                            }\r\n                                                        },\r\n                                                    }}\r\n                                                >\r\n                                                    <span dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                                        style={{\r\n                                                            height: '24px'\r\n                                                        }}\r\n                                                    />\r\n                                                </IconButton>\r\n                                            </>\r\n                                        }\r\n                                        placement=\"top\"\r\n                                        slotProps={{\r\n                                            tooltip: {\r\n                                                sx: {\r\n                                                    backgroundColor: 'white',\r\n                                                    color: 'black',\r\n                                                    borderRadius: '4px',\r\n                                                    padding: '0px 4px',\r\n                                                    border: \"1px dashed var(--primarycolor)\",\r\n                                                    marginBottom: '30px !important'\r\n                                                },\r\n                                            },\r\n                                        }}\r\n                                        PopperProps={{\r\n                                            modifiers: [\r\n                                                {\r\n                                                    name: 'preventOverflow',\r\n                                                    options: { boundary: 'viewport' },\r\n                                                },\r\n                                                {\r\n                                                    name: 'flip',\r\n                                                    options: { enabled: true },\r\n                                                },\r\n                                            ],\r\n                                        }}\r\n                                    >\r\n                                        <div style={{ width: \"98%\" }}>\r\n                                            <Box\r\n                                                dangerouslySetInnerHTML={{\r\n                                                    __html: rteText || `<span style='color: #6c757d;'>${translate(\"Start Writing ....\")}</span>`\r\n                                                }}\r\n                                                onClick={() => setEditingRTEId(id)}\r\n                                                sx={{\r\n                                                    width: \"100%\",\r\n                                                    padding: \"5px 2px\",\r\n                                                    cursor: \"text\",\r\n                                                    whiteSpace: \"pre-wrap\",\r\n                                                    wordBreak: \"break-word\",\r\n\r\n                                                }}\r\n                                            />\r\n                                        </div>\r\n                                    </Tooltip>\r\n                                ) : (\r\n                                    <div style={{ width: \"98%\" }}>\r\n                                        <Box\r\n                                                dangerouslySetInnerHTML={{ __html: rteText || `<span style='color: #6c757d;'>${translate(\"Start Writing ....\")}</span>` }} onClick={() => setEditingRTEId(id)}\r\n                                            sx={{\r\n                                                width: \"100%\",\r\n                                                padding: \"5px 2px\",\r\n                                                cursor: \"text\",\r\n                                                whiteSpace: \"pre-wrap\",\r\n                                                wordBreak: \"break-word\",\r\n                                                \"& p\": {\r\n                                                    margin: \"0\",\r\n                                                },\r\n                                            }}\r\n                                        />\r\n                                    </div>\r\n                                    )\r\n\r\n                            ) : (\r\n                                <div style={{ width: \"100%\", maxWidth: \"100%\", margin: \"0 auto\" }}> {}\r\n                                <JoditEditor\r\n                                    ref={currentEditorRef}\r\n                                    value={rteText}\r\n                                    config={config}\r\n                                    onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                />\r\n                            </div>\r\n\r\n                            )}\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,UAAU,CAACC,OAAO,KAAQ,OAAO,CAC9E,OAASC,GAAG,CAAaC,OAAO,CAAEC,UAAU,KAAQ,eAAe,CACnE,MAAO,CAAAC,WAAW,KAAM,aAAa,CACrC,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CACvD,OAASC,QAAQ,CAAEC,UAAU,KAAQ,6BAA6B,CAElE,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAY/C,KAAM,CAAAC,UAAqC,cAAGhB,UAAU,CACpD,CAAAiB,IAAA,CAAmGC,GAAG,GAAK,IAA1G,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,sBAAsB,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,eAAgB,CAAC,CAAAR,IAAA,CAC7F,KAAM,CAAES,CAAC,CAAEC,SAAU,CAAC,CAAGlB,cAAc,CAAC,CAAC,CACzC,KAAM,CACFmB,aAAa,CACbC,kBAAkB,CAClBC,mBAAmB,CACnBC,iBAAiB,CACjBC,eAAe,CACfC,gBAAgB,CAChBC,oBAAoB,CACpBC,yBAAyB,CACzBC,oBAAoB,CACpBC,0BAA0B,CAC1BC,qBAAqB,CACrBC,YAAY,CACZC,WAAW,CACXC,8BACJ,CAAC,CAAGnC,cAAc,CAAC,CAAC,CAEpB;AACA,KAAM,CAACoC,YAAY,CAAEC,eAAe,CAAC,CAAG9C,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAAA+C,UAAU,CAAG7C,MAAM,CAAS,EAAE,CAAC,CAErC;AACA,KAAM,CAAA8C,UAAU,CAAG9C,MAAM,CAAoC,GAAI,CAAA+C,GAAG,CAAC,CAAC,CAAC,CACvE,KAAM,CAAAC,aAAa,CAAGhD,MAAM,CAA+C,GAAI,CAAA+C,GAAG,CAAC,CAAC,CAAC,CAErF;AACA,KAAM,CAAAE,YAAY,CAAIC,KAAa,EAAK,CACpC,GAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,CAAE,CAChCJ,UAAU,CAACK,OAAO,CAACE,GAAG,CAACH,KAAK,cAAErD,KAAK,CAACyD,SAAS,CAAC,CAAC,CAAC,CACpD,CACA,MAAO,CAAAR,UAAU,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC,CACxC,CAAC,CAED;AACA,KAAM,CAAAM,eAAe,CAAIN,KAAa,EAAK,CACvC,GAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,CAAE,CACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,cAAErD,KAAK,CAACyD,SAAS,CAAC,CAAC,CAAC,CACvD,CACA,MAAO,CAAAN,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC,CAC3C,CAAC,CAED;AACAnD,SAAS,CAAC,IAAM,CACZ,KAAM,CAAA0D,kBAAkB,CAAIC,KAAiB,EAAK,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC9C,GAAI,CAACnB,YAAY,CAAE,OAAQ;AAE3B,KAAM,CAAAoB,yBAAyB,CAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,GAAK,IAAI,CACzG,KAAM,CAAAC,oBAAoB,CAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,GAAK,IAAI,CAC9F,KAAM,CAAAE,aAAa,EAAAR,qBAAA,CAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,UAAAV,qBAAA,iBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC,CAC5F,KAAM,CAAAO,kBAAkB,EAAAX,sBAAA,CAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,UAAAT,sBAAA,iBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC,CACnG,KAAM,CAAAQ,sBAAsB,CAAGD,kBAAkB,IAAAV,sBAAA,CAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,UAAAR,sBAAA,iBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC,EACnI,KAAM,CAAAS,iBAAiB,CAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC,CAChG,KAAM,CAAAC,WAAW,EAAAd,sBAAA,CAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,UAAAP,sBAAA,iBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC,CACpG,KAAM,CAAAa,qBAAqB,CAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,GAAK,IAAI,CAC7G,KAAM,CAAAa,cAAc,CAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,GAAK,IAAI,CAErG;AACA,KAAM,CAAAc,mBAAmB,CAAGvB,eAAe,CAACb,YAAY,CAAC,CAEzD;AACA,GACIoC,mBAAmB,SAAnBA,mBAAmB,WAAnBA,mBAAmB,CAAE5B,OAAO,EAC5B,CAAC4B,mBAAmB,CAAC5B,OAAO,CAACmB,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC,EAAI;AAC/D,CAACG,aAAa,EAAI;AAClB,CAACI,kBAAkB,EAAI;AACvB,CAACC,sBAAsB,EAAI;AAC3B,CAACC,iBAAiB,EAAI;AACtB,CAACG,WAAW,EAAI;AAChB,CAACC,qBAAqB,EAAI;AAC1B,CAACC,cAAc,EACf,CAACf,yBAAyB,EAC1B,CAACG,oBAAoB,CACvB,CACEtB,eAAe,CAAC,IAAI,CAAC,CAAE;AAC3B,CACJ,CAAC,CAEDwB,QAAQ,CAACY,gBAAgB,CAAC,WAAW,CAAEvB,kBAAkB,CAAC,CAC1D,MAAO,IAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,CAAExB,kBAAkB,CAAC,CAC9E,CAAC,CAAE,CAACd,YAAY,CAAC,CAAC,CAElB5C,SAAS,CAAC,IAAM,CACZ,GAAI4C,YAAY,CAAE,CACd,KAAM,CAAAuC,SAAS,CAAGjC,YAAY,CAACN,YAAY,CAAC,CAC5C,GAAIuC,SAAS,SAATA,SAAS,WAATA,SAAS,CAAE/B,OAAO,CAAE,CACpBgC,UAAU,CAAC,IAAM,CACb;AAAA,CACH,CAAE,EAAE,CAAC,CACV,CACJ,CACJ,CAAC,CAAE,CAACxC,YAAY,CAAC,CAAC,CAIlB,KAAM,CAAAyC,YAAY,CAAGA,CAACC,UAAkB,CAAEnC,KAAa,CAAEoC,WAAmB,GAAK,CAC7EzC,UAAU,CAACM,OAAO,CAAGkC,UAAU,CAE/B;AACA,KAAM,CAAAE,gBAAgB,CAAG/C,YAAY,GAAKN,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,CAAC,CACzH,KAAM,CAAAqD,QAAQ,CAAGhD,YAAY,EAAIN,gBAAgB,GAAK,MAAM,CAC5D,KAAM,CAAAuD,kBAAkB,CAAGD,QAAQ,EAAIrD,oBAAoB,GAAK,cAAc,CAC9E,KAAM,CAAAuD,YAAY,CAAGF,QAAQ,EAAIrD,oBAAoB,GAAK,QAAQ,CAClE,KAAM,CAAAwD,aAAa,CAAGH,QAAQ,GAAKrD,oBAAoB,GAAK,SAAS,EAAIA,oBAAoB,GAAK,SAAS,CAAC,CAE5GyD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAE,CACpCrD,YAAY,CACZN,gBAAgB,CAChBC,oBAAoB,CACpBoD,gBAAgB,CAChBC,QAAQ,CACRE,YAAY,CACZJ,WAAW,CACXD,UAAU,CAAEA,UAAU,CAACS,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAC9C,CAAC,CAAC,CAEF,GAAIP,gBAAgB,CAAE,CAClB,KAAM,CAAAQ,gBAAgB,CAAGtD,WAAW,CAAG,CAAC,CAExC,GAAIgD,kBAAkB,CAAE,KAAAO,qBAAA,CAAAC,sBAAA,CACpB;AACA,KAAM,CAAAC,gBAAgB,EAAAF,qBAAA,CAAG3D,oBAAoB,CAAC0D,gBAAgB,CAAC,UAAAC,qBAAA,kBAAAC,sBAAA,CAAtCD,qBAAA,CAAwCG,UAAU,UAAAF,sBAAA,iBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,EAAKA,SAAS,CAAC3B,EAAE,GAAKY,WAAW,EAAIe,SAAS,CAACC,IAAI,GAAK,KAC3E,CAAC,CAED,GAAIJ,gBAAgB,CAAE,CAClB;AACA3D,qBAAqB,CAAC+C,WAAW,CAAED,UAAU,CAAC,CAClD,CACJ,CAAC,IAAM,KAAAkB,qBAAA,CAAAC,sBAAA,CACH;AACA,KAAM,CAAAC,qBAAqB,EAAAF,qBAAA,CAAGnE,yBAAyB,CAAC2D,gBAAgB,CAAC,UAAAQ,qBAAA,kBAAAC,sBAAA,CAA3CD,qBAAA,CAA6CJ,UAAU,UAAAK,sBAAA,iBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,EAAKA,SAAS,CAAC3B,EAAE,GAAKY,WAAW,EAAIe,SAAS,CAACC,IAAI,GAAK,KAC3E,CAAC,CAED,GAAIG,qBAAqB,CAAE,CACvB;AACAnE,0BAA0B,CAACgD,WAAW,CAAED,UAAU,CAAC,CACvD,CACJ,CACJ,CAAC,IAAM,IAAIG,QAAQ,GAAKE,YAAY,EAAIC,aAAa,CAAC,CAAE,KAAAe,sBAAA,CAAAC,sBAAA,CACpD;AACA,KAAM,CAAAZ,gBAAgB,CAAGtD,WAAW,CAAG,CAAC,CACxC,KAAM,CAAAyD,gBAAgB,EAAAQ,sBAAA,CAAGrE,oBAAoB,CAAC0D,gBAAgB,CAAC,UAAAW,sBAAA,kBAAAC,sBAAA,CAAtCD,sBAAA,CAAwCP,UAAU,UAAAQ,sBAAA,iBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,EAAKA,SAAS,CAAC3B,EAAE,GAAKY,WAAW,EAAIe,SAAS,CAACC,IAAI,GAAK,KAC3E,CAAC,CAED,GAAIJ,gBAAgB,CAAE,CAClB;AACA3D,qBAAqB,CAAC+C,WAAW,CAAED,UAAU,CAAC,CAC9CO,OAAO,CAACC,GAAG,CAAC,kCAAkC1D,oBAAoB,kBAAkB,CAAC,CACzF,CAAC,IAAM,KAAAyE,sBAAA,CAAAC,sBAAA,CACHjB,OAAO,CAACkB,IAAI,CAAC,kCAAkC3E,oBAAoB,OAAO,CAAE,CACxE4D,gBAAgB,CAChBT,WAAW,CACXyB,mBAAmB,EAAAH,sBAAA,CAAEvE,oBAAoB,CAAC0D,gBAAgB,CAAC,UAAAa,sBAAA,kBAAAC,sBAAA,CAAtCD,sBAAA,CAAwCT,UAAU,UAAAU,sBAAA,iBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,GAAK,CAAEvC,EAAE,CAAEuC,CAAC,CAACvC,EAAE,CAAE4B,IAAI,CAAEW,CAAC,CAACX,IAAK,CAAC,CAAC,CAClH,CAAC,CAAC,CACN,CACJ,CAAC,IAAM,CACH;AACAxE,kBAAkB,CAACwD,WAAW,CAAEpC,KAAK,CAAEmC,UAAU,CAAC,CAClDO,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CAC7D,CAEA9D,mBAAmB,CAAC,IAAI,CAAC,CAC7B,CAAC,CACD,KAAM,CAAAmF,oBAAoB,CAAI5B,WAAmB,EAAK,CAClD;AACA,GAAI5D,eAAe,CAAE,CACjB,OAAQ;AACZ,CAEA;AACAM,iBAAiB,CAACsD,WAAW,CAAC,CAE9B;AACA,GAAI7D,OAAO,CAAE,CACTA,OAAO,CAAC,CAAC,CACb,CACJ,CAAC,CACD,KAAM,CAAA0F,mBAAmB,CAAGA,CAAC7B,WAAmB,CAAEpC,KAAY,GAAK,CAC/D;AACA,KAAM,CAAAqC,gBAAgB,CAAG/C,YAAY,GAAKN,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,CAAC,CAEzH,GAAIoD,gBAAgB,CAAE,CAClB;AACA;AACAtD,eAAe,CAACqD,WAAW,CAAEpC,KAAK,CAAC,CACvC,CAAC,IAAM,CACH;AACAjB,eAAe,CAACqD,WAAW,CAAEpC,KAAK,CAAC,CACvC,CAEA;AACA5B,sBAAsB,CAACC,KAAK,CAAC,CACjC,CAAC,CACD,KAAM,CAAA6F,WAAW,CAAI1D,KAA2C,EAAK,CACjEA,KAAK,CAAC2D,cAAc,CAAC,CAAC,CAEtB,KAAM,CAAAC,aAAa,CAAG5D,KAAK,CAAC4D,aAAa,CACzC,KAAM,CAAAC,UAAU,CAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC,CACtD,KAAM,CAAAC,UAAU,CAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC,CAErD,GAAIC,UAAU,CAAE,CACZ,KAAM,CAAAC,YAAY,CAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,CACtD,GAAID,YAAY,CAAE,CACdE,aAAa,CAACH,UAAU,CAAC,CAC7B,CAAC,IAAM,CACHG,aAAa,CAACH,UAAU,CAAC,CAC7B,CACJ,CAAC,IAAM,CACHG,aAAa,CAACL,UAAU,CAAC,CAC7B,CACJ,CAAC,CAGD,KAAM,CAAAK,aAAa,CAAIC,OAAe,EAAK,CACvC,GAAIlF,YAAY,CAAE,CACd,KAAM,CAAAuC,SAAS,CAAGjC,YAAY,CAACN,YAAY,CAAC,CAC5C,GAAIuC,SAAS,SAATA,SAAS,WAATA,SAAS,CAAE/B,OAAO,CAAE,CACpB,KAAM,CAAA2E,MAAM,CAAI5C,SAAS,CAAC/B,OAAO,CAAS2E,MAAM,CAChDA,MAAM,CAACC,SAAS,CAACC,UAAU,CAACH,OAAO,CAAC,CACxC,CACJ,CACJ,CAAC,CACD,KAAM,CAACI,cAAc,CAAEC,iBAAiB,CAAC,CAAGpI,QAAQ,CAAU,KAAK,CAAC,CACpEC,SAAS,CAAC,IAAM,CACpB,KAAM,CAAAoI,GAAG,CAAG/D,QAAQ,CAACgE,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,EAAI,KAAK,CACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,GAAK,KAAK,CAAC,CAClD,CAAC,CAAE,EAAE,CAAC,CACF,KAAM,CAAAC,MAAM,CAAGrI,OAAO,CAClB,KAAO,CACHsI,QAAQ,CAAE,KAAK,CAAE;AACjBC,SAAS,CAAER,cAAc,CAAG,KAAK,CAAY,KAAc,CAEvE;AACQS,QAAQ,CAAG,IAAI,CAAE;AACbC,aAAa,CAAE,KAAK,CACpBC,eAAe,CAAE,KAAK,CACtBC,OAAO,CAAE,CAEb,MAAM,CAAE,QAAQ,CAAE,WAAW,CAAE,eAAe,CAAE,IAAI,CAAE,IAAI,CAAE,OAAO,CACnE,MAAM,CAAE,UAAU,CAAE,MAAM,CAC1B,CACIC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,+DAA+D,CACxEC,IAAI,CAAE,CACM,QAAQ,CACR,OAAO,CAAE,OAAO,CAAE,OAAO,CACjC,OAAO,CAAE,MAAM,CAAE,MAAM,CAAE,GAAG,CAC5B,IAAI,CAAE,QAAQ,CAAE,YAAY,CAC5B,QAAQ,CAAE,UAAU,CAAE,OAAO,CAAE,aAAa,CAAE,WAAW,CAAE,GAAG,CAC9D,SAAS,CAAE,QAAQ,CAAE,WAAW,CAExC,CAAC,CACJ,CACDC,SAAS,CAAE,IAAI,CACfC,oBAAoB,CAAE,KAAc,CACpCC,MAAM,CAAE,CACIC,OAAO,CAAEhC,WAAa;AAClC,CAAC,CACDiC,QAAQ,CAAE,CACNC,IAAI,CAAE,CACFN,IAAI,CAAE,CACF,qBAAqB,CAAE,SAAS,CAChC,oBAAoB,CAAE,QAAQ,CAC9B,2BAA2B,CAAE,eAAe,CAC5C,uBAAuB,CAAE,WAAW,CACpC,qBAAqB,CAAE,SAAS,CAChC,4BAA4B,CAAE,gBAClC,CACJ,CACI,CACR,CAAC,CAAC,CAAC,CAACf,cAAc,CAElB,CAAC,CAEG;AACA,KAAM,CAAA1C,gBAAgB,CAAG/C,YAAY,GAAKN,gBAAgB,GAAK,cAAc,EAAIC,oBAAoB,GAAK,cAAc,CAAC,CACzH,KAAM,CAAAqD,QAAQ,CAAGhD,YAAY,EAAIN,gBAAgB,GAAK,MAAM,CAC5D,KAAM,CAAAuD,kBAAkB,CAAGD,QAAQ,EAAIrD,oBAAoB,GAAK,cAAc,CAC9E,KAAM,CAAAuD,YAAY,CAAGF,QAAQ,EAAIrD,oBAAoB,GAAK,QAAQ,CAClE,KAAM,CAAAwD,aAAa,CAAGH,QAAQ,GAAKrD,oBAAoB,GAAK,SAAS,EAAIA,oBAAoB,GAAK,SAAS,CAAC,CAC5G,KAAM,CAAA4D,gBAAgB,CAAGtD,WAAW,CAAG,CAAC,CAExC,GAAI,CAAA8G,kBAAyB,CAAG,EAAE,CAElC,GAAIhE,gBAAgB,EAAI,CAACE,kBAAkB,CAAE,CACzC;AACA8D,kBAAkB,CAAG7G,8BAA8B,CAACqD,gBAAgB,CAAE,KAAK,CAAC,CAChF,CAAC,IAAM,IAAIP,QAAQ,GAAKE,YAAY,EAAIC,aAAa,EAAIF,kBAAkB,CAAC,CAAE,KAAA+D,sBAAA,CAC1E;AACA,IAAAA,sBAAA,CAAInH,oBAAoB,CAAC0D,gBAAgB,CAAC,UAAAyD,sBAAA,WAAtCA,sBAAA,CAAwCrD,UAAU,CAAE,CACpDoD,kBAAkB,CAAGlH,oBAAoB,CAAC0D,gBAAgB,CAAC,CAACI,UAAU,CAACsD,MAAM,CAACxC,CAAC,EAAIA,CAAC,CAACX,IAAI,GAAK,KAAK,CAAC,CACpGV,OAAO,CAACC,GAAG,CAAC,yDAAyD1D,oBAAoB,SAAS4D,gBAAgB,GAAG,CAAE,CACnH2D,eAAe,CAAErH,oBAAoB,CAAC0D,gBAAgB,CAAC,CAACI,UAAU,CAACwD,MAAM,CACzEC,aAAa,CAAEL,kBAAkB,CAACI,MAAM,CACxCE,OAAO,CAAEN,kBAAkB,CAACvC,GAAG,CAACC,CAAC,GAAK,CAAEvC,EAAE,CAAEuC,CAAC,CAACvC,EAAE,CAAEoF,WAAW,CAAE7C,CAAC,CAAC6C,WAAY,CAAC,CAAC,CACnF,CAAC,CAAC,CACN,CAAC,IAAM,CACHlE,OAAO,CAACkB,IAAI,CAAC,iDAAiD3E,oBAAoB,SAAS4D,gBAAgB,EAAE,CAAC,CAC9GwD,kBAAkB,CAAG,EAAE,CAC3B,CACJ,CAAC,IAAM,CACH;AACAA,kBAAkB,CAAG1H,aAAa,CACtC,CAEA,mBACIjB,IAAA,CAAAE,SAAA,EAAAiJ,QAAA,CACKR,kBAAkB,CAACvC,GAAG,CAAEgD,IAAS,EAAK,CACnC,GAAI,CAAAC,OAAO,CAAG,EAAE,CAChB,GAAI,CAAA/G,KAAK,CAAG,EAAE,CACd,GAAI,CAAAwB,EAAE,CAAG,EAAE,CAEX,GAAKa,gBAAgB,EAAI,CAACE,kBAAkB,EAAMD,QAAQ,GAAKE,YAAY,EAAIC,aAAa,EAAIF,kBAAkB,CAAE,CAAE,CAClH;AACA;AACAwE,OAAO,CAAGD,IAAI,CAACF,WAAW,EAAI,EAAE,CAChC5G,KAAK,CAAG8G,IAAI,CAACtF,EAAE,CACfA,EAAE,CAAGsF,IAAI,CAACtF,EAAE,CAChB,CAAC,IAAM,KAAAwF,UAAA,CAAAC,WAAA,CAAAC,WAAA,CAAAC,YAAA,CACH;AACAJ,OAAO,CAAG,EAAAC,UAAA,CAAAF,IAAI,CAACM,IAAI,UAAAJ,UAAA,kBAAAC,WAAA,CAATD,UAAA,CAAY,CAAC,CAAC,UAAAC,WAAA,iBAAdA,WAAA,CAAgBI,IAAI,GAAI,EAAE,CACpCrH,KAAK,EAAAkH,WAAA,CAAGJ,IAAI,CAACM,IAAI,UAAAF,WAAA,kBAAAC,YAAA,CAATD,WAAA,CAAY,CAAC,CAAC,UAAAC,YAAA,iBAAdA,YAAA,CAAgB3F,EAAE,CAC1BA,EAAE,CAAGsF,IAAI,CAACtF,EAAE,CAChB,CAEA,GAAI,CAACA,EAAE,CAAE,MAAO,KAAI,CAEpB,KAAM,CAAA8F,kBAAkB,CAAG7H,YAAY,GAAK+B,EAAE,CAC9C,KAAM,CAAAK,mBAAmB,CAAGvB,eAAe,CAACkB,EAAE,CAAC,CAC/C,KAAM,CAAA+F,gBAAgB,CAAGxH,YAAY,CAACyB,EAAE,CAAC,CAEzC,mBACI9D,IAAA,CAACT,GAAG,EAEAgB,GAAG,CAAE4D,mBAAoB,CACzB2F,EAAE,CAAE,CACAC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,UAAU,CACpB,0BAA0B,CAAE,CACxBF,OAAO,CAAE,iBACb,CAAC,CACD,iBAAiB,CAAE,CACfG,UAAU,CAAE,iCAChB,CAAC,CACD,oBAAoB,CAAE,CAClBA,UAAU,CAAE,iCAChB,CAAC,CACD,8BAA8B,CAAE,CAC5BC,QAAQ,CAAE,iBACd,CAAC,CACD,wBAAwB,CAAE,CACtBC,KAAK,CAAE9I,gBAAgB,GAAK,QAAQ,CAAG,MAAM,CAAG,MAAM,CACtD+I,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YACf,CAAC,CACD,kBAAkB,CAAE,CAChBC,SAAS,CAAEjJ,gBAAgB,GAAG,QAAQ,EAAKA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,QAAS,CAAG,iBAAiB,CAAE,IAAI,CACtIiJ,SAAS,CAC3ClJ,gBAAgB,GAAK,QAAQ,EAC5BA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,QAAS,CAEhE,iBAAiB,CAEfD,gBAAgB,GAAK,cAAc,EAClCA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,cAAe,CAExE,gCAAgC,CAChC,IAAI,CAC0BkJ,QAAQ,CAAEnJ,gBAAgB,GAAG,QAAQ,EAAKA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,QAAS,CAAE,QAAQ,CAAG,iBAC5H,CAAC,CACD,kBAAkB,CAAE,CAChB4I,QAAQ,CAAC7I,gBAAgB,GAAG,QAAQ,EAAKA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,QAAS,CAAG,iBAAiB,CAAE,IAAI,CACpIgJ,SAAS,CAAEjJ,gBAAgB,GAAG,QAAQ,EAAIA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,QAAS,CAAG,iBAAiB,CAAE,IACrI,CAAC,CACD,qBAAqB,CAAE,CACnBwI,OAAO,CAAE,iBAAiB,CAC1BW,cAAc,CAAE,mBAAmB,CACnCC,MAAM,CAAErJ,gBAAgB,GAAG,QAAQ,EAAKA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,QAAS,CAAG,iBAAiB,CAAE,IAAI,CACnIiJ,SAAS,CAAElJ,gBAAgB,GAAG,QAAQ,EAAIA,gBAAgB,GAAK,MAAM,EAAIC,oBAAoB,GAAK,QAAS,CAAG,iBAAiB,CAAE,IACnI,CACN,CAAE,CACFqJ,SAAS,CAAC,WAAW,CAAAzB,QAAA,CAEpB,CAACS,kBAAkB,CAEZtI,gBAAgB,GAAK,cAAc,EAAIA,gBAAgB,GAAK,SAAS,EAAIA,gBAAgB,GAAK,SAAS,EAAMC,oBAAoB,GAAK,cAAc,EAAIA,oBAAoB,GAAK,SAAS,EAAIA,oBAAoB,GAAK,SAAU,cACjOvB,IAAA,CAACR,OAAO,EACJqL,KAAK,cACDzK,KAAA,CAAAF,SAAA,EAAAiJ,QAAA,eACInJ,IAAA,CAACP,UAAU,EACPqL,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAMzE,oBAAoB,CAAC8C,IAAI,CAACtF,EAAE,CAAE,CAC7CkH,QAAQ,CAAElK,eAAgB,CAC1B+J,KAAK,CAAE/J,eAAe,CAAGE,SAAS,CAAC,+CAA+C,CAAC,CAAGA,SAAS,CAAC,eAAe,CAAE,CACjH8I,EAAE,CAAE,CACA,SAAS,CAAE,CACPmB,eAAe,CAAE,wBACrB,CAAC,CACDC,GAAG,CAAE,CACDP,MAAM,CAAE,MAAM,CACdQ,IAAI,CAAE,CACFC,IAAI,CAAC,qBACT,CACJ,CACA,CAAE,CAAAjC,QAAA,cAENnJ,IAAA,SACIqL,uBAAuB,CAAE,CAAEC,MAAM,CAAE1L,QAAS,CAAE,CAC9C2L,KAAK,CAAE,CACHC,OAAO,CAAE1K,eAAe,CAAG,GAAG,CAAG,CAAC,CAClC6J,MAAM,CAAE,MACZ,CAAE,CACL,CAAC,CACM,CAAC,cACb3K,IAAA,CAACP,UAAU,EAACqL,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEA,CAAA,GAAMxE,mBAAmB,CAAC6C,IAAI,CAACtF,EAAE,CAAExB,KAAK,CAAE,CAC5EwH,EAAE,CAAE,CACA,SAAS,CAAE,CACPmB,eAAe,CAAE,wBACjB,CAAC,CACDC,GAAG,CAAE,CACDC,IAAI,CAAE,CACFC,IAAI,CAAC,qBACT,CACJ,CACJ,CAAE,CAAAjC,QAAA,cAEFnJ,IAAA,SAAMqL,uBAAuB,CAAE,CAAEC,MAAM,CAAEzL,UAAW,CAAE,CAClD0L,KAAK,CAAE,CACHZ,MAAM,CAAE,MACZ,CAAE,CACL,CAAC,CACM,CAAC,EACf,CACL,CACDc,SAAS,CAAC,KAAK,CACfC,SAAS,CAAE,CACPC,OAAO,CAAE,CACL7B,EAAE,CAAE,CACAmB,eAAe,CAAE,OAAO,CACxBW,KAAK,CAAE,OAAO,CACdC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,gCAAgC,CACxCC,YAAY,CAAE,iBAClB,CACJ,CACJ,CAAE,CACFC,WAAW,CAAE,CACTC,SAAS,CAAE,CACP,CACIhE,IAAI,CAAE,iBAAiB,CACvBiE,OAAO,CAAE,CAAEC,QAAQ,CAAE,UAAW,CACpC,CAAC,CACD,CACIlE,IAAI,CAAE,MAAM,CACZiE,OAAO,CAAE,CAAEE,OAAO,CAAE,IAAK,CAC7B,CAAC,CAET,CAAE,CAAAlD,QAAA,cAEFnJ,IAAA,QAAKuL,KAAK,CAAE,CAAEnB,KAAK,CAAE,KAAM,CAAE,CAAAjB,QAAA,cACzBnJ,IAAA,CAACT,GAAG,EACA8L,uBAAuB,CAAE,CACrBC,MAAM,CAAEjC,OAAO,EAAI,iCAAiCrI,SAAS,CAAC,oBAAoB,CAAC,SACvF,CAAE,CACF+J,OAAO,CAAEA,CAAA,GAAM/I,eAAe,CAAC8B,EAAE,CAAE,CACnCgG,EAAE,CAAE,CACAM,KAAK,CAAE,MAAM,CACb0B,OAAO,CAAE,SAAS,CAClBQ,MAAM,CAAE,MAAM,CACdjC,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAEf,CAAE,CACL,CAAC,CACD,CAAC,CACD,CAAC,cAEVtK,IAAA,QAAKuL,KAAK,CAAE,CAAEnB,KAAK,CAAE,KAAM,CAAE,CAAAjB,QAAA,cACzBnJ,IAAA,CAACT,GAAG,EACI8L,uBAAuB,CAAE,CAAEC,MAAM,CAAEjC,OAAO,EAAI,iCAAiCrI,SAAS,CAAC,oBAAoB,CAAC,SAAU,CAAE,CAAC+J,OAAO,CAAEA,CAAA,GAAM/I,eAAe,CAAC8B,EAAE,CAAE,CAClKgG,EAAE,CAAE,CACAM,KAAK,CAAE,MAAM,CACb0B,OAAO,CAAE,SAAS,CAClBQ,MAAM,CAAE,MAAM,CACdjC,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvB,KAAK,CAAE,CACHiC,MAAM,CAAE,GACZ,CACJ,CAAE,CACL,CAAC,CACD,CACJ,cAGLnM,KAAA,QAAKmL,KAAK,CAAE,CAAEnB,KAAK,CAAE,MAAM,CAAEoC,QAAQ,CAAE,MAAM,CAAED,MAAM,CAAE,QAAS,CAAE,CAAApD,QAAA,EAAC,GAAC,cACpEnJ,IAAA,CAACN,WAAW,EACRa,GAAG,CAAEsJ,gBAAiB,CACtB4C,KAAK,CAAEpD,OAAQ,CACf1B,MAAM,CAAEA,MAAO,CACf+E,QAAQ,CAAGjI,UAAU,EAAKD,YAAY,CAACC,UAAU,CAAEnC,KAAK,CAAEwB,EAAE,CAAE,CACjE,CAAC,EACD,CAEJ,EA7KIA,EA8KJ,CAAC,CAEd,CAAC,CAAC,CACJ,CAAC,CAEX,CACJ,CAAC,CAED,cAAe,CAAAzD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}