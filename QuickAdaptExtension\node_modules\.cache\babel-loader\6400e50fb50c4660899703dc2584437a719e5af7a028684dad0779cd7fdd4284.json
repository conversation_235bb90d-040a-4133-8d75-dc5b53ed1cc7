{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Quickadopt Bugs Devlatest\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Imagesection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, copyicon, deleteicon, sectionheight, Settings, CrossIcon } from \"../../../assets/icons/icons\";\nimport useDrawerStore, { IMG_CONTAINER_DEFAULT_HEIGHT, IMG_CONTAINER_MAX_HEIGHT, IMG_CONTAINER_MIN_HEIGHT, IMG_OBJECT_FIT, IMG_STEP_VALUE } from \"../../../store/drawerStore\";\nimport { ChromePicker } from \"react-color\";\nimport \"./PopupSections.css\";\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSection = ({\n  setImageSrc,\n  setImageName,\n  onDelete,\n  onClone,\n  isCloneDisabled\n}) => {\n  _s();\n  var _imagesContainer$find;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    uploadImage,\n    imagesContainer,\n    imageAnchorEl,\n    setImageAnchorEl,\n    replaceImage,\n    cloneImageContainer,\n    deleteImageContainer,\n    updateImageContainer,\n    toggleFit,\n    setImageSrc: storeImageSrc\n  } = useDrawerStore(state => state);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('info');\n  const [snackbarKey, setSnackbarKey] = useState(0);\n  const openSnackbar = () => {\n    setSnackbarKey(prev => prev + 1);\n    setSnackbarOpen(true);\n  };\n  const closeSnackbar = () => {\n    setSnackbarOpen(false);\n  };\n  const [showHyperlinkInput, setShowHyperlinkInput] = useState({\n    currentContainerId: \"\",\n    isOpen: false\n  });\n  const [imageLink, setImageLink] = useState(\"\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState({\n    currentContainerId: \"\",\n    isImage: false,\n    height: IMG_CONTAINER_DEFAULT_HEIGHT\n  });\n  const [selectedAction, setSelectedAction] = useState(\"none\");\n  const [isModelOpen, setModelOpen] = useState(false);\n  const [formOfUpload, setFormOfUpload] = useState(\"\");\n  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [isReplaceImage, setReplaceImage] = useState(false);\n  const openSettingsPopover = Boolean(settingsAnchorEl);\n  const handleActionChange = event => {\n    setSelectedAction(event.target.value);\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchorEl(event.currentTarget);\n  };\n  const handleCloseSettingsPopover = () => {\n    setSettingsAnchorEl(null);\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\",\n    marginTop: \"10px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"column\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    width: \"100%\"\n  };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      var _event$target$files2;\n      const parts = file.name.split('.');\n      const extension = parts.pop();\n\n      // Check for double extensions (e.g. file.html.png) or missing/invalid extension\n      if (parts.length > 1 || !extension) {\n        setSnackbarMessage(\"Uploaded file name should not contain any special character\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      if (file.name.length > 128) {\n        setSnackbarMessage(\"File name should not exceed 128 characters\");\n        setSnackbarSeverity(\"error\");\n        setSnackbarOpen(true);\n        event.target.value = '';\n        return;\n      }\n      setImageName((_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0].name);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        const base64Image = reader.result;\n        storeImageSrc(base64Image);\n        setImageSrc(base64Image);\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.name,\n          id: crypto.randomUUID(),\n          url: base64Image,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n    setModelOpen(false);\n  };\n  const handleImageUploadFormApp = file => {\n    if (file) {\n      storeImageSrc(file.Url);\n      setImageSrc(file.Url);\n      if (isReplaceImage) {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.FileName,\n          id: imageAnchorEl.buttonId,\n          url: file.Url,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n        setReplaceImage(false);\n      } else {\n        uploadImage(imageAnchorEl.containerId, {\n          altText: file.FileName,\n          id: crypto.randomUUID(),\n          // Use existing ID\n          url: file.Url,\n          // Directly use the URL\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      }\n    }\n    setModelOpen(false);\n  };\n  const handleReplaceImage = event => {\n    var _event$target$files3;\n    const file = (_event$target$files3 = event.target.files) === null || _event$target$files3 === void 0 ? void 0 : _event$target$files3[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\n          altText: file.name,\n          id: imageAnchorEl.buttonId,\n          url: reader.result,\n          backgroundColor: \"#ffffff\",\n          objectFit: IMG_OBJECT_FIT\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = (event, containerId, imageId, isImage, currentHeight) => {\n    // @ts-ignore\n    if ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\n    setImageAnchorEl({\n      buttonId: imageId,\n      containerId: containerId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    setSettingsAnchorEl(null);\n    setCurrentImageSectionInfo({\n      currentContainerId: containerId,\n      isImage,\n      height: currentHeight\n    });\n    setShowHyperlinkInput({\n      currentContainerId: \"\",\n      isOpen: false\n    });\n  };\n  const handleClose = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n  };\n  const open = Boolean(imageAnchorEl.value);\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = prevHeight => {\n    if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\n    const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const handleDecreaseHeight = prevHeight => {\n    if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\n    const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      height: newHeight\n    });\n    setCurrentImageSectionInfo(prev => ({\n      ...prev,\n      height: newHeight\n    }));\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n    // setModelOpen(true);\n    // setReplaceImage(true);\n  };\n  const currentContainerColor = ((_imagesContainer$find = imagesContainer.find(item => item.id === imageAnchorEl.containerId)) === null || _imagesContainer$find === void 0 ? void 0 : _imagesContainer$find.style.backgroundColor) || \"transparent\";\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setImageAnchorEl({\n      buttonId: \"\",\n      containerId: \"\",\n      // @ts-ignore\n      value: null\n    });\n\n    // Call the delete function from the store\n    deleteImageContainer(imageAnchorEl.containerId);\n\n    // Call the onDelete callback if provided\n    if (onDelete) {\n      onDelete();\n    }\n  };\n  const handleLinkSubmit = event => {\n    if (event.key === \"Enter\" && imageLink) {\n      uploadImage(imageAnchorEl.containerId, {\n        altText: \"New Image\",\n        id: crypto.randomUUID(),\n        url: imageLink,\n        backgroundColor: \"transparent\",\n        objectFit: IMG_OBJECT_FIT\n      });\n      setShowHyperlinkInput({\n        currentContainerId: \"\",\n        isOpen: false\n      });\n    }\n  };\n  const handleCloneImgContainer = () => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneImageContainer(imageAnchorEl.containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    updateImageContainer(imageAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [imagesContainer.map(item => {\n      var _item$images$, _item$images$2, _item$images$3, _item$style;\n      const imageSrc = (_item$images$ = item.images[0]) === null || _item$images$ === void 0 ? void 0 : _item$images$.url;\n      const imageId = (_item$images$2 = item.images[0]) === null || _item$images$2 === void 0 ? void 0 : _item$images$2.id;\n      const objectFit = ((_item$images$3 = item.images[0]) === null || _item$images$3 === void 0 ? void 0 : _item$images$3.objectFit) || IMG_OBJECT_FIT;\n      const currentSecHeight = (item === null || item === void 0 ? void 0 : (_item$style = item.style) === null || _item$style === void 0 ? void 0 : _item$style.height) || IMG_CONTAINER_DEFAULT_HEIGHT;\n      const id = item.id;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"100%\",\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          justifyContent: \"flex-start\",\n          alignItems: \"center\",\n          // padding: \"5px\",\n          margin: \"0px\",\n          overflow: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...imageContainerStyle,\n            backgroundColor: item.style.backgroundColor,\n            height: `${item.style.height}px`\n          },\n          onClick: e => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight),\n          component: \"div\",\n          id: id,\n          onMouseOver: () => {\n            setImageAnchorEl({\n              buttonId: imageId,\n              containerId: id,\n              value: null\n            });\n          },\n          children: imageSrc ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imageSrc,\n            alt: \"Uploaded\",\n            style: {\n              ...imageStyle,\n              objectFit\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 9\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\",\n              width: \"100%\",\n              height: \"100%\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              justifyContent: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: iconTextStyle,\n              component: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: uploadfile\n                },\n                style: {\n                  display: \"inline-block\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                align: \"center\",\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"600\"\n                },\n                children: translate(\"Upload file\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                fontSize: \"14px\"\n              },\n              children: translate(\"Drag & Drop to upload file\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              align: \"center\",\n              color: \"textSecondary\",\n              sx: {\n                marginTop: \"8px\",\n                fontSize: \"14px\"\n              },\n              children: translate(\"Or\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 10\n            }, this), showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? /*#__PURE__*/_jsxDEV(TextField, {\n              value: imageLink,\n              onChange: e => setImageLink(e.target.value),\n              onKeyDown: handleLinkSubmit,\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 11\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: iconRowStyle,\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    pointerEvents: \"auto\",\n                    cursor: \"pointer\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: hyperlink\n                    },\n                    style: {\n                      color: \"black\",\n                      cursor: \"pointer\",\n                      fontSize: \"32px\",\n                      opacity: \"0.5\",\n                      pointerEvents: \"none\"\n                    },\n                    id: \"hyperlink\",\n                    className: \"qadpt-image-upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 5\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 3\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Coming soon\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {\n                    //setModelOpen(true);\n                  },\n                  dangerouslySetInnerHTML: {\n                    __html: files\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\",\n                    opacity: \"0.5\"\n                  },\n                  id: \"folder\",\n                  className: \"qadpt-image-upload\"\n                  //title=\"Coming Soon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: translate(\"Upload File\"),\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  onClick: event => {\n                    var _document$getElementB2;\n                    event === null || event === void 0 ? void 0 : event.stopPropagation();\n                    (_document$getElementB2 = document.getElementById(\"file-upload\")) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n                  },\n                  id: \"file-upload1\",\n                  className: \"qadpt-image-upload\",\n                  dangerouslySetInnerHTML: {\n                    __html: uploadicon\n                  },\n                  style: {\n                    color: \"black\",\n                    cursor: \"pointer\",\n                    fontSize: \"32px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 14\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"file-upload\",\n                style: {\n                  display: \"none\"\n                },\n                accept: \"image/*\",\n                onChange: handleImageUpload\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 12\n              }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 3000,\n                onClose: closeSnackbar,\n                anchorOrigin: {\n                  vertical: 'bottom',\n                  horizontal: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Alert, {\n                  onClose: closeSnackbar,\n                  severity: snackbarSeverity,\n                  sx: {\n                    width: '100%'\n                  },\n                  children: snackbarMessage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 13\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 12\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 7\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 6\n      }, this);\n    }), Boolean(imageAnchorEl.value) ? /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-imgsec-popover\",\n      id: id,\n      open: open,\n      anchorEl: imageAnchorEl.value,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"20px\",\n          height: \"100%\",\n          padding: \"0 10px\",\n          fontSize: \"12px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\"\n          },\n          children: currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId && currentImageSectionInfo.isImage ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: replaceimageicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: \"12px\",\n              marginLeft: \"5px\",\n              onClick: triggerImageUpload,\n              children: translate(\"Replace Image\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"replace-upload\",\n              style: {\n                display: \"none\"\n              },\n              accept: \"image/*\",\n              onChange: handleReplaceImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-tool-items\",\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: sectionheight\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDecreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            fontSize: \"12px\",\n            children: currentImageSectionInfo.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleIncreaseHeight(currentImageSectionInfo.height),\n                size: \"small\",\n                disabled: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT,\n                sx: {\n                  opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\n                  cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\n                },\n                children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 1\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-tool-items\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSettingsClick,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: Settings\n                  },\n                  style: {\n                    color: \"black\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Popover, {\n              className: \"qadpt-imgset\",\n              open: openSettingsPopover,\n              anchorEl: settingsAnchorEl,\n              onClose: handleCloseSettingsPopover,\n              anchorOrigin: {\n                vertical: \"center\",\n                horizontal: \"right\"\n              },\n              transformOrigin: {\n                vertical: \"center\",\n                horizontal: \"left\"\n              },\n              slotProps: {\n                paper: {\n                  sx: {\n                    mt: 12,\n                    ml: 20,\n                    width: \"205px\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      color: \"rgba(95, 158, 160, 1)\"\n                    },\n                    children: translate(\"Image Properties\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleCloseSettingsPopover,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: CrossIcon\n                      },\n                      style: {\n                        color: \"black\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: translate(\"Coming soon\"),\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    mt: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      sx: {\n                        marginBottom: \"10px\"\n                      },\n                      children: translate(\"Image Actions\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 11\n                    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                      select: true,\n                      fullWidth: true,\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: selectedAction,\n                      onChange: handleActionChange,\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderColor: \"rgba(246, 238, 238, 1)\"\n                        }\n                      },\n                      disabled: true,\n                      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"none\",\n                        children: translate(\"None\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 683,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"specificStep\",\n                        children: translate(\"Specific Step\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 684,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"openUrl\",\n                        children: translate(\"Open URL\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"clickElement\",\n                        children: translate(\"Click Element\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 686,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startTour\",\n                        children: translate(\"Start Tour\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 687,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                        value: \"startMicroSurvey\",\n                        children: translate(\"Start Micro Survey\")\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 688,\n                        columnNumber: 14\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 11\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 10\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: translate(\"Image Formatting\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 1,\n                    mt: 1,\n                    children: [\"Fill\", \"Fit\"].map(item => {\n                      // Get current image's objectFit to determine selected state\n                      const currentContainer = imagesContainer.find(c => c.id === imageAnchorEl.containerId);\n                      const currentImage = currentContainer === null || currentContainer === void 0 ? void 0 : currentContainer.images.find(img => img.id === imageAnchorEl.buttonId);\n                      const currentObjectFit = (currentImage === null || currentImage === void 0 ? void 0 : currentImage.objectFit) || IMG_OBJECT_FIT;\n\n                      // Determine if this button should be selected\n                      const isSelected = item === \"Fill\" && currentObjectFit === \"cover\" || item === \"Fit\" && currentObjectFit === \"contain\";\n                      return /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => toggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item),\n                        variant: \"outlined\",\n                        size: \"small\",\n                        sx: {\n                          width: \"88.5px\",\n                          height: \"41px\",\n                          padding: \"10px 12px\",\n                          gap: \"12px\",\n                          borderRadius: \"6px 6px 6px 6px\",\n                          border: isSelected ? \"1px solid rgba(95, 158, 160, 1)\" : \"1px solid rgba(246, 238, 238, 1)\",\n                          backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\n                          backgroundBlendMode: \"multiply\",\n                          color: \"black\",\n                          \"&:hover\": {\n                            backgroundColor: isSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\"\n                          }\n                        },\n                        children: translate(item)\n                      }, item, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 14\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleBackgroundColorClick,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: selectedColor,\n                  borderRadius: \"100%\",\n                  width: \"20px\",\n                  height: \"20px\",\n                  display: \"inline-block\",\n                  marginTop: \"-3px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCloneImgContainer,\n              size: \"small\",\n              disabled: isCloneDisabled,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Section\"),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-tool-items\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDeleteSection,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  marginTop: \"-3px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 5\n    }, this) : null, isModelOpen && /*#__PURE__*/_jsxDEV(SelectImageFromApplication, {\n      isOpen: isModelOpen,\n      handleModelClose: () => setModelOpen(false),\n      onImageSelect: handleImageUploadFormApp,\n      setFormOfUpload: setFormOfUpload,\n      formOfUpload: formOfUpload,\n      handleReplaceImage: handleReplaceImage,\n      isReplaceImage: isReplaceImage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 6\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 8\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ImageSection, \"zQV2WXU8rCqzdvJ5fOh8C4lBU9c=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ImageSection;\nexport default ImageSection;\nvar _c;\n$RefreshReg$(_c, \"ImageSection\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "RemoveIcon", "AddIcon", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "SelectImageFromApplication", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSection", "setImageSrc", "setImageName", "onDelete", "onClone", "isCloneDisabled", "_s", "_imagesContainer$find", "t", "translate", "uploadImage", "imagesContainer", "imageAnchorEl", "setImageAnchorEl", "replaceImage", "cloneImageContainer", "deleteImageContainer", "updateImageContainer", "toggleFit", "storeImageSrc", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isReplaceImage", "setReplaceImage", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "_event$target$files2", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "containerId", "altText", "id", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "buttonId", "FileName", "handleReplaceImage", "_event$target$files3", "handleClick", "imageId", "currentHeight", "includes", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "currentContainerColor", "find", "item", "style", "handleDeleteSection", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "children", "map", "_item$images$", "_item$images$2", "_item$images$3", "_item$style", "imageSrc", "images", "currentSecHeight", "sx", "onClick", "e", "component", "onMouseOver", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "className", "_document$getElementB2", "stopPropagation", "type", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "anchorEl", "transform<PERSON><PERSON>in", "marginLeft", "size", "disabled", "slotProps", "paper", "mt", "ml", "p", "marginBottom", "select", "fullWidth", "borderColor", "currentC<PERSON><PERSON>", "c", "currentImage", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "handleModelClose", "onImageSelect", "_c", "$RefreshReg$"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Imagesection.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n\t<PERSON>,\r\n\tTypo<PERSON>,\r\n\t<PERSON>over,\r\n\tIconButton,\r\n\tTextField,\r\n\tMenuItem,\r\n\tButton,\r\n\tTooltip,\r\n\tSnackbar,\r\n\tAlert\r\n} from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n} from \"../../../store/drawerStore\";\r\nimport { Chrome<PERSON>icker, ColorResult } from \"react-color\";\r\nimport \"./PopupSections.css\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\n\r\nconst ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadImage,\r\n\t\timagesContainer,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceImage,\r\n\t\tcloneImageContainer,\r\n\t\tdeleteImageContainer,\r\n\t\tupdateImageContainer,\r\n\t\ttoggleFit,\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\r\n\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\r\n\tconst openSnackbar = () => {\r\n\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\tsetSnackbarOpen(true);\r\n\t};\r\n\tconst closeSnackbar = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\r\n   \t\t // Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\tsetImageSrc(base64Image);\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tsetImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t\t// setModelOpen(true);\r\n\t\t// setReplaceImage(true);\r\n\t};\r\n\r\n\tconst currentContainerColor =\r\n\t\timagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || \"transparent\";\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Call the delete function from the store\r\n\t\tdeleteImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\t\t// Check if cloning is disabled due to section limits\r\n\t\tif (isCloneDisabled) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\r\n\t\t// Call the clone function from the store\r\n\t\tcloneImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onClone callback if provided\r\n\t\tif (onClone) {\r\n\t\t\tonClone();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{imagesContainer.map((item) => {\r\n\t\t\t\tconst imageSrc = item.images[0]?.url;\r\n\t\t\t\tconst imageId = item.images[0]?.id;\r\n\t\t\t\tconst objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\tconst currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\tconst id = item.id;\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tkey={id}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t// padding: \"5px\",\r\n\t\t\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\theight: `${item.style.height}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageSrc ? (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n  <div style={{ pointerEvents: \"auto\", cursor:\"pointer\"}}>\r\n    <span\r\n      dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n      style={{\r\n        color: \"black\",\r\n        cursor: \"pointer\",\r\n        fontSize: \"32px\",\r\n        opacity: \"0.5\",\r\n        pointerEvents: \"none\",\r\n      }}\r\n      id=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n    />\r\n  </div>\r\n</Tooltip>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\")}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(\"file-upload\")?.click();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t{Boolean(imageAnchorEl.value) ? (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tclassName=\"qadpt-imgsec-popover\"\r\n\t\t\t\t\tid={id}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\tanchorEl={imageAnchorEl.value}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"20px\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box sx={{ display: \"flex\" }}>\r\n\t\t\t\t\t\t\t{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&\r\n\t\t\t\t\t\t\tcurrentImageSectionInfo.isImage ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tfontSize=\"12px\"\r\n\t\t\t\t\t\t\t\t\t\tmarginLeft={\"5px\"}\r\n\t\t\t\t\t\t\t\t\t\tonClick={triggerImageUpload}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleReplaceImage}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\r\n<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\")}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Settings\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-imgset\"\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={settingsAnchorEl}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"right\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tmt: 12,\r\n\t\t\t\t\t\t\t\t\t\tml: 20,\r\n\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")}>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as \"Fit\" | \"Fill\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t    display: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\tmarginTop:\"-3px\"\r\n\t\t\t\t\t\t\t\t}} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\")}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Section\")}>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} style={{ marginTop: \"-3px\" }}/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t) : null}\r\n\t\t\t{\r\n\t\t\t\tisModelOpen && (\r\n\t\t\t\t\t<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<style>\r\n    {`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n  </style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAeC,QAAQ,QAAQ,OAAO;AAClD,SACCC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACC,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,cAAc,QAAQ,eAAe;AAI9C,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,SAAS,QACH,6BAA6B;AACpC,OAAOC,cAAc,IACpBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,wBAAwB,EACxBC,cAAc,EACdC,cAAc,QACR,4BAA4B;AACnC,SAASC,YAAY,QAAqB,aAAa;AACvD,OAAO,qBAAqB;AAC5B,OAAOC,0BAA0B,MAAM,yCAAyC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjF,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,OAAO;EAAEC;AAAqB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChG,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGhC,cAAc,CAAC,CAAC;EACzC,MAAM;IACLiC,WAAW;IACXC,eAAe;IACfC,aAAa;IACbC,gBAAgB;IAChBC,YAAY;IACZC,mBAAmB;IACnBC,oBAAoB;IACpBC,oBAAoB;IACpBC,SAAS;IACTjB,WAAW,EAAEkB;EACd,CAAC,GAAG/B,cAAc,CAAEgC,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9D,QAAQ,CAA2C,MAAM,CAAC;EAE1G,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAS,CAAC,CAAC;EAEzD,MAAMiE,YAAY,GAAGA,CAAA,KAAM;IAC1BD,cAAc,CAACE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAChCR,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC3BT,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAACU,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAkD;IAC7GsE,kBAAkB,EAAE,EAAE;IACtBC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC0E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3E,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC4E,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG7E,QAAQ,CAInE;IAAEsE,kBAAkB,EAAE,EAAE;IAAEQ,OAAO,EAAE,KAAK;IAAEC,MAAM,EAAEtD;EAA6B,CAAC,CAAC;EAEpF,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAACkF,WAAW,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAqB,IAAI,CAAC;EAClF,MAAM,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAAC0F,cAAc,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EAGzD,MAAM4F,mBAAmB,GAAGC,OAAO,CAACP,gBAAgB,CAAC;EAErD,MAAMQ,kBAAkB,GAAIC,KAAU,IAAK;IAC1Cd,iBAAiB,CAACc,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACtC,CAAC;EAED,MAAMC,mBAAmB,GAAIH,KAAoC,IAAK;IACrER,mBAAmB,CAACQ,KAAK,CAACI,aAAa,CAAC;EACzC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACxCb,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMc,mBAAwC,GAAG;IAChDC,KAAK,EAAE,MAAM;IACbvB,MAAM,EAAE,MAAM;IACdwB,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCP,KAAK,EAAE,MAAM;IACbvB,MAAM,EAAE,MAAM;IACd4B,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVI,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCR,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBQ,GAAG,EAAE,MAAM;IACXC,SAAS,EAAE;EACZ,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1CX,OAAO,EAAE,MAAM;IACfY,aAAa,EAAE,QAAQ;IACvBV,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBF,KAAK,EAAE;EACR,CAAC;EAED,MAAMc,iBAAiB,GAAIrB,KAA0C,IAAK;IAAA,IAAAsB,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGtB,KAAK,CAACC,MAAM,CAAChF,KAAK,cAAAqG,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MAAA,IAAAC,oBAAA;MACT,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMC,SAAS,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAC;;MAE7B;MACC,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACF,SAAS,EAAG;QACvC/D,kBAAkB,CAAC,6DAA6D,CAAC;QAC5EE,mBAAmB,CAAC,OAAO,CAAC;QAClCJ,eAAe,CAAC,IAAI,CAAC;QACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MAEF;MACH,IAAGqB,IAAI,CAACG,IAAI,CAACI,MAAM,GAAG,GAAG,EAAC;QAC1BjE,kBAAkB,CAAC,4CAA4C,CAAC;QAC1DE,mBAAmB,CAAC,OAAO,CAAC;QACjCJ,eAAe,CAAC,IAAI,CAAC;QACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE;QAClB;MACN;MACD3D,YAAY,EAAAiF,oBAAA,GAACxB,KAAK,CAACC,MAAM,CAAChF,KAAK,cAAAuG,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACE,IAAI,CAAC;MAE1C,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB,MAAMC,WAAW,GAAGH,MAAM,CAACI,MAAgB;QAC3C3E,aAAa,CAAC0E,WAAW,CAAC;QAC1B5F,WAAW,CAAC4F,WAAW,CAAC;QACxBnF,WAAW,CAACE,aAAa,CAACmF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UACvBC,GAAG,EAAEP,WAAW;UAChBQ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE9G;QACZ,CAAC,CAAC;MACH,CAAC;MACDkG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;IACAnC,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMyD,wBAAwB,GAAItB,IAAgB,IAAK;IACtD,IAAIA,IAAI,EAAE;MACT/D,aAAa,CAAC+D,IAAI,CAACuB,GAAG,CAAC;MACvBxG,WAAW,CAACiF,IAAI,CAACuB,GAAG,CAAC;MACrB,IAAInD,cAAc,EAAE;QACnBxC,YAAY,CAACF,aAAa,CAACmF,WAAW,EAAEnF,aAAa,CAAC8F,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAErF,aAAa,CAAC8F,QAAQ;UAC1BN,GAAG,EAAElB,IAAI,CAACuB,GAAG;UACbJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE9G;QACZ,CAAC,CAAC;QACF+D,eAAe,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACN7C,WAAW,CAACE,aAAa,CAACmF,WAAW,EAAE;UACtCC,OAAO,EAAEd,IAAI,CAACyB,QAAQ;UACtBV,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;UAAE;UACzBC,GAAG,EAAElB,IAAI,CAACuB,GAAG;UAAE;UACfJ,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE9G;QACZ,CAAC,CAAC;MACH;IACD;IACAuD,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EACD,MAAM6D,kBAAkB,GAAIjD,KAA0C,IAAK;IAAA,IAAAkD,oBAAA;IAC1E,MAAM3B,IAAI,IAAA2B,oBAAA,GAAGlD,KAAK,CAACC,MAAM,CAAChF,KAAK,cAAAiI,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI3B,IAAI,EAAE;MACT,MAAMQ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB9E,YAAY,CAACF,aAAa,CAACmF,WAAW,EAAEnF,aAAa,CAAC8F,QAAQ,EAAE;UAC/DV,OAAO,EAAEd,IAAI,CAACG,IAAI;UAClBY,EAAE,EAAErF,aAAa,CAAC8F,QAAQ;UAC1BN,GAAG,EAAEV,MAAM,CAACI,MAAM;UAClBO,eAAe,EAAE,SAAS;UAC1BC,SAAS,EAAE9G;QACZ,CAAC,CAAC;MACH,CAAC;MACDkG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAM4B,WAAW,GAAGA,CACnBnD,KAAoC,EACpCoC,WAAmB,EACnBgB,OAAe,EACfrE,OAAgB,EAChBsE,aAAqB,KACjB;IACJ;IACA,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACqC,EAAE,CAAC,EAAE;IAC5DpF,gBAAgB,CAAC;MAChB6F,QAAQ,EAAEK,OAAO;MACjBhB,WAAW,EAAEA,WAAW;MACxB;MACAlC,KAAK,EAAEF,KAAK,CAACI;IACd,CAAC,CAAC;IACFZ,mBAAmB,CAAC,IAAI,CAAC;IACzBV,0BAA0B,CAAC;MAC1BP,kBAAkB,EAAE6D,WAAW;MAC/BrD,OAAO;MACPC,MAAM,EAAEqE;IACT,CAAC,CAAC;IACF/E,qBAAqB,CAAC;MACrBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE;IACT,CAAC,CAAC;EACH,CAAC;EAED,MAAM+E,WAAW,GAAGA,CAAA,KAAM;IACzBrG,gBAAgB,CAAC;MAChB6F,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC;EAED,MAAMsD,IAAI,GAAG1D,OAAO,CAAC7C,aAAa,CAACiD,KAAK,CAAC;EACzC,MAAMuD,eAAe,GAAG3D,OAAO,CAACnB,mBAAmB,CAAC;EAEpD,MAAM2D,EAAE,GAAGkB,IAAI,GAAG,eAAe,GAAGE,SAAS;EAE7C,MAAMC,oBAAoB,GAAIC,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIjI,wBAAwB,EAAE;IAC5C,MAAMkI,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,GAAG9H,cAAc,EAAEH,wBAAwB,CAAC;IACjF2B,oBAAoB,CAACL,aAAa,CAACmF,WAAW,EAAE,OAAO,EAAE;MACxDpD,MAAM,EAAE6E;IACT,CAAC,CAAC;IACF/E,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAE6E;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMG,oBAAoB,GAAIJ,UAAkB,IAAK;IACpD,IAAIA,UAAU,IAAIhI,wBAAwB,EAAE;IAC5C,MAAMiI,SAAS,GAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,GAAG9H,cAAc,EAAEF,wBAAwB,CAAC;IACjF0B,oBAAoB,CAACL,aAAa,CAACmF,WAAW,EAAE,OAAO,EAAE;MACxDpD,MAAM,EAAE6E;IACT,CAAC,CAAC;IACF/E,0BAA0B,CAAEX,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAE6E;IAAU,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;IAClD;IACA;EACD,CAAC;EAED,MAAMC,qBAAqB,GAC1B,EAAA3H,qBAAA,GAAAI,eAAe,CAACwH,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAKrF,aAAa,CAACmF,WAAW,CAAC,cAAAxF,qBAAA,uBAArEA,qBAAA,CAAuE8H,KAAK,CAAChC,eAAe,KAAI,aAAa;EAC9G;EACA,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IACjCzH,gBAAgB,CAAC;MAChB6F,QAAQ,EAAE,EAAE;MACZX,WAAW,EAAE,EAAE;MACf;MACAlC,KAAK,EAAE;IACR,CAAC,CAAC;;IAEF;IACA7C,oBAAoB,CAACJ,aAAa,CAACmF,WAAW,CAAC;;IAE/C;IACA,IAAI5F,QAAQ,EAAE;MACbA,QAAQ,CAAC,CAAC;IACX;EACD,CAAC;EAGD,MAAMoI,gBAAgB,GAAI5E,KAA4C,IAAK;IAC1E,IAAIA,KAAK,CAAC6E,GAAG,KAAK,OAAO,IAAIpG,SAAS,EAAE;MACvC1B,WAAW,CAACE,aAAa,CAACmF,WAAW,EAAE;QACtCC,OAAO,EAAE,WAAW;QACpBC,EAAE,EAAEC,MAAM,CAACC,UAAU,CAAC,CAAC;QACvBC,GAAG,EAAEhE,SAAS;QACdiE,eAAe,EAAE,aAAa;QAC9BC,SAAS,EAAE9G;MACZ,CAAC,CAAC;MACFyC,qBAAqB,CAAC;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE;MACT,CAAC,CAAC;IACH;EACD,CAAC;EAED,MAAMsG,uBAAuB,GAAGA,CAAA,KAAM;IACrC;IACA,IAAIpI,eAAe,EAAE;MACpB,OAAO,CAAC;IACT;;IAEA;IACAU,mBAAmB,CAACH,aAAa,CAACmF,WAAW,CAAC;;IAE9C;IACA,IAAI3F,OAAO,EAAE;MACZA,OAAO,CAAC,CAAC;IACV;EACD,CAAC;EAED,MAAMsI,sBAAsB,GAAGA,CAAA,KAAM;IACpCnG,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMoG,iBAAiB,GAAIC,KAAkB,IAAK;IACjDvF,gBAAgB,CAACuF,KAAK,CAACC,GAAG,CAAC;IAC3B5H,oBAAoB,CAACL,aAAa,CAACmF,WAAW,EAAE,OAAO,EAAE;MACxDM,eAAe,EAAEuC,KAAK,CAACC;IACxB,CAAC,CAAC;EACH,CAAC;EAED,MAAMC,0BAA0B,GAAInF,KAAoC,IAAK;IAC5EpB,sBAAsB,CAACoB,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;EAED,oBACClE,OAAA,CAAAE,SAAA;IAAAgJ,QAAA,GACEpI,eAAe,CAACqI,GAAG,CAAEZ,IAAI,IAAK;MAAA,IAAAa,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,WAAA;MAC9B,MAAMC,QAAQ,IAAAJ,aAAA,GAAGb,IAAI,CAACkB,MAAM,CAAC,CAAC,CAAC,cAAAL,aAAA,uBAAdA,aAAA,CAAgB7C,GAAG;MACpC,MAAMW,OAAO,IAAAmC,cAAA,GAAGd,IAAI,CAACkB,MAAM,CAAC,CAAC,CAAC,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBjD,EAAE;MAClC,MAAMK,SAAS,GAAG,EAAA6C,cAAA,GAAAf,IAAI,CAACkB,MAAM,CAAC,CAAC,CAAC,cAAAH,cAAA,uBAAdA,cAAA,CAAgB7C,SAAS,KAAI9G,cAAc;MAC7D,MAAM+J,gBAAgB,GAAG,CAACnB,IAAI,aAAJA,IAAI,wBAAAgB,WAAA,GAAJhB,IAAI,CAAEC,KAAK,cAAAe,WAAA,uBAAXA,WAAA,CAAazG,MAAM,KAAetD,4BAA4B;MACxF,MAAM4G,EAAE,GAAGmC,IAAI,CAACnC,EAAE;MAClB,oBACCpG,OAAA,CAAChC,GAAG;QAEH2L,EAAE,EAAE;UACHtF,KAAK,EAAE,MAAM;UACbvB,MAAM,EAAE,MAAM;UACdwB,OAAO,EAAE,MAAM;UACfY,aAAa,EAAE,QAAQ;UACvBX,cAAc,EAAE,YAAY;UAC5BC,UAAU,EAAE,QAAQ;UACpB;UACAE,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACX,CAAE;QAAAuE,QAAA,eAEFlJ,OAAA,CAAChC,GAAG;UACH2L,EAAE,EAAE;YACH,GAAGvF,mBAAmB;YACtBoC,eAAe,EAAE+B,IAAI,CAACC,KAAK,CAAChC,eAAe;YAC3C1D,MAAM,EAAE,GAAGyF,IAAI,CAACC,KAAK,CAAC1F,MAAM;UAC7B,CAAE;UACF8G,OAAO,EAAGC,CAAC,IAAK5C,WAAW,CAAC4C,CAAC,EAAEzD,EAAE,EAAEc,OAAO,EAAEsC,QAAQ,GAAG,IAAI,GAAG,KAAK,EAAEE,gBAAgB,CAAE;UACvFI,SAAS,EAAE,KAAM;UACjB1D,EAAE,EAAEA,EAAG;UACP2D,WAAW,EAAEA,CAAA,KAAM;YAClB/I,gBAAgB,CAAC;cAChB6F,QAAQ,EAAEK,OAAO;cACjBhB,WAAW,EAAEE,EAAE;cACfpC,KAAK,EAAE;YACR,CAAC,CAAC;UACH,CAAE;UAAAkF,QAAA,EAEDM,QAAQ,gBACRxJ,OAAA;YACCgK,GAAG,EAAER,QAAS;YACdS,GAAG,EAAC,UAAU;YACdzB,KAAK,EAAE;cAAE,GAAG5D,UAAU;cAAE6B;YAAU;UAAE;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAEFrK,OAAA,CAAChC,GAAG;YACH2L,EAAE,EAAE;cACHW,SAAS,EAAE,QAAQ;cACnBjG,KAAK,EAAE,MAAM;cACbvB,MAAM,EAAE,MAAM;cACdwB,OAAO,EAAE,MAAM;cACfY,aAAa,EAAE,QAAQ;cACvBX,cAAc,EAAE;YACjB,CAAE;YAAA2E,QAAA,gBAEFlJ,OAAA,CAAChC,GAAG;cACH2L,EAAE,EAAE1E,aAAc;cAClB6E,SAAS,EAAE,KAAM;cAAAZ,QAAA,gBAEjBlJ,OAAA;gBACCuK,uBAAuB,EAAE;kBAAEC,MAAM,EAAE3L;gBAAW,CAAE;gBAChD2J,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAe;cAAE;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFrK,OAAA,CAAC/B,UAAU;gBACVwM,OAAO,EAAC,IAAI;gBACZC,KAAK,EAAC,QAAQ;gBACdf,EAAE,EAAE;kBAAEgB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAM,CAAE;gBAAA1B,QAAA,EAE1CtI,SAAS,CAAC,aAAa;cAAC;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENrK,OAAA,CAAC/B,UAAU;cACVwM,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd3B,KAAK,EAAC,eAAe;cACrBY,EAAE,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEvBtI,SAAS,CAAC,4BAA4B;YAAC;cAAAsJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbrK,OAAA,CAAC/B,UAAU;cACVwM,OAAO,EAAC,OAAO;cACfC,KAAK,EAAC,QAAQ;cACd3B,KAAK,EAAC,eAAe;cACrBY,EAAE,EAAE;gBAAE3E,SAAS,EAAE,KAAK;gBAAE2F,QAAQ,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAEzCtI,SAAS,CAAC,IAAI;YAAC;cAAAsJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACZlI,kBAAkB,CAACG,MAAM,IAAIH,kBAAkB,CAACE,kBAAkB,KAAK+D,EAAE,gBACzEpG,OAAA,CAAC5B,SAAS;cACT4F,KAAK,EAAEzB,SAAU;cACjBsI,QAAQ,EAAGhB,CAAC,IAAKrH,YAAY,CAACqH,CAAC,CAAC9F,MAAM,CAACC,KAAK,CAAE;cAC9C8G,SAAS,EAAEpC,gBAAiB;cAC5BqC,SAAS;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,gBAEFrK,OAAA,CAAChC,GAAG;cAAC2L,EAAE,EAAE7E,YAAa;cAAAoE,QAAA,gBACnBlJ,OAAA,CAACzB,OAAO;gBAACyM,KAAK,EAAEpK,SAAS,CAAC,aAAa,CAAE;gBAAAsI,QAAA,eACpDlJ,OAAA;kBAAKwI,KAAK,EAAE;oBAAEyC,aAAa,EAAE,MAAM;oBAAEC,MAAM,EAAC;kBAAS,CAAE;kBAAAhC,QAAA,eACrDlJ,OAAA;oBACEuK,uBAAuB,EAAE;sBAAEC,MAAM,EAAE1L;oBAAU,CAAE;oBAC/C0J,KAAK,EAAE;sBACLO,KAAK,EAAE,OAAO;sBACdmC,MAAM,EAAE,SAAS;sBACjBP,QAAQ,EAAE,MAAM;sBAChBQ,OAAO,EAAE,KAAK;sBACdF,aAAa,EAAE;oBACjB,CAAE;oBACF7E,EAAE,EAAC,WAAW;oBACPgF,SAAS,EAAC;kBAAoB;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGGrK,OAAA,CAACzB,OAAO;gBAACyM,KAAK,EAAEpK,SAAS,CAAC,aAAa,CAAE;gBAAAsI,QAAA,eACxClJ,OAAA;kBACC4J,OAAO,EAAG9F,KAAK,IAAK;oBACnB;kBAAA,CACC;kBACLyG,uBAAuB,EAAE;oBAAEC,MAAM,EAAEzL;kBAAM,CAAE;kBAC3CyJ,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEmC,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE,MAAM;oBAAEQ,OAAO,EAAE;kBAAM,CAAE;kBAC/E/E,EAAE,EAAC,QAAQ;kBACXgF,SAAS,EAAC;kBACV;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACVrK,OAAA,CAACzB,OAAO;gBAACyM,KAAK,EAAEpK,SAAS,CAAC,aAAa,CAAE;gBAAAsI,QAAA,eAC3ClJ,OAAA;kBACI4J,OAAO,EAAG9F,KAAK,IAAK;oBAAA,IAAAuH,sBAAA;oBAEtBvH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwH,eAAe,CAAC,CAAC;oBACxB,CAAAD,sBAAA,GAAAnD,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAkD,sBAAA,uBAAtCA,sBAAA,CAAwCjD,KAAK,CAAC,CAAC;kBAChD,CAAE;kBACFhC,EAAE,EAAC,cAAc;kBACjBgF,SAAS,EAAC,oBAAoB;kBAC9Bb,uBAAuB,EAAE;oBAAEC,MAAM,EAAExL;kBAAW,CAAE;kBAChDwJ,KAAK,EAAE;oBAAEO,KAAK,EAAE,OAAO;oBAAEmC,MAAM,EAAE,SAAS;oBAAEP,QAAQ,EAAE;kBAAO;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACbrK,OAAA;gBACCuL,IAAI,EAAC,MAAM;gBACXnF,EAAE,EAAC,aAAa;gBAChBoC,KAAK,EAAE;kBAAElE,OAAO,EAAE;gBAAO,CAAE;gBAC3BkH,MAAM,EAAC,SAAS;gBAChBX,QAAQ,EAAE1F;cAAkB;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFrK,OAAA,CAACxB,QAAQ;gBAAC8I,IAAI,EAAE9F,YAAa;gBAACiK,gBAAgB,EAAE,IAAK;gBAACC,OAAO,EAAExJ,aAAc;gBAACyJ,YAAY,EAAE;kBAAEC,QAAQ,EAAE,QAAQ;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA3C,QAAA,eACxIlJ,OAAA,CAACvB,KAAK;kBAACiN,OAAO,EAAExJ,aAAc;kBAAC4J,QAAQ,EAAElK,gBAAiB;kBAAC+H,EAAE,EAAE;oBAAEtF,KAAK,EAAE;kBAAO,CAAE;kBAAA6E,QAAA,EAC/ExH;gBAAe;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC,GApJDjE,EAAE;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqJH,CAAC;IAER,CAAC,CAAC,EACDzG,OAAO,CAAC7C,aAAa,CAACiD,KAAK,CAAC,gBAC5BhE,OAAA,CAAC9B,OAAO;MACPkN,SAAS,EAAC,sBAAsB;MAChChF,EAAE,EAAEA,EAAG;MACPkB,IAAI,EAAEA,IAAK;MACXyE,QAAQ,EAAEhL,aAAa,CAACiD,KAAM;MAC9B0H,OAAO,EAAErE,WAAY;MACrBsE,YAAY,EAAE;QACbC,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFG,eAAe,EAAE;QAChBJ,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MAAA3C,QAAA,eAEFlJ,OAAA,CAAChC,GAAG;QACH2L,EAAE,EAAE;UACHrF,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBO,GAAG,EAAE,MAAM;UACXjC,MAAM,EAAE,MAAM;UACd2B,OAAO,EAAE,QAAQ;UACjBkG,QAAQ,EAAE;QACX,CAAE;QAAAzB,QAAA,gBAEFlJ,OAAA,CAAChC,GAAG;UAAC2L,EAAE,EAAE;YAAErF,OAAO,EAAE;UAAO,CAAE;UAAA4E,QAAA,EAC3BvG,uBAAuB,CAACN,kBAAkB,KAAKtB,aAAa,CAACmF,WAAW,IACzEvD,uBAAuB,CAACE,OAAO,gBAC9B7C,OAAA,CAAAE,SAAA;YAAAgJ,QAAA,gBACClJ,OAAA;cAAMuK,uBAAuB,EAAE;gBAAEC,MAAM,EAAEvL;cAAiB;YAAE;cAAAiL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DrK,OAAA,CAAC/B,UAAU;cACV0M,QAAQ,EAAC,MAAM;cACfsB,UAAU,EAAE,KAAM;cAClBrC,OAAO,EAAE5B,kBAAmB;cAAAkB,QAAA,EAE1BtI,SAAS,CAAC,eAAe;YAAC;cAAAsJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACbrK,OAAA;cACCuL,IAAI,EAAC,MAAM;cACXnF,EAAE,EAAC,gBAAgB;cACnBoC,KAAK,EAAE;gBAAElE,OAAO,EAAE;cAAO,CAAE;cAC3BkH,MAAM,EAAC,SAAS;cAChBX,QAAQ,EAAE9D;YAAmB;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA,eACD,CAAC,GACA;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEZrK,OAAA,CAAChC,GAAG;UACGoN,SAAS,EAAC,kBAAkB;UAC5BzB,EAAE,EAAE;YAAErF,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAA0E,QAAA,gBAE9ClJ,OAAA;YAAMuK,uBAAuB,EAAE;cAAEC,MAAM,EAAEpL;YAAc;UAAE;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DrK,OAAA,CAACzB,OAAO;YAACyM,KAAK,EAAErI,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAGkB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAAsI,QAAA,eAC/IlJ,OAAA;cAAAkJ,QAAA,eACClJ,OAAA,CAAC7B,UAAU;gBACVyL,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAACnF,uBAAuB,CAACG,MAAM,CAAE;gBACpEoJ,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAExJ,uBAAuB,CAACG,MAAM,IAAIpD,wBAAyB;gBACrEiK,EAAE,EAAE;kBACHwB,OAAO,EAAExI,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7EwL,MAAM,EAAEvI,uBAAuB,CAACG,MAAM,IAAIpD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAwJ,QAAA,eAEFlJ,OAAA,CAACtB,UAAU;kBAACiM,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACVrK,OAAA,CAAC/B,UAAU;YAAC0M,QAAQ,EAAC,MAAM;YAAAzB,QAAA,EAAEvG,uBAAuB,CAACG;UAAM;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzErK,OAAA,CAACzB,OAAO;YAACyM,KAAK,EAAErI,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAGmB,SAAS,CAAC,wBAAwB,CAAC,GAAGA,SAAS,CAAC,iBAAiB,CAAE;YAAAsI,QAAA,eAC/IlJ,OAAA;cAAAkJ,QAAA,eACClJ,OAAA,CAAC7B,UAAU;gBACVyL,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAAC9E,uBAAuB,CAACG,MAAM,CAAE;gBACpEoJ,IAAI,EAAC,OAAO;gBACZC,QAAQ,EAAExJ,uBAAuB,CAACG,MAAM,IAAIrD,wBAAyB;gBACrEkK,EAAE,EAAE;kBACHwB,OAAO,EAAExI,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,GAAG,GAAG,CAAC;kBAC7EyL,MAAM,EAAEvI,uBAAuB,CAACG,MAAM,IAAIrD,wBAAwB,GAAG,aAAa,GAAG;gBACtF,CAAE;gBAAAyJ,QAAA,eAEFlJ,OAAA,CAACrB,OAAO;kBAACgM,QAAQ,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNrK,OAAA,CAACzB,OAAO;UAACyM,KAAK,EAAEpK,SAAS,CAAC,UAAU,CAAE;UAAAsI,QAAA,eACtClJ,OAAA,CAAChC,GAAG;YAACoN,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,gBAChClJ,OAAA,CAAChC,GAAG;cAACoN,SAAS,EAAC,kBAAkB;cAAAlC,QAAA,eAChClJ,OAAA,CAAC7B,UAAU;gBACV+N,IAAI,EAAC,OAAO;gBACZtC,OAAO,EAAE3F,mBAAoB;gBAAAiF,QAAA,eAE7BlJ,OAAA;kBACCuK,uBAAuB,EAAE;oBAAEC,MAAM,EAAEnL;kBAAS,CAAE;kBAC9CmJ,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAQ;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAELrK,OAAA,CAAC9B,OAAO;cACEkN,SAAS,EAAC,cAAc;cAClC9D,IAAI,EAAE3D,mBAAoB;cAC1BoI,QAAQ,EAAE1I,gBAAiB;cAC3BqI,OAAO,EAAEvH,0BAA2B;cACpCwH,YAAY,EAAE;gBACbC,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACb,CAAE;cACFG,eAAe,EAAE;gBAChBJ,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE;cACb,CAAE;cACFO,SAAS,EAAE;gBACVC,KAAK,EAAE;kBACP1C,EAAE,EAAE;oBACH2C,EAAE,EAAE,EAAE;oBACNC,EAAE,EAAE,EAAE;oBACNlI,KAAK,EAAE;kBACR;gBACA;cACD,CAAE;cAAA6E,QAAA,eAEFlJ,OAAA,CAAChC,GAAG;gBAACwO,CAAC,EAAE,CAAE;gBAAAtD,QAAA,gBACTlJ,OAAA,CAAChC,GAAG;kBACHsG,OAAO,EAAC,MAAM;kBACdC,cAAc,EAAC,eAAe;kBAC9BC,UAAU,EAAC,QAAQ;kBAAA0E,QAAA,gBAEnBlJ,OAAA,CAAC/B,UAAU;oBACVwM,OAAO,EAAC,WAAW;oBACnBd,EAAE,EAAE;sBAAEZ,KAAK,EAAE;oBAAwB,CAAE;oBAAAG,QAAA,EAErCtI,SAAS,CAAC,kBAAkB;kBAAC;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACbrK,OAAA,CAAC7B,UAAU;oBACV+N,IAAI,EAAC,OAAO;oBACZtC,OAAO,EAAEzF,0BAA2B;oBAAA+E,QAAA,eAEpClJ,OAAA;sBACCuK,uBAAuB,EAAE;wBAAEC,MAAM,EAAElL;sBAAU,CAAE;sBAC/CkJ,KAAK,EAAE;wBAAEO,KAAK,EAAE;sBAAQ;oBAAE;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACNrK,OAAA,CAACzB,OAAO;kBAACyM,KAAK,EAAEpK,SAAS,CAAC,aAAa,CAAE;kBAAAsI,QAAA,eAC1ClJ,OAAA,CAAChC,GAAG;oBAACsO,EAAE,EAAE,CAAE;oBAAApD,QAAA,gBACVlJ,OAAA,CAAC/B,UAAU;sBACVwM,OAAO,EAAC,OAAO;sBACb1B,KAAK,EAAC,eAAe;sBACrBY,EAAE,EAAE;wBAAE8C,YAAY,EAAE;sBAAO,CAAE;sBAAAvD,QAAA,EAE5BtI,SAAS,CAAC,eAAe;oBAAC;sBAAAsJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACbrK,OAAA,CAAC5B,SAAS;sBACTsO,MAAM;sBACNC,SAAS;sBACTlC,OAAO,EAAC,UAAU;sBAClByB,IAAI,EAAC,OAAO;sBACZlI,KAAK,EAAEjB,cAAe;sBACtB8H,QAAQ,EAAEhH,kBAAmB;sBAC7B8F,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAC3BiD,WAAW,EAAE;wBACd;sBACD,CAAE;sBACFT,QAAQ;sBAAAjD,QAAA,gBAENlJ,OAAA,CAAC3B,QAAQ;wBAAC2F,KAAK,EAAC,MAAM;wBAAAkF,QAAA,EAAEtI,SAAS,CAAC,MAAM;sBAAC;wBAAAsJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACrDrK,OAAA,CAAC3B,QAAQ;wBAAC2F,KAAK,EAAC,cAAc;wBAAAkF,QAAA,EAAEtI,SAAS,CAAC,eAAe;sBAAC;wBAAAsJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtErK,OAAA,CAAC3B,QAAQ;wBAAC2F,KAAK,EAAC,SAAS;wBAAAkF,QAAA,EAAEtI,SAAS,CAAC,UAAU;sBAAC;wBAAAsJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAC5DrK,OAAA,CAAC3B,QAAQ;wBAAC2F,KAAK,EAAC,cAAc;wBAAAkF,QAAA,EAAEtI,SAAS,CAAC,eAAe;sBAAC;wBAAAsJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eACtErK,OAAA,CAAC3B,QAAQ;wBAAC2F,KAAK,EAAC,WAAW;wBAAAkF,QAAA,EAAEtI,SAAS,CAAC,YAAY;sBAAC;wBAAAsJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC,eAChErK,OAAA,CAAC3B,QAAQ;wBAAC2F,KAAK,EAAC,kBAAkB;wBAAAkF,QAAA,EAAEtI,SAAS,CAAC,oBAAoB;sBAAC;wBAAAsJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAW,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACVrK,OAAA,CAAChC,GAAG;kBAACsO,EAAE,EAAE,CAAE;kBAAApD,QAAA,gBACVlJ,OAAA,CAAC/B,UAAU;oBACVwM,OAAO,EAAC,OAAO;oBACf1B,KAAK,EAAC,eAAe;oBAAAG,QAAA,EAEnBtI,SAAS,CAAC,kBAAkB;kBAAC;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACbrK,OAAA,CAAChC,GAAG;oBACHsG,OAAO,EAAC,MAAM;oBACdS,GAAG,EAAE,CAAE;oBACPuH,EAAE,EAAE,CAAE;oBAAApD,QAAA,EAEL,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,GAAG,CAAEZ,IAAI,IAAK;sBAC9B;sBACA,MAAMsE,gBAAgB,GAAG/L,eAAe,CAACwH,IAAI,CAAEwE,CAAC,IAAKA,CAAC,CAAC1G,EAAE,KAAKrF,aAAa,CAACmF,WAAW,CAAC;sBACxF,MAAM6G,YAAY,GAAGF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEpD,MAAM,CAACnB,IAAI,CAAE0E,GAAG,IAAKA,GAAG,CAAC5G,EAAE,KAAKrF,aAAa,CAAC8F,QAAQ,CAAC;sBAC9F,MAAMoG,gBAAgB,GAAG,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEtG,SAAS,KAAI9G,cAAc;;sBAElE;sBACA,MAAMuN,UAAU,GAAI3E,IAAI,KAAK,MAAM,IAAI0E,gBAAgB,KAAK,OAAO,IAC5D1E,IAAI,KAAK,KAAK,IAAI0E,gBAAgB,KAAK,SAAU;sBAExD,oBACCjN,OAAA,CAAC1B,MAAM;wBAENsL,OAAO,EAAEA,CAAA,KACRvI,SAAS,CAACN,aAAa,CAACmF,WAAW,EAAEnF,aAAa,CAAC8F,QAAQ,EAAE0B,IAAsB,CACnF;wBACDkC,OAAO,EAAC,UAAU;wBAClByB,IAAI,EAAC,OAAO;wBACZvC,EAAE,EAAE;0BACHtF,KAAK,EAAE,QAAQ;0BACfvB,MAAM,EAAE,MAAM;0BACd2B,OAAO,EAAE,WAAW;0BACpBM,GAAG,EAAE,MAAM;0BACXF,YAAY,EAAE,iBAAiB;0BAC/BsI,MAAM,EACLD,UAAU,GACP,iCAAiC,GACjC,kCAAkC;0BACtC1G,eAAe,EACd0G,UAAU,GAAG,yBAAyB,GAAG,0BAA0B;0BACpEE,mBAAmB,EAAE,UAAU;0BAC/BrE,KAAK,EAAE,OAAO;0BACd,SAAS,EAAE;4BACVvC,eAAe,EACd0G,UAAU,GAAG,yBAAyB,GAAG;0BAC3C;wBACD,CAAE;wBAAAhE,QAAA,EAEDtI,SAAS,CAAC2H,IAAI;sBAAC,GA1BXA,IAAI;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BF,CAAC;oBAEX,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACVrK,OAAA,CAACzB,OAAO;UAACyM,KAAK,EAAEpK,SAAS,CAAC,kBAAkB,CAAE;UAAAsI,QAAA,eAC9ClJ,OAAA,CAAChC,GAAG;YAACoN,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChClJ,OAAA,CAAC7B,UAAU;cACVyL,OAAO,EAAEX,0BAA2B;cACpCiD,IAAI,EAAC,OAAO;cAAAhD,QAAA,eAEZlJ,OAAA;gBACAwI,KAAK,EAAE;kBACNhC,eAAe,EAAEjD,aAAa;kBAC9BsB,YAAY,EAAE,MAAM;kBACpBR,KAAK,EAAE,MAAM;kBACbvB,MAAM,EAAE,MAAM;kBACXwB,OAAO,EAAE,cAAc;kBAC1BU,SAAS,EAAC;gBACX;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACVrK,OAAA,CAACzB,OAAO;UAACyM,KAAK,EAAExK,eAAe,GAAGI,SAAS,CAAC,2CAA2C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;UAAAsI,QAAA,eACtHlJ,OAAA,CAAChC,GAAG;YAACoN,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChClJ,OAAA,CAAC7B,UAAU;cACVyL,OAAO,EAAEhB,uBAAwB;cACjCsD,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAE3L,eAAgB;cAAA0I,QAAA,eAE1BlJ,OAAA;gBACCuK,uBAAuB,EAAE;kBAAEC,MAAM,EAAEtL;gBAAS,CAAE;gBAC9CsJ,KAAK,EAAE;kBAAE2C,OAAO,EAAE3K,eAAe,GAAG,GAAG,GAAG;gBAAE;cAAE;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAER;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACVrK,OAAA,CAACzB,OAAO;UAACyM,KAAK,EAAEpK,SAAS,CAAC,gBAAgB,CAAE;UAAAsI,QAAA,eAE5ClJ,OAAA,CAAChC,GAAG;YAACoN,SAAS,EAAC,kBAAkB;YAAAlC,QAAA,eAChClJ,OAAA,CAAC7B,UAAU;cACVyL,OAAO,EAAEnB,mBAAoB;cAC7ByD,IAAI,EAAC,OAAO;cAAAhD,QAAA,eAEZlJ,OAAA;gBAAMuK,uBAAuB,EAAE;kBAAEC,MAAM,EAAErL;gBAAW,CAAE;gBAACqJ,KAAK,EAAE;kBAAExD,SAAS,EAAE;gBAAO;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACP,IAAI,EAEPpH,WAAW,iBACVjD,OAAA,CAACF,0BAA0B;MAACwC,MAAM,EAAEW,WAAY;MAACoK,gBAAgB,EAAEA,CAAA,KAAMnK,YAAY,CAAC,KAAK,CAAE;MAACoK,aAAa,EAAE3G,wBAAyB;MAACvD,eAAe,EAAEA,eAAgB;MAACD,YAAY,EAAEA,YAAa;MAAC4D,kBAAkB,EAAEA,kBAAmB;MAACtD,cAAc,EAAEA;IAAe;MAAAyG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAC7Q,eAEFrK,OAAA,CAAC9B,OAAO;MACPoJ,IAAI,EAAEC,eAAgB;MACtBwE,QAAQ,EAAEtJ,mBAAoB;MAC9BiJ,OAAO,EAAE7C,sBAAuB;MAChC8C,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFG,eAAe,EAAE;QAChBJ,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAA3C,QAAA,eAEFlJ,OAAA,CAAChC,GAAG;QAAAkL,QAAA,gBACHlJ,OAAA,CAACH,YAAY;UACZkJ,KAAK,EAAEV,qBAAsB;UAC7BwC,QAAQ,EAAE/B;QAAkB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACArK,OAAA;UAAAkJ,QAAA,EACF;AACL;AACA;AACA;AACA;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAAC5J,EAAA,CAtxBIN,YAAY;EAAA,QACQvB,cAAc,EAYnCW,cAAc;AAAA;AAAAgO,EAAA,GAbbpN,YAAY;AAwxBlB,eAAeA,YAAY;AAAC,IAAAoN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}