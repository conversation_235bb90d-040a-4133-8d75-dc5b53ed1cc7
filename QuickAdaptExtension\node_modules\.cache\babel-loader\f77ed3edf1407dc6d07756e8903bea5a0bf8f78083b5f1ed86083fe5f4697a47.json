{"ast": null, "code": "import React,{useState}from\"react\";import{<PERSON>,Typography,Popover,IconButton,Dialog}from\"@mui/material\";import RemoveIcon from\"@mui/icons-material/Remove\";import AddIcon from\"@mui/icons-material/Add\";import SettingsIcon from\"@mui/icons-material/Settings\";import{uploadfile,hyperlink,files,uploadicon,replaceimageicon,galleryicon,copyicon,deleteicon,sectionheight}from\"../../../assets/icons/icons\";import ImageGalleryPopup from\"./ImageGalleryPopup\";import ImageProperties from\"./ImageProperties\";//import ImagePageInteractions from \"./PageInteraction\";\nimport\"../guideBanner.css\";import{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ImageSectionField=_ref=>{let{setImageSrc,imageSrc,setImageName}=_ref;const{t:translate}=useTranslation();const[anchorEl,setAnchorEl]=useState(null);const[imageHeight,setImageHeight]=useState(70);const[showSection,setShowSection]=useState(true);const[isGalleryOpen,setIsGalleryOpen]=useState(false);const[isSettingsOpen,setIsSettingsOpen]=useState(false);const containerStyle={width:\"100%\",display:\"flex\",flexDirection:\"row\",justifyContent:\"flex-start\",alignItems:\"center\",padding:0,margin:0,overflow:\"hidden\"};const imageContainerStyle={width:\"100%\",height:`${imageHeight}px`,display:\"flex\",justifyContent:\"center\",alignItems:\"center\",padding:0,margin:0,overflow:\"hidden\",backgroundColor:\"#f0f0f0\"};const imageStyle={width:\"100%\",height:\"100%\",objectFit:\"cover\",margin:0,padding:0,borderRadius:\"0\"};const iconRowStyle={display:\"flex\",justifyContent:\"center\",gap:\"16px\"};const iconTextStyle={display:\"flex\",flexDirection:\"row\",alignItems:\"center\",justifyContent:\"center\",gap:\"8px\",width:\"100%\"};// const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n// \tconst file = event.target.files?.[0];\n// \tif (file) {\n// \t\tconst reader = new FileReader();\n// \t\treader.onloadend = () => {\n// \t\t\t// Use reader.result directly, including the base64 prefix\n// \t\t\tconst base64String = reader.result as string;\n// \t\t\tsetImageSrc(base64String); // Set the full base64 string including the prefix\n// \t\t\tsetImageName(file.name);\n// \t\t};\n// \t\treader.readAsDataURL(file); // This will automatically include the correct prefix\n// \t}\n// };\nconst handleImageUpload=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(file){var _event$target$files2;setImageName((_event$target$files2=event.target.files)===null||_event$target$files2===void 0?void 0:_event$target$files2[0].name);const reader=new FileReader();reader.onloadend=()=>{setImageSrc(reader.result);};reader.readAsDataURL(file);}};const handleClick=event=>{setAnchorEl(event.currentTarget);};const handleClose=()=>{setAnchorEl(null);};const open=Boolean(anchorEl);const id=open?\"image-popover\":undefined;const handleIncreaseHeight=()=>{setImageHeight(prevHeight=>prevHeight+5);};const handleDecreaseHeight=()=>{setImageHeight(prevHeight=>prevHeight>10?prevHeight-5:prevHeight);};const triggerImageUpload=()=>{var _document$getElementB;(_document$getElementB=document.getElementById(\"replace-upload\"))===null||_document$getElementB===void 0?void 0:_document$getElementB.click();};// Function to delete the section\nconst handleDeleteSection=()=>{setShowSection(false);// Hide the section by updating the state\n};const handleOpenGallery=()=>{setIsGalleryOpen(true);};const handleCloseGallery=()=>{setIsGalleryOpen(false);};const handleOpenSettings=()=>{setIsSettingsOpen(true);};const handleCloseSettings=()=>{setIsSettingsOpen(false);};return/*#__PURE__*/_jsx(_Fragment,{children:showSection&&/*#__PURE__*/_jsxs(Box,{sx:containerStyle,children:[/*#__PURE__*/_jsx(Box,{className:\"qadpt-imageupload\",onClick:handleClick,children:imageSrc?/*#__PURE__*/_jsx(\"img\",{src:imageSrc,alt:\"Uploaded\"}):/*#__PURE__*/_jsxs(Box,{className:\"upload-container\",children:[/*#__PURE__*/_jsxs(Box,{className:\"icon-text\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:uploadfile}}),/*#__PURE__*/_jsx(Typography,{align:\"center\",children:translate(\"Upload file\",{defaultValue:\"Upload file\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"icon-row\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:hyperlink}}),/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:files}}),/*#__PURE__*/_jsx(\"span\",{onClick:()=>{var _document$getElementB2;return(_document$getElementB2=document.getElementById(\"file-upload\"))===null||_document$getElementB2===void 0?void 0:_document$getElementB2.click();},dangerouslySetInnerHTML:{__html:uploadicon}}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"file-upload\",accept:\"image/*\",onChange:handleImageUpload})]})]})}),/*#__PURE__*/_jsx(Popover,{id:id,open:open,anchorEl:anchorEl,onClose:handleClose,anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"},PaperProps:{className:\"qadpt-imagepopup\"},children:/*#__PURE__*/_jsxs(Box,{className:\"qadpt-imagepopup-content\",children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-imagepopup-item\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:replaceimageicon}}),/*#__PURE__*/_jsx(Typography,{className:\"qadpt-imagepopup-text\",onClick:triggerImageUpload,children:translate(\"Replace Image\",{defaultValue:\"Replace Image\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"replace-upload\",className:\"qadpt-imagepopup-upload\",accept:\"image/*\",onChange:handleImageUpload})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-imagepopup-item\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:galleryicon}}),/*#__PURE__*/_jsx(Typography,{className:\"qadpt-imagepopup-text\",onClick:handleOpenGallery,children:translate(\"Open Gallery\",{defaultValue:\"Open Gallery\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-imagepopup-item\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:sectionheight}}),/*#__PURE__*/_jsx(IconButton,{onClick:handleDecreaseHeight,size:\"small\",children:/*#__PURE__*/_jsx(RemoveIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(Typography,{className:\"qadpt-imagepopup-text\",children:imageHeight}),/*#__PURE__*/_jsx(IconButton,{onClick:handleIncreaseHeight,size:\"small\",children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\"})})]}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-imagepopup-item\",children:/*#__PURE__*/_jsx(SettingsIcon,{fontSize:\"small\",onClick:handleOpenSettings})}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-imagepopup-item\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon}})}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-imagepopup-item\",onClick:handleDeleteSection,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon}})})]})}),/*#__PURE__*/_jsx(Dialog,{open:isSettingsOpen,onClose:handleCloseSettings,children:/*#__PURE__*/_jsx(ImageProperties,{})}),isGalleryOpen&&/*#__PURE__*/_jsx(ImageGalleryPopup,{open:isGalleryOpen,onClose:handleCloseGallery})]})});};export default ImageSectionField;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "IconButton", "Dialog", "RemoveIcon", "AddIcon", "SettingsIcon", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "galleryicon", "copyicon", "deleteicon", "sectionheight", "ImageGalleryPopup", "ImageProperties", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ImageSectionField", "_ref", "setImageSrc", "imageSrc", "setImageName", "t", "translate", "anchorEl", "setAnchorEl", "imageHeight", "setImageHeight", "showSection", "setShowSection", "isGalleryOpen", "setIsGalleryOpen", "isSettingsOpen", "setIsSettingsOpen", "containerStyle", "width", "display", "flexDirection", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageContainerStyle", "height", "backgroundColor", "imageStyle", "objectFit", "borderRadius", "iconRowStyle", "gap", "iconTextStyle", "handleImageUpload", "event", "_event$target$files", "file", "target", "_event$target$files2", "name", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleClick", "currentTarget", "handleClose", "open", "Boolean", "id", "undefined", "handleIncreaseHeight", "prevHeight", "handleDecreaseHeight", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "handleDeleteSection", "handleOpenGallery", "handleCloseGallery", "handleOpenSettings", "handleCloseSettings", "children", "sx", "className", "onClick", "src", "alt", "dangerouslySetInnerHTML", "__html", "align", "defaultValue", "_document$getElementB2", "type", "accept", "onChange", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "size", "fontSize"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/ImageSectionField.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { <PERSON>, <PERSON>po<PERSON>, Popover, IconButton, Dialog, TextField, MenuItem, Button } from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport SettingsIcon from \"@mui/icons-material/Settings\";\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tgalleryicon,\r\n\tbackgroundcoloricon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n} from \"../../../assets/icons/icons\";\r\nimport ImageGalleryPopup from \"./ImageGalleryPopup\";\r\nimport ImageProperties from \"./ImageProperties\";\r\n//import ImagePageInteractions from \"./PageInteraction\";\r\nimport \"../guideBanner.css\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst ImageSectionField: React.FC<{ setImageSrc: any; imageSrc: any; setImageName: any }> = ({\r\n\tsetImageSrc,\r\n\timageSrc,\r\n\tsetImageName,\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [imageHeight, setImageHeight] = useState<number>(70);\r\n\tconst [showSection, setShowSection] = useState<boolean>(true);\r\n\tconst [isGalleryOpen, setIsGalleryOpen] = useState(false);\r\n\tconst [isSettingsOpen, setIsSettingsOpen] = useState(false);\r\n\r\n\tconst containerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"row\",\r\n\t\tjustifyContent: \"flex-start\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: `${imageHeight}px`,\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t\tbackgroundColor: \"#f0f0f0\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tobjectFit: \"cover\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"row\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"8px\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\t// const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t// \tconst file = event.target.files?.[0];\r\n\t// \tif (file) {\r\n\t// \t\tconst reader = new FileReader();\r\n\t// \t\treader.onloadend = () => {\r\n\t// \t\t\t// Use reader.result directly, including the base64 prefix\r\n\t// \t\t\tconst base64String = reader.result as string;\r\n\t// \t\t\tsetImageSrc(base64String); // Set the full base64 string including the prefix\r\n\t// \t\t\tsetImageName(file.name);\r\n\t// \t\t};\r\n\t// \t\treader.readAsDataURL(file); // This will automatically include the correct prefix\r\n\t// \t}\r\n\t// };\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\r\n\t\tif (file) {\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tsetImageSrc(reader.result as string);\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = () => {\r\n\t\tsetImageHeight((prevHeight) => prevHeight + 5);\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = () => {\r\n\t\tsetImageHeight((prevHeight) => (prevHeight > 10 ? prevHeight - 5 : prevHeight));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t};\r\n\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetShowSection(false); // Hide the section by updating the state\r\n\t};\r\n\tconst handleOpenGallery = () => {\r\n\t\tsetIsGalleryOpen(true);\r\n\t};\r\n\r\n\tconst handleCloseGallery = () => {\r\n\t\tsetIsGalleryOpen(false);\r\n\t};\r\n\r\n\tconst handleOpenSettings = () => {\r\n\t\tsetIsSettingsOpen(true);\r\n\t};\r\n\r\n\tconst handleCloseSettings = () => {\r\n\t\tsetIsSettingsOpen(false);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{showSection && (\r\n\t\t\t\t<Box sx={containerStyle}>\r\n\t\t\t\t\t<Box className=\"qadpt-imageupload\" onClick={handleClick}>\r\n  {imageSrc ? (\r\n    <img src={imageSrc} alt=\"Uploaded\" />\r\n  ) : (\r\n    <Box className=\"upload-container\">\r\n      <Box className=\"icon-text\">\r\n        <span\r\n          dangerouslySetInnerHTML={{ __html: uploadfile }}\r\n        />\r\n        <Typography align=\"center\">\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\", { defaultValue: \"Upload file\" })}\r\n        </Typography>\r\n      </Box>\r\n\r\n      <Box className=\"icon-row\">\r\n        <span\r\n          dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n        />\r\n        <span\r\n          dangerouslySetInnerHTML={{ __html: files }}\r\n        />\r\n        <span\r\n          onClick={() => document.getElementById(\"file-upload\")?.click()}\r\n          dangerouslySetInnerHTML={{ __html: uploadicon }}\r\n        />\r\n        <input\r\n          type=\"file\"\r\n          id=\"file-upload\"\r\n          accept=\"image/*\"\r\n          onChange={handleImageUpload}\r\n        />\r\n      </Box>\r\n    </Box>\r\n  )}\r\n</Box>\r\n\r\n<Popover\r\n\tid={id}\r\n\topen={open}\r\n\tanchorEl={anchorEl}\r\n\tonClose={handleClose}\r\n\tanchorOrigin={{\r\n\t\tvertical: \"bottom\",\r\n\t\thorizontal: \"center\",\r\n\t}}\r\n\ttransformOrigin={{\r\n\t\tvertical: \"bottom\",\r\n\t\thorizontal: \"center\",\r\n\t}}\r\n\tPaperProps={{\r\n\t\tclassName: \"qadpt-imagepopup\",\r\n\t}}\r\n>\r\n\t<Box className=\"qadpt-imagepopup-content\">\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t<Typography className=\"qadpt-imagepopup-text\" onClick={triggerImageUpload}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\", { defaultValue: \"Replace Image\" })}\r\n\t\t\t</Typography>\r\n\t\t\t<input\r\n\t\t\t\ttype=\"file\"\r\n\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\tclassName=\"qadpt-imagepopup-upload\"\r\n\t\t\t\taccept=\"image/*\"\r\n\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t/>\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: galleryicon }} />\r\n\t\t\t<Typography className=\"qadpt-imagepopup-text\" onClick={handleOpenGallery}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Open Gallery\", { defaultValue: \"Open Gallery\" })}\r\n\t\t\t</Typography>\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n\t\t\t<IconButton onClick={handleDecreaseHeight} size=\"small\">\r\n\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t</IconButton>\r\n\t\t\t<Typography className=\"qadpt-imagepopup-text\">{imageHeight}</Typography>\r\n\t\t\t<IconButton onClick={handleIncreaseHeight} size=\"small\">\r\n\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t</IconButton>\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<SettingsIcon fontSize=\"small\" onClick={handleOpenSettings} />\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: copyicon }} />\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\" onClick={handleDeleteSection}>\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t</Box>\r\n\t</Box>\r\n</Popover>\r\n\r\n\t\t\t\t\t{/* Settings Dialog */}\r\n\t\t\t\t\t<Dialog\r\n\t\t\t\t\t\topen={isSettingsOpen}\r\n\t\t\t\t\t\tonClose={handleCloseSettings}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ImageProperties></ImageProperties>\r\n\r\n\t\t\t\t\t\t{/* <Box sx={{ padding: \"20px\", minWidth: \"300px\" }}>\r\n\t\t\t\t\t\t\t<Typography variant=\"h6\">Settings</Typography>\r\n\t\t\t\t\t\t\t{/* Additional Settings Fields \r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tlabel=\"Hyperlink\"\r\n\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tlabel=\"Actions\"\r\n\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Action1\">Action1</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Action2\">Action2</MenuItem>\r\n\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tlabel=\"Formatting\"\r\n\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Format1\">Format1</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Format2\">Format2</MenuItem>\r\n\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t<Box sx={{ marginTop: \"20px\", display: \"flex\", justifyContent: \"flex-end\" }}>\r\n\t\t\t\t\t\t\t\t<Button onClick={handleCloseSettings}>Close</Button>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Box> */}\r\n\t\t\t\t\t</Dialog>\r\n\r\n\t\t\t\t\t{isGalleryOpen && (\r\n\t\t\t\t\t\t<ImageGalleryPopup\r\n\t\t\t\t\t\t\topen={isGalleryOpen}\r\n\t\t\t\t\t\t\tonClose={handleCloseGallery}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSectionField;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,GAAG,CAAEC,UAAU,CAAEC,OAAO,CAAEC,UAAU,CAAEC,MAAM,KAAqC,eAAe,CACzG,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,OACCC,UAAU,CACVC,SAAS,CACTC,KAAK,CACLC,UAAU,CACVC,gBAAgB,CAChBC,WAAW,CAEXC,QAAQ,CACRC,UAAU,CACVC,aAAa,KACP,6BAA6B,CACpC,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CACnD,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C;AACA,MAAO,oBAAoB,CAC3B,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,iBAAmF,CAAGC,IAAA,EAItF,IAJuF,CAC5FC,WAAW,CACXC,QAAQ,CACRC,YACD,CAAC,CAAAH,IAAA,CACA,KAAM,CAAEI,CAAC,CAAEC,SAAU,CAAC,CAAGb,cAAc,CAAC,CAAC,CACzC,KAAM,CAACc,QAAQ,CAAEC,WAAW,CAAC,CAAGnC,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAACoC,WAAW,CAAEC,cAAc,CAAC,CAAGrC,QAAQ,CAAS,EAAE,CAAC,CAC1D,KAAM,CAACsC,WAAW,CAAEC,cAAc,CAAC,CAAGvC,QAAQ,CAAU,IAAI,CAAC,CAC7D,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC0C,cAAc,CAAEC,iBAAiB,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAAA4C,cAAmC,CAAG,CAC3CC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,CAAC,CACVC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QACX,CAAC,CAED,KAAM,CAAAC,mBAAwC,CAAG,CAChDR,KAAK,CAAE,MAAM,CACbS,MAAM,CAAE,GAAGlB,WAAW,IAAI,CAC1BU,OAAO,CAAE,MAAM,CACfE,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,CAAC,CACVC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QAAQ,CAClBG,eAAe,CAAE,SAClB,CAAC,CAED,KAAM,CAAAC,UAA+B,CAAG,CACvCX,KAAK,CAAE,MAAM,CACbS,MAAM,CAAE,MAAM,CACdG,SAAS,CAAE,OAAO,CAClBN,MAAM,CAAE,CAAC,CACTD,OAAO,CAAE,CAAC,CACVQ,YAAY,CAAE,GACf,CAAC,CAED,KAAM,CAAAC,YAAiC,CAAG,CACzCb,OAAO,CAAE,MAAM,CACfE,cAAc,CAAE,QAAQ,CACxBY,GAAG,CAAE,MACN,CAAC,CAED,KAAM,CAAAC,aAAkC,CAAG,CAC1Cf,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBY,GAAG,CAAE,KAAK,CACVf,KAAK,CAAE,MACR,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAAiB,iBAAiB,CAAIC,KAA0C,EAAK,KAAAC,mBAAA,CACzE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGD,KAAK,CAACG,MAAM,CAACvD,KAAK,UAAAqD,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CAEpC,GAAIC,IAAI,CAAE,KAAAE,oBAAA,CACTpC,YAAY,EAAAoC,oBAAA,CAACJ,KAAK,CAACG,MAAM,CAACvD,KAAK,UAAAwD,oBAAA,iBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACC,IAAI,CAAC,CAC1C,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACxB1C,WAAW,CAACwC,MAAM,CAACG,MAAgB,CAAC,CACrC,CAAC,CACDH,MAAM,CAACI,aAAa,CAACR,IAAI,CAAC,CAC3B,CACD,CAAC,CAED,KAAM,CAAAS,WAAW,CAAIX,KAAoC,EAAK,CAC7D5B,WAAW,CAAC4B,KAAK,CAACY,aAAa,CAAC,CACjC,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACzBzC,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,KAAM,CAAA0C,IAAI,CAAGC,OAAO,CAAC5C,QAAQ,CAAC,CAC9B,KAAM,CAAA6C,EAAE,CAAGF,IAAI,CAAG,eAAe,CAAGG,SAAS,CAE7C,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CAClC5C,cAAc,CAAE6C,UAAU,EAAKA,UAAU,CAAG,CAAC,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CAClC9C,cAAc,CAAE6C,UAAU,EAAMA,UAAU,CAAG,EAAE,CAAGA,UAAU,CAAG,CAAC,CAAGA,UAAW,CAAC,CAChF,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAChC,CAAAA,qBAAA,CAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,UAAAF,qBAAA,iBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC,CACnD,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CACjClD,cAAc,CAAC,KAAK,CAAC,CAAE;AACxB,CAAC,CACD,KAAM,CAAAmD,iBAAiB,CAAGA,CAAA,GAAM,CAC/BjD,gBAAgB,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAkD,kBAAkB,CAAGA,CAAA,GAAM,CAChClD,gBAAgB,CAAC,KAAK,CAAC,CACxB,CAAC,CAED,KAAM,CAAAmD,kBAAkB,CAAGA,CAAA,GAAM,CAChCjD,iBAAiB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAkD,mBAAmB,CAAGA,CAAA,GAAM,CACjClD,iBAAiB,CAAC,KAAK,CAAC,CACzB,CAAC,CAED,mBACCrB,IAAA,CAAAI,SAAA,EAAAoE,QAAA,CACExD,WAAW,eACXd,KAAA,CAACvB,GAAG,EAAC8F,EAAE,CAAEnD,cAAe,CAAAkD,QAAA,eACvBxE,IAAA,CAACrB,GAAG,EAAC+F,SAAS,CAAC,mBAAmB,CAACC,OAAO,CAAEvB,WAAY,CAAAoB,QAAA,CAC1DhE,QAAQ,cACPR,IAAA,QAAK4E,GAAG,CAAEpE,QAAS,CAACqE,GAAG,CAAC,UAAU,CAAE,CAAC,cAErC3E,KAAA,CAACvB,GAAG,EAAC+F,SAAS,CAAC,kBAAkB,CAAAF,QAAA,eAC/BtE,KAAA,CAACvB,GAAG,EAAC+F,SAAS,CAAC,WAAW,CAAAF,QAAA,eACxBxE,IAAA,SACE8E,uBAAuB,CAAE,CAAEC,MAAM,CAAE5F,UAAW,CAAE,CACjD,CAAC,cACFa,IAAA,CAACpB,UAAU,EAACoG,KAAK,CAAC,QAAQ,CAAAR,QAAA,CACtB7D,SAAS,CAAC,aAAa,CAAE,CAAEsE,YAAY,CAAE,aAAc,CAAC,CAAC,CACjD,CAAC,EACV,CAAC,cAEN/E,KAAA,CAACvB,GAAG,EAAC+F,SAAS,CAAC,UAAU,CAAAF,QAAA,eACvBxE,IAAA,SACE8E,uBAAuB,CAAE,CAAEC,MAAM,CAAE3F,SAAU,CAAE,CAChD,CAAC,cACFY,IAAA,SACE8E,uBAAuB,CAAE,CAAEC,MAAM,CAAE1F,KAAM,CAAE,CAC5C,CAAC,cACFW,IAAA,SACE2E,OAAO,CAAEA,CAAA,QAAAO,sBAAA,QAAAA,sBAAA,CAAMlB,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,UAAAiB,sBAAA,iBAAtCA,sBAAA,CAAwChB,KAAK,CAAC,CAAC,EAAC,CAC/DY,uBAAuB,CAAE,CAAEC,MAAM,CAAEzF,UAAW,CAAE,CACjD,CAAC,cACFU,IAAA,UACEmF,IAAI,CAAC,MAAM,CACX1B,EAAE,CAAC,aAAa,CAChB2B,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAE7C,iBAAkB,CAC7B,CAAC,EACC,CAAC,EACH,CACN,CACE,CAAC,cAENxC,IAAA,CAACnB,OAAO,EACP4E,EAAE,CAAEA,EAAG,CACPF,IAAI,CAAEA,IAAK,CACX3C,QAAQ,CAAEA,QAAS,CACnB0E,OAAO,CAAEhC,WAAY,CACrBiC,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFE,UAAU,CAAE,CACXjB,SAAS,CAAE,kBACZ,CAAE,CAAAF,QAAA,cAEFtE,KAAA,CAACvB,GAAG,EAAC+F,SAAS,CAAC,0BAA0B,CAAAF,QAAA,eACxCtE,KAAA,CAACvB,GAAG,EAAC+F,SAAS,CAAC,uBAAuB,CAAAF,QAAA,eACrCxE,IAAA,SAAM8E,uBAAuB,CAAE,CAAEC,MAAM,CAAExF,gBAAiB,CAAE,CAAE,CAAC,cAC/DS,IAAA,CAACpB,UAAU,EAAC8F,SAAS,CAAC,uBAAuB,CAACC,OAAO,CAAEb,kBAAmB,CAAAU,QAAA,CACnE7D,SAAS,CAAC,eAAe,CAAE,CAAEsE,YAAY,CAAE,eAAgB,CAAC,CAAC,CACxD,CAAC,cACbjF,IAAA,UACCmF,IAAI,CAAC,MAAM,CACX1B,EAAE,CAAC,gBAAgB,CACnBiB,SAAS,CAAC,yBAAyB,CACnCU,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAE7C,iBAAkB,CAC5B,CAAC,EACE,CAAC,cAENtC,KAAA,CAACvB,GAAG,EAAC+F,SAAS,CAAC,uBAAuB,CAAAF,QAAA,eACrCxE,IAAA,SAAM8E,uBAAuB,CAAE,CAAEC,MAAM,CAAEvF,WAAY,CAAE,CAAE,CAAC,cAC1DQ,IAAA,CAACpB,UAAU,EAAC8F,SAAS,CAAC,uBAAuB,CAACC,OAAO,CAAEP,iBAAkB,CAAAI,QAAA,CAClE7D,SAAS,CAAC,cAAc,CAAE,CAAEsE,YAAY,CAAE,cAAe,CAAC,CAAC,CACtD,CAAC,EACT,CAAC,cAEN/E,KAAA,CAACvB,GAAG,EAAC+F,SAAS,CAAC,uBAAuB,CAAAF,QAAA,eACrCxE,IAAA,SAAM8E,uBAAuB,CAAE,CAAEC,MAAM,CAAEpF,aAAc,CAAE,CAAE,CAAC,cAC5DK,IAAA,CAAClB,UAAU,EAAC6F,OAAO,CAAEd,oBAAqB,CAAC+B,IAAI,CAAC,OAAO,CAAApB,QAAA,cACtDxE,IAAA,CAAChB,UAAU,EAAC6G,QAAQ,CAAC,OAAO,CAAE,CAAC,CACpB,CAAC,cACb7F,IAAA,CAACpB,UAAU,EAAC8F,SAAS,CAAC,uBAAuB,CAAAF,QAAA,CAAE1D,WAAW,CAAa,CAAC,cACxEd,IAAA,CAAClB,UAAU,EAAC6F,OAAO,CAAEhB,oBAAqB,CAACiC,IAAI,CAAC,OAAO,CAAApB,QAAA,cACtDxE,IAAA,CAACf,OAAO,EAAC4G,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjB,CAAC,EACT,CAAC,cAEN7F,IAAA,CAACrB,GAAG,EAAC+F,SAAS,CAAC,uBAAuB,CAAAF,QAAA,cACrCxE,IAAA,CAACd,YAAY,EAAC2G,QAAQ,CAAC,OAAO,CAAClB,OAAO,CAAEL,kBAAmB,CAAE,CAAC,CAC1D,CAAC,cAENtE,IAAA,CAACrB,GAAG,EAAC+F,SAAS,CAAC,uBAAuB,CAAAF,QAAA,cACrCxE,IAAA,SAAM8E,uBAAuB,CAAE,CAAEC,MAAM,CAAEtF,QAAS,CAAE,CAAE,CAAC,CACnD,CAAC,cAENO,IAAA,CAACrB,GAAG,EAAC+F,SAAS,CAAC,uBAAuB,CAACC,OAAO,CAAER,mBAAoB,CAAAK,QAAA,cACnExE,IAAA,SAAM8E,uBAAuB,CAAE,CAAEC,MAAM,CAAErF,UAAW,CAAE,CAAE,CAAC,CACrD,CAAC,EACF,CAAC,CACE,CAAC,cAGLM,IAAA,CAACjB,MAAM,EACNwE,IAAI,CAAEnC,cAAe,CACrBkE,OAAO,CAAEf,mBAAoB,CAAAC,QAAA,cAE7BxE,IAAA,CAACH,eAAe,GAAkB,CAAC,CAgC5B,CAAC,CAERqB,aAAa,eACblB,IAAA,CAACJ,iBAAiB,EACjB2D,IAAI,CAAErC,aAAc,CACpBoE,OAAO,CAAEjB,kBAAmB,CAC5B,CACD,EACG,CACL,CACA,CAAC,CAEL,CAAC,CAED,cAAe,CAAAhE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}