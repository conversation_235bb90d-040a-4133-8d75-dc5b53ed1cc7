{"ast": null, "code": "import React,{useState}from'react';import{Box,Typography,Popover,IconButton}from'@mui/material';import RemoveIcon from'@mui/icons-material/Remove';import AddIcon from'@mui/icons-material/Add';import{uploadfile,hyperlink,uploadicon,replaceimageicon,backgroundcoloricon,copyicon,deleteicon,sectionheight}from'../../../assets/icons/icons';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const VideoSection=()=>{const{t:translate}=useTranslation();const[videoSrc,setVideoSrc]=useState(null);const[anchorEl,setAnchorEl]=useState(null);const[videoHeight,setVideoHeight]=useState(335);const[showSection,setShowSection]=useState(true);const containerStyle={width:'100%',height:'100%',display:'flex',flexDirection:'column',justifyContent:'flex-start',alignItems:'center',padding:0,margin:0,overflow:'hidden'};const videoContainerStyle={width:'100%',height:`${videoHeight}px`,display:'flex',justifyContent:'center',alignItems:'center',padding:0,margin:0,overflow:'hidden',backgroundColor:'#f0f0f0'};const videoStyle={width:'100%',height:'100%',objectFit:'cover',margin:0,padding:0,borderRadius:'0'};const iconRowStyle={display:'flex',justifyContent:'center',gap:'16px'};const iconTextStyle={display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',gap:'8px',width:'100%'};const handleVideoUpload=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(file){const reader=new FileReader();reader.onloadend=()=>{setVideoSrc(reader.result);};reader.readAsDataURL(file);}};const handleClick=event=>{setAnchorEl(event.currentTarget);};const handleClose=()=>{setAnchorEl(null);};const open=Boolean(anchorEl);const id=open?'video-popover':undefined;const handleIncreaseHeight=()=>{setVideoHeight(prevHeight=>prevHeight+10);};const handleDecreaseHeight=()=>{setVideoHeight(prevHeight=>prevHeight>10?prevHeight-10:prevHeight);};const triggerVideoUpload=()=>{var _document$getElementB;(_document$getElementB=document.getElementById('replace-video-upload'))===null||_document$getElementB===void 0?void 0:_document$getElementB.click();};// Function to delete the section\nconst handleDeleteSection=()=>{setShowSection(false);// Hide the section by updating the state\n};return/*#__PURE__*/_jsx(_Fragment,{children:showSection&&/*#__PURE__*/_jsxs(Box,{sx:containerStyle,children:[/*#__PURE__*/_jsx(Box,{sx:videoContainerStyle,onClick:handleClick,children:videoSrc?/*#__PURE__*/_jsx(\"video\",{src:videoSrc,controls:true,style:videoStyle}):/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',width:'100%',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center'},children:[/*#__PURE__*/_jsxs(Box,{sx:iconTextStyle,children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:uploadfile},style:{fontSize:'48px',display:'inline-block'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",align:\"center\",children:translate(\"Upload Video\")})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",align:\"center\",color:\"textSecondary\",children:translate(\"Drag & Drop to upload video\")}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",align:\"center\",color:\"textSecondary\",sx:{marginTop:'8px'},children:translate(\"Or\")}),/*#__PURE__*/_jsxs(Box,{sx:iconRowStyle,children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:hyperlink},style:{color:'black',cursor:'pointer',fontSize:'32px'}}),/*#__PURE__*/_jsx(\"span\",{onClick:()=>{var _document$getElementB2;return(_document$getElementB2=document.getElementById('video-file-upload'))===null||_document$getElementB2===void 0?void 0:_document$getElementB2.click();},dangerouslySetInnerHTML:{__html:uploadicon},style:{color:'black',cursor:'pointer',fontSize:'32px'}}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"video-file-upload\",style:{display:'none'},accept:\"video/*\",onChange:handleVideoUpload})]})]})}),/*#__PURE__*/_jsx(Popover,{id:id,open:open,anchorEl:anchorEl,onClose:handleClose,anchorOrigin:{vertical:'top',horizontal:'center'},transformOrigin:{vertical:'bottom',horizontal:'center'},PaperProps:{style:{height:'44px',width:'500px',marginLeft:'auto',marginRight:'auto',marginTop:'-10px'}},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',height:'100%',paddingLeft:'10px',paddingRight:'10px',fontSize:'12px'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:'8px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:replaceimageicon}}),/*#__PURE__*/_jsx(Typography,{fontSize:\"12px\",onClick:triggerVideoUpload,sx:{cursor:'pointer'},children:translate(\"Replace Video\")}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"replace-video-upload\",style:{display:'none'},accept:\"video/*\",onChange:handleVideoUpload})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:'8px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:sectionheight}}),/*#__PURE__*/_jsx(IconButton,{onClick:handleDecreaseHeight,size:\"small\",children:/*#__PURE__*/_jsx(RemoveIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(Typography,{fontSize:\"12px\",children:videoHeight}),/*#__PURE__*/_jsx(IconButton,{onClick:handleIncreaseHeight,size:\"small\",children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\"})})]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',gap:'8px',fontSize:'12px'},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:backgroundcoloricon}})}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',gap:'8px',fontSize:'12px'},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon}})}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',gap:'8px',fontSize:'12px',cursor:'pointer'},onClick:handleDeleteSection,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon}})})]})})]})});};export default VideoSection;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "IconButton", "RemoveIcon", "AddIcon", "uploadfile", "hyperlink", "uploadicon", "replaceimageicon", "backgroundcoloricon", "copyicon", "deleteicon", "sectionheight", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "VideoSection", "t", "translate", "videoSrc", "setVideoSrc", "anchorEl", "setAnchorEl", "videoHeight", "setVideoHeight", "showSection", "setShowSection", "containerStyle", "width", "height", "display", "flexDirection", "justifyContent", "alignItems", "padding", "margin", "overflow", "videoContainerStyle", "backgroundColor", "videoStyle", "objectFit", "borderRadius", "iconRowStyle", "gap", "iconTextStyle", "handleVideoUpload", "event", "_event$target$files", "file", "target", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleClick", "currentTarget", "handleClose", "open", "Boolean", "id", "undefined", "handleIncreaseHeight", "prevHeight", "handleDecreaseHeight", "triggerVideoUpload", "_document$getElementB", "document", "getElementById", "click", "handleDeleteSection", "children", "sx", "onClick", "src", "controls", "style", "textAlign", "dangerouslySetInnerHTML", "__html", "fontSize", "variant", "align", "color", "marginTop", "cursor", "_document$getElementB2", "type", "accept", "onChange", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "marginLeft", "marginRight", "paddingLeft", "paddingRight", "size"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/VideoSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Box, Typography, Popover, IconButton } from '@mui/material';\r\nimport RemoveIcon from '@mui/icons-material/Remove';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport { uploadfile, hyperlink, uploadicon, replaceimageicon, backgroundcoloricon, copyicon, deleteicon, sectionheight } from  '../../../assets/icons/icons';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst VideoSection: React.FC = () => {\r\n  const { t: translate } = useTranslation();\r\n  const [videoSrc, setVideoSrc] = useState<string | null>(null);\r\n  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n  const [videoHeight, setVideoHeight] = useState<number>(335);\r\n  const [showSection, setShowSection] = useState<boolean>(true);\r\n\r\n  const containerStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    height: '100%',\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    justifyContent: 'flex-start',\r\n    alignItems: 'center',\r\n    padding: 0,\r\n    margin: 0,\r\n    overflow: 'hidden',\r\n  };\r\n\r\n  const videoContainerStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    height: `${videoHeight}px`,\r\n    display: 'flex',\r\n    justifyContent: 'center',\r\n    alignItems: 'center',\r\n    padding: 0,\r\n    margin: 0,\r\n    overflow: 'hidden',\r\n    backgroundColor: '#f0f0f0',\r\n  };\r\n\r\n  const videoStyle: React.CSSProperties = {\r\n    width: '100%',\r\n    height: '100%',\r\n    objectFit: 'cover',\r\n    margin: 0,\r\n    padding: 0,\r\n    borderRadius: '0',\r\n  };\r\n\r\n  const iconRowStyle: React.CSSProperties = {\r\n    display: 'flex',\r\n    justifyContent: 'center',\r\n    gap: '16px',\r\n  };\r\n\r\n  const iconTextStyle: React.CSSProperties = {\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    gap: '8px',\r\n    width: '100%',\r\n  };\r\n\r\n  const handleVideoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setVideoSrc(reader.result as string);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleClick = (event: React.MouseEvent<HTMLElement>) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setAnchorEl(null);\r\n  };\r\n\r\n  const open = Boolean(anchorEl);\r\n  const id = open ? 'video-popover' : undefined;\r\n\r\n  const handleIncreaseHeight = () => {\r\n    setVideoHeight((prevHeight) => prevHeight + 10);\r\n  };\r\n\r\n  const handleDecreaseHeight = () => {\r\n    setVideoHeight((prevHeight) => (prevHeight > 10 ? prevHeight - 10 : prevHeight));\r\n  };\r\n\r\n  const triggerVideoUpload = () => {\r\n    document.getElementById('replace-video-upload')?.click();\r\n  };\r\n\r\n  // Function to delete the section\r\n  const handleDeleteSection = () => {\r\n    setShowSection(false); // Hide the section by updating the state\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showSection && (\r\n        <Box sx={containerStyle}>\r\n          <Box sx={videoContainerStyle} onClick={handleClick}>\r\n            {videoSrc ? (\r\n              <video src={videoSrc} controls style={videoStyle} />\r\n            ) : (\r\n              <Box sx={{ textAlign: 'center', width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\r\n                <Box sx={iconTextStyle}>\r\n                  <span\r\n                    dangerouslySetInnerHTML={{ __html: uploadfile }}\r\n                    style={{ fontSize: '48px', display: 'inline-block' }}\r\n                  />\r\n                  <Typography variant=\"h6\" align=\"center\">\r\n                      {translate(\"Upload Video\")}\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Typography variant=\"body2\" align=\"center\" color=\"textSecondary\">\r\n                    {translate(\"Drag & Drop to upload video\")}\r\n                </Typography>\r\n\r\n                <Typography variant=\"body2\" align=\"center\" color=\"textSecondary\" sx={{ marginTop: '8px' }}>\r\n                    {translate(\"Or\")}\r\n                </Typography>\r\n\r\n                <Box sx={iconRowStyle}>\r\n                  <span\r\n                    dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n                    style={{ color: 'black', cursor: 'pointer', fontSize: '32px' }}\r\n                  />\r\n                  <span\r\n                    onClick={() => document.getElementById('video-file-upload')?.click()}\r\n                    dangerouslySetInnerHTML={{ __html: uploadicon }}\r\n                    style={{ color: 'black', cursor: 'pointer', fontSize: '32px' }}\r\n                  />\r\n                  <input\r\n                    type=\"file\"\r\n                    id=\"video-file-upload\"\r\n                    style={{ display: 'none' }}\r\n                    accept=\"video/*\"\r\n                    onChange={handleVideoUpload}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n          <Popover\r\n            id={id}\r\n            open={open}\r\n            anchorEl={anchorEl}\r\n            onClose={handleClose}\r\n            anchorOrigin={{\r\n              vertical: 'top',\r\n              horizontal: 'center',\r\n            }}\r\n            transformOrigin={{\r\n              vertical: 'bottom',\r\n              horizontal: 'center',\r\n            }}\r\n            PaperProps={{\r\n              style: {\r\n                height: '44px',\r\n                width: '500px',\r\n                marginLeft: 'auto',\r\n                marginRight: 'auto',\r\n                marginTop: '-10px',\r\n              },\r\n            }}\r\n          >\r\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '100%', paddingLeft: '10px', paddingRight: '10px', fontSize: '12px' }}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n                <Typography fontSize=\"12px\" onClick={triggerVideoUpload} sx={{ cursor: 'pointer' }}>\r\n                  {translate(\"Replace Video\")}\r\n                </Typography>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"replace-video-upload\"\r\n                  style={{ display: 'none' }}\r\n                  accept=\"video/*\"\r\n                  onChange={handleVideoUpload}\r\n                />\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n                <IconButton onClick={handleDecreaseHeight} size=\"small\">\r\n                  <RemoveIcon fontSize=\"small\" />\r\n                </IconButton>\r\n                <Typography fontSize=\"12px\">{videoHeight}</Typography>\r\n                <IconButton onClick={handleIncreaseHeight} size=\"small\">\r\n                  <AddIcon fontSize=\"small\" />\r\n                </IconButton>\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: backgroundcoloricon }} />\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>\r\n                <span dangerouslySetInnerHTML={{ __html: copyicon }} />\r\n              </Box>\r\n\r\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px', cursor: 'pointer' }} onClick={handleDeleteSection}>\r\n                <span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n              </Box>\r\n            </Box>\r\n          </Popover>\r\n        </Box>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default VideoSection;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,GAAG,CAAEC,UAAU,CAAEC,OAAO,CAAEC,UAAU,KAAQ,eAAe,CACpE,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,OAASC,UAAU,CAAEC,SAAS,CAAEC,UAAU,CAAEC,gBAAgB,CAAEC,mBAAmB,CAAEC,QAAQ,CAAEC,UAAU,CAAEC,aAAa,KAAS,6BAA6B,CAC5J,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGT,cAAc,CAAC,CAAC,CACzC,KAAM,CAACU,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAgB,IAAI,CAAC,CAC7D,KAAM,CAAC2B,QAAQ,CAAEC,WAAW,CAAC,CAAG5B,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAAC6B,WAAW,CAAEC,cAAc,CAAC,CAAG9B,QAAQ,CAAS,GAAG,CAAC,CAC3D,KAAM,CAAC+B,WAAW,CAAEC,cAAc,CAAC,CAAGhC,QAAQ,CAAU,IAAI,CAAC,CAE7D,KAAM,CAAAiC,cAAmC,CAAG,CAC1CC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,CAAC,CACVC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QACZ,CAAC,CAED,KAAM,CAAAC,mBAAwC,CAAG,CAC/CT,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,GAAGN,WAAW,IAAI,CAC1BO,OAAO,CAAE,MAAM,CACfE,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,CAAC,CACVC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QAAQ,CAClBE,eAAe,CAAE,SACnB,CAAC,CAED,KAAM,CAAAC,UAA+B,CAAG,CACtCX,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdW,SAAS,CAAE,OAAO,CAClBL,MAAM,CAAE,CAAC,CACTD,OAAO,CAAE,CAAC,CACVO,YAAY,CAAE,GAChB,CAAC,CAED,KAAM,CAAAC,YAAiC,CAAG,CACxCZ,OAAO,CAAE,MAAM,CACfE,cAAc,CAAE,QAAQ,CACxBW,GAAG,CAAE,MACP,CAAC,CAED,KAAM,CAAAC,aAAkC,CAAG,CACzCd,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBW,GAAG,CAAE,KAAK,CACVf,KAAK,CAAE,MACT,CAAC,CAED,KAAM,CAAAiB,iBAAiB,CAAIC,KAA0C,EAAK,KAAAC,mBAAA,CACxE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAIC,IAAI,CAAE,CACR,KAAM,CAAAG,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACvBjC,WAAW,CAAC+B,MAAM,CAACG,MAAgB,CAAC,CACtC,CAAC,CACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAQ,WAAW,CAAIV,KAAoC,EAAK,CAC5DxB,WAAW,CAACwB,KAAK,CAACW,aAAa,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxBpC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED,KAAM,CAAAqC,IAAI,CAAGC,OAAO,CAACvC,QAAQ,CAAC,CAC9B,KAAM,CAAAwC,EAAE,CAAGF,IAAI,CAAG,eAAe,CAAGG,SAAS,CAE7C,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjCvC,cAAc,CAAEwC,UAAU,EAAKA,UAAU,CAAG,EAAE,CAAC,CACjD,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjCzC,cAAc,CAAEwC,UAAU,EAAMA,UAAU,CAAG,EAAE,CAAGA,UAAU,CAAG,EAAE,CAAGA,UAAW,CAAC,CAClF,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAC/B,CAAAA,qBAAA,CAAAC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC,UAAAF,qBAAA,iBAA/CA,qBAAA,CAAiDG,KAAK,CAAC,CAAC,CAC1D,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC7C,cAAc,CAAC,KAAK,CAAC,CAAE;AACzB,CAAC,CAED,mBACEf,IAAA,CAAAI,SAAA,EAAAyD,QAAA,CACG/C,WAAW,eACVZ,KAAA,CAAClB,GAAG,EAAC8E,EAAE,CAAE9C,cAAe,CAAA6C,QAAA,eACtB7D,IAAA,CAAChB,GAAG,EAAC8E,EAAE,CAAEpC,mBAAoB,CAACqC,OAAO,CAAElB,WAAY,CAAAgB,QAAA,CAChDrD,QAAQ,cACPR,IAAA,UAAOgE,GAAG,CAAExD,QAAS,CAACyD,QAAQ,MAACC,KAAK,CAAEtC,UAAW,CAAE,CAAC,cAEpD1B,KAAA,CAAClB,GAAG,EAAC8E,EAAE,CAAE,CAAEK,SAAS,CAAE,QAAQ,CAAElD,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAwC,QAAA,eAClI3D,KAAA,CAAClB,GAAG,EAAC8E,EAAE,CAAE7B,aAAc,CAAA4B,QAAA,eACrB7D,IAAA,SACEoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE/E,UAAW,CAAE,CAChD4E,KAAK,CAAE,CAAEI,QAAQ,CAAE,MAAM,CAAEnD,OAAO,CAAE,cAAe,CAAE,CACtD,CAAC,cACFnB,IAAA,CAACf,UAAU,EAACsF,OAAO,CAAC,IAAI,CAACC,KAAK,CAAC,QAAQ,CAAAX,QAAA,CAClCtD,SAAS,CAAC,cAAc,CAAC,CAClB,CAAC,EACV,CAAC,cAENP,IAAA,CAACf,UAAU,EAACsF,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,QAAQ,CAACC,KAAK,CAAC,eAAe,CAAAZ,QAAA,CAC3DtD,SAAS,CAAC,6BAA6B,CAAC,CACjC,CAAC,cAEbP,IAAA,CAACf,UAAU,EAACsF,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,QAAQ,CAACC,KAAK,CAAC,eAAe,CAACX,EAAE,CAAE,CAAEY,SAAS,CAAE,KAAM,CAAE,CAAAb,QAAA,CACrFtD,SAAS,CAAC,IAAI,CAAC,CACR,CAAC,cAEbL,KAAA,CAAClB,GAAG,EAAC8E,EAAE,CAAE/B,YAAa,CAAA8B,QAAA,eACpB7D,IAAA,SACEoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE9E,SAAU,CAAE,CAC/C2E,KAAK,CAAE,CAAEO,KAAK,CAAE,OAAO,CAAEE,MAAM,CAAE,SAAS,CAAEL,QAAQ,CAAE,MAAO,CAAE,CAChE,CAAC,cACFtE,IAAA,SACE+D,OAAO,CAAEA,CAAA,QAAAa,sBAAA,QAAAA,sBAAA,CAAMnB,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,UAAAkB,sBAAA,iBAA5CA,sBAAA,CAA8CjB,KAAK,CAAC,CAAC,EAAC,CACrES,uBAAuB,CAAE,CAAEC,MAAM,CAAE7E,UAAW,CAAE,CAChD0E,KAAK,CAAE,CAAEO,KAAK,CAAE,OAAO,CAAEE,MAAM,CAAE,SAAS,CAAEL,QAAQ,CAAE,MAAO,CAAE,CAChE,CAAC,cACFtE,IAAA,UACE6E,IAAI,CAAC,MAAM,CACX3B,EAAE,CAAC,mBAAmB,CACtBgB,KAAK,CAAE,CAAE/C,OAAO,CAAE,MAAO,CAAE,CAC3B2D,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAE7C,iBAAkB,CAC7B,CAAC,EACC,CAAC,EACH,CACN,CACE,CAAC,cACNlC,IAAA,CAACd,OAAO,EACNgE,EAAE,CAAEA,EAAG,CACPF,IAAI,CAAEA,IAAK,CACXtC,QAAQ,CAAEA,QAAS,CACnBsE,OAAO,CAAEjC,WAAY,CACrBkC,YAAY,CAAE,CACZC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACd,CAAE,CACFE,UAAU,CAAE,CACVnB,KAAK,CAAE,CACLhD,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,OAAO,CACdqE,UAAU,CAAE,MAAM,CAClBC,WAAW,CAAE,MAAM,CACnBb,SAAS,CAAE,OACb,CACF,CAAE,CAAAb,QAAA,cAEF3D,KAAA,CAAClB,GAAG,EAAC8E,EAAE,CAAE,CAAE3C,OAAO,CAAE,MAAM,CAAEE,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEJ,MAAM,CAAE,MAAM,CAAEsE,WAAW,CAAE,MAAM,CAAEC,YAAY,CAAE,MAAM,CAAEnB,QAAQ,CAAE,MAAO,CAAE,CAAAT,QAAA,eAC/J3D,KAAA,CAAClB,GAAG,EAAC8E,EAAE,CAAE,CAAE3C,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAQ,CAAEU,GAAG,CAAE,KAAK,CAAEsC,QAAQ,CAAE,MAAO,CAAE,CAAAT,QAAA,eAC/E7D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE5E,gBAAiB,CAAE,CAAE,CAAC,cAC/DO,IAAA,CAACf,UAAU,EAACqF,QAAQ,CAAC,MAAM,CAACP,OAAO,CAAER,kBAAmB,CAACO,EAAE,CAAE,CAAEa,MAAM,CAAE,SAAU,CAAE,CAAAd,QAAA,CAChFtD,SAAS,CAAC,eAAe,CAAC,CACjB,CAAC,cACbP,IAAA,UACE6E,IAAI,CAAC,MAAM,CACX3B,EAAE,CAAC,sBAAsB,CACzBgB,KAAK,CAAE,CAAE/C,OAAO,CAAE,MAAO,CAAE,CAC3B2D,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAE7C,iBAAkB,CAC7B,CAAC,EACC,CAAC,cAENhC,KAAA,CAAClB,GAAG,EAAC8E,EAAE,CAAE,CAAE3C,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAQ,CAAEU,GAAG,CAAE,KAAK,CAAEsC,QAAQ,CAAE,MAAO,CAAE,CAAAT,QAAA,eAC/E7D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAExE,aAAc,CAAE,CAAE,CAAC,cAC5DG,IAAA,CAACb,UAAU,EAAC4E,OAAO,CAAET,oBAAqB,CAACoC,IAAI,CAAC,OAAO,CAAA7B,QAAA,cACrD7D,IAAA,CAACZ,UAAU,EAACkF,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CAAC,cACbtE,IAAA,CAACf,UAAU,EAACqF,QAAQ,CAAC,MAAM,CAAAT,QAAA,CAAEjD,WAAW,CAAa,CAAC,cACtDZ,IAAA,CAACb,UAAU,EAAC4E,OAAO,CAAEX,oBAAqB,CAACsC,IAAI,CAAC,OAAO,CAAA7B,QAAA,cACrD7D,IAAA,CAACX,OAAO,EAACiF,QAAQ,CAAC,OAAO,CAAE,CAAC,CAClB,CAAC,EACV,CAAC,cAENtE,IAAA,CAAChB,GAAG,EAAC8E,EAAE,CAAE,CAAE3C,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAQ,CAAEU,GAAG,CAAE,KAAK,CAAEsC,QAAQ,CAAE,MAAO,CAAE,CAAAT,QAAA,cAC/E7D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE3E,mBAAoB,CAAE,CAAE,CAAC,CAC/D,CAAC,cAENM,IAAA,CAAChB,GAAG,EAAC8E,EAAE,CAAE,CAAE3C,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAQ,CAAEU,GAAG,CAAE,KAAK,CAAEsC,QAAQ,CAAE,MAAO,CAAE,CAAAT,QAAA,cAC/E7D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAE1E,QAAS,CAAE,CAAE,CAAC,CACpD,CAAC,cAENK,IAAA,CAAChB,GAAG,EAAC8E,EAAE,CAAE,CAAE3C,OAAO,CAAE,MAAM,CAAEG,UAAU,CAAE,QAAQ,CAAEU,GAAG,CAAE,KAAK,CAAEsC,QAAQ,CAAE,MAAM,CAAEK,MAAM,CAAE,SAAU,CAAE,CAACZ,OAAO,CAAEH,mBAAoB,CAAAC,QAAA,cAChI7D,IAAA,SAAMoE,uBAAuB,CAAE,CAAEC,MAAM,CAAEzE,UAAW,CAAE,CAAE,CAAC,CACtD,CAAC,EACH,CAAC,CACC,CAAC,EACP,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAS,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}