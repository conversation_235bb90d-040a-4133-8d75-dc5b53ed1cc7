{"ast": null, "code": "import React,{useEffect,useMemo,useState}from\"react\";import{Box,IconButton,Typography,Button,FormControl,Select,MenuItem,TextField,ToggleButton,ToggleButtonGroup}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import{AlignHorizontalLeft as TopLeftIcon,AlignHorizontalCenter as TopCenterIcon,AlignHorizontalRight as TopRightIcon,AlignVerticalTop as MiddleLeftIcon,AlignVerticalCenter as MiddleCenterIcon,AlignVerticalBottom as MiddleRightIcon,AlignHorizontalLeft as BottomLeftIcon,AlignHorizontalCenter as BottomCenterIcon,AlignHorizontalRight as BottomRightIcon}from\"@mui/icons-material\";import\"../../guideDesign/Canvas.module.scss\";import useDrawerStore from\"../../../store/drawerStore\";// import Draggable from \"react-draggable\";\nimport{useTranslation}from\"react-i18next\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ButtonSettings=()=>{const{t:translate}=useTranslation();const{buttonsContainer,cloneButtonContainer,updateButton,addNewButton,deleteButton,deleteButtonContainer,updateContainer,setSettingAnchorEl,selectedTemplate,setSelectedTemplate,buttonProperty,setButtonProperty,setSelectActions,currentButtonName,setCurrentButtonName,targetURL,setTargetURL,selectedInteraction,setSelectedInteraction,openInteractionList,setOpenInteractionList,selectedTab,setSelectedTab,guideListByOrg,getGuildeListByOrg,loading,setLoading,updateButtonInteraction,updateButtonAction,settingAnchorEl,btnBgColor,btnTextColor,btnBorderColor,setBtnBgColor,setBtnTextColor,setBtnBorderColor,getCurrentButtonInfo,cuntainerId,setCuntainerId,buttonId,setButtonId,btnname,setBtnName,setIsUnSavedChanges,createWithAI}=useDrawerStore(state=>state);const[selectedActions,setSelectedActions]=useState({value:\"close\",// Default action\ntargetURL:\"\",// Default empty target URL\ntab:\"same-tab\"// Default tab (same-tab)\n});const[borderColor,setBorderColor]=useState(\"#000000\");const[backgroundColor,setBackgroundColor]=useState(\"#FFFFFF\");const[isOpen,setIsOpen]=useState(true);const[selectedPosition,setSelectedPosition]=useState(\"\");const[url,setUrl]=useState(\"\");const[action,setAction]=useState(\"close\");const[openInNewTab,setOpenInNewTab]=useState(true);const userInfo=localStorage.getItem(\"userInfo\");const userInfoObj=JSON.parse(userInfo||\"{}\");const orgDetails=JSON.parse(userInfoObj.orgDetails||\"{}\");const organizationId=orgDetails.OrganizationId;const defaultButtonColors={backgroundColor:\"#5F9EA0\",borderColor:\"#70afaf\",color:\"#ffffff\"};const[tempColors,setTempColors]=useState(defaultButtonColors);const[colors,setColors]=useState({fill:\"#4CAF50\",border:\"#4CAF50\",text:\"#ffffff\"});// State variables for validation errors\nconst[buttonNameError,setButtonNameError]=useState(\"\");const[targetURLError,setTargetURLError]=useState(\"\");useEffect(()=>{const handleSelectButton=(containerId,buttonId)=>{const selectedButton=getCurrentButtonInfo(containerId,buttonId);if(selectedButton){setCurrentButtonName(selectedButton.title);// Save the initial button name\nsetTargetURL(selectedButton.targetURL||\"\");setTempColors({backgroundColor:selectedButton.bgColor||defaultButtonColors.backgroundColor,borderColor:selectedButton.borderColor||defaultButtonColors.borderColor,color:selectedButton.textColor||defaultButtonColors.color});setSelectedActions({value:selectedButton.selectedActions,// Use the actual saved action value\ntargetURL:selectedButton.targetURL||\"\",// Use the saved target URL\ntab:selectedButton.tab||\"same-tab\"// Use the saved tab value\n});setSelectedTab(selectedButton.tab||\"same-tab\");// Also set the selectedTab state\n}};handleSelectButton(settingAnchorEl.containerId,settingAnchorEl.buttonId);},[settingAnchorEl.containerId,settingAnchorEl.buttonId]);const positions=[{label:translate(\"Top Left\",{defaultValue:\"Top Left\"}),icon:/*#__PURE__*/_jsx(TopLeftIcon,{fontSize:\"small\"}),value:\"top-left\"},{label:translate(\"Top Center\",{defaultValue:\"Top Center\"}),icon:/*#__PURE__*/_jsx(TopCenterIcon,{fontSize:\"small\"}),value:\"top-center\"},{label:translate(\"Top Right\",{defaultValue:\"Top Right\"}),icon:/*#__PURE__*/_jsx(TopRightIcon,{fontSize:\"small\"}),value:\"top-right\"},{label:translate(\"Middle Left\",{defaultValue:\"Middle Left\"}),icon:/*#__PURE__*/_jsx(MiddleLeftIcon,{fontSize:\"small\"}),value:\"middle-left\"},{label:translate(\"Middle Center\",{defaultValue:\"Middle Center\"}),icon:/*#__PURE__*/_jsx(MiddleCenterIcon,{fontSize:\"small\"}),value:\"middle-center\"},{label:translate(\"Middle Right\",{defaultValue:\"Middle Right\"}),icon:/*#__PURE__*/_jsx(MiddleRightIcon,{fontSize:\"small\"}),value:\"middle-right\"},{label:translate(\"Bottom Left\",{defaultValue:\"Bottom Left\"}),icon:/*#__PURE__*/_jsx(BottomLeftIcon,{fontSize:\"small\"}),value:\"bottom-left\"},{label:translate(\"Bottom Center\",{defaultValue:\"Bottom Center\"}),icon:/*#__PURE__*/_jsx(BottomCenterIcon,{fontSize:\"small\"}),value:\"bottom-center\"},{label:translate(\"Bottom Right\",{defaultValue:\"Bottom Right\"}),icon:/*#__PURE__*/_jsx(BottomRightIcon,{fontSize:\"small\"}),value:\"bottom-right\"}];const curronButtonInfo=useMemo(()=>{const result=getCurrentButtonInfo(settingAnchorEl.containerId,settingAnchorEl.buttonId);setCurrentButtonName(result.title);return result;},[settingAnchorEl.containerId,settingAnchorEl.buttonId]);const handlePositionClick=position=>{setSelectedPosition(position);};const handleClose=()=>{setButtonProperty(false);};if(!isOpen)return null;// const handleColorChange = (type: any, color: any) => {\n// \tsetColors((prevColors) => ({ ...prevColors, [type]: color }));\n// };\nconst handleColorChange=(e,targetName)=>{const value=e.target.value;setTempColors(prev=>({...prev,[targetName]:value}));};const handleChangeActions=e=>{const value=e.target.value;// Casting to TInteractionValue\nsetSelectedActions({value:value,// Ensure that selectedActions.value is of type TInteractionValue\ntargetURL:targetURL,tab:selectedTab// Ensure tab is a valid value\n});};const handleChangeTabs=event=>{setSelectedTab(event.target.value);};const handleCloseInteraction=()=>{setOpenInteractionList(false);};const handleOpenInteraction=()=>{setOpenInteractionList(true);if(organizationId&&!guideListByOrg.length){(async()=>{setLoading(true);await getGuildeListByOrg(organizationId);setLoading(false);})();}};const validateTargetURL=url=>{if(selectedActions.value===\"open-url\"){if(!url){return\"URL is required\";}try{new URL(url);return\"\";}catch(error){return\"Invalid URL\";}}return\"\";};const handleApplyChanges=(containerId,buttonId)=>{setCuntainerId(containerId);setButtonId(buttonId);const targetURLError=validateTargetURL(targetURL);setTargetURLError(targetURLError);if(targetURLError){return;}// Retain the previously saved button name if the field is empty\nconst buttonNameToUpdate=!currentButtonName||!currentButtonName.trim()?getCurrentButtonInfo(containerId,buttonId).title:currentButtonName;setCurrentButtonName(buttonNameToUpdate);// Update button properties\nupdateButton(containerId,buttonId,\"style\",tempColors);updateButton(containerId,buttonId,\"name\",buttonNameToUpdate);// Update button actions with the complete action object\nconst actionToSave={value:selectedActions.value,targetURL:targetURL,tab:selectedTab,interaction:selectedInteraction};updateButton(containerId,buttonId,\"actions\",actionToSave);updateButtonAction(containerId,buttonId,actionToSave);setSettingAnchorEl({containerId:\"\",buttonId:\"\",value:null});handleClose();setIsUnSavedChanges(true);// Clear selection\n//setSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\n};const handleURLChange=e=>{const newURL=e.target.value;setTargetURL(newURL);setSelectedActions({value:selectedActions.value,targetURL:newURL,tab:selectedTab});};return/*#__PURE__*/(//<Draggable>\n_jsx(\"div\",{className:\"qadpt-designpopup qadpt-banbtnprop\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Properties\",{defaultValue:\"Properties\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:()=>handleClose(),children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock qadpt-btnpro\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{marginBottom:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",my:\"5px\",textAlign:\"left\"},children:translate(\"Button Name\",{defaultValue:\"Button Name\"})}),/*#__PURE__*/_jsx(TextField,{value:currentButtonName,size:\"small\",sx:{mb:\"5px\",border:\"1px solid #ccc\",borderRadius:\"4px\",\"& .MuiOutlinedInput-root\":{height:\"35px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},placeholder:translate(\"Button Name\",{defaultValue:\"Button Name\"}),onChange:e=>{setCurrentButtonName(e.target.value);// setBtnName(e.target.value);\n}}),/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",mb:\"5px\",textAlign:\"left\"},children:translate(\"Button Action\",{defaultValue:\"Button Action\"})}),/*#__PURE__*/_jsxs(Select,{value:selectedActions.value,onChange:handleChangeActions,sx:{mb:\"5px\",border:\"1px solid #ccc\",borderRadius:\"4px\",textAlign:\"left\",\"& .MuiSelect-select\":{padding:\"8px\"},\"& .MuiOutlinedInput-root\":{height:\"35px\",\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},\"& .MuiOutlinedInput-notchedOutline\":{border:\"none !important\"}},MenuProps:{PaperProps:{}},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"close\",children:translate(\"Close\",{defaultValue:\"Close\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"open-url\",children:translate(\"Open URL\",{defaultValue:\"Open URL\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"Next\",children:translate(\"Next\",{defaultValue:\"Next\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"Previous\",children:translate(\"Previous\",{defaultValue:\"Previous\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"Restart\",children:translate(\"Restart\",{defaultValue:\"Restart\"})})]}),selectedActions.value===\"open-url\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",fontWeight:\"bold\",my:\"5px\",textAlign:\"left\"},children:translate(\"Enter URL\")}),/*#__PURE__*/_jsx(TextField,{value:targetURL,size:\"small\",placeholder:\"https://quixy.com\",onChange:e=>{const newURL=e.target.value;setTargetURL(newURL);// Update the `targetURL` state with the new value\nhandleURLChange(e);// Update the selectedButton.targetURL with the new value\nsetTargetURLError(validateTargetURL(newURL));},error:!!targetURLError,helperText:targetURLError}),/*#__PURE__*/_jsx(ToggleButtonGroup,{value:selectedTab,onChange:handleChangeTabs,exclusive:true,\"aria-label\":translate(\"open in tab\",{defaultValue:\"open in tab\"}),sx:{gap:\"5px\",marginY:\"5px\",height:\"35px\"},children:[\"new-tab\",\"same-tab\"].map(tab=>{return/*#__PURE__*/_jsx(ToggleButton,{value:tab,\"aria-label\":\"new tab\",sx:{border:\"1px solid #7EA8A5\",textTransform:\"capitalize\",color:\"#000\",borderRadius:\"4px\",flex:1,padding:\"0 !important\",\"&.Mui-selected\":{backgroundColor:\"var(--border-color)\",color:\"#000\",border:\"2px solid #7EA8A5\"},\"&:hover\":{backgroundColor:\"#f5f5f5\"},\"&:last-child\":{borderLeft:\"1px solid var(--primarycolor) !important\"}},children:tab});})})]}):null]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Background\",{defaultValue:\"Background\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tempColors.backgroundColor,onChange:e=>handleColorChange(e,\"backgroundColor\"),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Border\",{defaultValue:\"Border\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tempColors.borderColor,onChange:e=>handleColorChange(e,\"borderColor\"),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Text\",{defaultValue:\"Text\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:tempColors.color,onChange:e=>handleColorChange(e,\"color\"),className:\"qadpt-color-input\"})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:()=>handleApplyChanges(settingAnchorEl.containerId,settingAnchorEl.buttonId),className:\"qadpt-btn\",children:translate(\"Apply\",{defaultValue:\"Apply\"})})})]})})//</Draggable>\n);};export default ButtonSettings;", "map": {"version": 3, "names": ["React", "useEffect", "useMemo", "useState", "Box", "IconButton", "Typography", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "TextField", "ToggleButton", "ToggleButtonGroup", "CloseIcon", "AlignHorizontalLeft", "TopLeftIcon", "AlignHorizontalCenter", "TopCenterIcon", "AlignHorizontalRight", "TopRightIcon", "AlignVerticalTop", "MiddleLeftIcon", "AlignVerticalCenter", "MiddleCenterIcon", "AlignVerticalBottom", "MiddleRightIcon", "BottomLeftIcon", "BottomCenterIcon", "BottomRightIcon", "useDrawerStore", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ButtonSettings", "t", "translate", "buttonsContainer", "cloneButtonContainer", "updateButton", "addNewButton", "deleteButton", "deleteButtonContainer", "updateContainer", "setSettingAnchorEl", "selectedTemplate", "setSelectedTemplate", "buttonProperty", "setButtonProperty", "setSelectActions", "currentButtonName", "setCurrentButtonName", "targetURL", "setTargetURL", "selectedInteraction", "setSelectedInteraction", "openInteractionList", "setOpenInteractionList", "selectedTab", "setSelectedTab", "guideListByOrg", "getGuildeListByOrg", "loading", "setLoading", "updateButtonInteraction", "updateButtonAction", "settingAnchorEl", "btnBgColor", "btnTextColor", "btnBorderColor", "setBtnBgColor", "setBtnTextColor", "setBtnBorderColor", "getCurrentButtonInfo", "cuntainerId", "setCuntainerId", "buttonId", "setButtonId", "btnname", "setBtnName", "setIsUnSavedChanges", "createWithAI", "state", "selectedActions", "setSelectedActions", "value", "tab", "borderColor", "setBorderColor", "backgroundColor", "setBackgroundColor", "isOpen", "setIsOpen", "selectedPosition", "setSelectedPosition", "url", "setUrl", "action", "setAction", "openInNewTab", "setOpenInNewTab", "userInfo", "localStorage", "getItem", "userInfoObj", "JSON", "parse", "orgDetails", "organizationId", "OrganizationId", "defaultButtonColors", "color", "tempColors", "setTempColors", "colors", "setColors", "fill", "border", "text", "buttonNameError", "setButtonNameError", "targetURLError", "setTargetURLError", "handleSelectButton", "containerId", "<PERSON><PERSON><PERSON><PERSON>", "title", "bgColor", "textColor", "positions", "label", "defaultValue", "icon", "fontSize", "curronButtonInfo", "result", "handlePositionClick", "position", "handleClose", "handleColorChange", "e", "targetName", "target", "prev", "handleChangeActions", "handleChangeTabs", "event", "handleCloseInteraction", "handleOpenInteraction", "length", "validateTargetURL", "URL", "error", "handleApplyChanges", "buttonNameToUpdate", "trim", "actionToSave", "interaction", "handleURLChange", "newURL", "className", "children", "size", "onClick", "fullWidth", "sx", "marginBottom", "fontWeight", "my", "textAlign", "mb", "borderRadius", "height", "placeholder", "onChange", "padding", "MenuProps", "PaperProps", "helperText", "exclusive", "gap", "marginY", "map", "textTransform", "flex", "borderLeft", "type", "variant"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/ButtonSettings.tsx"], "sourcesContent": ["import React, { useEffect, useMemo, useState } from \"react\";\r\nimport {\r\n\tDialog,\r\n\tDialogContent,\r\n\tuseMediaQ<PERSON>y,\r\n\tuseTheme,\r\n\tBox,\r\n\tIconButton,\r\n\tPopover,\r\n\tTypography,\r\n\tButton,\r\n\tFormControl,\r\n\tSelect,\r\n\tMenuItem,\r\n\tTextField,\r\n\tSelectChangeEvent,\r\n\tRadioGroup,\r\n\tRadio,\r\n\tFormControlLabel,\r\n\tInput,\r\n\tToggleButton,\r\n\tToggleButtonGroup,\r\n\tAutocomplete,\r\n\tCircularProgress,\r\n\tDialogTitle,\r\n\tTooltip,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport {\r\n\tAlignHorizontalLeft as TopLeftIcon,\r\n\tAlignHorizontalCenter as TopCenterIcon,\r\n\tAlignHorizontalRight as TopRightIcon,\r\n\tAlignVerticalTop as MiddleLeftIcon,\r\n\tAlignVerticalCenter as MiddleCenterIcon,\r\n\tAlignVerticalBottom as MiddleRightIcon,\r\n\tAlignHorizontalLeft as BottomLeftIcon,\r\n\tAlignHorizontalCenter as BottomCenterIcon,\r\n\tAlignHorizontalRight as BottomRightIcon,\r\n} from \"@mui/icons-material\";\r\nimport \"../../guideDesign/Canvas.module.scss\";\r\nimport useDrawerStore, { TButtonAction, TInteractionValue } from \"../../../store/drawerStore\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport zIndex from \"@mui/material/styles/zIndex\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst ButtonSettings = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tbuttonsContainer,\r\n\t\tcloneButtonContainer,\r\n\t\tupdateButton,\r\n\t\taddNewButton,\r\n\t\tdeleteButton,\r\n\t\tdeleteButtonContainer,\r\n\t\tupdateContainer,\r\n\t\tsetSettingAnchorEl,\r\n\t\tselectedTemplate,\r\n\t\tsetSelectedTemplate,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tsetSelectActions,\r\n\t\tcurrentButtonName,\r\n\t\tsetCurrentButtonName,\r\n\t\ttargetURL,\r\n\t\tsetTargetURL,\r\n\t\tselectedInteraction,\r\n\t\tsetSelectedInteraction,\r\n\t\topenInteractionList,\r\n\t\tsetOpenInteractionList,\r\n\t\tselectedTab,\r\n\t\tsetSelectedTab,\r\n\t\tguideListByOrg,\r\n\t\tgetGuildeListByOrg,\r\n\t\tloading,\r\n\t\tsetLoading,\r\n\t\tupdateButtonInteraction,\r\n\t\tupdateButtonAction,\r\n\t\tsettingAnchorEl,\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t\tsetBtnBgColor,\r\n\t\tsetBtnTextColor,\r\n\t\tsetBtnBorderColor,\r\n\t\tgetCurrentButtonInfo,\r\n\t\tcuntainerId,\r\n\t\tsetCuntainerId,\r\n\t\tbuttonId,\r\n\t\tsetButtonId,\r\n\t\tbtnname,\r\n\t\tsetBtnName,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tcreateWithAI\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [selectedActions, setSelectedActions] = useState<TButtonAction>({\r\n\t\tvalue: \"close\", // Default action\r\n\t\ttargetURL: \"\", // Default empty target URL\r\n\t\ttab: \"same-tab\", // Default tab (same-tab)\r\n\t});\r\n\tconst [borderColor, setBorderColor] = useState(\"#000000\");\r\n\tconst [backgroundColor, setBackgroundColor] = useState(\"#FFFFFF\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"\");\r\n\tconst [url, setUrl] = useState(\"\");\r\n\tconst [action, setAction] = useState(\"close\");\r\n\tconst [openInNewTab, setOpenInNewTab] = useState(true);\r\n\tconst userInfo = localStorage.getItem(\"userInfo\");\r\n\tconst userInfoObj = JSON.parse(userInfo || \"{}\");\r\n\tconst orgDetails = JSON.parse(userInfoObj.orgDetails || \"{}\");\r\n\tconst organizationId = orgDetails.OrganizationId;\r\n\tconst defaultButtonColors = {\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tborderColor: \"#70afaf\",\r\n\t\tcolor: \"#ffffff\",\r\n\t};\r\n\tconst [tempColors, setTempColors] = useState(defaultButtonColors);\r\n\tconst [colors, setColors] = useState({\r\n\t\tfill: \"#4CAF50\",\r\n\t\tborder: \"#4CAF50\",\r\n\t\ttext: \"#ffffff\",\r\n\t});\r\n\r\n\t// State variables for validation errors\r\n\tconst [buttonNameError, setButtonNameError] = useState(\"\");\r\n\tconst [targetURLError, setTargetURLError] = useState(\"\");\r\n\r\n\tuseEffect(() => {\r\n\t\tconst handleSelectButton = (containerId: any, buttonId: any) => {\r\n\t\t\tconst selectedButton = getCurrentButtonInfo(containerId, buttonId);\r\n\t\t\tif (selectedButton) {\r\n\t\t\t\tsetCurrentButtonName(selectedButton.title); // Save the initial button name\r\n\t\t\t\tsetTargetURL(selectedButton.targetURL || \"\");\r\n\t\t\t\tsetTempColors({\r\n\t\t\t\t\tbackgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,\r\n\t\t\t\t\tborderColor: selectedButton.borderColor || defaultButtonColors.borderColor,\r\n\t\t\t\t\tcolor: selectedButton.textColor || defaultButtonColors.color,\r\n\t\t\t\t});\r\n\t\t\t\tsetSelectedActions({\r\n\t\t\t\t\tvalue: selectedButton.selectedActions as TInteractionValue, // Use the actual saved action value\r\n\t\t\t\t\ttargetURL: selectedButton.targetURL || \"\", // Use the saved target URL\r\n\t\t\t\t\ttab: selectedButton.tab || \"same-tab\", // Use the saved tab value\r\n\t\t\t\t});\r\n\t\t\t\tsetSelectedTab(selectedButton.tab || \"same-tab\"); // Also set the selectedTab state\r\n\t\t\t}\r\n\t\t};\r\n\t\thandleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\", { defaultValue: \"Top Left\" }), icon: <TopLeftIcon fontSize=\"small\" />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\", { defaultValue: \"Top Center\" }), icon: <TopCenterIcon fontSize=\"small\" />, value: \"top-center\" },\r\n\t\t{ label: translate(\"Top Right\", { defaultValue: \"Top Right\" }), icon: <TopRightIcon fontSize=\"small\" />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\", { defaultValue: \"Middle Left\" }), icon: <MiddleLeftIcon fontSize=\"small\" />, value: \"middle-left\" },\r\n\t\t{ label: translate(\"Middle Center\", { defaultValue: \"Middle Center\" }), icon: <MiddleCenterIcon fontSize=\"small\" />, value: \"middle-center\" },\r\n\t\t{ label: translate(\"Middle Right\", { defaultValue: \"Middle Right\" }), icon: <MiddleRightIcon fontSize=\"small\" />, value: \"middle-right\" },\r\n\t\t{ label: translate(\"Bottom Left\", { defaultValue: \"Bottom Left\" }), icon: <BottomLeftIcon fontSize=\"small\" />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\", { defaultValue: \"Bottom Center\" }), icon: <BottomCenterIcon fontSize=\"small\" />, value: \"bottom-center\" },\r\n\t\t{ label: translate(\"Bottom Right\", { defaultValue: \"Bottom Right\" }), icon: <BottomRightIcon fontSize=\"small\" />, value: \"bottom-right\" },\r\n\t];\r\n\r\n\tconst curronButtonInfo = useMemo(() => {\r\n\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\tsetCurrentButtonName(result.title);\r\n\t\treturn result;\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\tconst handlePositionClick = (position: any) => {\r\n\t\tsetSelectedPosition(position);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetButtonProperty(false);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\t// const handleColorChange = (type: any, color: any) => {\r\n\t// \tsetColors((prevColors) => ({ ...prevColors, [type]: color }));\r\n\t// };\r\n\r\n\r\n\tconst handleColorChange = (e: any, targetName: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tsetTempColors((prev) => ({\r\n\t\t\t...prev,\r\n\t\t\t[targetName]: value,\r\n\t\t}));\r\n\t};\r\n\r\n\tconst handleChangeActions = (e: SelectChangeEvent) => {\r\n\t\tconst value: TInteractionValue = e.target.value as TInteractionValue; // Casting to TInteractionValue\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: value, // Ensure that selectedActions.value is of type TInteractionValue\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\", // Ensure tab is a valid value\r\n\t\t});\r\n\t};\r\n\tconst handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSelectedTab((event.target as HTMLInputElement).value);\r\n\t};\r\n\tconst handleCloseInteraction = () => {\r\n\t\tsetOpenInteractionList(false);\r\n\t};\r\n\r\n\tconst handleOpenInteraction = () => {\r\n\t\tsetOpenInteractionList(true);\r\n\t\tif (organizationId && !guideListByOrg.length) {\r\n\t\t\t(async () => {\r\n\t\t\t\tsetLoading(true);\r\n\t\t\t\tawait getGuildeListByOrg(organizationId);\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t})();\r\n\t\t}\r\n\t};\r\n\r\n\tconst validateTargetURL = (url: string) => {\r\n\t\tif (selectedActions.value === \"open-url\") {\r\n\t\t\tif (!url) {\r\n\t\t\t\treturn \"URL is required\";\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tnew URL(url);\r\n\t\t\t\treturn \"\";\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn \"Invalid URL\";\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn \"\";\r\n\t};\r\n\r\n\tconst handleApplyChanges = (containerId: any, buttonId: any) => {\r\n\t\tsetCuntainerId(containerId);\r\n\t\tsetButtonId(buttonId);\r\n\r\n\t\tconst targetURLError = validateTargetURL(targetURL);\r\n\t\tsetTargetURLError(targetURLError);\r\n\r\n\t\tif (targetURLError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Retain the previously saved button name if the field is empty\r\n\t\tconst buttonNameToUpdate = !currentButtonName || !currentButtonName.trim() ? getCurrentButtonInfo(containerId, buttonId).title : currentButtonName;\r\n\t\tsetCurrentButtonName(buttonNameToUpdate);\r\n\r\n\t\t// Update button properties\r\n\t\tupdateButton(containerId, buttonId, \"style\", tempColors);\r\n\t\tupdateButton(containerId, buttonId, \"name\", buttonNameToUpdate);\r\n\r\n\t\t// Update button actions with the complete action object\r\n\t\tconst actionToSave = {\r\n\t\t\tvalue: selectedActions.value,\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\",\r\n\t\t\tinteraction: selectedInteraction,\r\n\t\t};\r\n\t\tupdateButton(containerId, buttonId, \"actions\", actionToSave);\r\n\t\tupdateButtonAction(containerId, buttonId, actionToSave);\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t\t// Clear selection\r\n\t\t//setSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t};\r\n\tconst handleURLChange = (e: any) => {\r\n\t\tconst newURL = e.target.value;\r\n\t\tsetTargetURL(newURL);\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: selectedActions.value,\r\n\t\t\ttargetURL: newURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\",\r\n\t\t});\r\n\t}\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tclassName=\"qadpt-designpopup qadpt-banbtnprop\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Properties\", { defaultValue: \"Properties\" })}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={() => handleClose()}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock qadpt-btnpro\">\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t<FormControl\r\n\t\t\t\t\tfullWidth\r\n\t\t\t\t\tsx={{ marginBottom: \"5px\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\t\t\t{translate(\"Button Name\", { defaultValue: \"Button Name\" })}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t\t<TextField\r\n\t\t\t\t\t\tvalue={currentButtonName}\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tplaceholder={translate(\"Button Name\", { defaultValue: \"Button Name\" })}\r\n\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\tsetCurrentButtonName(e.target.value);\r\n\t\t\t\t\t\t\t// setBtnName(e.target.value);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\t\t\t{translate(\"Button Action\", { defaultValue: \"Button Action\" })}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\tvalue={selectedActions.value}\r\n\t\t\t\t\t\t\tonChange={handleChangeActions}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": {\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\tPaperProps: {},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"close\">{translate(\"Close\", { defaultValue: \"Close\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"open-url\">{translate(\"Open URL\", { defaultValue: \"Open URL\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Next\">{translate(\"Next\", { defaultValue: \"Next\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Previous\">{translate(\"Previous\", { defaultValue: \"Previous\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Restart\">{translate(\"Restart\", { defaultValue: \"Restart\" })}</MenuItem>\r\n\t\t\t\t\t</Select>\r\n\t\t\t\t\t{selectedActions.value === \"open-url\" ? (\r\n\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Enter URL\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={targetURL}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"https://quixy.com\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\tconst newURL = e.target.value;\r\n\t\t\t\t\t\t\t\t\tsetTargetURL(newURL);   // Update the `targetURL` state with the new value\r\n\t\t\t\t\t\t\t\t\thandleURLChange(e);  // Update the selectedButton.targetURL with the new value\r\n\t\t\t\t\t\t\t\t\tsetTargetURLError(validateTargetURL(newURL));\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\terror={!!targetURLError}\r\n\t\t\t\t\t\t\t\thelperText={targetURLError}\r\n\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t<ToggleButtonGroup\r\n\t\t\t\t\t\t\t\tvalue={selectedTab}\r\n\t\t\t\t\t\t\t\tonChange={handleChangeTabs}\r\n\t\t\t\t\t\t\t\texclusive\r\n\t\t\t\t\t\t\t\t\t\taria-label={translate(\"open in tab\", { defaultValue: \"open in tab\" })}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\tmarginY: \"5px\",\r\n\t\t\t\t\t\t\t\t\theight: \"35px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{[\"new-tab\", \"same-tab\"].map((tab) => {\r\n\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t<ToggleButton\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={tab}\r\n\t\t\t\t\t\t\t\t\t\t\taria-label=\"new tab\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-selected\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"2px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:last-child\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderLeft: \"1px solid var(--primarycolor) !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{tab}\r\n\t\t\t\t\t\t\t\t\t\t</ToggleButton>\r\n\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t</ToggleButtonGroup>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{/* {selectedActions.value === \"start-interaction\" ? (\r\n\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>Choose Interaction</Typography>\r\n\r\n\t\t\t\t\t\t\t<Autocomplete\r\n\t\t\t\t\t\t\t\t// sx={{ width: 300 }}\r\n\t\t\t\t\t\t\t\topen={openInteractionList}\r\n\t\t\t\t\t\t\t\tvalue={selectedInteraction}\r\n\t\t\t\t\t\t\t\tonChange={(event, newValue) => {\r\n\t\t\t\t\t\t\t\t\tsetSelectedInteraction(newValue);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonOpen={handleOpenInteraction}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseInteraction}\r\n\t\t\t\t\t\t\t\tisOptionEqualToValue={(option, value) => {\r\n\t\t\t\t\t\t\t\t\treturn option.guideId === value.guideId;\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tgetOptionLabel={(option) => {\r\n\t\t\t\t\t\t\t\t\treturn option.title;\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tfreeSolo\r\n\t\t\t\t\t\t\t\toptions={guideListByOrg}\r\n\t\t\t\t\t\t\t\tloading={loading}\r\n\t\t\t\t\t\t\t\trenderInput={(params) => (\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t{...params}\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"Select Interaction\"\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tinputLabel: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tshrink: false,\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t) : null} */}\r\n\t\t\t\t</FormControl>\r\n\t\t\t\t\t{/* <Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\tButton Color\r\n\t\t\t\t\t</Typography> */}\r\n\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\", { defaultValue: \"Background\" })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempColors.backgroundColor}\r\n\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"backgroundColor\")}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\", { defaultValue: \"Border\" })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempColors.borderColor}\r\n\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"borderColor\")}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Text\", { defaultValue: \"Text\" })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempColors.color}\r\n\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"color\")}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={() => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\", { defaultValue: \"Apply\" })}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ButtonSettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,OAAO,CAAEC,QAAQ,KAAQ,OAAO,CAC3D,OAKCC,GAAG,CACHC,UAAU,CAEVC,UAAU,CACVC,MAAM,CACNC,WAAW,CACXC,MAAM,CACNC,QAAQ,CACRC,SAAS,CAMTC,YAAY,CACZC,iBAAiB,KAKX,eAAe,CACtB,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,OACCC,mBAAmB,GAAI,CAAAC,WAAW,CAClCC,qBAAqB,GAAI,CAAAC,aAAa,CACtCC,oBAAoB,GAAI,CAAAC,YAAY,CACpCC,gBAAgB,GAAI,CAAAC,cAAc,CAClCC,mBAAmB,GAAI,CAAAC,gBAAgB,CACvCC,mBAAmB,GAAI,CAAAC,eAAe,CACtCX,mBAAmB,GAAI,CAAAY,cAAc,CACrCV,qBAAqB,GAAI,CAAAW,gBAAgB,CACzCT,oBAAoB,GAAI,CAAAU,eAAe,KACjC,qBAAqB,CAC5B,MAAO,sCAAsC,CAC7C,MAAO,CAAAC,cAAc,KAA4C,4BAA4B,CAC7F;AAGA,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,CAAC,CAAEC,SAAU,CAAC,CAAGT,cAAc,CAAC,CAAC,CACzC,KAAM,CACLU,gBAAgB,CAChBC,oBAAoB,CACpBC,YAAY,CACZC,YAAY,CACZC,YAAY,CACZC,qBAAqB,CACrBC,eAAe,CACfC,kBAAkB,CAClBC,gBAAgB,CAChBC,mBAAmB,CACnBC,cAAc,CACdC,iBAAiB,CACjBC,gBAAgB,CAChBC,iBAAiB,CACjBC,oBAAoB,CACpBC,SAAS,CACTC,YAAY,CACZC,mBAAmB,CACnBC,sBAAsB,CACtBC,mBAAmB,CACnBC,sBAAsB,CACtBC,WAAW,CACXC,cAAc,CACdC,cAAc,CACdC,kBAAkB,CAClBC,OAAO,CACPC,UAAU,CACVC,uBAAuB,CACvBC,kBAAkB,CAClBC,eAAe,CACfC,UAAU,CACVC,YAAY,CACZC,cAAc,CACdC,aAAa,CACbC,eAAe,CACfC,iBAAiB,CACjBC,oBAAoB,CACpBC,WAAW,CACXC,cAAc,CACdC,QAAQ,CACRC,WAAW,CACXC,OAAO,CACPC,UAAU,CACVC,mBAAmB,CACnBC,YACD,CAAC,CAAGvD,cAAc,CAAEwD,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGrF,QAAQ,CAAgB,CACrEsF,KAAK,CAAE,OAAO,CAAE;AAChBjC,SAAS,CAAE,EAAE,CAAE;AACfkC,GAAG,CAAE,UAAY;AAClB,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGzF,QAAQ,CAAC,SAAS,CAAC,CACzD,KAAM,CAAC0F,eAAe,CAAEC,kBAAkB,CAAC,CAAG3F,QAAQ,CAAC,SAAS,CAAC,CACjE,KAAM,CAAC4F,MAAM,CAAEC,SAAS,CAAC,CAAG7F,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAC8F,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACgG,GAAG,CAAEC,MAAM,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAClC,KAAM,CAACkG,MAAM,CAAEC,SAAS,CAAC,CAAGnG,QAAQ,CAAC,OAAO,CAAC,CAC7C,KAAM,CAACoG,YAAY,CAAEC,eAAe,CAAC,CAAGrG,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAAsG,QAAQ,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CACjD,KAAM,CAAAC,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,EAAI,IAAI,CAAC,CAChD,KAAM,CAAAM,UAAU,CAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,CAACG,UAAU,EAAI,IAAI,CAAC,CAC7D,KAAM,CAAAC,cAAc,CAAGD,UAAU,CAACE,cAAc,CAChD,KAAM,CAAAC,mBAAmB,CAAG,CAC3BrB,eAAe,CAAE,SAAS,CAC1BF,WAAW,CAAE,SAAS,CACtBwB,KAAK,CAAE,SACR,CAAC,CACD,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGlH,QAAQ,CAAC+G,mBAAmB,CAAC,CACjE,KAAM,CAACI,MAAM,CAAEC,SAAS,CAAC,CAAGpH,QAAQ,CAAC,CACpCqH,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,IAAI,CAAE,SACP,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0H,cAAc,CAAEC,iBAAiB,CAAC,CAAG3H,QAAQ,CAAC,EAAE,CAAC,CAExDF,SAAS,CAAC,IAAM,CACf,KAAM,CAAA8H,kBAAkB,CAAGA,CAACC,WAAgB,CAAEhD,QAAa,GAAK,CAC/D,KAAM,CAAAiD,cAAc,CAAGpD,oBAAoB,CAACmD,WAAW,CAAEhD,QAAQ,CAAC,CAClE,GAAIiD,cAAc,CAAE,CACnB1E,oBAAoB,CAAC0E,cAAc,CAACC,KAAK,CAAC,CAAE;AAC5CzE,YAAY,CAACwE,cAAc,CAACzE,SAAS,EAAI,EAAE,CAAC,CAC5C6D,aAAa,CAAC,CACbxB,eAAe,CAAEoC,cAAc,CAACE,OAAO,EAAIjB,mBAAmB,CAACrB,eAAe,CAC9EF,WAAW,CAAEsC,cAAc,CAACtC,WAAW,EAAIuB,mBAAmB,CAACvB,WAAW,CAC1EwB,KAAK,CAAEc,cAAc,CAACG,SAAS,EAAIlB,mBAAmB,CAACC,KACxD,CAAC,CAAC,CACF3B,kBAAkB,CAAC,CAClBC,KAAK,CAAEwC,cAAc,CAAC1C,eAAoC,CAAE;AAC5D/B,SAAS,CAAEyE,cAAc,CAACzE,SAAS,EAAI,EAAE,CAAE;AAC3CkC,GAAG,CAAEuC,cAAc,CAACvC,GAAG,EAAI,UAAY;AACxC,CAAC,CAAC,CACF3B,cAAc,CAACkE,cAAc,CAACvC,GAAG,EAAI,UAAU,CAAC,CAAE;AACnD,CACD,CAAC,CACDqC,kBAAkB,CAACzD,eAAe,CAAC0D,WAAW,CAAE1D,eAAe,CAACU,QAAQ,CAAC,CAC1E,CAAC,CAAE,CAACV,eAAe,CAAC0D,WAAW,CAAE1D,eAAe,CAACU,QAAQ,CAAC,CAAC,CAG3D,KAAM,CAAAqD,SAAS,CAAG,CACjB,CAAEC,KAAK,CAAE9F,SAAS,CAAC,UAAU,CAAE,CAAE+F,YAAY,CAAE,UAAW,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACjB,WAAW,EAACyH,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,UAAW,CAAC,CACzH,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,YAAY,CAAE,CAAE+F,YAAY,CAAE,YAAa,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACf,aAAa,EAACuH,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,YAAa,CAAC,CACjI,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,WAAW,CAAE,CAAE+F,YAAY,CAAE,WAAY,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACb,YAAY,EAACqH,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,WAAY,CAAC,CAC7H,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,aAAa,CAAE,CAAE+F,YAAY,CAAE,aAAc,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACX,cAAc,EAACmH,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,aAAc,CAAC,CACrI,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,eAAe,CAAE,CAAE+F,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACT,gBAAgB,EAACiH,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,eAAgB,CAAC,CAC7I,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,cAAc,CAAE,CAAE+F,YAAY,CAAE,cAAe,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACP,eAAe,EAAC+G,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,cAAe,CAAC,CACzI,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,aAAa,CAAE,CAAE+F,YAAY,CAAE,aAAc,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACN,cAAc,EAAC8G,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,aAAc,CAAC,CACrI,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,eAAe,CAAE,CAAE+F,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACL,gBAAgB,EAAC6G,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,eAAgB,CAAC,CAC7I,CAAE6C,KAAK,CAAE9F,SAAS,CAAC,cAAc,CAAE,CAAE+F,YAAY,CAAE,cAAe,CAAC,CAAC,CAAEC,IAAI,cAAEvG,IAAA,CAACJ,eAAe,EAAC4G,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAEhD,KAAK,CAAE,cAAe,CAAC,CACzI,CAED,KAAM,CAAAiD,gBAAgB,CAAGxI,OAAO,CAAC,IAAM,CACtC,KAAM,CAAAyI,MAAM,CAAG9D,oBAAoB,CAACP,eAAe,CAAC0D,WAAW,CAAE1D,eAAe,CAACU,QAAQ,CAAC,CAC1FzB,oBAAoB,CAACoF,MAAM,CAACT,KAAK,CAAC,CAClC,MAAO,CAAAS,MAAM,CACd,CAAC,CAAE,CAACrE,eAAe,CAAC0D,WAAW,CAAE1D,eAAe,CAACU,QAAQ,CAAC,CAAC,CAE3D,KAAM,CAAA4D,mBAAmB,CAAIC,QAAa,EAAK,CAC9C3C,mBAAmB,CAAC2C,QAAQ,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACzB1F,iBAAiB,CAAC,KAAK,CAAC,CACzB,CAAC,CAED,GAAI,CAAC2C,MAAM,CAAE,MAAO,KAAI,CAExB;AACA;AACA;AAGA,KAAM,CAAAgD,iBAAiB,CAAGA,CAACC,CAAM,CAAEC,UAAe,GAAK,CACtD,KAAM,CAAAxD,KAAK,CAAGuD,CAAC,CAACE,MAAM,CAACzD,KAAK,CAC5B4B,aAAa,CAAE8B,IAAI,GAAM,CACxB,GAAGA,IAAI,CACP,CAACF,UAAU,EAAGxD,KACf,CAAC,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA2D,mBAAmB,CAAIJ,CAAoB,EAAK,CACrD,KAAM,CAAAvD,KAAwB,CAAGuD,CAAC,CAACE,MAAM,CAACzD,KAA0B,CAAE;AACtED,kBAAkB,CAAC,CAClBC,KAAK,CAAEA,KAAK,CAAE;AACdjC,SAAS,CAAEA,SAAS,CACpBkC,GAAG,CAAE5B,WAAuC;AAC7C,CAAC,CAAC,CACH,CAAC,CACD,KAAM,CAAAuF,gBAAgB,CAAIC,KAAoC,EAAK,CAClEvF,cAAc,CAAEuF,KAAK,CAACJ,MAAM,CAAsBzD,KAAK,CAAC,CACzD,CAAC,CACD,KAAM,CAAA8D,sBAAsB,CAAGA,CAAA,GAAM,CACpC1F,sBAAsB,CAAC,KAAK,CAAC,CAC9B,CAAC,CAED,KAAM,CAAA2F,qBAAqB,CAAGA,CAAA,GAAM,CACnC3F,sBAAsB,CAAC,IAAI,CAAC,CAC5B,GAAImD,cAAc,EAAI,CAAChD,cAAc,CAACyF,MAAM,CAAE,CAC7C,CAAC,SAAY,CACZtF,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAF,kBAAkB,CAAC+C,cAAc,CAAC,CACxC7C,UAAU,CAAC,KAAK,CAAC,CAClB,CAAC,EAAE,CAAC,CACL,CACD,CAAC,CAED,KAAM,CAAAuF,iBAAiB,CAAIvD,GAAW,EAAK,CAC1C,GAAIZ,eAAe,CAACE,KAAK,GAAK,UAAU,CAAE,CACzC,GAAI,CAACU,GAAG,CAAE,CACT,MAAO,iBAAiB,CACzB,CACA,GAAI,CACH,GAAI,CAAAwD,GAAG,CAACxD,GAAG,CAAC,CACZ,MAAO,EAAE,CACV,CAAE,MAAOyD,KAAK,CAAE,CACf,MAAO,aAAa,CACrB,CACD,CACA,MAAO,EAAE,CACV,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAC7B,WAAgB,CAAEhD,QAAa,GAAK,CAC/DD,cAAc,CAACiD,WAAW,CAAC,CAC3B/C,WAAW,CAACD,QAAQ,CAAC,CAErB,KAAM,CAAA6C,cAAc,CAAG6B,iBAAiB,CAAClG,SAAS,CAAC,CACnDsE,iBAAiB,CAACD,cAAc,CAAC,CAEjC,GAAIA,cAAc,CAAE,CACnB,OACD,CAEA;AACA,KAAM,CAAAiC,kBAAkB,CAAG,CAACxG,iBAAiB,EAAI,CAACA,iBAAiB,CAACyG,IAAI,CAAC,CAAC,CAAGlF,oBAAoB,CAACmD,WAAW,CAAEhD,QAAQ,CAAC,CAACkD,KAAK,CAAG5E,iBAAiB,CAClJC,oBAAoB,CAACuG,kBAAkB,CAAC,CAExC;AACAnH,YAAY,CAACqF,WAAW,CAAEhD,QAAQ,CAAE,OAAO,CAAEoC,UAAU,CAAC,CACxDzE,YAAY,CAACqF,WAAW,CAAEhD,QAAQ,CAAE,MAAM,CAAE8E,kBAAkB,CAAC,CAE/D;AACA,KAAM,CAAAE,YAAY,CAAG,CACpBvE,KAAK,CAAEF,eAAe,CAACE,KAAK,CAC5BjC,SAAS,CAAEA,SAAS,CACpBkC,GAAG,CAAE5B,WAAqC,CAC1CmG,WAAW,CAAEvG,mBACd,CAAC,CACDf,YAAY,CAACqF,WAAW,CAAEhD,QAAQ,CAAE,SAAS,CAAEgF,YAAY,CAAC,CAC5D3F,kBAAkB,CAAC2D,WAAW,CAAEhD,QAAQ,CAAEgF,YAAY,CAAC,CACvDhH,kBAAkB,CAAC,CAAEgF,WAAW,CAAE,EAAE,CAAEhD,QAAQ,CAAE,EAAE,CAAES,KAAK,CAAE,IAAK,CAAC,CAAC,CAClEqD,WAAW,CAAC,CAAC,CACb1D,mBAAmB,CAAC,IAAI,CAAC,CACzB;AACA;AACD,CAAC,CACD,KAAM,CAAA8E,eAAe,CAAIlB,CAAM,EAAK,CACnC,KAAM,CAAAmB,MAAM,CAAGnB,CAAC,CAACE,MAAM,CAACzD,KAAK,CAC7BhC,YAAY,CAAC0G,MAAM,CAAC,CACpB3E,kBAAkB,CAAC,CAClBC,KAAK,CAAEF,eAAe,CAACE,KAAK,CAC5BjC,SAAS,CAAE2G,MAAM,CACjBzE,GAAG,CAAE5B,WACN,CAAC,CAAC,CACH,CAAC,CAED,mBACC;AACA7B,IAAA,QACCmI,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cAE9ClI,KAAA,QAAKiI,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BlI,KAAA,QAAKiI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnCpI,IAAA,QAAKmI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE7H,SAAS,CAAC,YAAY,CAAE,CAAE+F,YAAY,CAAE,YAAa,CAAC,CAAC,CAAM,CAAC,cAC5FtG,IAAA,CAAC5B,UAAU,EACViK,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBC,OAAO,CAAEA,CAAA,GAAMzB,WAAW,CAAC,CAAE,CAAAuB,QAAA,cAE7BpI,IAAA,CAACnB,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNmB,IAAA,QAAKmI,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC5ClI,KAAA,QAAKiI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC/BlI,KAAA,CAAC3B,WAAW,EACXgK,SAAS,MACTC,EAAE,CAAE,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAL,QAAA,eAE5BpI,IAAA,CAAC3B,UAAU,EAACmK,EAAE,CAAE,CAAEhC,QAAQ,CAAE,MAAM,CAAEkC,UAAU,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAK,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAR,QAAA,CACnF7H,SAAS,CAAC,aAAa,CAAE,CAAE+F,YAAY,CAAE,aAAc,CAAC,CAAC,CACjD,CAAC,cACbtG,IAAA,CAACtB,SAAS,EACT8E,KAAK,CAAEnC,iBAAkB,CACzBgH,IAAI,CAAC,OAAO,CACVG,EAAE,CAAE,CACHK,EAAE,CAAE,KAAK,CACTrD,MAAM,CAAE,gBAAgB,CACxBsD,YAAY,CAAE,KAAK,CAE1B,0BAA0B,CAAE,CAC1BC,MAAM,CAAE,MAAM,CACd,0CAA0C,CAAE,CAC1CvD,MAAM,CAAE,iBACV,CAAC,CACD,gDAAgD,CAAE,CAChDA,MAAM,CAAE,iBACV,CACF,CAAC,CACD,oCAAoC,CAAE,CACpCA,MAAM,CAAE,iBACV,CAEI,CAAE,CACAwD,WAAW,CAAEzI,SAAS,CAAC,aAAa,CAAE,CAAE+F,YAAY,CAAE,aAAc,CAAC,CAAE,CACzE2C,QAAQ,CAAGlC,CAAC,EAAK,CAChBzF,oBAAoB,CAACyF,CAAC,CAACE,MAAM,CAACzD,KAAK,CAAC,CACpC;AACD,CAAE,CACF,CAAC,cACFxD,IAAA,CAAC3B,UAAU,EAACmK,EAAE,CAAE,CAAEhC,QAAQ,CAAE,MAAM,CAAEkC,UAAU,CAAE,MAAM,CAAEG,EAAE,CAAE,KAAK,CAAED,SAAS,CAAE,MAAO,CAAE,CAAAR,QAAA,CACnF7H,SAAS,CAAC,eAAe,CAAE,CAAE+F,YAAY,CAAE,eAAgB,CAAC,CAAC,CACrD,CAAC,cACbpG,KAAA,CAAC1B,MAAM,EACLgF,KAAK,CAAEF,eAAe,CAACE,KAAM,CAC7ByF,QAAQ,CAAE9B,mBAAoB,CAE7BqB,EAAE,CAAE,CACHK,EAAE,CAAE,KAAK,CACTrD,MAAM,CAAE,gBAAgB,CACxBsD,YAAY,CAAE,KAAK,CACnBF,SAAS,CAAE,MAAM,CACjB,qBAAqB,CAAE,CACxBM,OAAO,CAAE,KACV,CAAC,CAEN,0BAA0B,CAAE,CAC1BH,MAAM,CAAE,MAAM,CACd,0CAA0C,CAAE,CAC1CvD,MAAM,CAAE,iBACV,CAAC,CACD,gDAAgD,CAAE,CAChDA,MAAM,CAAE,iBACV,CACF,CAAC,CACD,oCAAoC,CAAE,CACpCA,MAAM,CAAE,iBACV,CAEI,CAAE,CACF2D,SAAS,CAAE,CACVC,UAAU,CAAE,CAAC,CACd,CAAE,CAAAhB,QAAA,eAEApI,IAAA,CAACvB,QAAQ,EAAC+E,KAAK,CAAC,OAAO,CAAA4E,QAAA,CAAE7H,SAAS,CAAC,OAAO,CAAE,CAAE+F,YAAY,CAAE,OAAQ,CAAC,CAAC,CAAW,CAAC,cAClFtG,IAAA,CAACvB,QAAQ,EAAC+E,KAAK,CAAC,UAAU,CAAA4E,QAAA,CAAE7H,SAAS,CAAC,UAAU,CAAE,CAAE+F,YAAY,CAAE,UAAW,CAAC,CAAC,CAAW,CAAC,cAC3FtG,IAAA,CAACvB,QAAQ,EAAC+E,KAAK,CAAC,MAAM,CAAA4E,QAAA,CAAE7H,SAAS,CAAC,MAAM,CAAE,CAAE+F,YAAY,CAAE,MAAO,CAAC,CAAC,CAAW,CAAC,cAC/EtG,IAAA,CAACvB,QAAQ,EAAC+E,KAAK,CAAC,UAAU,CAAA4E,QAAA,CAAE7H,SAAS,CAAC,UAAU,CAAE,CAAE+F,YAAY,CAAE,UAAW,CAAC,CAAC,CAAW,CAAC,cAC3FtG,IAAA,CAACvB,QAAQ,EAAC+E,KAAK,CAAC,SAAS,CAAA4E,QAAA,CAAE7H,SAAS,CAAC,SAAS,CAAE,CAAE+F,YAAY,CAAE,SAAU,CAAC,CAAC,CAAW,CAAC,EACnF,CAAC,CACRhD,eAAe,CAACE,KAAK,GAAK,UAAU,cACpCtD,KAAA,CAAAE,SAAA,EAAAgI,QAAA,eACCpI,IAAA,CAAC3B,UAAU,EAACmK,EAAE,CAAE,CAAEhC,QAAQ,CAAE,MAAM,CAAEkC,UAAU,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAK,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAR,QAAA,CACnF7H,SAAS,CAAC,WAAW,CAAC,CACd,CAAC,cACbP,IAAA,CAACtB,SAAS,EACT8E,KAAK,CAAEjC,SAAU,CACjB8G,IAAI,CAAC,OAAO,CACZW,WAAW,CAAC,mBAAmB,CAC/BC,QAAQ,CAAGlC,CAAC,EAAK,CAChB,KAAM,CAAAmB,MAAM,CAAGnB,CAAC,CAACE,MAAM,CAACzD,KAAK,CAC7BhC,YAAY,CAAC0G,MAAM,CAAC,CAAI;AACxBD,eAAe,CAAClB,CAAC,CAAC,CAAG;AACrBlB,iBAAiB,CAAC4B,iBAAiB,CAACS,MAAM,CAAC,CAAC,CAC7C,CACC,CACDP,KAAK,CAAE,CAAC,CAAC/B,cAAe,CACxByD,UAAU,CAAEzD,cAAe,CAC3B,CAAC,cAEF5F,IAAA,CAACpB,iBAAiB,EACjB4E,KAAK,CAAE3B,WAAY,CACnBoH,QAAQ,CAAE7B,gBAAiB,CAC3BkC,SAAS,MACP,aAAY/I,SAAS,CAAC,aAAa,CAAE,CAAE+F,YAAY,CAAE,aAAc,CAAC,CAAE,CACxEkC,EAAE,CAAE,CACHe,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,KAAK,CACdT,MAAM,CAAE,MACT,CAAE,CAAAX,QAAA,CAED,CAAC,SAAS,CAAE,UAAU,CAAC,CAACqB,GAAG,CAAEhG,GAAG,EAAK,CACrC,mBACCzD,IAAA,CAACrB,YAAY,EACZ6E,KAAK,CAAEC,GAAI,CACX,aAAW,SAAS,CACpB+E,EAAE,CAAE,CACHhD,MAAM,CAAE,mBAAmB,CAC3BkE,aAAa,CAAE,YAAY,CAC3BxE,KAAK,CAAE,MAAM,CACb4D,YAAY,CAAE,KAAK,CACnBa,IAAI,CAAE,CAAC,CACPT,OAAO,CAAE,cAAc,CAEvB,gBAAgB,CAAE,CACjBtF,eAAe,CAAE,qBAAqB,CACtCsB,KAAK,CAAE,MAAM,CACbM,MAAM,CAAE,mBACT,CAAC,CACD,SAAS,CAAE,CACV5B,eAAe,CAAE,SAClB,CAAC,CACD,cAAc,CAAE,CACfgG,UAAU,CAAE,0CACb,CACD,CAAE,CAAAxB,QAAA,CAED3E,GAAG,CACS,CAAC,CAEjB,CAAC,CAAC,CACgB,CAAC,EACnB,CAAC,CACC,IAAI,EAwCG,CAAC,cAKbvD,KAAA,CAAC/B,GAAG,EACHgK,SAAS,CAAC,mBAAmB,CAC7BK,EAAE,CAAE,CAAEM,YAAY,CAAE,KAAM,CAAE,CAAAV,QAAA,eAE3BpI,IAAA,QAAKmI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE7H,SAAS,CAAC,YAAY,CAAE,CAAE+F,YAAY,CAAE,YAAa,CAAC,CAAC,CAAM,CAAC,cACpGtG,IAAA,QAAAoI,QAAA,cACDpI,IAAA,UACC6J,IAAI,CAAC,OAAO,CACZrG,KAAK,CAAE2B,UAAU,CAACvB,eAAgB,CAClCqF,QAAQ,CAAGlC,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAE,iBAAiB,CAAE,CACzDoB,SAAS,CAAC,mBAAmB,CAC3B,CAAC,CACG,CAAC,EACJ,CAAC,cAENjI,KAAA,CAAC/B,GAAG,EACHgK,SAAS,CAAC,mBAAmB,CAC7BK,EAAE,CAAE,CAAEM,YAAY,CAAE,KAAM,CAAE,CAAAV,QAAA,eAE3BpI,IAAA,QAAKmI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE7H,SAAS,CAAC,QAAQ,CAAE,CAAE+F,YAAY,CAAE,QAAS,CAAC,CAAC,CAAM,CAAC,cAC5FtG,IAAA,QAAAoI,QAAA,cACDpI,IAAA,UACC6J,IAAI,CAAC,OAAO,CACZrG,KAAK,CAAE2B,UAAU,CAACzB,WAAY,CAC9BuF,QAAQ,CAAGlC,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAE,aAAa,CAAE,CACrDoB,SAAS,CAAC,mBAAmB,CAC3B,CAAC,CACG,CAAC,EACJ,CAAC,cAENjI,KAAA,CAAC/B,GAAG,EACHgK,SAAS,CAAC,mBAAmB,CAC7BK,EAAE,CAAE,CAAEM,YAAY,CAAE,KAAM,CAAE,CAAAV,QAAA,eAE3BpI,IAAA,QAAKmI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE7H,SAAS,CAAC,MAAM,CAAE,CAAE+F,YAAY,CAAE,MAAO,CAAC,CAAC,CAAM,CAAC,cACxFtG,IAAA,QAAAoI,QAAA,cACDpI,IAAA,UACC6J,IAAI,CAAC,OAAO,CACZrG,KAAK,CAAE2B,UAAU,CAACD,KAAM,CACxB+D,QAAQ,CAAGlC,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAE,OAAO,CAAE,CAC/CoB,SAAS,CAAC,mBAAmB,CAC3B,CAAC,CACG,CAAC,EACJ,CAAC,EACD,CAAC,CACD,CAAC,cAEPnI,IAAA,QAAKmI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClCpI,IAAA,CAAC1B,MAAM,EACNwL,OAAO,CAAC,WAAW,CACnBxB,OAAO,CAAEA,CAAA,GAAMV,kBAAkB,CAACvF,eAAe,CAAC0D,WAAW,CAAE1D,eAAe,CAACU,QAAQ,CAAE,CACzFoF,SAAS,CAAC,WAAW,CAAAC,QAAA,CAEpB7H,SAAS,CAAC,OAAO,CAAE,CAAE+F,YAAY,CAAE,OAAQ,CAAC,CAAC,CACvC,CAAC,CACL,CAAC,EACF,CAAC,CACF,CACL;AAAA,EAEF,CAAC,CAED,cAAe,CAAAjG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}