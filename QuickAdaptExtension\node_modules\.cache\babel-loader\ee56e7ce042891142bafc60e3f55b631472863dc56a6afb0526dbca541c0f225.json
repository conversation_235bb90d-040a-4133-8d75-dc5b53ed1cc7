{"ast": null, "code": "const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = function (obj, path) {\n  let keySeparator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.';\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return this.forward(args, 'log', '', true);\n  }\n  warn() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return this.forward(args, 'warn', '', true);\n  }\n  error() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    return this.forward(args, 'error', '');\n  }\n  deprecate() {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event) {\n    for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {\n      args[_key5 - 1] = arguments[_key5];\n    }\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(_ref => {\n        let [observer, numTimesAdded] = _ref;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(_ref2 => {\n        let [observer, numTimesAdded] = _ref2;\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\nclass ResourceStore extends EventEmitter {\n  constructor(data) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      ns: ['translation'],\n      defaultNS: 'translation'\n    };\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value) {\n    let options = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      silent: false\n    };\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n      silent: false\n    };\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {\n      silent: false,\n      skipCopy: false\n    };\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key) {\n    let o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    var _this = this;\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, function () {\n        for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n          args[_key6] = arguments[_key6];\n        }\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          _this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return _this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys) {\n    let opt = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\nconst deepFindWithDefaults = function (data, defaultData, key) {\n  let keySeparator = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '.';\n  let ignoreJSONStructure = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      const keyEndIndex = /{.*}/.test(match[1]) ? match[1].lastIndexOf('}') + 1 : match[1].indexOf(this.formatSeparator);\n      if (keyEndIndex !== -1) {\n        formatters = match[1].slice(keyEndIndex).split(this.formatSeparator).map(elem => elem.trim()).filter(Boolean);\n        match[1] = match[1].slice(0, keyEndIndex);\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (formatters.length) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      interpolation: {}\n    };\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services) {\n    let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ;else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName) {\n    let tried = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n    let wait = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : this.retryTimeout;\n    let callback = arguments.length > 5 ? arguments[5] : undefined;\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    let callback = arguments.length > 3 ? arguments[3] : undefined;\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name) {\n    let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate) {\n    let options = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : {};\n    let clb = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : () => {};\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init() {\n    var _this2 = this;\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      const usingLegacyFormatFunction = this.options.interpolation.format && this.options.interpolation.format !== defOpts.interpolation.format;\n      if (usingLegacyFormatFunction) {\n        this.logger.warn(`init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting`);\n      }\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        if (s.formatter.init) s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', function (event) {\n        for (var _len7 = arguments.length, args = new Array(_len7 > 1 ? _len7 - 1 : 0), _key7 = 1; _key7 < _len7; _key7++) {\n          args[_key7 - 1] = arguments[_key7];\n        }\n        _this2.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', function (event) {\n        for (var _len8 = arguments.length, args = new Array(_len8 > 1 ? _len8 - 1 : 0), _key8 = 1; _key8 < _len8; _key8++) {\n          args[_key8 - 1] = arguments[_key8];\n        }\n        _this2.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = function () {\n        return _this2.store[fcName](...arguments);\n      };\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = function () {\n        _this2.store[fcName](...arguments);\n        return _this2;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language) {\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    var _this3 = this;\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve(function () {\n        return _this3.t(...arguments);\n      });\n      if (callback) callback(err, function () {\n        return _this3.t(...arguments);\n      });\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    var _this4 = this;\n    const fixedT = function (key, opts) {\n      let o;\n      if (typeof opts !== 'object') {\n        for (var _len9 = arguments.length, rest = new Array(_len9 > 2 ? _len9 - 2 : 0), _key9 = 2; _key9 < _len9; _key9++) {\n          rest[_key9 - 2] = arguments[_key9];\n        }\n        o = _this4.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = _this4.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return _this4.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t() {\n    for (var _len10 = arguments.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n      args[_key10] = arguments[_key10];\n    }\n    return this.translator?.translate(...args);\n  }\n  exists() {\n    for (var _len11 = arguments.length, args = new Array(_len11), _key11 = 0; _key11 < _len11; _key11++) {\n      args[_key11] = arguments[_key11];\n    }\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    try {\n      const l = new Intl.Locale(lng);\n      if (l && l.getTextInfo) {\n        const ti = l.getTextInfo();\n        if (ti && ti.direction) return ti.direction;\n      }\n    } catch (e) {}\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    if (lng.toLowerCase().indexOf('-latn') > 1) return 'ltr';\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 ? arguments[1] : undefined;\n    return new I18n(options, callback);\n  }\n  cloneInstance() {\n    let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', function (event) {\n      for (var _len12 = arguments.length, args = new Array(_len12 > 1 ? _len12 - 1 : 0), _key12 = 1; _key12 < _len12; _key12++) {\n        args[_key12 - 1] = arguments[_key12];\n      }\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };", "map": {"version": 3, "names": ["isString", "obj", "defer", "res", "rej", "promise", "Promise", "resolve", "reject", "makeString", "object", "copy", "a", "s", "t", "for<PERSON>ach", "m", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "key", "indexOf", "replace", "canNotTraverseDeeper", "getLastOfPath", "path", "Empty", "stack", "split", "stackIndex", "length", "Object", "prototype", "hasOwnProperty", "call", "k", "set<PERSON>ath", "newValue", "undefined", "e", "p", "slice", "last", "push<PERSON><PERSON>", "concat", "push", "<PERSON><PERSON><PERSON>", "getPathWithDefaults", "data", "defaultData", "value", "deepExtend", "target", "source", "overwrite", "prop", "String", "regexEscape", "str", "_entityMap", "escape", "RegExpCache", "constructor", "capacity", "regExpMap", "Map", "regExpQueue", "getRegExp", "pattern", "regExpFromCache", "get", "regExpNew", "RegExp", "delete", "shift", "set", "chars", "looksLikeObjectPathRegExpCache", "looksLikeObjectPath", "nsSeparator", "keySeparator", "possibleChars", "filter", "c", "r", "map", "join", "matched", "test", "ki", "substring", "deepFind", "arguments", "tokens", "current", "i", "next", "nextPath", "j", "getCleanedCode", "code", "consoleLogger", "type", "log", "args", "output", "warn", "error", "console", "apply", "<PERSON><PERSON>", "concreteLogger", "options", "init", "prefix", "logger", "debug", "_len", "Array", "_key", "forward", "_len2", "_key2", "_len3", "_key3", "deprecate", "_len4", "_key4", "lvl", "debugOnly", "create", "moduleName", "clone", "baseLogger", "EventEmitter", "observers", "on", "events", "listener", "event", "numListeners", "off", "emit", "_len5", "_key5", "cloned", "from", "entries", "_ref", "observer", "numTimesAdded", "_ref2", "ResourceStore", "ns", "defaultNS", "ignoreJSONStructure", "addNamespaces", "removeNamespaces", "index", "splice", "getResource", "lng", "isArray", "result", "addResource", "silent", "addResources", "resources", "addResourceBundle", "deep", "skipCopy", "pack", "JSON", "parse", "stringify", "removeResourceBundle", "hasResourceBundle", "getResourceBundle", "getDataByLanguage", "hasLanguageSomeTranslations", "n", "keys", "find", "v", "toJSON", "postProcessor", "processors", "addPostProcessor", "module", "name", "handle", "translator", "processor", "process", "checkedLoadedFor", "shouldHandleAsObject", "Translator", "services", "changeLanguage", "language", "exists", "o", "interpolation", "opt", "resolved", "extractFromKey", "namespaces", "wouldCheckForNsInKey", "seemsNaturalLanguage", "userDefinedKeySeparator", "userDefinedNsSeparator", "match", "interpolator", "nestingRegexp", "parts", "translate", "last<PERSON>ey", "overloadTranslationOptionHandler", "returnDetails", "namespace", "appendNamespaceToCIMode", "toLowerCase", "usedKey", "exactUsed<PERSON>ey", "usedLng", "usedNS", "usedParams", "getUsedParamsDetails", "resUsed<PERSON><PERSON>", "resExactUsedKey", "noObject", "joinArrays", "handleAsObjectInI18nFormat", "i18nFormat", "handleAsObject", "needsPluralHandling", "count", "hasDefaultValue", "defaultValueSuffix", "pluralResolver", "getSuffix", "defaultValueSuffixOrdinalFallback", "ordinal", "needsZeroSuffixLookup", "defaultValue", "pluralSeparator", "resForObjHndl", "resType", "toString", "returnObjects", "returnedObjectHandler", "resTypeIsArray", "newKeyToUse", "<PERSON><PERSON><PERSON>", "extendTranslation", "usedDefault", "isValidLookup", "missingKeyNoValueFallbackToKey", "resForMissing", "updateMissing", "fk", "lngs", "fallbackLngs", "languageUtils", "getFallbackCodes", "fallbackLng", "saveMissingTo", "toResolveHierarchy", "send", "l", "specificDefaultValue", "defaultForMissing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backendConnector", "saveMissing", "saveMissingPlurals", "suffixes", "getSuffixes", "suffix", "appendNamespaceToMissingKey", "parseMissingKeyHandler", "_this", "defaultVariables", "skipInterpolation", "skipOnVariables", "nestBef", "nb", "interpolate", "na", "nestAft", "nest", "_len6", "_key6", "context", "reset", "postProcess", "postProcessorNames", "applyPostProcessor", "postProcessPassResolved", "i18nResolved", "found", "extracted", "fallbackNS", "needsContextHandling", "codes", "utils", "hasLoadedNamespace", "finalKeys", "addLookupKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "contextSeparator", "<PERSON><PERSON><PERSON>", "pop", "returnNull", "returnEmptyString", "resourceStore", "optionsKeys", "useOptionsReplaceForData", "option", "LanguageUtil", "supportedLngs", "getScriptPartFromCode", "formatLanguageCode", "getLanguagePartFromCode", "formattedCode", "Intl", "getCanonicalLocales", "lowerCaseLng", "cleanCode", "isSupportedCode", "load", "nonExplicitSupportedLngs", "getBestMatchFromCodes", "cleanedLng", "lngScOnly", "lngOnly", "supportedLng", "fallbacks", "default", "fallbackCode", "fallbackCodes", "addCode", "fc", "suffixesOrder", "zero", "one", "two", "few", "many", "other", "dummyRule", "select", "resolvedOptions", "pluralCategories", "PluralResolver", "pluralRulesCache", "addRule", "rules", "clearCache", "getRule", "cleanedCode", "cache<PERSON>ey", "rule", "PluralRules", "err", "lngPart", "needsPlural", "getPluralFormsOfKey", "sort", "pluralCategory1", "pluralCategory2", "pluralCategory", "prepend", "deepFindWithDefaults", "regexSafe", "val", "Interpolator", "format", "escapeValue", "escape$1", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "resetRegExp", "getOrResetRegExp", "existingRegExp", "lastIndex", "regexp", "regexpUnescape", "replaces", "handleFormat", "interpolationkey", "trim", "f", "missingInterpolationHandler", "todos", "regex", "safeValue", "todo", "exec", "matchedVar", "temp", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "keyEndIndex", "lastIndexOf", "elem", "Boolean", "reduce", "parseFormatStr", "formatStr", "formatName", "formatOptions", "optStr", "currency", "range", "opts", "rest", "<PERSON><PERSON><PERSON>", "isNaN", "parseInt", "createCachedFormatter", "fn", "cache", "optForCache", "formatParams", "frm", "createNonCachedFormatter", "<PERSON><PERSON><PERSON>", "cf", "cacheInBuiltFormats", "formats", "number", "formatter", "NumberFormat", "style", "datetime", "DateTimeFormat", "relativetime", "RelativeTimeFormat", "list", "ListFormat", "add", "addCached", "findIndex", "mem", "formatted", "valOptions", "locale", "removePending", "q", "pending", "pendingCount", "Connector", "backend", "store", "waitingReads", "maxP<PERSON>llelReads", "readingCalls", "maxRetries", "retryTimeout", "state", "queue", "queueLoad", "languages", "callback", "toLoad", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "reload", "loaded", "errors", "done", "loadedKeys", "read", "fcName", "tried", "wait", "resolver", "setTimeout", "bind", "then", "catch", "prepareLoading", "loadOne", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "initAsync", "preload", "simplifyPluralSuffix", "partialBundledLanguages", "ret", "tDescription", "transformOptions", "initImmediate", "noop", "bindMemberFunctions", "inst", "mems", "getOwnPropertyNames", "getPrototypeOf", "I18n", "modules", "external", "isInitialized", "isClone", "_this2", "isInitializing", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "usingLegacyFormatFunction", "_len7", "_key7", "languageDetector", "detection", "_len8", "_key8", "storeApi", "storeApiChained", "deferred", "finish", "initializedStoreOnce", "loadResources", "usedCallback", "append", "resolvedLanguage", "setResolvedLanguage", "reloadResources", "use", "Error", "li", "lngInLngs", "unshift", "_this3", "isLanguageChangingTo", "setLngProps", "setLng", "fl", "cacheUserLanguage", "async", "detect", "getFixedT", "keyPrefix", "_this4", "fixedT", "_len9", "_key9", "<PERSON><PERSON><PERSON>", "_len10", "_key10", "_len11", "_key11", "setDefaultNamespace", "lastLng", "loadNotPending", "loadState", "precheck", "preResult", "loadNamespaces", "loadLanguages", "preloaded", "newLngs", "dir", "Locale", "getTextInfo", "ti", "direction", "rtlLngs", "createInstance", "cloneInstance", "forkResourceStore", "mergedOptions", "membersToCopy", "clonedData", "prev", "acc", "_len12", "_key12", "instance"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/node_modules/i18next/dist/esm/i18next.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      const keyEndIndex = /{.*}/.test(match[1]) ? match[1].lastIndexOf('}') + 1 : match[1].indexOf(this.formatSeparator);\n      if (keyEndIndex !== -1) {\n        formatters = match[1].slice(keyEndIndex).split(this.formatSeparator).map(elem => elem.trim()).filter(Boolean);\n        match[1] = match[1].slice(0, keyEndIndex);\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (formatters.length) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      const usingLegacyFormatFunction = this.options.interpolation.format && this.options.interpolation.format !== defOpts.interpolation.format;\n      if (usingLegacyFormatFunction) {\n        this.logger.warn(`init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting`);\n      }\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        if (s.formatter.init) s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    try {\n      const l = new Intl.Locale(lng);\n      if (l && l.getTextInfo) {\n        const ti = l.getTextInfo();\n        if (ti && ti.direction) return ti.direction;\n      }\n    } catch (e) {}\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    if (lng.toLowerCase().indexOf('-latn') > 1) return 'ltr';\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ;AAC/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,IAAIC,GAAG;EACP,IAAIC,GAAG;EACP,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC/CL,GAAG,GAAGI,OAAO;IACbH,GAAG,GAAGI,MAAM;EACd,CAAC,CAAC;EACFH,OAAO,CAACE,OAAO,GAAGJ,GAAG;EACrBE,OAAO,CAACG,MAAM,GAAGJ,GAAG;EACpB,OAAOC,OAAO;AAChB,CAAC;AACD,MAAMI,UAAU,GAAGC,MAAM,IAAI;EAC3B,IAAIA,MAAM,IAAI,IAAI,EAAE,OAAO,EAAE;EAC7B,OAAO,EAAE,GAAGA,MAAM;AACpB,CAAC;AACD,MAAMC,IAAI,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EACxBF,CAAC,CAACG,OAAO,CAACC,CAAC,IAAI;IACb,IAAIH,CAAC,CAACG,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACvB,CAAC,CAAC;AACJ,CAAC;AACD,MAAMC,yBAAyB,GAAG,MAAM;AACxC,MAAMC,QAAQ,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGD,GAAG,CAACE,OAAO,CAACJ,yBAAyB,EAAE,GAAG,CAAC,GAAGE,GAAG;AAC1G,MAAMG,oBAAoB,GAAGZ,MAAM,IAAI,CAACA,MAAM,IAAIV,QAAQ,CAACU,MAAM,CAAC;AAClE,MAAMa,aAAa,GAAGA,CAACb,MAAM,EAAEc,IAAI,EAAEC,KAAK,KAAK;EAC7C,MAAMC,KAAK,GAAG,CAAC1B,QAAQ,CAACwB,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;EACtD,IAAIC,UAAU,GAAG,CAAC;EAClB,OAAOA,UAAU,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;IACpC,IAAIP,oBAAoB,CAACZ,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3C,MAAMS,GAAG,GAAGD,QAAQ,CAACQ,KAAK,CAACE,UAAU,CAAC,CAAC;IACvC,IAAI,CAAClB,MAAM,CAACS,GAAG,CAAC,IAAIM,KAAK,EAAEf,MAAM,CAACS,GAAG,CAAC,GAAG,IAAIM,KAAK,CAAC,CAAC;IACpD,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACvB,MAAM,EAAES,GAAG,CAAC,EAAE;MACrDT,MAAM,GAAGA,MAAM,CAACS,GAAG,CAAC;IACtB,CAAC,MAAM;MACLT,MAAM,GAAG,CAAC,CAAC;IACb;IACA,EAAEkB,UAAU;EACd;EACA,IAAIN,oBAAoB,CAACZ,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;EAC3C,OAAO;IACLT,GAAG,EAAES,MAAM;IACXwB,CAAC,EAAEhB,QAAQ,CAACQ,KAAK,CAACE,UAAU,CAAC;EAC/B,CAAC;AACH,CAAC;AACD,MAAMO,OAAO,GAAGA,CAACzB,MAAM,EAAEc,IAAI,EAAEY,QAAQ,KAAK;EAC1C,MAAM;IACJnC,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,EAAEM,MAAM,CAAC;EACvC,IAAI7B,GAAG,KAAKoC,SAAS,IAAIb,IAAI,CAACK,MAAM,KAAK,CAAC,EAAE;IAC1C5B,GAAG,CAACiC,CAAC,CAAC,GAAGE,QAAQ;IACjB;EACF;EACA,IAAIE,CAAC,GAAGd,IAAI,CAACA,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EAC7B,IAAIU,CAAC,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAEhB,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EACtC,IAAIY,IAAI,GAAGlB,aAAa,CAACb,MAAM,EAAE6B,CAAC,EAAET,MAAM,CAAC;EAC3C,OAAOW,IAAI,CAACxC,GAAG,KAAKoC,SAAS,IAAIE,CAAC,CAACV,MAAM,EAAE;IACzCS,CAAC,GAAG,GAAGC,CAAC,CAACA,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC,IAAIS,CAAC,EAAE;IAC7BC,CAAC,GAAGA,CAAC,CAACC,KAAK,CAAC,CAAC,EAAED,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;IAC5BY,IAAI,GAAGlB,aAAa,CAACb,MAAM,EAAE6B,CAAC,EAAET,MAAM,CAAC;IACvC,IAAIW,IAAI,EAAExC,GAAG,IAAI,OAAOwC,IAAI,CAACxC,GAAG,CAAC,GAAGwC,IAAI,CAACP,CAAC,IAAII,CAAC,EAAE,CAAC,KAAK,WAAW,EAAE;MAClEG,IAAI,CAACxC,GAAG,GAAGoC,SAAS;IACtB;EACF;EACAI,IAAI,CAACxC,GAAG,CAAC,GAAGwC,IAAI,CAACP,CAAC,IAAII,CAAC,EAAE,CAAC,GAAGF,QAAQ;AACvC,CAAC;AACD,MAAMM,QAAQ,GAAGA,CAAChC,MAAM,EAAEc,IAAI,EAAEY,QAAQ,EAAEO,MAAM,KAAK;EACnD,MAAM;IACJ1C,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,EAAEM,MAAM,CAAC;EACvC7B,GAAG,CAACiC,CAAC,CAAC,GAAGjC,GAAG,CAACiC,CAAC,CAAC,IAAI,EAAE;EACrBjC,GAAG,CAACiC,CAAC,CAAC,CAACU,IAAI,CAACR,QAAQ,CAAC;AACvB,CAAC;AACD,MAAMS,OAAO,GAAGA,CAACnC,MAAM,EAAEc,IAAI,KAAK;EAChC,MAAM;IACJvB,GAAG;IACHiC;EACF,CAAC,GAAGX,aAAa,CAACb,MAAM,EAAEc,IAAI,CAAC;EAC/B,IAAI,CAACvB,GAAG,EAAE,OAAOoC,SAAS;EAC1B,IAAI,CAACP,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChC,GAAG,EAAEiC,CAAC,CAAC,EAAE,OAAOG,SAAS;EACnE,OAAOpC,GAAG,CAACiC,CAAC,CAAC;AACf,CAAC;AACD,MAAMY,mBAAmB,GAAGA,CAACC,IAAI,EAAEC,WAAW,EAAE7B,GAAG,KAAK;EACtD,MAAM8B,KAAK,GAAGJ,OAAO,CAACE,IAAI,EAAE5B,GAAG,CAAC;EAChC,IAAI8B,KAAK,KAAKZ,SAAS,EAAE;IACvB,OAAOY,KAAK;EACd;EACA,OAAOJ,OAAO,CAACG,WAAW,EAAE7B,GAAG,CAAC;AAClC,CAAC;AACD,MAAM+B,UAAU,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,SAAS,KAAK;EAChD,KAAK,MAAMC,IAAI,IAAIF,MAAM,EAAE;IACzB,IAAIE,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,aAAa,EAAE;MAClD,IAAIA,IAAI,IAAIH,MAAM,EAAE;QAClB,IAAInD,QAAQ,CAACmD,MAAM,CAACG,IAAI,CAAC,CAAC,IAAIH,MAAM,CAACG,IAAI,CAAC,YAAYC,MAAM,IAAIvD,QAAQ,CAACoD,MAAM,CAACE,IAAI,CAAC,CAAC,IAAIF,MAAM,CAACE,IAAI,CAAC,YAAYC,MAAM,EAAE;UACxH,IAAIF,SAAS,EAAEF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;QAC5C,CAAC,MAAM;UACLJ,UAAU,CAACC,MAAM,CAACG,IAAI,CAAC,EAAEF,MAAM,CAACE,IAAI,CAAC,EAAED,SAAS,CAAC;QACnD;MACF,CAAC,MAAM;QACLF,MAAM,CAACG,IAAI,CAAC,GAAGF,MAAM,CAACE,IAAI,CAAC;MAC7B;IACF;EACF;EACA,OAAOH,MAAM;AACf,CAAC;AACD,MAAMK,WAAW,GAAGC,GAAG,IAAIA,GAAG,CAACpC,OAAO,CAAC,qCAAqC,EAAE,MAAM,CAAC;AACrF,IAAIqC,UAAU,GAAG;EACf,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,MAAM,GAAGZ,IAAI,IAAI;EACrB,IAAI/C,QAAQ,CAAC+C,IAAI,CAAC,EAAE;IAClB,OAAOA,IAAI,CAAC1B,OAAO,CAAC,YAAY,EAAER,CAAC,IAAI6C,UAAU,CAAC7C,CAAC,CAAC,CAAC;EACvD;EACA,OAAOkC,IAAI;AACb,CAAC;AACD,MAAMa,WAAW,CAAC;EAChBC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,EAAE;EACvB;EACAC,SAASA,CAACC,OAAO,EAAE;IACjB,MAAMC,eAAe,GAAG,IAAI,CAACL,SAAS,CAACM,GAAG,CAACF,OAAO,CAAC;IACnD,IAAIC,eAAe,KAAK/B,SAAS,EAAE;MACjC,OAAO+B,eAAe;IACxB;IACA,MAAME,SAAS,GAAG,IAAIC,MAAM,CAACJ,OAAO,CAAC;IACrC,IAAI,IAAI,CAACF,WAAW,CAACpC,MAAM,KAAK,IAAI,CAACiC,QAAQ,EAAE;MAC7C,IAAI,CAACC,SAAS,CAACS,MAAM,CAAC,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC,CAAC,CAAC;IACjD;IACA,IAAI,CAACV,SAAS,CAACW,GAAG,CAACP,OAAO,EAAEG,SAAS,CAAC;IACtC,IAAI,CAACL,WAAW,CAACrB,IAAI,CAACuB,OAAO,CAAC;IAC9B,OAAOG,SAAS;EAClB;AACF;AACA,MAAMK,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACvC,MAAMC,8BAA8B,GAAG,IAAIhB,WAAW,CAAC,EAAE,CAAC;AAC1D,MAAMiB,mBAAmB,GAAGA,CAAC1D,GAAG,EAAE2D,WAAW,EAAEC,YAAY,KAAK;EAC9DD,WAAW,GAAGA,WAAW,IAAI,EAAE;EAC/BC,YAAY,GAAGA,YAAY,IAAI,EAAE;EACjC,MAAMC,aAAa,GAAGL,KAAK,CAACM,MAAM,CAACC,CAAC,IAAIJ,WAAW,CAAC1D,OAAO,CAAC8D,CAAC,CAAC,GAAG,CAAC,IAAIH,YAAY,CAAC3D,OAAO,CAAC8D,CAAC,CAAC,GAAG,CAAC,CAAC;EAClG,IAAIF,aAAa,CAACnD,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAC3C,MAAMsD,CAAC,GAAGP,8BAA8B,CAACV,SAAS,CAAC,IAAIc,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,KAAK,GAAG,GAAG,KAAK,GAAGA,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EAClH,IAAIC,OAAO,GAAG,CAACH,CAAC,CAACI,IAAI,CAACpE,GAAG,CAAC;EAC1B,IAAI,CAACmE,OAAO,EAAE;IACZ,MAAME,EAAE,GAAGrE,GAAG,CAACC,OAAO,CAAC2D,YAAY,CAAC;IACpC,IAAIS,EAAE,GAAG,CAAC,IAAI,CAACL,CAAC,CAACI,IAAI,CAACpE,GAAG,CAACsE,SAAS,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,EAAE;MAC3CF,OAAO,GAAG,IAAI;IAChB;EACF;EACA,OAAOA,OAAO;AAChB,CAAC;AACD,MAAMI,QAAQ,GAAG,SAAAA,CAACzF,GAAG,EAAEuB,IAAI,EAAyB;EAAA,IAAvBuD,YAAY,GAAAY,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,GAAG;EAC7C,IAAI,CAAC1F,GAAG,EAAE,OAAOoC,SAAS;EAC1B,IAAIpC,GAAG,CAACuB,IAAI,CAAC,EAAE;IACb,IAAI,CAACM,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChC,GAAG,EAAEuB,IAAI,CAAC,EAAE,OAAOa,SAAS;IACtE,OAAOpC,GAAG,CAACuB,IAAI,CAAC;EAClB;EACA,MAAMoE,MAAM,GAAGpE,IAAI,CAACG,KAAK,CAACoD,YAAY,CAAC;EACvC,IAAIc,OAAO,GAAG5F,GAAG;EACjB,KAAK,IAAI6F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAC/D,MAAM,GAAG;IAClC,IAAI,CAACgE,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC3C,OAAOxD,SAAS;IAClB;IACA,IAAI0D,IAAI;IACR,IAAIC,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAGH,CAAC,EAAEG,CAAC,GAAGL,MAAM,CAAC/D,MAAM,EAAE,EAAEoE,CAAC,EAAE;MACtC,IAAIA,CAAC,KAAKH,CAAC,EAAE;QACXE,QAAQ,IAAIjB,YAAY;MAC1B;MACAiB,QAAQ,IAAIJ,MAAM,CAACK,CAAC,CAAC;MACrBF,IAAI,GAAGF,OAAO,CAACG,QAAQ,CAAC;MACxB,IAAID,IAAI,KAAK1D,SAAS,EAAE;QACtB,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACjB,OAAO,CAAC,OAAO2E,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIE,CAAC,GAAGL,MAAM,CAAC/D,MAAM,GAAG,CAAC,EAAE;UACtF;QACF;QACAiE,CAAC,IAAIG,CAAC,GAAGH,CAAC,GAAG,CAAC;QACd;MACF;IACF;IACAD,OAAO,GAAGE,IAAI;EAChB;EACA,OAAOF,OAAO;AAChB,CAAC;AACD,MAAMK,cAAc,GAAGC,IAAI,IAAIA,IAAI,EAAE9E,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAEtD,MAAM+E,aAAa,GAAG;EACpBC,IAAI,EAAE,QAAQ;EACdC,GAAGA,CAACC,IAAI,EAAE;IACR,IAAI,CAACC,MAAM,CAAC,KAAK,EAAED,IAAI,CAAC;EAC1B,CAAC;EACDE,IAAIA,CAACF,IAAI,EAAE;IACT,IAAI,CAACC,MAAM,CAAC,MAAM,EAAED,IAAI,CAAC;EAC3B,CAAC;EACDG,KAAKA,CAACH,IAAI,EAAE;IACV,IAAI,CAACC,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;EAC5B,CAAC;EACDC,MAAMA,CAACH,IAAI,EAAEE,IAAI,EAAE;IACjBI,OAAO,GAAGN,IAAI,CAAC,EAAEO,KAAK,GAAGD,OAAO,EAAEJ,IAAI,CAAC;EACzC;AACF,CAAC;AACD,MAAMM,MAAM,CAAC;EACXhD,WAAWA,CAACiD,cAAc,EAAgB;IAAA,IAAdC,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACtC,IAAI,CAACqB,IAAI,CAACF,cAAc,EAAEC,OAAO,CAAC;EACpC;EACAC,IAAIA,CAACF,cAAc,EAAgB;IAAA,IAAdC,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAC/B,IAAI,CAACsB,MAAM,GAAGF,OAAO,CAACE,MAAM,IAAI,UAAU;IAC1C,IAAI,CAACC,MAAM,GAAGJ,cAAc,IAAIV,aAAa;IAC7C,IAAI,CAACW,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;EAC5B;EACAb,GAAGA,CAAA,EAAU;IAAA,SAAAc,IAAA,GAAAzB,SAAA,CAAA9D,MAAA,EAAN0E,IAAI,OAAAc,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAJf,IAAI,CAAAe,IAAA,IAAA3B,SAAA,CAAA2B,IAAA;IAAA;IACT,OAAO,IAAI,CAACC,OAAO,CAAChB,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC;EAC5C;EACAE,IAAIA,CAAA,EAAU;IAAA,SAAAe,KAAA,GAAA7B,SAAA,CAAA9D,MAAA,EAAN0E,IAAI,OAAAc,KAAA,CAAAG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJlB,IAAI,CAAAkB,KAAA,IAAA9B,SAAA,CAAA8B,KAAA;IAAA;IACV,OAAO,IAAI,CAACF,OAAO,CAAChB,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;EAC7C;EACAG,KAAKA,CAAA,EAAU;IAAA,SAAAgB,KAAA,GAAA/B,SAAA,CAAA9D,MAAA,EAAN0E,IAAI,OAAAc,KAAA,CAAAK,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJpB,IAAI,CAAAoB,KAAA,IAAAhC,SAAA,CAAAgC,KAAA;IAAA;IACX,OAAO,IAAI,CAACJ,OAAO,CAAChB,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC;EACxC;EACAqB,SAASA,CAAA,EAAU;IAAA,SAAAC,KAAA,GAAAlC,SAAA,CAAA9D,MAAA,EAAN0E,IAAI,OAAAc,KAAA,CAAAQ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJvB,IAAI,CAAAuB,KAAA,IAAAnC,SAAA,CAAAmC,KAAA;IAAA;IACf,OAAO,IAAI,CAACP,OAAO,CAAChB,IAAI,EAAE,MAAM,EAAE,sBAAsB,EAAE,IAAI,CAAC;EACjE;EACAgB,OAAOA,CAAChB,IAAI,EAAEwB,GAAG,EAAEd,MAAM,EAAEe,SAAS,EAAE;IACpC,IAAIA,SAAS,IAAI,CAAC,IAAI,CAACb,KAAK,EAAE,OAAO,IAAI;IACzC,IAAInH,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,GAAG,GAAGU,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIV,IAAI,CAAC,CAAC,CAAC,EAAE;IACrE,OAAO,IAAI,CAACW,MAAM,CAACa,GAAG,CAAC,CAACxB,IAAI,CAAC;EAC/B;EACA0B,MAAMA,CAACC,UAAU,EAAE;IACjB,OAAO,IAAIrB,MAAM,CAAC,IAAI,CAACK,MAAM,EAAE;MAC7B,GAAG;QACDD,MAAM,EAAE,GAAG,IAAI,CAACA,MAAM,IAAIiB,UAAU;MACtC,CAAC;MACD,GAAG,IAAI,CAACnB;IACV,CAAC,CAAC;EACJ;EACAoB,KAAKA,CAACpB,OAAO,EAAE;IACbA,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACA,OAAO;IACjCA,OAAO,CAACE,MAAM,GAAGF,OAAO,CAACE,MAAM,IAAI,IAAI,CAACA,MAAM;IAC9C,OAAO,IAAIJ,MAAM,CAAC,IAAI,CAACK,MAAM,EAAEH,OAAO,CAAC;EACzC;AACF;AACA,IAAIqB,UAAU,GAAG,IAAIvB,MAAM,CAAC,CAAC;AAE7B,MAAMwB,YAAY,CAAC;EACjBxE,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACyE,SAAS,GAAG,CAAC,CAAC;EACrB;EACAC,EAAEA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IACnBD,MAAM,CAAC7G,KAAK,CAAC,GAAG,CAAC,CAACZ,OAAO,CAAC2H,KAAK,IAAI;MACjC,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,EAAE,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,GAAG,IAAI1E,GAAG,CAAC,CAAC;MAC7D,MAAM2E,YAAY,GAAG,IAAI,CAACL,SAAS,CAACI,KAAK,CAAC,CAACrE,GAAG,CAACoE,QAAQ,CAAC,IAAI,CAAC;MAC7D,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC,CAAChE,GAAG,CAAC+D,QAAQ,EAAEE,YAAY,GAAG,CAAC,CAAC;IACvD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACAC,GAAGA,CAACF,KAAK,EAAED,QAAQ,EAAE;IACnB,IAAI,CAAC,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC,EAAE;IAC5B,IAAI,CAACD,QAAQ,EAAE;MACb,OAAO,IAAI,CAACH,SAAS,CAACI,KAAK,CAAC;MAC5B;IACF;IACA,IAAI,CAACJ,SAAS,CAACI,KAAK,CAAC,CAAClE,MAAM,CAACiE,QAAQ,CAAC;EACxC;EACAI,IAAIA,CAACH,KAAK,EAAW;IAAA,SAAAI,KAAA,GAAAnD,SAAA,CAAA9D,MAAA,EAAN0E,IAAI,OAAAc,KAAA,CAAAyB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJxC,IAAI,CAAAwC,KAAA,QAAApD,SAAA,CAAAoD,KAAA;IAAA;IACjB,IAAI,IAAI,CAACT,SAAS,CAACI,KAAK,CAAC,EAAE;MACzB,MAAMM,MAAM,GAAG3B,KAAK,CAAC4B,IAAI,CAAC,IAAI,CAACX,SAAS,CAACI,KAAK,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC;MAC1DF,MAAM,CAACjI,OAAO,CAACoI,IAAA,IAA+B;QAAA,IAA9B,CAACC,QAAQ,EAAEC,aAAa,CAAC,GAAAF,IAAA;QACvC,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,aAAa,EAAEvD,CAAC,EAAE,EAAE;UACtCsD,QAAQ,CAAC,GAAG7C,IAAI,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAAC+B,SAAS,CAAC,GAAG,CAAC,EAAE;MACvB,MAAMU,MAAM,GAAG3B,KAAK,CAAC4B,IAAI,CAAC,IAAI,CAACX,SAAS,CAAC,GAAG,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;MACxDF,MAAM,CAACjI,OAAO,CAACuI,KAAA,IAA+B;QAAA,IAA9B,CAACF,QAAQ,EAAEC,aAAa,CAAC,GAAAC,KAAA;QACvC,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,aAAa,EAAEvD,CAAC,EAAE,EAAE;UACtCsD,QAAQ,CAACxC,KAAK,CAACwC,QAAQ,EAAE,CAACV,KAAK,EAAE,GAAGnC,IAAI,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;IACJ;EACF;AACF;AAEA,MAAMgD,aAAa,SAASlB,YAAY,CAAC;EACvCxE,WAAWA,CAACd,IAAI,EAGb;IAAA,IAHegE,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG;MAC1B6D,EAAE,EAAE,CAAC,aAAa,CAAC;MACnBC,SAAS,EAAE;IACb,CAAC;IACC,KAAK,CAAC,CAAC;IACP,IAAI,CAAC1G,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACtB,IAAI,CAACgE,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAAChC,YAAY,KAAK1C,SAAS,EAAE;MAC3C,IAAI,CAAC0E,OAAO,CAAChC,YAAY,GAAG,GAAG;IACjC;IACA,IAAI,IAAI,CAACgC,OAAO,CAAC2C,mBAAmB,KAAKrH,SAAS,EAAE;MAClD,IAAI,CAAC0E,OAAO,CAAC2C,mBAAmB,GAAG,IAAI;IACzC;EACF;EACAC,aAAaA,CAACH,EAAE,EAAE;IAChB,IAAI,IAAI,CAACzC,OAAO,CAACyC,EAAE,CAACpI,OAAO,CAACoI,EAAE,CAAC,GAAG,CAAC,EAAE;MACnC,IAAI,CAACzC,OAAO,CAACyC,EAAE,CAAC5G,IAAI,CAAC4G,EAAE,CAAC;IAC1B;EACF;EACAI,gBAAgBA,CAACJ,EAAE,EAAE;IACnB,MAAMK,KAAK,GAAG,IAAI,CAAC9C,OAAO,CAACyC,EAAE,CAACpI,OAAO,CAACoI,EAAE,CAAC;IACzC,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC9C,OAAO,CAACyC,EAAE,CAACM,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAClC;EACF;EACAE,WAAWA,CAACC,GAAG,EAAER,EAAE,EAAErI,GAAG,EAAgB;IAAA,IAAd4F,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACpC,MAAMZ,YAAY,GAAGgC,OAAO,CAAChC,YAAY,KAAK1C,SAAS,GAAG0E,OAAO,CAAChC,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAC1G,MAAM2E,mBAAmB,GAAG3C,OAAO,CAAC2C,mBAAmB,KAAKrH,SAAS,GAAG0E,OAAO,CAAC2C,mBAAmB,GAAG,IAAI,CAAC3C,OAAO,CAAC2C,mBAAmB;IACtI,IAAIlI,IAAI;IACR,IAAIwI,GAAG,CAAC5I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAGwI,GAAG,CAACrI,KAAK,CAAC,GAAG,CAAC;IACvB,CAAC,MAAM;MACLH,IAAI,GAAG,CAACwI,GAAG,EAAER,EAAE,CAAC;MAChB,IAAIrI,GAAG,EAAE;QACP,IAAIkG,KAAK,CAAC4C,OAAO,CAAC9I,GAAG,CAAC,EAAE;UACtBK,IAAI,CAACoB,IAAI,CAAC,GAAGzB,GAAG,CAAC;QACnB,CAAC,MAAM,IAAInB,QAAQ,CAACmB,GAAG,CAAC,IAAI4D,YAAY,EAAE;UACxCvD,IAAI,CAACoB,IAAI,CAAC,GAAGzB,GAAG,CAACQ,KAAK,CAACoD,YAAY,CAAC,CAAC;QACvC,CAAC,MAAM;UACLvD,IAAI,CAACoB,IAAI,CAACzB,GAAG,CAAC;QAChB;MACF;IACF;IACA,MAAM+I,MAAM,GAAGrH,OAAO,CAAC,IAAI,CAACE,IAAI,EAAEvB,IAAI,CAAC;IACvC,IAAI,CAAC0I,MAAM,IAAI,CAACV,EAAE,IAAI,CAACrI,GAAG,IAAI6I,GAAG,CAAC5I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACnD4I,GAAG,GAAGxI,IAAI,CAAC,CAAC,CAAC;MACbgI,EAAE,GAAGhI,IAAI,CAAC,CAAC,CAAC;MACZL,GAAG,GAAGK,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC6C,IAAI,CAAC,GAAG,CAAC;IAC/B;IACA,IAAI6E,MAAM,IAAI,CAACR,mBAAmB,IAAI,CAAC1J,QAAQ,CAACmB,GAAG,CAAC,EAAE,OAAO+I,MAAM;IACnE,OAAOxE,QAAQ,CAAC,IAAI,CAAC3C,IAAI,GAAGiH,GAAG,CAAC,GAAGR,EAAE,CAAC,EAAErI,GAAG,EAAE4D,YAAY,CAAC;EAC5D;EACAoF,WAAWA,CAACH,GAAG,EAAER,EAAE,EAAErI,GAAG,EAAE8B,KAAK,EAE5B;IAAA,IAF8B8D,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG;MACzCyE,MAAM,EAAE;IACV,CAAC;IACC,MAAMrF,YAAY,GAAGgC,OAAO,CAAChC,YAAY,KAAK1C,SAAS,GAAG0E,OAAO,CAAChC,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAC1G,IAAIvD,IAAI,GAAG,CAACwI,GAAG,EAAER,EAAE,CAAC;IACpB,IAAIrI,GAAG,EAAEK,IAAI,GAAGA,IAAI,CAACmB,MAAM,CAACoC,YAAY,GAAG5D,GAAG,CAACQ,KAAK,CAACoD,YAAY,CAAC,GAAG5D,GAAG,CAAC;IACzE,IAAI6I,GAAG,CAAC5I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAGwI,GAAG,CAACrI,KAAK,CAAC,GAAG,CAAC;MACrBsB,KAAK,GAAGuG,EAAE;MACVA,EAAE,GAAGhI,IAAI,CAAC,CAAC,CAAC;IACd;IACA,IAAI,CAACmI,aAAa,CAACH,EAAE,CAAC;IACtBrH,OAAO,CAAC,IAAI,CAACY,IAAI,EAAEvB,IAAI,EAAEyB,KAAK,CAAC;IAC/B,IAAI,CAAC8D,OAAO,CAACqD,MAAM,EAAE,IAAI,CAACvB,IAAI,CAAC,OAAO,EAAEmB,GAAG,EAAER,EAAE,EAAErI,GAAG,EAAE8B,KAAK,CAAC;EAC9D;EACAoH,YAAYA,CAACL,GAAG,EAAER,EAAE,EAAEc,SAAS,EAE5B;IAAA,IAF8BvD,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG;MACzCyE,MAAM,EAAE;IACV,CAAC;IACC,KAAK,MAAMpJ,CAAC,IAAIsJ,SAAS,EAAE;MACzB,IAAItK,QAAQ,CAACsK,SAAS,CAACtJ,CAAC,CAAC,CAAC,IAAIqG,KAAK,CAAC4C,OAAO,CAACK,SAAS,CAACtJ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACmJ,WAAW,CAACH,GAAG,EAAER,EAAE,EAAExI,CAAC,EAAEsJ,SAAS,CAACtJ,CAAC,CAAC,EAAE;QACpGoJ,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA,IAAI,CAACrD,OAAO,CAACqD,MAAM,EAAE,IAAI,CAACvB,IAAI,CAAC,OAAO,EAAEmB,GAAG,EAAER,EAAE,EAAEc,SAAS,CAAC;EAC7D;EACAC,iBAAiBA,CAACP,GAAG,EAAER,EAAE,EAAEc,SAAS,EAAEE,IAAI,EAAEnH,SAAS,EAGlD;IAAA,IAHoD0D,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG;MAC/DyE,MAAM,EAAE,KAAK;MACbK,QAAQ,EAAE;IACZ,CAAC;IACC,IAAIjJ,IAAI,GAAG,CAACwI,GAAG,EAAER,EAAE,CAAC;IACpB,IAAIQ,GAAG,CAAC5I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACzBI,IAAI,GAAGwI,GAAG,CAACrI,KAAK,CAAC,GAAG,CAAC;MACrB6I,IAAI,GAAGF,SAAS;MAChBA,SAAS,GAAGd,EAAE;MACdA,EAAE,GAAGhI,IAAI,CAAC,CAAC,CAAC;IACd;IACA,IAAI,CAACmI,aAAa,CAACH,EAAE,CAAC;IACtB,IAAIkB,IAAI,GAAG7H,OAAO,CAAC,IAAI,CAACE,IAAI,EAAEvB,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,CAACuF,OAAO,CAAC0D,QAAQ,EAAEH,SAAS,GAAGK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACP,SAAS,CAAC,CAAC;IACxE,IAAIE,IAAI,EAAE;MACRtH,UAAU,CAACwH,IAAI,EAAEJ,SAAS,EAAEjH,SAAS,CAAC;IACxC,CAAC,MAAM;MACLqH,IAAI,GAAG;QACL,GAAGA,IAAI;QACP,GAAGJ;MACL,CAAC;IACH;IACAnI,OAAO,CAAC,IAAI,CAACY,IAAI,EAAEvB,IAAI,EAAEkJ,IAAI,CAAC;IAC9B,IAAI,CAAC3D,OAAO,CAACqD,MAAM,EAAE,IAAI,CAACvB,IAAI,CAAC,OAAO,EAAEmB,GAAG,EAAER,EAAE,EAAEc,SAAS,CAAC;EAC7D;EACAQ,oBAAoBA,CAACd,GAAG,EAAER,EAAE,EAAE;IAC5B,IAAI,IAAI,CAACuB,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE;MACnC,OAAO,IAAI,CAACzG,IAAI,CAACiH,GAAG,CAAC,CAACR,EAAE,CAAC;IAC3B;IACA,IAAI,CAACI,gBAAgB,CAACJ,EAAE,CAAC;IACzB,IAAI,CAACX,IAAI,CAAC,SAAS,EAAEmB,GAAG,EAAER,EAAE,CAAC;EAC/B;EACAuB,iBAAiBA,CAACf,GAAG,EAAER,EAAE,EAAE;IACzB,OAAO,IAAI,CAACO,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC,KAAKnH,SAAS;EAChD;EACA2I,iBAAiBA,CAAChB,GAAG,EAAER,EAAE,EAAE;IACzB,IAAI,CAACA,EAAE,EAAEA,EAAE,GAAG,IAAI,CAACzC,OAAO,CAAC0C,SAAS;IACpC,OAAO,IAAI,CAACM,WAAW,CAACC,GAAG,EAAER,EAAE,CAAC;EAClC;EACAyB,iBAAiBA,CAACjB,GAAG,EAAE;IACrB,OAAO,IAAI,CAACjH,IAAI,CAACiH,GAAG,CAAC;EACvB;EACAkB,2BAA2BA,CAAClB,GAAG,EAAE;IAC/B,MAAMjH,IAAI,GAAG,IAAI,CAACkI,iBAAiB,CAACjB,GAAG,CAAC;IACxC,MAAMmB,CAAC,GAAGpI,IAAI,IAAIjB,MAAM,CAACsJ,IAAI,CAACrI,IAAI,CAAC,IAAI,EAAE;IACzC,OAAO,CAAC,CAACoI,CAAC,CAACE,IAAI,CAACC,CAAC,IAAIvI,IAAI,CAACuI,CAAC,CAAC,IAAIxJ,MAAM,CAACsJ,IAAI,CAACrI,IAAI,CAACuI,CAAC,CAAC,CAAC,CAACzJ,MAAM,GAAG,CAAC,CAAC;EAClE;EACA0J,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxI,IAAI;EAClB;AACF;AAEA,IAAIyI,aAAa,GAAG;EAClBC,UAAU,EAAE,CAAC,CAAC;EACdC,gBAAgBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACF,UAAU,CAACE,MAAM,CAACC,IAAI,CAAC,GAAGD,MAAM;EACvC,CAAC;EACDE,MAAMA,CAACJ,UAAU,EAAExI,KAAK,EAAE9B,GAAG,EAAE4F,OAAO,EAAE+E,UAAU,EAAE;IAClDL,UAAU,CAAC1K,OAAO,CAACgL,SAAS,IAAI;MAC9B9I,KAAK,GAAG,IAAI,CAACwI,UAAU,CAACM,SAAS,CAAC,EAAEC,OAAO,CAAC/I,KAAK,EAAE9B,GAAG,EAAE4F,OAAO,EAAE+E,UAAU,CAAC,IAAI7I,KAAK;IACvF,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;AACF,CAAC;AAED,MAAMgJ,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAMC,oBAAoB,GAAG/L,GAAG,IAAI,CAACH,QAAQ,CAACG,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,QAAQ;AACzG,MAAMgM,UAAU,SAAS9D,YAAY,CAAC;EACpCxE,WAAWA,CAACuI,QAAQ,EAAgB;IAAA,IAAdrF,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAChC,KAAK,CAAC,CAAC;IACPhF,IAAI,CAAC,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,OAAO,CAAC,EAAEyL,QAAQ,EAAE,IAAI,CAAC;IACrI,IAAI,CAACrF,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAAChC,YAAY,KAAK1C,SAAS,EAAE;MAC3C,IAAI,CAAC0E,OAAO,CAAChC,YAAY,GAAG,GAAG;IACjC;IACA,IAAI,CAACmC,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,YAAY,CAAC;EAC/C;EACAoE,cAAcA,CAACrC,GAAG,EAAE;IAClB,IAAIA,GAAG,EAAE,IAAI,CAACsC,QAAQ,GAAGtC,GAAG;EAC9B;EACAuC,MAAMA,CAACpL,GAAG,EAEP;IAAA,IAFSqL,CAAC,GAAA7G,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG;MACd8G,aAAa,EAAE,CAAC;IAClB,CAAC;IACC,MAAMC,GAAG,GAAG;MACV,GAAGF;IACL,CAAC;IACD,IAAIrL,GAAG,IAAI,IAAI,EAAE,OAAO,KAAK;IAC7B,MAAMwL,QAAQ,GAAG,IAAI,CAACpM,OAAO,CAACY,GAAG,EAAEuL,GAAG,CAAC;IACvC,OAAOC,QAAQ,EAAExM,GAAG,KAAKkC,SAAS;EACpC;EACAuK,cAAcA,CAACzL,GAAG,EAAEuL,GAAG,EAAE;IACvB,IAAI5H,WAAW,GAAG4H,GAAG,CAAC5H,WAAW,KAAKzC,SAAS,GAAGqK,GAAG,CAAC5H,WAAW,GAAG,IAAI,CAACiC,OAAO,CAACjC,WAAW;IAC5F,IAAIA,WAAW,KAAKzC,SAAS,EAAEyC,WAAW,GAAG,GAAG;IAChD,MAAMC,YAAY,GAAG2H,GAAG,CAAC3H,YAAY,KAAK1C,SAAS,GAAGqK,GAAG,CAAC3H,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAClG,IAAI8H,UAAU,GAAGH,GAAG,CAAClD,EAAE,IAAI,IAAI,CAACzC,OAAO,CAAC0C,SAAS,IAAI,EAAE;IACvD,MAAMqD,oBAAoB,GAAGhI,WAAW,IAAI3D,GAAG,CAACC,OAAO,CAAC0D,WAAW,CAAC,GAAG,CAAC,CAAC;IACzE,MAAMiI,oBAAoB,GAAG,CAAC,IAAI,CAAChG,OAAO,CAACiG,uBAAuB,IAAI,CAACN,GAAG,CAAC3H,YAAY,IAAI,CAAC,IAAI,CAACgC,OAAO,CAACkG,sBAAsB,IAAI,CAACP,GAAG,CAAC5H,WAAW,IAAI,CAACD,mBAAmB,CAAC1D,GAAG,EAAE2D,WAAW,EAAEC,YAAY,CAAC;IAC3M,IAAI+H,oBAAoB,IAAI,CAACC,oBAAoB,EAAE;MACjD,MAAM/L,CAAC,GAAGG,GAAG,CAAC+L,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;MACpD,IAAIpM,CAAC,IAAIA,CAAC,CAACa,MAAM,GAAG,CAAC,EAAE;QACrB,OAAO;UACLV,GAAG;UACH0L,UAAU,EAAE7M,QAAQ,CAAC6M,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA;QACpD,CAAC;MACH;MACA,MAAMQ,KAAK,GAAGlM,GAAG,CAACQ,KAAK,CAACmD,WAAW,CAAC;MACpC,IAAIA,WAAW,KAAKC,YAAY,IAAID,WAAW,KAAKC,YAAY,IAAI,IAAI,CAACgC,OAAO,CAACyC,EAAE,CAACpI,OAAO,CAACiM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAER,UAAU,GAAGQ,KAAK,CAAC5I,KAAK,CAAC,CAAC;MACtItD,GAAG,GAAGkM,KAAK,CAAChI,IAAI,CAACN,YAAY,CAAC;IAChC;IACA,OAAO;MACL5D,GAAG;MACH0L,UAAU,EAAE7M,QAAQ,CAAC6M,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA;IACpD,CAAC;EACH;EACAS,SAASA,CAAClC,IAAI,EAAEoB,CAAC,EAAEe,OAAO,EAAE;IAC1B,IAAIb,GAAG,GAAG,OAAOF,CAAC,KAAK,QAAQ,GAAG;MAChC,GAAGA;IACL,CAAC,GAAGA,CAAC;IACL,IAAI,OAAOE,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC3F,OAAO,CAACyG,gCAAgC,EAAE;MAC5Ed,GAAG,GAAG,IAAI,CAAC3F,OAAO,CAACyG,gCAAgC,CAAC7H,SAAS,CAAC;IAChE;IACA,IAAI,OAAOoB,OAAO,KAAK,QAAQ,EAAE2F,GAAG,GAAG;MACrC,GAAGA;IACL,CAAC;IACD,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;IAClB,IAAItB,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE;IAC3B,IAAI,CAAC/D,KAAK,CAAC4C,OAAO,CAACmB,IAAI,CAAC,EAAEA,IAAI,GAAG,CAAC7H,MAAM,CAAC6H,IAAI,CAAC,CAAC;IAC/C,MAAMqC,aAAa,GAAGf,GAAG,CAACe,aAAa,KAAKpL,SAAS,GAAGqK,GAAG,CAACe,aAAa,GAAG,IAAI,CAAC1G,OAAO,CAAC0G,aAAa;IACtG,MAAM1I,YAAY,GAAG2H,GAAG,CAAC3H,YAAY,KAAK1C,SAAS,GAAGqK,GAAG,CAAC3H,YAAY,GAAG,IAAI,CAACgC,OAAO,CAAChC,YAAY;IAClG,MAAM;MACJ5D,GAAG;MACH0L;IACF,CAAC,GAAG,IAAI,CAACD,cAAc,CAACxB,IAAI,CAACA,IAAI,CAACvJ,MAAM,GAAG,CAAC,CAAC,EAAE6K,GAAG,CAAC;IACnD,MAAMgB,SAAS,GAAGb,UAAU,CAACA,UAAU,CAAChL,MAAM,GAAG,CAAC,CAAC;IACnD,IAAIiD,WAAW,GAAG4H,GAAG,CAAC5H,WAAW,KAAKzC,SAAS,GAAGqK,GAAG,CAAC5H,WAAW,GAAG,IAAI,CAACiC,OAAO,CAACjC,WAAW;IAC5F,IAAIA,WAAW,KAAKzC,SAAS,EAAEyC,WAAW,GAAG,GAAG;IAChD,MAAMkF,GAAG,GAAG0C,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ;IACpC,MAAMqB,uBAAuB,GAAGjB,GAAG,CAACiB,uBAAuB,IAAI,IAAI,CAAC5G,OAAO,CAAC4G,uBAAuB;IACnG,IAAI3D,GAAG,EAAE4D,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MACnC,IAAID,uBAAuB,EAAE;QAC3B,IAAIF,aAAa,EAAE;UACjB,OAAO;YACLtN,GAAG,EAAE,GAAGuN,SAAS,GAAG5I,WAAW,GAAG3D,GAAG,EAAE;YACvC0M,OAAO,EAAE1M,GAAG;YACZ2M,YAAY,EAAE3M,GAAG;YACjB4M,OAAO,EAAE/D,GAAG;YACZgE,MAAM,EAAEN,SAAS;YACjBO,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACxB,GAAG;UAC3C,CAAC;QACH;QACA,OAAO,GAAGgB,SAAS,GAAG5I,WAAW,GAAG3D,GAAG,EAAE;MAC3C;MACA,IAAIsM,aAAa,EAAE;QACjB,OAAO;UACLtN,GAAG,EAAEgB,GAAG;UACR0M,OAAO,EAAE1M,GAAG;UACZ2M,YAAY,EAAE3M,GAAG;UACjB4M,OAAO,EAAE/D,GAAG;UACZgE,MAAM,EAAEN,SAAS;UACjBO,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACxB,GAAG;QAC3C,CAAC;MACH;MACA,OAAOvL,GAAG;IACZ;IACA,MAAMwL,QAAQ,GAAG,IAAI,CAACpM,OAAO,CAAC6K,IAAI,EAAEsB,GAAG,CAAC;IACxC,IAAIvM,GAAG,GAAGwM,QAAQ,EAAExM,GAAG;IACvB,MAAMgO,UAAU,GAAGxB,QAAQ,EAAEkB,OAAO,IAAI1M,GAAG;IAC3C,MAAMiN,eAAe,GAAGzB,QAAQ,EAAEmB,YAAY,IAAI3M,GAAG;IACrD,MAAMkN,QAAQ,GAAG,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;IAC5E,MAAMC,UAAU,GAAG5B,GAAG,CAAC4B,UAAU,KAAKjM,SAAS,GAAGqK,GAAG,CAAC4B,UAAU,GAAG,IAAI,CAACvH,OAAO,CAACuH,UAAU;IAC1F,MAAMC,0BAA0B,GAAG,CAAC,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACC,cAAc;IACrF,MAAMC,mBAAmB,GAAGhC,GAAG,CAACiC,KAAK,KAAKtM,SAAS,IAAI,CAACrC,QAAQ,CAAC0M,GAAG,CAACiC,KAAK,CAAC;IAC3E,MAAMC,eAAe,GAAGzC,UAAU,CAACyC,eAAe,CAAClC,GAAG,CAAC;IACvD,MAAMmC,kBAAkB,GAAGH,mBAAmB,GAAG,IAAI,CAACI,cAAc,CAACC,SAAS,CAAC/E,GAAG,EAAE0C,GAAG,CAACiC,KAAK,EAAEjC,GAAG,CAAC,GAAG,EAAE;IACxG,MAAMsC,iCAAiC,GAAGtC,GAAG,CAACuC,OAAO,IAAIP,mBAAmB,GAAG,IAAI,CAACI,cAAc,CAACC,SAAS,CAAC/E,GAAG,EAAE0C,GAAG,CAACiC,KAAK,EAAE;MAC3HM,OAAO,EAAE;IACX,CAAC,CAAC,GAAG,EAAE;IACP,MAAMC,qBAAqB,GAAGR,mBAAmB,IAAI,CAAChC,GAAG,CAACuC,OAAO,IAAIvC,GAAG,CAACiC,KAAK,KAAK,CAAC;IACpF,MAAMQ,YAAY,GAAGD,qBAAqB,IAAIxC,GAAG,CAAC,eAAe,IAAI,CAAC3F,OAAO,CAACqI,eAAe,MAAM,CAAC,IAAI1C,GAAG,CAAC,eAAemC,kBAAkB,EAAE,CAAC,IAAInC,GAAG,CAAC,eAAesC,iCAAiC,EAAE,CAAC,IAAItC,GAAG,CAACyC,YAAY;IAC/N,IAAIE,aAAa,GAAGlP,GAAG;IACvB,IAAIoO,0BAA0B,IAAI,CAACpO,GAAG,IAAIyO,eAAe,EAAE;MACzDS,aAAa,GAAGF,YAAY;IAC9B;IACA,MAAMV,cAAc,GAAGvC,oBAAoB,CAACmD,aAAa,CAAC;IAC1D,MAAMC,OAAO,GAAGxN,MAAM,CAACC,SAAS,CAACwN,QAAQ,CAAC3I,KAAK,CAACyI,aAAa,CAAC;IAC9D,IAAId,0BAA0B,IAAIc,aAAa,IAAIZ,cAAc,IAAIJ,QAAQ,CAACjN,OAAO,CAACkO,OAAO,CAAC,GAAG,CAAC,IAAI,EAAEtP,QAAQ,CAACsO,UAAU,CAAC,IAAIjH,KAAK,CAAC4C,OAAO,CAACoF,aAAa,CAAC,CAAC,EAAE;MAC7J,IAAI,CAAC3C,GAAG,CAAC8C,aAAa,IAAI,CAAC,IAAI,CAACzI,OAAO,CAACyI,aAAa,EAAE;QACrD,IAAI,CAAC,IAAI,CAACzI,OAAO,CAAC0I,qBAAqB,EAAE;UACvC,IAAI,CAACvI,MAAM,CAACT,IAAI,CAAC,iEAAiE,CAAC;QACrF;QACA,MAAMtB,CAAC,GAAG,IAAI,CAAC4B,OAAO,CAAC0I,qBAAqB,GAAG,IAAI,CAAC1I,OAAO,CAAC0I,qBAAqB,CAACtB,UAAU,EAAEkB,aAAa,EAAE;UAC3G,GAAG3C,GAAG;UACNlD,EAAE,EAAEqD;QACN,CAAC,CAAC,GAAG,QAAQ1L,GAAG,KAAK,IAAI,CAACmL,QAAQ,0CAA0C;QAC5E,IAAImB,aAAa,EAAE;UACjBd,QAAQ,CAACxM,GAAG,GAAGgF,CAAC;UAChBwH,QAAQ,CAACsB,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAACxB,GAAG,CAAC;UACpD,OAAOC,QAAQ;QACjB;QACA,OAAOxH,CAAC;MACV;MACA,IAAIJ,YAAY,EAAE;QAChB,MAAM2K,cAAc,GAAGrI,KAAK,CAAC4C,OAAO,CAACoF,aAAa,CAAC;QACnD,MAAM1O,IAAI,GAAG+O,cAAc,GAAG,EAAE,GAAG,CAAC,CAAC;QACrC,MAAMC,WAAW,GAAGD,cAAc,GAAGtB,eAAe,GAAGD,UAAU;QACjE,KAAK,MAAMnN,CAAC,IAAIqO,aAAa,EAAE;UAC7B,IAAIvN,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACoN,aAAa,EAAErO,CAAC,CAAC,EAAE;YAC1D,MAAM4O,OAAO,GAAG,GAAGD,WAAW,GAAG5K,YAAY,GAAG/D,CAAC,EAAE;YACnD,IAAI4N,eAAe,IAAI,CAACzO,GAAG,EAAE;cAC3BQ,IAAI,CAACK,CAAC,CAAC,GAAG,IAAI,CAACsM,SAAS,CAACsC,OAAO,EAAE;gBAChC,GAAGlD,GAAG;gBACNyC,YAAY,EAAEjD,oBAAoB,CAACiD,YAAY,CAAC,GAAGA,YAAY,CAACnO,CAAC,CAAC,GAAGqB,SAAS;gBAC9E,GAAG;kBACDiM,UAAU,EAAE,KAAK;kBACjB9E,EAAE,EAAEqD;gBACN;cACF,CAAC,CAAC;YACJ,CAAC,MAAM;cACLlM,IAAI,CAACK,CAAC,CAAC,GAAG,IAAI,CAACsM,SAAS,CAACsC,OAAO,EAAE;gBAChC,GAAGlD,GAAG;gBACN,GAAG;kBACD4B,UAAU,EAAE,KAAK;kBACjB9E,EAAE,EAAEqD;gBACN;cACF,CAAC,CAAC;YACJ;YACA,IAAIlM,IAAI,CAACK,CAAC,CAAC,KAAK4O,OAAO,EAAEjP,IAAI,CAACK,CAAC,CAAC,GAAGqO,aAAa,CAACrO,CAAC,CAAC;UACrD;QACF;QACAb,GAAG,GAAGQ,IAAI;MACZ;IACF,CAAC,MAAM,IAAI4N,0BAA0B,IAAIvO,QAAQ,CAACsO,UAAU,CAAC,IAAIjH,KAAK,CAAC4C,OAAO,CAAC9J,GAAG,CAAC,EAAE;MACnFA,GAAG,GAAGA,GAAG,CAACkF,IAAI,CAACiJ,UAAU,CAAC;MAC1B,IAAInO,GAAG,EAAEA,GAAG,GAAG,IAAI,CAAC0P,iBAAiB,CAAC1P,GAAG,EAAEiL,IAAI,EAAEsB,GAAG,EAAEa,OAAO,CAAC;IAChE,CAAC,MAAM;MACL,IAAIuC,WAAW,GAAG,KAAK;MACvB,IAAIjC,OAAO,GAAG,KAAK;MACnB,IAAI,CAAC,IAAI,CAACkC,aAAa,CAAC5P,GAAG,CAAC,IAAIyO,eAAe,EAAE;QAC/CkB,WAAW,GAAG,IAAI;QAClB3P,GAAG,GAAGgP,YAAY;MACpB;MACA,IAAI,CAAC,IAAI,CAACY,aAAa,CAAC5P,GAAG,CAAC,EAAE;QAC5B0N,OAAO,GAAG,IAAI;QACd1N,GAAG,GAAGgB,GAAG;MACX;MACA,MAAM6O,8BAA8B,GAAGtD,GAAG,CAACsD,8BAA8B,IAAI,IAAI,CAACjJ,OAAO,CAACiJ,8BAA8B;MACxH,MAAMC,aAAa,GAAGD,8BAA8B,IAAInC,OAAO,GAAGxL,SAAS,GAAGlC,GAAG;MACjF,MAAM+P,aAAa,GAAGtB,eAAe,IAAIO,YAAY,KAAKhP,GAAG,IAAI,IAAI,CAAC4G,OAAO,CAACmJ,aAAa;MAC3F,IAAIrC,OAAO,IAAIiC,WAAW,IAAII,aAAa,EAAE;QAC3C,IAAI,CAAChJ,MAAM,CAACZ,GAAG,CAAC4J,aAAa,GAAG,WAAW,GAAG,YAAY,EAAElG,GAAG,EAAE0D,SAAS,EAAEvM,GAAG,EAAE+O,aAAa,GAAGf,YAAY,GAAGhP,GAAG,CAAC;QACpH,IAAI4E,YAAY,EAAE;UAChB,MAAMoL,EAAE,GAAG,IAAI,CAAC5P,OAAO,CAACY,GAAG,EAAE;YAC3B,GAAGuL,GAAG;YACN3H,YAAY,EAAE;UAChB,CAAC,CAAC;UACF,IAAIoL,EAAE,IAAIA,EAAE,CAAChQ,GAAG,EAAE,IAAI,CAAC+G,MAAM,CAACT,IAAI,CAAC,iLAAiL,CAAC;QACvN;QACA,IAAI2J,IAAI,GAAG,EAAE;QACb,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAACxJ,OAAO,CAACyJ,WAAW,EAAE9D,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QAC5G,IAAI,IAAI,CAACvF,OAAO,CAAC0J,aAAa,KAAK,UAAU,IAAIJ,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,EAAE;UAChF,KAAK,IAAIvK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuK,YAAY,CAACxO,MAAM,EAAEiE,CAAC,EAAE,EAAE;YAC5CsK,IAAI,CAACxN,IAAI,CAACyN,YAAY,CAACvK,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC,MAAM,IAAI,IAAI,CAACiB,OAAO,CAAC0J,aAAa,KAAK,KAAK,EAAE;UAC/CL,IAAI,GAAG,IAAI,CAACE,aAAa,CAACI,kBAAkB,CAAChE,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QACxE,CAAC,MAAM;UACL8D,IAAI,CAACxN,IAAI,CAAC8J,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,CAAC;QACrC;QACA,MAAMqE,IAAI,GAAGA,CAACC,CAAC,EAAE1O,CAAC,EAAE2O,oBAAoB,KAAK;UAC3C,MAAMC,iBAAiB,GAAGlC,eAAe,IAAIiC,oBAAoB,KAAK1Q,GAAG,GAAG0Q,oBAAoB,GAAGZ,aAAa;UAChH,IAAI,IAAI,CAAClJ,OAAO,CAACgK,iBAAiB,EAAE;YAClC,IAAI,CAAChK,OAAO,CAACgK,iBAAiB,CAACH,CAAC,EAAElD,SAAS,EAAExL,CAAC,EAAE4O,iBAAiB,EAAEZ,aAAa,EAAExD,GAAG,CAAC;UACxF,CAAC,MAAM,IAAI,IAAI,CAACsE,gBAAgB,EAAEC,WAAW,EAAE;YAC7C,IAAI,CAACD,gBAAgB,CAACC,WAAW,CAACL,CAAC,EAAElD,SAAS,EAAExL,CAAC,EAAE4O,iBAAiB,EAAEZ,aAAa,EAAExD,GAAG,CAAC;UAC3F;UACA,IAAI,CAAC7D,IAAI,CAAC,YAAY,EAAE+H,CAAC,EAAElD,SAAS,EAAExL,CAAC,EAAE/B,GAAG,CAAC;QAC/C,CAAC;QACD,IAAI,IAAI,CAAC4G,OAAO,CAACkK,WAAW,EAAE;UAC5B,IAAI,IAAI,CAAClK,OAAO,CAACmK,kBAAkB,IAAIxC,mBAAmB,EAAE;YAC1D0B,IAAI,CAACrP,OAAO,CAACuL,QAAQ,IAAI;cACvB,MAAM6E,QAAQ,GAAG,IAAI,CAACrC,cAAc,CAACsC,WAAW,CAAC9E,QAAQ,EAAEI,GAAG,CAAC;cAC/D,IAAIwC,qBAAqB,IAAIxC,GAAG,CAAC,eAAe,IAAI,CAAC3F,OAAO,CAACqI,eAAe,MAAM,CAAC,IAAI+B,QAAQ,CAAC/P,OAAO,CAAC,GAAG,IAAI,CAAC2F,OAAO,CAACqI,eAAe,MAAM,CAAC,GAAG,CAAC,EAAE;gBAClJ+B,QAAQ,CAACvO,IAAI,CAAC,GAAG,IAAI,CAACmE,OAAO,CAACqI,eAAe,MAAM,CAAC;cACtD;cACA+B,QAAQ,CAACpQ,OAAO,CAACsQ,MAAM,IAAI;gBACzBV,IAAI,CAAC,CAACrE,QAAQ,CAAC,EAAEnL,GAAG,GAAGkQ,MAAM,EAAE3E,GAAG,CAAC,eAAe2E,MAAM,EAAE,CAAC,IAAIlC,YAAY,CAAC;cAC9E,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,MAAM;YACLwB,IAAI,CAACP,IAAI,EAAEjP,GAAG,EAAEgO,YAAY,CAAC;UAC/B;QACF;MACF;MACAhP,GAAG,GAAG,IAAI,CAAC0P,iBAAiB,CAAC1P,GAAG,EAAEiL,IAAI,EAAEsB,GAAG,EAAEC,QAAQ,EAAEY,OAAO,CAAC;MAC/D,IAAIM,OAAO,IAAI1N,GAAG,KAAKgB,GAAG,IAAI,IAAI,CAAC4F,OAAO,CAACuK,2BAA2B,EAAE;QACtEnR,GAAG,GAAG,GAAGuN,SAAS,GAAG5I,WAAW,GAAG3D,GAAG,EAAE;MAC1C;MACA,IAAI,CAAC0M,OAAO,IAAIiC,WAAW,KAAK,IAAI,CAAC/I,OAAO,CAACwK,sBAAsB,EAAE;QACnEpR,GAAG,GAAG,IAAI,CAAC4G,OAAO,CAACwK,sBAAsB,CAAC,IAAI,CAACxK,OAAO,CAACuK,2BAA2B,GAAG,GAAG5D,SAAS,GAAG5I,WAAW,GAAG3D,GAAG,EAAE,GAAGA,GAAG,EAAE2O,WAAW,GAAG3P,GAAG,GAAGkC,SAAS,EAAEqK,GAAG,CAAC;MACpK;IACF;IACA,IAAIe,aAAa,EAAE;MACjBd,QAAQ,CAACxM,GAAG,GAAGA,GAAG;MAClBwM,QAAQ,CAACsB,UAAU,GAAG,IAAI,CAACC,oBAAoB,CAACxB,GAAG,CAAC;MACpD,OAAOC,QAAQ;IACjB;IACA,OAAOxM,GAAG;EACZ;EACA0P,iBAAiBA,CAAC1P,GAAG,EAAEgB,GAAG,EAAEuL,GAAG,EAAEC,QAAQ,EAAEY,OAAO,EAAE;IAAA,IAAAiE,KAAA;IAClD,IAAI,IAAI,CAAChD,UAAU,EAAE5D,KAAK,EAAE;MAC1BzK,GAAG,GAAG,IAAI,CAACqO,UAAU,CAAC5D,KAAK,CAACzK,GAAG,EAAE;QAC/B,GAAG,IAAI,CAAC4G,OAAO,CAAC0F,aAAa,CAACgF,gBAAgB;QAC9C,GAAG/E;MACL,CAAC,EAAEA,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,IAAIK,QAAQ,CAACoB,OAAO,EAAEpB,QAAQ,CAACqB,MAAM,EAAErB,QAAQ,CAACkB,OAAO,EAAE;QAClFlB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAACD,GAAG,CAACgF,iBAAiB,EAAE;MACjC,IAAIhF,GAAG,CAACD,aAAa,EAAE,IAAI,CAACU,YAAY,CAACnG,IAAI,CAAC;QAC5C,GAAG0F,GAAG;QACN,GAAG;UACDD,aAAa,EAAE;YACb,GAAG,IAAI,CAAC1F,OAAO,CAAC0F,aAAa;YAC7B,GAAGC,GAAG,CAACD;UACT;QACF;MACF,CAAC,CAAC;MACF,MAAMkF,eAAe,GAAG3R,QAAQ,CAACG,GAAG,CAAC,KAAKuM,GAAG,EAAED,aAAa,EAAEkF,eAAe,KAAKtP,SAAS,GAAGqK,GAAG,CAACD,aAAa,CAACkF,eAAe,GAAG,IAAI,CAAC5K,OAAO,CAAC0F,aAAa,CAACkF,eAAe,CAAC;MAC7K,IAAIC,OAAO;MACX,IAAID,eAAe,EAAE;QACnB,MAAME,EAAE,GAAG1R,GAAG,CAAC+M,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;QACrDwE,OAAO,GAAGC,EAAE,IAAIA,EAAE,CAAChQ,MAAM;MAC3B;MACA,IAAIkB,IAAI,GAAG2J,GAAG,CAACrL,OAAO,IAAI,CAACrB,QAAQ,CAAC0M,GAAG,CAACrL,OAAO,CAAC,GAAGqL,GAAG,CAACrL,OAAO,GAAGqL,GAAG;MACpE,IAAI,IAAI,CAAC3F,OAAO,CAAC0F,aAAa,CAACgF,gBAAgB,EAAE1O,IAAI,GAAG;QACtD,GAAG,IAAI,CAACgE,OAAO,CAAC0F,aAAa,CAACgF,gBAAgB;QAC9C,GAAG1O;MACL,CAAC;MACD5C,GAAG,GAAG,IAAI,CAACgN,YAAY,CAAC2E,WAAW,CAAC3R,GAAG,EAAE4C,IAAI,EAAE2J,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,IAAIK,QAAQ,CAACoB,OAAO,EAAErB,GAAG,CAAC;MACjG,IAAIiF,eAAe,EAAE;QACnB,MAAMI,EAAE,GAAG5R,GAAG,CAAC+M,KAAK,CAAC,IAAI,CAACC,YAAY,CAACC,aAAa,CAAC;QACrD,MAAM4E,OAAO,GAAGD,EAAE,IAAIA,EAAE,CAAClQ,MAAM;QAC/B,IAAI+P,OAAO,GAAGI,OAAO,EAAEtF,GAAG,CAACuF,IAAI,GAAG,KAAK;MACzC;MACA,IAAI,CAACvF,GAAG,CAAC1C,GAAG,IAAI2C,QAAQ,IAAIA,QAAQ,CAACxM,GAAG,EAAEuM,GAAG,CAAC1C,GAAG,GAAG,IAAI,CAACsC,QAAQ,IAAIK,QAAQ,CAACoB,OAAO;MACrF,IAAIrB,GAAG,CAACuF,IAAI,KAAK,KAAK,EAAE9R,GAAG,GAAG,IAAI,CAACgN,YAAY,CAAC8E,IAAI,CAAC9R,GAAG,EAAE,YAAa;QAAA,SAAA+R,KAAA,GAAAvM,SAAA,CAAA9D,MAAA,EAAT0E,IAAI,OAAAc,KAAA,CAAA6K,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJ5L,IAAI,CAAA4L,KAAA,IAAAxM,SAAA,CAAAwM,KAAA;QAAA;QAChE,IAAI5E,OAAO,GAAG,CAAC,CAAC,KAAKhH,IAAI,CAAC,CAAC,CAAC,IAAI,CAACmG,GAAG,CAAC0F,OAAO,EAAE;UAC5CZ,KAAI,CAACtK,MAAM,CAACT,IAAI,CAAC,6CAA6CF,IAAI,CAAC,CAAC,CAAC,YAAYpF,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;UAC1F,OAAO,IAAI;QACb;QACA,OAAOqQ,KAAI,CAAClE,SAAS,CAAC,GAAG/G,IAAI,EAAEpF,GAAG,CAAC;MACrC,CAAC,EAAEuL,GAAG,CAAC;MACP,IAAIA,GAAG,CAACD,aAAa,EAAE,IAAI,CAACU,YAAY,CAACkF,KAAK,CAAC,CAAC;IAClD;IACA,MAAMC,WAAW,GAAG5F,GAAG,CAAC4F,WAAW,IAAI,IAAI,CAACvL,OAAO,CAACuL,WAAW;IAC/D,MAAMC,kBAAkB,GAAGvS,QAAQ,CAACsS,WAAW,CAAC,GAAG,CAACA,WAAW,CAAC,GAAGA,WAAW;IAC9E,IAAInS,GAAG,IAAI,IAAI,IAAIoS,kBAAkB,EAAE1Q,MAAM,IAAI6K,GAAG,CAAC8F,kBAAkB,KAAK,KAAK,EAAE;MACjFrS,GAAG,GAAGqL,aAAa,CAACK,MAAM,CAAC0G,kBAAkB,EAAEpS,GAAG,EAAEgB,GAAG,EAAE,IAAI,CAAC4F,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC0L,uBAAuB,GAAG;QAC9GC,YAAY,EAAE;UACZ,GAAG/F,QAAQ;UACXsB,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACxB,GAAG;QAC3C,CAAC;QACD,GAAGA;MACL,CAAC,GAAGA,GAAG,EAAE,IAAI,CAAC;IAChB;IACA,OAAOvM,GAAG;EACZ;EACAI,OAAOA,CAAC6K,IAAI,EAAY;IAAA,IAAVsB,GAAG,GAAA/G,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACpB,IAAIgN,KAAK;IACT,IAAI9E,OAAO;IACX,IAAIC,YAAY;IAChB,IAAIC,OAAO;IACX,IAAIC,MAAM;IACV,IAAIhO,QAAQ,CAACoL,IAAI,CAAC,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjCA,IAAI,CAACrK,OAAO,CAACmB,CAAC,IAAI;MAChB,IAAI,IAAI,CAAC6N,aAAa,CAAC4C,KAAK,CAAC,EAAE;MAC/B,MAAMC,SAAS,GAAG,IAAI,CAAChG,cAAc,CAAC1K,CAAC,EAAEwK,GAAG,CAAC;MAC7C,MAAMvL,GAAG,GAAGyR,SAAS,CAACzR,GAAG;MACzB0M,OAAO,GAAG1M,GAAG;MACb,IAAI0L,UAAU,GAAG+F,SAAS,CAAC/F,UAAU;MACrC,IAAI,IAAI,CAAC9F,OAAO,CAAC8L,UAAU,EAAEhG,UAAU,GAAGA,UAAU,CAAClK,MAAM,CAAC,IAAI,CAACoE,OAAO,CAAC8L,UAAU,CAAC;MACpF,MAAMnE,mBAAmB,GAAGhC,GAAG,CAACiC,KAAK,KAAKtM,SAAS,IAAI,CAACrC,QAAQ,CAAC0M,GAAG,CAACiC,KAAK,CAAC;MAC3E,MAAMO,qBAAqB,GAAGR,mBAAmB,IAAI,CAAChC,GAAG,CAACuC,OAAO,IAAIvC,GAAG,CAACiC,KAAK,KAAK,CAAC;MACpF,MAAMmE,oBAAoB,GAAGpG,GAAG,CAAC0F,OAAO,KAAK/P,SAAS,KAAKrC,QAAQ,CAAC0M,GAAG,CAAC0F,OAAO,CAAC,IAAI,OAAO1F,GAAG,CAAC0F,OAAO,KAAK,QAAQ,CAAC,IAAI1F,GAAG,CAAC0F,OAAO,KAAK,EAAE;MAC1I,MAAMW,KAAK,GAAGrG,GAAG,CAAC0D,IAAI,GAAG1D,GAAG,CAAC0D,IAAI,GAAG,IAAI,CAACE,aAAa,CAACI,kBAAkB,CAAChE,GAAG,CAAC1C,GAAG,IAAI,IAAI,CAACsC,QAAQ,EAAEI,GAAG,CAAC8D,WAAW,CAAC;MACpH3D,UAAU,CAAC9L,OAAO,CAACyI,EAAE,IAAI;QACvB,IAAI,IAAI,CAACuG,aAAa,CAAC4C,KAAK,CAAC,EAAE;QAC/B3E,MAAM,GAAGxE,EAAE;QACX,IAAI,CAACyC,gBAAgB,CAAC,GAAG8G,KAAK,CAAC,CAAC,CAAC,IAAIvJ,EAAE,EAAE,CAAC,IAAI,IAAI,CAACwJ,KAAK,EAAEC,kBAAkB,IAAI,CAAC,IAAI,CAACD,KAAK,EAAEC,kBAAkB,CAACjF,MAAM,CAAC,EAAE;UACvH/B,gBAAgB,CAAC,GAAG8G,KAAK,CAAC,CAAC,CAAC,IAAIvJ,EAAE,EAAE,CAAC,GAAG,IAAI;UAC5C,IAAI,CAACtC,MAAM,CAACT,IAAI,CAAC,QAAQoH,OAAO,oBAAoBkF,KAAK,CAAC1N,IAAI,CAAC,IAAI,CAAC,sCAAsC2I,MAAM,sBAAsB,EAAE,0NAA0N,CAAC;QACrW;QACA+E,KAAK,CAAChS,OAAO,CAACoF,IAAI,IAAI;UACpB,IAAI,IAAI,CAAC4J,aAAa,CAAC4C,KAAK,CAAC,EAAE;UAC/B5E,OAAO,GAAG5H,IAAI;UACd,MAAM+M,SAAS,GAAG,CAAC/R,GAAG,CAAC;UACvB,IAAI,IAAI,CAACqN,UAAU,EAAE2E,aAAa,EAAE;YAClC,IAAI,CAAC3E,UAAU,CAAC2E,aAAa,CAACD,SAAS,EAAE/R,GAAG,EAAEgF,IAAI,EAAEqD,EAAE,EAAEkD,GAAG,CAAC;UAC9D,CAAC,MAAM;YACL,IAAI0G,YAAY;YAChB,IAAI1E,mBAAmB,EAAE0E,YAAY,GAAG,IAAI,CAACtE,cAAc,CAACC,SAAS,CAAC5I,IAAI,EAAEuG,GAAG,CAACiC,KAAK,EAAEjC,GAAG,CAAC;YAC3F,MAAM2G,UAAU,GAAG,GAAG,IAAI,CAACtM,OAAO,CAACqI,eAAe,MAAM;YACxD,MAAMkE,aAAa,GAAG,GAAG,IAAI,CAACvM,OAAO,CAACqI,eAAe,UAAU,IAAI,CAACrI,OAAO,CAACqI,eAAe,EAAE;YAC7F,IAAIV,mBAAmB,EAAE;cACvBwE,SAAS,CAACtQ,IAAI,CAACzB,GAAG,GAAGiS,YAAY,CAAC;cAClC,IAAI1G,GAAG,CAACuC,OAAO,IAAImE,YAAY,CAAChS,OAAO,CAACkS,aAAa,CAAC,KAAK,CAAC,EAAE;gBAC5DJ,SAAS,CAACtQ,IAAI,CAACzB,GAAG,GAAGiS,YAAY,CAAC/R,OAAO,CAACiS,aAAa,EAAE,IAAI,CAACvM,OAAO,CAACqI,eAAe,CAAC,CAAC;cACzF;cACA,IAAIF,qBAAqB,EAAE;gBACzBgE,SAAS,CAACtQ,IAAI,CAACzB,GAAG,GAAGkS,UAAU,CAAC;cAClC;YACF;YACA,IAAIP,oBAAoB,EAAE;cACxB,MAAMS,UAAU,GAAG,GAAGpS,GAAG,GAAG,IAAI,CAAC4F,OAAO,CAACyM,gBAAgB,GAAG9G,GAAG,CAAC0F,OAAO,EAAE;cACzEc,SAAS,CAACtQ,IAAI,CAAC2Q,UAAU,CAAC;cAC1B,IAAI7E,mBAAmB,EAAE;gBACvBwE,SAAS,CAACtQ,IAAI,CAAC2Q,UAAU,GAAGH,YAAY,CAAC;gBACzC,IAAI1G,GAAG,CAACuC,OAAO,IAAImE,YAAY,CAAChS,OAAO,CAACkS,aAAa,CAAC,KAAK,CAAC,EAAE;kBAC5DJ,SAAS,CAACtQ,IAAI,CAAC2Q,UAAU,GAAGH,YAAY,CAAC/R,OAAO,CAACiS,aAAa,EAAE,IAAI,CAACvM,OAAO,CAACqI,eAAe,CAAC,CAAC;gBAChG;gBACA,IAAIF,qBAAqB,EAAE;kBACzBgE,SAAS,CAACtQ,IAAI,CAAC2Q,UAAU,GAAGF,UAAU,CAAC;gBACzC;cACF;YACF;UACF;UACA,IAAII,WAAW;UACf,OAAOA,WAAW,GAAGP,SAAS,CAACQ,GAAG,CAAC,CAAC,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC3D,aAAa,CAAC4C,KAAK,CAAC,EAAE;cAC9B7E,YAAY,GAAG2F,WAAW;cAC1Bd,KAAK,GAAG,IAAI,CAAC5I,WAAW,CAAC5D,IAAI,EAAEqD,EAAE,EAAEiK,WAAW,EAAE/G,GAAG,CAAC;YACtD;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO;MACLvM,GAAG,EAAEwS,KAAK;MACV9E,OAAO;MACPC,YAAY;MACZC,OAAO;MACPC;IACF,CAAC;EACH;EACA+B,aAAaA,CAAC5P,GAAG,EAAE;IACjB,OAAOA,GAAG,KAAKkC,SAAS,IAAI,EAAE,CAAC,IAAI,CAAC0E,OAAO,CAAC4M,UAAU,IAAIxT,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC4G,OAAO,CAAC6M,iBAAiB,IAAIzT,GAAG,KAAK,EAAE,CAAC;EAC7H;EACA4J,WAAWA,CAAC5D,IAAI,EAAEqD,EAAE,EAAErI,GAAG,EAAgB;IAAA,IAAd4F,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC6I,UAAU,EAAEzE,WAAW,EAAE,OAAO,IAAI,CAACyE,UAAU,CAACzE,WAAW,CAAC5D,IAAI,EAAEqD,EAAE,EAAErI,GAAG,EAAE4F,OAAO,CAAC;IAC5F,OAAO,IAAI,CAAC8M,aAAa,CAAC9J,WAAW,CAAC5D,IAAI,EAAEqD,EAAE,EAAErI,GAAG,EAAE4F,OAAO,CAAC;EAC/D;EACAmH,oBAAoBA,CAAA,EAAe;IAAA,IAAdnH,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAC/B,MAAMmO,WAAW,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC;IACxN,MAAMC,wBAAwB,GAAGhN,OAAO,CAAC1F,OAAO,IAAI,CAACrB,QAAQ,CAAC+G,OAAO,CAAC1F,OAAO,CAAC;IAC9E,IAAI0B,IAAI,GAAGgR,wBAAwB,GAAGhN,OAAO,CAAC1F,OAAO,GAAG0F,OAAO;IAC/D,IAAIgN,wBAAwB,IAAI,OAAOhN,OAAO,CAAC4H,KAAK,KAAK,WAAW,EAAE;MACpE5L,IAAI,CAAC4L,KAAK,GAAG5H,OAAO,CAAC4H,KAAK;IAC5B;IACA,IAAI,IAAI,CAAC5H,OAAO,CAAC0F,aAAa,CAACgF,gBAAgB,EAAE;MAC/C1O,IAAI,GAAG;QACL,GAAG,IAAI,CAACgE,OAAO,CAAC0F,aAAa,CAACgF,gBAAgB;QAC9C,GAAG1O;MACL,CAAC;IACH;IACA,IAAI,CAACgR,wBAAwB,EAAE;MAC7BhR,IAAI,GAAG;QACL,GAAGA;MACL,CAAC;MACD,KAAK,MAAM5B,GAAG,IAAI2S,WAAW,EAAE;QAC7B,OAAO/Q,IAAI,CAAC5B,GAAG,CAAC;MAClB;IACF;IACA,OAAO4B,IAAI;EACb;EACA,OAAO6L,eAAeA,CAAC7H,OAAO,EAAE;IAC9B,MAAME,MAAM,GAAG,cAAc;IAC7B,KAAK,MAAM+M,MAAM,IAAIjN,OAAO,EAAE;MAC5B,IAAIjF,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC8E,OAAO,EAAEiN,MAAM,CAAC,IAAI/M,MAAM,KAAK+M,MAAM,CAACvO,SAAS,CAAC,CAAC,EAAEwB,MAAM,CAACpF,MAAM,CAAC,IAAIQ,SAAS,KAAK0E,OAAO,CAACiN,MAAM,CAAC,EAAE;QAC3I,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;AACF;AAEA,MAAMC,YAAY,CAAC;EACjBpQ,WAAWA,CAACkD,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmN,aAAa,GAAG,IAAI,CAACnN,OAAO,CAACmN,aAAa,IAAI,KAAK;IACxD,IAAI,CAAChN,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,eAAe,CAAC;EAClD;EACAkM,qBAAqBA,CAAChO,IAAI,EAAE;IAC1BA,IAAI,GAAGD,cAAc,CAACC,IAAI,CAAC;IAC3B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI;IAC/C,MAAMmB,CAAC,GAAG4D,IAAI,CAACxE,KAAK,CAAC,GAAG,CAAC;IACzB,IAAIY,CAAC,CAACV,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC/BU,CAAC,CAACmR,GAAG,CAAC,CAAC;IACP,IAAInR,CAAC,CAACA,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC,CAAC+L,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;IACtD,OAAO,IAAI,CAACwG,kBAAkB,CAAC7R,CAAC,CAAC8C,IAAI,CAAC,GAAG,CAAC,CAAC;EAC7C;EACAgP,uBAAuBA,CAAClO,IAAI,EAAE;IAC5BA,IAAI,GAAGD,cAAc,CAACC,IAAI,CAAC;IAC3B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO+E,IAAI;IAC/C,MAAM5D,CAAC,GAAG4D,IAAI,CAACxE,KAAK,CAAC,GAAG,CAAC;IACzB,OAAO,IAAI,CAACyS,kBAAkB,CAAC7R,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC;EACA6R,kBAAkBA,CAACjO,IAAI,EAAE;IACvB,IAAInG,QAAQ,CAACmG,IAAI,CAAC,IAAIA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5C,IAAIkT,aAAa;MACjB,IAAI;QACFA,aAAa,GAAGC,IAAI,CAACC,mBAAmB,CAACrO,IAAI,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,OAAO7D,CAAC,EAAE,CAAC;MACb,IAAIgS,aAAa,IAAI,IAAI,CAACvN,OAAO,CAAC0N,YAAY,EAAE;QAC9CH,aAAa,GAAGA,aAAa,CAAC1G,WAAW,CAAC,CAAC;MAC7C;MACA,IAAI0G,aAAa,EAAE,OAAOA,aAAa;MACvC,IAAI,IAAI,CAACvN,OAAO,CAAC0N,YAAY,EAAE;QAC7B,OAAOtO,IAAI,CAACyH,WAAW,CAAC,CAAC;MAC3B;MACA,OAAOzH,IAAI;IACb;IACA,OAAO,IAAI,CAACY,OAAO,CAAC2N,SAAS,IAAI,IAAI,CAAC3N,OAAO,CAAC0N,YAAY,GAAGtO,IAAI,CAACyH,WAAW,CAAC,CAAC,GAAGzH,IAAI;EACxF;EACAwO,eAAeA,CAACxO,IAAI,EAAE;IACpB,IAAI,IAAI,CAACY,OAAO,CAAC6N,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC7N,OAAO,CAAC8N,wBAAwB,EAAE;MACjF1O,IAAI,GAAG,IAAI,CAACkO,uBAAuB,CAAClO,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC,IAAI,CAAC+N,aAAa,IAAI,CAAC,IAAI,CAACA,aAAa,CAACrS,MAAM,IAAI,IAAI,CAACqS,aAAa,CAAC9S,OAAO,CAAC+E,IAAI,CAAC,GAAG,CAAC,CAAC;EACnG;EACA2O,qBAAqBA,CAAC/B,KAAK,EAAE;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IACvB,IAAIJ,KAAK;IACTI,KAAK,CAAChS,OAAO,CAACoF,IAAI,IAAI;MACpB,IAAIwM,KAAK,EAAE;MACX,MAAMoC,UAAU,GAAG,IAAI,CAACX,kBAAkB,CAACjO,IAAI,CAAC;MAChD,IAAI,CAAC,IAAI,CAACY,OAAO,CAACmN,aAAa,IAAI,IAAI,CAACS,eAAe,CAACI,UAAU,CAAC,EAAEpC,KAAK,GAAGoC,UAAU;IACzF,CAAC,CAAC;IACF,IAAI,CAACpC,KAAK,IAAI,IAAI,CAAC5L,OAAO,CAACmN,aAAa,EAAE;MACxCnB,KAAK,CAAChS,OAAO,CAACoF,IAAI,IAAI;QACpB,IAAIwM,KAAK,EAAE;QACX,MAAMqC,SAAS,GAAG,IAAI,CAACb,qBAAqB,CAAChO,IAAI,CAAC;QAClD,IAAI,IAAI,CAACwO,eAAe,CAACK,SAAS,CAAC,EAAE,OAAOrC,KAAK,GAAGqC,SAAS;QAC7D,MAAMC,OAAO,GAAG,IAAI,CAACZ,uBAAuB,CAAClO,IAAI,CAAC;QAClD,IAAI,IAAI,CAACwO,eAAe,CAACM,OAAO,CAAC,EAAE,OAAOtC,KAAK,GAAGsC,OAAO;QACzDtC,KAAK,GAAG,IAAI,CAAC5L,OAAO,CAACmN,aAAa,CAAC7I,IAAI,CAAC6J,YAAY,IAAI;UACtD,IAAIA,YAAY,KAAKD,OAAO,EAAE,OAAOC,YAAY;UACjD,IAAIA,YAAY,CAAC9T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI6T,OAAO,CAAC7T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;UAC/D,IAAI8T,YAAY,CAAC9T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI6T,OAAO,CAAC7T,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI8T,YAAY,CAACzP,SAAS,CAAC,CAAC,EAAEyP,YAAY,CAAC9T,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK6T,OAAO,EAAE,OAAOC,YAAY;UACtJ,IAAIA,YAAY,CAAC9T,OAAO,CAAC6T,OAAO,CAAC,KAAK,CAAC,IAAIA,OAAO,CAACpT,MAAM,GAAG,CAAC,EAAE,OAAOqT,YAAY;QACpF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAACvC,KAAK,EAAEA,KAAK,GAAG,IAAI,CAACpC,gBAAgB,CAAC,IAAI,CAACxJ,OAAO,CAACyJ,WAAW,CAAC,CAAC,CAAC,CAAC;IACtE,OAAOmC,KAAK;EACd;EACApC,gBAAgBA,CAAC4E,SAAS,EAAEhP,IAAI,EAAE;IAChC,IAAI,CAACgP,SAAS,EAAE,OAAO,EAAE;IACzB,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAEA,SAAS,GAAGA,SAAS,CAAChP,IAAI,CAAC;IAChE,IAAInG,QAAQ,CAACmV,SAAS,CAAC,EAAEA,SAAS,GAAG,CAACA,SAAS,CAAC;IAChD,IAAI9N,KAAK,CAAC4C,OAAO,CAACkL,SAAS,CAAC,EAAE,OAAOA,SAAS;IAC9C,IAAI,CAAChP,IAAI,EAAE,OAAOgP,SAAS,CAACC,OAAO,IAAI,EAAE;IACzC,IAAIzC,KAAK,GAAGwC,SAAS,CAAChP,IAAI,CAAC;IAC3B,IAAI,CAACwM,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAAC,IAAI,CAAChB,qBAAqB,CAAChO,IAAI,CAAC,CAAC;IAC/D,IAAI,CAACwM,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAAC,IAAI,CAACf,kBAAkB,CAACjO,IAAI,CAAC,CAAC;IAC5D,IAAI,CAACwM,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAAC,IAAI,CAACd,uBAAuB,CAAClO,IAAI,CAAC,CAAC;IACjE,IAAI,CAACwM,KAAK,EAAEA,KAAK,GAAGwC,SAAS,CAACC,OAAO;IACrC,OAAOzC,KAAK,IAAI,EAAE;EACpB;EACAjC,kBAAkBA,CAACvK,IAAI,EAAEkP,YAAY,EAAE;IACrC,MAAMC,aAAa,GAAG,IAAI,CAAC/E,gBAAgB,CAAC,CAAC8E,YAAY,KAAK,KAAK,GAAG,EAAE,GAAGA,YAAY,KAAK,IAAI,CAACtO,OAAO,CAACyJ,WAAW,IAAI,EAAE,EAAErK,IAAI,CAAC;IACjI,MAAM4M,KAAK,GAAG,EAAE;IAChB,MAAMwC,OAAO,GAAGrQ,CAAC,IAAI;MACnB,IAAI,CAACA,CAAC,EAAE;MACR,IAAI,IAAI,CAACyP,eAAe,CAACzP,CAAC,CAAC,EAAE;QAC3B6N,KAAK,CAACnQ,IAAI,CAACsC,CAAC,CAAC;MACf,CAAC,MAAM;QACL,IAAI,CAACgC,MAAM,CAACT,IAAI,CAAC,uDAAuDvB,CAAC,EAAE,CAAC;MAC9E;IACF,CAAC;IACD,IAAIlF,QAAQ,CAACmG,IAAI,CAAC,KAAKA,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI+E,IAAI,CAAC/E,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MACxE,IAAI,IAAI,CAAC2F,OAAO,CAAC6N,IAAI,KAAK,cAAc,EAAEW,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACjO,IAAI,CAAC,CAAC;MAChF,IAAI,IAAI,CAACY,OAAO,CAAC6N,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC7N,OAAO,CAAC6N,IAAI,KAAK,aAAa,EAAEW,OAAO,CAAC,IAAI,CAACpB,qBAAqB,CAAChO,IAAI,CAAC,CAAC;MAC1H,IAAI,IAAI,CAACY,OAAO,CAAC6N,IAAI,KAAK,aAAa,EAAEW,OAAO,CAAC,IAAI,CAAClB,uBAAuB,CAAClO,IAAI,CAAC,CAAC;IACtF,CAAC,MAAM,IAAInG,QAAQ,CAACmG,IAAI,CAAC,EAAE;MACzBoP,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACjO,IAAI,CAAC,CAAC;IACxC;IACAmP,aAAa,CAACvU,OAAO,CAACyU,EAAE,IAAI;MAC1B,IAAIzC,KAAK,CAAC3R,OAAO,CAACoU,EAAE,CAAC,GAAG,CAAC,EAAED,OAAO,CAAC,IAAI,CAACnB,kBAAkB,CAACoB,EAAE,CAAC,CAAC;IACjE,CAAC,CAAC;IACF,OAAOzC,KAAK;EACd;AACF;AAEA,MAAM0C,aAAa,GAAG;EACpBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAEtH,KAAK,IAAIA,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;EAC9CuH,eAAe,EAAEA,CAAA,MAAO;IACtBC,gBAAgB,EAAE,CAAC,KAAK,EAAE,OAAO;EACnC,CAAC;AACH,CAAC;AACD,MAAMC,cAAc,CAAC;EACnBvS,WAAWA,CAACyM,aAAa,EAAgB;IAAA,IAAdvJ,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACrC,IAAI,CAAC2K,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACvJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,gBAAgB,CAAC;IACjD,IAAI,CAACoO,gBAAgB,GAAG,CAAC,CAAC;EAC5B;EACAC,OAAOA,CAACtM,GAAG,EAAE/J,GAAG,EAAE;IAChB,IAAI,CAACsW,KAAK,CAACvM,GAAG,CAAC,GAAG/J,GAAG;EACvB;EACAuW,UAAUA,CAAA,EAAG;IACX,IAAI,CAACH,gBAAgB,GAAG,CAAC,CAAC;EAC5B;EACAI,OAAOA,CAACtQ,IAAI,EAAgB;IAAA,IAAdY,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACxB,MAAM+Q,WAAW,GAAGxQ,cAAc,CAACC,IAAI,KAAK,KAAK,GAAG,IAAI,GAAGA,IAAI,CAAC;IAChE,MAAME,IAAI,GAAGU,OAAO,CAACkI,OAAO,GAAG,SAAS,GAAG,UAAU;IACrD,MAAM0H,QAAQ,GAAGhM,IAAI,CAACE,SAAS,CAAC;MAC9B6L,WAAW;MACXrQ;IACF,CAAC,CAAC;IACF,IAAIsQ,QAAQ,IAAI,IAAI,CAACN,gBAAgB,EAAE;MACrC,OAAO,IAAI,CAACA,gBAAgB,CAACM,QAAQ,CAAC;IACxC;IACA,IAAIC,IAAI;IACR,IAAI;MACFA,IAAI,GAAG,IAAIrC,IAAI,CAACsC,WAAW,CAACH,WAAW,EAAE;QACvCrQ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOyQ,GAAG,EAAE;MACZ,IAAI,CAACvC,IAAI,EAAE;QACT,IAAI,CAACrN,MAAM,CAACR,KAAK,CAAC,+CAA+C,CAAC;QAClE,OAAOsP,SAAS;MAClB;MACA,IAAI,CAAC7P,IAAI,CAAC+G,KAAK,CAAC,KAAK,CAAC,EAAE,OAAO8I,SAAS;MACxC,MAAMe,OAAO,GAAG,IAAI,CAACzG,aAAa,CAAC+D,uBAAuB,CAAClO,IAAI,CAAC;MAChEyQ,IAAI,GAAG,IAAI,CAACH,OAAO,CAACM,OAAO,EAAEhQ,OAAO,CAAC;IACvC;IACA,IAAI,CAACsP,gBAAgB,CAACM,QAAQ,CAAC,GAAGC,IAAI;IACtC,OAAOA,IAAI;EACb;EACAI,WAAWA,CAAC7Q,IAAI,EAAgB;IAAA,IAAdY,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAC5B,IAAIiR,IAAI,GAAG,IAAI,CAACH,OAAO,CAACtQ,IAAI,EAAEY,OAAO,CAAC;IACtC,IAAI,CAAC6P,IAAI,EAAEA,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,KAAK,EAAE1P,OAAO,CAAC;IAC9C,OAAO6P,IAAI,EAAEV,eAAe,CAAC,CAAC,CAACC,gBAAgB,CAACtU,MAAM,GAAG,CAAC;EAC5D;EACAoV,mBAAmBA,CAAC9Q,IAAI,EAAEhF,GAAG,EAAgB;IAAA,IAAd4F,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACzC,OAAO,IAAI,CAACyL,WAAW,CAACjL,IAAI,EAAEY,OAAO,CAAC,CAAC3B,GAAG,CAACiM,MAAM,IAAI,GAAGlQ,GAAG,GAAGkQ,MAAM,EAAE,CAAC;EACzE;EACAD,WAAWA,CAACjL,IAAI,EAAgB;IAAA,IAAdY,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAC5B,IAAIiR,IAAI,GAAG,IAAI,CAACH,OAAO,CAACtQ,IAAI,EAAEY,OAAO,CAAC;IACtC,IAAI,CAAC6P,IAAI,EAAEA,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,KAAK,EAAE1P,OAAO,CAAC;IAC9C,IAAI,CAAC6P,IAAI,EAAE,OAAO,EAAE;IACpB,OAAOA,IAAI,CAACV,eAAe,CAAC,CAAC,CAACC,gBAAgB,CAACe,IAAI,CAAC,CAACC,eAAe,EAAEC,eAAe,KAAK3B,aAAa,CAAC0B,eAAe,CAAC,GAAG1B,aAAa,CAAC2B,eAAe,CAAC,CAAC,CAAChS,GAAG,CAACiS,cAAc,IAAI,GAAG,IAAI,CAACtQ,OAAO,CAACuQ,OAAO,GAAGvQ,OAAO,CAACkI,OAAO,GAAG,UAAU,IAAI,CAAClI,OAAO,CAACuQ,OAAO,EAAE,GAAG,EAAE,GAAGD,cAAc,EAAE,CAAC;EACxR;EACAtI,SAASA,CAAC5I,IAAI,EAAEwI,KAAK,EAAgB;IAAA,IAAd5H,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACjC,MAAMiR,IAAI,GAAG,IAAI,CAACH,OAAO,CAACtQ,IAAI,EAAEY,OAAO,CAAC;IACxC,IAAI6P,IAAI,EAAE;MACR,OAAO,GAAG,IAAI,CAAC7P,OAAO,CAACuQ,OAAO,GAAGvQ,OAAO,CAACkI,OAAO,GAAG,UAAU,IAAI,CAAClI,OAAO,CAACuQ,OAAO,EAAE,GAAG,EAAE,GAAGV,IAAI,CAACX,MAAM,CAACtH,KAAK,CAAC,EAAE;IACjH;IACA,IAAI,CAACzH,MAAM,CAACT,IAAI,CAAC,6BAA6BN,IAAI,EAAE,CAAC;IACrD,OAAO,IAAI,CAAC4I,SAAS,CAAC,KAAK,EAAEJ,KAAK,EAAE5H,OAAO,CAAC;EAC9C;AACF;AAEA,MAAMwQ,oBAAoB,GAAG,SAAAA,CAACxU,IAAI,EAAEC,WAAW,EAAE7B,GAAG,EAAqD;EAAA,IAAnD4D,YAAY,GAAAY,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,GAAG;EAAA,IAAE+D,mBAAmB,GAAA/D,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,IAAI;EAClG,IAAInE,IAAI,GAAGsB,mBAAmB,CAACC,IAAI,EAAEC,WAAW,EAAE7B,GAAG,CAAC;EACtD,IAAI,CAACK,IAAI,IAAIkI,mBAAmB,IAAI1J,QAAQ,CAACmB,GAAG,CAAC,EAAE;IACjDK,IAAI,GAAGkE,QAAQ,CAAC3C,IAAI,EAAE5B,GAAG,EAAE4D,YAAY,CAAC;IACxC,IAAIvD,IAAI,KAAKa,SAAS,EAAEb,IAAI,GAAGkE,QAAQ,CAAC1C,WAAW,EAAE7B,GAAG,EAAE4D,YAAY,CAAC;EACzE;EACA,OAAOvD,IAAI;AACb,CAAC;AACD,MAAMgW,SAAS,GAAGC,GAAG,IAAIA,GAAG,CAACpW,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;AACnD,MAAMqW,YAAY,CAAC;EACjB7T,WAAWA,CAAA,EAAe;IAAA,IAAdkD,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACtB,IAAI,CAACuB,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,cAAc,CAAC;IAC/C,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4Q,MAAM,GAAG5Q,OAAO,EAAE0F,aAAa,EAAEkL,MAAM,KAAK1U,KAAK,IAAIA,KAAK,CAAC;IAChE,IAAI,CAAC+D,IAAI,CAACD,OAAO,CAAC;EACpB;EACAC,IAAIA,CAAA,EAAe;IAAA,IAAdD,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACf,IAAI,CAACoB,OAAO,CAAC0F,aAAa,EAAE1F,OAAO,CAAC0F,aAAa,GAAG;MAClDmL,WAAW,EAAE;IACf,CAAC;IACD,MAAM;MACJjU,MAAM,EAAEkU,QAAQ;MAChBD,WAAW;MACXE,mBAAmB;MACnB7Q,MAAM;MACN8Q,aAAa;MACb1G,MAAM;MACN2G,aAAa;MACbC,eAAe;MACfC,cAAc;MACdC,cAAc;MACdC,aAAa;MACbC,oBAAoB;MACpBC,aAAa;MACbC,oBAAoB;MACpBC,uBAAuB;MACvBC,WAAW;MACXC;IACF,CAAC,GAAG3R,OAAO,CAAC0F,aAAa;IACzB,IAAI,CAAC9I,MAAM,GAAGkU,QAAQ,KAAKxV,SAAS,GAAGwV,QAAQ,GAAGlU,MAAM;IACxD,IAAI,CAACiU,WAAW,GAAGA,WAAW,KAAKvV,SAAS,GAAGuV,WAAW,GAAG,IAAI;IACjE,IAAI,CAACE,mBAAmB,GAAGA,mBAAmB,KAAKzV,SAAS,GAAGyV,mBAAmB,GAAG,KAAK;IAC1F,IAAI,CAAC7Q,MAAM,GAAGA,MAAM,GAAGzD,WAAW,CAACyD,MAAM,CAAC,GAAG8Q,aAAa,IAAI,IAAI;IAClE,IAAI,CAAC1G,MAAM,GAAGA,MAAM,GAAG7N,WAAW,CAAC6N,MAAM,CAAC,GAAG2G,aAAa,IAAI,IAAI;IAClE,IAAI,CAACC,eAAe,GAAGA,eAAe,IAAI,GAAG;IAC7C,IAAI,CAACE,cAAc,GAAGD,cAAc,GAAG,EAAE,GAAGC,cAAc,IAAI,GAAG;IACjE,IAAI,CAACD,cAAc,GAAG,IAAI,CAACC,cAAc,GAAG,EAAE,GAAGD,cAAc,IAAI,EAAE;IACrE,IAAI,CAACE,aAAa,GAAGA,aAAa,GAAG5U,WAAW,CAAC4U,aAAa,CAAC,GAAGC,oBAAoB,IAAI7U,WAAW,CAAC,KAAK,CAAC;IAC5G,IAAI,CAAC8U,aAAa,GAAGA,aAAa,GAAG9U,WAAW,CAAC8U,aAAa,CAAC,GAAGC,oBAAoB,IAAI/U,WAAW,CAAC,GAAG,CAAC;IAC1G,IAAI,CAACgV,uBAAuB,GAAGA,uBAAuB,IAAI,GAAG;IAC7D,IAAI,CAACC,WAAW,GAAGA,WAAW,IAAI,IAAI;IACtC,IAAI,CAACC,YAAY,GAAGA,YAAY,KAAKrW,SAAS,GAAGqW,YAAY,GAAG,KAAK;IACrE,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB;EACAtG,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACtL,OAAO,EAAE,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,OAAO,CAAC;EAC3C;EACA4R,WAAWA,CAAA,EAAG;IACZ,MAAMC,gBAAgB,GAAGA,CAACC,cAAc,EAAE1U,OAAO,KAAK;MACpD,IAAI0U,cAAc,EAAEzV,MAAM,KAAKe,OAAO,EAAE;QACtC0U,cAAc,CAACC,SAAS,GAAG,CAAC;QAC5B,OAAOD,cAAc;MACvB;MACA,OAAO,IAAItU,MAAM,CAACJ,OAAO,EAAE,GAAG,CAAC;IACjC,CAAC;IACD,IAAI,CAAC4U,MAAM,GAAGH,gBAAgB,CAAC,IAAI,CAACG,MAAM,EAAE,GAAG,IAAI,CAAC9R,MAAM,QAAQ,IAAI,CAACoK,MAAM,EAAE,CAAC;IAChF,IAAI,CAAC2H,cAAc,GAAGJ,gBAAgB,CAAC,IAAI,CAACI,cAAc,EAAE,GAAG,IAAI,CAAC/R,MAAM,GAAG,IAAI,CAACkR,cAAc,QAAQ,IAAI,CAACD,cAAc,GAAG,IAAI,CAAC7G,MAAM,EAAE,CAAC;IAC5I,IAAI,CAACjE,aAAa,GAAGwL,gBAAgB,CAAC,IAAI,CAACxL,aAAa,EAAE,GAAG,IAAI,CAACgL,aAAa,QAAQ,IAAI,CAACE,aAAa,EAAE,CAAC;EAC9G;EACAxG,WAAWA,CAACrO,GAAG,EAAEV,IAAI,EAAEiH,GAAG,EAAEjD,OAAO,EAAE;IACnC,IAAImG,KAAK;IACT,IAAIjK,KAAK;IACT,IAAIgW,QAAQ;IACZ,MAAMjW,WAAW,GAAG,IAAI,CAAC+D,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC0F,aAAa,IAAI,IAAI,CAAC1F,OAAO,CAAC0F,aAAa,CAACgF,gBAAgB,IAAI,CAAC,CAAC;IACnH,MAAMyH,YAAY,GAAG/X,GAAG,IAAI;MAC1B,IAAIA,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC6W,eAAe,CAAC,GAAG,CAAC,EAAE;QACzC,MAAMzW,IAAI,GAAG+V,oBAAoB,CAACxU,IAAI,EAAEC,WAAW,EAAE7B,GAAG,EAAE,IAAI,CAAC4F,OAAO,CAAChC,YAAY,EAAE,IAAI,CAACgC,OAAO,CAAC2C,mBAAmB,CAAC;QACtH,OAAO,IAAI,CAACgP,YAAY,GAAG,IAAI,CAACf,MAAM,CAACnW,IAAI,EAAEa,SAAS,EAAE2H,GAAG,EAAE;UAC3D,GAAGjD,OAAO;UACV,GAAGhE,IAAI;UACPoW,gBAAgB,EAAEhY;QACpB,CAAC,CAAC,GAAGK,IAAI;MACX;MACA,MAAMe,CAAC,GAAGpB,GAAG,CAACQ,KAAK,CAAC,IAAI,CAACsW,eAAe,CAAC;MACzC,MAAM/V,CAAC,GAAGK,CAAC,CAACkC,KAAK,CAAC,CAAC,CAAC2U,IAAI,CAAC,CAAC;MAC1B,MAAMC,CAAC,GAAG9W,CAAC,CAAC8C,IAAI,CAAC,IAAI,CAAC4S,eAAe,CAAC,CAACmB,IAAI,CAAC,CAAC;MAC7C,OAAO,IAAI,CAACzB,MAAM,CAACJ,oBAAoB,CAACxU,IAAI,EAAEC,WAAW,EAAEd,CAAC,EAAE,IAAI,CAAC6E,OAAO,CAAChC,YAAY,EAAE,IAAI,CAACgC,OAAO,CAAC2C,mBAAmB,CAAC,EAAE2P,CAAC,EAAErP,GAAG,EAAE;QAClI,GAAGjD,OAAO;QACV,GAAGhE,IAAI;QACPoW,gBAAgB,EAAEjX;MACpB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAACyW,WAAW,CAAC,CAAC;IAClB,MAAMW,2BAA2B,GAAGvS,OAAO,EAAEuS,2BAA2B,IAAI,IAAI,CAACvS,OAAO,CAACuS,2BAA2B;IACpH,MAAM3H,eAAe,GAAG5K,OAAO,EAAE0F,aAAa,EAAEkF,eAAe,KAAKtP,SAAS,GAAG0E,OAAO,CAAC0F,aAAa,CAACkF,eAAe,GAAG,IAAI,CAAC5K,OAAO,CAAC0F,aAAa,CAACkF,eAAe;IAClK,MAAM4H,KAAK,GAAG,CAAC;MACbC,KAAK,EAAE,IAAI,CAACR,cAAc;MAC1BS,SAAS,EAAEhC,GAAG,IAAID,SAAS,CAACC,GAAG;IACjC,CAAC,EAAE;MACD+B,KAAK,EAAE,IAAI,CAACT,MAAM;MAClBU,SAAS,EAAEhC,GAAG,IAAI,IAAI,CAACG,WAAW,GAAGJ,SAAS,CAAC,IAAI,CAAC7T,MAAM,CAAC8T,GAAG,CAAC,CAAC,GAAGD,SAAS,CAACC,GAAG;IAClF,CAAC,CAAC;IACF8B,KAAK,CAACxY,OAAO,CAAC2Y,IAAI,IAAI;MACpBT,QAAQ,GAAG,CAAC;MACZ,OAAO/L,KAAK,GAAGwM,IAAI,CAACF,KAAK,CAACG,IAAI,CAAClW,GAAG,CAAC,EAAE;QACnC,MAAMmW,UAAU,GAAG1M,KAAK,CAAC,CAAC,CAAC,CAACkM,IAAI,CAAC,CAAC;QAClCnW,KAAK,GAAGiW,YAAY,CAACU,UAAU,CAAC;QAChC,IAAI3W,KAAK,KAAKZ,SAAS,EAAE;UACvB,IAAI,OAAOiX,2BAA2B,KAAK,UAAU,EAAE;YACrD,MAAMO,IAAI,GAAGP,2BAA2B,CAAC7V,GAAG,EAAEyJ,KAAK,EAAEnG,OAAO,CAAC;YAC7D9D,KAAK,GAAGjD,QAAQ,CAAC6Z,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;UACpC,CAAC,MAAM,IAAI9S,OAAO,IAAIjF,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC8E,OAAO,EAAE6S,UAAU,CAAC,EAAE;YAC/E3W,KAAK,GAAG,EAAE;UACZ,CAAC,MAAM,IAAI0O,eAAe,EAAE;YAC1B1O,KAAK,GAAGiK,KAAK,CAAC,CAAC,CAAC;YAChB;UACF,CAAC,MAAM;YACL,IAAI,CAAChG,MAAM,CAACT,IAAI,CAAC,8BAA8BmT,UAAU,sBAAsBnW,GAAG,EAAE,CAAC;YACrFR,KAAK,GAAG,EAAE;UACZ;QACF,CAAC,MAAM,IAAI,CAACjD,QAAQ,CAACiD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC6U,mBAAmB,EAAE;UACxD7U,KAAK,GAAGxC,UAAU,CAACwC,KAAK,CAAC;QAC3B;QACA,MAAMwW,SAAS,GAAGC,IAAI,CAACD,SAAS,CAACxW,KAAK,CAAC;QACvCQ,GAAG,GAAGA,GAAG,CAACpC,OAAO,CAAC6L,KAAK,CAAC,CAAC,CAAC,EAAEuM,SAAS,CAAC;QACtC,IAAI9H,eAAe,EAAE;UACnB+H,IAAI,CAACF,KAAK,CAACV,SAAS,IAAI7V,KAAK,CAACpB,MAAM;UACpC6X,IAAI,CAACF,KAAK,CAACV,SAAS,IAAI5L,KAAK,CAAC,CAAC,CAAC,CAACrL,MAAM;QACzC,CAAC,MAAM;UACL6X,IAAI,CAACF,KAAK,CAACV,SAAS,GAAG,CAAC;QAC1B;QACAG,QAAQ,EAAE;QACV,IAAIA,QAAQ,IAAI,IAAI,CAACR,WAAW,EAAE;UAChC;QACF;MACF;IACF,CAAC,CAAC;IACF,OAAOhV,GAAG;EACZ;EACAwO,IAAIA,CAACxO,GAAG,EAAE+R,EAAE,EAAgB;IAAA,IAAdzO,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACxB,IAAIuH,KAAK;IACT,IAAIjK,KAAK;IACT,IAAI6W,aAAa;IACjB,MAAMC,gBAAgB,GAAGA,CAAC5Y,GAAG,EAAE6Y,gBAAgB,KAAK;MAClD,MAAMC,GAAG,GAAG,IAAI,CAACzB,uBAAuB;MACxC,IAAIrX,GAAG,CAACC,OAAO,CAAC6Y,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO9Y,GAAG;MACpC,MAAM+D,CAAC,GAAG/D,GAAG,CAACQ,KAAK,CAAC,IAAI4C,MAAM,CAAC,GAAG0V,GAAG,OAAO,CAAC,CAAC;MAC9C,IAAIC,aAAa,GAAG,IAAIhV,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B/D,GAAG,GAAG+D,CAAC,CAAC,CAAC,CAAC;MACVgV,aAAa,GAAG,IAAI,CAACpI,WAAW,CAACoI,aAAa,EAAEJ,aAAa,CAAC;MAC9D,MAAMK,mBAAmB,GAAGD,aAAa,CAAChN,KAAK,CAAC,IAAI,CAAC;MACrD,MAAMkN,mBAAmB,GAAGF,aAAa,CAAChN,KAAK,CAAC,IAAI,CAAC;MACrD,IAAI,CAACiN,mBAAmB,EAAEtY,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAACuY,mBAAmB,IAAIA,mBAAmB,CAACvY,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QAChHqY,aAAa,GAAGA,aAAa,CAAC7Y,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAClD;MACA,IAAI;QACFyY,aAAa,GAAGnP,IAAI,CAACC,KAAK,CAACsP,aAAa,CAAC;QACzC,IAAIF,gBAAgB,EAAEF,aAAa,GAAG;UACpC,GAAGE,gBAAgB;UACnB,GAAGF;QACL,CAAC;MACH,CAAC,CAAC,OAAOxX,CAAC,EAAE;QACV,IAAI,CAAC4E,MAAM,CAACT,IAAI,CAAC,oDAAoDtF,GAAG,EAAE,EAAEmB,CAAC,CAAC;QAC9E,OAAO,GAAGnB,GAAG,GAAG8Y,GAAG,GAAGC,aAAa,EAAE;MACvC;MACA,IAAIJ,aAAa,CAAC3K,YAAY,IAAI2K,aAAa,CAAC3K,YAAY,CAAC/N,OAAO,CAAC,IAAI,CAAC6F,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO6S,aAAa,CAAC3K,YAAY;MACzH,OAAOhO,GAAG;IACZ,CAAC;IACD,OAAO+L,KAAK,GAAG,IAAI,CAACE,aAAa,CAACuM,IAAI,CAAClW,GAAG,CAAC,EAAE;MAC3C,IAAI4W,UAAU,GAAG,EAAE;MACnBP,aAAa,GAAG;QACd,GAAG/S;MACL,CAAC;MACD+S,aAAa,GAAGA,aAAa,CAACzY,OAAO,IAAI,CAACrB,QAAQ,CAAC8Z,aAAa,CAACzY,OAAO,CAAC,GAAGyY,aAAa,CAACzY,OAAO,GAAGyY,aAAa;MACjHA,aAAa,CAACtH,kBAAkB,GAAG,KAAK;MACxC,OAAOsH,aAAa,CAAC3K,YAAY;MACjC,MAAMmL,WAAW,GAAG,MAAM,CAAC/U,IAAI,CAAC2H,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACqN,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGrN,KAAK,CAAC,CAAC,CAAC,CAAC9L,OAAO,CAAC,IAAI,CAAC6W,eAAe,CAAC;MAClH,IAAIqC,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBD,UAAU,GAAGnN,KAAK,CAAC,CAAC,CAAC,CAAC1K,KAAK,CAAC8X,WAAW,CAAC,CAAC3Y,KAAK,CAAC,IAAI,CAACsW,eAAe,CAAC,CAAC7S,GAAG,CAACoV,IAAI,IAAIA,IAAI,CAACpB,IAAI,CAAC,CAAC,CAAC,CAACnU,MAAM,CAACwV,OAAO,CAAC;QAC7GvN,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC1K,KAAK,CAAC,CAAC,EAAE8X,WAAW,CAAC;MAC3C;MACArX,KAAK,GAAGuS,EAAE,CAACuE,gBAAgB,CAAC9X,IAAI,CAAC,IAAI,EAAEiL,KAAK,CAAC,CAAC,CAAC,CAACkM,IAAI,CAAC,CAAC,EAAEU,aAAa,CAAC,EAAEA,aAAa,CAAC;MACtF,IAAI7W,KAAK,IAAIiK,KAAK,CAAC,CAAC,CAAC,KAAKzJ,GAAG,IAAI,CAACzD,QAAQ,CAACiD,KAAK,CAAC,EAAE,OAAOA,KAAK;MAC/D,IAAI,CAACjD,QAAQ,CAACiD,KAAK,CAAC,EAAEA,KAAK,GAAGxC,UAAU,CAACwC,KAAK,CAAC;MAC/C,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACiE,MAAM,CAACT,IAAI,CAAC,qBAAqByG,KAAK,CAAC,CAAC,CAAC,gBAAgBzJ,GAAG,EAAE,CAAC;QACpER,KAAK,GAAG,EAAE;MACZ;MACA,IAAIoX,UAAU,CAACxY,MAAM,EAAE;QACrBoB,KAAK,GAAGoX,UAAU,CAACK,MAAM,CAAC,CAACpP,CAAC,EAAE+N,CAAC,KAAK,IAAI,CAAC1B,MAAM,CAACrM,CAAC,EAAE+N,CAAC,EAAEtS,OAAO,CAACiD,GAAG,EAAE;UACjE,GAAGjD,OAAO;UACVoS,gBAAgB,EAAEjM,KAAK,CAAC,CAAC,CAAC,CAACkM,IAAI,CAAC;QAClC,CAAC,CAAC,EAAEnW,KAAK,CAACmW,IAAI,CAAC,CAAC,CAAC;MACnB;MACA3V,GAAG,GAAGA,GAAG,CAACpC,OAAO,CAAC6L,KAAK,CAAC,CAAC,CAAC,EAAEjK,KAAK,CAAC;MAClC,IAAI,CAAC8V,MAAM,CAACD,SAAS,GAAG,CAAC;IAC3B;IACA,OAAOrV,GAAG;EACZ;AACF;AAEA,MAAMkX,cAAc,GAAGC,SAAS,IAAI;EAClC,IAAIC,UAAU,GAAGD,SAAS,CAAChN,WAAW,CAAC,CAAC,CAACwL,IAAI,CAAC,CAAC;EAC/C,MAAM0B,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIF,SAAS,CAACxZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAMmB,CAAC,GAAGqY,SAAS,CAACjZ,KAAK,CAAC,GAAG,CAAC;IAC9BkZ,UAAU,GAAGtY,CAAC,CAAC,CAAC,CAAC,CAACqL,WAAW,CAAC,CAAC,CAACwL,IAAI,CAAC,CAAC;IACtC,MAAM2B,MAAM,GAAGxY,CAAC,CAAC,CAAC,CAAC,CAACkD,SAAS,CAAC,CAAC,EAAElD,CAAC,CAAC,CAAC,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;IACjD,IAAIgZ,UAAU,KAAK,UAAU,IAAIE,MAAM,CAAC3Z,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACxD,IAAI,CAAC0Z,aAAa,CAACE,QAAQ,EAAEF,aAAa,CAACE,QAAQ,GAAGD,MAAM,CAAC3B,IAAI,CAAC,CAAC;IACrE,CAAC,MAAM,IAAIyB,UAAU,KAAK,cAAc,IAAIE,MAAM,CAAC3Z,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACnE,IAAI,CAAC0Z,aAAa,CAACG,KAAK,EAAEH,aAAa,CAACG,KAAK,GAAGF,MAAM,CAAC3B,IAAI,CAAC,CAAC;IAC/D,CAAC,MAAM;MACL,MAAM8B,IAAI,GAAGH,MAAM,CAACpZ,KAAK,CAAC,GAAG,CAAC;MAC9BuZ,IAAI,CAACna,OAAO,CAAC2L,GAAG,IAAI;QAClB,IAAIA,GAAG,EAAE;UACP,MAAM,CAACvL,GAAG,EAAE,GAAGga,IAAI,CAAC,GAAGzO,GAAG,CAAC/K,KAAK,CAAC,GAAG,CAAC;UACrC,MAAM8V,GAAG,GAAG0D,IAAI,CAAC9V,IAAI,CAAC,GAAG,CAAC,CAAC+T,IAAI,CAAC,CAAC,CAAC/X,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;UACzD,MAAM+Z,UAAU,GAAGja,GAAG,CAACiY,IAAI,CAAC,CAAC;UAC7B,IAAI,CAAC0B,aAAa,CAACM,UAAU,CAAC,EAAEN,aAAa,CAACM,UAAU,CAAC,GAAG3D,GAAG;UAC/D,IAAIA,GAAG,KAAK,OAAO,EAAEqD,aAAa,CAACM,UAAU,CAAC,GAAG,KAAK;UACtD,IAAI3D,GAAG,KAAK,MAAM,EAAEqD,aAAa,CAACM,UAAU,CAAC,GAAG,IAAI;UACpD,IAAI,CAACC,KAAK,CAAC5D,GAAG,CAAC,EAAEqD,aAAa,CAACM,UAAU,CAAC,GAAGE,QAAQ,CAAC7D,GAAG,EAAE,EAAE,CAAC;QAChE;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAO;IACLoD,UAAU;IACVC;EACF,CAAC;AACH,CAAC;AACD,MAAMS,qBAAqB,GAAGC,EAAE,IAAI;EAClC,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,OAAO,CAACnQ,CAAC,EAAEsF,CAAC,EAAEpE,CAAC,KAAK;IAClB,IAAIkP,WAAW,GAAGlP,CAAC;IACnB,IAAIA,CAAC,IAAIA,CAAC,CAAC2M,gBAAgB,IAAI3M,CAAC,CAACmP,YAAY,IAAInP,CAAC,CAACmP,YAAY,CAACnP,CAAC,CAAC2M,gBAAgB,CAAC,IAAI3M,CAAC,CAACA,CAAC,CAAC2M,gBAAgB,CAAC,EAAE;MAC5GuC,WAAW,GAAG;QACZ,GAAGA,WAAW;QACd,CAAClP,CAAC,CAAC2M,gBAAgB,GAAG9W;MACxB,CAAC;IACH;IACA,MAAMlB,GAAG,GAAGyP,CAAC,GAAGjG,IAAI,CAACE,SAAS,CAAC6Q,WAAW,CAAC;IAC3C,IAAIE,GAAG,GAAGH,KAAK,CAACta,GAAG,CAAC;IACpB,IAAI,CAACya,GAAG,EAAE;MACRA,GAAG,GAAGJ,EAAE,CAACtV,cAAc,CAAC0K,CAAC,CAAC,EAAEpE,CAAC,CAAC;MAC9BiP,KAAK,CAACta,GAAG,CAAC,GAAGya,GAAG;IAClB;IACA,OAAOA,GAAG,CAACtQ,CAAC,CAAC;EACf,CAAC;AACH,CAAC;AACD,MAAMuQ,wBAAwB,GAAGL,EAAE,IAAI,CAAClQ,CAAC,EAAEsF,CAAC,EAAEpE,CAAC,KAAKgP,EAAE,CAACtV,cAAc,CAAC0K,CAAC,CAAC,EAAEpE,CAAC,CAAC,CAAClB,CAAC,CAAC;AAC/E,MAAMwQ,SAAS,CAAC;EACdjY,WAAWA,CAAA,EAAe;IAAA,IAAdkD,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACtB,IAAI,CAACuB,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,WAAW,CAAC;IAC5C,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,CAACD,OAAO,CAAC;EACpB;EACAC,IAAIA,CAACoF,QAAQ,EAEV;IAAA,IAFYrF,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG;MACvB8G,aAAa,EAAE,CAAC;IAClB,CAAC;IACC,IAAI,CAACwL,eAAe,GAAGlR,OAAO,CAAC0F,aAAa,CAACwL,eAAe,IAAI,GAAG;IACnE,MAAM8D,EAAE,GAAGhV,OAAO,CAACiV,mBAAmB,GAAGT,qBAAqB,GAAGM,wBAAwB;IACzF,IAAI,CAACI,OAAO,GAAG;MACbC,MAAM,EAAEH,EAAE,CAAC,CAAC/R,GAAG,EAAE0C,GAAG,KAAK;QACvB,MAAMyP,SAAS,GAAG,IAAI5H,IAAI,CAAC6H,YAAY,CAACpS,GAAG,EAAE;UAC3C,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO+K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACFuD,QAAQ,EAAEe,EAAE,CAAC,CAAC/R,GAAG,EAAE0C,GAAG,KAAK;QACzB,MAAMyP,SAAS,GAAG,IAAI5H,IAAI,CAAC6H,YAAY,CAACpS,GAAG,EAAE;UAC3C,GAAG0C,GAAG;UACN2P,KAAK,EAAE;QACT,CAAC,CAAC;QACF,OAAO5E,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACF6E,QAAQ,EAAEP,EAAE,CAAC,CAAC/R,GAAG,EAAE0C,GAAG,KAAK;QACzB,MAAMyP,SAAS,GAAG,IAAI5H,IAAI,CAACgI,cAAc,CAACvS,GAAG,EAAE;UAC7C,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO+K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC,CAAC;MACF+E,YAAY,EAAET,EAAE,CAAC,CAAC/R,GAAG,EAAE0C,GAAG,KAAK;QAC7B,MAAMyP,SAAS,GAAG,IAAI5H,IAAI,CAACkI,kBAAkB,CAACzS,GAAG,EAAE;UACjD,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO+K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,EAAE/K,GAAG,CAACuO,KAAK,IAAI,KAAK,CAAC;MACzD,CAAC,CAAC;MACFyB,IAAI,EAAEX,EAAE,CAAC,CAAC/R,GAAG,EAAE0C,GAAG,KAAK;QACrB,MAAMyP,SAAS,GAAG,IAAI5H,IAAI,CAACoI,UAAU,CAAC3S,GAAG,EAAE;UACzC,GAAG0C;QACL,CAAC,CAAC;QACF,OAAO+K,GAAG,IAAI0E,SAAS,CAACxE,MAAM,CAACF,GAAG,CAAC;MACrC,CAAC;IACH,CAAC;EACH;EACAmF,GAAGA,CAAChR,IAAI,EAAE4J,EAAE,EAAE;IACZ,IAAI,CAACyG,OAAO,CAACrQ,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACwL,IAAI,CAAC,CAAC,CAAC,GAAG5D,EAAE;EAC9C;EACAqH,SAASA,CAACjR,IAAI,EAAE4J,EAAE,EAAE;IAClB,IAAI,CAACyG,OAAO,CAACrQ,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACwL,IAAI,CAAC,CAAC,CAAC,GAAGmC,qBAAqB,CAAC/F,EAAE,CAAC;EACrE;EACAmC,MAAMA,CAAC1U,KAAK,EAAE0U,MAAM,EAAE3N,GAAG,EAAgB;IAAA,IAAdjD,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACrC,MAAMsW,OAAO,GAAGtE,MAAM,CAAChW,KAAK,CAAC,IAAI,CAACsW,eAAe,CAAC;IAClD,IAAIgE,OAAO,CAACpa,MAAM,GAAG,CAAC,IAAIoa,OAAO,CAAC,CAAC,CAAC,CAAC7a,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI6a,OAAO,CAAC,CAAC,CAAC,CAAC7a,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI6a,OAAO,CAAC5Q,IAAI,CAACgO,CAAC,IAAIA,CAAC,CAACjY,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MAC9H,MAAM0X,SAAS,GAAGmD,OAAO,CAACa,SAAS,CAACzD,CAAC,IAAIA,CAAC,CAACjY,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7D6a,OAAO,CAAC,CAAC,CAAC,GAAG,CAACA,OAAO,CAAC,CAAC,CAAC,EAAE,GAAGA,OAAO,CAACnS,MAAM,CAAC,CAAC,EAAEgP,SAAS,CAAC,CAAC,CAACzT,IAAI,CAAC,IAAI,CAAC4S,eAAe,CAAC;IACvF;IACA,MAAM/N,MAAM,GAAG+R,OAAO,CAACvB,MAAM,CAAC,CAACqC,GAAG,EAAE1D,CAAC,KAAK;MACxC,MAAM;QACJwB,UAAU;QACVC;MACF,CAAC,GAAGH,cAAc,CAACtB,CAAC,CAAC;MACrB,IAAI,IAAI,CAAC4C,OAAO,CAACpB,UAAU,CAAC,EAAE;QAC5B,IAAImC,SAAS,GAAGD,GAAG;QACnB,IAAI;UACF,MAAME,UAAU,GAAGlW,OAAO,EAAE4U,YAAY,GAAG5U,OAAO,CAACoS,gBAAgB,CAAC,IAAI,CAAC,CAAC;UAC1E,MAAMvI,CAAC,GAAGqM,UAAU,CAACC,MAAM,IAAID,UAAU,CAACjT,GAAG,IAAIjD,OAAO,CAACmW,MAAM,IAAInW,OAAO,CAACiD,GAAG,IAAIA,GAAG;UACrFgT,SAAS,GAAG,IAAI,CAACf,OAAO,CAACpB,UAAU,CAAC,CAACkC,GAAG,EAAEnM,CAAC,EAAE;YAC3C,GAAGkK,aAAa;YAChB,GAAG/T,OAAO;YACV,GAAGkW;UACL,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOvW,KAAK,EAAE;UACd,IAAI,CAACQ,MAAM,CAACT,IAAI,CAACC,KAAK,CAAC;QACzB;QACA,OAAOsW,SAAS;MAClB,CAAC,MAAM;QACL,IAAI,CAAC9V,MAAM,CAACT,IAAI,CAAC,oCAAoCoU,UAAU,EAAE,CAAC;MACpE;MACA,OAAOkC,GAAG;IACZ,CAAC,EAAE9Z,KAAK,CAAC;IACT,OAAOiH,MAAM;EACf;AACF;AAEA,MAAMiT,aAAa,GAAGA,CAACC,CAAC,EAAExR,IAAI,KAAK;EACjC,IAAIwR,CAAC,CAACC,OAAO,CAACzR,IAAI,CAAC,KAAKvJ,SAAS,EAAE;IACjC,OAAO+a,CAAC,CAACC,OAAO,CAACzR,IAAI,CAAC;IACtBwR,CAAC,CAACE,YAAY,EAAE;EAClB;AACF,CAAC;AACD,MAAMC,SAAS,SAASlV,YAAY,CAAC;EACnCxE,WAAWA,CAAC2Z,OAAO,EAAEC,KAAK,EAAErR,QAAQ,EAAgB;IAAA,IAAdrF,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAChD,KAAK,CAAC,CAAC;IACP,IAAI,CAAC6X,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACrR,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACkE,aAAa,GAAGlE,QAAQ,CAACkE,aAAa;IAC3C,IAAI,CAACvJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,MAAM,GAAGkB,UAAU,CAACH,MAAM,CAAC,kBAAkB,CAAC;IACnD,IAAI,CAACyV,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAG5W,OAAO,CAAC4W,gBAAgB,IAAI,EAAE;IACtD,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,UAAU,GAAG9W,OAAO,CAAC8W,UAAU,IAAI,CAAC,GAAG9W,OAAO,CAAC8W,UAAU,GAAG,CAAC;IAClE,IAAI,CAACC,YAAY,GAAG/W,OAAO,CAAC+W,YAAY,IAAI,CAAC,GAAG/W,OAAO,CAAC+W,YAAY,GAAG,GAAG;IAC1E,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACR,OAAO,EAAExW,IAAI,GAAGoF,QAAQ,EAAErF,OAAO,CAACyW,OAAO,EAAEzW,OAAO,CAAC;EAC1D;EACAkX,SAASA,CAACC,SAAS,EAAErR,UAAU,EAAE9F,OAAO,EAAEoX,QAAQ,EAAE;IAClD,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMf,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMgB,eAAe,GAAG,CAAC,CAAC;IAC1B,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAC3BJ,SAAS,CAACnd,OAAO,CAACiJ,GAAG,IAAI;MACvB,IAAIuU,gBAAgB,GAAG,IAAI;MAC3B1R,UAAU,CAAC9L,OAAO,CAACyI,EAAE,IAAI;QACvB,MAAMoC,IAAI,GAAG,GAAG5B,GAAG,IAAIR,EAAE,EAAE;QAC3B,IAAI,CAACzC,OAAO,CAACyX,MAAM,IAAI,IAAI,CAACf,KAAK,CAAC1S,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE;UAC5D,IAAI,CAACuU,KAAK,CAACnS,IAAI,CAAC,GAAG,CAAC;QACtB,CAAC,MAAM,IAAI,IAAI,CAACmS,KAAK,CAACnS,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAM,IAAI,IAAI,CAACmS,KAAK,CAACnS,IAAI,CAAC,KAAK,CAAC,EAAE;UAClE,IAAIyR,OAAO,CAACzR,IAAI,CAAC,KAAKvJ,SAAS,EAAEgb,OAAO,CAACzR,IAAI,CAAC,GAAG,IAAI;QACvD,CAAC,MAAM;UACL,IAAI,CAACmS,KAAK,CAACnS,IAAI,CAAC,GAAG,CAAC;UACpB2S,gBAAgB,GAAG,KAAK;UACxB,IAAIlB,OAAO,CAACzR,IAAI,CAAC,KAAKvJ,SAAS,EAAEgb,OAAO,CAACzR,IAAI,CAAC,GAAG,IAAI;UACrD,IAAIwS,MAAM,CAACxS,IAAI,CAAC,KAAKvJ,SAAS,EAAE+b,MAAM,CAACxS,IAAI,CAAC,GAAG,IAAI;UACnD,IAAI0S,gBAAgB,CAAC9U,EAAE,CAAC,KAAKnH,SAAS,EAAEic,gBAAgB,CAAC9U,EAAE,CAAC,GAAG,IAAI;QACrE;MACF,CAAC,CAAC;MACF,IAAI,CAAC+U,gBAAgB,EAAEF,eAAe,CAACrU,GAAG,CAAC,GAAG,IAAI;IACpD,CAAC,CAAC;IACF,IAAIlI,MAAM,CAACsJ,IAAI,CAACgT,MAAM,CAAC,CAACvc,MAAM,IAAIC,MAAM,CAACsJ,IAAI,CAACiS,OAAO,CAAC,CAACxb,MAAM,EAAE;MAC7D,IAAI,CAACmc,KAAK,CAACpb,IAAI,CAAC;QACdya,OAAO;QACPC,YAAY,EAAExb,MAAM,CAACsJ,IAAI,CAACiS,OAAO,CAAC,CAACxb,MAAM;QACzC4c,MAAM,EAAE,CAAC,CAAC;QACVC,MAAM,EAAE,EAAE;QACVP;MACF,CAAC,CAAC;IACJ;IACA,OAAO;MACLC,MAAM,EAAEtc,MAAM,CAACsJ,IAAI,CAACgT,MAAM,CAAC;MAC3Bf,OAAO,EAAEvb,MAAM,CAACsJ,IAAI,CAACiS,OAAO,CAAC;MAC7BgB,eAAe,EAAEvc,MAAM,CAACsJ,IAAI,CAACiT,eAAe,CAAC;MAC7CC,gBAAgB,EAAExc,MAAM,CAACsJ,IAAI,CAACkT,gBAAgB;IAChD,CAAC;EACH;EACAG,MAAMA,CAAC7S,IAAI,EAAEkL,GAAG,EAAE/T,IAAI,EAAE;IACtB,MAAMlC,CAAC,GAAG+K,IAAI,CAACjK,KAAK,CAAC,GAAG,CAAC;IACzB,MAAMqI,GAAG,GAAGnJ,CAAC,CAAC,CAAC,CAAC;IAChB,MAAM2I,EAAE,GAAG3I,CAAC,CAAC,CAAC,CAAC;IACf,IAAIiW,GAAG,EAAE,IAAI,CAACjO,IAAI,CAAC,eAAe,EAAEmB,GAAG,EAAER,EAAE,EAAEsN,GAAG,CAAC;IACjD,IAAI,CAACA,GAAG,IAAI/T,IAAI,EAAE;MAChB,IAAI,CAAC0a,KAAK,CAAClT,iBAAiB,CAACP,GAAG,EAAER,EAAE,EAAEzG,IAAI,EAAEV,SAAS,EAAEA,SAAS,EAAE;QAChEoI,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,IAAI,CAACsT,KAAK,CAACnS,IAAI,CAAC,GAAGkL,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/B,IAAIA,GAAG,IAAI/T,IAAI,EAAE,IAAI,CAACgb,KAAK,CAACnS,IAAI,CAAC,GAAG,CAAC;IACrC,MAAM6S,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAACT,KAAK,CAACjd,OAAO,CAACqc,CAAC,IAAI;MACtB1a,QAAQ,CAAC0a,CAAC,CAACqB,MAAM,EAAE,CAACzU,GAAG,CAAC,EAAER,EAAE,CAAC;MAC7B2T,aAAa,CAACC,CAAC,EAAExR,IAAI,CAAC;MACtB,IAAIkL,GAAG,EAAEsG,CAAC,CAACsB,MAAM,CAAC9b,IAAI,CAACkU,GAAG,CAAC;MAC3B,IAAIsG,CAAC,CAACE,YAAY,KAAK,CAAC,IAAI,CAACF,CAAC,CAACuB,IAAI,EAAE;QACnC7c,MAAM,CAACsJ,IAAI,CAACgS,CAAC,CAACqB,MAAM,CAAC,CAAC1d,OAAO,CAAC6P,CAAC,IAAI;UACjC,IAAI,CAAC6N,MAAM,CAAC7N,CAAC,CAAC,EAAE6N,MAAM,CAAC7N,CAAC,CAAC,GAAG,CAAC,CAAC;UAC9B,MAAMgO,UAAU,GAAGxB,CAAC,CAACqB,MAAM,CAAC7N,CAAC,CAAC;UAC9B,IAAIgO,UAAU,CAAC/c,MAAM,EAAE;YACrB+c,UAAU,CAAC7d,OAAO,CAACoK,CAAC,IAAI;cACtB,IAAIsT,MAAM,CAAC7N,CAAC,CAAC,CAACzF,CAAC,CAAC,KAAK9I,SAAS,EAAEoc,MAAM,CAAC7N,CAAC,CAAC,CAACzF,CAAC,CAAC,GAAG,IAAI;YACrD,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QACFiS,CAAC,CAACuB,IAAI,GAAG,IAAI;QACb,IAAIvB,CAAC,CAACsB,MAAM,CAAC7c,MAAM,EAAE;UACnBub,CAAC,CAACe,QAAQ,CAACf,CAAC,CAACsB,MAAM,CAAC;QACtB,CAAC,MAAM;UACLtB,CAAC,CAACe,QAAQ,CAAC,CAAC;QACd;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACtV,IAAI,CAAC,QAAQ,EAAE4V,MAAM,CAAC;IAC3B,IAAI,CAACT,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC/Y,MAAM,CAACmY,CAAC,IAAI,CAACA,CAAC,CAACuB,IAAI,CAAC;EAC9C;EACAE,IAAIA,CAAC7U,GAAG,EAAER,EAAE,EAAEsV,MAAM,EAAiD;IAAA,IAA/CC,KAAK,GAAApZ,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC;IAAA,IAAEqZ,IAAI,GAAArZ,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,IAAI,CAACmY,YAAY;IAAA,IAAEK,QAAQ,GAAAxY,SAAA,CAAA9D,MAAA,OAAA8D,SAAA,MAAAtD,SAAA;IACjE,IAAI,CAAC2H,GAAG,CAACnI,MAAM,EAAE,OAAOsc,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,CAACP,YAAY,IAAI,IAAI,CAACD,gBAAgB,EAAE;MAC9C,IAAI,CAACD,YAAY,CAAC9a,IAAI,CAAC;QACrBoH,GAAG;QACHR,EAAE;QACFsV,MAAM;QACNC,KAAK;QACLC,IAAI;QACJb;MACF,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACP,YAAY,EAAE;IACnB,MAAMqB,QAAQ,GAAGA,CAACnI,GAAG,EAAE/T,IAAI,KAAK;MAC9B,IAAI,CAAC6a,YAAY,EAAE;MACnB,IAAI,IAAI,CAACF,YAAY,CAAC7b,MAAM,GAAG,CAAC,EAAE;QAChC,MAAMkE,IAAI,GAAG,IAAI,CAAC2X,YAAY,CAACjZ,KAAK,CAAC,CAAC;QACtC,IAAI,CAACoa,IAAI,CAAC9Y,IAAI,CAACiE,GAAG,EAAEjE,IAAI,CAACyD,EAAE,EAAEzD,IAAI,CAAC+Y,MAAM,EAAE/Y,IAAI,CAACgZ,KAAK,EAAEhZ,IAAI,CAACiZ,IAAI,EAAEjZ,IAAI,CAACoY,QAAQ,CAAC;MACjF;MACA,IAAIrH,GAAG,IAAI/T,IAAI,IAAIgc,KAAK,GAAG,IAAI,CAAClB,UAAU,EAAE;QAC1CqB,UAAU,CAAC,MAAM;UACf,IAAI,CAACL,IAAI,CAAC5c,IAAI,CAAC,IAAI,EAAE+H,GAAG,EAAER,EAAE,EAAEsV,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEb,QAAQ,CAAC;QACtE,CAAC,EAAEa,IAAI,CAAC;QACR;MACF;MACAb,QAAQ,CAACrH,GAAG,EAAE/T,IAAI,CAAC;IACrB,CAAC;IACD,MAAMyS,EAAE,GAAG,IAAI,CAACgI,OAAO,CAACsB,MAAM,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC;IAClD,IAAIhI,EAAE,CAAC3T,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI;QACF,MAAMsD,CAAC,GAAGqQ,EAAE,CAACxL,GAAG,EAAER,EAAE,CAAC;QACrB,IAAIrE,CAAC,IAAI,OAAOA,CAAC,CAACia,IAAI,KAAK,UAAU,EAAE;UACrCja,CAAC,CAACia,IAAI,CAACrc,IAAI,IAAIkc,QAAQ,CAAC,IAAI,EAAElc,IAAI,CAAC,CAAC,CAACsc,KAAK,CAACJ,QAAQ,CAAC;QACtD,CAAC,MAAM;UACLA,QAAQ,CAAC,IAAI,EAAE9Z,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAO2R,GAAG,EAAE;QACZmI,QAAQ,CAACnI,GAAG,CAAC;MACf;MACA;IACF;IACA,OAAOtB,EAAE,CAACxL,GAAG,EAAER,EAAE,EAAEyV,QAAQ,CAAC;EAC9B;EACAK,cAAcA,CAACpB,SAAS,EAAErR,UAAU,EAA0B;IAAA,IAAxB9F,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEwY,QAAQ,GAAAxY,SAAA,CAAA9D,MAAA,OAAA8D,SAAA,MAAAtD,SAAA;IAC1D,IAAI,CAAC,IAAI,CAACmb,OAAO,EAAE;MACjB,IAAI,CAACtW,MAAM,CAACT,IAAI,CAAC,gEAAgE,CAAC;MAClF,OAAO0X,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAIne,QAAQ,CAACke,SAAS,CAAC,EAAEA,SAAS,GAAG,IAAI,CAAC5N,aAAa,CAACI,kBAAkB,CAACwN,SAAS,CAAC;IACrF,IAAIle,QAAQ,CAAC6M,UAAU,CAAC,EAAEA,UAAU,GAAG,CAACA,UAAU,CAAC;IACnD,MAAMuR,MAAM,GAAG,IAAI,CAACH,SAAS,CAACC,SAAS,EAAErR,UAAU,EAAE9F,OAAO,EAAEoX,QAAQ,CAAC;IACvE,IAAI,CAACC,MAAM,CAACA,MAAM,CAACvc,MAAM,EAAE;MACzB,IAAI,CAACuc,MAAM,CAACf,OAAO,CAACxb,MAAM,EAAEsc,QAAQ,CAAC,CAAC;MACtC,OAAO,IAAI;IACb;IACAC,MAAM,CAACA,MAAM,CAACrd,OAAO,CAAC6K,IAAI,IAAI;MAC5B,IAAI,CAAC2T,OAAO,CAAC3T,IAAI,CAAC;IACpB,CAAC,CAAC;EACJ;EACAgJ,IAAIA,CAACsJ,SAAS,EAAErR,UAAU,EAAEsR,QAAQ,EAAE;IACpC,IAAI,CAACmB,cAAc,CAACpB,SAAS,EAAErR,UAAU,EAAE,CAAC,CAAC,EAAEsR,QAAQ,CAAC;EAC1D;EACAK,MAAMA,CAACN,SAAS,EAAErR,UAAU,EAAEsR,QAAQ,EAAE;IACtC,IAAI,CAACmB,cAAc,CAACpB,SAAS,EAAErR,UAAU,EAAE;MACzC2R,MAAM,EAAE;IACV,CAAC,EAAEL,QAAQ,CAAC;EACd;EACAoB,OAAOA,CAAC3T,IAAI,EAAe;IAAA,IAAb3E,MAAM,GAAAtB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,EAAE;IACvB,MAAM9E,CAAC,GAAG+K,IAAI,CAACjK,KAAK,CAAC,GAAG,CAAC;IACzB,MAAMqI,GAAG,GAAGnJ,CAAC,CAAC,CAAC,CAAC;IAChB,MAAM2I,EAAE,GAAG3I,CAAC,CAAC,CAAC,CAAC;IACf,IAAI,CAACge,IAAI,CAAC7U,GAAG,EAAER,EAAE,EAAE,MAAM,EAAEnH,SAAS,EAAEA,SAAS,EAAE,CAACyU,GAAG,EAAE/T,IAAI,KAAK;MAC9D,IAAI+T,GAAG,EAAE,IAAI,CAAC5P,MAAM,CAACT,IAAI,CAAC,GAAGQ,MAAM,qBAAqBuC,EAAE,iBAAiBQ,GAAG,SAAS,EAAE8M,GAAG,CAAC;MAC7F,IAAI,CAACA,GAAG,IAAI/T,IAAI,EAAE,IAAI,CAACmE,MAAM,CAACZ,GAAG,CAAC,GAAGW,MAAM,oBAAoBuC,EAAE,iBAAiBQ,GAAG,EAAE,EAAEjH,IAAI,CAAC;MAC9F,IAAI,CAAC0b,MAAM,CAAC7S,IAAI,EAAEkL,GAAG,EAAE/T,IAAI,CAAC;IAC9B,CAAC,CAAC;EACJ;EACAkO,WAAWA,CAACiN,SAAS,EAAExQ,SAAS,EAAEvM,GAAG,EAAEqe,aAAa,EAAEC,QAAQ,EAAgC;IAAA,IAA9B1Y,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAE+Z,GAAG,GAAA/Z,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,MAAM,CAAC,CAAC;IAC1F,IAAI,IAAI,CAACyG,QAAQ,EAAE4G,KAAK,EAAEC,kBAAkB,IAAI,CAAC,IAAI,CAAC7G,QAAQ,EAAE4G,KAAK,EAAEC,kBAAkB,CAACvF,SAAS,CAAC,EAAE;MACpG,IAAI,CAACxG,MAAM,CAACT,IAAI,CAAC,qBAAqBtF,GAAG,uBAAuBuM,SAAS,sBAAsB,EAAE,0NAA0N,CAAC;MAC5T;IACF;IACA,IAAIvM,GAAG,KAAKkB,SAAS,IAAIlB,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,EAAE,EAAE;IACrD,IAAI,IAAI,CAACqc,OAAO,EAAEvV,MAAM,EAAE;MACxB,MAAMiT,IAAI,GAAG;QACX,GAAGnU,OAAO;QACV0Y;MACF,CAAC;MACD,MAAMjK,EAAE,GAAG,IAAI,CAACgI,OAAO,CAACvV,MAAM,CAACkX,IAAI,CAAC,IAAI,CAAC3B,OAAO,CAAC;MACjD,IAAIhI,EAAE,CAAC3T,MAAM,GAAG,CAAC,EAAE;QACjB,IAAI;UACF,IAAIsD,CAAC;UACL,IAAIqQ,EAAE,CAAC3T,MAAM,KAAK,CAAC,EAAE;YACnBsD,CAAC,GAAGqQ,EAAE,CAAC0I,SAAS,EAAExQ,SAAS,EAAEvM,GAAG,EAAEqe,aAAa,EAAEtE,IAAI,CAAC;UACxD,CAAC,MAAM;YACL/V,CAAC,GAAGqQ,EAAE,CAAC0I,SAAS,EAAExQ,SAAS,EAAEvM,GAAG,EAAEqe,aAAa,CAAC;UAClD;UACA,IAAIra,CAAC,IAAI,OAAOA,CAAC,CAACia,IAAI,KAAK,UAAU,EAAE;YACrCja,CAAC,CAACia,IAAI,CAACrc,IAAI,IAAI2c,GAAG,CAAC,IAAI,EAAE3c,IAAI,CAAC,CAAC,CAACsc,KAAK,CAACK,GAAG,CAAC;UAC5C,CAAC,MAAM;YACLA,GAAG,CAAC,IAAI,EAAEva,CAAC,CAAC;UACd;QACF,CAAC,CAAC,OAAO2R,GAAG,EAAE;UACZ4I,GAAG,CAAC5I,GAAG,CAAC;QACV;MACF,CAAC,MAAM;QACLtB,EAAE,CAAC0I,SAAS,EAAExQ,SAAS,EAAEvM,GAAG,EAAEqe,aAAa,EAAEE,GAAG,EAAExE,IAAI,CAAC;MACzD;IACF;IACA,IAAI,CAACgD,SAAS,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EAAE;IACjC,IAAI,CAACT,KAAK,CAACtT,WAAW,CAAC+T,SAAS,CAAC,CAAC,CAAC,EAAExQ,SAAS,EAAEvM,GAAG,EAAEqe,aAAa,CAAC;EACrE;AACF;AAEA,MAAMnb,GAAG,GAAGA,CAAA,MAAO;EACjB8C,KAAK,EAAE,KAAK;EACZwY,SAAS,EAAE,IAAI;EACfnW,EAAE,EAAE,CAAC,aAAa,CAAC;EACnBC,SAAS,EAAE,CAAC,aAAa,CAAC;EAC1B+G,WAAW,EAAE,CAAC,KAAK,CAAC;EACpBqC,UAAU,EAAE,KAAK;EACjBqB,aAAa,EAAE,KAAK;EACpBW,wBAAwB,EAAE,KAAK;EAC/BD,IAAI,EAAE,KAAK;EACXgL,OAAO,EAAE,KAAK;EACdC,oBAAoB,EAAE,IAAI;EAC1B9a,YAAY,EAAE,GAAG;EACjBD,WAAW,EAAE,GAAG;EAChBsK,eAAe,EAAE,GAAG;EACpBoE,gBAAgB,EAAE,GAAG;EACrBsM,uBAAuB,EAAE,KAAK;EAC9B7O,WAAW,EAAE,KAAK;EAClBf,aAAa,EAAE,KAAK;EACpBO,aAAa,EAAE,UAAU;EACzBS,kBAAkB,EAAE,IAAI;EACxBH,iBAAiB,EAAE,KAAK;EACxBuI,2BAA2B,EAAE,KAAK;EAClChH,WAAW,EAAE,KAAK;EAClBG,uBAAuB,EAAE,KAAK;EAC9BkB,UAAU,EAAE,KAAK;EACjBC,iBAAiB,EAAE,IAAI;EACvBpE,aAAa,EAAE,KAAK;EACpBlB,UAAU,EAAE,KAAK;EACjBmB,qBAAqB,EAAE,KAAK;EAC5B8B,sBAAsB,EAAE,KAAK;EAC7BD,2BAA2B,EAAE,KAAK;EAClC3D,uBAAuB,EAAE,KAAK;EAC9BH,gCAAgC,EAAEjH,IAAI,IAAI;IACxC,IAAIwZ,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,OAAOxZ,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAEwZ,GAAG,GAAGxZ,IAAI,CAAC,CAAC,CAAC;IAC9C,IAAIvG,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEwZ,GAAG,CAAC5Q,YAAY,GAAG5I,IAAI,CAAC,CAAC,CAAC;IACjD,IAAIvG,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEwZ,GAAG,CAACC,YAAY,GAAGzZ,IAAI,CAAC,CAAC,CAAC;IACjD,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC9D,MAAMQ,OAAO,GAAGR,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC;MAClCzE,MAAM,CAACsJ,IAAI,CAACrE,OAAO,CAAC,CAAChG,OAAO,CAACI,GAAG,IAAI;QAClC4e,GAAG,CAAC5e,GAAG,CAAC,GAAG4F,OAAO,CAAC5F,GAAG,CAAC;MACzB,CAAC,CAAC;IACJ;IACA,OAAO4e,GAAG;EACZ,CAAC;EACDtT,aAAa,EAAE;IACbmL,WAAW,EAAE,IAAI;IACjBD,MAAM,EAAE1U,KAAK,IAAIA,KAAK;IACtBgE,MAAM,EAAE,IAAI;IACZoK,MAAM,EAAE,IAAI;IACZ4G,eAAe,EAAE,GAAG;IACpBE,cAAc,EAAE,GAAG;IACnBC,aAAa,EAAE,KAAK;IACpBE,aAAa,EAAE,GAAG;IAClBE,uBAAuB,EAAE,GAAG;IAC5BC,WAAW,EAAE,IAAI;IACjB9G,eAAe,EAAE;EACnB,CAAC;EACDqK,mBAAmB,EAAE;AACvB,CAAC,CAAC;AACF,MAAMiE,gBAAgB,GAAGlZ,OAAO,IAAI;EAClC,IAAI/G,QAAQ,CAAC+G,OAAO,CAACyC,EAAE,CAAC,EAAEzC,OAAO,CAACyC,EAAE,GAAG,CAACzC,OAAO,CAACyC,EAAE,CAAC;EACnD,IAAIxJ,QAAQ,CAAC+G,OAAO,CAACyJ,WAAW,CAAC,EAAEzJ,OAAO,CAACyJ,WAAW,GAAG,CAACzJ,OAAO,CAACyJ,WAAW,CAAC;EAC9E,IAAIxQ,QAAQ,CAAC+G,OAAO,CAAC8L,UAAU,CAAC,EAAE9L,OAAO,CAAC8L,UAAU,GAAG,CAAC9L,OAAO,CAAC8L,UAAU,CAAC;EAC3E,IAAI9L,OAAO,CAACmN,aAAa,EAAE9S,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAClD2F,OAAO,CAACmN,aAAa,GAAGnN,OAAO,CAACmN,aAAa,CAACvR,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClE;EACA,IAAI,OAAOoE,OAAO,CAACmZ,aAAa,KAAK,SAAS,EAAEnZ,OAAO,CAAC4Y,SAAS,GAAG5Y,OAAO,CAACmZ,aAAa;EACzF,OAAOnZ,OAAO;AAChB,CAAC;AAED,MAAMoZ,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,MAAMC,mBAAmB,GAAGC,IAAI,IAAI;EAClC,MAAMC,IAAI,GAAGxe,MAAM,CAACye,mBAAmB,CAACze,MAAM,CAAC0e,cAAc,CAACH,IAAI,CAAC,CAAC;EACpEC,IAAI,CAACvf,OAAO,CAACgc,GAAG,IAAI;IAClB,IAAI,OAAOsD,IAAI,CAACtD,GAAG,CAAC,KAAK,UAAU,EAAE;MACnCsD,IAAI,CAACtD,GAAG,CAAC,GAAGsD,IAAI,CAACtD,GAAG,CAAC,CAACoC,IAAI,CAACkB,IAAI,CAAC;IAClC;EACF,CAAC,CAAC;AACJ,CAAC;AACD,MAAMI,IAAI,SAASpY,YAAY,CAAC;EAC9BxE,WAAWA,CAAA,EAAyB;IAAA,IAAxBkD,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEwY,QAAQ,GAAAxY,SAAA,CAAA9D,MAAA,OAAA8D,SAAA,MAAAtD,SAAA;IAChC,KAAK,CAAC,CAAC;IACP,IAAI,CAAC0E,OAAO,GAAGkZ,gBAAgB,CAAClZ,OAAO,CAAC;IACxC,IAAI,CAACqF,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAAClF,MAAM,GAAGkB,UAAU;IACxB,IAAI,CAACsY,OAAO,GAAG;MACbC,QAAQ,EAAE;IACZ,CAAC;IACDP,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAIjC,QAAQ,IAAI,CAAC,IAAI,CAACyC,aAAa,IAAI,CAAC7Z,OAAO,CAAC8Z,OAAO,EAAE;MACvD,IAAI,CAAC,IAAI,CAAC9Z,OAAO,CAAC4Y,SAAS,EAAE;QAC3B,IAAI,CAAC3Y,IAAI,CAACD,OAAO,EAAEoX,QAAQ,CAAC;QAC5B,OAAO,IAAI;MACb;MACAe,UAAU,CAAC,MAAM;QACf,IAAI,CAAClY,IAAI,CAACD,OAAO,EAAEoX,QAAQ,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EACAnX,IAAIA,CAAA,EAAyB;IAAA,IAAA8Z,MAAA;IAAA,IAAxB/Z,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEwY,QAAQ,GAAAxY,SAAA,CAAA9D,MAAA,OAAA8D,SAAA,MAAAtD,SAAA;IACzB,IAAI,CAAC0e,cAAc,GAAG,IAAI;IAC1B,IAAI,OAAOha,OAAO,KAAK,UAAU,EAAE;MACjCoX,QAAQ,GAAGpX,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,IAAIA,OAAO,CAAC0C,SAAS,IAAI,IAAI,IAAI1C,OAAO,CAACyC,EAAE,EAAE;MAC3C,IAAIxJ,QAAQ,CAAC+G,OAAO,CAACyC,EAAE,CAAC,EAAE;QACxBzC,OAAO,CAAC0C,SAAS,GAAG1C,OAAO,CAACyC,EAAE;MAChC,CAAC,MAAM,IAAIzC,OAAO,CAACyC,EAAE,CAACpI,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;QAChD2F,OAAO,CAAC0C,SAAS,GAAG1C,OAAO,CAACyC,EAAE,CAAC,CAAC,CAAC;MACnC;IACF;IACA,MAAMwX,OAAO,GAAG3c,GAAG,CAAC,CAAC;IACrB,IAAI,CAAC0C,OAAO,GAAG;MACb,GAAGia,OAAO;MACV,GAAG,IAAI,CAACja,OAAO;MACf,GAAGkZ,gBAAgB,CAAClZ,OAAO;IAC7B,CAAC;IACD,IAAI,CAACA,OAAO,CAAC0F,aAAa,GAAG;MAC3B,GAAGuU,OAAO,CAACvU,aAAa;MACxB,GAAG,IAAI,CAAC1F,OAAO,CAAC0F;IAClB,CAAC;IACD,IAAI1F,OAAO,CAAChC,YAAY,KAAK1C,SAAS,EAAE;MACtC,IAAI,CAAC0E,OAAO,CAACiG,uBAAuB,GAAGjG,OAAO,CAAChC,YAAY;IAC7D;IACA,IAAIgC,OAAO,CAACjC,WAAW,KAAKzC,SAAS,EAAE;MACrC,IAAI,CAAC0E,OAAO,CAACkG,sBAAsB,GAAGlG,OAAO,CAACjC,WAAW;IAC3D;IACA,MAAMmc,mBAAmB,GAAGC,aAAa,IAAI;MAC3C,IAAI,CAACA,aAAa,EAAE,OAAO,IAAI;MAC/B,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE,OAAO,IAAIA,aAAa,CAAC,CAAC;MACnE,OAAOA,aAAa;IACtB,CAAC;IACD,IAAI,CAAC,IAAI,CAACna,OAAO,CAAC8Z,OAAO,EAAE;MACzB,IAAI,IAAI,CAACH,OAAO,CAACxZ,MAAM,EAAE;QACvBkB,UAAU,CAACpB,IAAI,CAACia,mBAAmB,CAAC,IAAI,CAACP,OAAO,CAACxZ,MAAM,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;MACzE,CAAC,MAAM;QACLqB,UAAU,CAACpB,IAAI,CAAC,IAAI,EAAE,IAAI,CAACD,OAAO,CAAC;MACrC;MACA,IAAIoV,SAAS;MACb,IAAI,IAAI,CAACuE,OAAO,CAACvE,SAAS,EAAE;QAC1BA,SAAS,GAAG,IAAI,CAACuE,OAAO,CAACvE,SAAS;MACpC,CAAC,MAAM;QACLA,SAAS,GAAGL,SAAS;MACvB;MACA,MAAMqF,EAAE,GAAG,IAAIlN,YAAY,CAAC,IAAI,CAAClN,OAAO,CAAC;MACzC,IAAI,CAAC0W,KAAK,GAAG,IAAIlU,aAAa,CAAC,IAAI,CAACxC,OAAO,CAACuD,SAAS,EAAE,IAAI,CAACvD,OAAO,CAAC;MACpE,MAAMlG,CAAC,GAAG,IAAI,CAACuL,QAAQ;MACvBvL,CAAC,CAACqG,MAAM,GAAGkB,UAAU;MACrBvH,CAAC,CAACgT,aAAa,GAAG,IAAI,CAAC4J,KAAK;MAC5B5c,CAAC,CAACyP,aAAa,GAAG6Q,EAAE;MACpBtgB,CAAC,CAACiO,cAAc,GAAG,IAAIsH,cAAc,CAAC+K,EAAE,EAAE;QACxC7J,OAAO,EAAE,IAAI,CAACvQ,OAAO,CAACqI,eAAe;QACrCyQ,oBAAoB,EAAE,IAAI,CAAC9Y,OAAO,CAAC8Y;MACrC,CAAC,CAAC;MACF,MAAMuB,yBAAyB,GAAG,IAAI,CAACra,OAAO,CAAC0F,aAAa,CAACkL,MAAM,IAAI,IAAI,CAAC5Q,OAAO,CAAC0F,aAAa,CAACkL,MAAM,KAAKqJ,OAAO,CAACvU,aAAa,CAACkL,MAAM;MACzI,IAAIyJ,yBAAyB,EAAE;QAC7B,IAAI,CAACla,MAAM,CAACT,IAAI,CAAC,4IAA4I,CAAC;MAChK;MACA,IAAI0V,SAAS,KAAK,CAAC,IAAI,CAACpV,OAAO,CAAC0F,aAAa,CAACkL,MAAM,IAAI,IAAI,CAAC5Q,OAAO,CAAC0F,aAAa,CAACkL,MAAM,KAAKqJ,OAAO,CAACvU,aAAa,CAACkL,MAAM,CAAC,EAAE;QAC3H9W,CAAC,CAACsb,SAAS,GAAG8E,mBAAmB,CAAC9E,SAAS,CAAC;QAC5C,IAAItb,CAAC,CAACsb,SAAS,CAACnV,IAAI,EAAEnG,CAAC,CAACsb,SAAS,CAACnV,IAAI,CAACnG,CAAC,EAAE,IAAI,CAACkG,OAAO,CAAC;QACvD,IAAI,CAACA,OAAO,CAAC0F,aAAa,CAACkL,MAAM,GAAG9W,CAAC,CAACsb,SAAS,CAACxE,MAAM,CAACwH,IAAI,CAACte,CAAC,CAACsb,SAAS,CAAC;MAC1E;MACAtb,CAAC,CAACsM,YAAY,GAAG,IAAIuK,YAAY,CAAC,IAAI,CAAC3Q,OAAO,CAAC;MAC/ClG,CAAC,CAACmS,KAAK,GAAG;QACRC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACkM,IAAI,CAAC,IAAI;MACvD,CAAC;MACDte,CAAC,CAACmQ,gBAAgB,GAAG,IAAIuM,SAAS,CAAC0D,mBAAmB,CAAC,IAAI,CAACP,OAAO,CAAClD,OAAO,CAAC,EAAE3c,CAAC,CAACgT,aAAa,EAAEhT,CAAC,EAAE,IAAI,CAACkG,OAAO,CAAC;MAC/GlG,CAAC,CAACmQ,gBAAgB,CAACzI,EAAE,CAAC,GAAG,EAAE,UAACG,KAAK,EAAc;QAAA,SAAA2Y,KAAA,GAAA1b,SAAA,CAAA9D,MAAA,EAAT0E,IAAI,OAAAc,KAAA,CAAAga,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJ/a,IAAI,CAAA+a,KAAA,QAAA3b,SAAA,CAAA2b,KAAA;QAAA;QACxCR,MAAI,CAACjY,IAAI,CAACH,KAAK,EAAE,GAAGnC,IAAI,CAAC;MAC3B,CAAC,CAAC;MACF,IAAI,IAAI,CAACma,OAAO,CAACa,gBAAgB,EAAE;QACjC1gB,CAAC,CAAC0gB,gBAAgB,GAAGN,mBAAmB,CAAC,IAAI,CAACP,OAAO,CAACa,gBAAgB,CAAC;QACvE,IAAI1gB,CAAC,CAAC0gB,gBAAgB,CAACva,IAAI,EAAEnG,CAAC,CAAC0gB,gBAAgB,CAACva,IAAI,CAACnG,CAAC,EAAE,IAAI,CAACkG,OAAO,CAACya,SAAS,EAAE,IAAI,CAACza,OAAO,CAAC;MAC/F;MACA,IAAI,IAAI,CAAC2Z,OAAO,CAAClS,UAAU,EAAE;QAC3B3N,CAAC,CAAC2N,UAAU,GAAGyS,mBAAmB,CAAC,IAAI,CAACP,OAAO,CAAClS,UAAU,CAAC;QAC3D,IAAI3N,CAAC,CAAC2N,UAAU,CAACxH,IAAI,EAAEnG,CAAC,CAAC2N,UAAU,CAACxH,IAAI,CAAC,IAAI,CAAC;MAChD;MACA,IAAI,CAAC8E,UAAU,GAAG,IAAIK,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACrF,OAAO,CAAC;MAC7D,IAAI,CAAC+E,UAAU,CAACvD,EAAE,CAAC,GAAG,EAAE,UAACG,KAAK,EAAc;QAAA,SAAA+Y,KAAA,GAAA9b,SAAA,CAAA9D,MAAA,EAAT0E,IAAI,OAAAc,KAAA,CAAAoa,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJnb,IAAI,CAAAmb,KAAA,QAAA/b,SAAA,CAAA+b,KAAA;QAAA;QACrCZ,MAAI,CAACjY,IAAI,CAACH,KAAK,EAAE,GAAGnC,IAAI,CAAC;MAC3B,CAAC,CAAC;MACF,IAAI,CAACma,OAAO,CAACC,QAAQ,CAAC5f,OAAO,CAACC,CAAC,IAAI;QACjC,IAAIA,CAAC,CAACgG,IAAI,EAAEhG,CAAC,CAACgG,IAAI,CAAC,IAAI,CAAC;MAC1B,CAAC,CAAC;IACJ;IACA,IAAI,CAAC2Q,MAAM,GAAG,IAAI,CAAC5Q,OAAO,CAAC0F,aAAa,CAACkL,MAAM;IAC/C,IAAI,CAACwG,QAAQ,EAAEA,QAAQ,GAAGgC,IAAI;IAC9B,IAAI,IAAI,CAACpZ,OAAO,CAACyJ,WAAW,IAAI,CAAC,IAAI,CAACpE,QAAQ,CAACmV,gBAAgB,IAAI,CAAC,IAAI,CAACxa,OAAO,CAACiD,GAAG,EAAE;MACpF,MAAM+I,KAAK,GAAG,IAAI,CAAC3G,QAAQ,CAACkE,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAACxJ,OAAO,CAACyJ,WAAW,CAAC;MACpF,IAAIuC,KAAK,CAAClR,MAAM,GAAG,CAAC,IAAIkR,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,IAAI,CAAChM,OAAO,CAACiD,GAAG,GAAG+I,KAAK,CAAC,CAAC,CAAC;IACzE;IACA,IAAI,CAAC,IAAI,CAAC3G,QAAQ,CAACmV,gBAAgB,IAAI,CAAC,IAAI,CAACxa,OAAO,CAACiD,GAAG,EAAE;MACxD,IAAI,CAAC9C,MAAM,CAACT,IAAI,CAAC,yDAAyD,CAAC;IAC7E;IACA,MAAMkb,QAAQ,GAAG,CAAC,aAAa,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;IAC/FA,QAAQ,CAAC5gB,OAAO,CAAC+d,MAAM,IAAI;MACzB,IAAI,CAACA,MAAM,CAAC,GAAG;QAAA,OAAagC,MAAI,CAACrD,KAAK,CAACqB,MAAM,CAAC,CAAC,GAAAnZ,SAAO,CAAC;MAAA;IACzD,CAAC,CAAC;IACF,MAAMic,eAAe,GAAG,CAAC,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;IACpGA,eAAe,CAAC7gB,OAAO,CAAC+d,MAAM,IAAI;MAChC,IAAI,CAACA,MAAM,CAAC,GAAG,YAAa;QAC1BgC,MAAI,CAACrD,KAAK,CAACqB,MAAM,CAAC,CAAC,GAAAnZ,SAAO,CAAC;QAC3B,OAAOmb,MAAI;MACb,CAAC;IACH,CAAC,CAAC;IACF,MAAMe,QAAQ,GAAG3hB,KAAK,CAAC,CAAC;IACxB,MAAM0U,IAAI,GAAGA,CAAA,KAAM;MACjB,MAAMkN,MAAM,GAAGA,CAAChL,GAAG,EAAEhW,CAAC,KAAK;QACzB,IAAI,CAACigB,cAAc,GAAG,KAAK;QAC3B,IAAI,IAAI,CAACH,aAAa,IAAI,CAAC,IAAI,CAACmB,oBAAoB,EAAE,IAAI,CAAC7a,MAAM,CAACT,IAAI,CAAC,uEAAuE,CAAC;QAC/I,IAAI,CAACma,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,IAAI,CAAC7Z,OAAO,CAAC8Z,OAAO,EAAE,IAAI,CAAC3Z,MAAM,CAACZ,GAAG,CAAC,aAAa,EAAE,IAAI,CAACS,OAAO,CAAC;QACvE,IAAI,CAAC8B,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC9B,OAAO,CAAC;QACtC8a,QAAQ,CAACthB,OAAO,CAACO,CAAC,CAAC;QACnBqd,QAAQ,CAACrH,GAAG,EAAEhW,CAAC,CAAC;MAClB,CAAC;MACD,IAAI,IAAI,CAACod,SAAS,IAAI,CAAC,IAAI,CAAC0C,aAAa,EAAE,OAAOkB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAChhB,CAAC,CAACqe,IAAI,CAAC,IAAI,CAAC,CAAC;MACjF,IAAI,CAAC9S,cAAc,CAAC,IAAI,CAACtF,OAAO,CAACiD,GAAG,EAAE8X,MAAM,CAAC;IAC/C,CAAC;IACD,IAAI,IAAI,CAAC/a,OAAO,CAACuD,SAAS,IAAI,CAAC,IAAI,CAACvD,OAAO,CAAC4Y,SAAS,EAAE;MACrD/K,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLsK,UAAU,CAACtK,IAAI,EAAE,CAAC,CAAC;IACrB;IACA,OAAOiN,QAAQ;EACjB;EACAG,aAAaA,CAAC1V,QAAQ,EAAmB;IAAA,IAAjB6R,QAAQ,GAAAxY,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAGwa,IAAI;IACrC,IAAI8B,YAAY,GAAG9D,QAAQ;IAC3B,MAAMpQ,OAAO,GAAG/N,QAAQ,CAACsM,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC7D,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE2V,YAAY,GAAG3V,QAAQ;IAC3D,IAAI,CAAC,IAAI,CAACvF,OAAO,CAACuD,SAAS,IAAI,IAAI,CAACvD,OAAO,CAAC+Y,uBAAuB,EAAE;MACnE,IAAI/R,OAAO,EAAEH,WAAW,CAAC,CAAC,KAAK,QAAQ,KAAK,CAAC,IAAI,CAAC7G,OAAO,CAAC6Y,OAAO,IAAI,IAAI,CAAC7Y,OAAO,CAAC6Y,OAAO,CAAC/d,MAAM,KAAK,CAAC,CAAC,EAAE,OAAOogB,YAAY,CAAC,CAAC;MAC9H,MAAM7D,MAAM,GAAG,EAAE;MACjB,MAAM8D,MAAM,GAAGlY,GAAG,IAAI;QACpB,IAAI,CAACA,GAAG,EAAE;QACV,IAAIA,GAAG,KAAK,QAAQ,EAAE;QACtB,MAAMoG,IAAI,GAAG,IAAI,CAAChE,QAAQ,CAACkE,aAAa,CAACI,kBAAkB,CAAC1G,GAAG,CAAC;QAChEoG,IAAI,CAACrP,OAAO,CAAC6P,CAAC,IAAI;UAChB,IAAIA,CAAC,KAAK,QAAQ,EAAE;UACpB,IAAIwN,MAAM,CAAChd,OAAO,CAACwP,CAAC,CAAC,GAAG,CAAC,EAAEwN,MAAM,CAACxb,IAAI,CAACgO,CAAC,CAAC;QAC3C,CAAC,CAAC;MACJ,CAAC;MACD,IAAI,CAAC7C,OAAO,EAAE;QACZ,MAAMoH,SAAS,GAAG,IAAI,CAAC/I,QAAQ,CAACkE,aAAa,CAACC,gBAAgB,CAAC,IAAI,CAACxJ,OAAO,CAACyJ,WAAW,CAAC;QACxF2E,SAAS,CAACpU,OAAO,CAAC6P,CAAC,IAAIsR,MAAM,CAACtR,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM;QACLsR,MAAM,CAACnU,OAAO,CAAC;MACjB;MACA,IAAI,CAAChH,OAAO,CAAC6Y,OAAO,EAAE7e,OAAO,GAAG6P,CAAC,IAAIsR,MAAM,CAACtR,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACxE,QAAQ,CAAC4E,gBAAgB,CAAC4D,IAAI,CAACwJ,MAAM,EAAE,IAAI,CAACrX,OAAO,CAACyC,EAAE,EAAElH,CAAC,IAAI;QAChE,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,CAAC6f,gBAAgB,IAAI,IAAI,CAAC7V,QAAQ,EAAE,IAAI,CAAC8V,mBAAmB,CAAC,IAAI,CAAC9V,QAAQ,CAAC;QAC1F2V,YAAY,CAAC3f,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL2f,YAAY,CAAC,IAAI,CAAC;IACpB;EACF;EACAI,eAAeA,CAACjS,IAAI,EAAE5G,EAAE,EAAE2U,QAAQ,EAAE;IAClC,MAAM0D,QAAQ,GAAG3hB,KAAK,CAAC,CAAC;IACxB,IAAI,OAAOkQ,IAAI,KAAK,UAAU,EAAE;MAC9B+N,QAAQ,GAAG/N,IAAI;MACfA,IAAI,GAAG/N,SAAS;IAClB;IACA,IAAI,OAAOmH,EAAE,KAAK,UAAU,EAAE;MAC5B2U,QAAQ,GAAG3U,EAAE;MACbA,EAAE,GAAGnH,SAAS;IAChB;IACA,IAAI,CAAC+N,IAAI,EAAEA,IAAI,GAAG,IAAI,CAAC8N,SAAS;IAChC,IAAI,CAAC1U,EAAE,EAAEA,EAAE,GAAG,IAAI,CAACzC,OAAO,CAACyC,EAAE;IAC7B,IAAI,CAAC2U,QAAQ,EAAEA,QAAQ,GAAGgC,IAAI;IAC9B,IAAI,CAAC/T,QAAQ,CAAC4E,gBAAgB,CAACwN,MAAM,CAACpO,IAAI,EAAE5G,EAAE,EAAEsN,GAAG,IAAI;MACrD+K,QAAQ,CAACthB,OAAO,CAAC,CAAC;MAClB4d,QAAQ,CAACrH,GAAG,CAAC;IACf,CAAC,CAAC;IACF,OAAO+K,QAAQ;EACjB;EACAS,GAAGA,CAAC3W,MAAM,EAAE;IACV,IAAI,CAACA,MAAM,EAAE,MAAM,IAAI4W,KAAK,CAAC,+FAA+F,CAAC;IAC7H,IAAI,CAAC5W,MAAM,CAACtF,IAAI,EAAE,MAAM,IAAIkc,KAAK,CAAC,0FAA0F,CAAC;IAC7H,IAAI5W,MAAM,CAACtF,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAI,CAACqa,OAAO,CAAClD,OAAO,GAAG7R,MAAM;IAC/B;IACA,IAAIA,MAAM,CAACtF,IAAI,KAAK,QAAQ,IAAIsF,MAAM,CAACrF,GAAG,IAAIqF,MAAM,CAAClF,IAAI,IAAIkF,MAAM,CAACjF,KAAK,EAAE;MACzE,IAAI,CAACga,OAAO,CAACxZ,MAAM,GAAGyE,MAAM;IAC9B;IACA,IAAIA,MAAM,CAACtF,IAAI,KAAK,kBAAkB,EAAE;MACtC,IAAI,CAACqa,OAAO,CAACa,gBAAgB,GAAG5V,MAAM;IACxC;IACA,IAAIA,MAAM,CAACtF,IAAI,KAAK,YAAY,EAAE;MAChC,IAAI,CAACqa,OAAO,CAAClS,UAAU,GAAG7C,MAAM;IAClC;IACA,IAAIA,MAAM,CAACtF,IAAI,KAAK,eAAe,EAAE;MACnCmF,aAAa,CAACE,gBAAgB,CAACC,MAAM,CAAC;IACxC;IACA,IAAIA,MAAM,CAACtF,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAI,CAACqa,OAAO,CAACvE,SAAS,GAAGxQ,MAAM;IACjC;IACA,IAAIA,MAAM,CAACtF,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI,CAACqa,OAAO,CAACC,QAAQ,CAAC/d,IAAI,CAAC+I,MAAM,CAAC;IACpC;IACA,OAAO,IAAI;EACb;EACAyW,mBAAmBA,CAACxR,CAAC,EAAE;IACrB,IAAI,CAACA,CAAC,IAAI,CAAC,IAAI,CAACsN,SAAS,EAAE;IAC3B,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC9c,OAAO,CAACwP,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;IACvC,KAAK,IAAI4R,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACtE,SAAS,CAACrc,MAAM,EAAE2gB,EAAE,EAAE,EAAE;MACjD,MAAMC,SAAS,GAAG,IAAI,CAACvE,SAAS,CAACsE,EAAE,CAAC;MACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAACphB,OAAO,CAACqhB,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/C,IAAI,IAAI,CAAChF,KAAK,CAACvS,2BAA2B,CAACuX,SAAS,CAAC,EAAE;QACrD,IAAI,CAACN,gBAAgB,GAAGM,SAAS;QACjC;MACF;IACF;IACA,IAAI,CAAC,IAAI,CAACN,gBAAgB,IAAI,IAAI,CAACjE,SAAS,CAAC9c,OAAO,CAACwP,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC6M,KAAK,CAACvS,2BAA2B,CAAC0F,CAAC,CAAC,EAAE;MACxG,IAAI,CAACuR,gBAAgB,GAAGvR,CAAC;MACzB,IAAI,CAACsN,SAAS,CAACwE,OAAO,CAAC9R,CAAC,CAAC;IAC3B;EACF;EACAvE,cAAcA,CAACrC,GAAG,EAAEmU,QAAQ,EAAE;IAAA,IAAAwE,MAAA;IAC5B,IAAI,CAACC,oBAAoB,GAAG5Y,GAAG;IAC/B,MAAM6X,QAAQ,GAAG3hB,KAAK,CAAC,CAAC;IACxB,IAAI,CAAC2I,IAAI,CAAC,kBAAkB,EAAEmB,GAAG,CAAC;IAClC,MAAM6Y,WAAW,GAAGjS,CAAC,IAAI;MACvB,IAAI,CAACtE,QAAQ,GAAGsE,CAAC;MACjB,IAAI,CAACsN,SAAS,GAAG,IAAI,CAAC9R,QAAQ,CAACkE,aAAa,CAACI,kBAAkB,CAACE,CAAC,CAAC;MAClE,IAAI,CAACuR,gBAAgB,GAAG9f,SAAS;MACjC,IAAI,CAAC+f,mBAAmB,CAACxR,CAAC,CAAC;IAC7B,CAAC;IACD,MAAM+N,IAAI,GAAGA,CAAC7H,GAAG,EAAElG,CAAC,KAAK;MACvB,IAAIA,CAAC,EAAE;QACL,IAAI,IAAI,CAACgS,oBAAoB,KAAK5Y,GAAG,EAAE;UACrC6Y,WAAW,CAACjS,CAAC,CAAC;UACd,IAAI,CAAC9E,UAAU,CAACO,cAAc,CAACuE,CAAC,CAAC;UACjC,IAAI,CAACgS,oBAAoB,GAAGvgB,SAAS;UACrC,IAAI,CAACwG,IAAI,CAAC,iBAAiB,EAAE+H,CAAC,CAAC;UAC/B,IAAI,CAAC1J,MAAM,CAACZ,GAAG,CAAC,iBAAiB,EAAEsK,CAAC,CAAC;QACvC;MACF,CAAC,MAAM;QACL,IAAI,CAACgS,oBAAoB,GAAGvgB,SAAS;MACvC;MACAwf,QAAQ,CAACthB,OAAO,CAAC;QAAA,OAAaoiB,MAAI,CAAC7hB,CAAC,CAAC,GAAA6E,SAAO,CAAC;MAAA,EAAC;MAC9C,IAAIwY,QAAQ,EAAEA,QAAQ,CAACrH,GAAG,EAAE;QAAA,OAAa6L,MAAI,CAAC7hB,CAAC,CAAC,GAAA6E,SAAO,CAAC;MAAA,EAAC;IAC3D,CAAC;IACD,MAAMmd,MAAM,GAAG1S,IAAI,IAAI;MACrB,IAAI,CAACpG,GAAG,IAAI,CAACoG,IAAI,IAAI,IAAI,CAAChE,QAAQ,CAACmV,gBAAgB,EAAEnR,IAAI,GAAG,EAAE;MAC9D,MAAM2S,EAAE,GAAG/iB,QAAQ,CAACoQ,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC;MAClD,MAAMQ,CAAC,GAAG,IAAI,CAAC6M,KAAK,CAACvS,2BAA2B,CAAC6X,EAAE,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC3W,QAAQ,CAACkE,aAAa,CAACwE,qBAAqB,CAAC9U,QAAQ,CAACoQ,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAGA,IAAI,CAAC;MAC7I,IAAIQ,CAAC,EAAE;QACL,IAAI,CAAC,IAAI,CAACtE,QAAQ,EAAE;UAClBuW,WAAW,CAACjS,CAAC,CAAC;QAChB;QACA,IAAI,CAAC,IAAI,CAAC9E,UAAU,CAACQ,QAAQ,EAAE,IAAI,CAACR,UAAU,CAACO,cAAc,CAACuE,CAAC,CAAC;QAChE,IAAI,CAACxE,QAAQ,CAACmV,gBAAgB,EAAEyB,iBAAiB,GAAGpS,CAAC,CAAC;MACxD;MACA,IAAI,CAACoR,aAAa,CAACpR,CAAC,EAAEkG,GAAG,IAAI;QAC3B6H,IAAI,CAAC7H,GAAG,EAAElG,CAAC,CAAC;MACd,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAC5G,GAAG,IAAI,IAAI,CAACoC,QAAQ,CAACmV,gBAAgB,IAAI,CAAC,IAAI,CAACnV,QAAQ,CAACmV,gBAAgB,CAAC0B,KAAK,EAAE;MACnFH,MAAM,CAAC,IAAI,CAAC1W,QAAQ,CAACmV,gBAAgB,CAAC2B,MAAM,CAAC,CAAC,CAAC;IACjD,CAAC,MAAM,IAAI,CAAClZ,GAAG,IAAI,IAAI,CAACoC,QAAQ,CAACmV,gBAAgB,IAAI,IAAI,CAACnV,QAAQ,CAACmV,gBAAgB,CAAC0B,KAAK,EAAE;MACzF,IAAI,IAAI,CAAC7W,QAAQ,CAACmV,gBAAgB,CAAC2B,MAAM,CAACrhB,MAAM,KAAK,CAAC,EAAE;QACtD,IAAI,CAACuK,QAAQ,CAACmV,gBAAgB,CAAC2B,MAAM,CAAC,CAAC,CAAC9D,IAAI,CAAC0D,MAAM,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAAC1W,QAAQ,CAACmV,gBAAgB,CAAC2B,MAAM,CAACJ,MAAM,CAAC;MAC/C;IACF,CAAC,MAAM;MACLA,MAAM,CAAC9Y,GAAG,CAAC;IACb;IACA,OAAO6X,QAAQ;EACjB;EACAsB,SAASA,CAACnZ,GAAG,EAAER,EAAE,EAAE4Z,SAAS,EAAE;IAAA,IAAAC,MAAA;IAC5B,MAAMC,MAAM,GAAG,SAAAA,CAACniB,GAAG,EAAE+Z,IAAI,EAAc;MACrC,IAAI1O,CAAC;MACL,IAAI,OAAO0O,IAAI,KAAK,QAAQ,EAAE;QAAA,SAAAqI,KAAA,GAAA5d,SAAA,CAAA9D,MAAA,EAFFsZ,IAAI,OAAA9T,KAAA,CAAAkc,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJrI,IAAI,CAAAqI,KAAA,QAAA7d,SAAA,CAAA6d,KAAA;QAAA;QAG9BhX,CAAC,GAAG6W,MAAI,CAACtc,OAAO,CAACyG,gCAAgC,CAAC,CAACrM,GAAG,EAAE+Z,IAAI,CAAC,CAACvY,MAAM,CAACwY,IAAI,CAAC,CAAC;MAC7E,CAAC,MAAM;QACL3O,CAAC,GAAG;UACF,GAAG0O;QACL,CAAC;MACH;MACA1O,CAAC,CAACxC,GAAG,GAAGwC,CAAC,CAACxC,GAAG,IAAIsZ,MAAM,CAACtZ,GAAG;MAC3BwC,CAAC,CAAC4D,IAAI,GAAG5D,CAAC,CAAC4D,IAAI,IAAIkT,MAAM,CAAClT,IAAI;MAC9B5D,CAAC,CAAChD,EAAE,GAAGgD,CAAC,CAAChD,EAAE,IAAI8Z,MAAM,CAAC9Z,EAAE;MACxB,IAAIgD,CAAC,CAAC4W,SAAS,KAAK,EAAE,EAAE5W,CAAC,CAAC4W,SAAS,GAAG5W,CAAC,CAAC4W,SAAS,IAAIA,SAAS,IAAIE,MAAM,CAACF,SAAS;MAClF,MAAMre,YAAY,GAAGse,MAAI,CAACtc,OAAO,CAAChC,YAAY,IAAI,GAAG;MACrD,IAAI0e,SAAS;MACb,IAAIjX,CAAC,CAAC4W,SAAS,IAAI/b,KAAK,CAAC4C,OAAO,CAAC9I,GAAG,CAAC,EAAE;QACrCsiB,SAAS,GAAGtiB,GAAG,CAACiE,GAAG,CAAClD,CAAC,IAAI,GAAGsK,CAAC,CAAC4W,SAAS,GAAGre,YAAY,GAAG7C,CAAC,EAAE,CAAC;MAC/D,CAAC,MAAM;QACLuhB,SAAS,GAAGjX,CAAC,CAAC4W,SAAS,GAAG,GAAG5W,CAAC,CAAC4W,SAAS,GAAGre,YAAY,GAAG5D,GAAG,EAAE,GAAGA,GAAG;MACvE;MACA,OAAOkiB,MAAI,CAACviB,CAAC,CAAC2iB,SAAS,EAAEjX,CAAC,CAAC;IAC7B,CAAC;IACD,IAAIxM,QAAQ,CAACgK,GAAG,CAAC,EAAE;MACjBsZ,MAAM,CAACtZ,GAAG,GAAGA,GAAG;IAClB,CAAC,MAAM;MACLsZ,MAAM,CAAClT,IAAI,GAAGpG,GAAG;IACnB;IACAsZ,MAAM,CAAC9Z,EAAE,GAAGA,EAAE;IACd8Z,MAAM,CAACF,SAAS,GAAGA,SAAS;IAC5B,OAAOE,MAAM;EACf;EACAxiB,CAACA,CAAA,EAAU;IAAA,SAAA4iB,MAAA,GAAA/d,SAAA,CAAA9D,MAAA,EAAN0E,IAAI,OAAAc,KAAA,CAAAqc,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAJpd,IAAI,CAAAod,MAAA,IAAAhe,SAAA,CAAAge,MAAA;IAAA;IACP,OAAO,IAAI,CAAC7X,UAAU,EAAEwB,SAAS,CAAC,GAAG/G,IAAI,CAAC;EAC5C;EACAgG,MAAMA,CAAA,EAAU;IAAA,SAAAqX,MAAA,GAAAje,SAAA,CAAA9D,MAAA,EAAN0E,IAAI,OAAAc,KAAA,CAAAuc,MAAA,GAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;MAAJtd,IAAI,CAAAsd,MAAA,IAAAle,SAAA,CAAAke,MAAA;IAAA;IACZ,OAAO,IAAI,CAAC/X,UAAU,EAAES,MAAM,CAAC,GAAGhG,IAAI,CAAC;EACzC;EACAud,mBAAmBA,CAACta,EAAE,EAAE;IACtB,IAAI,CAACzC,OAAO,CAAC0C,SAAS,GAAGD,EAAE;EAC7B;EACAyJ,kBAAkBA,CAACzJ,EAAE,EAAgB;IAAA,IAAdzC,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IACjC,IAAI,CAAC,IAAI,CAACib,aAAa,EAAE;MACvB,IAAI,CAAC1Z,MAAM,CAACT,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAACyX,SAAS,CAAC;MACnF,OAAO,KAAK;IACd;IACA,IAAI,CAAC,IAAI,CAACA,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACrc,MAAM,EAAE;MAC7C,IAAI,CAACqF,MAAM,CAACT,IAAI,CAAC,4DAA4D,EAAE,IAAI,CAACyX,SAAS,CAAC;MAC9F,OAAO,KAAK;IACd;IACA,MAAMlU,GAAG,GAAGjD,OAAO,CAACiD,GAAG,IAAI,IAAI,CAACmY,gBAAgB,IAAI,IAAI,CAACjE,SAAS,CAAC,CAAC,CAAC;IACrE,MAAM1N,WAAW,GAAG,IAAI,CAACzJ,OAAO,GAAG,IAAI,CAACA,OAAO,CAACyJ,WAAW,GAAG,KAAK;IACnE,MAAMuT,OAAO,GAAG,IAAI,CAAC7F,SAAS,CAAC,IAAI,CAACA,SAAS,CAACrc,MAAM,GAAG,CAAC,CAAC;IACzD,IAAImI,GAAG,CAAC4D,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAO,IAAI;IAC/C,MAAMoW,cAAc,GAAGA,CAACpT,CAAC,EAAEzF,CAAC,KAAK;MAC/B,MAAM8Y,SAAS,GAAG,IAAI,CAAC7X,QAAQ,CAAC4E,gBAAgB,CAAC+M,KAAK,CAAC,GAAGnN,CAAC,IAAIzF,CAAC,EAAE,CAAC;MACnE,OAAO8Y,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK,CAAC;IAC/D,CAAC;IACD,IAAIld,OAAO,CAACmd,QAAQ,EAAE;MACpB,MAAMC,SAAS,GAAGpd,OAAO,CAACmd,QAAQ,CAAC,IAAI,EAAEF,cAAc,CAAC;MACxD,IAAIG,SAAS,KAAK9hB,SAAS,EAAE,OAAO8hB,SAAS;IAC/C;IACA,IAAI,IAAI,CAACpZ,iBAAiB,CAACf,GAAG,EAAER,EAAE,CAAC,EAAE,OAAO,IAAI;IAChD,IAAI,CAAC,IAAI,CAAC4C,QAAQ,CAAC4E,gBAAgB,CAACwM,OAAO,IAAI,IAAI,CAACzW,OAAO,CAACuD,SAAS,IAAI,CAAC,IAAI,CAACvD,OAAO,CAAC+Y,uBAAuB,EAAE,OAAO,IAAI;IAC3H,IAAIkE,cAAc,CAACha,GAAG,EAAER,EAAE,CAAC,KAAK,CAACgH,WAAW,IAAIwT,cAAc,CAACD,OAAO,EAAEva,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI;IACzF,OAAO,KAAK;EACd;EACA4a,cAAcA,CAAC5a,EAAE,EAAE2U,QAAQ,EAAE;IAC3B,MAAM0D,QAAQ,GAAG3hB,KAAK,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC6G,OAAO,CAACyC,EAAE,EAAE;MACpB,IAAI2U,QAAQ,EAAEA,QAAQ,CAAC,CAAC;MACxB,OAAO7d,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IACA,IAAIP,QAAQ,CAACwJ,EAAE,CAAC,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC;IAC3BA,EAAE,CAACzI,OAAO,CAACoK,CAAC,IAAI;MACd,IAAI,IAAI,CAACpE,OAAO,CAACyC,EAAE,CAACpI,OAAO,CAAC+J,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACpE,OAAO,CAACyC,EAAE,CAAC5G,IAAI,CAACuI,CAAC,CAAC;IAC7D,CAAC,CAAC;IACF,IAAI,CAAC6W,aAAa,CAAClL,GAAG,IAAI;MACxB+K,QAAQ,CAACthB,OAAO,CAAC,CAAC;MAClB,IAAI4d,QAAQ,EAAEA,QAAQ,CAACrH,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO+K,QAAQ;EACjB;EACAwC,aAAaA,CAACjU,IAAI,EAAE+N,QAAQ,EAAE;IAC5B,MAAM0D,QAAQ,GAAG3hB,KAAK,CAAC,CAAC;IACxB,IAAIF,QAAQ,CAACoQ,IAAI,CAAC,EAAEA,IAAI,GAAG,CAACA,IAAI,CAAC;IACjC,MAAMkU,SAAS,GAAG,IAAI,CAACvd,OAAO,CAAC6Y,OAAO,IAAI,EAAE;IAC5C,MAAM2E,OAAO,GAAGnU,IAAI,CAACnL,MAAM,CAAC+E,GAAG,IAAIsa,SAAS,CAACljB,OAAO,CAAC4I,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAACoC,QAAQ,CAACkE,aAAa,CAACqE,eAAe,CAAC3K,GAAG,CAAC,CAAC;IAClH,IAAI,CAACua,OAAO,CAAC1iB,MAAM,EAAE;MACnB,IAAIsc,QAAQ,EAAEA,QAAQ,CAAC,CAAC;MACxB,OAAO7d,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IACA,IAAI,CAACwG,OAAO,CAAC6Y,OAAO,GAAG0E,SAAS,CAAC3hB,MAAM,CAAC4hB,OAAO,CAAC;IAChD,IAAI,CAACvC,aAAa,CAAClL,GAAG,IAAI;MACxB+K,QAAQ,CAACthB,OAAO,CAAC,CAAC;MAClB,IAAI4d,QAAQ,EAAEA,QAAQ,CAACrH,GAAG,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO+K,QAAQ;EACjB;EACA2C,GAAGA,CAACxa,GAAG,EAAE;IACP,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAG,IAAI,CAACmY,gBAAgB,KAAK,IAAI,CAACjE,SAAS,EAAErc,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqc,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC5R,QAAQ,CAAC;IACzG,IAAI,CAACtC,GAAG,EAAE,OAAO,KAAK;IACtB,IAAI;MACF,MAAM4G,CAAC,GAAG,IAAI2D,IAAI,CAACkQ,MAAM,CAACza,GAAG,CAAC;MAC9B,IAAI4G,CAAC,IAAIA,CAAC,CAAC8T,WAAW,EAAE;QACtB,MAAMC,EAAE,GAAG/T,CAAC,CAAC8T,WAAW,CAAC,CAAC;QAC1B,IAAIC,EAAE,IAAIA,EAAE,CAACC,SAAS,EAAE,OAAOD,EAAE,CAACC,SAAS;MAC7C;IACF,CAAC,CAAC,OAAOtiB,CAAC,EAAE,CAAC;IACb,MAAMuiB,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IACxb,MAAMvU,aAAa,GAAG,IAAI,CAAClE,QAAQ,EAAEkE,aAAa,IAAI,IAAI2D,YAAY,CAAC5P,GAAG,CAAC,CAAC,CAAC;IAC7E,IAAI2F,GAAG,CAAC4D,WAAW,CAAC,CAAC,CAACxM,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK;IACxD,OAAOyjB,OAAO,CAACzjB,OAAO,CAACkP,aAAa,CAAC+D,uBAAuB,CAACrK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIA,GAAG,CAAC4D,WAAW,CAAC,CAAC,CAACxM,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK;EACnI;EACA,OAAO0jB,cAAcA,CAAA,EAAyB;IAAA,IAAxB/d,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEwY,QAAQ,GAAAxY,SAAA,CAAA9D,MAAA,OAAA8D,SAAA,MAAAtD,SAAA;IAC1C,OAAO,IAAIoe,IAAI,CAAC1Z,OAAO,EAAEoX,QAAQ,CAAC;EACpC;EACA4G,aAAaA,CAAA,EAAgC;IAAA,IAA/Bhe,OAAO,GAAApB,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEwY,QAAQ,GAAAxY,SAAA,CAAA9D,MAAA,QAAA8D,SAAA,QAAAtD,SAAA,GAAAsD,SAAA,MAAGwa,IAAI;IACzC,MAAM6E,iBAAiB,GAAGje,OAAO,CAACie,iBAAiB;IACnD,IAAIA,iBAAiB,EAAE,OAAOje,OAAO,CAACie,iBAAiB;IACvD,MAAMC,aAAa,GAAG;MACpB,GAAG,IAAI,CAACle,OAAO;MACf,GAAGA,OAAO;MACV,GAAG;QACD8Z,OAAO,EAAE;MACX;IACF,CAAC;IACD,MAAM1Y,KAAK,GAAG,IAAIsY,IAAI,CAACwE,aAAa,CAAC;IACrC,IAAIle,OAAO,CAACI,KAAK,KAAK9E,SAAS,IAAI0E,OAAO,CAACE,MAAM,KAAK5E,SAAS,EAAE;MAC/D8F,KAAK,CAACjB,MAAM,GAAGiB,KAAK,CAACjB,MAAM,CAACiB,KAAK,CAACpB,OAAO,CAAC;IAC5C;IACA,MAAMme,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;IACvDA,aAAa,CAACnkB,OAAO,CAACC,CAAC,IAAI;MACzBmH,KAAK,CAACnH,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;IACpB,CAAC,CAAC;IACFmH,KAAK,CAACiE,QAAQ,GAAG;MACf,GAAG,IAAI,CAACA;IACV,CAAC;IACDjE,KAAK,CAACiE,QAAQ,CAAC4G,KAAK,GAAG;MACrBC,kBAAkB,EAAE9K,KAAK,CAAC8K,kBAAkB,CAACkM,IAAI,CAAChX,KAAK;IACzD,CAAC;IACD,IAAI6c,iBAAiB,EAAE;MACrB,MAAMG,UAAU,GAAGrjB,MAAM,CAACsJ,IAAI,CAAC,IAAI,CAACqS,KAAK,CAAC1a,IAAI,CAAC,CAAC2X,MAAM,CAAC,CAAC0K,IAAI,EAAExU,CAAC,KAAK;QAClEwU,IAAI,CAACxU,CAAC,CAAC,GAAG;UACR,GAAG,IAAI,CAAC6M,KAAK,CAAC1a,IAAI,CAAC6N,CAAC;QACtB,CAAC;QACDwU,IAAI,CAACxU,CAAC,CAAC,GAAG9O,MAAM,CAACsJ,IAAI,CAACga,IAAI,CAACxU,CAAC,CAAC,CAAC,CAAC8J,MAAM,CAAC,CAAC2K,GAAG,EAAEla,CAAC,KAAK;UAChDka,GAAG,CAACla,CAAC,CAAC,GAAG;YACP,GAAGia,IAAI,CAACxU,CAAC,CAAC,CAACzF,CAAC;UACd,CAAC;UACD,OAAOka,GAAG;QACZ,CAAC,EAAED,IAAI,CAACxU,CAAC,CAAC,CAAC;QACX,OAAOwU,IAAI;MACb,CAAC,EAAE,CAAC,CAAC,CAAC;MACNjd,KAAK,CAACsV,KAAK,GAAG,IAAIlU,aAAa,CAAC4b,UAAU,EAAEF,aAAa,CAAC;MAC1D9c,KAAK,CAACiE,QAAQ,CAACyH,aAAa,GAAG1L,KAAK,CAACsV,KAAK;IAC5C;IACAtV,KAAK,CAAC2D,UAAU,GAAG,IAAIK,UAAU,CAAChE,KAAK,CAACiE,QAAQ,EAAE6Y,aAAa,CAAC;IAChE9c,KAAK,CAAC2D,UAAU,CAACvD,EAAE,CAAC,GAAG,EAAE,UAACG,KAAK,EAAc;MAAA,SAAA4c,MAAA,GAAA3f,SAAA,CAAA9D,MAAA,EAAT0E,IAAI,OAAAc,KAAA,CAAAie,MAAA,OAAAA,MAAA,WAAAC,MAAA,MAAAA,MAAA,GAAAD,MAAA,EAAAC,MAAA;QAAJhf,IAAI,CAAAgf,MAAA,QAAA5f,SAAA,CAAA4f,MAAA;MAAA;MACtCpd,KAAK,CAACU,IAAI,CAACH,KAAK,EAAE,GAAGnC,IAAI,CAAC;IAC5B,CAAC,CAAC;IACF4B,KAAK,CAACnB,IAAI,CAACie,aAAa,EAAE9G,QAAQ,CAAC;IACnChW,KAAK,CAAC2D,UAAU,CAAC/E,OAAO,GAAGke,aAAa;IACxC9c,KAAK,CAAC2D,UAAU,CAACkF,gBAAgB,CAAC5E,QAAQ,CAAC4G,KAAK,GAAG;MACjDC,kBAAkB,EAAE9K,KAAK,CAAC8K,kBAAkB,CAACkM,IAAI,CAAChX,KAAK;IACzD,CAAC;IACD,OAAOA,KAAK;EACd;EACAoD,MAAMA,CAAA,EAAG;IACP,OAAO;MACLxE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrB0W,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBnR,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB4R,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBiE,gBAAgB,EAAE,IAAI,CAACA;IACzB,CAAC;EACH;AACF;AACA,MAAMqD,QAAQ,GAAG/E,IAAI,CAACqE,cAAc,CAAC,CAAC;AACtCU,QAAQ,CAACV,cAAc,GAAGrE,IAAI,CAACqE,cAAc;AAE7C,MAAMA,cAAc,GAAGU,QAAQ,CAACV,cAAc;AAC9C,MAAMN,GAAG,GAAGgB,QAAQ,CAAChB,GAAG;AACxB,MAAMxd,IAAI,GAAGwe,QAAQ,CAACxe,IAAI;AAC1B,MAAMgb,aAAa,GAAGwD,QAAQ,CAACxD,aAAa;AAC5C,MAAMK,eAAe,GAAGmD,QAAQ,CAACnD,eAAe;AAChD,MAAMC,GAAG,GAAGkD,QAAQ,CAAClD,GAAG;AACxB,MAAMjW,cAAc,GAAGmZ,QAAQ,CAACnZ,cAAc;AAC9C,MAAM8W,SAAS,GAAGqC,QAAQ,CAACrC,SAAS;AACpC,MAAMriB,CAAC,GAAG0kB,QAAQ,CAAC1kB,CAAC;AACpB,MAAMyL,MAAM,GAAGiZ,QAAQ,CAACjZ,MAAM;AAC9B,MAAMuX,mBAAmB,GAAG0B,QAAQ,CAAC1B,mBAAmB;AACxD,MAAM7Q,kBAAkB,GAAGuS,QAAQ,CAACvS,kBAAkB;AACtD,MAAMmR,cAAc,GAAGoB,QAAQ,CAACpB,cAAc;AAC9C,MAAMC,aAAa,GAAGmB,QAAQ,CAACnB,aAAa;AAE5C,SAAShY,cAAc,EAAEyY,cAAc,EAAEU,QAAQ,IAAIpQ,OAAO,EAAEoP,GAAG,EAAEjY,MAAM,EAAE4W,SAAS,EAAElQ,kBAAkB,EAAEjM,IAAI,EAAEqd,aAAa,EAAED,cAAc,EAAEpC,aAAa,EAAEK,eAAe,EAAEyB,mBAAmB,EAAEhjB,CAAC,EAAEwhB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}