{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{Box,Typo<PERSON>,Popover,IconButton,TextField,MenuItem,Button,Tooltip,Snackbar,Alert}from\"@mui/material\";import RemoveIcon from\"@mui/icons-material/Remove\";import AddIcon from\"@mui/icons-material/Add\";import SelectImageFromApplication from\"../../common/SelectImageFromApplication\";import{useTranslation}from\"react-i18next\";import{uploadfile,hyperlink,files,uploadicon,replaceimageicon,copyicon,deleteicon,sectionheight,Settings,CrossIcon}from\"../../../assets/icons/icons\";import useDrawerStore,{IMG_CONTAINER_DEFAULT_HEIGHT,IMG_CONTAINER_MAX_HEIGHT,IMG_CONTAINER_MIN_HEIGHT,IMG_OBJECT_FIT,IMG_STEP_VALUE}from\"../../../store/drawerStore\";import{ChromePicker}from\"react-color\";import\"../../guideSetting/PopupSections/PopupSections.css\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ImageSection=_ref=>{var _document$getElementB3,_document$getElementB4,_document$getElementB5,_document$getElementB6,_useDrawerStore$,_useDrawerStore$$cont,_useDrawerStore$2,_useDrawerStore$2$con;let{items:imagesContainer,isCloneDisabled}=_ref;const{t:translate}=useTranslation();const{uploadTooltipImage:uploadImage,imageAnchorEl,setImageAnchorEl,replaceTooltipImage:replaceImage,cloneTooltipImage:cloneImageContainer,deleteTooltipImageContainer:deleteImageContainer,updateTooltipImageContainer:updateImageContainer,// TODO\ntoggleTooltipImageFit:toggleFit,//TODO\nsetImageSrc:storeImageSrc,tooltip}=useDrawerStore(state=>state);const[snackbarOpen,setSnackbarOpen]=useState(false);const[snackbarMessage,setSnackbarMessage]=useState('');const[snackbarSeverity,setSnackbarSeverity]=useState('info');const[snackbarKey,setSnackbarKey]=useState(0);const openSnackbar=()=>{setSnackbarKey(prev=>prev+1);setSnackbarOpen(true);};const closeSnackbar=()=>{setSnackbarOpen(false);};const[showHyperlinkInput,setShowHyperlinkInput]=useState({currentContainerId:\"\",isOpen:false});const[imageLink,setImageLink]=useState(\"\");const[colorPickerAnchorEl,setColorPickerAnchorEl]=useState(null);const[currentImageSectionInfo,setCurrentImageSectionInfo]=useState({currentContainerId:\"\",isImage:false,height:IMG_CONTAINER_DEFAULT_HEIGHT});const[selectedAction,setSelectedAction]=useState(\"none\");const[settingsAnchorEl,setSettingsAnchorEl]=useState(null);const[selectedColor,setSelectedColor]=useState(\"\");const[isModelOpen,setModelOpen]=useState(false);const[formOfUpload,setFormOfUpload]=useState(\"\");const[isReplaceImage,setReplaceImage]=useState(false);const openSettingsPopover=Boolean(settingsAnchorEl);const handleActionChange=event=>{setSelectedAction(event.target.value);};const handleSettingsClick=event=>{setSettingsAnchorEl(event.currentTarget);};const handleCloseSettingsPopover=()=>{setSettingsAnchorEl(null);};const imageContainerStyle={width:\"100%\",height:`${imagesContainer.style.height}px`,display:\"flex\",justifyContent:\"center\",alignItems:\"center\",padding:0,margin:0,overflow:\"hidden\"};const imageStyle={width:\"100%\",height:\"100%\",margin:0,padding:0,borderRadius:\"0\"};const iconRowStyle={display:\"flex\",justifyContent:\"center\",gap:\"16px\",marginTop:\"10px\"};const iconTextStyle={display:\"flex\",flexDirection:\"column\",alignItems:\"center\",justifyContent:\"center\",width:\"100%\"};const handleImageUpload=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];let urll;if(file){const parts=file.name.split('.');const extension=parts.pop();// Check for double extensions (e.g. file.html.png) or missing/invalid extension\nif(parts.length>1||!extension){setSnackbarMessage(\"Uploaded file name should not contain any special character\");setSnackbarSeverity(\"error\");setSnackbarOpen(true);event.target.value='';return;}if(file.name.length>128){setSnackbarMessage(\"File name should not exceed 128 characters\");setSnackbarSeverity(\"error\");setSnackbarOpen(true);event.target.value='';return;}// setImageName(event.target.files?.[0].name);\nconst reader=new FileReader();reader.onloadend=()=>{const base64Image=reader.result;storeImageSrc(base64Image);// urll = base64Image;\nuploadImage(imagesContainer.id,{altText:file.name,id:crypto.randomUUID(),url:base64Image,backgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});};reader.readAsDataURL(file);}};const handleImageUploadFormApp=file=>{if(file){storeImageSrc(file.Url);if(isReplaceImage){replaceImage(imagesContainer.id,{altText:file.FileName,url:file.Url,backgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});setReplaceImage(false);}else{uploadImage(imagesContainer.id,{altText:file.FileName,id:crypto.randomUUID(),// Use existing ID\nurl:file.Url,// Directly use the URL\nbackgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});}}setModelOpen(false);};const handleReplaceImage=event=>{var _event$target$files2;const file=(_event$target$files2=event.target.files)===null||_event$target$files2===void 0?void 0:_event$target$files2[0];if(file){const reader=new FileReader();reader.onloadend=()=>{replaceImage(imagesContainer.id,{altText:file.name,url:reader.result,backgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});};reader.readAsDataURL(file);}};const handleClick=(event,containerId,imageId,isImage,currentHeight)=>{// @ts-ignore\nif([\"file-upload\",\"hyperlink\"].includes(event.target.id))return;setImageAnchorEl({buttonId:imageId,containerId:containerId,// @ts-ignore\nvalue:event.currentTarget});setSettingsAnchorEl(null);setCurrentImageSectionInfo({currentContainerId:containerId,isImage,height:currentHeight});setShowHyperlinkInput({currentContainerId:\"\",isOpen:false});};const handleClose=()=>{setImageAnchorEl({buttonId:\"\",containerId:\"\",// @ts-ignore\nvalue:null});setSettingsAnchorEl(null);};const open=Boolean(imageAnchorEl.value&&imagesContainer.id===imageAnchorEl.containerId);const colorPickerOpen=Boolean(colorPickerAnchorEl);const id=open?\"image-popover\":undefined;const handleIncreaseHeight=prevHeight=>{if(prevHeight>=IMG_CONTAINER_MAX_HEIGHT)return;const newHeight=Math.min(prevHeight+IMG_STEP_VALUE,IMG_CONTAINER_MAX_HEIGHT);updateImageContainer(imagesContainer.id,\"style\",{height:newHeight});setCurrentImageSectionInfo(prev=>({...prev,height:newHeight}));};const handleDecreaseHeight=prevHeight=>{if(prevHeight<=IMG_CONTAINER_MIN_HEIGHT)return;const newHeight=Math.max(prevHeight-IMG_STEP_VALUE,IMG_CONTAINER_MIN_HEIGHT);updateImageContainer(imagesContainer.id,\"style\",{height:newHeight});setCurrentImageSectionInfo(prev=>({...prev,height:newHeight}));};const triggerImageUpload=()=>{var _document$getElementB;// setReplaceImage(true);\n// setModelOpen(true);\n(_document$getElementB=document.getElementById(\"replace-upload\"))===null||_document$getElementB===void 0?void 0:_document$getElementB.click();};// Function to delete the section\nconst handleDeleteSection=()=>{setImageAnchorEl({buttonId:\"\",containerId:\"\",// @ts-ignore\nvalue:null});setSettingsAnchorEl(null);deleteImageContainer(imagesContainer.id);};const handleHyperlinkClick=(cId,isOpen)=>{setShowHyperlinkInput({currentContainerId:cId,isOpen});};const handleLinkSubmit=event=>{if(event.key===\"Enter\"&&imageLink){uploadImage(imageAnchorEl.containerId,{altText:\"New Image\",id:crypto.randomUUID(),url:imageLink,backgroundColor:\"transparent\",objectFit:IMG_OBJECT_FIT});setShowHyperlinkInput({currentContainerId:\"\",isOpen:false});}};const handleCloneImgContainer=()=>{cloneImageContainer(imagesContainer.id);};const handleCloseColorPicker=()=>{setColorPickerAnchorEl(null);};const handleColorChange=color=>{setSelectedColor(color.hex);updateImageContainer(imagesContainer.id,\"style\",{backgroundColor:color.hex});};const handleBackgroundColorClick=event=>{setColorPickerAnchorEl(event.currentTarget);};// console.log({ imageAnchorEl, tooltip });\nuseEffect(()=>{if(!tooltip.visible){setImageAnchorEl({buttonId:\"\",containerId:\"\",// @ts-ignore\nvalue:null});setSettingsAnchorEl(null);}},[tooltip.visible]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Box// key={id}\n,{sx:{width:\"100%\",height:\"100%\",display:\"flex\",flexDirection:\"column\",justifyContent:\"flex-start\",alignItems:\"center\",// padding: \"5px\",\nmargin:\"0px\",overflow:\"auto\"},children:!imagesContainer.images.length?/*#__PURE__*/_jsxs(Box,{sx:{...imageContainerStyle,backgroundColor:imagesContainer.style.backgroundColor,height:`${imagesContainer.style.height}px`,textAlign:\"center\",width:\"100%\",// height: \"100%\",\ndisplay:\"flex\",flexDirection:\"column\",justifyContent:\"center\"},onClick:e=>{var _e$target;// prevent to open toolbar when upload file clicked\n// @ts-ignore\nif(!((_e$target=e.target)!==null&&_e$target!==void 0&&_e$target.id.startsWith(\"file-upload\"))){var _imagesContainer$styl;handleClick(e,imagesContainer.id,\"\",false,(imagesContainer===null||imagesContainer===void 0?void 0:(_imagesContainer$styl=imagesContainer.style)===null||_imagesContainer$styl===void 0?void 0:_imagesContainer$styl.height)||IMG_CONTAINER_DEFAULT_HEIGHT);}},id:imagesContainer.id,children:[/*#__PURE__*/_jsxs(Box,{sx:iconTextStyle,component:\"div\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:uploadfile},style:{display:\"inline-block\"}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",align:\"center\",color:\"textSecondary\",sx:{fontSize:\"14px\",fontWeight:\"600\"},children:translate(\"Upload file\",{defaultValue:\"Upload file\"})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",align:\"center\",color:\"textSecondary\",sx:{fontSize:\"14px\"},children:translate(\"Drag & Drop to upload file\",{defaultValue:\"Drag & Drop to upload file\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",align:\"center\",color:\"textSecondary\",sx:{marginTop:\"8px\",fontSize:\"14px\"},children:translate(\"Or\",{defaultValue:\"Or\"})}),showHyperlinkInput.isOpen&&showHyperlinkInput.currentContainerId===id?/*#__PURE__*/_jsx(TextField,{value:imageLink,onChange:e=>setImageLink(e.target.value),onKeyDown:handleLinkSubmit,autoFocus:true}):/*#__PURE__*/_jsxs(Box,{sx:iconRowStyle,children:[/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming soon\",{defaultValue:\"Coming soon\"}),children:/*#__PURE__*/_jsx(\"div\",{style:{pointerEvents:\"auto\",cursor:\"pointer\"},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:hyperlink},style:{color:\"black\",cursor:\"pointer\",fontSize:\"32px\",opacity:\"0.5\",pointerEvents:\"none\"},id:\"hyperlink\"})})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming soon\",{defaultValue:\"Coming soon\"}),children:/*#__PURE__*/_jsx(\"span\",{onClick:()=>{//setModelOpen(true);\n},dangerouslySetInnerHTML:{__html:files},style:{color:\"black\",cursor:\"pointer\",fontSize:\"32px\",opacity:\"0.5\"},id:\"folder\"//title=\"Coming Soon\"\n})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Upload File\",{defaultValue:\"Upload File\"}),children:/*#__PURE__*/_jsx(\"span\",{onClick:event=>{var _document$getElementB2;event===null||event===void 0?void 0:event.stopPropagation();(_document$getElementB2=document.getElementById(`file-upload-${imagesContainer.id}`))===null||_document$getElementB2===void 0?void 0:_document$getElementB2.click();},id:\"file-upload1\",dangerouslySetInnerHTML:{__html:uploadicon},style:{color:\"black\",cursor:\"pointer\",fontSize:\"32px\"}})}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:`file-upload-${imagesContainer.id}`,style:{display:\"none\"},accept:\"image/*\",onChange:handleImageUpload}),/*#__PURE__*/_jsx(Snackbar,{open:snackbarOpen,autoHideDuration:3000,onClose:closeSnackbar,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:closeSnackbar,severity:snackbarSeverity,sx:{width:'100%'},children:snackbarMessage})})]})]}):imagesContainer.images.map(item=>{var _imagesContainer$styl2;const imageSrc=item===null||item===void 0?void 0:item.url;const imageId=item===null||item===void 0?void 0:item.id;const objectFit=(item===null||item===void 0?void 0:item.objectFit)||IMG_OBJECT_FIT;const currentSecHeight=(imagesContainer===null||imagesContainer===void 0?void 0:(_imagesContainer$styl2=imagesContainer.style)===null||_imagesContainer$styl2===void 0?void 0:_imagesContainer$styl2.height)||IMG_CONTAINER_DEFAULT_HEIGHT;const id=imagesContainer.id;return/*#__PURE__*/_jsx(Box,{sx:{...imageContainerStyle,backgroundColor:imagesContainer.style.backgroundColor,height:`${imagesContainer.style.height}px`},onClick:e=>handleClick(e,id,imageId,imageSrc?true:false,currentSecHeight),component:\"div\",id:id,onMouseOver:()=>{setImageAnchorEl({buttonId:imageId,containerId:id,value:null});setSettingsAnchorEl(null);},children:/*#__PURE__*/_jsx(\"img\",{src:imageSrc,alt:\"Uploaded\",style:{...imageStyle,objectFit}})});})}),/*#__PURE__*/_jsx(Popover// className=\"qadpt-imgsec-popover\"\n,{id:\"image-popover\",open:open// anchorEl={document.getElementById(\"Tooltip-unique\")}\n,anchorReference:\"anchorPosition\",anchorPosition:{left:((_document$getElementB3=document.getElementById(\"Tooltip-unique\"))===null||_document$getElementB3===void 0?void 0:(_document$getElementB4=_document$getElementB3.getBoundingClientRect())===null||_document$getElementB4===void 0?void 0:_document$getElementB4.x)||150,top:((_document$getElementB5=document.getElementById(\"Tooltip-unique\"))===null||_document$getElementB5===void 0?void 0:(_document$getElementB6=_document$getElementB5.getBoundingClientRect())===null||_document$getElementB6===void 0?void 0:_document$getElementB6.y)||80},onClose:handleClose,anchorOrigin:{vertical:\"top\",horizontal:\"right\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"},children:/*#__PURE__*/_jsxs(Box// className=\"qadpt-tool-btn\"\n,{sx:{display:\"flex\",// justifyContent: \"space-between\",\nalignItems:\"center\",gap:\"15px\",height:\"100%\",padding:\"0 10px\",fontSize:\"12px\"},children:[/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\"},children:currentImageSectionInfo.currentContainerId===imageAnchorEl.containerId&&currentImageSectionInfo.isImage?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:replaceimageicon}}),/*#__PURE__*/_jsx(Typography,{fontSize:\"12px\",marginLeft:\"5px\",onClick:triggerImageUpload,children:translate(\"Replace Image\",{defaultValue:\"Replace Image\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"replace-upload\",style:{display:\"none\"},accept:\"image/*\",onChange:handleReplaceImage})]}):null}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-tool-items\",sx:{display:\"flex\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:sectionheight}}),/*#__PURE__*/_jsx(Tooltip,{title:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT?translate(\"Minimum height reached\",{defaultValue:\"Minimum height reached\"}):translate(\"Decrease height\",{defaultValue:\"Decrease height\"}),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleDecreaseHeight(currentImageSectionInfo.height),size:\"small\",disabled:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT,sx:{opacity:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT?0.5:1,cursor:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT?'not-allowed':'pointer'},children:/*#__PURE__*/_jsx(RemoveIcon,{fontSize:\"small\"})})})}),/*#__PURE__*/_jsx(Typography,{fontSize:\"12px\",children:currentImageSectionInfo.height}),/*#__PURE__*/_jsx(Tooltip,{title:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT?translate(\"Maximum height reached\",{defaultValue:\"Maximum height reached\"}):translate(\"Increase height\",{defaultValue:\"Increase height\"}),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleIncreaseHeight(currentImageSectionInfo.height),size:\"small\",disabled:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT,sx:{opacity:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT?0.5:1,cursor:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT?'not-allowed':'pointer'},children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\"})})})})]}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Settings\",{defaultValue:\"Settings\"}),children:/*#__PURE__*/_jsxs(Box,{className:\"qadpt-tool-items\",children:[/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSettingsClick,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:Settings},style:{color:\"black\"}})})}),/*#__PURE__*/_jsx(Popover,{open:openSettingsPopover,anchorEl:settingsAnchorEl,id:\"image-properties\",onClose:handleCloseSettingsPopover,anchorOrigin:{vertical:\"center\",horizontal:\"right\"},disablePortal:true,transformOrigin:{vertical:\"center\",horizontal:\"left\"},slotProps:{root:{sx:{zIndex:theme=>theme.zIndex.tooltip+2000}}},PaperProps:{sx:{mt:12,ml:20,width:\"205px\"}},children:/*#__PURE__*/_jsxs(Box,{p:2,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{color:\"rgba(95, 158, 160, 1)\"},children:translate(\"Image Properties\",{defaultValue:\"Image Properties\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleCloseSettingsPopover,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:CrossIcon},style:{color:\"black\"}})})]}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming soon\",{defaultValue:\"Coming soon\"}),children:/*#__PURE__*/_jsxs(Box,{mt:2,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{marginBottom:\"10px\"},children:translate(\"Image Actions\",{defaultValue:\"Image Actions\"})}),/*#__PURE__*/_jsxs(TextField,{select:true,fullWidth:true,variant:\"outlined\",size:\"small\",value:selectedAction,onChange:handleActionChange,sx:{\"& .MuiOutlinedInput-root\":{borderColor:\"rgba(246, 238, 238, 1)\"}},disabled:true,children:[/*#__PURE__*/_jsx(MenuItem,{value:\"none\",children:translate(\"None\",{defaultValue:\"None\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"specificStep\",children:translate(\"Specific Step\",{defaultValue:\"Specific Step\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"openUrl\",children:translate(\"Open URL\",{defaultValue:\"Open URL\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"clickElement\",children:translate(\"Click Element\",{defaultValue:\"Click Element\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"startTour\",children:translate(\"Start Tour\",{defaultValue:\"Start Tour\"})}),/*#__PURE__*/_jsx(MenuItem,{value:\"startMicroSurvey\",children:translate(\"Start Micro Survey\",{defaultValue:\"Start Micro Survey\"})})]})]})}),/*#__PURE__*/_jsxs(Box,{mt:2,component:\"div\",id:\"toggle-fit\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:translate(\"Image Formatting\",{defaultValue:\"Image Formatting\"})}),/*#__PURE__*/_jsx(Box,{display:\"flex\",gap:1,mt:1,children:[\"Fill\",\"Fit\"].map(item=>{// Get current image's objectFit to determine selected state\n// imagesContainer is a single object, not an array\nconst currentImage=imagesContainer.images.find(img=>img.id===imageAnchorEl.buttonId);const currentObjectFit=(currentImage===null||currentImage===void 0?void 0:currentImage.objectFit)||IMG_OBJECT_FIT;// Determine if this button should be selected\nconst isSelected=item===\"Fill\"&&currentObjectFit===\"cover\"||item===\"Fit\"&&currentObjectFit===\"contain\";return/*#__PURE__*/_jsx(Button,{onClick:()=>toggleFit(imagesContainer.id,item),variant:\"outlined\",size:\"small\",sx:{width:\"88.5px\",height:\"41px\",padding:\"10px 12px\",gap:\"12px\",borderRadius:\"6px 6px 6px 6px\",border:isSelected?\"1px solid rgba(95, 158, 160, 1)\":\"1px solid rgba(246, 238, 238, 1)\",backgroundColor:isSelected?\"rgba(95, 158, 160, 0.2)\":\"rgba(246, 238, 238, 0.5)\",backgroundBlendMode:\"multiply\",color:\"black\",\"&:hover\":{backgroundColor:isSelected?\"rgba(95, 158, 160, 0.3)\":\"rgba(246, 238, 238, 0.6)\"}},children:translate(item,{defaultValue:item})},item);})})]})]})})]})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Background Color\",{defaultValue:\"Background Color\"}),children:/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleBackgroundColorClick,size:\"small\",sx:{height:\"20px\",width:\"20px\",backgroundColor:imagesContainer.style.backgroundColor,border:\"2px solid black\",marginTop:\"-3px\"}})})}),/*#__PURE__*/_jsx(Tooltip,{title:isCloneDisabled?translate(\"Maximum limit of 3 Image sections reached\",{defaultValue:\"Maximum limit of 3 Image sections reached\"}):translate(\"Clone Section\",{defaultValue:\"Clone Section\"}),children:/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleCloneImgContainer,size:\"small\",disabled:isCloneDisabled,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon},style:{opacity:isCloneDisabled?0.5:1}})})})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Delete Section\",{defaultValue:\"Delete Section\"}),children:/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleDeleteSection,size:\"small\",disabled:((_useDrawerStore$=useDrawerStore(state=>state.toolTipGuideMetaData)[0])===null||_useDrawerStore$===void 0?void 0:(_useDrawerStore$$cont=_useDrawerStore$.containers)===null||_useDrawerStore$$cont===void 0?void 0:_useDrawerStore$$cont.length)===1,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon},style:{opacity:((_useDrawerStore$2=useDrawerStore(state=>state.toolTipGuideMetaData)[0])===null||_useDrawerStore$2===void 0?void 0:(_useDrawerStore$2$con=_useDrawerStore$2.containers)===null||_useDrawerStore$2$con===void 0?void 0:_useDrawerStore$2$con.length)===1?0.5:1,pointerEvents:'none'}// style={{ marginTop: \"-3px\" }}\n})})})})]})}),/*#__PURE__*/_jsx(Popover,{id:\"color-popover\",open:colorPickerOpen,anchorEl:colorPickerAnchorEl,onClose:handleCloseColorPicker,anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"top\",horizontal:\"center\"},slotProps:{root:{sx:{// zIndex: (theme) => theme.zIndex.tooltip + 1101,\nzIndex:\"9999\"}}},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(ChromePicker,{color:imagesContainer.style.backgroundColor,onChange:handleColorChange}),/*#__PURE__*/_jsx(\"style\",{children:`\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `})]})}),isModelOpen&&/*#__PURE__*/_jsx(SelectImageFromApplication,{isOpen:isModelOpen,handleModelClose:()=>setModelOpen(false),onImageSelect:handleImageUploadFormApp})]});};export default ImageSection;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "RemoveIcon", "AddIcon", "SelectImageFromApplication", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ImageSection", "_ref", "_document$getElementB3", "_document$getElementB4", "_document$getElementB5", "_document$getElementB6", "_useDrawerStore$", "_useDrawerStore$$cont", "_useDrawerStore$2", "_useDrawerStore$2$con", "items", "imagesContainer", "isCloneDisabled", "t", "translate", "uploadTooltipImage", "uploadImage", "imageAnchorEl", "setImageAnchorEl", "replaceTooltipImage", "replaceImage", "cloneTooltipImage", "cloneImageContainer", "deleteTooltipImageContainer", "deleteImageContainer", "updateTooltipImageContainer", "updateImageContainer", "toggleTooltipImageFit", "toggleFit", "setImageSrc", "storeImageSrc", "tooltip", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "isReplaceImage", "setReplaceImage", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "style", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "urll", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "id", "altText", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "FileName", "handleReplaceImage", "_event$target$files2", "handleClick", "containerId", "imageId", "currentHeight", "includes", "buttonId", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "handleDeleteSection", "handleHyperlinkClick", "cId", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "visible", "children", "sx", "images", "textAlign", "onClick", "e", "_e$target", "startsWith", "_imagesContainer$styl", "component", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "defaultValue", "onChange", "onKeyDown", "autoFocus", "title", "pointerEvents", "cursor", "opacity", "_document$getElementB2", "stopPropagation", "type", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "map", "item", "_imagesContainer$styl2", "imageSrc", "currentSecHeight", "onMouseOver", "src", "alt", "anchorReference", "anchorPosition", "left", "getBoundingClientRect", "x", "top", "y", "transform<PERSON><PERSON>in", "marginLeft", "className", "size", "disabled", "anchorEl", "disable<PERSON><PERSON><PERSON>", "slotProps", "root", "zIndex", "theme", "PaperProps", "mt", "ml", "p", "marginBottom", "select", "fullWidth", "borderColor", "currentImage", "find", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "toolTipGuideMetaData", "containers", "handleModelClose", "onImageSelect"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/ImageSection.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip, Snackbar, Alert } from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DriveFolderUploadIcon from \"@mui/icons-material/DriveFolderUpload\";\r\nimport BackupIcon from \"@mui/icons-material/Backup\";\r\nimport Modal from \"@mui/material/Modal\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_EXPONENT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n\tTImageContainer,\r\n} from \"../../../store/drawerStore\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport \"../../guideSetting/PopupSections/PopupSections.css\";\r\nimport { getAllFiles } from \"../../../services/FileService\";\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nconst ImageSection: React.FC<{ items: TImageContainer & { type: \"image\" }; isCloneDisabled?: boolean; }> = ({ items: imagesContainer, isCloneDisabled }) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadTooltipImage: uploadImage,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceTooltipImage: replaceImage,\r\n\t\tcloneTooltipImage: cloneImageContainer,\r\n\t\tdeleteTooltipImageContainer: deleteImageContainer,\r\n\t\tupdateTooltipImageContainer: updateImageContainer, // TODO\r\n\t\ttoggleTooltipImageFit: toggleFit, //TODO\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t\ttooltip,\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\t\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\t\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\t\r\n\t\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\t\r\n\t\tconst openSnackbar = () => {\r\n\t\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\t\tsetSnackbarOpen(true);\r\n\t\t};\r\n\t\tconst closeSnackbar = () => {\r\n\t\t\tsetSnackbarOpen(false);\r\n\t\t};\r\n\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: `${imagesContainer.style.height}px`,\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tlet urll: any;\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\t\t\t// Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\t// setImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\t// urll = base64Image;\r\n\t\t\t\tuploadImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imagesContainer.id, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value && imagesContainer.id === imageAnchorEl.containerId);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imagesContainer.id, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imagesContainer.id, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\t// setReplaceImage(true);\r\n\t\t// setModelOpen(true);\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t};\r\n\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\r\n\t\tdeleteImageContainer(imagesContainer.id);\r\n\t};\r\n\tconst handleHyperlinkClick = (cId: string, isOpen: boolean) => {\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: cId,\r\n\t\t\tisOpen,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\t\tcloneImageContainer(imagesContainer.id);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imagesContainer.id, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\t// console.log({ imageAnchorEl, tooltip });\r\n\r\n\tuseEffect(() => {\r\n\t\tif (!tooltip.visible) {\r\n\t\t\tsetImageAnchorEl({\r\n\t\t\t\tbuttonId: \"\",\r\n\t\t\t\tcontainerId: \"\",\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: null,\r\n\t\t\t});\r\n\t\t\tsetSettingsAnchorEl(null);\r\n\t\t}\r\n\t}, [tooltip.visible]);\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Box\r\n\t\t\t\t// key={id}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t// padding: \"5px\",\r\n\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t{!imagesContainer.images.length ? (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\theight: `${imagesContainer.style.height}px`,\r\n\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t// height: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t// prevent to open toolbar when upload file clicked\r\n\t\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\t\tif (!e.target?.id.startsWith(\"file-upload\")) {\r\n\t\t\t\t\t\t\t\thandleClick(\r\n\t\t\t\t\t\t\t\t\te,\r\n\t\t\t\t\t\t\t\t\timagesContainer.id,\r\n\t\t\t\t\t\t\t\t\t\"\",\r\n\t\t\t\t\t\t\t\t\tfalse,\r\n\t\t\t\t\t\t\t\t\t(imagesContainer?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tid={imagesContainer.id}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Upload file\", { defaultValue: \"Upload file\" })}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\", { defaultValue: \"Drag & Drop to upload file\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Or\", { defaultValue: \"Or\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\", { defaultValue: \"Coming soon\" })}>\r\n\t\t\t\t\t\t\t\t\t<div style={{ pointerEvents: \"auto\", cursor: \"pointer\" }}>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: hyperlink }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"32px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: \"0.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\", { defaultValue: \"Coming soon\" })}>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\", { defaultValue: \"Upload File\" })}>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(`file-upload-${imagesContainer.id}`)?.click();\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\tid={`file-upload-${imagesContainer.id}`}\r\n\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t) : (\r\n\t\t\t\t\timagesContainer.images.map((item: any) => {\r\n\t\t\t\t\t\tconst imageSrc = item?.url;\r\n\t\t\t\t\t\tconst imageId = item?.id;\r\n\t\t\t\t\t\tconst objectFit = item?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\t\t\tconst currentSecHeight = (imagesContainer?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\t\t\tconst id = imagesContainer.id;\r\n\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\theight: `${imagesContainer.style.height}px`,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tsetSettingsAnchorEl(null);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t})\r\n\t\t\t\t)}\r\n\t\t\t</Box>\r\n\r\n\t\t\t<Popover\r\n\t\t\t\t// className=\"qadpt-imgsec-popover\"\r\n\t\t\t\tid={\"image-popover\"}\r\n\t\t\t\topen={open}\r\n\t\t\t\t// anchorEl={document.getElementById(\"Tooltip-unique\")}\r\n\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\tanchorPosition={{\r\n\t\t\t\t\tleft: document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.x || 150,\r\n\t\t\t\t\ttop: document.getElementById(\"Tooltip-unique\")?.getBoundingClientRect()?.y || 80,\r\n\t\t\t\t}}\r\n\t\t\t\tonClose={handleClose}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"right\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\t\r\n\t\t\t>\r\n\t\t\t\t<Box\r\n\t\t\t\t\t// className=\"qadpt-tool-btn\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t// justifyContent: \"space-between\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tgap: \"15px\",\r\n\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box sx={{ display: \"flex\" }}>\r\n\t\t\t\t\t\t{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&\r\n\t\t\t\t\t\tcurrentImageSectionInfo.isImage ? (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tfontSize=\"12px\"\r\n\t\t\t\t\t\t\t\t\tmarginLeft={\"5px\"}\r\n\t\t\t\t\t\t\t\t\tonClick={triggerImageUpload}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\", { defaultValue: \"Replace Image\" })}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleReplaceImage}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\", { defaultValue: \"Minimum height reached\" }) : translate(\"Decrease height\", { defaultValue: \"Decrease height\" })}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\", { defaultValue: \"Maximum height reached\" }) : translate(\"Increase height\", { defaultValue: \"Increase height\" })}>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t<Tooltip title={translate(\"Settings\", { defaultValue: \"Settings\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={settingsAnchorEl}\r\n\t\t\t\t\t\t\t\tid=\"image-properties\"\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"right\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisablePortal\r\n\t\t\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 2000,\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tPaperProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tmt: 12,\r\n\t\t\t\t\t\t\t\t\t\tml: 20,\r\n\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\", { defaultValue: \"Image Properties\" })}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\", { defaultValue: \"Coming soon\" })}>\r\n\t\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\", { defaultValue: \"Image Actions\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\", { defaultValue: \"None\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\", { defaultValue: \"Specific Step\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\", { defaultValue: \"Open URL\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\", { defaultValue: \"Click Element\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\", { defaultValue: \"Start Tour\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\", { defaultValue: \"Start Micro Survey\" })}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tmt={2}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t\tid=\"toggle-fit\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\", { defaultValue: \"Image Formatting\" })}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\t// imagesContainer is a single object, not an array\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = imagesContainer.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => toggleFit(imagesContainer.id, item as \"Fit\" | \"Fill\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item, { defaultValue: item })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip title={translate(\"Background Color\", { defaultValue: \"Background Color\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\tborder: \"2px solid black\",\r\n\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{/* <span\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: imagesContainer.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\", { defaultValue: \"Maximum limit of 3 Image sections reached\" }) : translate(\"Clone Section\", { defaultValue: \"Clone Section\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t{/* cloneImageContainer */}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip title={translate(\"Delete Section\", { defaultValue: \"Delete Section\" })}>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={\r\n\t\t\t\t\t\t\t\t\tuseDrawerStore((state) => state.toolTipGuideMetaData)[0]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\topacity:\r\n\t\t\t\t\t\t\t\t\t\t\tuseDrawerStore((state) => state.toolTipGuideMetaData)[0]?.containers?.length === 1\r\n\t\t\t\t\t\t\t\t\t\t\t\t? 0.5\r\n\t\t\t\t\t\t\t\t\t\t\t\t: 1,\r\n\t\t\t\t\t\t\t\t\t\tpointerEvents: 'none',\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t// style={{ marginTop: \"-3px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t\t<Popover\r\n\t\t\t\tid=\"color-popover\"\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t// zIndex: (theme) => theme.zIndex.tooltip + 1101,\r\n\t\t\t\t\t\t\tzIndex: \"9999\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={imagesContainer.style.backgroundColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t\t{isModelOpen && (\r\n\t\t\t\t<SelectImageFromApplication\r\n\t\t\t\t\tisOpen={isModelOpen}\r\n\t\t\t\t\thandleModelClose={() => setModelOpen(false)}\r\n\t\t\t\t\tonImageSelect={handleImageUploadFormApp}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,UAAU,CAAEC,OAAO,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,KAAK,KAAQ,eAAe,CAC3H,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAI7C,MAAO,CAAAC,0BAA0B,KAAM,yCAAyC,CAChF,OAASC,cAAc,KAAQ,eAAe,CAE9C,OACCC,UAAU,CACVC,SAAS,CACTC,KAAK,CACLC,UAAU,CACVC,gBAAgB,CAChBC,QAAQ,CACRC,UAAU,CACVC,aAAa,CACbC,QAAQ,CACRC,SAAS,KACH,6BAA6B,CACpC,MAAO,CAAAC,cAAc,EACpBC,4BAA4B,CAC5BC,wBAAwB,CACxBC,wBAAwB,CAExBC,cAAc,CACdC,cAAc,KAER,4BAA4B,CACnC,OAASC,YAAY,KAAqB,aAAa,CACvD,MAAO,oDAAoD,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAI5D,KAAM,CAAAC,YAAkG,CAAGC,IAAA,EAAiD,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,IAAhD,CAAEC,KAAK,CAAEC,eAAe,CAAEC,eAAgB,CAAC,CAAAX,IAAA,CACtJ,KAAM,CAAEY,CAAC,CAAEC,SAAU,CAAC,CAAGtC,cAAc,CAAC,CAAC,CACzC,KAAM,CACLuC,kBAAkB,CAAEC,WAAW,CAC/BC,aAAa,CACbC,gBAAgB,CAChBC,mBAAmB,CAAEC,YAAY,CACjCC,iBAAiB,CAAEC,mBAAmB,CACtCC,2BAA2B,CAAEC,oBAAoB,CACjDC,2BAA2B,CAAEC,oBAAoB,CAAE;AACnDC,qBAAqB,CAAEC,SAAS,CAAE;AAClCC,WAAW,CAAEC,aAAa,CAC1BC,OACD,CAAC,CAAG5C,cAAc,CAAE6C,KAAU,EAAKA,KAAK,CAAC,CAEzC,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACtD,KAAM,CAACyE,eAAe,CAAEC,kBAAkB,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC2E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG5E,QAAQ,CAA2C,MAAM,CAAC,CAE1G,KAAM,CAAC6E,WAAW,CAAEC,cAAc,CAAC,CAAG9E,QAAQ,CAAS,CAAC,CAAC,CAEzD,KAAM,CAAA+E,YAAY,CAAGA,CAAA,GAAM,CAC1BD,cAAc,CAACE,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAChCR,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,CACD,KAAM,CAAAS,aAAa,CAAGA,CAAA,GAAM,CAC3BT,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CAEF,KAAM,CAACU,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnF,QAAQ,CAAkD,CAC7GoF,kBAAkB,CAAE,EAAE,CACtBC,MAAM,CAAE,KACT,CAAC,CAAC,CACF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGvF,QAAQ,CAAS,EAAE,CAAC,CACtD,KAAM,CAACwF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzF,QAAQ,CAAqB,IAAI,CAAC,CACxF,KAAM,CAAC0F,uBAAuB,CAAEC,0BAA0B,CAAC,CAAG3F,QAAQ,CAInE,CAAEoF,kBAAkB,CAAE,EAAE,CAAEQ,OAAO,CAAE,KAAK,CAAEC,MAAM,CAAEnE,4BAA6B,CAAC,CAAC,CAEpF,KAAM,CAACoE,cAAc,CAAEC,iBAAiB,CAAC,CAAG/F,QAAQ,CAAC,MAAM,CAAC,CAC5D,KAAM,CAACgG,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjG,QAAQ,CAAqB,IAAI,CAAC,CAClF,KAAM,CAACkG,aAAa,CAAEC,gBAAgB,CAAC,CAAGnG,QAAQ,CAAS,EAAE,CAAC,CAC9D,KAAM,CAACoG,WAAW,CAAEC,YAAY,CAAC,CAAGrG,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACsG,YAAY,CAAEC,eAAe,CAAC,CAAGvG,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACwG,cAAc,CAAEC,eAAe,CAAC,CAAGzG,QAAQ,CAAC,KAAK,CAAC,CAEzD,KAAM,CAAA0G,mBAAmB,CAAGC,OAAO,CAACX,gBAAgB,CAAC,CACrD,KAAM,CAAAY,kBAAkB,CAAIC,KAAU,EAAK,CAC1Cd,iBAAiB,CAACc,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CACtC,CAAC,CACD,KAAM,CAAAC,mBAAmB,CAAIH,KAAoC,EAAK,CACrEZ,mBAAmB,CAACY,KAAK,CAACI,aAAa,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,0BAA0B,CAAGA,CAAA,GAAM,CACxCjB,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAkB,mBAAwC,CAAG,CAChDC,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,GAAG5C,eAAe,CAACoE,KAAK,CAACxB,MAAM,IAAI,CAC3CyB,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,CAAC,CACVC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QACX,CAAC,CAED,KAAM,CAAAC,UAA+B,CAAG,CACvCR,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,MAAM,CACd6B,MAAM,CAAE,CAAC,CACTD,OAAO,CAAE,CAAC,CACVI,YAAY,CAAE,GACf,CAAC,CAED,KAAM,CAAAC,YAAiC,CAAG,CACzCR,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBQ,GAAG,CAAE,MAAM,CACXC,SAAS,CAAE,MACZ,CAAC,CAED,KAAM,CAAAC,aAAkC,CAAG,CAC1CX,OAAO,CAAE,MAAM,CACfY,aAAa,CAAE,QAAQ,CACvBV,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBH,KAAK,CAAE,MACR,CAAC,CAED,KAAM,CAAAe,iBAAiB,CAAItB,KAA0C,EAAK,KAAAuB,mBAAA,CACzE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGvB,KAAK,CAACC,MAAM,CAAC7F,KAAK,UAAAmH,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAI,CAAAE,IAAS,CACb,GAAID,IAAI,CAAE,CACT,KAAM,CAAAE,KAAK,CAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAC/B,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACI,GAAG,CAAC,CAAC,CAChC;AACI,GAAIJ,KAAK,CAACK,MAAM,CAAG,CAAC,EAAI,CAACF,SAAS,CAAG,CACvChE,kBAAkB,CAAC,6DAA6D,CAAC,CAC5EE,mBAAmB,CAAC,OAAO,CAAC,CAClCJ,eAAe,CAAC,IAAI,CAAC,CACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAG,EAAE,CAClB,OAEF,CACH,GAAGsB,IAAI,CAACG,IAAI,CAACI,MAAM,CAAG,GAAG,CAAC,CAC1BlE,kBAAkB,CAAC,4CAA4C,CAAC,CAC1DE,mBAAmB,CAAC,OAAO,CAAC,CACjCJ,eAAe,CAAC,IAAI,CAAC,CACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAG,EAAE,CAClB,OACN,CACD;AAEA,KAAM,CAAA8B,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACxB,KAAM,CAAAC,WAAW,CAAGH,MAAM,CAACI,MAAgB,CAC3C7E,aAAa,CAAC4E,WAAW,CAAC,CAC1B;AACA1F,WAAW,CAACL,eAAe,CAACiG,EAAE,CAAE,CAC/BC,OAAO,CAAEd,IAAI,CAACG,IAAI,CAClBU,EAAE,CAAEE,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBC,GAAG,CAAEN,WAAW,CAChBO,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE3H,cACZ,CAAC,CAAC,CACH,CAAC,CACDgH,MAAM,CAACY,aAAa,CAACpB,IAAI,CAAC,CAC3B,CACD,CAAC,CAED,KAAM,CAAAqB,wBAAwB,CAAIrB,IAAgB,EAAK,CACtD,GAAIA,IAAI,CAAE,CACTjE,aAAa,CAACiE,IAAI,CAACsB,GAAG,CAAC,CACvB,GAAInD,cAAc,CAAE,CACnB9C,YAAY,CAACT,eAAe,CAACiG,EAAE,CAAE,CAChCC,OAAO,CAAEd,IAAI,CAACuB,QAAQ,CACtBN,GAAG,CAAEjB,IAAI,CAACsB,GAAG,CACbJ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE3H,cACZ,CAAC,CAAC,CACF4E,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,IAAM,CACNnD,WAAW,CAACL,eAAe,CAACiG,EAAE,CAAE,CAC/BC,OAAO,CAAEd,IAAI,CAACuB,QAAQ,CACtBV,EAAE,CAAEE,MAAM,CAACC,UAAU,CAAC,CAAC,CAAE;AACzBC,GAAG,CAAEjB,IAAI,CAACsB,GAAG,CAAE;AACfJ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE3H,cACZ,CAAC,CAAC,CACH,CACD,CACAwE,YAAY,CAAC,KAAK,CAAC,CACpB,CAAC,CAED,KAAM,CAAAwD,kBAAkB,CAAIhD,KAA0C,EAAK,KAAAiD,oBAAA,CAC1E,KAAM,CAAAzB,IAAI,EAAAyB,oBAAA,CAAGjD,KAAK,CAACC,MAAM,CAAC7F,KAAK,UAAA6I,oBAAA,iBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CACpC,GAAIzB,IAAI,CAAE,CACT,KAAM,CAAAQ,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACxBrF,YAAY,CAACT,eAAe,CAACiG,EAAE,CAAE,CAChCC,OAAO,CAAEd,IAAI,CAACG,IAAI,CAClBc,GAAG,CAAET,MAAM,CAACI,MAAM,CAClBM,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE3H,cACZ,CAAC,CAAC,CACH,CAAC,CACDgH,MAAM,CAACY,aAAa,CAACpB,IAAI,CAAC,CAC3B,CACD,CAAC,CAED,KAAM,CAAA0B,WAAW,CAAGA,CACnBlD,KAAoC,CACpCmD,WAAmB,CACnBC,OAAe,CACfrE,OAAgB,CAChBsE,aAAqB,GACjB,CACJ;AACA,GAAI,CAAC,aAAa,CAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACoC,EAAE,CAAC,CAAE,OAC5D1F,gBAAgB,CAAC,CAChB4G,QAAQ,CAAEH,OAAO,CACjBD,WAAW,CAAEA,WAAW,CACxB;AACAjD,KAAK,CAAEF,KAAK,CAACI,aACd,CAAC,CAAC,CACFhB,mBAAmB,CAAC,IAAI,CAAC,CACzBN,0BAA0B,CAAC,CAC1BP,kBAAkB,CAAE4E,WAAW,CAC/BpE,OAAO,CACPC,MAAM,CAAEqE,aACT,CAAC,CAAC,CACF/E,qBAAqB,CAAC,CACrBC,kBAAkB,CAAE,EAAE,CACtBC,MAAM,CAAE,KACT,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAgF,WAAW,CAAGA,CAAA,GAAM,CACzB7G,gBAAgB,CAAC,CAChB4G,QAAQ,CAAE,EAAE,CACZJ,WAAW,CAAE,EAAE,CACf;AACAjD,KAAK,CAAE,IACR,CAAC,CAAC,CACFd,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAqE,IAAI,CAAG3D,OAAO,CAACpD,aAAa,CAACwD,KAAK,EAAI9D,eAAe,CAACiG,EAAE,GAAK3F,aAAa,CAACyG,WAAW,CAAC,CAC7F,KAAM,CAAAO,eAAe,CAAG5D,OAAO,CAACnB,mBAAmB,CAAC,CAEpD,KAAM,CAAA0D,EAAE,CAAGoB,IAAI,CAAG,eAAe,CAAGE,SAAS,CAE7C,KAAM,CAAAC,oBAAoB,CAAIC,UAAkB,EAAK,CACpD,GAAIA,UAAU,EAAI/I,wBAAwB,CAAE,OAC5C,KAAM,CAAAgJ,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,CAAG5I,cAAc,CAAEH,wBAAwB,CAAC,CACjFqC,oBAAoB,CAACf,eAAe,CAACiG,EAAE,CAAE,OAAO,CAAE,CACjDrD,MAAM,CAAE8E,SACT,CAAC,CAAC,CACFhF,0BAA0B,CAAEX,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAEa,MAAM,CAAE8E,SAAU,CAAC,CAAC,CAAC,CACvE,CAAC,CAED,KAAM,CAAAG,oBAAoB,CAAIJ,UAAkB,EAAK,CACpD,GAAIA,UAAU,EAAI9I,wBAAwB,CAAE,OAC5C,KAAM,CAAA+I,SAAS,CAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,CAAG5I,cAAc,CAAEF,wBAAwB,CAAC,CACjFoC,oBAAoB,CAACf,eAAe,CAACiG,EAAE,CAAE,OAAO,CAAE,CACjDrD,MAAM,CAAE8E,SACT,CAAC,CAAC,CACFhF,0BAA0B,CAAEX,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAEa,MAAM,CAAE8E,SAAU,CAAC,CAAC,CAAC,CACvE,CAAC,CAED,KAAM,CAAAK,kBAAkB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAChC;AACA;AACA,CAAAA,qBAAA,CAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,UAAAF,qBAAA,iBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC,CACnD,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CACjC7H,gBAAgB,CAAC,CAChB4G,QAAQ,CAAE,EAAE,CACZJ,WAAW,CAAE,EAAE,CACf;AACAjD,KAAK,CAAE,IACR,CAAC,CAAC,CACFd,mBAAmB,CAAC,IAAI,CAAC,CAEzBnC,oBAAoB,CAACb,eAAe,CAACiG,EAAE,CAAC,CACzC,CAAC,CACD,KAAM,CAAAoC,oBAAoB,CAAGA,CAACC,GAAW,CAAElG,MAAe,GAAK,CAC9DF,qBAAqB,CAAC,CACrBC,kBAAkB,CAAEmG,GAAG,CACvBlG,MACD,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAmG,gBAAgB,CAAI3E,KAA4C,EAAK,CAC1E,GAAIA,KAAK,CAAC4E,GAAG,GAAK,OAAO,EAAInG,SAAS,CAAE,CACvChC,WAAW,CAACC,aAAa,CAACyG,WAAW,CAAE,CACtCb,OAAO,CAAE,WAAW,CACpBD,EAAE,CAAEE,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBC,GAAG,CAAEhE,SAAS,CACdiE,eAAe,CAAE,aAAa,CAC9BC,SAAS,CAAE3H,cACZ,CAAC,CAAC,CACFsD,qBAAqB,CAAC,CACrBC,kBAAkB,CAAE,EAAE,CACtBC,MAAM,CAAE,KACT,CAAC,CAAC,CACH,CACD,CAAC,CAED,KAAM,CAAAqG,uBAAuB,CAAGA,CAAA,GAAM,CACrC9H,mBAAmB,CAACX,eAAe,CAACiG,EAAE,CAAC,CACxC,CAAC,CAED,KAAM,CAAAyC,sBAAsB,CAAGA,CAAA,GAAM,CACpClG,sBAAsB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAmG,iBAAiB,CAAIC,KAAkB,EAAK,CACjD1F,gBAAgB,CAAC0F,KAAK,CAACC,GAAG,CAAC,CAC3B9H,oBAAoB,CAACf,eAAe,CAACiG,EAAE,CAAE,OAAO,CAAE,CACjDK,eAAe,CAAEsC,KAAK,CAACC,GACxB,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,0BAA0B,CAAIlF,KAAoC,EAAK,CAC5EpB,sBAAsB,CAACoB,KAAK,CAACI,aAAa,CAAC,CAC5C,CAAC,CAED;AAEAlH,SAAS,CAAC,IAAM,CACf,GAAI,CAACsE,OAAO,CAAC2H,OAAO,CAAE,CACrBxI,gBAAgB,CAAC,CAChB4G,QAAQ,CAAE,EAAE,CACZJ,WAAW,CAAE,EAAE,CACf;AACAjD,KAAK,CAAE,IACR,CAAC,CAAC,CACFd,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CACD,CAAC,CAAE,CAAC5B,OAAO,CAAC2H,OAAO,CAAC,CAAC,CAErB,mBACC7J,KAAA,CAAAE,SAAA,EAAA4J,QAAA,eACChK,IAAA,CAAChC,GACA;AAAA,EACAiM,EAAE,CAAE,CACH9E,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,MAAM,CACdyB,OAAO,CAAE,MAAM,CACfY,aAAa,CAAE,QAAQ,CACvBX,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,QAAQ,CACpB;AACAE,MAAM,CAAE,KAAK,CACbC,QAAQ,CAAE,MACX,CAAE,CAAAsE,QAAA,CAED,CAAChJ,eAAe,CAACkJ,MAAM,CAACvD,MAAM,cAC9BzG,KAAA,CAAClC,GAAG,EACHiM,EAAE,CAAE,CACH,GAAG/E,mBAAmB,CACtBoC,eAAe,CAAEtG,eAAe,CAACoE,KAAK,CAACkC,eAAe,CACtD1D,MAAM,CAAE,GAAG5C,eAAe,CAACoE,KAAK,CAACxB,MAAM,IAAI,CAC3CuG,SAAS,CAAE,QAAQ,CACnBhF,KAAK,CAAE,MAAM,CACb;AACAE,OAAO,CAAE,MAAM,CACfY,aAAa,CAAE,QAAQ,CACvBX,cAAc,CAAE,QACjB,CAAE,CACF8E,OAAO,CAAGC,CAAC,EAAK,KAAAC,SAAA,CACf;AACA;AACA,GAAI,GAAAA,SAAA,CAACD,CAAC,CAACxF,MAAM,UAAAyF,SAAA,WAARA,SAAA,CAAUrD,EAAE,CAACsD,UAAU,CAAC,aAAa,CAAC,EAAE,KAAAC,qBAAA,CAC5C1C,WAAW,CACVuC,CAAC,CACDrJ,eAAe,CAACiG,EAAE,CAClB,EAAE,CACF,KAAK,CACL,CAACjG,eAAe,SAAfA,eAAe,kBAAAwJ,qBAAA,CAAfxJ,eAAe,CAAEoE,KAAK,UAAAoF,qBAAA,iBAAtBA,qBAAA,CAAwB5G,MAAM,GAAenE,4BAC/C,CAAC,CACF,CACD,CAAE,CACFwH,EAAE,CAAEjG,eAAe,CAACiG,EAAG,CAAA+C,QAAA,eAEvB9J,KAAA,CAAClC,GAAG,EACHiM,EAAE,CAAEjE,aAAc,CAClByE,SAAS,CAAE,KAAM,CAAAT,QAAA,eAEjBhK,IAAA,SACC0K,uBAAuB,CAAE,CAAEC,MAAM,CAAE7L,UAAW,CAAE,CAChDsG,KAAK,CAAE,CAAEC,OAAO,CAAE,cAAe,CAAE,CACnC,CAAC,cACFrF,IAAA,CAAC/B,UAAU,EACV2M,OAAO,CAAC,IAAI,CACZC,KAAK,CAAC,QAAQ,CACdjB,KAAK,CAAC,eAAe,CACrBK,EAAE,CAAE,CAAEa,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAf,QAAA,CAE3C7I,SAAS,CAAC,aAAa,CAAE,CAAE6J,YAAY,CAAE,aAAc,CAAC,CAAC,CAC/C,CAAC,EACT,CAAC,cAENhL,IAAA,CAAC/B,UAAU,EACV2M,OAAO,CAAC,OAAO,CACfC,KAAK,CAAC,QAAQ,CACdjB,KAAK,CAAC,eAAe,CACrBK,EAAE,CAAE,CAAEa,QAAQ,CAAE,MAAO,CAAE,CAAAd,QAAA,CAExB7I,SAAS,CAAC,4BAA4B,CAAE,CAAE6J,YAAY,CAAE,4BAA6B,CAAC,CAAC,CAC7E,CAAC,cACbhL,IAAA,CAAC/B,UAAU,EACV2M,OAAO,CAAC,OAAO,CACfC,KAAK,CAAC,QAAQ,CACdjB,KAAK,CAAC,eAAe,CACrBK,EAAE,CAAE,CAAElE,SAAS,CAAE,KAAK,CAAE+E,QAAQ,CAAE,MAAO,CAAE,CAAAd,QAAA,CAE1C7I,SAAS,CAAC,IAAI,CAAE,CAAE6J,YAAY,CAAE,IAAK,CAAC,CAAC,CAC7B,CAAC,CACZ/H,kBAAkB,CAACG,MAAM,EAAIH,kBAAkB,CAACE,kBAAkB,GAAK8D,EAAE,cACzEjH,IAAA,CAAC5B,SAAS,EACT0G,KAAK,CAAEzB,SAAU,CACjB4H,QAAQ,CAAGZ,CAAC,EAAK/G,YAAY,CAAC+G,CAAC,CAACxF,MAAM,CAACC,KAAK,CAAE,CAC9CoG,SAAS,CAAE3B,gBAAiB,CAC5B4B,SAAS,MACT,CAAC,cAEFjL,KAAA,CAAClC,GAAG,EAACiM,EAAE,CAAEpE,YAAa,CAAAmE,QAAA,eACpBhK,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEjK,SAAS,CAAC,aAAa,CAAE,CAAE6J,YAAY,CAAE,aAAc,CAAC,CAAE,CAAAhB,QAAA,cAC1EhK,IAAA,QAAKoF,KAAK,CAAE,CAAEiG,aAAa,CAAE,MAAM,CAAEC,MAAM,CAAE,SAAU,CAAE,CAAAtB,QAAA,cACxDhK,IAAA,SACC0K,uBAAuB,CAAE,CAAEC,MAAM,CAAE5L,SAAU,CAAE,CAC/CqG,KAAK,CAAE,CACNwE,KAAK,CAAE,OAAO,CACd0B,MAAM,CAAE,SAAS,CACjBR,QAAQ,CAAE,MAAM,CAChBS,OAAO,CAAE,KAAK,CACdF,aAAa,CAAE,MAChB,CAAE,CACFpE,EAAE,CAAC,WAAW,CACd,CAAC,CACE,CAAC,CACE,CAAC,cAETjH,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEjK,SAAS,CAAC,aAAa,CAAE,CAAE6J,YAAY,CAAE,aAAc,CAAC,CAAE,CAAAhB,QAAA,cAC1EhK,IAAA,SACCoK,OAAO,CAAEA,CAAA,GAAM,CACd;AAAA,CACC,CACFM,uBAAuB,CAAE,CAAEC,MAAM,CAAE3L,KAAM,CAAE,CAC3CoG,KAAK,CAAE,CAAEwE,KAAK,CAAE,OAAO,CAAE0B,MAAM,CAAE,SAAS,CAAER,QAAQ,CAAE,MAAM,CAAES,OAAO,CAAE,KAAM,CAAE,CAC/EtE,EAAE,CAAC,QACH;AAAA,CACA,CAAC,CACM,CAAC,cACTjH,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEjK,SAAS,CAAC,aAAa,CAAE,CAAE6J,YAAY,CAAE,aAAc,CAAC,CAAE,CAAAhB,QAAA,cAC1EhK,IAAA,SACCoK,OAAO,CAAGxF,KAAK,EAAK,KAAA4G,sBAAA,CACnB5G,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE6G,eAAe,CAAC,CAAC,CACxB,CAAAD,sBAAA,CAAAvC,QAAQ,CAACC,cAAc,CAAC,eAAelI,eAAe,CAACiG,EAAE,EAAE,CAAC,UAAAuE,sBAAA,iBAA5DA,sBAAA,CAA8DrC,KAAK,CAAC,CAAC,CACtE,CAAE,CACFlC,EAAE,CAAC,cAAc,CACjByD,uBAAuB,CAAE,CAAEC,MAAM,CAAE1L,UAAW,CAAE,CAChDmG,KAAK,CAAE,CAAEwE,KAAK,CAAE,OAAO,CAAE0B,MAAM,CAAE,SAAS,CAAER,QAAQ,CAAE,MAAO,CAAE,CAC/D,CAAC,CACM,CAAC,cACV9K,IAAA,UACC0L,IAAI,CAAC,MAAM,CACXzE,EAAE,CAAE,eAAejG,eAAe,CAACiG,EAAE,EAAG,CACxC7B,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC3BsG,MAAM,CAAC,SAAS,CAChBV,QAAQ,CAAE/E,iBAAkB,CAC5B,CAAC,cACFlG,IAAA,CAACxB,QAAQ,EAAC6J,IAAI,CAAE/F,YAAa,CAACsJ,gBAAgB,CAAE,IAAK,CAACC,OAAO,CAAE7I,aAAc,CAAC8I,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAhC,QAAA,cACvIhK,IAAA,CAACvB,KAAK,EAACoN,OAAO,CAAE7I,aAAc,CAACiJ,QAAQ,CAAEvJ,gBAAiB,CAACuH,EAAE,CAAE,CAAE9E,KAAK,CAAE,MAAO,CAAE,CAAA6E,QAAA,CAC/ExH,eAAe,CACV,CAAC,CACA,CAAC,EACP,CACL,EACG,CAAC,CAENxB,eAAe,CAACkJ,MAAM,CAACgC,GAAG,CAAEC,IAAS,EAAK,KAAAC,sBAAA,CACzC,KAAM,CAAAC,QAAQ,CAAGF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE9E,GAAG,CAC1B,KAAM,CAAAW,OAAO,CAAGmE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAElF,EAAE,CACxB,KAAM,CAAAM,SAAS,CAAG,CAAA4E,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE5E,SAAS,GAAI3H,cAAc,CACnD,KAAM,CAAA0M,gBAAgB,CAAG,CAACtL,eAAe,SAAfA,eAAe,kBAAAoL,sBAAA,CAAfpL,eAAe,CAAEoE,KAAK,UAAAgH,sBAAA,iBAAtBA,sBAAA,CAAwBxI,MAAM,GAAenE,4BAA4B,CACnG,KAAM,CAAAwH,EAAE,CAAGjG,eAAe,CAACiG,EAAE,CAC7B,mBACCjH,IAAA,CAAChC,GAAG,EACHiM,EAAE,CAAE,CACH,GAAG/E,mBAAmB,CACtBoC,eAAe,CAAEtG,eAAe,CAACoE,KAAK,CAACkC,eAAe,CACtD1D,MAAM,CAAE,GAAG5C,eAAe,CAACoE,KAAK,CAACxB,MAAM,IACxC,CAAE,CACFwG,OAAO,CAAGC,CAAC,EAAKvC,WAAW,CAACuC,CAAC,CAAEpD,EAAE,CAAEe,OAAO,CAAEqE,QAAQ,CAAG,IAAI,CAAG,KAAK,CAAEC,gBAAgB,CAAE,CACvF7B,SAAS,CAAE,KAAM,CACjBxD,EAAE,CAAEA,EAAG,CACPsF,WAAW,CAAEA,CAAA,GAAM,CAClBhL,gBAAgB,CAAC,CAChB4G,QAAQ,CAAEH,OAAO,CACjBD,WAAW,CAAEd,EAAE,CACfnC,KAAK,CAAE,IACR,CAAC,CAAC,CACFd,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAE,CAAAgG,QAAA,cAEFhK,IAAA,QACCwM,GAAG,CAAEH,QAAS,CACdI,GAAG,CAAC,UAAU,CACdrH,KAAK,CAAE,CAAE,GAAGO,UAAU,CAAE4B,SAAU,CAAE,CACpC,CAAC,CACE,CAAC,CAER,CAAC,CACD,CACG,CAAC,cAENvH,IAAA,CAAC9B,OACA;AAAA,EACA+I,EAAE,CAAE,eAAgB,CACpBoB,IAAI,CAAEA,IACN;AAAA,CACAqE,eAAe,CAAC,gBAAgB,CAChCC,cAAc,CAAE,CACfC,IAAI,CAAE,EAAArM,sBAAA,CAAA0I,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,UAAA3I,sBAAA,kBAAAC,sBAAA,CAAzCD,sBAAA,CAA2CsM,qBAAqB,CAAC,CAAC,UAAArM,sBAAA,iBAAlEA,sBAAA,CAAoEsM,CAAC,GAAI,GAAG,CAClFC,GAAG,CAAE,EAAAtM,sBAAA,CAAAwI,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,UAAAzI,sBAAA,kBAAAC,sBAAA,CAAzCD,sBAAA,CAA2CoM,qBAAqB,CAAC,CAAC,UAAAnM,sBAAA,iBAAlEA,sBAAA,CAAoEsM,CAAC,GAAI,EAC/E,CAAE,CACFnB,OAAO,CAAEzD,WAAY,CACrB0D,YAAY,CAAE,CACbC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,OACb,CAAE,CACFiB,eAAe,CAAE,CAChBlB,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,MACb,CAAE,CAAAhC,QAAA,cAGF9J,KAAA,CAAClC,GACA;AAAA,EACAiM,EAAE,CAAE,CACH5E,OAAO,CAAE,MAAM,CACf;AACAE,UAAU,CAAE,QAAQ,CACpBO,GAAG,CAAE,MAAM,CACXlC,MAAM,CAAE,MAAM,CACd4B,OAAO,CAAE,QAAQ,CACjBsF,QAAQ,CAAE,MACX,CAAE,CAAAd,QAAA,eAEFhK,IAAA,CAAChC,GAAG,EAACiM,EAAE,CAAE,CAAE5E,OAAO,CAAE,MAAO,CAAE,CAAA2E,QAAA,CAC3BvG,uBAAuB,CAACN,kBAAkB,GAAK7B,aAAa,CAACyG,WAAW,EACzEtE,uBAAuB,CAACE,OAAO,cAC9BzD,KAAA,CAAAE,SAAA,EAAA4J,QAAA,eACChK,IAAA,SAAM0K,uBAAuB,CAAE,CAAEC,MAAM,CAAEzL,gBAAiB,CAAE,CAAE,CAAC,cAC/Dc,IAAA,CAAC/B,UAAU,EACV6M,QAAQ,CAAC,MAAM,CACfoC,UAAU,CAAE,KAAM,CAClB9C,OAAO,CAAErB,kBAAmB,CAAAiB,QAAA,CAE1B7I,SAAS,CAAC,eAAe,CAAE,CAAE6J,YAAY,CAAE,eAAgB,CAAC,CAAC,CACpD,CAAC,cACbhL,IAAA,UACC0L,IAAI,CAAC,MAAM,CACXzE,EAAE,CAAC,gBAAgB,CACnB7B,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC3BsG,MAAM,CAAC,SAAS,CAChBV,QAAQ,CAAErD,kBAAmB,CAC7B,CAAC,EACD,CAAC,CACA,IAAI,CACJ,CAAC,cACN1H,KAAA,CAAClC,GAAG,EACFmP,SAAS,CAAC,kBAAkB,CAC5BlD,EAAE,CAAE,CAAE5E,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAyE,QAAA,eAE9ChK,IAAA,SAAM0K,uBAAuB,CAAE,CAAEC,MAAM,CAAEtL,aAAc,CAAE,CAAE,CAAC,cAC7DW,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAE3H,uBAAuB,CAACG,MAAM,EAAIjE,wBAAwB,CAAGwB,SAAS,CAAC,wBAAwB,CAAE,CAAE6J,YAAY,CAAE,wBAAyB,CAAC,CAAC,CAAG7J,SAAS,CAAC,iBAAiB,CAAE,CAAE6J,YAAY,CAAE,iBAAkB,CAAC,CAAE,CAAAhB,QAAA,cAC/NhK,IAAA,SAAAgK,QAAA,cACChK,IAAA,CAAC7B,UAAU,EACViM,OAAO,CAAEA,CAAA,GAAMvB,oBAAoB,CAACpF,uBAAuB,CAACG,MAAM,CAAE,CACpEwJ,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAE5J,uBAAuB,CAACG,MAAM,EAAIjE,wBAAyB,CACrEsK,EAAE,CAAE,CACHsB,OAAO,CAAE9H,uBAAuB,CAACG,MAAM,EAAIjE,wBAAwB,CAAG,GAAG,CAAG,CAAC,CAC7E2L,MAAM,CAAE7H,uBAAuB,CAACG,MAAM,EAAIjE,wBAAwB,CAAG,aAAa,CAAG,SACtF,CAAE,CAAAqK,QAAA,cAEFhK,IAAA,CAACtB,UAAU,EAACoM,QAAQ,CAAC,OAAO,CAAE,CAAC,CACpB,CAAC,CACR,CAAC,CACC,CAAC,cACV9K,IAAA,CAAC/B,UAAU,EAAC6M,QAAQ,CAAC,MAAM,CAAAd,QAAA,CAAEvG,uBAAuB,CAACG,MAAM,CAAa,CAAC,cAC1E5D,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAE3H,uBAAuB,CAACG,MAAM,EAAIlE,wBAAwB,CAAGyB,SAAS,CAAC,wBAAwB,CAAE,CAAE6J,YAAY,CAAE,wBAAyB,CAAC,CAAC,CAAG7J,SAAS,CAAC,iBAAiB,CAAE,CAAE6J,YAAY,CAAE,iBAAkB,CAAC,CAAE,CAAAhB,QAAA,cAC/NhK,IAAA,SAAAgK,QAAA,cACChK,IAAA,CAAC7B,UAAU,EACViM,OAAO,CAAEA,CAAA,GAAM5B,oBAAoB,CAAC/E,uBAAuB,CAACG,MAAM,CAAE,CACpEwJ,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAE5J,uBAAuB,CAACG,MAAM,EAAIlE,wBAAyB,CACrEuK,EAAE,CAAE,CACHsB,OAAO,CAAE9H,uBAAuB,CAACG,MAAM,EAAIlE,wBAAwB,CAAG,GAAG,CAAG,CAAC,CAC7E4L,MAAM,CAAE7H,uBAAuB,CAACG,MAAM,EAAIlE,wBAAwB,CAAG,aAAa,CAAG,SACtF,CAAE,CAAAsK,QAAA,cAEFhK,IAAA,CAACrB,OAAO,EAACmM,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjB,CAAC,CACR,CAAC,CACC,CAAC,EACN,CAAC,cACP9K,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEjK,SAAS,CAAC,UAAU,CAAE,CAAE6J,YAAY,CAAE,UAAW,CAAC,CAAE,CAAAhB,QAAA,cACnE9J,KAAA,CAAClC,GAAG,EAACmP,SAAS,CAAC,kBAAkB,CAAAnD,QAAA,eAChChK,IAAA,CAAChC,GAAG,EAACmP,SAAS,CAAC,kBAAkB,CAAAnD,QAAA,cAChChK,IAAA,CAAC7B,UAAU,EACViP,IAAI,CAAC,OAAO,CACZhD,OAAO,CAAErF,mBAAoB,CAAAiF,QAAA,cAE7BhK,IAAA,SACC0K,uBAAuB,CAAE,CAAEC,MAAM,CAAErL,QAAS,CAAE,CAC9C8F,KAAK,CAAE,CAAEwE,KAAK,CAAE,OAAQ,CAAE,CAC1B,CAAC,CACS,CAAC,CACT,CAAC,cAEN5J,IAAA,CAAC9B,OAAO,EACPmK,IAAI,CAAE5D,mBAAoB,CAC1B6I,QAAQ,CAAEvJ,gBAAiB,CAC3BkD,EAAE,CAAC,kBAAkB,CACrB4E,OAAO,CAAE5G,0BAA2B,CACpC6G,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,OACb,CAAE,CACFuB,aAAa,MACbN,eAAe,CAAE,CAChBlB,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,MACb,CAAE,CACFwB,SAAS,CAAE,CACVC,IAAI,CAAE,CACLxD,EAAE,CAAE,CACHyD,MAAM,CAAGC,KAAK,EAAKA,KAAK,CAACD,MAAM,CAACtL,OAAO,CAAG,IAC3C,CACD,CACD,CAAE,CACFwL,UAAU,CAAE,CACX3D,EAAE,CAAE,CACH4D,EAAE,CAAE,EAAE,CACNC,EAAE,CAAE,EAAE,CACN3I,KAAK,CAAE,OACR,CACD,CAAE,CAAA6E,QAAA,cAEF9J,KAAA,CAAClC,GAAG,EAAC+P,CAAC,CAAE,CAAE,CAAA/D,QAAA,eACT9J,KAAA,CAAClC,GAAG,EACHqH,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,eAAe,CAC9BC,UAAU,CAAC,QAAQ,CAAAyE,QAAA,eAEnBhK,IAAA,CAAC/B,UAAU,EACV2M,OAAO,CAAC,WAAW,CACnBX,EAAE,CAAE,CAAEL,KAAK,CAAE,uBAAwB,CAAE,CAAAI,QAAA,CAEtC7I,SAAS,CAAC,kBAAkB,CAAE,CAAE6J,YAAY,CAAE,kBAAmB,CAAC,CAAC,CACzD,CAAC,cACbhL,IAAA,CAAC7B,UAAU,EACViP,IAAI,CAAC,OAAO,CACZhD,OAAO,CAAEnF,0BAA2B,CAAA+E,QAAA,cAEpChK,IAAA,SACC0K,uBAAuB,CAAE,CAAEC,MAAM,CAAEpL,SAAU,CAAE,CAC/C6F,KAAK,CAAE,CAAEwE,KAAK,CAAE,OAAQ,CAAE,CAC1B,CAAC,CACS,CAAC,EACT,CAAC,cACN5J,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEjK,SAAS,CAAC,aAAa,CAAE,CAAE6J,YAAY,CAAE,aAAc,CAAC,CAAE,CAAAhB,QAAA,cACzE9J,KAAA,CAAClC,GAAG,EAAC6P,EAAE,CAAE,CAAE,CAAA7D,QAAA,eACVhK,IAAA,CAAC/B,UAAU,EACV2M,OAAO,CAAC,OAAO,CACfhB,KAAK,CAAC,eAAe,CACrBK,EAAE,CAAE,CAAE+D,YAAY,CAAE,MAAO,CAAE,CAAAhE,QAAA,CAE5B7I,SAAS,CAAC,eAAe,CAAE,CAAE6J,YAAY,CAAE,eAAgB,CAAC,CAAC,CACnD,CAAC,cACb9K,KAAA,CAAC9B,SAAS,EACT6P,MAAM,MACNC,SAAS,MACTtD,OAAO,CAAC,UAAU,CAClBwC,IAAI,CAAC,OAAO,CACZtI,KAAK,CAAEjB,cAAe,CACtBoH,QAAQ,CAAEtG,kBAAmB,CAC7BsF,EAAE,CAAE,CACH,0BAA0B,CAAE,CAC3BkE,WAAW,CAAE,wBACd,CACD,CAAE,CACFd,QAAQ,MAAArD,QAAA,eAERhK,IAAA,CAAC3B,QAAQ,EAACyG,KAAK,CAAC,MAAM,CAAAkF,QAAA,CAAE7I,SAAS,CAAC,MAAM,CAAE,CAAE6J,YAAY,CAAE,MAAO,CAAC,CAAC,CAAW,CAAC,cAC/EhL,IAAA,CAAC3B,QAAQ,EAACyG,KAAK,CAAC,cAAc,CAAAkF,QAAA,CAAE7I,SAAS,CAAC,eAAe,CAAE,CAAE6J,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAW,CAAC,cACzGhL,IAAA,CAAC3B,QAAQ,EAACyG,KAAK,CAAC,SAAS,CAAAkF,QAAA,CAAE7I,SAAS,CAAC,UAAU,CAAE,CAAE6J,YAAY,CAAE,UAAW,CAAC,CAAC,CAAW,CAAC,cAC1FhL,IAAA,CAAC3B,QAAQ,EAACyG,KAAK,CAAC,cAAc,CAAAkF,QAAA,CAAE7I,SAAS,CAAC,eAAe,CAAE,CAAE6J,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAW,CAAC,cACzGhL,IAAA,CAAC3B,QAAQ,EAACyG,KAAK,CAAC,WAAW,CAAAkF,QAAA,CAAE7I,SAAS,CAAC,YAAY,CAAE,CAAE6J,YAAY,CAAE,YAAa,CAAC,CAAC,CAAW,CAAC,cAChGhL,IAAA,CAAC3B,QAAQ,EAACyG,KAAK,CAAC,kBAAkB,CAAAkF,QAAA,CAAE7I,SAAS,CAAC,oBAAoB,CAAE,CAAE6J,YAAY,CAAE,oBAAqB,CAAC,CAAC,CAAW,CAAC,EAC7G,CAAC,EACR,CAAC,CACE,CAAC,cACV9K,KAAA,CAAClC,GAAG,EACH6P,EAAE,CAAE,CAAE,CACNpD,SAAS,CAAE,KAAM,CACjBxD,EAAE,CAAC,YAAY,CAAA+C,QAAA,eAEfhK,IAAA,CAAC/B,UAAU,EACV2M,OAAO,CAAC,OAAO,CACfhB,KAAK,CAAC,eAAe,CAAAI,QAAA,CAEpB7I,SAAS,CAAC,kBAAkB,CAAE,CAAE6J,YAAY,CAAE,kBAAmB,CAAC,CAAC,CACzD,CAAC,cACbhL,IAAA,CAAChC,GAAG,EACHqH,OAAO,CAAC,MAAM,CACdS,GAAG,CAAE,CAAE,CACP+H,EAAE,CAAE,CAAE,CAAA7D,QAAA,CAEL,CAAC,MAAM,CAAE,KAAK,CAAC,CAACkC,GAAG,CAAEC,IAAI,EAAK,CAC9B;AACA;AACA,KAAM,CAAAiC,YAAY,CAAGpN,eAAe,CAACkJ,MAAM,CAACmE,IAAI,CAAEC,GAAG,EAAKA,GAAG,CAACrH,EAAE,GAAK3F,aAAa,CAAC6G,QAAQ,CAAC,CAC5F,KAAM,CAAAoG,gBAAgB,CAAG,CAAAH,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE7G,SAAS,GAAI3H,cAAc,CAElE;AACA,KAAM,CAAA4O,UAAU,CAAIrC,IAAI,GAAK,MAAM,EAAIoC,gBAAgB,GAAK,OAAO,EAC5DpC,IAAI,GAAK,KAAK,EAAIoC,gBAAgB,GAAK,SAAU,CACxD,mBACCvO,IAAA,CAAC1B,MAAM,EAEN8L,OAAO,CAAEA,CAAA,GAAMnI,SAAS,CAACjB,eAAe,CAACiG,EAAE,CAAEkF,IAAsB,CAAE,CACrEvB,OAAO,CAAC,UAAU,CAClBwC,IAAI,CAAC,OAAO,CACZnD,EAAE,CAAE,CACH9E,KAAK,CAAE,QAAQ,CACfvB,MAAM,CAAE,MAAM,CACd4B,OAAO,CAAE,WAAW,CACpBM,GAAG,CAAE,MAAM,CACXF,YAAY,CAAE,iBAAiB,CAC/B6I,MAAM,CACLD,UAAU,CACP,iCAAiC,CACjC,kCAAkC,CACtClH,eAAe,CACdkH,UAAU,CAAG,yBAAyB,CAAG,0BAA0B,CACpEE,mBAAmB,CAAE,UAAU,CAC/B9E,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,CACVtC,eAAe,CACdkH,UAAU,CAAG,yBAAyB,CAAG,0BAC3C,CACD,CAAE,CAAAxE,QAAA,CAED7I,SAAS,CAACgL,IAAI,CAAE,CAAEnB,YAAY,CAAEmB,IAAK,CAAC,CAAC,EAxBnCA,IAyBE,CAAC,CAEX,CAAC,CAAC,CACE,CAAC,EACF,CAAC,EACF,CAAC,CACE,CAAC,EACN,CAAC,CACE,CAAC,cACVnM,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEjK,SAAS,CAAC,kBAAkB,CAAE,CAAE6J,YAAY,CAAE,kBAAmB,CAAC,CAAE,CAAAhB,QAAA,cACnFhK,IAAA,CAAChC,GAAG,EAACmP,SAAS,CAAC,kBAAkB,CAAAnD,QAAA,cAChChK,IAAA,CAAC7B,UAAU,EACViM,OAAO,CAAEN,0BAA2B,CACpCsD,IAAI,CAAC,OAAO,CACZnD,EAAE,CAAE,CACHrG,MAAM,CAAE,MAAM,CACduB,KAAK,CAAE,MAAM,CACbmC,eAAe,CAAEtG,eAAe,CAACoE,KAAK,CAACkC,eAAe,CACtDmH,MAAM,CAAE,iBAAiB,CACzB1I,SAAS,CAAE,MACZ,CAAE,CAYS,CAAC,CACT,CAAC,CACE,CAAC,cACV/F,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEnK,eAAe,CAAGE,SAAS,CAAC,2CAA2C,CAAE,CAAE6J,YAAY,CAAE,2CAA4C,CAAC,CAAC,CAAG7J,SAAS,CAAC,eAAe,CAAE,CAAE6J,YAAY,CAAE,eAAgB,CAAC,CAAE,CAAAhB,QAAA,cACvNhK,IAAA,CAAChC,GAAG,EAACmP,SAAS,CAAC,kBAAkB,CAAAnD,QAAA,cAChChK,IAAA,CAAC7B,UAAU,EACViM,OAAO,CAAEX,uBAAwB,CACjC2D,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAEpM,eAAgB,CAAA+I,QAAA,cAE1BhK,IAAA,SACC0K,uBAAuB,CAAE,CAAEC,MAAM,CAAExL,QAAS,CAAE,CAC9CiG,KAAK,CAAE,CAAEmG,OAAO,CAAEtK,eAAe,CAAG,GAAG,CAAG,CAAE,CAAE,CAC9C,CAAC,CACS,CAAC,CAET,CAAC,CACE,CAAC,cACVjB,IAAA,CAACzB,OAAO,EAAC6M,KAAK,CAAEjK,SAAS,CAAC,gBAAgB,CAAE,CAAE6J,YAAY,CAAE,gBAAiB,CAAC,CAAE,CAAAhB,QAAA,cAC/EhK,IAAA,CAAChC,GAAG,EAACmP,SAAS,CAAC,kBAAkB,CAAAnD,QAAA,cAChChK,IAAA,CAAC7B,UAAU,EACViM,OAAO,CAAEhB,mBAAoB,CAC7BgE,IAAI,CAAC,OAAO,CACZC,QAAQ,CACP,EAAA1M,gBAAA,CAAAnB,cAAc,CAAE6C,KAAK,EAAKA,KAAK,CAACsM,oBAAoB,CAAC,CAAC,CAAC,CAAC,UAAAhO,gBAAA,kBAAAC,qBAAA,CAAxDD,gBAAA,CAA0DiO,UAAU,UAAAhO,qBAAA,iBAApEA,qBAAA,CAAsE+F,MAAM,IAAK,CACjF,CAAAqD,QAAA,cAEDhK,IAAA,SACC0K,uBAAuB,CAAE,CAAEC,MAAM,CAAEvL,UAAW,CAAE,CAChDgG,KAAK,CAAE,CACNmG,OAAO,CACN,EAAA1K,iBAAA,CAAArB,cAAc,CAAE6C,KAAK,EAAKA,KAAK,CAACsM,oBAAoB,CAAC,CAAC,CAAC,CAAC,UAAA9N,iBAAA,kBAAAC,qBAAA,CAAxDD,iBAAA,CAA0D+N,UAAU,UAAA9N,qBAAA,iBAApEA,qBAAA,CAAsE6F,MAAM,IAAK,CAAC,CAC/E,GAAG,CACH,CAAC,CACL0E,aAAa,CAAE,MAChB,CACA;AAAA,CACA,CAAC,CACS,CAAC,CACT,CAAC,CACE,CAAC,EACN,CAAC,CACE,CAAC,cACVrL,IAAA,CAAC9B,OAAO,EACP+I,EAAE,CAAC,eAAe,CAClBoB,IAAI,CAAEC,eAAgB,CACtBgF,QAAQ,CAAE/J,mBAAoB,CAC9BsI,OAAO,CAAEnC,sBAAuB,CAChCoC,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFiB,eAAe,CAAE,CAChBlB,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CACFwB,SAAS,CAAE,CACVC,IAAI,CAAE,CACLxD,EAAE,CAAE,CACH;AACAyD,MAAM,CAAE,MACT,CACD,CACD,CAAE,CAAA1D,QAAA,cAEF9J,KAAA,CAAClC,GAAG,EAAAgM,QAAA,eACHhK,IAAA,CAACF,YAAY,EACZ8J,KAAK,CAAE5I,eAAe,CAACoE,KAAK,CAACkC,eAAgB,CAC7C2D,QAAQ,CAAEtB,iBAAkB,CAC5B,CAAC,cACF3J,IAAA,UAAAgK,QAAA,CACE;AACP;AACA;AACA;AACA,KAAK,CACO,CAAC,EACJ,CAAC,CACE,CAAC,CACT7F,WAAW,eACXnE,IAAA,CAACpB,0BAA0B,EAC1BwE,MAAM,CAAEe,WAAY,CACpB0K,gBAAgB,CAAEA,CAAA,GAAMzK,YAAY,CAAC,KAAK,CAAE,CAC5C0K,aAAa,CAAErH,wBAAyB,CACxC,CACD,EACA,CAAC,CAEL,CAAC,CAED,cAAe,CAAApH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}