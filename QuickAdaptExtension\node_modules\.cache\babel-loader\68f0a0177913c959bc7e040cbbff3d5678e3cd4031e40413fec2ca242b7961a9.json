{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{useTranslation}from\"react-i18next\";import{Box,Typography,TextField,IconButton,Button}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import{warning}from\"../../assets/icons/icons\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TitleSubTitle=_ref=>{let{currentGuide}=_ref;const{t:translate}=useTranslation();const{titlePopup,setTitlePopup,setDesignPopup,titleColor,setTitleColor,checklistTitle,setChecklistTitle,checklistSubTitle,setChecklistSubTitle,checklistGuideMetaData,updateChecklistTitleSubTitle,setIsUnSavedChanges,isUnSavedChanges}=useDrawerStore(state=>state);const[titleError,setTitleError]=useState(\"\");// Store error message\nconst[subtitleError,setsubTitleError]=useState(\"\");// Store error message\nconst[isDisabled,setIsDisabled]=useState(true);const[hasChanges,setHasChanges]=useState(false);// Track if any changes were made\nfunction getLocalizedDefaults(t){var _existing$titleBold,_existing$titleItalic,_existing$subTitleBol,_existing$subTitleIta;let existing=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const defaultTitle=\"Checklist Title\";const defaultSubTitle=\"Context about the tasks in the checklist below users should prioritize completing.\";return{...existing,title:(existing===null||existing===void 0?void 0:existing.title)===defaultTitle||!(existing!==null&&existing!==void 0&&existing.title)?t(defaultTitle,{defaultValue:defaultTitle}):existing===null||existing===void 0?void 0:existing.title,subTitle:(existing===null||existing===void 0?void 0:existing.subTitle)===defaultSubTitle||!(existing!==null&&existing!==void 0&&existing.subTitle)?t(defaultSubTitle,{defaultValue:defaultSubTitle}):existing===null||existing===void 0?void 0:existing.subTitle,titleColor:(existing===null||existing===void 0?void 0:existing.titleColor)||\"#333\",titleBold:(_existing$titleBold=existing===null||existing===void 0?void 0:existing.titleBold)!==null&&_existing$titleBold!==void 0?_existing$titleBold:true,titleItalic:(_existing$titleItalic=existing===null||existing===void 0?void 0:existing.titleItalic)!==null&&_existing$titleItalic!==void 0?_existing$titleItalic:false,subTitleColor:(existing===null||existing===void 0?void 0:existing.subTitleColor)||\"#8D8D8D\",subTitleBold:(_existing$subTitleBol=existing===null||existing===void 0?void 0:existing.subTitleBold)!==null&&_existing$subTitleBol!==void 0?_existing$subTitleBol:false,subTitleItalic:(_existing$subTitleIta=existing===null||existing===void 0?void 0:existing.subTitleItalic)!==null&&_existing$subTitleIta!==void 0?_existing$subTitleIta:false};}// Function to check if the Apply button should be enabled\nconst updateApplyButtonState=(titleErr,subtitleErr,changed)=>{// Enable the button if there are no errors AND changes have been made\nsetIsDisabled(!!titleErr||!!subtitleErr||!changed);};const handleTitleChange=e=>{const value=e.target.value;let errorMessage=\"\";if(value.length<5){errorMessage=translate(\"Min: 5 Characters\");}else if(value.length>50){errorMessage=translate(\"Max: 50 Characters\");}setTitleError(errorMessage);setHasChanges(true);// Mark that changes have been made\nupdateApplyButtonState(errorMessage,subtitleError,true);onPropertyChange(\"title\",value);};const handleSubTitleChange=e=>{const value=e.target.value;let errorMessage=\"\";if(value.length<5){errorMessage=translate(\"Min: 5 Characters\");}else if(value.length>500){errorMessage=translate(\"Max: 500 Characters\");}setsubTitleError(errorMessage);setHasChanges(true);// Mark that changes have been made\nupdateApplyButtonState(titleError,errorMessage,true);onPropertyChange(\"subTitle\",value);};const[titleSubTitleProperties,setTitleSubTitleProperties]=useState(()=>{var _checklistGuideMetaDa;return getLocalizedDefaults(translate,(_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:_checklistGuideMetaDa.TitleSubTitle);});const[tempTitle,setTempTitle]=useState(titleSubTitleProperties===null||titleSubTitleProperties===void 0?void 0:titleSubTitleProperties.title);const[tempSubTitle,settempTempTitle]=useState(titleSubTitleProperties===null||titleSubTitleProperties===void 0?void 0:titleSubTitleProperties.subTitle);// Store the initial state to compare for changes\nconst[initialState,setInitialState]=useState(titleSubTitleProperties);// Effect to check for any changes compared to initial state\nuseEffect(()=>{// Compare current properties with initial state\nconst hasAnyChanges=Object.keys(titleSubTitleProperties).some(key=>titleSubTitleProperties[key]!==initialState[key]);setHasChanges(hasAnyChanges);updateApplyButtonState(titleError,subtitleError,hasAnyChanges);},[titleSubTitleProperties,initialState,titleError,subtitleError]);const handleTitleColorChange=e=>setTitleColor(e.target.value);const onPropertyChange=(key,value)=>{setTitleSubTitleProperties(prevState=>{// Check if the value has actually changed\nif(prevState[key]!==value){setHasChanges(true);updateApplyButtonState(titleError,subtitleError,true);}return{...prevState,[key]:value};});};const handleApplyChanges=()=>{updateChecklistTitleSubTitle(titleSubTitleProperties);// Update the initial state to the current state after applying changes\nsetInitialState({...titleSubTitleProperties});// Reset the changes flag\nsetHasChanges(false);// Disable the Apply button\nsetIsDisabled(true);// Close the popup\nsetTitlePopup(false);// Mark changes as unsaved\nsetIsUnSavedChanges(true);};const handleClose=()=>{setTitlePopup(false);};const handledesignclose=()=>{setDesignPopup(false);};const handleBlur=()=>{// If the title is empty, restore the previous title\nif(titleSubTitleProperties.title.trim()===\"\"){setTitleSubTitleProperties(prevState=>({...prevState,title:tempTitle// Reset to the default value\n}));}if(titleSubTitleProperties.subTitle.trim()===\"\"){setTitleSubTitleProperties(prevState=>({...prevState,subTitle:tempSubTitle// Reset to the default value\n}));}};return/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"back\",onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Title & SubTitle\",{defaultValue:\"Title & SubTitle\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls qadpt-errmsg\",children:[/*#__PURE__*/_jsxs(Box,{sx:{backgroundColor:\"#EAE2E2\",borderRadius:\"var(--button-border-radius)\",padding:\"8px\",marginBottom:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{fontWeight:\"600\",paddingBottom:\"5px\",textAlign:\"left\"},children:translate(\"Title\",{defaultValue:\"Title\"})}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-control-box\",sx:{padding:\"0 !important\",height:\"auto !important\"},children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Checklist Title\",{defaultValue:\"Checklist Title\"}),className:\"qadpt-control-input\",value:titleSubTitleProperties.title,style:{width:\"100%\"},onChange:handleTitleChange,error:Boolean(titleError)// Show error if message exists\n,helperText:titleError?/*#__PURE__*/_jsxs(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(titleError,{defaultValue:titleError})]}):null,InputProps:{sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\",marginBottom:\"5px\"}}}})}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{backgroundColor:\"#E5DADA !important\",height:\"35px !important\",borderRadius:\"6px !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Title Color\",{defaultValue:\"Title Color\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:titleSubTitleProperties.titleColor,onChange:e=>onPropertyChange(\"titleColor\",e.target.value),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{backgroundColor:\"#E5DADA !important\",height:\"35px !important\",borderRadius:\"6px !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Bold\",{defaultValue:\"Bold\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch qadpt-toggle-group\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:titleSubTitleProperties.titleBold,onChange:e=>onPropertyChange(\"titleBold\",e.target.checked),name:\"toggleSwitch\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{backgroundColor:\"#E5DADA !important\",height:\"35px !important\",borderRadius:\"6px !important\"},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-control-label\",children:[translate(\"Italic\",{defaultValue:\"Italic\"}),\" \"]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch qadpt-toggle-group\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:titleSubTitleProperties.titleItalic,onChange:e=>onPropertyChange(\"titleItalic\",e.target.checked),name:\"toggleSwitch\"//disabled={(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && status}\n}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{backgroundColor:\"#EAE2E2\",borderRadius:\"var(--button-border-radius)\",height:\"auto\",padding:\"8px\",marginBottom:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{fontWeight:\"600\",paddingBottom:\"5px\",textAlign:\"left\"},children:translate(\"Sub Title\",{defaultValue:\"Sub Title\"})}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-control-box\",sx:{padding:\"0 !important\",height:\"auto !important\"},children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate(\"Checklist Title\",{defaultValue:\"Checklist Title\"}),className:\"qadpt-control-input\",value:titleSubTitleProperties.subTitle,style:{width:\"100%\"},onChange:handleSubTitleChange,error:Boolean(subtitleError)// Show error if message exists\n,helperText:subtitleError?/*#__PURE__*/_jsxs(\"span\",{style:{display:\"flex\",fontSize:\"12px\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"4px\"},dangerouslySetInnerHTML:{__html:warning}}),translate(subtitleError,{defaultValue:subtitleError})]}):null,InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\",marginBottom:\"5px\"}}}})}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{backgroundColor:\"#E5DADA !important\",height:\"35px !important\",borderRadius:\"6px !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"SubTitle Color\",{defaultValue:\"SubTitle Color\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:titleSubTitleProperties.subTitleColor,onChange:e=>onPropertyChange(\"subTitleColor\",e.target.value),className:\"qadpt-color-input\"})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{backgroundColor:\"#E5DADA !important\",height:\"35px !important\",borderRadius:\"6px !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate(\"Bold\",{defaultValue:\"Bold\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch \",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:titleSubTitleProperties.subTitleBold,onChange:e=>onPropertyChange(\"subTitleBold\",e.target.checked),name:\"toggleSwitch\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{backgroundColor:\"#E5DADA !important\",height:\"35px !important\",borderRadius:\"6px !important\"},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-control-label\",children:[translate(\"Italic\",{defaultValue:\"Italic\"}),\" \"]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch qadpt-toggle-group\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:titleSubTitleProperties.subTitleItalic,onChange:e=>onPropertyChange(\"subTitleItalic\",e.target.checked),name:\"toggleSwitch\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn ${isDisabled?\"disabled\":\"\"}`,disabled:isDisabled,children:translate(\"Apply\",{defaultValue:\"Apply\"})})})]})});};export default TitleSubTitle;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "CloseIcon", "useDrawerStore", "warning", "ArrowBackIosNewOutlinedIcon", "jsx", "_jsx", "jsxs", "_jsxs", "TitleSubTitle", "_ref", "currentGuide", "t", "translate", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checklistTitle", "setChecklistTitle", "checklistSubTitle", "setChecklistSubTitle", "checklistGuideMetaData", "updateChecklistTitleSubTitle", "setIsUnSavedChanges", "isUnSavedChanges", "state", "titleError", "setTitleError", "subtitleError", "setsubTitleError", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "getLocalizedDefaults", "_existing$titleBold", "_existing$titleItalic", "_existing$subTitleBol", "_existing$subTitleIta", "existing", "arguments", "length", "undefined", "defaultTitle", "defaultSubTitle", "title", "defaultValue", "subTitle", "titleBold", "titleItalic", "subTitleColor", "subTitleBold", "subTitleItalic", "updateApplyButtonState", "titleErr", "subtitleErr", "changed", "handleTitleChange", "e", "value", "target", "errorMessage", "onPropertyChange", "handleSubTitleChange", "titleSubTitleProperties", "setTitleSubTitleProperties", "_checklistGuideMetaDa", "tempTitle", "setTempTitle", "tempSubTitle", "settempTempTitle", "initialState", "setInitialState", "hasAnyChanges", "Object", "keys", "some", "key", "handleTitleColorChange", "prevState", "handleApplyChanges", "handleClose", "handledesignclose", "handleBlur", "trim", "id", "className", "children", "onClick", "size", "sx", "backgroundColor", "borderRadius", "padding", "marginBottom", "fontWeight", "paddingBottom", "textAlign", "height", "variant", "placeholder", "style", "width", "onChange", "error", "Boolean", "helperText", "display", "fontSize", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "InputProps", "border", "paddingLeft", "type", "checked", "name", "endAdornment", "disabled"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/checklist/TitleSubTitle.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect } from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n  Solid,\r\n  warning,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\n\r\nconst TitleSubTitle = ({ currentGuide }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n    const {\r\n\t\t\ttitlePopup,\r\n\t\t\tsetTitlePopup,\r\n\t\t\tsetDesignPopup,\r\n\t\t\ttitleColor,\r\n\t\t\tsetTitleColor,\r\n\t\t\tchecklistTitle,\r\n\t\t\tsetChecklistTitle,\r\n\t\t\tchecklistSubTitle,\r\n\t\t\tsetChecklistSubTitle,\r\n\t\t\tchecklistGuideMetaData,\r\n\t\t\tupdateChecklistTitleSubTitle,\r\n\t\t\tsetIsUnSavedChanges,\r\n\t\t\tisUnSavedChanges,\r\n\t\t} = useDrawerStore((state: any) => state);\r\n\t\tconst [titleError, setTitleError] = useState(\"\"); // Store error message\r\n\t\tconst [subtitleError, setsubTitleError] = useState(\"\"); // Store error message\r\n\t\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\t\tconst [hasChanges, setHasChanges] = useState(false); // Track if any changes were made\r\n\r\n\t\r\nfunction getLocalizedDefaults(t: any, existing: any = {}): any {\r\n  const defaultTitle = \"Checklist Title\";\r\n  const defaultSubTitle = \"Context about the tasks in the checklist below users should prioritize completing.\";\r\n\r\n  return {\r\n    ...existing,\r\n    title:\r\n      existing?.title === defaultTitle || !existing?.title\r\n        ? t(defaultTitle, { defaultValue: defaultTitle })\r\n        : existing?.title,\r\n    subTitle:\r\n      existing?.subTitle === defaultSubTitle || !existing?.subTitle\r\n        ? t(defaultSubTitle, { defaultValue: defaultSubTitle })\r\n        : existing?.subTitle,\r\n    titleColor: existing?.titleColor || \"#333\",\r\n    titleBold: existing?.titleBold ?? true,\r\n    titleItalic: existing?.titleItalic ?? false,\r\n    subTitleColor: existing?.subTitleColor || \"#8D8D8D\",\r\n    subTitleBold: existing?.subTitleBold ?? false,\r\n    subTitleItalic: existing?.subTitleItalic ?? false,\r\n  };\r\n}\r\n\t\r\n\t\t// Function to check if the Apply button should be enabled\r\n\t\tconst updateApplyButtonState = (titleErr: string, subtitleErr: string, changed: boolean) => {\r\n\t\t\t// Enable the button if there are no errors AND changes have been made\r\n\t\t\tsetIsDisabled(!!titleErr || !!subtitleErr || !changed);\r\n\t\t};\r\n\r\n\t\tconst handleTitleChange = (e: any) => {\r\n\t\t\tconst value = e.target.value;\r\n\t\t\tlet errorMessage = \"\";\r\n\r\n\t\t\tif (value.length < 5) {\r\n\t\t\t\terrorMessage = translate(\"Min: 5 Characters\");\r\n\t\t\t} else if (value.length > 50) {\r\n\t\t\t\terrorMessage = translate(\"Max: 50 Characters\");\r\n\t\t\t}\r\n\r\n\t\t\tsetTitleError(errorMessage);\r\n\t\t\tsetHasChanges(true); // Mark that changes have been made\r\n\t\t\tupdateApplyButtonState(errorMessage, subtitleError, true);\r\n\t\t\tonPropertyChange(\"title\", value);\r\n\t\t};\r\n\r\n\t\tconst handleSubTitleChange = (e: any) => {\r\n\t\t\tconst value = e.target.value;\r\n\t\t\tlet errorMessage = \"\";\r\n\r\n\t\t\tif (value.length < 5) {\r\n\t\t\t\terrorMessage = translate(\"Min: 5 Characters\");\r\n\t\t\t} else if (value.length > 500) {\r\n\t\t\t\terrorMessage = translate(\"Max: 500 Characters\");\r\n\t\t\t}\r\n\r\n\t\t\tsetsubTitleError(errorMessage);\r\n\t\t\tsetHasChanges(true); // Mark that changes have been made\r\n\t\t\tupdateApplyButtonState(titleError, errorMessage, true);\r\n\t\t\tonPropertyChange(\"subTitle\", value);\r\n\t\t};\r\n\r\n\t\tconst [titleSubTitleProperties, setTitleSubTitleProperties] = useState(() =>\r\n\t\t\tgetLocalizedDefaults(translate, checklistGuideMetaData[0]?.TitleSubTitle)\r\n);\r\n\r\n\t\tconst [tempTitle, setTempTitle] = useState(titleSubTitleProperties?.title);\r\n\t\tconst [tempSubTitle, settempTempTitle] = useState(titleSubTitleProperties?.subTitle);\r\n\r\n\t\t// Store the initial state to compare for changes\r\n\t\tconst [initialState, setInitialState] = useState(titleSubTitleProperties);\r\n\r\n\t\t// Effect to check for any changes compared to initial state\r\n\t\tuseEffect(() => {\r\n\t\t\t// Compare current properties with initial state\r\n\t\t\tconst hasAnyChanges = Object.keys(titleSubTitleProperties).some(\r\n\t\t\t\t(key) => titleSubTitleProperties[key] !== initialState[key]\r\n\t\t\t);\r\n\r\n\t\t\tsetHasChanges(hasAnyChanges);\r\n\t\t\tupdateApplyButtonState(titleError, subtitleError, hasAnyChanges);\r\n\t\t}, [titleSubTitleProperties, initialState, titleError, subtitleError]);\r\n\t\tconst handleTitleColorChange = (e: any) => setTitleColor(e.target.value);\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetTitleSubTitleProperties((prevState: any) => {\r\n\t\t\t\t// Check if the value has actually changed\r\n\t\t\t\tif (prevState[key] !== value) {\r\n\t\t\t\t\tsetHasChanges(true);\r\n\t\t\t\t\tupdateApplyButtonState(titleError, subtitleError, true);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn {\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\t[key]: value,\r\n\t\t\t\t};\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tconst handleApplyChanges = () => {\r\n\t\t\tupdateChecklistTitleSubTitle(titleSubTitleProperties);\r\n\t\t\t// Update the initial state to the current state after applying changes\r\n\t\t\tsetInitialState({ ...titleSubTitleProperties });\r\n\t\t\t// Reset the changes flag\r\n\t\t\tsetHasChanges(false);\r\n\t\t\t// Disable the Apply button\r\n\t\t\tsetIsDisabled(true);\r\n\t\t\t// Close the popup\r\n\t\t\tsetTitlePopup(false);\r\n\t\t\t// Mark changes as unsaved\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetTitlePopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleBlur = () => {\r\n\t\t\t// If the title is empty, restore the previous title\r\n\t\t\tif (titleSubTitleProperties.title.trim() === \"\") {\r\n\t\t\t\tsetTitleSubTitleProperties((prevState: any) => ({\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\ttitle: tempTitle, // Reset to the default value\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t\tif (titleSubTitleProperties.subTitle.trim() === \"\") {\r\n\t\t\t\tsetTitleSubTitleProperties((prevState: any) => ({\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\tsubTitle: tempSubTitle, // Reset to the default value\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Title & SubTitle\", { defaultValue: \"Title & SubTitle\" })}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontWeight: \"600\", paddingBottom: \"5px\", textAlign: \"left\" }}>{translate(\"Title\", { defaultValue: \"Title\" })}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Checklist Title\", { defaultValue: \"Checklist Title\" })}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.title}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleTitleChange}\r\n\t\t\t\t\t\t\t\t\t\terror={Boolean(titleError)} // Show error if message exists\r\n\t\t\t\t\t\t\t\t\t\thelperText={\r\n\t\t\t\t\t\t\t\t\t\t\ttitleError ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(titleError, { defaultValue: titleError })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\", marginBottom: \"5px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Title Color\", { defaultValue: \"Title Color\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.titleColor}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"titleColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Bold\", { defaultValue: \"Bold\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.titleBold}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"titleBold\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Italic\", { defaultValue: \"Italic\" })} </div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.titleItalic}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"titleItalic\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//disabled={(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && status}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography sx={{ fontWeight: \"600\", paddingBottom: \"5px\", textAlign: \"left\" }}>{translate(\"Sub Title\", { defaultValue: \"Sub Title\" })}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Checklist Title\", { defaultValue: \"Checklist Title\" })}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.subTitle}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleSubTitleChange}\r\n\t\t\t\t\t\t\t\t\t\terror={Boolean(subtitleError)} // Show error if message exists\r\n\t\t\t\t\t\t\t\t\t\thelperText={\r\n\t\t\t\t\t\t\t\t\t\t\tsubtitleError ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(subtitleError, { defaultValue: subtitleError })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t) : null\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\", marginBottom: \"5px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"SubTitle Color\", { defaultValue: \"SubTitle Color\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={titleSubTitleProperties.subTitleColor}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"subTitleColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Bold\", { defaultValue: \"Bold\" })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch \">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.subTitleBold}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"subTitleBold\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#E5DADA !important\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"35px !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Italic\", { defaultValue: \"Italic\" })} </div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch qadpt-toggle-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tchecked={titleSubTitleProperties.subTitleItalic}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"subTitleItalic\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"toggleSwitch\"\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\", { defaultValue: \"Apply\" })}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n};\r\n\r\nexport default TitleSubTitle;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAgBC,QAAQ,CAACC,SAAS,KAAQ,OAAO,CAC7D,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAEC,MAAM,KAAgH,eAAe,CAC5L,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAA6E,yBAAyB,CAE3H,OAKEC,OAAO,KACF,0BAA0B,CACjC,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtF,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,YAAkB,CAAC,CAAAD,IAAA,CAC3C,KAAM,CAAEE,CAAC,CAAEC,SAAU,CAAC,CAAGlB,cAAc,CAAC,CAAC,CACtC,KAAM,CACPmB,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,iBAAiB,CACjBC,iBAAiB,CACjBC,oBAAoB,CACpBC,sBAAsB,CACtBC,4BAA4B,CAC5BC,mBAAmB,CACnBC,gBACD,CAAC,CAAGxB,cAAc,CAAEyB,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAAE;AAClD,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAAE;AACxD,KAAM,CAACuC,UAAU,CAAEC,aAAa,CAAC,CAAGxC,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACyC,UAAU,CAAEC,aAAa,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CAAE;AAGvD,QAAS,CAAA2C,oBAAoBA,CAACxB,CAAM,CAA2B,KAAAyB,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,IAAzB,CAAAC,QAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACtD,KAAM,CAAAG,YAAY,CAAG,iBAAiB,CACtC,KAAM,CAAAC,eAAe,CAAG,oFAAoF,CAE5G,MAAO,CACL,GAAGL,QAAQ,CACXM,KAAK,CACH,CAAAN,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEM,KAAK,IAAKF,YAAY,EAAI,EAACJ,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEM,KAAK,EAChDnC,CAAC,CAACiC,YAAY,CAAE,CAAEG,YAAY,CAAEH,YAAa,CAAC,CAAC,CAC/CJ,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEM,KAAK,CACrBE,QAAQ,CACN,CAAAR,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEQ,QAAQ,IAAKH,eAAe,EAAI,EAACL,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEQ,QAAQ,EACzDrC,CAAC,CAACkC,eAAe,CAAE,CAAEE,YAAY,CAAEF,eAAgB,CAAC,CAAC,CACrDL,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEQ,QAAQ,CACxBhC,UAAU,CAAE,CAAAwB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAExB,UAAU,GAAI,MAAM,CAC1CiC,SAAS,EAAAb,mBAAA,CAAEI,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAES,SAAS,UAAAb,mBAAA,UAAAA,mBAAA,CAAI,IAAI,CACtCc,WAAW,EAAAb,qBAAA,CAAEG,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEU,WAAW,UAAAb,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC3Cc,aAAa,CAAE,CAAAX,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEW,aAAa,GAAI,SAAS,CACnDC,YAAY,EAAAd,qBAAA,CAAEE,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEY,YAAY,UAAAd,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC7Ce,cAAc,EAAAd,qBAAA,CAAEC,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEa,cAAc,UAAAd,qBAAA,UAAAA,qBAAA,CAAI,KAC9C,CAAC,CACH,CAEE;AACA,KAAM,CAAAe,sBAAsB,CAAGA,CAACC,QAAgB,CAAEC,WAAmB,CAAEC,OAAgB,GAAK,CAC3F;AACAzB,aAAa,CAAC,CAAC,CAACuB,QAAQ,EAAI,CAAC,CAACC,WAAW,EAAI,CAACC,OAAO,CAAC,CACvD,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAIC,CAAM,EAAK,CACrC,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5B,GAAI,CAAAE,YAAY,CAAG,EAAE,CAErB,GAAIF,KAAK,CAAClB,MAAM,CAAG,CAAC,CAAE,CACrBoB,YAAY,CAAGlD,SAAS,CAAC,mBAAmB,CAAC,CAC9C,CAAC,IAAM,IAAIgD,KAAK,CAAClB,MAAM,CAAG,EAAE,CAAE,CAC7BoB,YAAY,CAAGlD,SAAS,CAAC,oBAAoB,CAAC,CAC/C,CAEAgB,aAAa,CAACkC,YAAY,CAAC,CAC3B5B,aAAa,CAAC,IAAI,CAAC,CAAE;AACrBoB,sBAAsB,CAACQ,YAAY,CAAEjC,aAAa,CAAE,IAAI,CAAC,CACzDkC,gBAAgB,CAAC,OAAO,CAAEH,KAAK,CAAC,CACjC,CAAC,CAED,KAAM,CAAAI,oBAAoB,CAAIL,CAAM,EAAK,CACxC,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5B,GAAI,CAAAE,YAAY,CAAG,EAAE,CAErB,GAAIF,KAAK,CAAClB,MAAM,CAAG,CAAC,CAAE,CACrBoB,YAAY,CAAGlD,SAAS,CAAC,mBAAmB,CAAC,CAC9C,CAAC,IAAM,IAAIgD,KAAK,CAAClB,MAAM,CAAG,GAAG,CAAE,CAC9BoB,YAAY,CAAGlD,SAAS,CAAC,qBAAqB,CAAC,CAChD,CAEAkB,gBAAgB,CAACgC,YAAY,CAAC,CAC9B5B,aAAa,CAAC,IAAI,CAAC,CAAE;AACrBoB,sBAAsB,CAAC3B,UAAU,CAAEmC,YAAY,CAAE,IAAI,CAAC,CACtDC,gBAAgB,CAAC,UAAU,CAAEH,KAAK,CAAC,CACpC,CAAC,CAED,KAAM,CAACK,uBAAuB,CAAEC,0BAA0B,CAAC,CAAG1E,QAAQ,CAAC,SAAA2E,qBAAA,OACtE,CAAAhC,oBAAoB,CAACvB,SAAS,EAAAuD,qBAAA,CAAE7C,sBAAsB,CAAC,CAAC,CAAC,UAAA6C,qBAAA,iBAAzBA,qBAAA,CAA2B3D,aAAa,CAAC,EAC5E,CAAC,CAEC,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7E,QAAQ,CAACyE,uBAAuB,SAAvBA,uBAAuB,iBAAvBA,uBAAuB,CAAEnB,KAAK,CAAC,CAC1E,KAAM,CAACwB,YAAY,CAAEC,gBAAgB,CAAC,CAAG/E,QAAQ,CAACyE,uBAAuB,SAAvBA,uBAAuB,iBAAvBA,uBAAuB,CAAEjB,QAAQ,CAAC,CAEpF;AACA,KAAM,CAACwB,YAAY,CAAEC,eAAe,CAAC,CAAGjF,QAAQ,CAACyE,uBAAuB,CAAC,CAEzE;AACAxE,SAAS,CAAC,IAAM,CACf;AACA,KAAM,CAAAiF,aAAa,CAAGC,MAAM,CAACC,IAAI,CAACX,uBAAuB,CAAC,CAACY,IAAI,CAC7DC,GAAG,EAAKb,uBAAuB,CAACa,GAAG,CAAC,GAAKN,YAAY,CAACM,GAAG,CAC3D,CAAC,CAED5C,aAAa,CAACwC,aAAa,CAAC,CAC5BpB,sBAAsB,CAAC3B,UAAU,CAAEE,aAAa,CAAE6C,aAAa,CAAC,CACjE,CAAC,CAAE,CAACT,uBAAuB,CAAEO,YAAY,CAAE7C,UAAU,CAAEE,aAAa,CAAC,CAAC,CACtE,KAAM,CAAAkD,sBAAsB,CAAIpB,CAAM,EAAK1C,aAAa,CAAC0C,CAAC,CAACE,MAAM,CAACD,KAAK,CAAC,CAExE,KAAM,CAAAG,gBAAgB,CAAGA,CAACe,GAAQ,CAAElB,KAAU,GAAK,CAClDM,0BAA0B,CAAEc,SAAc,EAAK,CAC9C;AACA,GAAIA,SAAS,CAACF,GAAG,CAAC,GAAKlB,KAAK,CAAE,CAC7B1B,aAAa,CAAC,IAAI,CAAC,CACnBoB,sBAAsB,CAAC3B,UAAU,CAAEE,aAAa,CAAE,IAAI,CAAC,CACxD,CAEA,MAAO,CACN,GAAGmD,SAAS,CACZ,CAACF,GAAG,EAAGlB,KACR,CAAC,CACF,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAqB,kBAAkB,CAAGA,CAAA,GAAM,CAChC1D,4BAA4B,CAAC0C,uBAAuB,CAAC,CACrD;AACAQ,eAAe,CAAC,CAAE,GAAGR,uBAAwB,CAAC,CAAC,CAC/C;AACA/B,aAAa,CAAC,KAAK,CAAC,CACpB;AACAF,aAAa,CAAC,IAAI,CAAC,CACnB;AACAlB,aAAa,CAAC,KAAK,CAAC,CACpB;AACAU,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAA0D,WAAW,CAAGA,CAAA,GAAM,CACzBpE,aAAa,CAAC,KAAK,CAAC,CACrB,CAAC,CACD,KAAM,CAAAqE,iBAAiB,CAAGA,CAAA,GAAM,CAC/BpE,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CACD,KAAM,CAAAqE,UAAU,CAAGA,CAAA,GAAM,CACxB;AACA,GAAInB,uBAAuB,CAACnB,KAAK,CAACuC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAChDnB,0BAA0B,CAAEc,SAAc,GAAM,CAC/C,GAAGA,SAAS,CACZlC,KAAK,CAAEsB,SAAW;AACnB,CAAC,CAAC,CAAC,CACJ,CACA,GAAIH,uBAAuB,CAACjB,QAAQ,CAACqC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACnDnB,0BAA0B,CAAEc,SAAc,GAAM,CAC/C,GAAGA,SAAS,CACZhC,QAAQ,CAAEsB,YAAc;AACzB,CAAC,CAAC,CAAC,CACJ,CACD,CAAC,CAED,mBACCjE,IAAA,QACCiF,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BjF,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BjF,KAAA,QAAKgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnCnF,IAAA,CAACP,UAAU,EACV,aAAW,MAAM,CACjB2F,OAAO,CAAEP,WAAY,CAAAM,QAAA,cAErBnF,IAAA,CAACF,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbE,IAAA,QAAKkF,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE5E,SAAS,CAAC,kBAAkB,CAAE,CAAEmC,YAAY,CAAE,kBAAmB,CAAC,CAAC,CAAM,CAAC,cACxG1C,IAAA,CAACP,UAAU,EACV4F,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBD,OAAO,CAAEP,WAAY,CAAAM,QAAA,cAErBnF,IAAA,CAACL,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNK,IAAA,QAAKkF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC9BjF,KAAA,QAAKgF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC3CjF,KAAA,CAACZ,GAAG,EACHgG,EAAE,CAAE,CACHC,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,6BAA6B,CAC3CC,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,KACf,CAAE,CAAAP,QAAA,eAEFnF,IAAA,CAACT,UAAU,EAAC+F,EAAE,CAAE,CAAEK,UAAU,CAAE,KAAK,CAAEC,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAE5E,SAAS,CAAC,OAAO,CAAE,CAAEmC,YAAY,CAAE,OAAQ,CAAC,CAAC,CAAa,CAAC,cAE5I1C,IAAA,CAACV,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEG,OAAO,CAAE,cAAc,CAAEK,MAAM,CAAE,iBAAkB,CAAE,CAAAX,QAAA,cAE3DnF,IAAA,CAACR,SAAS,EACTuG,OAAO,CAAC,UAAU,CAClBV,IAAI,CAAC,OAAO,CACZW,WAAW,CAAEzF,SAAS,CAAC,iBAAiB,CAAE,CAAEmC,YAAY,CAAE,iBAAkB,CAAC,CAAE,CAC/EwC,SAAS,CAAC,qBAAqB,CAC/B3B,KAAK,CAAEK,uBAAuB,CAACnB,KAAM,CACrCwD,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzBC,QAAQ,CAAE9C,iBAAkB,CAC5B+C,KAAK,CAAEC,OAAO,CAAC/E,UAAU,CAAG;AAAA,CAC5BgF,UAAU,CACThF,UAAU,cACTpB,KAAA,SAAM+F,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAtB,QAAA,eACxEnF,IAAA,SACCiG,KAAK,CAAE,CAAES,WAAW,CAAE,KAAM,CAAE,CAC9BC,uBAAuB,CAAE,CAAEC,MAAM,CAAE/G,OAAQ,CAAE,CAC7C,CAAC,CACDU,SAAS,CAACe,UAAU,CAAE,CAAEoB,YAAY,CAAEpB,UAAW,CAAC,CAAC,EAC/C,CAAC,CACJ,IACJ,CACDuF,UAAU,CAAE,CACXvB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEwB,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEjB,SAAS,CAAE,iBAAiB,CAAEkB,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAEjB,MAAM,CAAE,iBAAiB,CAAEJ,YAAY,CAAE,KAAM,CACzE,CACD,CAAE,CACF,CAAC,CACE,CAAC,cAENxF,KAAA,CAACZ,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CACHC,eAAe,CAAE,oBAAoB,CACrCO,MAAM,CAAE,iBAAiB,CACzBN,YAAY,CAAE,gBACf,CAAE,CAAAL,QAAA,eAEFnF,IAAA,QAAKkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5E,SAAS,CAAC,aAAa,CAAE,CAAEmC,YAAY,CAAE,aAAc,CAAC,CAAC,CAAM,CAAC,cACtG1C,IAAA,QAAAmF,QAAA,cACCnF,IAAA,UACCgH,IAAI,CAAC,OAAO,CACZzD,KAAK,CAAEK,uBAAuB,CAACjD,UAAW,CAC1CwF,QAAQ,CAAG7C,CAAC,EAAKI,gBAAgB,CAAC,YAAY,CAAEJ,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE,CAChE2B,SAAS,CAAC,mBAAmB,CAC7B,CAAC,CACE,CAAC,EACF,CAAC,cAENhF,KAAA,CAACZ,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CACHC,eAAe,CAAE,oBAAoB,CACrCO,MAAM,CAAE,iBAAiB,CACzBN,YAAY,CAAE,gBACf,CAAE,CAAAL,QAAA,eAEFnF,IAAA,QAAKkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5E,SAAS,CAAC,MAAM,CAAE,CAAEmC,YAAY,CAAE,MAAO,CAAC,CAAC,CAAM,CAAC,cACxF1C,IAAA,QAAAmF,QAAA,cACCjF,KAAA,UAAOgF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAClDnF,IAAA,UACCgH,IAAI,CAAC,UAAU,CACfC,OAAO,CAAErD,uBAAuB,CAAChB,SAAU,CAC3CuD,QAAQ,CAAG7C,CAAC,EAAKI,gBAAgB,CAAC,WAAW,CAAEJ,CAAC,CAACE,MAAM,CAACyD,OAAO,CAAE,CACjEC,IAAI,CAAC,cAAc,CACnB,CAAC,cACFlH,IAAA,SAAMkF,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACJ,CAAC,EACF,CAAC,cAENhF,KAAA,CAACZ,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CACHC,eAAe,CAAE,oBAAoB,CACrCO,MAAM,CAAE,iBAAiB,CACzBN,YAAY,CAAE,gBACf,CAAE,CAAAL,QAAA,eAEFjF,KAAA,QAAKgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAE5E,SAAS,CAAC,QAAQ,CAAE,CAAEmC,YAAY,CAAE,QAAS,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,cAC7F1C,IAAA,QAAAmF,QAAA,cACCjF,KAAA,UAAOgF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAClDnF,IAAA,UACCgH,IAAI,CAAC,UAAU,CACfC,OAAO,CAAErD,uBAAuB,CAACf,WAAY,CAC7CsD,QAAQ,CAAG7C,CAAC,EAAKI,gBAAgB,CAAC,aAAa,CAAEJ,CAAC,CAACE,MAAM,CAACyD,OAAO,CAAE,CACnEC,IAAI,CAAC,cACL;AAAA,CACA,CAAC,cACFlH,IAAA,SAAMkF,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACJ,CAAC,EACF,CAAC,EACF,CAAC,cAENhF,KAAA,CAACZ,GAAG,EACHgG,EAAE,CAAE,CACHC,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,6BAA6B,CAC3CM,MAAM,CAAE,MAAM,CACdL,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,KACf,CAAE,CAAAP,QAAA,eAEFnF,IAAA,CAACT,UAAU,EAAC+F,EAAE,CAAE,CAAEK,UAAU,CAAE,KAAK,CAAEC,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAE5E,SAAS,CAAC,WAAW,CAAE,CAAEmC,YAAY,CAAE,WAAY,CAAC,CAAC,CAAa,CAAC,cAEpJ1C,IAAA,CAACV,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CAAEG,OAAO,CAAE,cAAc,CAAEK,MAAM,CAAE,iBAAkB,CAAE,CAAAX,QAAA,cAE3DnF,IAAA,CAACR,SAAS,EACTuG,OAAO,CAAC,UAAU,CAClBV,IAAI,CAAC,OAAO,CACZW,WAAW,CAAEzF,SAAS,CAAC,iBAAiB,CAAE,CAAEmC,YAAY,CAAE,iBAAkB,CAAC,CAAE,CAC/EwC,SAAS,CAAC,qBAAqB,CAC/B3B,KAAK,CAAEK,uBAAuB,CAACjB,QAAS,CACxCsD,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CACzBC,QAAQ,CAAExC,oBAAqB,CAC/ByC,KAAK,CAAEC,OAAO,CAAC7E,aAAa,CAAG;AAAA,CAC/B8E,UAAU,CACT9E,aAAa,cACZtB,KAAA,SAAM+F,KAAK,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAtB,QAAA,eACxEnF,IAAA,SACCiG,KAAK,CAAE,CAAES,WAAW,CAAE,KAAM,CAAE,CAC9BC,uBAAuB,CAAE,CAAEC,MAAM,CAAE/G,OAAQ,CAAE,CAC7C,CAAC,CACDU,SAAS,CAACiB,aAAa,CAAE,CAAEkB,YAAY,CAAElB,aAAc,CAAC,CAAC,EACrD,CAAC,CACJ,IACJ,CACDqF,UAAU,CAAE,CACXM,YAAY,CAAE,EAAE,CAChB7B,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEwB,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CAChC,SAAS,CAAE,CAAEjB,SAAS,CAAE,iBAAiB,CAAEkB,WAAW,CAAE,iBAAkB,CAAC,CAC3E,qBAAqB,CAAE,CAAEjB,MAAM,CAAE,iBAAiB,CAAEJ,YAAY,CAAE,KAAM,CACzE,CACD,CAAE,CACF,CAAC,CACE,CAAC,cAENxF,KAAA,CAACZ,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CACHC,eAAe,CAAE,oBAAoB,CACrCO,MAAM,CAAE,iBAAiB,CACzBN,YAAY,CAAE,gBACf,CAAE,CAAAL,QAAA,eAEFnF,IAAA,QAAKkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5E,SAAS,CAAC,gBAAgB,CAAE,CAAEmC,YAAY,CAAE,gBAAiB,CAAC,CAAC,CAAM,CAAC,cAC5G1C,IAAA,QAAAmF,QAAA,cACCnF,IAAA,UACCgH,IAAI,CAAC,OAAO,CACZzD,KAAK,CAAEK,uBAAuB,CAACd,aAAc,CAC7CqD,QAAQ,CAAG7C,CAAC,EAAKI,gBAAgB,CAAC,eAAe,CAAEJ,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE,CACnE2B,SAAS,CAAC,mBAAmB,CAC7B,CAAC,CACE,CAAC,EACF,CAAC,cAENhF,KAAA,CAACZ,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CACHC,eAAe,CAAE,oBAAoB,CACrCO,MAAM,CAAE,iBAAiB,CACzBN,YAAY,CAAE,gBACf,CAAE,CAAAL,QAAA,eAEFnF,IAAA,QAAKkF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE5E,SAAS,CAAC,MAAM,CAAE,CAAEmC,YAAY,CAAE,MAAO,CAAC,CAAC,CAAM,CAAC,cACxF1C,IAAA,QAAAmF,QAAA,cACCjF,KAAA,UAAOgF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAChCnF,IAAA,UACCgH,IAAI,CAAC,UAAU,CACfC,OAAO,CAAErD,uBAAuB,CAACb,YAAa,CAC9CoD,QAAQ,CAAG7C,CAAC,EAAKI,gBAAgB,CAAC,cAAc,CAAEJ,CAAC,CAACE,MAAM,CAACyD,OAAO,CAAE,CACpEC,IAAI,CAAC,cAAc,CACnB,CAAC,cACFlH,IAAA,SAAMkF,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACJ,CAAC,EACF,CAAC,cAENhF,KAAA,CAACZ,GAAG,EACH4F,SAAS,CAAC,mBAAmB,CAC7BI,EAAE,CAAE,CACHC,eAAe,CAAE,oBAAoB,CACrCO,MAAM,CAAE,iBAAiB,CACzBN,YAAY,CAAE,gBACf,CAAE,CAAAL,QAAA,eAEFjF,KAAA,QAAKgF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAE5E,SAAS,CAAC,QAAQ,CAAE,CAAEmC,YAAY,CAAE,QAAS,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,cAC7F1C,IAAA,QAAAmF,QAAA,cACCjF,KAAA,UAAOgF,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAClDnF,IAAA,UACCgH,IAAI,CAAC,UAAU,CACfC,OAAO,CAAErD,uBAAuB,CAACZ,cAAe,CAChDmD,QAAQ,CAAG7C,CAAC,EAAKI,gBAAgB,CAAC,gBAAgB,CAAEJ,CAAC,CAACE,MAAM,CAACyD,OAAO,CAAE,CACtEC,IAAI,CAAC,cAAc,CACnB,CAAC,cACFlH,IAAA,SAAMkF,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACJ,CAAC,EACF,CAAC,EACF,CAAC,EACF,CAAC,CACF,CAAC,cACNlF,IAAA,QAAKkF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClCnF,IAAA,CAACN,MAAM,EACNqG,OAAO,CAAC,WAAW,CACnBX,OAAO,CAAER,kBAAmB,CAC5BM,SAAS,CAAE,aAAaxD,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CACvD0F,QAAQ,CAAE1F,UAAW,CAAAyD,QAAA,CAEpB5E,SAAS,CAAC,OAAO,CAAE,CAAEmC,YAAY,CAAE,OAAQ,CAAC,CAAC,CACvC,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,CAET,CAAC,CAED,cAAe,CAAAvC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}